.react-error h1, .react-loading h1 {
	text-align: center;
	color: #999;
	margin-top: 150px;
}

.react-loading {
	position: absolute;
	width: 100%;
	height: 100%;
}

.react-loading-spinner {
	position: absolute;
	width: 120px;
	height: 120px;
	left: 50%;
	margin-left: -65px;

	background-color: #333;
	border-radius: 100%;
	-webkit-animation: sk-scaleout-loading 1.0s infinite ease-in-out;
	animation: sk-scaleout-loading 1.0s infinite ease-in-out;
}

.react-error p {
	text-align: center;
	line-height: 1;
}

.react-error pre {
	border: 1px solid #aaa;
	background-color: white;
	padding: 10px;
	margin: 0 auto;
	width: 600px;
}

p.versions {
	text-align: left;
	width: 600px;
	margin: 0 auto;
	line-height: 1.6;
	color: #666;
	font-size: 12px;
	background-color: white;
	padding: 10px;
	border: 1px solid #ddd;
}

@-webkit-keyframes sk-scaleout-loading {
  0% { -webkit-transform: scale(0) }
  100% {
    -webkit-transform: scale(1.0);
    opacity: 0;
  }
}

@keyframes sk-scaleout-loading {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  } 100% {
    -webkit-transform: scale(1.0);
    transform: scale(1.0);
    opacity: 0;
  }
}
