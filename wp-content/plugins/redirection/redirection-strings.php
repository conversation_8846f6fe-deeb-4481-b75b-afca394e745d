<?php
/* THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY. */
$redirection_strings = array(
_n( "Are you sure you want to delete this item?", "Are you sure you want to delete the %d selected items?", 1, "redirection" ), // client/lib/store/index.js:11
__( "Are you sure want to delete all %d matching items?", "redirection" ), // client/lib/store/index.js:18
__( "Database problem", "redirection" ), // client/component/database/api-error.js:11
__( "Try again", "redirection" ), // client/component/database/api-error.js:15
__( "Database problem", "redirection" ), // client/component/database/database-error.js:41
__( "Try again", "redirection" ), // client/component/database/database-error.js:45
__( "Skip this stage", "redirection" ), // client/component/database/database-error.js:50
__( "Stop upgrade", "redirection" ), // client/component/database/database-error.js:56
__( "If you want to {{support}}ask for support{{/support}} please include these details:", "redirection" ), // client/component/database/database-error.js:62
__( "Upgrading Redirection", "redirection" ), // client/component/database/index.js:27
__( "Setting up Redirection", "redirection" ), // client/component/database/index.js:30
__( "Leaving before the process has completed may cause problems.", "redirection" ), // client/component/database/index.js:71
__( "Please remain on this page until complete.", "redirection" ), // client/component/database/index.js:77
__( "Progress: %(complete)d\$", "redirection" ), // client/component/database/index.js:81
__( "Custom Display", "redirection" ), // client/component/display-options/index.js:30
__( "Pre-defined", "redirection" ), // client/component/display-options/index.js:64
__( "Custom", "redirection" ), // client/component/display-options/index.js:69
__( "Geo IP Error", "redirection" ), // client/component/geo-map/index.js:29
__( "Something went wrong obtaining this information", "redirection" ), // client/component/geo-map/index.js:30
__( "Geo IP", "redirection" ), // client/component/geo-map/index.js:43
__( "This is an IP from a private network. This means it is located inside a home or business network and no more information can be displayed.", "redirection" ), // client/component/geo-map/index.js:46
__( "Geo IP", "redirection" ), // client/component/geo-map/index.js:57
__( "No details are known for this address.", "redirection" ), // client/component/geo-map/index.js:60
__( "Geo IP", "redirection" ), // client/component/geo-map/index.js:77
__( "City", "redirection" ), // client/component/geo-map/index.js:82
__( "Area", "redirection" ), // client/component/geo-map/index.js:86
__( "Timezone", "redirection" ), // client/component/geo-map/index.js:90
__( "Geo Location", "redirection" ), // client/component/geo-map/index.js:94
__( "An unknown errorm", "redirection" ), // client/component/http-check/details.js:37
__( "Something is wrong with the server. This is not a problem with Redirection and you will need to resolve the error yourself.", "redirection" ), // client/component/http-check/details.js:41
__( "An error page was returned. This is unlikely to be a problem with Redirection. {{support}}What does this mean?{{/support}}.", "redirection" ), // client/component/http-check/details.js:47
__( "Redirected by Redirection.", "redirection" ), // client/component/http-check/details.js:61
__( "Matches your redirect", "redirection" ), // client/component/http-check/details.js:63
__( "Redirected by Redirection.", "redirection" ), // client/component/http-check/details.js:74
__( "Redirected by %1s. {{support}}What does this mean?{{/support}}.", "redirection" ), // client/component/http-check/details.js:77
__( "Redirected by an unknown agent. {{support}}What does this mean?{{/support}}.", "redirection" ), // client/component/http-check/details.js:87
__( "Page was loaded.", "redirection" ), // client/component/http-check/details.js:95
__( "Unable to check that URL. It may not be valid or accessible.", "redirection" ), // client/component/http-check/details.js:188
__( "If this is not expected then this {{support}}support page{{/support}} may help.", "redirection" ), // client/component/http-check/details.js:206
__( "If your browser is behaving differently then you should clear your browser cache.", "redirection" ), // client/component/http-check/details.js:219
__( "View full redirect.li results.", "redirection" ), // client/component/http-check/details.js:223
__( "Error", "redirection" ), // client/component/http-check/response.js:21
__( "Something went wrong obtaining this information. It may work in the future.", "redirection" ), // client/component/http-check/response.js:22
__( "Check redirect for: {{code}}%s{{/code}}", "redirection" ), // client/component/http-check/response.js:50
__( "Filters", "redirection" ), // client/component/log-page/log-filters.js:87
__( "Powered by {{link}}redirect.li{{/link}}", "redirection" ), // client/component/powered-by/index.js:16
__( "Saving...", "redirection" ), // client/component/progress/index.js:23
__( "Saving...", "redirection" ), // client/component/progress/index.js:26
__( "with HTTP code", "redirection" ), // client/component/redirect-edit/action-code.js:42
__( "URL only", "redirection" ), // client/component/redirect-edit/constants.js:30
__( "URL and login status", "redirection" ), // client/component/redirect-edit/constants.js:34
__( "URL and role/capability", "redirection" ), // client/component/redirect-edit/constants.js:38
__( "URL and referrer", "redirection" ), // client/component/redirect-edit/constants.js:42
__( "URL and user agent", "redirection" ), // client/component/redirect-edit/constants.js:46
__( "URL and cookie", "redirection" ), // client/component/redirect-edit/constants.js:50
__( "URL and IP", "redirection" ), // client/component/redirect-edit/constants.js:54
__( "URL and server", "redirection" ), // client/component/redirect-edit/constants.js:58
__( "URL and HTTP header", "redirection" ), // client/component/redirect-edit/constants.js:62
__( "URL and custom filter", "redirection" ), // client/component/redirect-edit/constants.js:66
__( "URL and WordPress page type", "redirection" ), // client/component/redirect-edit/constants.js:70
__( "URL and language", "redirection" ), // client/component/redirect-edit/constants.js:74
__( "Redirect to URL", "redirection" ), // client/component/redirect-edit/constants.js:81
__( "Redirect to random post", "redirection" ), // client/component/redirect-edit/constants.js:85
__( "Pass-through", "redirection" ), // client/component/redirect-edit/constants.js:89
__( "Error (404)", "redirection" ), // client/component/redirect-edit/constants.js:93
__( "Do nothing (ignore)", "redirection" ), // client/component/redirect-edit/constants.js:97
__( "301 - Moved Permanently", "redirection" ), // client/component/redirect-edit/constants.js:104
__( "302 - Found", "redirection" ), // client/component/redirect-edit/constants.js:108
__( "303 - See Other", "redirection" ), // client/component/redirect-edit/constants.js:112
__( "304 - Not Modified", "redirection" ), // client/component/redirect-edit/constants.js:116
__( "307 - Temporary Redirect", "redirection" ), // client/component/redirect-edit/constants.js:120
__( "308 - Permanent Redirect", "redirection" ), // client/component/redirect-edit/constants.js:124
__( "400 - Bad Request", "redirection" ), // client/component/redirect-edit/constants.js:131
__( "401 - Unauthorized", "redirection" ), // client/component/redirect-edit/constants.js:135
__( "403 - Forbidden", "redirection" ), // client/component/redirect-edit/constants.js:139
__( "404 - Not Found", "redirection" ), // client/component/redirect-edit/constants.js:143
__( "410 - Gone", "redirection" ), // client/component/redirect-edit/constants.js:147
__( "418 - I'm a teapot", "redirection" ), // client/component/redirect-edit/constants.js:151
__( "451 - Unavailable For Legal Reasons", "redirection" ), // client/component/redirect-edit/constants.js:155
__( "500 - Internal Server Error", "redirection" ), // client/component/redirect-edit/constants.js:159
__( "501 - Not implemented", "redirection" ), // client/component/redirect-edit/constants.js:163
__( "502 - Bad Gateway", "redirection" ), // client/component/redirect-edit/constants.js:167
__( "503 - Service Unavailable", "redirection" ), // client/component/redirect-edit/constants.js:171
__( "504 - Gateway Timeout", "redirection" ), // client/component/redirect-edit/constants.js:175
__( "Regex", "redirection" ), // client/component/redirect-edit/constants.js:184
__( "Ignore Slash", "redirection" ), // client/component/redirect-edit/constants.js:188
__( "Ignore Case", "redirection" ), // client/component/redirect-edit/constants.js:192
__( "Exact match", "redirection" ), // client/component/redirect-edit/constants.js:203
__( "Exact match in any order", "redirection" ), // client/component/redirect-edit/constants.js:207
__( "Ignore all parameters", "redirection" ), // client/component/redirect-edit/constants.js:211
__( "Ignore & pass parameters to the target", "redirection" ), // client/component/redirect-edit/constants.js:215
__( "Exclude from logs", "redirection" ), // client/component/redirect-edit/index.js:318
__( "When matched", "redirection" ), // client/component/redirect-edit/index.js:360
__( "Group", "redirection" ), // client/component/redirect-edit/index.js:384
__( "Save", "redirection" ), // client/component/redirect-edit/index.js:394
__( "Cancel", "redirection" ), // client/component/redirect-edit/index.js:415
__( "Close", "redirection" ), // client/component/redirect-edit/index.js:420
__( "Show advanced options", "redirection" ), // client/component/redirect-edit/index.js:429
__( "Match", "redirection" ), // client/component/redirect-edit/match-type.js:19
__( "Position", "redirection" ), // client/component/redirect-edit/position.js:12
__( "Query Parameters", "redirection" ), // client/component/redirect-edit/source-query.js:25
__( "Source URL", "redirection" ), // client/component/redirect-edit/source-url.js:25
__( "Source URL", "redirection" ), // client/component/redirect-edit/source-url.js:40
__( "The relative URL you want to redirect from", "redirection" ), // client/component/redirect-edit/source-url.js:48
__( "URL options / Regex", "redirection" ), // client/component/redirect-edit/source-url.js:55
__( "The target URL you want to redirect, or auto-complete on post name or permalink.", "redirection" ), // client/component/redirect-edit/target.js:24
__( "Title", "redirection" ), // client/component/redirect-edit/title.js:23
__( "Describe the purpose of this redirect (optional)", "redirection" ), // client/component/redirect-edit/title.js:29
__( "Anchor values are not sent to the server and cannot be redirected.", "redirection" ), // client/component/redirect-edit/warning.js:57
__( "This will be converted to a server redirect for the domain {{code}}%(server)s{{/code}}.", "redirection" ), // client/component/redirect-edit/warning.js:66
__( "The source URL should probably start with a {{code}}/{{/code}}", "redirection" ), // client/component/redirect-edit/warning.js:87
__( "Remember to enable the \"regex\" option if this is a regular expression.", "redirection" ), // client/component/redirect-edit/warning.js:99
__( "Please add migrated permalinks to the Site page under the \"Permalink Migration\" section.", "redirection" ), // client/component/redirect-edit/warning.js:108
__( "To prevent a greedy regular expression you can use {{code}}^{{/code}} to anchor it to the start of the URL. For example: {{code}}%(example)s{{/code}}", "redirection" ), // client/component/redirect-edit/warning.js:126
__( "The caret {{code}}^{{/code}} should be at the start. For example: {{code}}%(example)s{{/code}}", "redirection" ), // client/component/redirect-edit/warning.js:142
__( "To match {{code}}?{{/code}} you need to escape it with {{code}}\\?{{/code}}", "redirection" ), // client/component/redirect-edit/warning.js:155
__( "Wildcards are not supported. You need to use a {{link}}regular expression{{/link}}.", "redirection" ), // client/component/redirect-edit/warning.js:164
__( "If you want to redirect everything please use a site relocation or alias from the Site page.", "redirection" ), // client/component/redirect-edit/warning.js:174
__( "Your source is the same as a target and this will create a loop. Leave a target blank if you do not want to take action.", "redirection" ), // client/component/redirect-edit/warning.js:184
__( "Your target URL should be an absolute URL like {{code}}https://domain.com/%(url)s{{/code}} or start with a slash {{code}}/%(url)s{{/code}}.", "redirection" ), // client/component/redirect-edit/warning.js:205
__( "Your target URL contains the invalid character {{code}}%(invalid)s{{/code}}", "redirection" ), // client/component/redirect-edit/warning.js:225
__( "Your URL appears to contain a domain inside the path: {{code}}%(relative)s{{/code}}. Did you mean to use {{code}}%(absolute)s{{/code}} instead?", "redirection" ), // client/component/redirect-edit/warning.js:244
__( "Some servers may be configured to serve file resources directly, preventing a redirect occurring.", "redirection" ), // client/component/redirect-edit/warning.js:264
__( "Request Headers", "redirection" ), // client/component/request-data/index.js:21
__( "Redirect Source", "redirection" ), // client/component/request-data/index.js:44
__( "Working!", "redirection" ), // client/component/rest-api-status/api-result-pass.js:15
__( "Show Full", "redirection" ), // client/component/rest-api-status/api-result-raw.js:41
__( "Hide", "redirection" ), // client/component/rest-api-status/api-result-raw.js:42
__( "Good", "redirection" ), // client/component/rest-api-status/index.js:102
__( "Working but some issues", "redirection" ), // client/component/rest-api-status/index.js:104
__( "Unavailable", "redirection" ), // client/component/rest-api-status/index.js:107
__( "There are some problems connecting to your REST API. It is not necessary to fix these problems and the plugin is able to work.", "redirection" ), // client/component/rest-api-status/index.js:122
__( "Your REST API is not working and the plugin will not be able to continue until this is fixed.", "redirection" ), // client/component/rest-api-status/index.js:127
__( "Summary", "redirection" ), // client/component/rest-api-status/index.js:135
__( "Show Problems", "redirection" ), // client/component/rest-api-status/index.js:141
__( "Testing - %s\$", "redirection" ), // client/component/rest-api-status/index.js:170
__( "Check Again", "redirection" ), // client/component/rest-api-status/index.js:179
__( "Bulk Actions", "redirection" ), // client/component/table/bulk-actions.js:45
__( "Apply", "redirection" ), // client/component/table/bulk-actions.js:61
__( "Apply", "redirection" ), // client/component/table/group.js:31
__( "Useragent Error", "redirection" ), // client/component/useragent/index.js:30
__( "Something went wrong obtaining this information", "redirection" ), // client/component/useragent/index.js:31
__( "Unknown Useragent", "redirection" ), // client/component/useragent/index.js:42
__( "Device", "redirection" ), // client/component/useragent/index.js:97
__( "Operating System", "redirection" ), // client/component/useragent/index.js:101
__( "Browser", "redirection" ), // client/component/useragent/index.js:105
__( "Engine", "redirection" ), // client/component/useragent/index.js:109
__( "Useragent", "redirection" ), // client/component/useragent/index.js:114
__( "Agent", "redirection" ), // client/component/useragent/index.js:118
__( "Something went wrong when installing Redirection.", "redirection" ), // client/component/welcome-wizard/index.js:89
__( "Redirection", "redirection" ), // client/component/welcome-wizard/index.js:94
__( "I need support!", "redirection" ), // client/component/welcome-wizard/index.js:107
__( "Manual Install", "redirection" ), // client/component/welcome-wizard/manual-install.js:27
__( "If your site needs special database permissions, or you would rather do it yourself, you can manually run the following SQL.", "redirection" ), // client/component/welcome-wizard/manual-install.js:30
__( "Click \"Finished! 🎉\" when finished.", "redirection" ), // client/component/welcome-wizard/manual-install.js:33
__( "Database problem", "redirection" ), // client/component/welcome-wizard/manual-install.js:47
__( "The Redirection database does not appear to exist. Have you run the above SQL?", "redirection" ), // client/component/welcome-wizard/manual-install.js:48
__( "Finished! 🎉", "redirection" ), // client/component/welcome-wizard/manual-install.js:53
__( "Go back", "redirection" ), // client/component/welcome-wizard/manual-install.js:56
__( "If you do not complete the manual install you will be returned here.", "redirection" ), // client/component/welcome-wizard/manual-install.js:58
__( "REST API", "redirection" ), // client/component/welcome-wizard/step-api.js:38
__( "Redirection uses the {{link}}WordPress REST API{{/link}} to communicate with WordPress. This is enabled and working by default. Sometimes the REST API is blocked by:", "redirection" ), // client/component/welcome-wizard/step-api.js:41
__( "A security plugin (e.g Wordfence)", "redirection" ), // client/component/welcome-wizard/step-api.js:52
__( "A server firewall or other server configuration (e.g OVH)", "redirection" ), // client/component/welcome-wizard/step-api.js:53
__( "Caching software (e.g Cloudflare)", "redirection" ), // client/component/welcome-wizard/step-api.js:54
__( "Some other plugin that blocks the REST API", "redirection" ), // client/component/welcome-wizard/step-api.js:55
__( "If you do experience a problem then please consult your plugin documentation, or try contacting your host support. This is generally {{link}}not a problem caused by Redirection{{/link}}.", "redirection" ), // client/component/welcome-wizard/step-api.js:59
__( "You have different URLs configured on your WordPress Settings > General page, which is usually an indication of a misconfiguration, and it can cause problems with the REST API. Please review your settings.", "redirection" ), // client/component/welcome-wizard/step-api.js:71
__( "You will need at least one working REST API to continue.", "redirection" ), // client/component/welcome-wizard/step-api.js:85
__( "Finish Setup", "redirection" ), // client/component/welcome-wizard/step-api.js:89
__( "Continue", "redirection" ), // client/component/welcome-wizard/step-database.js:30
__( "Installation Complete", "redirection" ), // client/component/welcome-wizard/step-finish.js:42
__( "Redirection is now installed!", "redirection" ), // client/component/welcome-wizard/step-finish.js:44
__( "Please take a moment to consult the {{support}}support site{{/support}} for information about how to use Redirection.", "redirection" ), // client/component/welcome-wizard/step-finish.js:46
__( "Ready to begin! 🎉", "redirection" ), // client/component/welcome-wizard/step-finish.js:53
__( "Import Existing Redirects", "redirection" ), // client/component/welcome-wizard/step-importer.js:34
__( "Importing existing redirects from WordPress or other plugins is a good way to get started with Redirection. Check each set of redirects you wish to import.", "redirection" ), // client/component/welcome-wizard/step-importer.js:37
__( "WordPress automatically creates redirects when you change a post URL. Importing these into Redirection will allow you to manage and monitor them.", "redirection" ), // client/component/welcome-wizard/step-importer.js:45
__( "The following plugins have been detected.", "redirection" ), // client/component/welcome-wizard/step-importer.js:67
__( "Continue", "redirection" ), // client/component/welcome-wizard/step-importer.js:88
__( "Go back", "redirection" ), // client/component/welcome-wizard/step-importer.js:92
__( "Import Existing Redirects", "redirection" ), // client/component/welcome-wizard/step-importing.js:32
__( "Please wait, importing.", "redirection" ), // client/component/welcome-wizard/step-importing.js:36
__( "Import finished.", "redirection" ), // client/component/welcome-wizard/step-importing.js:46
__( "Importing failed.", "redirection" ), // client/component/welcome-wizard/step-importing.js:46
__( "Retry", "redirection" ), // client/component/welcome-wizard/step-importing.js:51
__( "Continue", "redirection" ), // client/component/welcome-wizard/step-importing.js:56
__( "Basic Setup", "redirection" ), // client/component/welcome-wizard/step-options.js:36
__( "These are some options you may want to enable now. They can be changed at any time.", "redirection" ), // client/component/welcome-wizard/step-options.js:38
__( "Monitor permalink changes in WordPress posts and pages", "redirection" ), // client/component/welcome-wizard/step-options.js:49
__( "If you change the permalink in a post or page then Redirection can automatically create a redirect for you.", "redirection" ), // client/component/welcome-wizard/step-options.js:53
__( "{{link}}Read more about this.{{/link}}", "redirection" ), // client/component/welcome-wizard/step-options.js:57
__( "Keep a log of all redirects and 404 errors.", "redirection" ), // client/component/welcome-wizard/step-options.js:69
__( "Storing logs for redirects and 404s will allow you to see what is happening on your site. This will increase your database storage requirements.", "redirection" ), // client/component/welcome-wizard/step-options.js:73
__( "{{link}}Read more about this.{{/link}}", "redirection" ), // client/component/welcome-wizard/step-options.js:77
__( "Store IP information for redirects and 404 errors.", "redirection" ), // client/component/welcome-wizard/step-options.js:89
__( "Storing the IP address allows you to perform additional log actions. Note that you will need to adhere to local laws regarding the collection of data (for example GDPR).", "redirection" ), // client/component/welcome-wizard/step-options.js:93
__( "{{link}}Read more about this.{{/link}}", "redirection" ), // client/component/welcome-wizard/step-options.js:97
__( "Continue", "redirection" ), // client/component/welcome-wizard/step-options.js:107
__( "Go back", "redirection" ), // client/component/welcome-wizard/step-options.js:111
__( "Welcome to Redirection 🚀🎉", "redirection" ), // client/component/welcome-wizard/step-welcome.js:27
__( "Thank you for installing and using Redirection v%(version)s. This plugin will allow you to manage 301 redirections, keep track of 404 errors, and improve your site, with no knowledge of Apache or Nginx needed.", "redirection" ), // client/component/welcome-wizard/step-welcome.js:30
__( "Redirection is designed to be used on sites with a few redirects to sites with thousands of redirects.", "redirection" ), // client/component/welcome-wizard/step-welcome.js:40
__( "How do I use this plugin?", "redirection" ), // client/component/welcome-wizard/step-welcome.js:45
__( "A simple redirect involves setting a {{strong}}source URL{{/strong}} (the old URL) and a {{strong}}target URL{{/strong}} (the new URL). Here's an example:", "redirection" ), // client/component/welcome-wizard/step-welcome.js:47
__( "Source URL", "redirection" ), // client/component/welcome-wizard/step-welcome.js:60
__( "(Example) The source URL is your old or original URL", "redirection" ), // client/component/welcome-wizard/step-welcome.js:66
__( "Target URL", "redirection" ), // client/component/welcome-wizard/step-welcome.js:71
__( "(Example) The target URL is the new URL", "redirection" ), // client/component/welcome-wizard/step-welcome.js:77
__( "That's all there is to it - you are now redirecting! Note that the above is just an example.", "redirection" ), // client/component/welcome-wizard/step-welcome.js:85
__( "Full documentation can be found on the {{link}}Redirection website.{{/link}}", "redirection" ), // client/component/welcome-wizard/step-welcome.js:88
__( "Some features you may find useful are", "redirection" ), // client/component/welcome-wizard/step-welcome.js:95
__( "{{link}}Monitor 404 errors{{/link}}, get detailed information about the visitor, and fix any problems", "redirection" ), // client/component/welcome-wizard/step-welcome.js:99
__( "{{link}}Import{{/link}} from .htaccess, CSV, and a variety of other plugins", "redirection" ), // client/component/welcome-wizard/step-welcome.js:109
__( "More powerful URL matching, including {{regular}}regular expressions{{/regular}}, and {{other}}other conditions{{/other}}", "redirection" ), // client/component/welcome-wizard/step-welcome.js:116
__( "Check a URL is being redirected", "redirection" ), // client/component/welcome-wizard/step-welcome.js:128
__( "What's next?", "redirection" ), // client/component/welcome-wizard/step-welcome.js:131
__( "First you will be asked a few questions, and then Redirection will set up your database.", "redirection" ), // client/component/welcome-wizard/step-welcome.js:132
__( "Start Setup", "redirection" ), // client/component/welcome-wizard/step-welcome.js:136
__( "Manual Setup", "redirection" ), // client/component/welcome-wizard/step-welcome.js:139
__( "Name", "redirection" ), // client/page/groups/constants.js:8
__( "Module", "redirection" ), // client/page/groups/constants.js:9
__( "Status", "redirection" ), // client/page/groups/constants.js:10
__( "Redirects", "redirection" ), // client/page/groups/constants.js:11
__( "Standard Display", "redirection" ), // client/page/groups/constants.js:17
__( "Compact Display", "redirection" ), // client/page/groups/constants.js:22
__( "Display All", "redirection" ), // client/page/groups/constants.js:27
__( "Status", "redirection" ), // client/page/groups/constants.js:34
__( "Enabled", "redirection" ), // client/page/groups/constants.js:38
__( "Disabled", "redirection" ), // client/page/groups/constants.js:42
__( "Module", "redirection" ), // client/page/groups/constants.js:48
__( "Status", "redirection" ), // client/page/groups/constants.js:57
__( "Name", "redirection" ), // client/page/groups/constants.js:62
__( "Redirects", "redirection" ), // client/page/groups/constants.js:67
__( "Module", "redirection" ), // client/page/groups/constants.js:72
__( "Delete", "redirection" ), // client/page/groups/constants.js:80
__( "Enable", "redirection" ), // client/page/groups/constants.js:84
__( "Disable", "redirection" ), // client/page/groups/constants.js:88
__( "Search", "redirection" ), // client/page/groups/constants.js:95
__( "Add Group", "redirection" ), // client/page/groups/create-group.js:29
__( "Use groups to organise your redirects. Groups are assigned to a module, which affects how the redirects in that group work. If you are unsure then stick to the WordPress module.", "redirection" ), // client/page/groups/create-group.js:31
__( "Name", "redirection" ), // client/page/groups/create-group.js:40
__( "Note that you will need to set the Apache module path in your Redirection options.", "redirection" ), // client/page/groups/create-group.js:73
__( "Edit", "redirection" ), // client/page/groups/row-actions.js:30
__( "Delete", "redirection" ), // client/page/groups/row-actions.js:38
__( "View Redirects", "redirection" ), // client/page/groups/row-actions.js:49
__( "Disable", "redirection" ), // client/page/groups/row-actions.js:58
__( "Enable", "redirection" ), // client/page/groups/row-actions.js:64
__( "Cached Redirection detected", "redirection" ), // client/page/home/<USER>
__( "Please clear your browser cache and reload this page.", "redirection" ), // client/page/home/<USER>
__( "If you are using a caching system such as Cloudflare then please read this: ", "redirection" ), // client/page/home/<USER>
__( "clearing your cache.", "redirection" ), // client/page/home/<USER>
__( "Redirection is not working. Try clearing your browser cache and reloading this page.", "redirection" ), // client/page/home/<USER>
__( "If you are using a page caching plugin or service (CloudFlare, OVH, etc) then you can also try clearing that cache.", "redirection" ), // client/page/home/<USER>
__( "If that doesn't help, open your browser's error console and create a {{link}}new issue{{/link}} with the details.", "redirection" ), // client/page/home/<USER>
__( "Please check the {{link}}support site{{/link}} before proceeding further.", "redirection" ), // client/page/home/<USER>
__( "If that did not help then {{strong}}create an issue{{/strong}} or send it in an {{strong}}email{{/strong}}.", "redirection" ), // client/page/home/<USER>
__( "Create An Issue", "redirection" ), // client/page/home/<USER>
__( "Email", "redirection" ), // client/page/home/<USER>
__( "Include these details in your report along with a description of what you were doing and a screenshot.", "redirection" ), // client/page/home/<USER>
__( "What do I do next?", "redirection" ), // client/page/home/<USER>
__( "Take a look at the {{link}}plugin status{{/link}}. It may be able to identify and \"magic fix\" the problem.", "redirection" ), // client/page/home/<USER>
__( "{{link}}Caching software{{/link}}, in particular Cloudflare, can cache the wrong thing. Try clearing all your caches.", "redirection" ), // client/page/home/<USER>
__( "{{link}}Please temporarily disable other plugins!{{/link}} This fixes so many problems.", "redirection" ), // client/page/home/<USER>
__( "If you are using WordPress 5.2 or newer then look at your {{link}}Site Health{{/link}} and resolve any issues.", "redirection" ), // client/page/home/<USER>
__( "Redirections", "redirection" ), // client/page/home/<USER>
__( "Site", "redirection" ), // client/page/home/<USER>
__( "Groups", "redirection" ), // client/page/home/<USER>
__( "Import/Export", "redirection" ), // client/page/home/<USER>
__( "Logs", "redirection" ), // client/page/home/<USER>
__( "404 errors", "redirection" ), // client/page/home/<USER>
__( "Options", "redirection" ), // client/page/home/<USER>
__( "Support", "redirection" ), // client/page/home/<USER>
__( "Redirects", "redirection" ), // client/page/home/<USER>
__( "Groups", "redirection" ), // client/page/home/<USER>
__( "Site", "redirection" ), // client/page/home/<USER>
__( "Log", "redirection" ), // client/page/home/<USER>
__( "404s", "redirection" ), // client/page/home/<USER>
__( "Import/Export", "redirection" ), // client/page/home/<USER>
__( "Options", "redirection" ), // client/page/home/<USER>
__( "Support", "redirection" ), // client/page/home/<USER>
__( "Add New", "redirection" ), // client/page/home/<USER>
__( "Version %s installed! Please read the {{url}}release notes{{/url}} for details.", "redirection" ), // client/page/home/<USER>
__( "OK", "redirection" ), // client/page/home/<USER>
__( "total = ", "redirection" ), // client/page/io/importer.js:17
__( "Import from %s", "redirection" ), // client/page/io/importer.js:20
__( "Import to group", "redirection" ), // client/page/io/index.js:101
__( "Import a CSV, .htaccess, or JSON file.", "redirection" ), // client/page/io/index.js:109
__( "Click 'Add File' or drag and drop here.", "redirection" ), // client/page/io/index.js:110
__( "Add File", "redirection" ), // client/page/io/index.js:112
__( "File selected", "redirection" ), // client/page/io/index.js:123
__( "Upload", "redirection" ), // client/page/io/index.js:129
__( "Cancel", "redirection" ), // client/page/io/index.js:130
__( "Importing", "redirection" ), // client/page/io/index.js:140
__( "Finished importing", "redirection" ), // client/page/io/index.js:156
__( "Total redirects imported:", "redirection" ), // client/page/io/index.js:158
__( "Double-check the file is the correct format!", "redirection" ), // client/page/io/index.js:159
__( "OK", "redirection" ), // client/page/io/index.js:161
__( "Close", "redirection" ), // client/page/io/index.js:210
__( "Are you sure you want to import from %s?", "redirection" ), // client/page/io/index.js:224
__( "Plugin Importers", "redirection" ), // client/page/io/index.js:232
__( "The following redirect plugins were detected on your site and can be imported from.", "redirection" ), // client/page/io/index.js:234
__( "Import", "redirection" ), // client/page/io/index.js:246
__( "All imports will be appended to the current database - nothing is merged.", "redirection" ), // client/page/io/index.js:257
__( "{{strong}}CSV file format{{/strong}}: {{code}}source URL, target URL{{/code}} - and can be optionally followed with {{code}}regex, http code{{/code}} ({{code}}regex{{/code}} - 0 for no, 1 for yes).", "redirection" ), // client/page/io/index.js:260
__( "CSV does not include all information, and everything is imported/exported as \"URL only\" matches. Use the JSON format for a full set of data.", "redirection" ), // client/page/io/index.js:267
__( "Export", "redirection" ), // client/page/io/index.js:270
__( "Export to CSV, Apache .htaccess, Nginx, or Redirection JSON. The JSON format contains full information, and other formats contain partial information appropriate to the format.", "redirection" ), // client/page/io/index.js:271
__( "Everything", "redirection" ), // client/page/io/index.js:275
__( "WordPress redirects", "redirection" ), // client/page/io/index.js:276
__( "Apache redirects", "redirection" ), // client/page/io/index.js:277
__( "Nginx redirects", "redirection" ), // client/page/io/index.js:278
__( "Complete data (JSON)", "redirection" ), // client/page/io/index.js:282
__( "CSV", "redirection" ), // client/page/io/index.js:283
__( "Apache .htaccess", "redirection" ), // client/page/io/index.js:284
__( "Nginx rewrite rules", "redirection" ), // client/page/io/index.js:285
__( "View", "redirection" ), // client/page/io/index.js:288
__( "Download", "redirection" ), // client/page/io/index.js:289
__( "Export redirect", "redirection" ), // client/page/io/index.js:296
__( "Export 404", "redirection" ), // client/page/io/index.js:297
__( "Source URL", "redirection" ), // client/page/logs/constants.js:12
__( "Count", "redirection" ), // client/page/logs/constants.js:18
__( "IP", "redirection" ), // client/page/logs/constants.js:26
__( "Count", "redirection" ), // client/page/logs/constants.js:32
__( "User Agent", "redirection" ), // client/page/logs/constants.js:40
__( "Count", "redirection" ), // client/page/logs/constants.js:46
__( "Date", "redirection" ), // client/page/logs/constants.js:54
__( "Method", "redirection" ), // client/page/logs/constants.js:58
__( "Domain", "redirection" ), // client/page/logs/constants.js:63
__( "Source URL", "redirection" ), // client/page/logs/constants.js:68
__( "Target URL", "redirection" ), // client/page/logs/constants.js:73
__( "Redirect By", "redirection" ), // client/page/logs/constants.js:78
__( "HTTP code", "redirection" ), // client/page/logs/constants.js:83
__( "Referrer", "redirection" ), // client/page/logs/constants.js:88
__( "User Agent", "redirection" ), // client/page/logs/constants.js:93
__( "IP", "redirection" ), // client/page/logs/constants.js:98
__( "Delete", "redirection" ), // client/page/logs/constants.js:107
__( "Group", "redirection" ), // client/page/logs/constants.js:113
__( "Standard Display", "redirection" ), // client/page/logs/constants.js:118
__( "Compact Display", "redirection" ), // client/page/logs/constants.js:123
__( "Display All", "redirection" ), // client/page/logs/constants.js:128
__( "URL", "redirection" ), // client/page/logs/constants.js:136
__( "Count", "redirection" ), // client/page/logs/constants.js:136
__( "User Agent", "redirection" ), // client/page/logs/constants.js:140
__( "Count", "redirection" ), // client/page/logs/constants.js:140
__( "IP", "redirection" ), // client/page/logs/constants.js:144
__( "Count", "redirection" ), // client/page/logs/constants.js:144
__( "Date", "redirection" ), // client/page/logs/constants.js:148
__( "Method", "redirection" ), // client/page/logs/constants.js:149
__( "Domain", "redirection" ), // client/page/logs/constants.js:150
__( "URL", "redirection" ), // client/page/logs/constants.js:151
__( "Redirect By", "redirection" ), // client/page/logs/constants.js:152
__( "HTTP code", "redirection" ), // client/page/logs/constants.js:153
__( "Referrer", "redirection" ), // client/page/logs/constants.js:154
__( "User Agent", "redirection" ), // client/page/logs/constants.js:155
__( "Target", "redirection" ), // client/page/logs/constants.js:156
__( "IP", "redirection" ), // client/page/logs/constants.js:157
__( "Search URL", "redirection" ), // client/page/logs/constants.js:164
__( "Search exact URL", "redirection" ), // client/page/logs/constants.js:168
__( "Search referrer", "redirection" ), // client/page/logs/constants.js:172
__( "Search user agent", "redirection" ), // client/page/logs/constants.js:176
__( "Search IP", "redirection" ), // client/page/logs/constants.js:180
__( "Search target URL", "redirection" ), // client/page/logs/constants.js:184
__( "Search domain", "redirection" ), // client/page/logs/constants.js:188
__( "No grouping", "redirection" ), // client/page/logs/constants.js:196
__( "Group by URL", "redirection" ), // client/page/logs/constants.js:200
__( "Group by user agent", "redirection" ), // client/page/logs/constants.js:204
__( "Group by IP", "redirection" ), // client/page/logs/constants.js:211
__( "Method", "redirection" ), // client/page/logs/constants.js:220
__( "Redirect By", "redirection" ), // client/page/logs/constants.js:238
__( "WordPress", "redirection" ), // client/page/logs/constants.js:242
__( "Redirection", "redirection" ), // client/page/logs/constants.js:246
__( "RSS", "redirection" ), // client/page/logs/index.js:112
__( "Delete", "redirection" ), // client/page/logs/row-actions.js:30
__( "View Redirect", "redirection" ), // client/page/logs/row-actions.js:52
__( "Source URL", "redirection" ), // client/page/logs404/constants.js:13
__( "Count", "redirection" ), // client/page/logs404/constants.js:19
__( "User Agent", "redirection" ), // client/page/logs404/constants.js:29
__( "Count", "redirection" ), // client/page/logs404/constants.js:35
__( "IP", "redirection" ), // client/page/logs404/constants.js:45
__( "Count", "redirection" ), // client/page/logs404/constants.js:51
__( "Date", "redirection" ), // client/page/logs404/constants.js:60
__( "Method", "redirection" ), // client/page/logs404/constants.js:64
__( "Domain", "redirection" ), // client/page/logs404/constants.js:68
__( "Source URL", "redirection" ), // client/page/logs404/constants.js:72
__( "HTTP code", "redirection" ), // client/page/logs404/constants.js:77
__( "Referrer", "redirection" ), // client/page/logs404/constants.js:82
__( "User Agent", "redirection" ), // client/page/logs404/constants.js:87
__( "IP", "redirection" ), // client/page/logs404/constants.js:92
__( "Delete", "redirection" ), // client/page/logs404/constants.js:108
__( "Redirect All", "redirection" ), // client/page/logs404/constants.js:116
__( "Block IP", "redirection" ), // client/page/logs404/constants.js:120
__( "Redirect All", "redirection" ), // client/page/logs404/constants.js:132
__( "Ignore URL", "redirection" ), // client/page/logs404/constants.js:137
__( "No grouping", "redirection" ), // client/page/logs404/constants.js:147
__( "Group by URL", "redirection" ), // client/page/logs404/constants.js:151
__( "Group by user agent", "redirection" ), // client/page/logs404/constants.js:155
__( "Group by IP", "redirection" ), // client/page/logs404/constants.js:162
__( "Group", "redirection" ), // client/page/logs404/constants.js:171
__( "Standard Display", "redirection" ), // client/page/logs404/constants.js:177
__( "Compact Display", "redirection" ), // client/page/logs404/constants.js:182
__( "Display All", "redirection" ), // client/page/logs404/constants.js:187
__( "URL", "redirection" ), // client/page/logs404/constants.js:199
__( "Count", "redirection" ), // client/page/logs404/constants.js:199
__( "User Agent", "redirection" ), // client/page/logs404/constants.js:203
__( "Count", "redirection" ), // client/page/logs404/constants.js:203
__( "IP", "redirection" ), // client/page/logs404/constants.js:207
__( "Count", "redirection" ), // client/page/logs404/constants.js:207
__( "Date", "redirection" ), // client/page/logs404/constants.js:211
__( "Method", "redirection" ), // client/page/logs404/constants.js:212
__( "Domain", "redirection" ), // client/page/logs404/constants.js:213
__( "URL", "redirection" ), // client/page/logs404/constants.js:214
__( "HTTP code", "redirection" ), // client/page/logs404/constants.js:215
__( "Referrer", "redirection" ), // client/page/logs404/constants.js:216
__( "User Agent", "redirection" ), // client/page/logs404/constants.js:217
__( "IP", "redirection" ), // client/page/logs404/constants.js:218
__( "Method", "redirection" ), // client/page/logs404/constants.js:224
__( "HTTP Status Code", "redirection" ), // client/page/logs404/constants.js:242
__( "Search URL", "redirection" ), // client/page/logs404/constants.js:251
__( "Search exact URL", "redirection" ), // client/page/logs404/constants.js:255
__( "Search referrer", "redirection" ), // client/page/logs404/constants.js:259
__( "Search user agent", "redirection" ), // client/page/logs404/constants.js:263
__( "Search IP", "redirection" ), // client/page/logs404/constants.js:267
__( "Search domain", "redirection" ), // client/page/logs404/constants.js:271
__( "Add Redirect", "redirection" ), // client/page/logs404/create-redirect.js:53
__( "Are you sure you want to delete the selected items?", "redirection" ), // client/page/logs404/create-redirect.js:62
__( "Are you sure you want to delete this item?", "redirection" ), // client/page/logs404/create-redirect.js:63
__( "Delete Log Entries", "redirection" ), // client/page/logs404/create-redirect.js:71
__( "Delete logs for this entry", "redirection" ), // client/page/logs404/create-redirect.js:81
__( "Delete logs for these entries", "redirection" ), // client/page/logs404/create-redirect.js:82
__( "Delete", "redirection" ), // client/page/logs404/row-actions.js:42
__( "Add Redirect", "redirection" ), // client/page/logs404/row-actions.js:52
__( "Show All", "redirection" ), // client/page/logs404/row-actions.js:70
__( "Block IP", "redirection" ), // client/page/logs404/row-actions.js:77
__( "Ignore URL", "redirection" ), // client/page/logs404/row-actions.js:87
__( "Delete the plugin - are you sure?", "redirection" ), // client/page/options/delete-plugin.js:37
__( "Deleting the plugin will remove all your redirections, logs, and settings. Do this if you want to remove the plugin for good, or if you want to reset the plugin.", "redirection" ), // client/page/options/delete-plugin.js:38
__( "Once deleted your redirections will stop working. If they appear to continue working then please clear your browser cache.", "redirection" ), // client/page/options/delete-plugin.js:39
__( "Yes! Delete the plugin", "redirection" ), // client/page/options/delete-plugin.js:41
__( "No! Don't delete the plugin", "redirection" ), // client/page/options/delete-plugin.js:41
__( "Delete Redirection", "redirection" ), // client/page/options/delete-plugin.js:52
__( "Selecting this option will delete all redirections, all logs, and any options associated with the Redirection plugin.  Make sure this is what you want to do.", "redirection" ), // client/page/options/delete-plugin.js:54
__( "Delete", "redirection" ), // client/page/options/delete-plugin.js:55
__( "You've supported this plugin - thank you!", "redirection" ), // client/page/options/donation.js:82
__( "I'd like to support some more.", "redirection" ), // client/page/options/donation.js:83
__( "Redirection is free to use - life is wonderful and lovely! It has required a great deal of time and effort to develop and you can help support this development by {{strong}}making a small donation{{/strong}}.", "redirection" ), // client/page/options/donation.js:99
__( "You get useful software and I get to carry on making it better.", "redirection" ), // client/page/options/donation.js:104
__( "Support 💰", "redirection" ), // client/page/options/donation.js:127
__( "Plugin Support", "redirection" ), // client/page/options/donation.js:139
__( "Newsletter", "redirection" ), // client/page/options/newsletter.js:23
__( "Thanks for subscribing! {{a}}Click here{{/a}} if you need to return to your subscription.", "redirection" ), // client/page/options/newsletter.js:25
__( "Newsletter", "redirection" ), // client/page/options/newsletter.js:36
__( "Want to keep up to date with changes to Redirection?", "redirection" ), // client/page/options/newsletter.js:38
__( "Sign up for the tiny Redirection newsletter - a low volume newsletter about new features and changes to the plugin. Ideal if you want to test beta changes before release.", "redirection" ), // client/page/options/newsletter.js:39
__( "Your email address:", "redirection" ), // client/page/options/newsletter.js:43
__( "Status", "redirection" ), // client/page/redirects/constants.js:11
__( "URL", "redirection" ), // client/page/redirects/constants.js:16
__( "Match Type", "redirection" ), // client/page/redirects/constants.js:21
__( "Action Type", "redirection" ), // client/page/redirects/constants.js:26
__( "Code", "redirection" ), // client/page/redirects/constants.js:31
__( "Group", "redirection" ), // client/page/redirects/constants.js:36
__( "Pos", "redirection" ), // client/page/redirects/constants.js:41
__( "Hits", "redirection" ), // client/page/redirects/constants.js:45
__( "Last Access", "redirection" ), // client/page/redirects/constants.js:49
__( "Delete", "redirection" ), // client/page/redirects/constants.js:56
__( "Enable", "redirection" ), // client/page/redirects/constants.js:60
__( "Disable", "redirection" ), // client/page/redirects/constants.js:64
__( "Reset hits", "redirection" ), // client/page/redirects/constants.js:68
__( "Source", "redirection" ), // client/page/redirects/constants.js:73
__( "URL options", "redirection" ), // client/page/redirects/constants.js:74
__( "Query Parameters", "redirection" ), // client/page/redirects/constants.js:75
__( "Title", "redirection" ), // client/page/redirects/constants.js:76
__( "Target", "redirection" ), // client/page/redirects/constants.js:77
__( "HTTP code", "redirection" ), // client/page/redirects/constants.js:78
__( "Match Type", "redirection" ), // client/page/redirects/constants.js:79
__( "Position", "redirection" ), // client/page/redirects/constants.js:80
__( "Hits", "redirection" ), // client/page/redirects/constants.js:81
__( "Last Access", "redirection" ), // client/page/redirects/constants.js:82
__( "Status", "redirection" ), // client/page/redirects/constants.js:83
__( "Action Type", "redirection" ), // client/page/redirects/constants.js:84
__( "Group", "redirection" ), // client/page/redirects/constants.js:85
__( "Standard Display", "redirection" ), // client/page/redirects/constants.js:91
__( "Compact Display", "redirection" ), // client/page/redirects/constants.js:96
__( "Display All", "redirection" ), // client/page/redirects/constants.js:101
__( "Status", "redirection" ), // client/page/redirects/constants.js:108
__( "Enabled", "redirection" ), // client/page/redirects/constants.js:112
__( "Disabled", "redirection" ), // client/page/redirects/constants.js:116
__( "URL match", "redirection" ), // client/page/redirects/constants.js:122
__( "Regular Expression", "redirection" ), // client/page/redirects/constants.js:126
__( "Plain", "redirection" ), // client/page/redirects/constants.js:130
__( "Match Type", "redirection" ), // client/page/redirects/constants.js:136
__( "Action Type", "redirection" ), // client/page/redirects/constants.js:141
__( "HTTP Status Code", "redirection" ), // client/page/redirects/constants.js:147
__( "Last Accessed", "redirection" ), // client/page/redirects/constants.js:152
__( "Never accessed", "redirection" ), // client/page/redirects/constants.js:156
__( "Not accessed in last month", "redirection" ), // client/page/redirects/constants.js:160
__( "Not accessed in last year", "redirection" ), // client/page/redirects/constants.js:164
__( "Search URL", "redirection" ), // client/page/redirects/constants.js:174
__( "Search target URL", "redirection" ), // client/page/redirects/constants.js:178
__( "Search title", "redirection" ), // client/page/redirects/constants.js:182
__( "Add new redirection", "redirection" ), // client/page/redirects/create.js:27
__( "Add Redirect", "redirection" ), // client/page/redirects/create.js:32
__( "All groups", "redirection" ), // client/page/redirects/index.js:62
__( "Edit", "redirection" ), // client/page/redirects/row-actions.js:28
__( "Delete", "redirection" ), // client/page/redirects/row-actions.js:32
__( "Disable", "redirection" ), // client/page/redirects/row-actions.js:37
__( "Enable", "redirection" ), // client/page/redirects/row-actions.js:39
__( "Check Redirect", "redirection" ), // client/page/redirects/row-actions.js:44
__( "Options on this page can cause problems if used incorrectly. You can {{link}}temporarily disable them{{/link}} to make changes.", "redirection" ), // client/page/site/index.js:72
__( "Update", "redirection" ), // client/page/site/index.js:107
__( "Database version", "redirection" ), // client/page/support/debug.js:68
__( "Do not change unless advised to do so!", "redirection" ), // client/page/support/debug.js:79
__( "Save", "redirection" ), // client/page/support/debug.js:81
__( "IP Headers", "redirection" ), // client/page/support/debug.js:88
__( "Need help?", "redirection" ), // client/page/support/help.js:14
__( "Full documentation for Redirection can be found at {{site}}https://redirection.me{{/site}}. If you have a problem please check the {{faq}}FAQ{{/faq}} first.", "redirection" ), // client/page/support/help.js:16
__( "If you want to report a bug please read the {{report}}Reporting Bugs{{/report}} guide.", "redirection" ), // client/page/support/help.js:28
__( "Please note that any support is provide on as-time-is-available basis and is not guaranteed. I do not provide paid support.", "redirection" ), // client/page/support/help.js:50
__( "If you want to submit information that you don't want in a public repository then send it directly via {{email}}email{{/email}} - include as much information as you can!", "redirection" ), // client/page/support/help.js:55
__( "Need to search and replace?", "redirection" ), // client/page/support/help.js:71
__( "The companion plugin Search Regex allows you to search and replace data on your site. It also supports Redirection, and is handy if you want to bulk update a lot of redirects.", "redirection" ), // client/page/support/help.js:73
__( "Redirect Tester", "redirection" ), // client/page/support/http-tester.js:41
__( "Sometimes your browser can cache a URL, making it hard to know if it's working as expected. Use this service from {{link}}redirect.li{{/link}} to get accurate results.", "redirection" ), // client/page/support/http-tester.js:44
__( "URL", "redirection" ), // client/page/support/http-tester.js:50
__( "Enter full URL, including http:// or https://", "redirection" ), // client/page/support/http-tester.js:58
__( "Check", "redirection" ), // client/page/support/http-tester.js:64
__( "If the magic button doesn't work then you should read the error and see if you can fix it manually, otherwise follow the 'Need help' section below.", "redirection" ), // client/page/support/plugin-status.js:22
__( "⚡️ Magic fix ⚡️", "redirection" ), // client/page/support/plugin-status.js:27
__( "Good", "redirection" ), // client/page/support/plugin-status.js:39
__( "Problem", "redirection" ), // client/page/support/plugin-status.js:39
__( "WordPress REST API", "redirection" ), // client/page/support/status.js:29
__( "Redirection communicates with WordPress through the WordPress REST API. This is a standard part of WordPress, and you will experience problems if you cannot use it.", "redirection" ), // client/page/support/status.js:30
__( "Plugin Status", "redirection" ), // client/page/support/status.js:33
__( "Plugin Debug", "redirection" ), // client/page/support/status.js:38
__( "This information is provided for debugging purposes. Be careful making any changes.", "redirection" ), // client/page/support/status.js:39
__( "Redirection saved", "redirection" ), // client/state/message/reducer.js:49
__( "Log deleted", "redirection" ), // client/state/message/reducer.js:50
__( "Settings saved", "redirection" ), // client/state/message/reducer.js:51
__( "Group saved", "redirection" ), // client/state/message/reducer.js:52
__( "404 deleted", "redirection" ), // client/state/message/reducer.js:53
__( "View notice", "redirection" ), // client/wp-plugin-components/snackbar/index.js:75
__( "Add File", "redirection" ), // client/wp-plugin-components/uploader/content.js:45
__( "Upload", "redirection" ), // client/wp-plugin-components/uploader/content.js:54
__( "Cancel", "redirection" ), // client/wp-plugin-components/uploader/content.js:57
__( "View Data", "redirection" ), // client/component/log-page/log-actions/extra-data.js:28
__( "Geo Info", "redirection" ), // client/component/log-page/log-actions/geo-map.js:32
__( "Agent Info", "redirection" ), // client/component/log-page/log-actions/user-agent.js:32
__( "Filter by IP", "redirection" ), // client/component/log-page/log-columns/column-ip.js:60
__( "Redirection", "redirection" ), // client/component/log-page/log-columns/index.js:77
__( "Logged In", "redirection" ), // client/component/redirect-edit/action/login.js:20
__( "Target URL when matched (empty to ignore)", "redirection" ), // client/component/redirect-edit/action/login.js:21
__( "Logged Out", "redirection" ), // client/component/redirect-edit/action/login.js:23
__( "Target URL when not matched (empty to ignore)", "redirection" ), // client/component/redirect-edit/action/login.js:24
__( "Matched Target", "redirection" ), // client/component/redirect-edit/action/url-from.js:20
__( "Target URL when matched (empty to ignore)", "redirection" ), // client/component/redirect-edit/action/url-from.js:21
__( "Unmatched Target", "redirection" ), // client/component/redirect-edit/action/url-from.js:23
__( "Target URL when not matched (empty to ignore)", "redirection" ), // client/component/redirect-edit/action/url-from.js:24
__( "Target URL", "redirection" ), // client/component/redirect-edit/action/url.js:20
__( "User Agent", "redirection" ), // client/component/redirect-edit/match/agent.js:51
__( "Match against this browser user agent", "redirection" ), // client/component/redirect-edit/match/agent.js:52
__( "Custom", "redirection" ), // client/component/redirect-edit/match/agent.js:55
__( "Mobile", "redirection" ), // client/component/redirect-edit/match/agent.js:56
__( "Feed Readers", "redirection" ), // client/component/redirect-edit/match/agent.js:57
__( "Libraries", "redirection" ), // client/component/redirect-edit/match/agent.js:58
__( "Regex", "redirection" ), // client/component/redirect-edit/match/agent.js:62
__( "Cookie", "redirection" ), // client/component/redirect-edit/match/cookie.js:20
__( "Cookie name", "redirection" ), // client/component/redirect-edit/match/cookie.js:21
__( "Cookie value", "redirection" ), // client/component/redirect-edit/match/cookie.js:22
__( "Regex", "redirection" ), // client/component/redirect-edit/match/cookie.js:25
__( "Filter Name", "redirection" ), // client/component/redirect-edit/match/custom.js:18
__( "WordPress filter name", "redirection" ), // client/component/redirect-edit/match/custom.js:19
__( "HTTP Header", "redirection" ), // client/component/redirect-edit/match/header.js:50
__( "Header name", "redirection" ), // client/component/redirect-edit/match/header.js:51
__( "Header value", "redirection" ), // client/component/redirect-edit/match/header.js:52
__( "Custom", "redirection" ), // client/component/redirect-edit/match/header.js:55
__( "Accept Language", "redirection" ), // client/component/redirect-edit/match/header.js:56
__( "Regex", "redirection" ), // client/component/redirect-edit/match/header.js:60
__( "Note it is your responsibility to pass HTTP headers to PHP. Please contact your hosting provider for support about this.", "redirection" ), // client/component/redirect-edit/match/header.js:67
__( "IP", "redirection" ), // client/component/redirect-edit/match/ip.js:22
__( "Enter IP addresses (one per line)", "redirection" ), // client/component/redirect-edit/match/ip.js:23
__( "Language", "redirection" ), // client/component/redirect-edit/match/language.js:18
__( "Comma separated list of languages to match against (i.e. da, en-GB)", "redirection" ), // client/component/redirect-edit/match/language.js:19
__( "Page Type", "redirection" ), // client/component/redirect-edit/match/page.js:17
__( "Only the 404 page type is currently supported.", "redirection" ), // client/component/redirect-edit/match/page.js:19
__( "Please do not try and redirect all your 404s - this is not a good thing to do.", "redirection" ), // client/component/redirect-edit/match/page.js:20
__( "Referrer", "redirection" ), // client/component/redirect-edit/match/referrer.js:19
__( "Match against this browser referrer text", "redirection" ), // client/component/redirect-edit/match/referrer.js:20
__( "Regex", "redirection" ), // client/component/redirect-edit/match/referrer.js:23
__( "Role", "redirection" ), // client/component/redirect-edit/match/role.js:18
__( "Enter role or capability value", "redirection" ), // client/component/redirect-edit/match/role.js:19
__( "Server", "redirection" ), // client/component/redirect-edit/match/server.js:18
__( "Enter server URL to match against", "redirection" ), // client/component/redirect-edit/match/server.js:19
__( "Select All", "redirection" ), // client/component/table/header/check-column.js:23
_n( "%s item", "%s items", 1, "redirection" ), // client/component/table/navigation/navigation-pages.js:33
__( "%1d of %1d selected. {{all}}Select All.{{/all}}", "redirection" ), // client/component/table/navigation/navigation-pages.js:36
__( "%1d of %1d selected. {{all}}Clear All.{{/all}}", "redirection" ), // client/component/table/navigation/navigation-pages.js:43
__( "First page", "redirection" ), // client/component/table/navigation/pagination-links.js:39
__( "Prev page", "redirection" ), // client/component/table/navigation/pagination-links.js:47
__( "Current Page", "redirection" ), // client/component/table/navigation/pagination-links.js:55
__( "of %(page)s", "redirection" ), // client/component/table/navigation/pagination-links.js:72
__( "Next page", "redirection" ), // client/component/table/navigation/pagination-links.js:84
__( "Last page", "redirection" ), // client/component/table/navigation/pagination-links.js:92
__( "Nothing to display.", "redirection" ), // client/component/table/row/empty-row.js:13
__( "Sorry, something went wrong loading the data - please try again", "redirection" ), // client/component/table/row/failed-row.js:14
__( "Name", "redirection" ), // client/page/groups/columns/edit.js:41
__( "Module", "redirection" ), // client/page/groups/columns/edit.js:53
__( "Save", "redirection" ), // client/page/groups/columns/edit.js:71
__( "Cancel", "redirection" ), // client/page/groups/columns/edit.js:78
__( "Note that you will need to set the Apache module path in your Redirection options.", "redirection" ), // client/page/groups/columns/edit.js:86
__( "Filter on: %(type)s", "redirection" ), // client/page/groups/columns/module.js:24
__( "I'm a nice person and I have helped support the author of this plugin", "redirection" ), // client/page/options/options-form/index.js:57
__( "Update", "redirection" ), // client/page/options/options-form/index.js:75
__( "No logs", "redirection" ), // client/page/options/options-form/log-options.js:15
__( "A day", "redirection" ), // client/page/options/options-form/log-options.js:16
__( "A week", "redirection" ), // client/page/options/options-form/log-options.js:17
__( "A month", "redirection" ), // client/page/options/options-form/log-options.js:18
__( "Two months", "redirection" ), // client/page/options/options-form/log-options.js:19
__( "Forever", "redirection" ), // client/page/options/options-form/log-options.js:20
__( "No IP logging", "redirection" ), // client/page/options/options-form/log-options.js:23
__( "Full IP logging", "redirection" ), // client/page/options/options-form/log-options.js:24
__( "Anonymize IP (mask last part)", "redirection" ), // client/page/options/options-form/log-options.js:25
__( "Logs", "redirection" ), // client/page/options/options-form/log-options.js:36
__( "Redirect Logs", "redirection" ), // client/page/options/options-form/log-options.js:39
__( "(time to keep logs for)", "redirection" ), // client/page/options/options-form/log-options.js:46
__( "404 Logs", "redirection" ), // client/page/options/options-form/log-options.js:48
__( "(time to keep logs for)", "redirection" ), // client/page/options/options-form/log-options.js:55
__( "IP Logging", "redirection" ), // client/page/options/options-form/log-options.js:57
__( "(IP logging level)", "redirection" ), // client/page/options/options-form/log-options.js:64
__( "Logging", "redirection" ), // client/page/options/options-form/log-options.js:66
__( "Log \"external\" redirects - those not from Redirection. This can increase your log size and contains no user information.", "redirection" ), // client/page/options/options-form/log-options.js:76
__( "Track redirect hits and date of last access. Contains no user information.", "redirection" ), // client/page/options/options-form/log-options.js:84
__( "Capture HTTP header information with logs (except cookies). It may include user information, and could increase your log size.", "redirection" ), // client/page/options/options-form/log-options.js:90
__( "Redirection stores no user identifiable information other than what is configured above. It is your responsibility to ensure your site meets any applicable {{link}}privacy requirements{{/link}}.", "redirection" ), // client/page/options/options-form/log-options.js:99
__( "Default REST API", "redirection" ), // client/page/options/options-form/other-options.js:15
__( "Raw REST API", "redirection" ), // client/page/options/options-form/other-options.js:16
__( "Relative REST API", "redirection" ), // client/page/options/options-form/other-options.js:17
__( "Upgrade manually when prompted", "redirection" ), // client/page/options/options-form/other-options.js:24
__( "Automatically upgrade on admin pages", "redirection" ), // client/page/options/options-form/other-options.js:28
__( "Advanced", "redirection" ), // client/page/options/options-form/other-options.js:41
__( "RSS Token", "redirection" ), // client/page/options/options-form/other-options.js:44
__( "A unique token allowing feed readers access to Redirection log RSS (leave blank to auto-generate)", "redirection" ), // client/page/options/options-form/other-options.js:48
__( "Apache .htaccess", "redirection" ), // client/page/options/options-form/other-options.js:53
__( "Redirects added to an Apache group can be saved to an {{code}}.htaccess{{/code}} file by adding the full path here. For reference, your WordPress is installed to {{code}}%(installed)s{{/code}}.", "redirection" ), // client/page/options/options-form/other-options.js:66
__( "Unable to save .htaccess file", "redirection" ), // client/page/options/options-form/other-options.js:81
__( "REST API", "redirection" ), // client/page/options/options-form/other-options.js:86
__( "How Redirection uses the REST API - don't change unless necessary", "redirection" ), // client/page/options/options-form/other-options.js:90
__( "Data Upgrade", "redirection" ), // client/page/options/options-form/other-options.js:93
__( "Decide how Redirection updates itself, if needed.", "redirection" ), // client/page/options/options-form/other-options.js:102
__( "Monitor changes to %(type)s", "redirection" ), // client/page/options/options-form/url-monitor.js:42
__( "URL Monitor", "redirection" ), // client/page/options/options-form/url-monitor.js:98
__( "URL Monitor Changes", "redirection" ), // client/page/options/options-form/url-monitor.js:103
__( "Save changes to this group", "redirection" ), // client/page/options/options-form/url-monitor.js:106
__( "For example \"/amp\"", "redirection" ), // client/page/options/options-form/url-monitor.js:113
__( "Create associated redirect (added to end of URL)", "redirection" ), // client/page/options/options-form/url-monitor.js:116
__( "Exact match in any order", "redirection" ), // client/page/options/options-form/url-options.js:16
__( "Ignore all query parameters", "redirection" ), // client/page/options/options-form/url-options.js:17
__( "Ignore and pass all query parameters", "redirection" ), // client/page/options/options-form/url-options.js:18
__( "Never cache", "redirection" ), // client/page/options/options-form/url-options.js:21
__( "An hour", "redirection" ), // client/page/options/options-form/url-options.js:22
__( "A day", "redirection" ), // client/page/options/options-form/url-options.js:23
__( "A week", "redirection" ), // client/page/options/options-form/url-options.js:24
__( "Forever", "redirection" ), // client/page/options/options-form/url-options.js:25
__( "URL", "redirection" ), // client/page/options/options-form/url-options.js:36
__( "Default URL settings", "redirection" ), // client/page/options/options-form/url-options.js:46
__( "Applies to all redirections unless you configure them otherwise.", "redirection" ), // client/page/options/options-form/url-options.js:47
__( "Case insensitive matches (i.e. {{code}}/Exciting-Post{{/code}} will match {{code}}/exciting-post{{/code}})", "redirection" ), // client/page/options/options-form/url-options.js:51
__( "Ignore trailing slashes (i.e. {{code}}/exciting-post/{{/code}} will match {{code}}/exciting-post{{/code}})", "redirection" ), // client/page/options/options-form/url-options.js:70
__( "Default query matching", "redirection" ), // client/page/options/options-form/url-options.js:81
__( "Applies to all redirections unless you configure them otherwise.", "redirection" ), // client/page/options/options-form/url-options.js:82
__( "Exact - matches the query parameters exactly defined in your source, in any order", "redirection" ), // client/page/options/options-form/url-options.js:88
__( "Ignore - as exact, but ignores any query parameters not in your source", "redirection" ), // client/page/options/options-form/url-options.js:92
__( "Pass - as ignore, but also copies the query parameters to the target", "redirection" ), // client/page/options/options-form/url-options.js:93
__( "Auto-generate URL", "redirection" ), // client/page/options/options-form/url-options.js:96
__( "Used to auto-generate a URL if no URL is given. Use the special tags {{code}}\$dec\${{/code}} or {{code}}\$hex\${{/code}} to insert a unique ID instead", "redirection" ), // client/page/options/options-form/url-options.js:106
__( "HTTP Cache Header", "redirection" ), // client/page/options/options-form/url-options.js:117
__( "How long to cache redirected 301 URLs (via \"Expires\" HTTP header)", "redirection" ), // client/page/options/options-form/url-options.js:126
__( "Redirect Caching", "redirection" ), // client/page/options/options-form/url-options.js:129
__( "(beta) Enable caching of redirects via WordPress object cache. Can improve performance. Requires an object cache.", "redirection" ), // client/page/options/options-form/url-options.js:134
__( "pass", "redirection" ), // client/page/redirects/columns/code.js:16
__( "Exact Query", "redirection" ), // client/page/redirects/columns/source-query.js:21
__( "Ignore Query", "redirection" ), // client/page/redirects/columns/source-query.js:24
__( "Ignore & Pass Query", "redirection" ), // client/page/redirects/columns/source-query.js:26
__( "Site Aliases", "redirection" ), // client/page/site/aliases/index.js:39
__( "A site alias is another domain that you want to be redirected to this site. For example, an old domain, or a subdomain. This will redirect all URLs, including WordPress login and admin.", "redirection" ), // client/page/site/aliases/index.js:41
__( "You will need to configure your system (DNS and server) to pass requests for these domains to this WordPress install.", "redirection" ), // client/page/site/aliases/index.js:42
__( "Aliased Domain", "redirection" ), // client/page/site/aliases/index.js:47
__( "Alias", "redirection" ), // client/page/site/aliases/index.js:48
__( "No aliases", "redirection" ), // client/page/site/aliases/index.js:64
__( "Add Alias", "redirection" ), // client/page/site/aliases/index.js:68
__( "Don't set a preferred domain - {{code}}%(site)s{{/code}}", "redirection" ), // client/page/site/canonical/index.js:10
__( "Remove www from domain - {{code}}%(siteWWW)s{{/code}} ⇒ {{code}}%(site)s{{/code}}", "redirection" ), // client/page/site/canonical/index.js:21
__( "Add www to domain - {{code}}%(site)s{{/code}} ⇒ {{code}}%(siteWWW)s{{/code}}", "redirection" ), // client/page/site/canonical/index.js:33
__( "Canonical Settings", "redirection" ), // client/page/site/canonical/index.js:85
__( "Force a redirect from HTTP to HTTPS - {{code}}%(site)s{{/code}} ⇒ {{code}}%(siteHTTPS)s{{/code}}", "redirection" ), // client/page/site/canonical/index.js:89
__( "{{strong}}Warning{{/strong}}: ensure your HTTPS is working before forcing a redirect.", "redirection" ), // client/page/site/canonical/index.js:102
__( "Preferred domain", "redirection" ), // client/page/site/canonical/index.js:109
__( "You should update your site URL to match your canonical settings: {{code}}%(current)s{{/code}} ⇒ {{code}}%(site)s{{/code}}", "redirection" ), // client/page/site/canonical/index.js:119
__( "Site", "redirection" ), // client/page/site/headers/header.js:24
__( "Redirect", "redirection" ), // client/page/site/headers/header.js:28
__( "General", "redirection" ), // client/page/site/headers/header.js:225
__( "Custom Header", "redirection" ), // client/page/site/headers/header.js:258
__( "Add Header", "redirection" ), // client/page/site/headers/index.js:17
__( "Add Security Presets", "redirection" ), // client/page/site/headers/index.js:21
__( "Add CORS Presets", "redirection" ), // client/page/site/headers/index.js:25
__( "HTTP Headers", "redirection" ), // client/page/site/headers/index.js:84
__( "Site headers are added across your site, including redirects. Redirect headers are only added to redirects.", "redirection" ), // client/page/site/headers/index.js:85
__( "Location", "redirection" ), // client/page/site/headers/index.js:90
__( "Header", "redirection" ), // client/page/site/headers/index.js:91
__( "No headers", "redirection" ), // client/page/site/headers/index.js:106
__( "Note that some HTTP headers are set by your server and cannot be changed.", "redirection" ), // client/page/site/headers/index.js:117
__( "Permalink Migration", "redirection" ), // client/page/site/permalink/index.js:39
__( "Enter old permalinks structures to automatically migrate them to your current one.", "redirection" ), // client/page/site/permalink/index.js:40
__( "Note: this is in beta and will only migrate posts. Certain permalinks will not work. If yours does not work then you will need to wait until it is out of beta.", "redirection" ), // client/page/site/permalink/index.js:41
__( "Permalinks", "redirection" ), // client/page/site/permalink/index.js:46
__( "No migrated permalinks", "redirection" ), // client/page/site/permalink/index.js:62
__( "Add Permalink", "redirection" ), // client/page/site/permalink/index.js:70
__( "Relocate Site", "redirection" ), // client/page/site/relocate/index.js:31
__( "Want to redirect the entire site? Enter a domain to redirect everything, except WordPress login and admin. Enabling this option will disable any site aliases or canonical settings.", "redirection" ), // client/page/site/relocate/index.js:32
__( "Relocate to domain", "redirection" ), // client/page/site/relocate/index.js:34
__( "Show debug", "redirection" ), // client/wp-plugin-components/error/debug/index.js:70
__( "Debug Information", "redirection" ), // client/wp-plugin-components/error/debug/index.js:80
__( "WordPress did not return a response. This could mean an error occurred or that the request was blocked. Please check your server error_log.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:76
__( "Your REST API is probably being blocked by a security plugin. Please disable this, or configure it to allow REST API requests.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:88
__( "Read this REST API guide for more information.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:94
__( "Your WordPress REST API is returning a 404 page. This is almost certainly an external plugin or server configuration issue.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:105
__( "You will will need to fix this on your site. Redirection is not causing the error.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:111
__( "Can you access your {{api}}REST API{{/api}} without it redirecting?.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:116
__( "Check your {{link}}Site Health{{/link}} and fix any issues.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:123
__( "Your server configuration is blocking access to the REST API.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:129
__( "A security plugin or firewall is blocking access. You will need to whitelist the REST API.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:131
__( "Read this REST API guide for more information.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:138
__( "Your REST API is being redirected. Please remove the redirection for the API.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:146
__( "Your server has rejected the request for being too big. You will need to reconfigure it to continue.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:152
__( "An unknown error occurred.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:160
__( "Your REST API is showing a deprecated PHP error. Please fix this error.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:167
__( "This could be a security plugin, or your server is out of memory or has an external error. Please check your server error log", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:177
__( "Read this REST API guide for more information.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:183
__( "Your WordPress REST API has been disabled. You will need to enable it to continue.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:191
__( "WordPress returned an unexpected message. This could be a PHP error from another plugin, or data inserted by your theme.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:201
__( "Possible cause", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:207
__( "Unable to make request due to browser security. This is typically because your WordPress and Site URL settings are inconsistent, or the request was blocked by your site CORS policy.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:219
__( "Read this REST API guide for more information.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:225
__( "Your REST API appears to be cached and this will cause problems. Please exclude your REST API from your caching system.", "redirection" ), // client/wp-plugin-components/error/decode-error/index.js:237
__( "Bad data", "redirection" ), // client/wp-plugin-components/error/display/error-api.js:17
__( "There was a problem making a request to your site. This could indicate you provided data that did not match requirements, or that the plugin sent a bad request.", "redirection" ), // client/wp-plugin-components/error/display/error-api.js:19
__( "Please review your data and try again.", "redirection" ), // client/wp-plugin-components/error/display/error-api.js:20
__( "REST API 404", "redirection" ), // client/wp-plugin-components/error/display/error-default.js:18
__( "Something went wrong 🙁", "redirection" ), // client/wp-plugin-components/error/display/error-default.js:21
__( "Something went wrong 🙁", "redirection" ), // client/wp-plugin-components/error/display/error-fixed.js:19
__( "Something went wrong 🙁", "redirection" ), // client/wp-plugin-components/error/display/error-known.js:26
__( "You are using an old or cached session", "redirection" ), // client/wp-plugin-components/error/display/error-nonce.js:17
__( "This is usually fixed by doing one of the following:", "redirection" ), // client/wp-plugin-components/error/display/error-nonce.js:19
__( "Reload the page - your current session is old.", "redirection" ), // client/wp-plugin-components/error/display/error-nonce.js:21
__( "Log out, clear your browser cache, and log in again - your browser has cached an old session.", "redirection" ), // client/wp-plugin-components/error/display/error-nonce.js:23
__( "Your admin pages are being cached. Clear this cache and try again. There may be multiple caches involved.", "redirection" ), // client/wp-plugin-components/error/display/error-nonce.js:28
__( "All", "redirection" ), // client/page/site/headers/types/multi-choice.js:26
__( "Values", "redirection" ), // client/page/site/headers/types/multi-choice.js:29
__( "Value", "redirection" ), // client/page/site/headers/types/plain-value.js:10
);
/* THIS IS THE END OF THE GENERATED FILE */