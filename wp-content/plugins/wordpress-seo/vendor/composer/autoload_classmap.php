<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installers\\AglInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php',
    'Composer\\Installers\\AkauntingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AkauntingInstaller.php',
    'Composer\\Installers\\AnnotateCmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
    'Composer\\Installers\\AsgardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
    'Composer\\Installers\\AttogramInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
    'Composer\\Installers\\BaseInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
    'Composer\\Installers\\BitrixInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
    'Composer\\Installers\\BonefishInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
    'Composer\\Installers\\BotbleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BotbleInstaller.php',
    'Composer\\Installers\\CakePHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
    'Composer\\Installers\\ChefInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
    'Composer\\Installers\\CiviCrmInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
    'Composer\\Installers\\ClanCatsFrameworkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
    'Composer\\Installers\\CockpitInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
    'Composer\\Installers\\CodeIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
    'Composer\\Installers\\Concrete5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
    'Composer\\Installers\\ConcreteCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ConcreteCMSInstaller.php',
    'Composer\\Installers\\CroogoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
    'Composer\\Installers\\DecibelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
    'Composer\\Installers\\DframeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
    'Composer\\Installers\\DokuWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
    'Composer\\Installers\\DolibarrInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
    'Composer\\Installers\\DrupalInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
    'Composer\\Installers\\ElggInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
    'Composer\\Installers\\EliasisInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
    'Composer\\Installers\\ExpressionEngineInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
    'Composer\\Installers\\EzPlatformInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
    'Composer\\Installers\\ForkCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ForkCMSInstaller.php',
    'Composer\\Installers\\FuelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
    'Composer\\Installers\\FuelphpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
    'Composer\\Installers\\GravInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php',
    'Composer\\Installers\\HuradInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
    'Composer\\Installers\\ImageCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
    'Composer\\Installers\\Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php',
    'Composer\\Installers\\ItopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
    'Composer\\Installers\\KanboardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
    'Composer\\Installers\\KnownInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
    'Composer\\Installers\\KodiCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
    'Composer\\Installers\\KohanaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
    'Composer\\Installers\\LanManagementSystemInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
    'Composer\\Installers\\LaravelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
    'Composer\\Installers\\LavaLiteInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
    'Composer\\Installers\\LithiumInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
    'Composer\\Installers\\MODULEWorkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
    'Composer\\Installers\\MODXEvoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
    'Composer\\Installers\\MagentoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
    'Composer\\Installers\\MajimaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
    'Composer\\Installers\\MakoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
    'Composer\\Installers\\MantisBTInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
    'Composer\\Installers\\MatomoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MatomoInstaller.php',
    'Composer\\Installers\\MauticInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
    'Composer\\Installers\\MayaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
    'Composer\\Installers\\MediaWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
    'Composer\\Installers\\MiaoxingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php',
    'Composer\\Installers\\MicroweberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
    'Composer\\Installers\\ModxInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
    'Composer\\Installers\\MoodleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
    'Composer\\Installers\\OctoberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
    'Composer\\Installers\\OntoWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
    'Composer\\Installers\\OsclassInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
    'Composer\\Installers\\OxidInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
    'Composer\\Installers\\PPIInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
    'Composer\\Installers\\PantheonInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PantheonInstaller.php',
    'Composer\\Installers\\PhiftyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
    'Composer\\Installers\\PhpBBInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
    'Composer\\Installers\\PiwikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
    'Composer\\Installers\\PlentymarketsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
    'Composer\\Installers\\Plugin' => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php',
    'Composer\\Installers\\PortoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
    'Composer\\Installers\\PrestashopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
    'Composer\\Installers\\ProcessWireInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
    'Composer\\Installers\\PuppetInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
    'Composer\\Installers\\PxcmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
    'Composer\\Installers\\RadPHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
    'Composer\\Installers\\ReIndexInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
    'Composer\\Installers\\Redaxo5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
    'Composer\\Installers\\RedaxoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
    'Composer\\Installers\\RoundcubeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
    'Composer\\Installers\\SMFInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
    'Composer\\Installers\\ShopwareInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
    'Composer\\Installers\\SilverStripeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
    'Composer\\Installers\\SiteDirectInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
    'Composer\\Installers\\StarbugInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
    'Composer\\Installers\\SyDESInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
    'Composer\\Installers\\SyliusInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
    'Composer\\Installers\\TaoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
    'Composer\\Installers\\TastyIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php',
    'Composer\\Installers\\TheliaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
    'Composer\\Installers\\TuskInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
    'Composer\\Installers\\UserFrostingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
    'Composer\\Installers\\VanillaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
    'Composer\\Installers\\VgmcpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
    'Composer\\Installers\\WHMCSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
    'Composer\\Installers\\WinterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php',
    'Composer\\Installers\\WolfCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
    'Composer\\Installers\\WordPressInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
    'Composer\\Installers\\YawikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
    'Composer\\Installers\\ZendInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
    'Composer\\Installers\\ZikulaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
    'WPSEO_Abstract_Capability_Manager' => $baseDir . '/admin/capabilities/class-abstract-capability-manager.php',
    'WPSEO_Abstract_Metabox_Tab_With_Sections' => $baseDir . '/admin/metabox/class-abstract-sectioned-metabox-tab.php',
    'WPSEO_Abstract_Post_Filter' => $baseDir . '/admin/filters/class-abstract-post-filter.php',
    'WPSEO_Abstract_Role_Manager' => $baseDir . '/admin/roles/class-abstract-role-manager.php',
    'WPSEO_Addon_Manager' => $baseDir . '/inc/class-addon-manager.php',
    'WPSEO_Admin' => $baseDir . '/admin/class-admin.php',
    'WPSEO_Admin_Asset' => $baseDir . '/admin/class-asset.php',
    'WPSEO_Admin_Asset_Analysis_Worker_Location' => $baseDir . '/admin/class-admin-asset-analysis-worker-location.php',
    'WPSEO_Admin_Asset_Dev_Server_Location' => $baseDir . '/admin/class-admin-asset-dev-server-location.php',
    'WPSEO_Admin_Asset_Location' => $baseDir . '/admin/class-admin-asset-location.php',
    'WPSEO_Admin_Asset_Manager' => $baseDir . '/admin/class-admin-asset-manager.php',
    'WPSEO_Admin_Asset_SEO_Location' => $baseDir . '/admin/class-admin-asset-seo-location.php',
    'WPSEO_Admin_Bar_Menu' => $baseDir . '/inc/class-wpseo-admin-bar-menu.php',
    'WPSEO_Admin_Editor_Specific_Replace_Vars' => $baseDir . '/admin/class-admin-editor-specific-replace-vars.php',
    'WPSEO_Admin_Gutenberg_Compatibility_Notification' => $baseDir . '/admin/class-admin-gutenberg-compatibility-notification.php',
    'WPSEO_Admin_Help_Panel' => $baseDir . '/admin/class-admin-help-panel.php',
    'WPSEO_Admin_Init' => $baseDir . '/admin/class-admin-init.php',
    'WPSEO_Admin_Menu' => $baseDir . '/admin/menu/class-admin-menu.php',
    'WPSEO_Admin_Pages' => $baseDir . '/admin/class-config.php',
    'WPSEO_Admin_Recommended_Replace_Vars' => $baseDir . '/admin/class-admin-recommended-replace-vars.php',
    'WPSEO_Admin_Settings_Changed_Listener' => $baseDir . '/admin/admin-settings-changed-listener.php',
    'WPSEO_Admin_User_Profile' => $baseDir . '/admin/class-admin-user-profile.php',
    'WPSEO_Admin_Utils' => $baseDir . '/admin/class-admin-utils.php',
    'WPSEO_Author_Sitemap_Provider' => $baseDir . '/inc/sitemaps/class-author-sitemap-provider.php',
    'WPSEO_Base_Menu' => $baseDir . '/admin/menu/class-base-menu.php',
    'WPSEO_Breadcrumbs' => $baseDir . '/src/deprecated/frontend/breadcrumbs.php',
    'WPSEO_Bulk_Description_List_Table' => $baseDir . '/admin/class-bulk-description-editor-list-table.php',
    'WPSEO_Bulk_List_Table' => $baseDir . '/admin/class-bulk-editor-list-table.php',
    'WPSEO_Bulk_Title_Editor_List_Table' => $baseDir . '/admin/class-bulk-title-editor-list-table.php',
    'WPSEO_Capability_Manager' => $baseDir . '/admin/capabilities/class-capability-manager.php',
    'WPSEO_Capability_Manager_Factory' => $baseDir . '/admin/capabilities/class-capability-manager-factory.php',
    'WPSEO_Capability_Manager_Integration' => $baseDir . '/admin/capabilities/class-capability-manager-integration.php',
    'WPSEO_Capability_Manager_VIP' => $baseDir . '/admin/capabilities/class-capability-manager-vip.php',
    'WPSEO_Capability_Manager_WP' => $baseDir . '/admin/capabilities/class-capability-manager-wp.php',
    'WPSEO_Capability_Utils' => $baseDir . '/admin/capabilities/class-capability-utils.php',
    'WPSEO_Collection' => $baseDir . '/admin/interface-collection.php',
    'WPSEO_Collector' => $baseDir . '/admin/class-collector.php',
    'WPSEO_Content_Images' => $baseDir . '/inc/class-wpseo-content-images.php',
    'WPSEO_Cornerstone_Filter' => $baseDir . '/admin/filters/class-cornerstone-filter.php',
    'WPSEO_Custom_Fields' => $baseDir . '/inc/class-wpseo-custom-fields.php',
    'WPSEO_Custom_Taxonomies' => $baseDir . '/inc/class-wpseo-custom-taxonomies.php',
    'WPSEO_Customizer' => $baseDir . '/src/deprecated/admin/class-customizer.php',
    'WPSEO_Database_Proxy' => $baseDir . '/admin/class-database-proxy.php',
    'WPSEO_Date_Helper' => $baseDir . '/inc/date-helper.php',
    'WPSEO_Dismissible_Notification' => $baseDir . '/admin/notifiers/dismissible-notification.php',
    'WPSEO_Endpoint' => $baseDir . '/admin/endpoints/class-endpoint.php',
    'WPSEO_Endpoint_File_Size' => $baseDir . '/admin/endpoints/class-endpoint-file-size.php',
    'WPSEO_Endpoint_Statistics' => $baseDir . '/admin/endpoints/class-endpoint-statistics.php',
    'WPSEO_Export' => $baseDir . '/admin/class-export.php',
    'WPSEO_Expose_Shortlinks' => $baseDir . '/admin/class-expose-shortlinks.php',
    'WPSEO_File_Size_Exception' => $baseDir . '/admin/exceptions/class-file-size-exception.php',
    'WPSEO_File_Size_Service' => $baseDir . '/admin/services/class-file-size.php',
    'WPSEO_Frontend' => $baseDir . '/src/deprecated/frontend/frontend.php',
    'WPSEO_GSC' => $baseDir . '/admin/google_search_console/class-gsc.php',
    'WPSEO_Gutenberg_Compatibility' => $baseDir . '/admin/class-gutenberg-compatibility.php',
    'WPSEO_Image_Utils' => $baseDir . '/inc/class-wpseo-image-utils.php',
    'WPSEO_Import_AIOSEO' => $baseDir . '/admin/import/plugins/class-import-aioseo.php',
    'WPSEO_Import_AIOSEO_V4' => $baseDir . '/admin/import/plugins/class-import-aioseo-v4.php',
    'WPSEO_Import_Greg_SEO' => $baseDir . '/admin/import/plugins/class-import-greg-high-performance-seo.php',
    'WPSEO_Import_HeadSpace' => $baseDir . '/admin/import/plugins/class-import-headspace.php',
    'WPSEO_Import_Jetpack_SEO' => $baseDir . '/admin/import/plugins/class-import-jetpack.php',
    'WPSEO_Import_Platinum_SEO' => $baseDir . '/admin/import/plugins/class-import-platinum-seo-pack.php',
    'WPSEO_Import_Plugin' => $baseDir . '/admin/import/class-import-plugin.php',
    'WPSEO_Import_Plugins_Detector' => $baseDir . '/admin/import/class-import-detector.php',
    'WPSEO_Import_Premium_SEO_Pack' => $baseDir . '/admin/import/plugins/class-import-premium-seo-pack.php',
    'WPSEO_Import_RankMath' => $baseDir . '/admin/import/plugins/class-import-rankmath.php',
    'WPSEO_Import_SEOPressor' => $baseDir . '/admin/import/plugins/class-import-seopressor.php',
    'WPSEO_Import_SEO_Framework' => $baseDir . '/admin/import/plugins/class-import-seo-framework.php',
    'WPSEO_Import_Settings' => $baseDir . '/admin/import/class-import-settings.php',
    'WPSEO_Import_Smartcrawl_SEO' => $baseDir . '/admin/import/plugins/class-import-smartcrawl.php',
    'WPSEO_Import_Squirrly' => $baseDir . '/admin/import/plugins/class-import-squirrly.php',
    'WPSEO_Import_Status' => $baseDir . '/admin/import/class-import-status.php',
    'WPSEO_Import_Ultimate_SEO' => $baseDir . '/admin/import/plugins/class-import-ultimate-seo.php',
    'WPSEO_Import_WPSEO' => $baseDir . '/admin/import/plugins/class-import-wpseo.php',
    'WPSEO_Import_WP_Meta_SEO' => $baseDir . '/admin/import/plugins/class-import-wp-meta-seo.php',
    'WPSEO_Import_WooThemes_SEO' => $baseDir . '/admin/import/plugins/class-import-woothemes-seo.php',
    'WPSEO_Installable' => $baseDir . '/admin/interface-installable.php',
    'WPSEO_Installation' => $baseDir . '/inc/class-wpseo-installation.php',
    'WPSEO_Language_Utils' => $baseDir . '/inc/language-utils.php',
    'WPSEO_Listener' => $baseDir . '/admin/listeners/class-listener.php',
    'WPSEO_Menu' => $baseDir . '/admin/menu/class-menu.php',
    'WPSEO_Meta' => $baseDir . '/inc/class-wpseo-meta.php',
    'WPSEO_Meta_Columns' => $baseDir . '/admin/class-meta-columns.php',
    'WPSEO_Metabox' => $baseDir . '/admin/metabox/class-metabox.php',
    'WPSEO_Metabox_Analysis' => $baseDir . '/admin/metabox/interface-metabox-analysis.php',
    'WPSEO_Metabox_Analysis_Inclusive_Language' => $baseDir . '/admin/metabox/class-metabox-analysis-inclusive-language.php',
    'WPSEO_Metabox_Analysis_Readability' => $baseDir . '/admin/metabox/class-metabox-analysis-readability.php',
    'WPSEO_Metabox_Analysis_SEO' => $baseDir . '/admin/metabox/class-metabox-analysis-seo.php',
    'WPSEO_Metabox_Collapsible' => $baseDir . '/admin/metabox/class-metabox-collapsible.php',
    'WPSEO_Metabox_Collapsibles_Sections' => $baseDir . '/admin/metabox/class-metabox-collapsibles-section.php',
    'WPSEO_Metabox_Editor' => $baseDir . '/admin/metabox/class-metabox-editor.php',
    'WPSEO_Metabox_Form_Tab' => $baseDir . '/admin/metabox/class-metabox-form-tab.php',
    'WPSEO_Metabox_Formatter' => $baseDir . '/admin/formatter/class-metabox-formatter.php',
    'WPSEO_Metabox_Formatter_Interface' => $baseDir . '/admin/formatter/interface-metabox-formatter.php',
    'WPSEO_Metabox_Null_Tab' => $baseDir . '/admin/metabox/class-metabox-null-tab.php',
    'WPSEO_Metabox_Section' => $baseDir . '/admin/metabox/interface-metabox-section.php',
    'WPSEO_Metabox_Section_Additional' => $baseDir . '/admin/metabox/class-metabox-section-additional.php',
    'WPSEO_Metabox_Section_Inclusive_Language' => $baseDir . '/admin/metabox/class-metabox-section-inclusive-language.php',
    'WPSEO_Metabox_Section_React' => $baseDir . '/admin/metabox/class-metabox-section-react.php',
    'WPSEO_Metabox_Section_Readability' => $baseDir . '/admin/metabox/class-metabox-section-readability.php',
    'WPSEO_Metabox_Tab' => $baseDir . '/admin/metabox/interface-metabox-tab.php',
    'WPSEO_MyYoast_Api_Request' => $baseDir . '/inc/class-my-yoast-api-request.php',
    'WPSEO_MyYoast_Bad_Request_Exception' => $baseDir . '/inc/exceptions/class-myyoast-bad-request-exception.php',
    'WPSEO_MyYoast_Invalid_JSON_Exception' => $baseDir . '/inc/exceptions/class-myyoast-invalid-json-exception.php',
    'WPSEO_MyYoast_Proxy' => $baseDir . '/admin/class-my-yoast-proxy.php',
    'WPSEO_Network_Admin_Menu' => $baseDir . '/admin/menu/class-network-admin-menu.php',
    'WPSEO_Notification_Handler' => $baseDir . '/admin/notifiers/interface-notification-handler.php',
    'WPSEO_Option' => $baseDir . '/inc/options/class-wpseo-option.php',
    'WPSEO_Option_MS' => $baseDir . '/inc/options/class-wpseo-option-ms.php',
    'WPSEO_Option_Social' => $baseDir . '/inc/options/class-wpseo-option-social.php',
    'WPSEO_Option_Tab' => $baseDir . '/admin/class-option-tab.php',
    'WPSEO_Option_Tabs' => $baseDir . '/admin/class-option-tabs.php',
    'WPSEO_Option_Tabs_Formatter' => $baseDir . '/admin/class-option-tabs-formatter.php',
    'WPSEO_Option_Titles' => $baseDir . '/inc/options/class-wpseo-option-titles.php',
    'WPSEO_Option_Wpseo' => $baseDir . '/inc/options/class-wpseo-option-wpseo.php',
    'WPSEO_Options' => $baseDir . '/inc/options/class-wpseo-options.php',
    'WPSEO_Paper_Presenter' => $baseDir . '/admin/class-paper-presenter.php',
    'WPSEO_Plugin_Availability' => $baseDir . '/admin/class-plugin-availability.php',
    'WPSEO_Plugin_Conflict' => $baseDir . '/admin/class-plugin-conflict.php',
    'WPSEO_Plugin_Importer' => $baseDir . '/admin/import/plugins/class-abstract-plugin-importer.php',
    'WPSEO_Plugin_Importers' => $baseDir . '/admin/import/plugins/class-importers.php',
    'WPSEO_Post_Metabox_Formatter' => $baseDir . '/admin/formatter/class-post-metabox-formatter.php',
    'WPSEO_Post_Type' => $baseDir . '/inc/class-post-type.php',
    'WPSEO_Post_Type_Sitemap_Provider' => $baseDir . '/inc/sitemaps/class-post-type-sitemap-provider.php',
    'WPSEO_Premium_Popup' => $baseDir . '/admin/class-premium-popup.php',
    'WPSEO_Premium_Upsell_Admin_Block' => $baseDir . '/admin/class-premium-upsell-admin-block.php',
    'WPSEO_Primary_Term' => $baseDir . '/inc/class-wpseo-primary-term.php',
    'WPSEO_Primary_Term_Admin' => $baseDir . '/admin/class-primary-term-admin.php',
    'WPSEO_Product_Upsell_Notice' => $baseDir . '/admin/class-product-upsell-notice.php',
    'WPSEO_Rank' => $baseDir . '/inc/class-wpseo-rank.php',
    'WPSEO_Register_Capabilities' => $baseDir . '/admin/capabilities/class-register-capabilities.php',
    'WPSEO_Register_Roles' => $baseDir . '/admin/roles/class-register-roles.php',
    'WPSEO_Remote_Request' => $baseDir . '/admin/class-remote-request.php',
    'WPSEO_Replace_Vars' => $baseDir . '/inc/class-wpseo-replace-vars.php',
    'WPSEO_Replacement_Variable' => $baseDir . '/inc/class-wpseo-replacement-variable.php',
    'WPSEO_Replacevar_Editor' => $baseDir . '/admin/menu/class-replacevar-editor.php',
    'WPSEO_Replacevar_Field' => $baseDir . '/admin/menu/class-replacevar-field.php',
    'WPSEO_Rewrite' => $baseDir . '/inc/class-rewrite.php',
    'WPSEO_Role_Manager' => $baseDir . '/admin/roles/class-role-manager.php',
    'WPSEO_Role_Manager_Factory' => $baseDir . '/admin/roles/class-role-manager-factory.php',
    'WPSEO_Role_Manager_WP' => $baseDir . '/admin/roles/class-role-manager-wp.php',
    'WPSEO_Schema_Person_Upgrade_Notification' => $baseDir . '/admin/class-schema-person-upgrade-notification.php',
    'WPSEO_Shortcode_Filter' => $baseDir . '/admin/ajax/class-shortcode-filter.php',
    'WPSEO_Shortlinker' => $baseDir . '/inc/class-wpseo-shortlinker.php',
    'WPSEO_Sitemap_Cache_Data' => $baseDir . '/inc/sitemaps/class-sitemap-cache-data.php',
    'WPSEO_Sitemap_Cache_Data_Interface' => $baseDir . '/inc/sitemaps/interface-sitemap-cache-data.php',
    'WPSEO_Sitemap_Image_Parser' => $baseDir . '/inc/sitemaps/class-sitemap-image-parser.php',
    'WPSEO_Sitemap_Provider' => $baseDir . '/inc/sitemaps/interface-sitemap-provider.php',
    'WPSEO_Sitemaps' => $baseDir . '/inc/sitemaps/class-sitemaps.php',
    'WPSEO_Sitemaps_Admin' => $baseDir . '/inc/sitemaps/class-sitemaps-admin.php',
    'WPSEO_Sitemaps_Cache' => $baseDir . '/inc/sitemaps/class-sitemaps-cache.php',
    'WPSEO_Sitemaps_Cache_Validator' => $baseDir . '/inc/sitemaps/class-sitemaps-cache-validator.php',
    'WPSEO_Sitemaps_Renderer' => $baseDir . '/inc/sitemaps/class-sitemaps-renderer.php',
    'WPSEO_Sitemaps_Router' => $baseDir . '/inc/sitemaps/class-sitemaps-router.php',
    'WPSEO_Slug_Change_Watcher' => $baseDir . '/admin/watchers/class-slug-change-watcher.php',
    'WPSEO_Statistic_Integration' => $baseDir . '/admin/statistics/class-statistics-integration.php',
    'WPSEO_Statistics' => $baseDir . '/inc/class-wpseo-statistics.php',
    'WPSEO_Statistics_Service' => $baseDir . '/admin/statistics/class-statistics-service.php',
    'WPSEO_Submenu_Capability_Normalize' => $baseDir . '/admin/menu/class-submenu-capability-normalize.php',
    'WPSEO_Suggested_Plugins' => $baseDir . '/admin/class-suggested-plugins.php',
    'WPSEO_Taxonomy' => $baseDir . '/admin/taxonomy/class-taxonomy.php',
    'WPSEO_Taxonomy_Columns' => $baseDir . '/admin/taxonomy/class-taxonomy-columns.php',
    'WPSEO_Taxonomy_Fields' => $baseDir . '/admin/taxonomy/class-taxonomy-fields.php',
    'WPSEO_Taxonomy_Fields_Presenter' => $baseDir . '/admin/taxonomy/class-taxonomy-fields-presenter.php',
    'WPSEO_Taxonomy_Meta' => $baseDir . '/inc/options/class-wpseo-taxonomy-meta.php',
    'WPSEO_Taxonomy_Metabox' => $baseDir . '/admin/taxonomy/class-taxonomy-metabox.php',
    'WPSEO_Taxonomy_Sitemap_Provider' => $baseDir . '/inc/sitemaps/class-taxonomy-sitemap-provider.php',
    'WPSEO_Term_Metabox_Formatter' => $baseDir . '/admin/formatter/class-term-metabox-formatter.php',
    'WPSEO_Tracking' => $baseDir . '/admin/tracking/class-tracking.php',
    'WPSEO_Tracking_Addon_Data' => $baseDir . '/admin/tracking/class-tracking-addon-data.php',
    'WPSEO_Tracking_Default_Data' => $baseDir . '/admin/tracking/class-tracking-default-data.php',
    'WPSEO_Tracking_Plugin_Data' => $baseDir . '/admin/tracking/class-tracking-plugin-data.php',
    'WPSEO_Tracking_Server_Data' => $baseDir . '/admin/tracking/class-tracking-server-data.php',
    'WPSEO_Tracking_Settings_Data' => $baseDir . '/admin/tracking/class-tracking-settings-data.php',
    'WPSEO_Tracking_Theme_Data' => $baseDir . '/admin/tracking/class-tracking-theme-data.php',
    'WPSEO_Upgrade' => $baseDir . '/inc/class-upgrade.php',
    'WPSEO_Upgrade_History' => $baseDir . '/inc/class-upgrade-history.php',
    'WPSEO_Utils' => $baseDir . '/inc/class-wpseo-utils.php',
    'WPSEO_WordPress_AJAX_Integration' => $baseDir . '/inc/interface-wpseo-wordpress-ajax-integration.php',
    'WPSEO_WordPress_Integration' => $baseDir . '/inc/interface-wpseo-wordpress-integration.php',
    'WPSEO_Yoast_Columns' => $baseDir . '/admin/class-yoast-columns.php',
    'Wincher_Dashboard_Widget' => $baseDir . '/admin/class-wincher-dashboard-widget.php',
    'YoastSEO_Vendor\\GuzzleHttp\\BodySummarizer' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/BodySummarizer.php',
    'YoastSEO_Vendor\\GuzzleHttp\\BodySummarizerInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Client' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Client.php',
    'YoastSEO_Vendor\\GuzzleHttp\\ClientInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/ClientInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\ClientTrait' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/ClientTrait.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Cookie\\CookieJar' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Cookie\\CookieJarInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Cookie\\FileCookieJar' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Cookie\\SessionCookieJar' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Cookie\\SetCookie' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\BadResponseException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\ClientException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\ConnectException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\GuzzleException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\RequestException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\ServerException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\TooManyRedirectsException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Exception\\TransferException' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\HandlerStack' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/HandlerStack.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\CurlFactory' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\CurlFactoryInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\CurlHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\CurlMultiHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\EasyHandle' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\HeaderProcessor' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\MockHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\Proxy' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Handler\\StreamHandler' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'YoastSEO_Vendor\\GuzzleHttp\\MessageFormatter' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/MessageFormatter.php',
    'YoastSEO_Vendor\\GuzzleHttp\\MessageFormatterInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Middleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Middleware.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Pool' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Pool.php',
    'YoastSEO_Vendor\\GuzzleHttp\\PrepareBodyMiddleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\AggregateException' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/AggregateException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\CancellationException' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/CancellationException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\Coroutine' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Coroutine.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\Create' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Create.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\Each' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Each.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\EachPromise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/EachPromise.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\FulfilledPromise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/FulfilledPromise.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\Is' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Is.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\Promise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Promise.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\PromiseInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/PromiseInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\PromisorInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/PromisorInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\RejectedPromise' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/RejectedPromise.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\RejectionException' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/RejectionException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\TaskQueue' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/TaskQueue.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\TaskQueueInterface' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/TaskQueueInterface.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Promise\\Utils' => $baseDir . '/vendor_prefixed/guzzlehttp/promises/src/Utils.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\AppendStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/AppendStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\BufferStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/BufferStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\CachingStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/CachingStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\DroppingStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/DroppingStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\FnStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/FnStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Header' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Header.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\HttpFactory' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/HttpFactory.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\InflateStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/InflateStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\LazyOpenStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/LazyOpenStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\LimitStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/LimitStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Message' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Message.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\MessageTrait' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/MessageTrait.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\MimeType' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/MimeType.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\MultipartStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/MultipartStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\NoSeekStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/NoSeekStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\PumpStream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/PumpStream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Query' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Query.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Request' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Request.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Response' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Response.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Rfc7230' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Rfc7230.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\ServerRequest' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/ServerRequest.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Stream' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Stream.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\StreamWrapper' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/StreamWrapper.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\UploadedFile' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UploadedFile.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Uri' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Uri.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\UriComparator' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UriComparator.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\UriNormalizer' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UriNormalizer.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\UriResolver' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/UriResolver.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Psr7\\Utils' => $baseDir . '/vendor_prefixed/guzzlehttp/psr7/src/Utils.php',
    'YoastSEO_Vendor\\GuzzleHttp\\RedirectMiddleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'YoastSEO_Vendor\\GuzzleHttp\\RequestOptions' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/RequestOptions.php',
    'YoastSEO_Vendor\\GuzzleHttp\\RetryMiddleware' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'YoastSEO_Vendor\\GuzzleHttp\\TransferStats' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/TransferStats.php',
    'YoastSEO_Vendor\\GuzzleHttp\\Utils' => $baseDir . '/vendor_prefixed/guzzlehttp/guzzle/src/Utils.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\AbstractGrant' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/AbstractGrant.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\AuthorizationCode' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/AuthorizationCode.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\ClientCredentials' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/ClientCredentials.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\Exception\\InvalidGrantException' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/Exception/InvalidGrantException.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\GrantFactory' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/GrantFactory.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\Password' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/Password.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Grant\\RefreshToken' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Grant/RefreshToken.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\OptionProvider\\HttpBasicAuthOptionProvider' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/OptionProvider/HttpBasicAuthOptionProvider.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\OptionProvider\\OptionProviderInterface' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/OptionProvider/OptionProviderInterface.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\OptionProvider\\PostAuthOptionProvider' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/OptionProvider/PostAuthOptionProvider.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Provider\\AbstractProvider' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Provider/AbstractProvider.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Provider\\Exception\\IdentityProviderException' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Provider/Exception/IdentityProviderException.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Provider\\GenericProvider' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Provider/GenericProvider.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Provider\\GenericResourceOwner' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Provider/GenericResourceOwner.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Provider\\ResourceOwnerInterface' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Provider/ResourceOwnerInterface.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Token\\AccessToken' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Token/AccessToken.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Token\\AccessTokenInterface' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Token/AccessTokenInterface.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Token\\ResourceOwnerAccessTokenInterface' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Token/ResourceOwnerAccessTokenInterface.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\ArrayAccessorTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/ArrayAccessorTrait.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\BearerAuthorizationTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/BearerAuthorizationTrait.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\GuardedPropertyTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/GuardedPropertyTrait.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\MacAuthorizationTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/MacAuthorizationTrait.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\ProviderRedirectTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/ProviderRedirectTrait.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\QueryBuilderTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/QueryBuilderTrait.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\RequestFactory' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/RequestFactory.php',
    'YoastSEO_Vendor\\League\\OAuth2\\Client\\Tool\\RequiredParameterTrait' => $baseDir . '/vendor_prefixed/league/oauth2-client/src/Tool/RequiredParameterTrait.php',
    'YoastSEO_Vendor\\Psr\\Container\\ContainerExceptionInterface' => $baseDir . '/vendor_prefixed/psr/container/src/ContainerExceptionInterface.php',
    'YoastSEO_Vendor\\Psr\\Container\\ContainerInterface' => $baseDir . '/vendor_prefixed/psr/container/src/ContainerInterface.php',
    'YoastSEO_Vendor\\Psr\\Container\\NotFoundExceptionInterface' => $baseDir . '/vendor_prefixed/psr/container/src/NotFoundExceptionInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Client\\ClientExceptionInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/ClientExceptionInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Client\\ClientInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/ClientInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Client\\NetworkExceptionInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/NetworkExceptionInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Client\\RequestExceptionInterface' => $baseDir . '/vendor_prefixed/psr/http-client/src/RequestExceptionInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\MessageInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/MessageInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\RequestFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/RequestFactoryInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\RequestInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/RequestInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\ResponseFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/ResponseFactoryInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\ResponseInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/ResponseInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\ServerRequestFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\ServerRequestInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/ServerRequestInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\StreamFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/StreamFactoryInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\StreamInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/StreamInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\UploadedFileFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\UploadedFileInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/UploadedFileInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\UriFactoryInterface' => $baseDir . '/vendor_prefixed/psr/http-factory/src/UriFactoryInterface.php',
    'YoastSEO_Vendor\\Psr\\Http\\Message\\UriInterface' => $baseDir . '/vendor_prefixed/psr/http-message/src/UriInterface.php',
    'YoastSEO_Vendor\\Psr\\Log\\AbstractLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/AbstractLogger.php',
    'YoastSEO_Vendor\\Psr\\Log\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/InvalidArgumentException.php',
    'YoastSEO_Vendor\\Psr\\Log\\LogLevel' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LogLevel.php',
    'YoastSEO_Vendor\\Psr\\Log\\LoggerAwareInterface' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareInterface.php',
    'YoastSEO_Vendor\\Psr\\Log\\LoggerAwareTrait' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerAwareTrait.php',
    'YoastSEO_Vendor\\Psr\\Log\\LoggerInterface' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerInterface.php',
    'YoastSEO_Vendor\\Psr\\Log\\LoggerTrait' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/LoggerTrait.php',
    'YoastSEO_Vendor\\Psr\\Log\\NullLogger' => $baseDir . '/vendor_prefixed/psr/log/Psr/Log/NullLogger.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Argument\\RewindableGenerator' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Argument/RewindableGenerator.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Container' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Container.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\ContainerInterface' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/ContainerInterface.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\EnvNotFoundException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/EnvNotFoundException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\ExceptionInterface' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/ExceptionInterface.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\InvalidArgumentException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/InvalidArgumentException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\LogicException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/LogicException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\ParameterCircularReferenceException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/ParameterCircularReferenceException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\RuntimeException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/RuntimeException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\ServiceCircularReferenceException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/ServiceCircularReferenceException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\Exception\\ServiceNotFoundException' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/Exception/ServiceNotFoundException.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\ParameterBag\\EnvPlaceholderParameterBag' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/ParameterBag/EnvPlaceholderParameterBag.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\ParameterBag\\FrozenParameterBag' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/ParameterBag/FrozenParameterBag.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBag' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/ParameterBag/ParameterBag.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBagInterface' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/ParameterBag/ParameterBagInterface.php',
    'YoastSEO_Vendor\\Symfony\\Component\\DependencyInjection\\ResettableContainerInterface' => $baseDir . '/vendor_prefixed/symfony/dependency-injection/ResettableContainerInterface.php',
    'Yoast\\WP\\Lib\\Abstract_Main' => $baseDir . '/lib/abstract-main.php',
    'Yoast\\WP\\Lib\\Dependency_Injection\\Container_Registry' => $baseDir . '/lib/dependency-injection/container-registry.php',
    'Yoast\\WP\\Lib\\Migrations\\Adapter' => $baseDir . '/lib/migrations/adapter.php',
    'Yoast\\WP\\Lib\\Migrations\\Column' => $baseDir . '/lib/migrations/column.php',
    'Yoast\\WP\\Lib\\Migrations\\Constants' => $baseDir . '/lib/migrations/constants.php',
    'Yoast\\WP\\Lib\\Migrations\\Migration' => $baseDir . '/lib/migrations/migration.php',
    'Yoast\\WP\\Lib\\Migrations\\Table' => $baseDir . '/lib/migrations/table.php',
    'Yoast\\WP\\Lib\\Model' => $baseDir . '/lib/model.php',
    'Yoast\\WP\\Lib\\ORM' => $baseDir . '/lib/orm.php',
    'Yoast\\WP\\SEO\\Actions\\Addon_Installation\\Addon_Activate_Action' => $baseDir . '/src/actions/addon-installation/addon-activate-action.php',
    'Yoast\\WP\\SEO\\Actions\\Addon_Installation\\Addon_Install_Action' => $baseDir . '/src/actions/addon-installation/addon-install-action.php',
    'Yoast\\WP\\SEO\\Actions\\Alert_Dismissal_Action' => $baseDir . '/src/actions/alert-dismissal-action.php',
    'Yoast\\WP\\SEO\\Actions\\Configuration\\First_Time_Configuration_Action' => $baseDir . '/src/actions/configuration/first-time-configuration-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Abstract_Aioseo_Importing_Action' => $baseDir . '/src/actions/importing/abstract-aioseo-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Abstract_Aioseo_Settings_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/abstract-aioseo-settings-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Cleanup_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-cleanup-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Custom_Archive_Settings_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-custom-archive-settings-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Default_Archive_Settings_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-default-archive-settings-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_General_Settings_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-general-settings-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Posts_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-posts-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Posttype_Defaults_Settings_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-posttype-defaults-settings-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Taxonomy_Settings_Importing_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-taxonomy-settings-importing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Aioseo\\Aioseo_Validate_Data_Action' => $baseDir . '/src/actions/importing/aioseo/aioseo-validate-data-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Deactivate_Conflicting_Plugins_Action' => $baseDir . '/src/actions/importing/deactivate-conflicting-plugins-action.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Importing_Action_Interface' => $baseDir . '/src/actions/importing/importing-action-interface.php',
    'Yoast\\WP\\SEO\\Actions\\Importing\\Importing_Indexation_Action_Interface' => $baseDir . '/src/actions/importing/importing-indexation-action-interface.php',
    'Yoast\\WP\\SEO\\Actions\\Indexables\\Indexable_Head_Action' => $baseDir . '/src/actions/indexables/indexable-head-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Abstract_Indexing_Action' => $baseDir . '/src/actions/indexing/abstract-indexing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Abstract_Link_Indexing_Action' => $baseDir . '/src/actions/indexing/abstract-link-indexing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexable_General_Indexation_Action' => $baseDir . '/src/actions/indexing/indexable-general-indexation-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexable_Indexing_Complete_Action' => $baseDir . '/src/actions/indexing/indexable-indexing-complete-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexable_Post_Indexation_Action' => $baseDir . '/src/actions/indexing/indexable-post-indexation-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexable_Post_Type_Archive_Indexation_Action' => $baseDir . '/src/actions/indexing/indexable-post-type-archive-indexation-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexable_Term_Indexation_Action' => $baseDir . '/src/actions/indexing/indexable-term-indexation-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexation_Action_Interface' => $baseDir . '/src/actions/indexing/indexation-action-interface.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexing_Complete_Action' => $baseDir . '/src/actions/indexing/indexing-complete-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Indexing_Prepare_Action' => $baseDir . '/src/actions/indexing/indexing-prepare-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Limited_Indexing_Action_Interface' => $baseDir . '/src/actions/indexing/limited-indexing-action-interface.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Post_Link_Indexing_Action' => $baseDir . '/src/actions/indexing/post-link-indexing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Indexing\\Term_Link_Indexing_Action' => $baseDir . '/src/actions/indexing/term-link-indexing-action.php',
    'Yoast\\WP\\SEO\\Actions\\Integrations_Action' => $baseDir . '/src/actions/integrations-action.php',
    'Yoast\\WP\\SEO\\Actions\\SEMrush\\SEMrush_Login_Action' => $baseDir . '/src/actions/semrush/semrush-login-action.php',
    'Yoast\\WP\\SEO\\Actions\\SEMrush\\SEMrush_Options_Action' => $baseDir . '/src/actions/semrush/semrush-options-action.php',
    'Yoast\\WP\\SEO\\Actions\\SEMrush\\SEMrush_Phrases_Action' => $baseDir . '/src/actions/semrush/semrush-phrases-action.php',
    'Yoast\\WP\\SEO\\Actions\\Wincher\\Wincher_Account_Action' => $baseDir . '/src/actions/wincher/wincher-account-action.php',
    'Yoast\\WP\\SEO\\Actions\\Wincher\\Wincher_Keyphrases_Action' => $baseDir . '/src/actions/wincher/wincher-keyphrases-action.php',
    'Yoast\\WP\\SEO\\Actions\\Wincher\\Wincher_Login_Action' => $baseDir . '/src/actions/wincher/wincher-login-action.php',
    'Yoast\\WP\\SEO\\Analytics\\Application\\Missing_Indexables_Collector' => $baseDir . '/src/analytics/application/missing-indexables-collector.php',
    'Yoast\\WP\\SEO\\Analytics\\Application\\To_Be_Cleaned_Indexables_Collector' => $baseDir . '/src/analytics/application/to-be-cleaned-indexables-collector.php',
    'Yoast\\WP\\SEO\\Analytics\\Domain\\Missing_Indexable_Bucket' => $baseDir . '/src/analytics/domain/missing-indexable-bucket.php',
    'Yoast\\WP\\SEO\\Analytics\\Domain\\Missing_Indexable_Count' => $baseDir . '/src/analytics/domain/missing-indexable-count.php',
    'Yoast\\WP\\SEO\\Analytics\\Domain\\To_Be_Cleaned_Indexable_Bucket' => $baseDir . '/src/analytics/domain/to-be-cleaned-indexable-bucket.php',
    'Yoast\\WP\\SEO\\Analytics\\Domain\\To_Be_Cleaned_Indexable_Count' => $baseDir . '/src/analytics/domain/to-be-cleaned-indexable-count.php',
    'Yoast\\WP\\SEO\\Analytics\\User_Interface\\Last_Completed_Indexation_Integration' => $baseDir . '/src/analytics/user-interface/last-completed-indexation-integration.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Author_Builder' => $baseDir . '/src/builders/indexable-author-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Builder' => $baseDir . '/src/builders/indexable-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Date_Archive_Builder' => $baseDir . '/src/builders/indexable-date-archive-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Hierarchy_Builder' => $baseDir . '/src/builders/indexable-hierarchy-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Home_Page_Builder' => $baseDir . '/src/builders/indexable-home-page-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Link_Builder' => $baseDir . '/src/builders/indexable-link-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Post_Builder' => $baseDir . '/src/builders/indexable-post-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Post_Type_Archive_Builder' => $baseDir . '/src/builders/indexable-post-type-archive-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Social_Image_Trait' => $baseDir . '/src/builders/indexable-social-image-trait.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_System_Page_Builder' => $baseDir . '/src/builders/indexable-system-page-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Indexable_Term_Builder' => $baseDir . '/src/builders/indexable-term-builder.php',
    'Yoast\\WP\\SEO\\Builders\\Primary_Term_Builder' => $baseDir . '/src/builders/primary-term-builder.php',
    'Yoast\\WP\\SEO\\Commands\\Cleanup_Command' => $baseDir . '/src/commands/cleanup-command.php',
    'Yoast\\WP\\SEO\\Commands\\Command_Interface' => $baseDir . '/src/commands/command-interface.php',
    'Yoast\\WP\\SEO\\Commands\\Index_Command' => $baseDir . '/src/commands/index-command.php',
    'Yoast\\WP\\SEO\\Conditionals\\Addon_Installation_Conditional' => $baseDir . '/src/conditionals/addon-installation-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Doing_Post_Quick_Edit_Save_Conditional' => $baseDir . '/src/conditionals/admin/doing-post-quick-edit-save-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Estimated_Reading_Time_Conditional' => $baseDir . '/src/conditionals/admin/estimated-reading-time-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Licenses_Page_Conditional' => $baseDir . '/src/conditionals/admin/licenses-page-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Non_Network_Admin_Conditional' => $baseDir . '/src/conditionals/admin/non-network-admin-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Post_Conditional' => $baseDir . '/src/conditionals/admin/post-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Posts_Overview_Or_Ajax_Conditional' => $baseDir . '/src/conditionals/admin/posts-overview-or-ajax-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin\\Yoast_Admin_Conditional' => $baseDir . '/src/conditionals/admin/yoast-admin-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Admin_Conditional' => $baseDir . '/src/conditionals/admin-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Attachment_Redirections_Enabled_Conditional' => $baseDir . '/src/conditionals/attachment-redirections-enabled-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Check_Required_Version_Conditional' => $baseDir . '/src/conditionals/check-required-version-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Conditional' => $baseDir . '/src/conditionals/conditional-interface.php',
    'Yoast\\WP\\SEO\\Conditionals\\Deactivating_Yoast_Seo_Conditional' => $baseDir . '/src/conditionals/deactivating-yoast-seo-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Development_Conditional' => $baseDir . '/src/conditionals/development-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Feature_Flag_Conditional' => $baseDir . '/src/conditionals/feature-flag-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Front_End_Conditional' => $baseDir . '/src/conditionals/front-end-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Get_Request_Conditional' => $baseDir . '/src/conditionals/get-request-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Google_Site_Kit_Feature_Conditional' => $baseDir . '/src/conditionals/google-site-kit-feature-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Headless_Rest_Endpoints_Enabled_Conditional' => $baseDir . '/src/conditionals/headless-rest-endpoints-enabled-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Import_Tool_Selected_Conditional' => $baseDir . '/src/conditionals/import-tool-selected-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Jetpack_Conditional' => $baseDir . '/src/conditionals/jetpack-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Migrations_Conditional' => $baseDir . '/src/conditionals/migrations-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\New_Settings_Ui_Conditional' => $baseDir . '/src/conditionals/new-settings-ui-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\News_Conditional' => $baseDir . '/src/conditionals/news-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\No_Conditionals' => $baseDir . '/src/conditionals/no-conditionals-trait.php',
    'Yoast\\WP\\SEO\\Conditionals\\No_Tool_Selected_Conditional' => $baseDir . '/src/conditionals/no-tool-selected-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Non_Multisite_Conditional' => $baseDir . '/src/conditionals/non-multisite-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Not_Admin_Ajax_Conditional' => $baseDir . '/src/conditionals/not-admin-ajax-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Open_Graph_Conditional' => $baseDir . '/src/conditionals/open-graph-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Premium_Active_Conditional' => $baseDir . '/src/conditionals/premium-active-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Premium_Inactive_Conditional' => $baseDir . '/src/conditionals/premium-inactive-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Primary_Category_Conditional' => $baseDir . '/src/conditionals/primary-category-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Robots_Txt_Conditional' => $baseDir . '/src/conditionals/robots-txt-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\SEMrush_Enabled_Conditional' => $baseDir . '/src/conditionals/semrush-enabled-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Settings_Conditional' => $baseDir . '/src/conditionals/settings-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Should_Index_Links_Conditional' => $baseDir . '/src/conditionals/should-index-links-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Text_Formality_Conditional' => $baseDir . '/src/conditionals/text-formality-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\Elementor_Activated_Conditional' => $baseDir . '/src/conditionals/third-party/elementor-activated-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\Elementor_Edit_Conditional' => $baseDir . '/src/conditionals/third-party/elementor-edit-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\Polylang_Conditional' => $baseDir . '/src/conditionals/third-party/polylang-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\Site_Kit_Conditional' => $baseDir . '/src/conditionals/third-party/site-kit-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\TranslatePress_Conditional' => $baseDir . '/src/conditionals/third-party/translatepress-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\W3_Total_Cache_Conditional' => $baseDir . '/src/conditionals/third-party/w3-total-cache-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\WPML_Conditional' => $baseDir . '/src/conditionals/third-party/wpml-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\WPML_WPSEO_Conditional' => $baseDir . '/src/conditionals/third-party/wpml-wpseo-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\Wordproof_Integration_Active_Conditional' => $baseDir . '/src/deprecated/src/conditionals/third-party/wordproof-integration-active-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Third_Party\\Wordproof_Plugin_Inactive_Conditional' => $baseDir . '/src/deprecated/src/conditionals/third-party/wordproof-plugin-inactive-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Traits\\Admin_Conditional_Trait' => $baseDir . '/src/conditionals/traits/admin-conditional-trait.php',
    'Yoast\\WP\\SEO\\Conditionals\\Updated_Importer_Framework_Conditional' => $baseDir . '/src/conditionals/updated-importer-framework-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\User_Can_Edit_Users_Conditional' => $baseDir . '/src/conditionals/user-can-edit-users-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\User_Can_Manage_Wpseo_Options_Conditional' => $baseDir . '/src/conditionals/user-can-manage-wpseo-options-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\User_Can_Publish_Posts_And_Pages_Conditional' => $baseDir . '/src/conditionals/user-can-publish-posts-and-pages-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\User_Edit_Conditional' => $baseDir . '/src/conditionals/user-edit-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\User_Profile_Conditional' => $baseDir . '/src/conditionals/user-profile-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\WP_CRON_Enabled_Conditional' => $baseDir . '/src/conditionals/wp-cron-enabled-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\WP_Robots_Conditional' => $baseDir . '/src/conditionals/wp-robots-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Web_Stories_Conditional' => $baseDir . '/src/conditionals/web-stories-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Wincher_Automatically_Track_Conditional' => $baseDir . '/src/conditionals/wincher-automatically-track-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Wincher_Conditional' => $baseDir . '/src/conditionals/wincher-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Wincher_Enabled_Conditional' => $baseDir . '/src/conditionals/wincher-enabled-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Wincher_Token_Conditional' => $baseDir . '/src/conditionals/wincher-token-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\WooCommerce_Conditional' => $baseDir . '/src/conditionals/woocommerce-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\XMLRPC_Conditional' => $baseDir . '/src/conditionals/xmlrpc-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Yoast_Admin_And_Dashboard_Conditional' => $baseDir . '/src/conditionals/yoast-admin-and-dashboard-conditional.php',
    'Yoast\\WP\\SEO\\Conditionals\\Yoast_Tools_Page_Conditional' => $baseDir . '/src/conditionals/yoast-tools-page-conditional.php',
    'Yoast\\WP\\SEO\\Config\\Badge_Group_Names' => $baseDir . '/src/config/badge-group-names.php',
    'Yoast\\WP\\SEO\\Config\\Conflicting_Plugins' => $baseDir . '/src/config/conflicting-plugins.php',
    'Yoast\\WP\\SEO\\Config\\Indexing_Reasons' => $baseDir . '/src/config/indexing-reasons.php',
    'Yoast\\WP\\SEO\\Config\\Migration_Status' => $baseDir . '/src/config/migration-status.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddCollationToTables' => $baseDir . '/src/config/migrations/20200408101900_AddCollationToTables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddColumnsToIndexables' => $baseDir . '/src/config/migrations/20200420073606_AddColumnsToIndexables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddEstimatedReadingTime' => $baseDir . '/src/config/migrations/20201202144329_AddEstimatedReadingTime.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddHasAncestorsColumn' => $baseDir . '/src/config/migrations/20200609154515_AddHasAncestorsColumn.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddInclusiveLanguageScore' => $baseDir . '/src/config/migrations/20230417083836_AddInclusiveLanguageScore.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddIndexableObjectIdAndTypeIndex' => $baseDir . '/src/config/migrations/20200430075614_AddIndexableObjectIdAndTypeIndex.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddIndexesForProminentWordsOnIndexables' => $baseDir . '/src/config/migrations/20200728095334_AddIndexesForProminentWordsOnIndexables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddObjectTimestamps' => $baseDir . '/src/config/migrations/20211020091404_AddObjectTimestamps.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\AddVersionColumnToIndexables' => $baseDir . '/src/config/migrations/20210817092415_AddVersionColumnToIndexables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\BreadcrumbTitleAndHierarchyReset' => $baseDir . '/src/config/migrations/20200428123747_BreadcrumbTitleAndHierarchyReset.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\ClearIndexableTables' => $baseDir . '/src/config/migrations/20200430150130_ClearIndexableTables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\CreateIndexableSubpagesIndex' => $baseDir . '/src/config/migrations/20200702141921_CreateIndexableSubpagesIndex.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\CreateSEOLinksTable' => $baseDir . '/src/config/migrations/20200617122511_CreateSEOLinksTable.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\DeleteDuplicateIndexables' => $baseDir . '/src/config/migrations/20200507054848_DeleteDuplicateIndexables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\ExpandIndexableColumnLengths' => $baseDir . '/src/config/migrations/20200428194858_ExpandIndexableColumnLengths.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\ExpandIndexableIDColumnLengths' => $baseDir . '/src/config/migrations/20201216124002_ExpandIndexableIDColumnLengths.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\ExpandPrimaryTermIDColumnLengths' => $baseDir . '/src/config/migrations/20201216141134_ExpandPrimaryTermIDColumnLengths.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\ReplacePermalinkHashIndex' => $baseDir . '/src/config/migrations/20200616130143_ReplacePermalinkHashIndex.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\ResetIndexableHierarchyTable' => $baseDir . '/src/config/migrations/20200513133401_ResetIndexableHierarchyTable.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\TruncateIndexableTables' => $baseDir . '/src/config/migrations/20200429105310_TruncateIndexableTables.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\WpYoastDropIndexableMetaTableIfExists' => $baseDir . '/src/config/migrations/20190529075038_WpYoastDropIndexableMetaTableIfExists.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\WpYoastIndexable' => $baseDir . '/src/config/migrations/20171228151840_WpYoastIndexable.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\WpYoastIndexableHierarchy' => $baseDir . '/src/config/migrations/20191011111109_WpYoastIndexableHierarchy.php',
    'Yoast\\WP\\SEO\\Config\\Migrations\\WpYoastPrimaryTerm' => $baseDir . '/src/config/migrations/20171228151841_WpYoastPrimaryTerm.php',
    'Yoast\\WP\\SEO\\Config\\OAuth_Client' => $baseDir . '/src/config/oauth-client.php',
    'Yoast\\WP\\SEO\\Config\\Researcher_Languages' => $baseDir . '/src/config/researcher-languages.php',
    'Yoast\\WP\\SEO\\Config\\SEMrush_Client' => $baseDir . '/src/config/semrush-client.php',
    'Yoast\\WP\\SEO\\Config\\Schema_IDs' => $baseDir . '/src/config/schema-ids.php',
    'Yoast\\WP\\SEO\\Config\\Schema_Types' => $baseDir . '/src/config/schema-types.php',
    'Yoast\\WP\\SEO\\Config\\Wincher_Client' => $baseDir . '/src/config/wincher-client.php',
    'Yoast\\WP\\SEO\\Config\\Wincher_PKCE_Provider' => $baseDir . '/src/config/wincher-pkce-provider.php',
    'Yoast\\WP\\SEO\\Config\\Wordproof_App_Config' => $baseDir . '/src/deprecated/src/config/wordproof-app-config.php',
    'Yoast\\WP\\SEO\\Config\\Wordproof_Translations' => $baseDir . '/src/deprecated/src/config/wordproof-translations.php',
    'Yoast\\WP\\SEO\\Content_Type_Visibility\\Application\\Content_Type_Visibility_Dismiss_Notifications' => $baseDir . '/src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php',
    'Yoast\\WP\\SEO\\Content_Type_Visibility\\Application\\Content_Type_Visibility_Watcher_Actions' => $baseDir . '/src/content-type-visibility/application/content-type-visibility-watcher-actions.php',
    'Yoast\\WP\\SEO\\Content_Type_Visibility\\User_Interface\\Content_Type_Visibility_Dismiss_New_Route' => $baseDir . '/src/content-type-visibility/user-interface/content-type-visibility-dismiss-new-route.php',
    'Yoast\\WP\\SEO\\Context\\Meta_Tags_Context' => $baseDir . '/src/context/meta-tags-context.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Configuration\\Dashboard_Configuration' => $baseDir . '/src/dashboard/application/configuration/dashboard-configuration.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Content_Types\\Content_Types_Repository' => $baseDir . '/src/dashboard/application/content-types/content-types-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Endpoints\\Endpoints_Repository' => $baseDir . '/src/dashboard/application/endpoints/endpoints-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Filter_Pairs\\Filter_Pairs_Repository' => $baseDir . '/src/dashboard/application/filter-pairs/filter-pairs-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Score_Groups\\SEO_Score_Groups\\SEO_Score_Groups_Repository' => $baseDir . '/src/dashboard/application/score-groups/seo-score-groups/seo-score-groups-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Score_Results\\Abstract_Score_Results_Repository' => $baseDir . '/src/dashboard/application/score-results/abstract-score-results-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Score_Results\\Current_Scores_Repository' => $baseDir . '/src/dashboard/application/score-results/current-scores-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Score_Results\\Readability_Score_Results\\Readability_Score_Results_Repository' => $baseDir . '/src/dashboard/application/score-results/readability-score-results/readability-score-results-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Score_Results\\SEO_Score_Results\\SEO_Score_Results_Repository' => $baseDir . '/src/dashboard/application/score-results/seo-score-results/seo-score-results-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Search_Rankings\\Search_Ranking_Compare_Repository' => $baseDir . '/src/dashboard/application/search-rankings/search-ranking-compare-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Search_Rankings\\Top_Page_Repository' => $baseDir . '/src/dashboard/application/search-rankings/top-page-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Search_Rankings\\Top_Query_Repository' => $baseDir . '/src/dashboard/application/search-rankings/top-query-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Taxonomies\\Taxonomies_Repository' => $baseDir . '/src/dashboard/application/taxonomies/taxonomies-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Tracking\\Setup_Steps_Tracking' => $baseDir . '/src/dashboard/application/tracking/setup-steps-tracking.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Traffic\\Organic_Sessions_Compare_Repository' => $baseDir . '/src/dashboard/application/traffic/organic-sessions-compare-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Application\\Traffic\\Organic_Sessions_Daily_Repository' => $baseDir . '/src/dashboard/application/traffic/organic-sessions-daily-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Analytics_4\\Failed_Request_Exception' => $baseDir . '/src/dashboard/domain/analytics-4/failed-request-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Analytics_4\\Invalid_Request_Exception' => $baseDir . '/src/dashboard/domain/analytics-4/invalid-request-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Analytics_4\\Unexpected_Response_Exception' => $baseDir . '/src/dashboard/domain/analytics-4/unexpected-response-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Content_Types\\Content_Type' => $baseDir . '/src/dashboard/domain/content-types/content-type.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Content_Types\\Content_Types_List' => $baseDir . '/src/dashboard/domain/content-types/content-types-list.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Data_Provider\\Dashboard_Repository_Interface' => $baseDir . '/src/dashboard/domain/data-provider/dashboard-repository-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Data_Provider\\Data_Container' => $baseDir . '/src/dashboard/domain/data-provider/data-container.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Data_Provider\\Data_Interface' => $baseDir . '/src/dashboard/domain/data-provider/data-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Data_Provider\\Parameters' => $baseDir . '/src/dashboard/domain/data-provider/parameters.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Endpoint\\Endpoint_Interface' => $baseDir . '/src/dashboard/domain/endpoint/endpoint-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Endpoint\\Endpoint_List' => $baseDir . '/src/dashboard/domain/endpoint/endpoint-list.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Filter_Pairs\\Filter_Pairs_Interface' => $baseDir . '/src/dashboard/domain/filter-pairs/filter-pairs-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Filter_Pairs\\Product_Category_Filter_Pair' => $baseDir . '/src/dashboard/domain/filter-pairs/product-category-filter-pair.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Abstract_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/abstract-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Readability_Score_Groups\\Abstract_Readability_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/readability-score-groups/abstract-readability-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Readability_Score_Groups\\Bad_Readability_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/readability-score-groups/bad-readability-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Readability_Score_Groups\\Good_Readability_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/readability-score-groups/good-readability-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Readability_Score_Groups\\No_Readability_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/readability-score-groups/no-readability-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Readability_Score_Groups\\Ok_Readability_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/readability-score-groups/ok-readability-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Readability_Score_Groups\\Readability_Score_Groups_Interface' => $baseDir . '/src/dashboard/domain/score-groups/readability-score-groups/readability-score-groups-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\SEO_Score_Groups\\Abstract_SEO_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/seo-score-groups/abstract-seo-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\SEO_Score_Groups\\Bad_SEO_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/seo-score-groups/bad-seo-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\SEO_Score_Groups\\Good_SEO_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/seo-score-groups/good-seo-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\SEO_Score_Groups\\No_SEO_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/seo-score-groups/no-seo-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\SEO_Score_Groups\\Ok_SEO_Score_Group' => $baseDir . '/src/dashboard/domain/score-groups/seo-score-groups/ok-seo-score-group.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\SEO_Score_Groups\\SEO_Score_Groups_Interface' => $baseDir . '/src/dashboard/domain/score-groups/seo-score-groups/seo-score-groups-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Groups\\Score_Groups_Interface' => $baseDir . '/src/dashboard/domain/score-groups/score-groups-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Results\\Current_Score' => $baseDir . '/src/dashboard/domain/score-results/current-score.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Results\\Current_Scores_List' => $baseDir . '/src/dashboard/domain/score-results/current-scores-list.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Results\\Score_Result' => $baseDir . '/src/dashboard/domain/score-results/score-result.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Score_Results\\Score_Results_Not_Found_Exception' => $baseDir . '/src/dashboard/domain/score-results/score-results-not-found-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Search_Console\\Failed_Request_Exception' => $baseDir . '/src/dashboard/domain/search-console/failed-request-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Search_Console\\Unexpected_Response_Exception' => $baseDir . '/src/dashboard/domain/search-console/unexpected-response-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Search_Rankings\\Comparison_Search_Ranking_Data' => $baseDir . '/src/dashboard/domain/search-rankings/comparison-search-ranking-data.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Search_Rankings\\Search_Ranking_Data' => $baseDir . '/src/dashboard/domain/search-rankings/search-ranking-data.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Search_Rankings\\Top_Page_Data' => $baseDir . '/src/dashboard/domain/search-rankings/top-page-data.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Taxonomies\\Taxonomy' => $baseDir . '/src/dashboard/domain/taxonomies/taxonomy.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Time_Based_SEO_Metrics\\Repository_Not_Found_Exception' => $baseDir . '/src/dashboard/domain/time-based-seo-metrics/repository-not-found-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Time_Based_Seo_Metrics\\Data_Source_Not_Available_Exception' => $baseDir . '/src/dashboard/domain/time-based-seo-metrics/data-source-not-available-exception.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Traffic\\Comparison_Traffic_Data' => $baseDir . '/src/dashboard/domain/traffic/comparison-traffic-data.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Traffic\\Daily_Traffic_Data' => $baseDir . '/src/dashboard/domain/traffic/daily-traffic-data.php',
    'Yoast\\WP\\SEO\\Dashboard\\Domain\\Traffic\\Traffic_Data' => $baseDir . '/src/dashboard/domain/traffic/traffic-data.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Analytics_4\\Analytics_4_Parameters' => $baseDir . '/src/dashboard/infrastructure/analytics-4/analytics-4-parameters.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Analytics_4\\Site_Kit_Analytics_4_Adapter' => $baseDir . '/src/dashboard/infrastructure/analytics-4/site-kit-analytics-4-adapter.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Analytics_4\\Site_Kit_Analytics_4_Api_Call' => $baseDir . '/src/dashboard/infrastructure/analytics-4/site-kit-analytics-4-api-call.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Browser_Cache\\Browser_Cache_Configuration' => $baseDir . '/src/dashboard/infrastructure/browser-cache/browser-cache-configuration.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Configuration\\Permanently_Dismissed_Site_Kit_Configuration_Repository' => $baseDir . '/src/dashboard/infrastructure/configuration/permanently-dismissed-site-kit-configuration-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Configuration\\Permanently_Dismissed_Site_Kit_Configuration_Repository_Interface' => $baseDir . '/src/dashboard/infrastructure/configuration/permanently-dismissed-site-kit-configuration-repository-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Configuration\\Site_Kit_Consent_Repository' => $baseDir . '/src/dashboard/infrastructure/configuration/site-kit-consent-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Configuration\\Site_Kit_Consent_Repository_Interface' => $baseDir . '/src/dashboard/infrastructure/configuration/site-kit-consent-repository-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Connection\\Site_Kit_Is_Connected_Call' => $baseDir . '/src/dashboard/infrastructure/connection/site-kit-is-connected-call.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Content_Types\\Content_Types_Collector' => $baseDir . '/src/dashboard/infrastructure/content-types/content-types-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Endpoints\\Readability_Scores_Endpoint' => $baseDir . '/src/dashboard/infrastructure/endpoints/readability-scores-endpoint.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Endpoints\\SEO_Scores_Endpoint' => $baseDir . '/src/dashboard/infrastructure/endpoints/seo-scores-endpoint.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Endpoints\\Setup_Steps_Tracking_Endpoint' => $baseDir . '/src/dashboard/infrastructure/endpoints/setup-steps-tracking-endpoint.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Endpoints\\Site_Kit_Configuration_Dismissal_Endpoint' => $baseDir . '/src/dashboard/infrastructure/endpoints/site-kit-configuration-dismissal-endpoint.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Endpoints\\Site_Kit_Consent_Management_Endpoint' => $baseDir . '/src/dashboard/infrastructure/endpoints/site-kit-consent-management-endpoint.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Endpoints\\Time_Based_SEO_Metrics_Endpoint' => $baseDir . '/src/dashboard/infrastructure/endpoints/time-based-seo-metrics-endpoint.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Indexables\\Top_Page_Indexable_Collector' => $baseDir . '/src/dashboard/infrastructure/indexables/top-page-indexable-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Integrations\\Site_Kit' => $baseDir . '/src/dashboard/infrastructure/integrations/site-kit.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Nonces\\Nonce_Repository' => $baseDir . '/src/dashboard/infrastructure/nonces/nonce-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Score_Groups\\Score_Group_Link_Collector' => $baseDir . '/src/dashboard/infrastructure/score-groups/score-group-link-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Score_Results\\Readability_Score_Results\\Cached_Readability_Score_Results_Collector' => $baseDir . '/src/dashboard/infrastructure/score-results/readability-score-results/cached-readability-score-results-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Score_Results\\Readability_Score_Results\\Readability_Score_Results_Collector' => $baseDir . '/src/dashboard/infrastructure/score-results/readability-score-results/readability-score-results-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Score_Results\\SEO_Score_Results\\Cached_SEO_Score_Results_Collector' => $baseDir . '/src/dashboard/infrastructure/score-results/seo-score-results/cached-seo-score-results-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Score_Results\\SEO_Score_Results\\SEO_Score_Results_Collector' => $baseDir . '/src/dashboard/infrastructure/score-results/seo-score-results/seo-score-results-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Score_Results\\Score_Results_Collector_Interface' => $baseDir . '/src/dashboard/infrastructure/score-results/score-results-collector-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Search_Console\\Search_Console_Parameters' => $baseDir . '/src/dashboard/infrastructure/search-console/search-console-parameters.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Search_Console\\Site_Kit_Search_Console_Adapter' => $baseDir . '/src/dashboard/infrastructure/search-console/site-kit-search-console-adapter.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Search_Console\\Site_Kit_Search_Console_Api_Call' => $baseDir . '/src/dashboard/infrastructure/search-console/site-kit-search-console-api-call.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Taxonomies\\Taxonomies_Collector' => $baseDir . '/src/dashboard/infrastructure/taxonomies/taxonomies-collector.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Taxonomies\\Taxonomy_Validator' => $baseDir . '/src/dashboard/infrastructure/taxonomies/taxonomy-validator.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Tracking\\Setup_Steps_Tracking_Repository' => $baseDir . '/src/dashboard/infrastructure/tracking/setup-steps-tracking-repository.php',
    'Yoast\\WP\\SEO\\Dashboard\\Infrastructure\\Tracking\\Setup_Steps_Tracking_Repository_Interface' => $baseDir . '/src/dashboard/infrastructure/tracking/setup-steps-tracking-repository-interface.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Configuration\\Site_Kit_Capabilities_Integration' => $baseDir . '/src/dashboard/user-interface/configuration/site-kit-capabilities-integration.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Configuration\\Site_Kit_Configuration_Dismissal_Route' => $baseDir . '/src/dashboard/user-interface/configuration/site-kit-configuration-dismissal-route.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Configuration\\Site_Kit_Consent_Management_Route' => $baseDir . '/src/dashboard/user-interface/configuration/site-kit-consent-management-route.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Scores\\Abstract_Scores_Route' => $baseDir . '/src/dashboard/user-interface/scores/abstract-scores-route.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Scores\\Readability_Scores_Route' => $baseDir . '/src/dashboard/user-interface/scores/readability-scores-route.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Scores\\SEO_Scores_Route' => $baseDir . '/src/dashboard/user-interface/scores/seo-scores-route.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Setup\\Setup_Flow_Interceptor' => $baseDir . '/src/dashboard/user-interface/setup/setup-flow-interceptor.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Setup\\Setup_Url_Interceptor' => $baseDir . '/src/dashboard/user-interface/setup/setup-url-interceptor.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Time_Based_SEO_Metrics\\Time_Based_SEO_Metrics_Route' => $baseDir . '/src/dashboard/user-interface/time-based-seo-metrics/time-based-seo-metrics-route.php',
    'Yoast\\WP\\SEO\\Dashboard\\User_Interface\\Tracking\\Setup_Steps_Tracking_Route' => $baseDir . '/src/dashboard/user-interface/tracking/setup-steps-tracking-route.php',
    'Yoast\\WP\\SEO\\Editors\\Application\\Analysis_Features\\Enabled_Analysis_Features_Repository' => $baseDir . '/src/editors/application/analysis-features/enabled-analysis-features-repository.php',
    'Yoast\\WP\\SEO\\Editors\\Application\\Integrations\\Integration_Information_Repository' => $baseDir . '/src/editors/application/integrations/integration-information-repository.php',
    'Yoast\\WP\\SEO\\Editors\\Application\\Seo\\Post_Seo_Information_Repository' => $baseDir . '/src/editors/application/seo/post-seo-information-repository.php',
    'Yoast\\WP\\SEO\\Editors\\Application\\Seo\\Term_Seo_Information_Repository' => $baseDir . '/src/editors/application/seo/term-seo-information-repository.php',
    'Yoast\\WP\\SEO\\Editors\\Application\\Site\\Website_Information_Repository' => $baseDir . '/src/editors/application/site/website-information-repository.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Analysis_Features\\Analysis_Feature' => $baseDir . '/src/editors/domain/analysis-features/analysis-feature.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Analysis_Features\\Analysis_Feature_Interface' => $baseDir . '/src/editors/domain/analysis-features/analysis-feature-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Analysis_Features\\Analysis_Features_List' => $baseDir . '/src/editors/domain/analysis-features/analysis-features-list.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Integrations\\Integration_Data_Provider_Interface' => $baseDir . '/src/editors/domain/integrations/integration-data-provider-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Seo\\Description' => $baseDir . '/src/editors/domain/seo/description.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Seo\\Keyphrase' => $baseDir . '/src/editors/domain/seo/keyphrase.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Seo\\Seo_Plugin_Data_Interface' => $baseDir . '/src/editors/domain/seo/seo-plugin-data-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Seo\\Social' => $baseDir . '/src/editors/domain/seo/social.php',
    'Yoast\\WP\\SEO\\Editors\\Domain\\Seo\\Title' => $baseDir . '/src/editors/domain/seo/title.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Cornerstone_Content' => $baseDir . '/src/editors/framework/cornerstone-content.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Inclusive_Language_Analysis' => $baseDir . '/src/editors/framework/inclusive-language-analysis.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\Jetpack_Markdown' => $baseDir . '/src/editors/framework/integrations/jetpack-markdown.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\Multilingual' => $baseDir . '/src/editors/framework/integrations/multilingual.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\News_SEO' => $baseDir . '/src/editors/framework/integrations/news-seo.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\Semrush' => $baseDir . '/src/editors/framework/integrations/semrush.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\Wincher' => $baseDir . '/src/editors/framework/integrations/wincher.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\WooCommerce' => $baseDir . '/src/editors/framework/integrations/woocommerce.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Integrations\\WooCommerce_SEO' => $baseDir . '/src/editors/framework/integrations/woocommerce-seo.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Keyphrase_Analysis' => $baseDir . '/src/editors/framework/keyphrase-analysis.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Previously_Used_Keyphrase' => $baseDir . '/src/editors/framework/previously-used-keyphrase.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Readability_Analysis' => $baseDir . '/src/editors/framework/readability-analysis.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Description_Data_Provider_Interface' => $baseDir . '/src/editors/framework/seo/description-data-provider-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Keyphrase_Interface' => $baseDir . '/src/editors/framework/seo/keyphrase-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Posts\\Abstract_Post_Seo_Data_Provider' => $baseDir . '/src/editors/framework/seo/posts/abstract-post-seo-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Posts\\Description_Data_Provider' => $baseDir . '/src/editors/framework/seo/posts/description-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Posts\\Keyphrase_Data_Provider' => $baseDir . '/src/editors/framework/seo/posts/keyphrase-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Posts\\Social_Data_Provider' => $baseDir . '/src/editors/framework/seo/posts/social-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Posts\\Title_Data_Provider' => $baseDir . '/src/editors/framework/seo/posts/title-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Social_Data_Provider_Interface' => $baseDir . '/src/editors/framework/seo/social-data-provider-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Terms\\Abstract_Term_Seo_Data_Provider' => $baseDir . '/src/editors/framework/seo/terms/abstract-term-seo-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Terms\\Description_Data_Provider' => $baseDir . '/src/editors/framework/seo/terms/description-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Terms\\Keyphrase_Data_Provider' => $baseDir . '/src/editors/framework/seo/terms/keyphrase-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Terms\\Social_Data_Provider' => $baseDir . '/src/editors/framework/seo/terms/social-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Terms\\Title_Data_Provider' => $baseDir . '/src/editors/framework/seo/terms/title-data-provider.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Seo\\Title_Data_Provider_Interface' => $baseDir . '/src/editors/framework/seo/title-data-provider-interface.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Site\\Base_Site_Information' => $baseDir . '/src/editors/framework/site/base-site-information.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Site\\Post_Site_Information' => $baseDir . '/src/editors/framework/site/post-site-information.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Site\\Term_Site_Information' => $baseDir . '/src/editors/framework/site/term-site-information.php',
    'Yoast\\WP\\SEO\\Editors\\Framework\\Word_Form_Recognition' => $baseDir . '/src/editors/framework/word-form-recognition.php',
    'Yoast\\WP\\SEO\\Elementor\\Infrastructure\\Request_Post' => $baseDir . '/src/elementor/infrastructure/request-post.php',
    'Yoast\\WP\\SEO\\Exceptions\\Addon_Installation\\Addon_Activation_Error_Exception' => $baseDir . '/src/exceptions/addon-installation/addon-activation-error-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Addon_Installation\\Addon_Already_Installed_Exception' => $baseDir . '/src/exceptions/addon-installation/addon-already-installed-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Addon_Installation\\Addon_Installation_Error_Exception' => $baseDir . '/src/exceptions/addon-installation/addon-installation-error-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Addon_Installation\\User_Cannot_Activate_Plugins_Exception' => $baseDir . '/src/exceptions/addon-installation/user-cannot-activate-plugins-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Addon_Installation\\User_Cannot_Install_Plugins_Exception' => $baseDir . '/src/exceptions/addon-installation/user-cannot-install-plugins-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Forbidden_Property_Mutation_Exception' => $baseDir . '/src/exceptions/forbidden-property-mutation-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Importing\\Aioseo_Validation_Exception' => $baseDir . '/src/exceptions/importing/aioseo-validation-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Author_Not_Built_Exception' => $baseDir . '/src/exceptions/indexable/author-not-built-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Indexable_Exception' => $baseDir . '/src/exceptions/indexable/indexable-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Invalid_Term_Exception' => $baseDir . '/src/exceptions/indexable/invalid-term-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Not_Built_Exception' => $baseDir . '/src/exceptions/indexable/not-built-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Post_Not_Built_Exception' => $baseDir . '/src/exceptions/indexable/post-not-built-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Post_Not_Found_Exception' => $baseDir . '/src/exceptions/indexable/post-not-found-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Post_Type_Not_Built_Exception' => $baseDir . '/src/exceptions/indexable/post-type-not-built-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Source_Exception' => $baseDir . '/src/exceptions/indexable/source-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Term_Not_Built_Exception' => $baseDir . '/src/exceptions/indexable/term-not-built-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Indexable\\Term_Not_Found_Exception' => $baseDir . '/src/exceptions/indexable/term-not-found-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\Missing_Method' => $baseDir . '/src/exceptions/missing-method.php',
    'Yoast\\WP\\SEO\\Exceptions\\OAuth\\Authentication_Failed_Exception' => $baseDir . '/src/exceptions/oauth/authentication-failed-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\OAuth\\Tokens\\Empty_Property_Exception' => $baseDir . '/src/exceptions/oauth/tokens/empty-property-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\OAuth\\Tokens\\Empty_Token_Exception' => $baseDir . '/src/exceptions/oauth/tokens/empty-token-exception.php',
    'Yoast\\WP\\SEO\\Exceptions\\OAuth\\Tokens\\Failed_Storage_Exception' => $baseDir . '/src/exceptions/oauth/tokens/failed-storage-exception.php',
    'Yoast\\WP\\SEO\\General\\User_Interface\\General_Page_Integration' => $baseDir . '/src/general/user-interface/general-page-integration.php',
    'Yoast\\WP\\SEO\\Generated\\Cached_Container' => $baseDir . '/src/generated/container.php',
    'Yoast\\WP\\SEO\\Generators\\Breadcrumbs_Generator' => $baseDir . '/src/generators/breadcrumbs-generator.php',
    'Yoast\\WP\\SEO\\Generators\\Generator_Interface' => $baseDir . '/src/generators/generator-interface.php',
    'Yoast\\WP\\SEO\\Generators\\Open_Graph_Image_Generator' => $baseDir . '/src/generators/open-graph-image-generator.php',
    'Yoast\\WP\\SEO\\Generators\\Open_Graph_Locale_Generator' => $baseDir . '/src/generators/open-graph-locale-generator.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Abstract_Schema_Piece' => $baseDir . '/src/generators/schema/abstract-schema-piece.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Article' => $baseDir . '/src/generators/schema/article.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Author' => $baseDir . '/src/generators/schema/author.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Breadcrumb' => $baseDir . '/src/generators/schema/breadcrumb.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\FAQ' => $baseDir . '/src/generators/schema/faq.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\HowTo' => $baseDir . '/src/generators/schema/howto.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Main_Image' => $baseDir . '/src/generators/schema/main-image.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Organization' => $baseDir . '/src/generators/schema/organization.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Person' => $baseDir . '/src/generators/schema/person.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\WebPage' => $baseDir . '/src/generators/schema/webpage.php',
    'Yoast\\WP\\SEO\\Generators\\Schema\\Website' => $baseDir . '/src/generators/schema/website.php',
    'Yoast\\WP\\SEO\\Generators\\Schema_Generator' => $baseDir . '/src/generators/schema-generator.php',
    'Yoast\\WP\\SEO\\Generators\\Twitter_Image_Generator' => $baseDir . '/src/generators/twitter-image-generator.php',
    'Yoast\\WP\\SEO\\Helpers\\Aioseo_Helper' => $baseDir . '/src/helpers/aioseo-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Asset_Helper' => $baseDir . '/src/helpers/asset-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Attachment_Cleanup_Helper' => $baseDir . '/src/helpers/attachment-cleanup-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Author_Archive_Helper' => $baseDir . '/src/helpers/author-archive-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Blocks_Helper' => $baseDir . '/src/helpers/blocks-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Capability_Helper' => $baseDir . '/src/helpers/capability-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Crawl_Cleanup_Helper' => $baseDir . '/src/helpers/crawl-cleanup-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Curl_Helper' => $baseDir . '/src/helpers/curl-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Current_Page_Helper' => $baseDir . '/src/helpers/current-page-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Date_Helper' => $baseDir . '/src/helpers/date-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Environment_Helper' => $baseDir . '/src/helpers/environment-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\First_Time_Configuration_Notice_Helper' => $baseDir . '/src/helpers/first-time-configuration-notice-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Home_Url_Helper' => $baseDir . '/src/helpers/home-url-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Image_Helper' => $baseDir . '/src/helpers/image-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Import_Cursor_Helper' => $baseDir . '/src/helpers/import-cursor-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Import_Helper' => $baseDir . '/src/helpers/import-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Indexable_Helper' => $baseDir . '/src/helpers/indexable-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Indexable_To_Postmeta_Helper' => $baseDir . '/src/helpers/indexable-to-postmeta-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Indexing_Helper' => $baseDir . '/src/helpers/indexing-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Language_Helper' => $baseDir . '/src/helpers/language-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Meta_Helper' => $baseDir . '/src/helpers/meta-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Notification_Helper' => $baseDir . '/src/helpers/notification-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Open_Graph\\Image_Helper' => $baseDir . '/src/helpers/open-graph/image-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Open_Graph\\Values_Helper' => $baseDir . '/src/helpers/open-graph/values-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Options_Helper' => $baseDir . '/src/helpers/options-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Pagination_Helper' => $baseDir . '/src/helpers/pagination-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Permalink_Helper' => $baseDir . '/src/helpers/permalink-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Post_Helper' => $baseDir . '/src/helpers/post-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Post_Type_Helper' => $baseDir . '/src/helpers/post-type-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Primary_Term_Helper' => $baseDir . '/src/helpers/primary-term-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Product_Helper' => $baseDir . '/src/helpers/product-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Redirect_Helper' => $baseDir . '/src/helpers/redirect-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Request_Helper' => $baseDir . '/src/deprecated/src/helpers/request-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Require_File_Helper' => $baseDir . '/src/helpers/require-file-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Robots_Helper' => $baseDir . '/src/helpers/robots-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Robots_Txt_Helper' => $baseDir . '/src/helpers/robots-txt-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Sanitization_Helper' => $baseDir . '/src/helpers/sanitization-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Schema\\Article_Helper' => $baseDir . '/src/helpers/schema/article-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Schema\\HTML_Helper' => $baseDir . '/src/helpers/schema/html-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Schema\\ID_Helper' => $baseDir . '/src/helpers/schema/id-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Schema\\Image_Helper' => $baseDir . '/src/helpers/schema/image-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Schema\\Language_Helper' => $baseDir . '/src/helpers/schema/language-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Schema\\Replace_Vars_Helper' => $baseDir . '/src/helpers/schema/replace-vars-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Score_Icon_Helper' => $baseDir . '/src/helpers/score-icon-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Short_Link_Helper' => $baseDir . '/src/helpers/short-link-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Site_Helper' => $baseDir . '/src/helpers/site-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Social_Profiles_Helper' => $baseDir . '/src/helpers/social-profiles-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\String_Helper' => $baseDir . '/src/helpers/string-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Taxonomy_Helper' => $baseDir . '/src/helpers/taxonomy-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Twitter\\Image_Helper' => $baseDir . '/src/helpers/twitter/image-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Url_Helper' => $baseDir . '/src/helpers/url-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\User_Helper' => $baseDir . '/src/helpers/user-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Wincher_Helper' => $baseDir . '/src/helpers/wincher-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Woocommerce_Helper' => $baseDir . '/src/helpers/woocommerce-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Wordpress_Helper' => $baseDir . '/src/helpers/wordpress-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Wordproof_Helper' => $baseDir . '/src/deprecated/src/helpers/wordproof-helper.php',
    'Yoast\\WP\\SEO\\Helpers\\Wpdb_Helper' => $baseDir . '/src/helpers/wpdb-helper.php',
    'Yoast\\WP\\SEO\\Images\\Application\\Image_Content_Extractor' => $baseDir . '/src/images/Application/image-content-extractor.php',
    'Yoast\\WP\\SEO\\Initializers\\Crawl_Cleanup_Permalinks' => $baseDir . '/src/initializers/crawl-cleanup-permalinks.php',
    'Yoast\\WP\\SEO\\Initializers\\Disable_Core_Sitemaps' => $baseDir . '/src/initializers/disable-core-sitemaps.php',
    'Yoast\\WP\\SEO\\Initializers\\Initializer_Interface' => $baseDir . '/src/initializers/initializer-interface.php',
    'Yoast\\WP\\SEO\\Initializers\\Migration_Runner' => $baseDir . '/src/initializers/migration-runner.php',
    'Yoast\\WP\\SEO\\Initializers\\Plugin_Headers' => $baseDir . '/src/initializers/plugin-headers.php',
    'Yoast\\WP\\SEO\\Initializers\\Woocommerce' => $baseDir . '/src/initializers/woocommerce.php',
    'Yoast\\WP\\SEO\\Integrations\\Abstract_Exclude_Post_Type' => $baseDir . '/src/integrations/abstract-exclude-post-type.php',
    'Yoast\\WP\\SEO\\Integrations\\Academy_Integration' => $baseDir . '/src/integrations/academy-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Activation_Cleanup_Integration' => $baseDir . '/src/integrations/admin/activation-cleanup-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Addon_Installation\\Dialog_Integration' => $baseDir . '/src/integrations/admin/addon-installation/dialog-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Addon_Installation\\Installation_Integration' => $baseDir . '/src/integrations/admin/addon-installation/installation-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Admin_Columns_Cache_Integration' => $baseDir . '/src/integrations/admin/admin-columns-cache-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Background_Indexing_Integration' => $baseDir . '/src/integrations/admin/background-indexing-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Check_Required_Version' => $baseDir . '/src/integrations/admin/check-required-version.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Crawl_Settings_Integration' => $baseDir . '/src/integrations/admin/crawl-settings-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Cron_Integration' => $baseDir . '/src/integrations/admin/cron-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Deactivated_Premium_Integration' => $baseDir . '/src/integrations/admin/deactivated-premium-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Disable_Concatenate_Scripts_Integration' => $baseDir . '/src/deprecated/src/integrations/admin/disable-concatenate-scripts-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\First_Time_Configuration_Integration' => $baseDir . '/src/integrations/admin/first-time-configuration-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\First_Time_Configuration_Notice_Integration' => $baseDir . '/src/integrations/admin/first-time-configuration-notice-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Fix_News_Dependencies_Integration' => $baseDir . '/src/integrations/admin/fix-news-dependencies-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Health_Check_Integration' => $baseDir . '/src/integrations/admin/health-check-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\HelpScout_Beacon' => $baseDir . '/src/integrations/admin/helpscout-beacon.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Import_Integration' => $baseDir . '/src/integrations/admin/import-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Indexables_Exclude_Taxonomy_Integration' => $baseDir . '/src/integrations/admin/indexables-exclude-taxonomy-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Indexing_Notification_Integration' => $baseDir . '/src/integrations/admin/indexing-notification-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Indexing_Tool_Integration' => $baseDir . '/src/integrations/admin/indexing-tool-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Installation_Success_Integration' => $baseDir . '/src/integrations/admin/installation-success-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Integrations_Page' => $baseDir . '/src/integrations/admin/integrations-page.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Link_Count_Columns_Integration' => $baseDir . '/src/integrations/admin/link-count-columns-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Menu_Badge_Integration' => $baseDir . '/src/integrations/admin/menu-badge-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Migration_Error_Integration' => $baseDir . '/src/integrations/admin/migration-error-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Old_Configuration_Integration' => $baseDir . '/src/integrations/admin/old-configuration-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Old_Premium_Integration' => $baseDir . '/src/deprecated/src/integrations/admin/old-premium-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Redirect_Integration' => $baseDir . '/src/integrations/admin/redirect-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Redirects_Page_Integration' => $baseDir . '/src/integrations/admin/redirects-page-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Unsupported_PHP_Version_Notice' => $baseDir . '/src/deprecated/src/integrations/admin/unsupported-php-version-notice.php',
    'Yoast\\WP\\SEO\\Integrations\\Admin\\Workouts_Integration' => $baseDir . '/src/integrations/admin/workouts-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Alerts\\Abstract_Dismissable_Alert' => $baseDir . '/src/integrations/alerts/abstract-dismissable-alert.php',
    'Yoast\\WP\\SEO\\Integrations\\Alerts\\Black_Friday_Product_Editor_Checklist_Notification' => $baseDir . '/src/integrations/alerts/black-friday-product-editor-checklist-notification.php',
    'Yoast\\WP\\SEO\\Integrations\\Alerts\\Black_Friday_Promotion_Notification' => $baseDir . '/src/integrations/alerts/black-friday-promotion-notification.php',
    'Yoast\\WP\\SEO\\Integrations\\Alerts\\Black_Friday_Sidebar_Checklist_Notification' => $baseDir . '/src/integrations/alerts/black-friday-sidebar-checklist-notification.php',
    'Yoast\\WP\\SEO\\Integrations\\Alerts\\Trustpilot_Review_Notification' => $baseDir . '/src/integrations/alerts/trustpilot-review-notification.php',
    'Yoast\\WP\\SEO\\Integrations\\Alerts\\Webinar_Promo_Notification' => $baseDir . '/src/integrations/alerts/webinar-promo-notification.php',
    'Yoast\\WP\\SEO\\Integrations\\Blocks\\Block_Editor_Integration' => $baseDir . '/src/integrations/blocks/block-editor-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Blocks\\Breadcrumbs_Block' => $baseDir . '/src/integrations/blocks/breadcrumbs-block.php',
    'Yoast\\WP\\SEO\\Integrations\\Blocks\\Dynamic_Block' => $baseDir . '/src/integrations/blocks/abstract-dynamic-block.php',
    'Yoast\\WP\\SEO\\Integrations\\Blocks\\Dynamic_Block_V3' => $baseDir . '/src/integrations/blocks/abstract-dynamic-block-v3.php',
    'Yoast\\WP\\SEO\\Integrations\\Blocks\\Internal_Linking_Category' => $baseDir . '/src/integrations/blocks/block-categories.php',
    'Yoast\\WP\\SEO\\Integrations\\Blocks\\Structured_Data_Blocks' => $baseDir . '/src/integrations/blocks/structured-data-blocks.php',
    'Yoast\\WP\\SEO\\Integrations\\Breadcrumbs_Integration' => $baseDir . '/src/integrations/breadcrumbs-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Cleanup_Integration' => $baseDir . '/src/integrations/cleanup-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Duplicate_Post_Integration' => $baseDir . '/src/deprecated/src/integrations/duplicate-post-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Estimated_Reading_Time' => $baseDir . '/src/integrations/estimated-reading-time.php',
    'Yoast\\WP\\SEO\\Integrations\\Exclude_Attachment_Post_Type' => $baseDir . '/src/integrations/exclude-attachment-post-type.php',
    'Yoast\\WP\\SEO\\Integrations\\Exclude_Oembed_Cache_Post_Type' => $baseDir . '/src/integrations/exclude-oembed-cache-post-type.php',
    'Yoast\\WP\\SEO\\Integrations\\Feature_Flag_Integration' => $baseDir . '/src/integrations/feature-flag-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Backwards_Compatibility' => $baseDir . '/src/integrations/front-end/backwards-compatibility.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Category_Term_Description' => $baseDir . '/src/integrations/front-end/category-term-description.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Comment_Link_Fixer' => $baseDir . '/src/integrations/front-end/comment-link-fixer.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Crawl_Cleanup_Basic' => $baseDir . '/src/integrations/front-end/crawl-cleanup-basic.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Crawl_Cleanup_Rss' => $baseDir . '/src/integrations/front-end/crawl-cleanup-rss.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Crawl_Cleanup_Searches' => $baseDir . '/src/integrations/front-end/crawl-cleanup-searches.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Feed_Improvements' => $baseDir . '/src/integrations/front-end/feed-improvements.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Force_Rewrite_Title' => $baseDir . '/src/integrations/front-end/force-rewrite-title.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Handle_404' => $baseDir . '/src/integrations/front-end/handle-404.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Indexing_Controls' => $baseDir . '/src/integrations/front-end/indexing-controls.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Open_Graph_OEmbed' => $baseDir . '/src/integrations/front-end/open-graph-oembed.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\RSS_Footer_Embed' => $baseDir . '/src/integrations/front-end/rss-footer-embed.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Redirects' => $baseDir . '/src/integrations/front-end/redirects.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Robots_Txt_Integration' => $baseDir . '/src/integrations/front-end/robots-txt-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\Schema_Accessibility_Feature' => $baseDir . '/src/integrations/front-end/schema-accessibility-feature.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End\\WP_Robots_Integration' => $baseDir . '/src/integrations/front-end/wp-robots-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Front_End_Integration' => $baseDir . '/src/integrations/front-end-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Integration_Interface' => $baseDir . '/src/integrations/integration-interface.php',
    'Yoast\\WP\\SEO\\Integrations\\Primary_Category' => $baseDir . '/src/integrations/primary-category.php',
    'Yoast\\WP\\SEO\\Integrations\\Settings_Integration' => $baseDir . '/src/integrations/settings-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Support_Integration' => $baseDir . '/src/integrations/support-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\AMP' => $baseDir . '/src/integrations/third-party/amp.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\BbPress' => $baseDir . '/src/integrations/third-party/bbpress.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Elementor' => $baseDir . '/src/integrations/third-party/elementor.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Exclude_Elementor_Post_Types' => $baseDir . '/src/integrations/third-party/exclude-elementor-post-types.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Exclude_WooCommerce_Post_Types' => $baseDir . '/src/integrations/third-party/exclude-woocommerce-post-types.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Jetpack' => $baseDir . '/src/integrations/third-party/jetpack.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\W3_Total_Cache' => $baseDir . '/src/integrations/third-party/w3-total-cache.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\WPML' => $baseDir . '/src/integrations/third-party/wpml.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\WPML_WPSEO_Notification' => $baseDir . '/src/integrations/third-party/wpml-wpseo-notification.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Web_Stories' => $baseDir . '/src/integrations/third-party/web-stories.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Web_Stories_Post_Edit' => $baseDir . '/src/integrations/third-party/web-stories-post-edit.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Wincher' => $baseDir . '/src/deprecated/src/integrations/third-party/wincher.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Wincher_Publish' => $baseDir . '/src/integrations/third-party/wincher-publish.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\WooCommerce' => $baseDir . '/src/integrations/third-party/woocommerce.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\WooCommerce_Post_Edit' => $baseDir . '/src/integrations/third-party/woocommerce-post-edit.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Woocommerce_Permalinks' => $baseDir . '/src/integrations/third-party/woocommerce-permalinks.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Wordproof' => $baseDir . '/src/deprecated/src/integrations/third-party/wordproof.php',
    'Yoast\\WP\\SEO\\Integrations\\Third_Party\\Wordproof_Integration_Toggle' => $baseDir . '/src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php',
    'Yoast\\WP\\SEO\\Integrations\\Uninstall_Integration' => $baseDir . '/src/integrations/uninstall-integration.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Addon_Update_Watcher' => $baseDir . '/src/integrations/watchers/addon-update-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Auto_Update_Watcher' => $baseDir . '/src/integrations/watchers/auto-update-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Ancestor_Watcher' => $baseDir . '/src/integrations/watchers/indexable-ancestor-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Attachment_Watcher' => $baseDir . '/src/integrations/watchers/indexable-attachment-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Author_Archive_Watcher' => $baseDir . '/src/integrations/watchers/indexable-author-archive-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Author_Watcher' => $baseDir . '/src/integrations/watchers/indexable-author-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Category_Permalink_Watcher' => $baseDir . '/src/integrations/watchers/indexable-category-permalink-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Date_Archive_Watcher' => $baseDir . '/src/integrations/watchers/indexable-date-archive-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_HomeUrl_Watcher' => $baseDir . '/src/integrations/watchers/indexable-homeurl-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Home_Page_Watcher' => $baseDir . '/src/integrations/watchers/indexable-home-page-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Permalink_Watcher' => $baseDir . '/src/integrations/watchers/indexable-permalink-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Post_Meta_Watcher' => $baseDir . '/src/integrations/watchers/indexable-post-meta-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Post_Type_Archive_Watcher' => $baseDir . '/src/integrations/watchers/indexable-post-type-archive-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Post_Type_Change_Watcher' => $baseDir . '/src/integrations/watchers/indexable-post-type-change-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Post_Watcher' => $baseDir . '/src/integrations/watchers/indexable-post-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Static_Home_Page_Watcher' => $baseDir . '/src/integrations/watchers/indexable-static-home-page-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_System_Page_Watcher' => $baseDir . '/src/integrations/watchers/indexable-system-page-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Taxonomy_Change_Watcher' => $baseDir . '/src/integrations/watchers/indexable-taxonomy-change-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Indexable_Term_Watcher' => $baseDir . '/src/integrations/watchers/indexable-term-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Option_Titles_Watcher' => $baseDir . '/src/integrations/watchers/option-titles-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Option_Wpseo_Watcher' => $baseDir . '/src/integrations/watchers/option-wpseo-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Primary_Category_Quick_Edit_Watcher' => $baseDir . '/src/integrations/watchers/primary-category-quick-edit-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Primary_Term_Watcher' => $baseDir . '/src/integrations/watchers/primary-term-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Search_Engines_Discouraged_Watcher' => $baseDir . '/src/integrations/watchers/search-engines-discouraged-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\Watchers\\Woocommerce_Beta_Editor_Watcher' => $baseDir . '/src/integrations/watchers/woocommerce-beta-editor-watcher.php',
    'Yoast\\WP\\SEO\\Integrations\\XMLRPC' => $baseDir . '/src/integrations/xmlrpc.php',
    'Yoast\\WP\\SEO\\Introductions\\Application\\Ai_Fix_Assessments_Upsell' => $baseDir . '/src/introductions/application/ai-fix-assessments-upsell.php',
    'Yoast\\WP\\SEO\\Introductions\\Application\\Ai_Generate_Titles_And_Descriptions_Introduction_Upsell' => $baseDir . '/src/deprecated/src/introductions/application/ai-generate-titles-and-descriptions-introduction-upsell.php',
    'Yoast\\WP\\SEO\\Introductions\\Application\\Current_Page_Trait' => $baseDir . '/src/introductions/application/current-page-trait.php',
    'Yoast\\WP\\SEO\\Introductions\\Application\\Introductions_Collector' => $baseDir . '/src/introductions/application/introductions-collector.php',
    'Yoast\\WP\\SEO\\Introductions\\Application\\User_Allowed_Trait' => $baseDir . '/src/introductions/application/user-allowed-trait.php',
    'Yoast\\WP\\SEO\\Introductions\\Application\\Version_Trait' => $baseDir . '/src/introductions/application/version-trait.php',
    'Yoast\\WP\\SEO\\Introductions\\Domain\\Introduction_Interface' => $baseDir . '/src/introductions/domain/introduction-interface.php',
    'Yoast\\WP\\SEO\\Introductions\\Domain\\Introduction_Item' => $baseDir . '/src/introductions/domain/introduction-item.php',
    'Yoast\\WP\\SEO\\Introductions\\Domain\\Introductions_Bucket' => $baseDir . '/src/introductions/domain/introductions-bucket.php',
    'Yoast\\WP\\SEO\\Introductions\\Domain\\Invalid_User_Id_Exception' => $baseDir . '/src/introductions/domain/invalid-user-id-exception.php',
    'Yoast\\WP\\SEO\\Introductions\\Infrastructure\\Introductions_Seen_Repository' => $baseDir . '/src/introductions/infrastructure/introductions-seen-repository.php',
    'Yoast\\WP\\SEO\\Introductions\\Infrastructure\\Wistia_Embed_Permission_Repository' => $baseDir . '/src/introductions/infrastructure/wistia-embed-permission-repository.php',
    'Yoast\\WP\\SEO\\Introductions\\User_Interface\\Introductions_Integration' => $baseDir . '/src/introductions/user-interface/introductions-integration.php',
    'Yoast\\WP\\SEO\\Introductions\\User_Interface\\Introductions_Seen_Route' => $baseDir . '/src/introductions/user-interface/introductions-seen-route.php',
    'Yoast\\WP\\SEO\\Introductions\\User_Interface\\Wistia_Embed_Permission_Route' => $baseDir . '/src/introductions/user-interface/wistia-embed-permission-route.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\File\\Commands\\Populate_File_Command_Handler' => $baseDir . '/src/llms-txt/application/file/commands/populate-file-command-handler.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\File\\Commands\\Remove_File_Command_Handler' => $baseDir . '/src/llms-txt/application/file/commands/remove-file-command-handler.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\File\\Llms_Txt_Cron_Scheduler' => $baseDir . '/src/llms-txt/application/file/llms-txt-cron-scheduler.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Health_Check\\File_Check' => $baseDir . '/src/llms-txt/application/health-check/file-check.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Health_Check\\File_Runner' => $baseDir . '/src/llms-txt/application/health-check/file-runner.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Markdown_Builders\\Description_Builder' => $baseDir . '/src/llms-txt/application/markdown-builders/description-builder.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Markdown_Builders\\Intro_Builder' => $baseDir . '/src/llms-txt/application/markdown-builders/intro-builder.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Markdown_Builders\\Link_Lists_Builder' => $baseDir . '/src/llms-txt/application/markdown-builders/link-lists-builder.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Markdown_Builders\\Markdown_Builder' => $baseDir . '/src/llms-txt/application/markdown-builders/markdown-builder.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Markdown_Builders\\Title_Builder' => $baseDir . '/src/llms-txt/application/markdown-builders/title-builder.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Application\\Markdown_Escaper' => $baseDir . '/src/llms-txt/application/markdown-escaper.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\File\\Llms_File_System_Interface' => $baseDir . '/src/llms-txt/domain/file/llms-file-system-interface.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\File\\Llms_Txt_Permission_Gate_Interface' => $baseDir . '/src/llms-txt/domain/file/llms-txt-permission-gate-interface.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Items\\Item_Interface' => $baseDir . '/src/llms-txt/domain/markdown/items/item-interface.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Items\\Link' => $baseDir . '/src/llms-txt/domain/markdown/items/link.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Llms_Txt_Renderer' => $baseDir . '/src/llms-txt/domain/markdown/llms-txt-renderer.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Sections\\Description' => $baseDir . '/src/llms-txt/domain/markdown/sections/description.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Sections\\Intro' => $baseDir . '/src/llms-txt/domain/markdown/sections/intro.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Sections\\Link_List' => $baseDir . '/src/llms-txt/domain/markdown/sections/link-list.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Sections\\Section_Interface' => $baseDir . '/src/llms-txt/domain/markdown/sections/section-interface.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Domain\\Markdown\\Sections\\Title' => $baseDir . '/src/llms-txt/domain/markdown/sections/title.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\File\\WordPress_File_System_Adapter' => $baseDir . '/src/llms-txt/infrastructure/file/wordpress-file-system-adapter.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\File\\WordPress_Llms_Txt_Permission_Gate' => $baseDir . '/src/llms-txt/infrastructure/file/wordpress-llms-txt-permission-gate.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\Markdown_Services\\Content_Types_Collector' => $baseDir . '/src/llms-txt/infrastructure/markdown-services/content-types-collector.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\Markdown_Services\\Description_Adapter' => $baseDir . '/src/llms-txt/infrastructure/markdown-services/description-adapter.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\Markdown_Services\\Sitemap_Link_Collector' => $baseDir . '/src/llms-txt/infrastructure/markdown-services/sitemap-link-collector.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\Markdown_Services\\Terms_Collector' => $baseDir . '/src/llms-txt/infrastructure/markdown-services/terms-collector.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\Infrastructure\\Markdown_Services\\Title_Adapter' => $baseDir . '/src/llms-txt/infrastructure/markdown-services/title-adapter.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\User_Interface\\Cleanup_Llms_Txt_On_Deactivation' => $baseDir . '/src/llms-txt/user-interface/cleanup-llms-txt-on-deactivation.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\User_Interface\\Enable_Llms_Txt_Option_Watcher' => $baseDir . '/src/llms-txt/user-interface/enable-llms-txt-option-watcher.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\User_Interface\\Health_Check\\File_Reports' => $baseDir . '/src/llms-txt/user-interface/health-check/file-reports.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\User_Interface\\Llms_Txt_Cron_Callback_Integration' => $baseDir . '/src/llms-txt/user-interface/llms-txt-cron-callback-integration.php',
    'Yoast\\WP\\SEO\\Llms_Txt\\User_Interface\\Schedule_Population_On_Activation_Integration' => $baseDir . '/src/llms-txt/user-interface/schedule-population-on-activation-integration.php',
    'Yoast\\WP\\SEO\\Loadable_Interface' => $baseDir . '/src/loadable-interface.php',
    'Yoast\\WP\\SEO\\Loader' => $baseDir . '/src/loader.php',
    'Yoast\\WP\\SEO\\Loggers\\Logger' => $baseDir . '/src/loggers/logger.php',
    'Yoast\\WP\\SEO\\Main' => $baseDir . '/src/main.php',
    'Yoast\\WP\\SEO\\Memoizers\\Meta_Tags_Context_Memoizer' => $baseDir . '/src/memoizers/meta-tags-context-memoizer.php',
    'Yoast\\WP\\SEO\\Memoizers\\Presentation_Memoizer' => $baseDir . '/src/memoizers/presentation-memoizer.php',
    'Yoast\\WP\\SEO\\Models\\Indexable' => $baseDir . '/src/models/indexable.php',
    'Yoast\\WP\\SEO\\Models\\Indexable_Extension' => $baseDir . '/src/models/indexable-extension.php',
    'Yoast\\WP\\SEO\\Models\\Indexable_Hierarchy' => $baseDir . '/src/models/indexable-hierarchy.php',
    'Yoast\\WP\\SEO\\Models\\Primary_Term' => $baseDir . '/src/models/primary-term.php',
    'Yoast\\WP\\SEO\\Models\\SEO_Links' => $baseDir . '/src/models/seo-links.php',
    'Yoast\\WP\\SEO\\Models\\SEO_Meta' => $baseDir . '/src/models/seo-meta.php',
    'Yoast\\WP\\SEO\\Presentations\\Abstract_Presentation' => $baseDir . '/src/presentations/abstract-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Archive_Adjacent' => $baseDir . '/src/presentations/archive-adjacent-trait.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Author_Archive_Presentation' => $baseDir . '/src/presentations/indexable-author-archive-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Date_Archive_Presentation' => $baseDir . '/src/presentations/indexable-date-archive-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Error_Page_Presentation' => $baseDir . '/src/presentations/indexable-error-page-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Home_Page_Presentation' => $baseDir . '/src/presentations/indexable-home-page-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Post_Type_Archive_Presentation' => $baseDir . '/src/presentations/indexable-post-type-archive-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Post_Type_Presentation' => $baseDir . '/src/presentations/indexable-post-type-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Presentation' => $baseDir . '/src/presentations/indexable-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Search_Result_Page_Presentation' => $baseDir . '/src/presentations/indexable-search-result-page-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Static_Home_Page_Presentation' => $baseDir . '/src/presentations/indexable-static-home-page-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Static_Posts_Page_Presentation' => $baseDir . '/src/presentations/indexable-static-posts-page-presentation.php',
    'Yoast\\WP\\SEO\\Presentations\\Indexable_Term_Archive_Presentation' => $baseDir . '/src/presentations/indexable-term-archive-presentation.php',
    'Yoast\\WP\\SEO\\Presenters\\Abstract_Indexable_Presenter' => $baseDir . '/src/presenters/abstract-indexable-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Abstract_Indexable_Tag_Presenter' => $baseDir . '/src/presenters/abstract-indexable-tag-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Abstract_Presenter' => $baseDir . '/src/presenters/abstract-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Alert_Presenter' => $baseDir . '/src/presenters/admin/alert-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Badge_Presenter' => $baseDir . '/src/presenters/admin/badge-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Beta_Badge_Presenter' => $baseDir . '/src/presenters/admin/beta-badge-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Help_Link_Presenter' => $baseDir . '/src/presenters/admin/help-link-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Indexing_Error_Presenter' => $baseDir . '/src/presenters/admin/indexing-error-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Indexing_Failed_Notification_Presenter' => $baseDir . '/src/presenters/admin/indexing-failed-notification-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Indexing_List_Item_Presenter' => $baseDir . '/src/presenters/admin/indexing-list-item-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Indexing_Notification_Presenter' => $baseDir . '/src/presenters/admin/indexing-notification-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Light_Switch_Presenter' => $baseDir . '/src/presenters/admin/light-switch-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Meta_Fields_Presenter' => $baseDir . '/src/presenters/admin/meta-fields-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Migration_Error_Presenter' => $baseDir . '/src/presenters/admin/migration-error-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Notice_Presenter' => $baseDir . '/src/presenters/admin/notice-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Premium_Badge_Presenter' => $baseDir . '/src/presenters/admin/premium-badge-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Search_Engines_Discouraged_Presenter' => $baseDir . '/src/presenters/admin/search-engines-discouraged-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Sidebar_Presenter' => $baseDir . '/src/presenters/admin/sidebar-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Admin\\Woocommerce_Beta_Editor_Presenter' => $baseDir . '/src/presenters/admin/woocommerce-beta-editor-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Breadcrumbs_Presenter' => $baseDir . '/src/presenters/breadcrumbs-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Canonical_Presenter' => $baseDir . '/src/presenters/canonical-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Debug\\Marker_Close_Presenter' => $baseDir . '/src/presenters/debug/marker-close-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Debug\\Marker_Open_Presenter' => $baseDir . '/src/presenters/debug/marker-open-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Meta_Author_Presenter' => $baseDir . '/src/presenters/meta-author-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Meta_Description_Presenter' => $baseDir . '/src/presenters/meta-description-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Article_Author_Presenter' => $baseDir . '/src/presenters/open-graph/article-author-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Article_Modified_Time_Presenter' => $baseDir . '/src/presenters/open-graph/article-modified-time-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Article_Published_Time_Presenter' => $baseDir . '/src/presenters/open-graph/article-published-time-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Article_Publisher_Presenter' => $baseDir . '/src/presenters/open-graph/article-publisher-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Description_Presenter' => $baseDir . '/src/presenters/open-graph/description-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Image_Presenter' => $baseDir . '/src/presenters/open-graph/image-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Locale_Presenter' => $baseDir . '/src/presenters/open-graph/locale-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Site_Name_Presenter' => $baseDir . '/src/presenters/open-graph/site-name-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Title_Presenter' => $baseDir . '/src/presenters/open-graph/title-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Type_Presenter' => $baseDir . '/src/presenters/open-graph/type-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Open_Graph\\Url_Presenter' => $baseDir . '/src/presenters/open-graph/url-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Rel_Next_Presenter' => $baseDir . '/src/presenters/rel-next-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Rel_Prev_Presenter' => $baseDir . '/src/presenters/rel-prev-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Robots_Presenter' => $baseDir . '/src/presenters/robots-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Robots_Txt_Presenter' => $baseDir . '/src/presenters/robots-txt-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Schema_Presenter' => $baseDir . '/src/presenters/schema-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Score_Icon_Presenter' => $baseDir . '/src/presenters/score-icon-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Slack\\Enhanced_Data_Presenter' => $baseDir . '/src/presenters/slack/enhanced-data-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Title_Presenter' => $baseDir . '/src/presenters/title-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Twitter\\Card_Presenter' => $baseDir . '/src/presenters/twitter/card-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Twitter\\Creator_Presenter' => $baseDir . '/src/presenters/twitter/creator-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Twitter\\Description_Presenter' => $baseDir . '/src/presenters/twitter/description-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Twitter\\Image_Presenter' => $baseDir . '/src/presenters/twitter/image-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Twitter\\Site_Presenter' => $baseDir . '/src/presenters/twitter/site-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Twitter\\Title_Presenter' => $baseDir . '/src/presenters/twitter/title-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Url_List_Presenter' => $baseDir . '/src/presenters/url-list-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Webmaster\\Baidu_Presenter' => $baseDir . '/src/presenters/webmaster/baidu-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Webmaster\\Bing_Presenter' => $baseDir . '/src/presenters/webmaster/bing-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Webmaster\\Google_Presenter' => $baseDir . '/src/presenters/webmaster/google-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Webmaster\\Pinterest_Presenter' => $baseDir . '/src/presenters/webmaster/pinterest-presenter.php',
    'Yoast\\WP\\SEO\\Presenters\\Webmaster\\Yandex_Presenter' => $baseDir . '/src/presenters/webmaster/yandex-presenter.php',
    'Yoast\\WP\\SEO\\Promotions\\Application\\Promotion_Manager' => $baseDir . '/src/promotions/application/promotion-manager.php',
    'Yoast\\WP\\SEO\\Promotions\\Application\\Promotion_Manager_Interface' => $baseDir . '/src/promotions/application/promotion-manager-interface.php',
    'Yoast\\WP\\SEO\\Promotions\\Domain\\Abstract_Promotion' => $baseDir . '/src/promotions/domain/abstract-promotion.php',
    'Yoast\\WP\\SEO\\Promotions\\Domain\\Black_Friday_Checklist_Promotion' => $baseDir . '/src/promotions/domain/black-friday-checklist-promotion.php',
    'Yoast\\WP\\SEO\\Promotions\\Domain\\Black_Friday_Promotion' => $baseDir . '/src/promotions/domain/black-friday-promotion.php',
    'Yoast\\WP\\SEO\\Promotions\\Domain\\Promotion_Interface' => $baseDir . '/src/promotions/domain/promotion-interface.php',
    'Yoast\\WP\\SEO\\Promotions\\Domain\\Time_Interval' => $baseDir . '/src/promotions/domain/time-interval.php',
    'Yoast\\WP\\SEO\\Repositories\\Indexable_Cleanup_Repository' => $baseDir . '/src/repositories/indexable-cleanup-repository.php',
    'Yoast\\WP\\SEO\\Repositories\\Indexable_Hierarchy_Repository' => $baseDir . '/src/repositories/indexable-hierarchy-repository.php',
    'Yoast\\WP\\SEO\\Repositories\\Indexable_Repository' => $baseDir . '/src/repositories/indexable-repository.php',
    'Yoast\\WP\\SEO\\Repositories\\Primary_Term_Repository' => $baseDir . '/src/repositories/primary-term-repository.php',
    'Yoast\\WP\\SEO\\Repositories\\SEO_Links_Repository' => $baseDir . '/src/repositories/seo-links-repository.php',
    'Yoast\\WP\\SEO\\Routes\\Abstract_Action_Route' => $baseDir . '/src/routes/abstract-action-route.php',
    'Yoast\\WP\\SEO\\Routes\\Abstract_Indexation_Route' => $baseDir . '/src/routes/abstract-indexation-route.php',
    'Yoast\\WP\\SEO\\Routes\\Alert_Dismissal_Route' => $baseDir . '/src/routes/alert-dismissal-route.php',
    'Yoast\\WP\\SEO\\Routes\\First_Time_Configuration_Route' => $baseDir . '/src/routes/first-time-configuration-route.php',
    'Yoast\\WP\\SEO\\Routes\\Importing_Route' => $baseDir . '/src/routes/importing-route.php',
    'Yoast\\WP\\SEO\\Routes\\Indexables_Head_Route' => $baseDir . '/src/routes/indexables-head-route.php',
    'Yoast\\WP\\SEO\\Routes\\Indexing_Route' => $baseDir . '/src/routes/indexing-route.php',
    'Yoast\\WP\\SEO\\Routes\\Integrations_Route' => $baseDir . '/src/routes/integrations-route.php',
    'Yoast\\WP\\SEO\\Routes\\Meta_Search_Route' => $baseDir . '/src/routes/meta-search-route.php',
    'Yoast\\WP\\SEO\\Routes\\Route_Interface' => $baseDir . '/src/routes/route-interface.php',
    'Yoast\\WP\\SEO\\Routes\\SEMrush_Route' => $baseDir . '/src/routes/semrush-route.php',
    'Yoast\\WP\\SEO\\Routes\\Supported_Features_Route' => $baseDir . '/src/routes/supported-features-route.php',
    'Yoast\\WP\\SEO\\Routes\\Wincher_Route' => $baseDir . '/src/routes/wincher-route.php',
    'Yoast\\WP\\SEO\\Routes\\Workouts_Route' => $baseDir . '/src/routes/workouts-route.php',
    'Yoast\\WP\\SEO\\Routes\\Yoast_Head_REST_Field' => $baseDir . '/src/routes/yoast-head-rest-field.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Default_Tagline_Check' => $baseDir . '/src/services/health-check/default-tagline-check.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Default_Tagline_Reports' => $baseDir . '/src/services/health-check/default-tagline-reports.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Default_Tagline_Runner' => $baseDir . '/src/services/health-check/default-tagline-runner.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Health_Check' => $baseDir . '/src/services/health-check/health-check.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Links_Table_Check' => $baseDir . '/src/services/health-check/links-table-check.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Links_Table_Reports' => $baseDir . '/src/services/health-check/links-table-reports.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Links_Table_Runner' => $baseDir . '/src/services/health-check/links-table-runner.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\MyYoast_Api_Request_Factory' => $baseDir . '/src/services/health-check/myyoast-api-request-factory.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Page_Comments_Check' => $baseDir . '/src/services/health-check/page-comments-check.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Page_Comments_Reports' => $baseDir . '/src/services/health-check/page-comments-reports.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Page_Comments_Runner' => $baseDir . '/src/services/health-check/page-comments-runner.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Postname_Permalink_Check' => $baseDir . '/src/services/health-check/postname-permalink-check.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Postname_Permalink_Reports' => $baseDir . '/src/services/health-check/postname-permalink-reports.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Postname_Permalink_Runner' => $baseDir . '/src/services/health-check/postname-permalink-runner.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Report_Builder' => $baseDir . '/src/services/health-check/report-builder.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Report_Builder_Factory' => $baseDir . '/src/services/health-check/report-builder-factory.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Reports_Trait' => $baseDir . '/src/services/health-check/reports-trait.php',
    'Yoast\\WP\\SEO\\Services\\Health_Check\\Runner_Interface' => $baseDir . '/src/services/health-check/runner-interface.php',
    'Yoast\\WP\\SEO\\Services\\Importing\\Aioseo\\Aioseo_Replacevar_Service' => $baseDir . '/src/services/importing/aioseo/aioseo-replacevar-service.php',
    'Yoast\\WP\\SEO\\Services\\Importing\\Aioseo\\Aioseo_Robots_Provider_Service' => $baseDir . '/src/services/importing/aioseo/aioseo-robots-provider-service.php',
    'Yoast\\WP\\SEO\\Services\\Importing\\Aioseo\\Aioseo_Robots_Transformer_Service' => $baseDir . '/src/services/importing/aioseo/aioseo-robots-transformer-service.php',
    'Yoast\\WP\\SEO\\Services\\Importing\\Aioseo\\Aioseo_Social_Images_Provider_Service' => $baseDir . '/src/services/importing/aioseo/aioseo-social-images-provider-service.php',
    'Yoast\\WP\\SEO\\Services\\Importing\\Conflicting_Plugins_Service' => $baseDir . '/src/services/importing/conflicting-plugins-service.php',
    'Yoast\\WP\\SEO\\Services\\Importing\\Importable_Detector_Service' => $baseDir . '/src/services/importing/importable-detector-service.php',
    'Yoast\\WP\\SEO\\Services\\Indexables\\Indexable_Version_Manager' => $baseDir . '/src/services/indexables/indexable-version-manager.php',
    'Yoast\\WP\\SEO\\Surfaces\\Classes_Surface' => $baseDir . '/src/surfaces/classes-surface.php',
    'Yoast\\WP\\SEO\\Surfaces\\Helpers_Surface' => $baseDir . '/src/surfaces/helpers-surface.php',
    'Yoast\\WP\\SEO\\Surfaces\\Meta_Surface' => $baseDir . '/src/surfaces/meta-surface.php',
    'Yoast\\WP\\SEO\\Surfaces\\Open_Graph_Helpers_Surface' => $baseDir . '/src/surfaces/open-graph-helpers-surface.php',
    'Yoast\\WP\\SEO\\Surfaces\\Schema_Helpers_Surface' => $baseDir . '/src/surfaces/schema-helpers-surface.php',
    'Yoast\\WP\\SEO\\Surfaces\\Twitter_Helpers_Surface' => $baseDir . '/src/surfaces/twitter-helpers-surface.php',
    'Yoast\\WP\\SEO\\Surfaces\\Values\\Meta' => $baseDir . '/src/surfaces/values/meta.php',
    'Yoast\\WP\\SEO\\User_Meta\\Application\\Additional_Contactmethods_Collector' => $baseDir . '/src/user-meta/application/additional-contactmethods-collector.php',
    'Yoast\\WP\\SEO\\User_Meta\\Application\\Cleanup_Service' => $baseDir . '/src/user-meta/application/cleanup-service.php',
    'Yoast\\WP\\SEO\\User_Meta\\Application\\Custom_Meta_Collector' => $baseDir . '/src/user-meta/application/custom-meta-collector.php',
    'Yoast\\WP\\SEO\\User_Meta\\Domain\\Additional_Contactmethod_Interface' => $baseDir . '/src/user-meta/domain/additional-contactmethod-interface.php',
    'Yoast\\WP\\SEO\\User_Meta\\Domain\\Custom_Meta_Interface' => $baseDir . '/src/user-meta/domain/custom-meta-interface.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Facebook' => $baseDir . '/src/user-meta/framework/additional-contactmethods/facebook.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Instagram' => $baseDir . '/src/user-meta/framework/additional-contactmethods/instagram.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Linkedin' => $baseDir . '/src/user-meta/framework/additional-contactmethods/linkedin.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Myspace' => $baseDir . '/src/user-meta/framework/additional-contactmethods/myspace.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Pinterest' => $baseDir . '/src/user-meta/framework/additional-contactmethods/pinterest.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Soundcloud' => $baseDir . '/src/user-meta/framework/additional-contactmethods/soundcloud.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Tumblr' => $baseDir . '/src/user-meta/framework/additional-contactmethods/tumblr.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Wikipedia' => $baseDir . '/src/user-meta/framework/additional-contactmethods/wikipedia.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\X' => $baseDir . '/src/user-meta/framework/additional-contactmethods/x.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Additional_Contactmethods\\Youtube' => $baseDir . '/src/user-meta/framework/additional-contactmethods/youtube.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Custom_Meta\\Author_Metadesc' => $baseDir . '/src/user-meta/framework/custom-meta/author-metadesc.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Custom_Meta\\Author_Title' => $baseDir . '/src/user-meta/framework/custom-meta/author-title.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Custom_Meta\\Content_Analysis_Disable' => $baseDir . '/src/user-meta/framework/custom-meta/content-analysis-disable.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Custom_Meta\\Inclusive_Language_Analysis_Disable' => $baseDir . '/src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Custom_Meta\\Keyword_Analysis_Disable' => $baseDir . '/src/user-meta/framework/custom-meta/keyword-analysis-disable.php',
    'Yoast\\WP\\SEO\\User_Meta\\Framework\\Custom_Meta\\Noindex_Author' => $baseDir . '/src/user-meta/framework/custom-meta/noindex-author.php',
    'Yoast\\WP\\SEO\\User_Meta\\Infrastructure\\Cleanup_Repository' => $baseDir . '/src/user-meta/infrastructure/cleanup-repository.php',
    'Yoast\\WP\\SEO\\User_Meta\\User_Interface\\Additional_Contactmethods_Integration' => $baseDir . '/src/user-meta/user-interface/additional-contactmethods-integration.php',
    'Yoast\\WP\\SEO\\User_Meta\\User_Interface\\Cleanup_Integration' => $baseDir . '/src/user-meta/user-interface/cleanup-integration.php',
    'Yoast\\WP\\SEO\\User_Meta\\User_Interface\\Custom_Meta_Integration' => $baseDir . '/src/user-meta/user-interface/custom-meta-integration.php',
    'Yoast\\WP\\SEO\\User_Profiles_Additions\\User_Interface\\User_Profiles_Additions_Ui' => $baseDir . '/src/user-profiles-additions/user-interface/user-profiles-additions-ui.php',
    'Yoast\\WP\\SEO\\Values\\Images' => $baseDir . '/src/values/images.php',
    'Yoast\\WP\\SEO\\Values\\Indexables\\Indexable_Builder_Versions' => $baseDir . '/src/values/indexables/indexable-builder-versions.php',
    'Yoast\\WP\\SEO\\Values\\OAuth\\OAuth_Token' => $baseDir . '/src/values/oauth/oauth-token.php',
    'Yoast\\WP\\SEO\\Values\\Open_Graph\\Images' => $baseDir . '/src/values/open-graph/images.php',
    'Yoast\\WP\\SEO\\Values\\Robots\\Directive' => $baseDir . '/src/values/robots/directive.php',
    'Yoast\\WP\\SEO\\Values\\Robots\\User_Agent' => $baseDir . '/src/values/robots/user-agent.php',
    'Yoast\\WP\\SEO\\Values\\Robots\\User_Agent_List' => $baseDir . '/src/values/robots/user-agent-list.php',
    'Yoast\\WP\\SEO\\Values\\Twitter\\Images' => $baseDir . '/src/values/twitter/images.php',
    'Yoast\\WP\\SEO\\WordPress\\Wrapper' => $baseDir . '/src/wordpress/wrapper.php',
    'Yoast\\WP\\SEO\\Wrappers\\WP_Query_Wrapper' => $baseDir . '/src/wrappers/wp-query-wrapper.php',
    'Yoast\\WP\\SEO\\Wrappers\\WP_Remote_Handler' => $baseDir . '/src/wrappers/wp-remote-handler.php',
    'Yoast\\WP\\SEO\\Wrappers\\WP_Rewrite_Wrapper' => $baseDir . '/src/wrappers/wp-rewrite-wrapper.php',
    'Yoast_Dashboard_Widget' => $baseDir . '/admin/class-yoast-dashboard-widget.php',
    'Yoast_Dismissable_Notice_Ajax' => $baseDir . '/admin/ajax/class-yoast-dismissable-notice.php',
    'Yoast_Dynamic_Rewrites' => $baseDir . '/inc/class-yoast-dynamic-rewrites.php',
    'Yoast_Feature_Toggle' => $baseDir . '/admin/views/class-yoast-feature-toggle.php',
    'Yoast_Feature_Toggles' => $baseDir . '/admin/views/class-yoast-feature-toggles.php',
    'Yoast_Form' => $baseDir . '/admin/class-yoast-form.php',
    'Yoast_Form_Element' => $baseDir . '/admin/views/interface-yoast-form-element.php',
    'Yoast_Input_Select' => $baseDir . '/admin/views/class-yoast-input-select.php',
    'Yoast_Input_Validation' => $baseDir . '/admin/class-yoast-input-validation.php',
    'Yoast_Integration_Toggles' => $baseDir . '/admin/views/class-yoast-integration-toggles.php',
    'Yoast_Network_Admin' => $baseDir . '/admin/class-yoast-network-admin.php',
    'Yoast_Network_Settings_API' => $baseDir . '/admin/class-yoast-network-settings-api.php',
    'Yoast_Notification' => $baseDir . '/admin/class-yoast-notification.php',
    'Yoast_Notification_Center' => $baseDir . '/admin/class-yoast-notification-center.php',
    'Yoast_Notifications' => $baseDir . '/admin/class-yoast-notifications.php',
    'Yoast_Plugin_Conflict' => $baseDir . '/admin/class-yoast-plugin-conflict.php',
    'Yoast_Plugin_Conflict_Ajax' => $baseDir . '/admin/ajax/class-yoast-plugin-conflict-ajax.php',
);
