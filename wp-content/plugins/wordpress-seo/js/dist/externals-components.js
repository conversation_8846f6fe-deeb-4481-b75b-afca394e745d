(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var r in s)e.o(s,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:s[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.i18n,r=window.wp.components,o=window.yoast.componentsNew,n=window.yoast.helpers,a=window.yoast.propTypes;var i=e.n(a);const l=window.wp.element,c=window.yoast.styledComponents;var d=e.n(c);const u=d().div`
	display: flex;
	margin-top: 8px;
`;class p extends l.Component{render(){return(0,t.createElement)(u,null,(0,t.createElement)(o.Toggle,{id:this.props.id,labelText:(0,s.__)("Mark as cornerstone content","wordpress-seo"),isEnabled:this.props.isEnabled,onSetToggleState:this.props.onToggle,onToggleDisabled:this.props.onToggleDisabled}))}}p.propTypes={id:i().string,isEnabled:i().bool,onToggle:i().func,onToggleDisabled:i().func},p.defaultProps={id:"cornerstone-toggle",isEnabled:!0,onToggle:()=>{},onToggleDisabled:()=>{}};const m=p,h=d()(o.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,y=e=>(0,t.createElement)(h,{hasPadding:!0,hasSeparator:!0,...e}),g=e=>{const[s,r]=(0,l.useState)(!1),{prefixIcon:n}=e;return(0,t.createElement)("div",{className:"yoast components-panel__body "+(s?"is-opened":"")},(0,t.createElement)("h2",{className:"components-panel__body-title"},(0,t.createElement)("button",{onClick:function(){r(!s)},className:"components-button components-panel__body-toggle",type:"button",id:e.buttonId},(0,t.createElement)("span",{className:"yoast-icon-span",style:{fill:`${n&&n.color||""}`}},n&&(0,t.createElement)(o.SvgIcon,{icon:n.icon,color:n.color,size:n.size})),(0,t.createElement)("span",{className:"yoast-title-container"},(0,t.createElement)("div",{className:"yoast-title"},e.title),(0,t.createElement)("div",{className:"yoast-subtitle"},e.subTitle)),e.hasBetaBadgeLabel&&(0,t.createElement)(o.BetaBadge,null),(0,t.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),s&&e.children)},w=g;g.propTypes={title:i().string.isRequired,children:i().oneOfType([i().node,i().arrayOf(i().node)]).isRequired,prefixIcon:i().object,subTitle:i().string,hasBetaBadgeLabel:i().bool,buttonId:i().string},g.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null};const b=(0,n.makeOutboundLink)();function f({isCornerstone:e,onChange:a,learnMoreUrl:i,location:l}){const c="metabox"===l?y:w;return(0,t.createElement)(c,{id:(0,n.join)(["yoast-cornerstone-collapsible",l]),title:(0,s.__)("Cornerstone content","wordpress-seo")},(0,t.createElement)(o.HelpText,null,(0,s.__)("Cornerstone content should be the most important and extensive articles on your site.","wordpress-seo")+" ",(0,t.createElement)(b,{href:i},(0,s.__)("Learn more about Cornerstone Content.","wordpress-seo"))),(0,t.createElement)(m,{id:(0,n.join)(["yoast-cornerstone",l]),isEnabled:e,onToggle:a}),(0,t.createElement)(r.Slot,{name:"YoastAfterCornerstoneToggle"}))}f.propTypes={isCornerstone:i().bool,onChange:i().func,learnMoreUrl:i().string.isRequired,location:i().string},f.defaultProps={isCornerstone:!0,onChange:()=>{},location:""};const E=window.wp.compose,k=window.wp.data,x=window.yoast.externals.contexts,_=window.wp.url,v=window.wp.apiFetch;var R=e.n(v);const N=window.yoast.relatedKeyphraseSuggestions;class S extends l.Component{constructor(e){super(e),this.onModalOpen=this.onModalOpen.bind(this),this.onLinkClick=this.onLinkClick.bind(this),this.listenToMessages=this.listenToMessages.bind(this)}onModalOpen(){const{keyphrase:e,onOpenWithNoKeyphrase:t,onOpen:s,location:r,newRequest:o,countryCode:n}=this.props;e.trim()?(s(r),o(n,e)):t()}onLinkClick(e){if(e.preventDefault(),!this.props.keyphrase.trim())return void this.props.onOpenWithNoKeyphrase();const t=e.target.href,s=["top="+(window.top.outerHeight/2+window.top.screenY-285),"left="+(window.top.outerWidth/2+window.top.screenX-170),"width=340","height=570","resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(t,"SEMrush_login",s.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.listenToMessages,!1)}async listenToMessages(e){const{data:t,source:s,origin:r}=e;"https://oauth.semrush.com"===r&&this.popup===s&&("semrush:oauth:success"===t.type&&(this.popup.close(),window.removeEventListener("message",this.listenToMessages,!1),await this.performAuthenticationRequest(t)),"semrush:oauth:denied"===t.type&&(this.popup.close(),window.removeEventListener("message",this.listenToMessages,!1),this.props.onAuthentication(!1)))}async performAuthenticationRequest(e){try{const t=new URL(e.url).searchParams.get("code"),s=await R()({path:"yoast/v1/semrush/authenticate",method:"POST",data:{code:t}});200===s.status?(this.props.onAuthentication(!0),this.onModalOpen(),this.popup.close()):console.error(s.error)}catch(e){console.error(e.message)}}render(){const{keyphrase:e,location:n,whichModalOpen:a,isLoggedIn:i,onClose:c,countryCode:d,learnMoreLink:u}=this.props,p=new URL("https://www.semrush.com/analytics/keywordoverview/");return p.searchParams.append("q",e),p.searchParams.append("db",d),(0,t.createElement)(l.Fragment,null,i&&(0,t.createElement)("div",{className:"yoast"},(0,t.createElement)(o.NewButton,{variant:"secondary",id:`yoast-get-related-keyphrases-${n}`,onClick:this.onModalOpen},(0,s.__)("Get related keyphrases","wordpress-seo"))),(0,t.createElement)(N.Modal,{isOpen:Boolean(e)&&a===n,onClose:c,insightsLink:p.toString(),learnMoreLink:u},(0,t.createElement)(r.Slot,{name:"YoastRelatedKeyphrases"})),!i&&(0,t.createElement)("div",{className:"yoast"},(0,t.createElement)(o.ButtonStyledLink,{variant:"secondary",id:`yoast-get-related-keyphrases-${n}`,href:"https://oauth.semrush.com/oauth2/authorize?ref=1513012826&client_id=yoast&redirect_uri=https%3A%2F%2Foauth.semrush.com%2Foauth2%2Fyoast%2Fsuccess&response_type=code&scope=user.id",onClick:this.onLinkClick},(0,s.__)("Get related keyphrases","wordpress-seo"),(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,s.__)("(Opens in a new browser tab)","wordpress-seo")))))}}S.propTypes={keyphrase:i().string,location:i().string,whichModalOpen:i().oneOf(["none","metabox","sidebar"]),isLoggedIn:i().bool,onOpen:i().func.isRequired,onOpenWithNoKeyphrase:i().func.isRequired,onClose:i().func.isRequired,onAuthentication:i().func.isRequired,countryCode:i().string,learnMoreLink:i().string,newRequest:i().func.isRequired},S.defaultProps={keyphrase:"",location:"",whichModalOpen:"none",isLoggedIn:!1,countryCode:"en_US",learnMoreLink:""};const L=S,B=(0,E.compose)([(0,k.withSelect)((e=>{const{getSEMrushModalOpen:t,getSEMrushLoginStatus:s,getSEMrushSelectedCountry:r,getPreference:o,selectLinkParams:n,getFocusKeyphrase:a}=e("yoast-seo/editor");return{whichModalOpen:t(),isLoggedIn:s(),countryCode:r(),isRtl:o("isRtl",!1),learnMoreLink:(0,_.addQueryArgs)("https://yoa.st/3-v",n()),keyphrase:a()}})),(0,k.withDispatch)((e=>{const{setSEMrushNoKeyphraseMessage:t,setSEMrushOpenModal:s,setSEMrushDismissModal:r,setSEMrushLoginStatus:o,setSEMrushNewRequest:n}=e("yoast-seo/editor");return{onOpenWithNoKeyphrase:()=>{t()},onOpen:e=>{s(e)},onClose:()=>{r()},onAuthentication:e=>{o(e)},newRequest:(e,t)=>{n(e,t)}}}))])(L),C=window.yoast.styleGuide,I=(0,n.makeOutboundLink)(d().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${C.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${C.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${C.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`),P=window.lodash,T=C.colors.$color_bad,A=C.colors.$palette_error_background,M=C.colors.$color_grey_text_light,O=C.colors.$palette_error_text,$=d().div`
	display: flex;
	flex-direction: column;
`,F=d().label`
	font-size: var(--yoast-font-size-default);
	font-weight: var(--yoast-font-weight-bold);
	${(0,n.getDirectionalStyle)("margin-right: 4px","margin-left: 4px")};
`,q=d().span`
	margin-bottom: 0.5em;
`,U=d()(o.InputField)`
	flex: 1 !important;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0; // Reset margins inherited from WordPress.

	// Hide native X in Edge and IE11.
	&::-ms-clear {
		display: none;
	}

	&.has-error {
		border-color: ${T} !important;
		background-color: ${A} !important;

		&:focus {
			box-shadow: 0 0 2px ${T} !important;
		}
	}
`,K=d().ul`
	color: ${O};
	list-style-type: disc;
	list-style-position: outside;
	margin: 0;
	margin-left: 1.2em;
`,z=d().li`
	color: ${O};
	margin: 0 0 0.5em 0;
`,H=(0,o.addFocusStyle)(d().button`
		border: 1px solid transparent;
		box-shadow: none;
		background: none;
		flex: 0 0 32px;
		height: 32px;
		max-width: 32px;
		padding: 0;
		cursor: pointer;
	`);H.propTypes={type:i().string,focusColor:i().string,focusBackgroundColor:i().string,focusBorderColor:i().string},H.defaultProps={type:"button",focusColor:C.colors.$color_button_text_hover,focusBackgroundColor:"transparent",focusBorderColor:C.colors.$color_blue};const j=d()(o.SvgIcon)`
	margin-top: 4px;
`,Y=d().div`
	display: flex;
	flex-direction: row;
	align-items: center;

	&.has-remove-keyword-button {
		${U} {
			${(0,n.getDirectionalStyle)("padding-right: 40px","padding-left: 40px")};
		}

		${H} {
			${(0,n.getDirectionalStyle)("margin-left: -32px","margin-right: -32px")};
		}
	}
`;class W extends l.Component{constructor(e){super(e),this.handleChange=this.handleChange.bind(this)}handleChange(e){this.props.onChange(e.target.value)}renderLabel(){const{id:e,label:s,helpLink:r}=this.props;return(0,t.createElement)(q,null,(0,t.createElement)(F,{htmlFor:e},s),r)}renderErrorMessages(){const e=[...this.props.errorMessages];return!(0,P.isEmpty)(e)&&(0,t.createElement)(K,null,e.map(((e,s)=>(0,t.createElement)(z,{key:s},(0,t.createElement)("span",{role:"alert"},e)))))}render(){const{id:e,showLabel:s,keyword:r,onRemoveKeyword:o,onBlurKeyword:n,onFocusKeyword:a,hasError:i}=this.props,l=!s,c=o!==P.noop;return(0,t.createElement)($,null,s&&this.renderLabel(),i&&this.renderErrorMessages(),(0,t.createElement)(Y,{className:c?"has-remove-keyword-button":null},(0,t.createElement)(U,{"aria-label":l?this.props.label:null,type:"text",id:e,className:i?"has-error":null,onChange:this.handleChange,onFocus:a,onBlur:n,value:r,autoComplete:"off"}),c&&(0,t.createElement)(H,{onClick:o,focusBoxShadowColor:"#084A67"},(0,t.createElement)(j,{size:"18px",icon:"times-circle",color:M}))))}}W.propTypes={id:i().string.isRequired,showLabel:i().bool,keyword:i().string,onChange:i().func.isRequired,onRemoveKeyword:i().func,onBlurKeyword:i().func,onFocusKeyword:i().func,label:i().string.isRequired,helpLink:i().node,hasError:i().bool,errorMessages:i().arrayOf(i().string)},W.defaultProps={showLabel:!0,keyword:"",onRemoveKeyword:P.noop,onBlurKeyword:P.noop,onFocusKeyword:P.noop,helpLink:null,hasError:!1,errorMessages:[]};const D=W,G=d().div`
	padding: 16px;
	/* Necessary to compensate negative top margin of the collapsible after the keyword input. */
	border-bottom: 1px solid transparent;
`;class V extends l.Component{constructor(e){super(e),this.validate=this.validate.bind(this)}static renderHelpLink(){return(0,t.createElement)(I,{href:wpseoAdminL10n["shortlinks.focus_keyword_info"],className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,s.__)("Help on choosing the perfect focus keyphrase","wordpress-seo")))}validate(){const e=[...this.props.errors];return 0===this.props.keyword.trim().length&&this.props.displayNoKeyphraseMessage&&e.push((0,s.__)("Please enter a focus keyphrase first to get related keyphrases","wordpress-seo")),0===this.props.keyword.trim().length&&this.props.displayNoKeyphrasForTrackingMessage&&e.push((0,s.__)("Please enter a focus keyphrase first to track keyphrase performance","wordpress-seo")),this.props.keyword.includes(",")&&e.push((0,s.__)("Are you trying to use multiple keyphrases? You should add them separately below.","wordpress-seo")),this.props.keyword.length>191&&e.push((0,s.__)("Your keyphrase is too long. It can be a maximum of 191 characters.","wordpress-seo")),e}render(){const e=this.validate();return(0,t.createElement)(x.LocationConsumer,null,(o=>(0,t.createElement)("div",{style:"sidebar"===o?{borderBottom:"1px solid #f0f0f0"}:{}},(0,t.createElement)(G,{location:o},(0,t.createElement)(D,{id:`focus-keyword-input-${o}`,onChange:this.props.onFocusKeywordChange,keyword:this.props.keyword,label:(0,s.__)("Focus keyphrase","wordpress-seo"),helpLink:V.renderHelpLink(),onBlurKeyword:this.props.onBlurKeyword,onFocusKeyword:this.props.onFocusKeyword,hasError:e.length>0,errorMessages:e}),this.props.isSEMrushIntegrationActive&&(0,t.createElement)(B,{location:o,keyphrase:this.props.keyword})),(0,t.createElement)(r.Slot,{name:`YoastAfterKeywordInput${o.charAt(0).toUpperCase()+o.slice(1)}`}))))}}V.propTypes={keyword:i().string,onFocusKeywordChange:i().func.isRequired,onFocusKeyword:i().func.isRequired,onBlurKeyword:i().func.isRequired,isSEMrushIntegrationActive:i().bool,displayNoKeyphraseMessage:i().bool,displayNoKeyphrasForTrackingMessage:i().bool,errors:i().arrayOf(i().string)},V.defaultProps={keyword:"",isSEMrushIntegrationActive:!1,displayNoKeyphraseMessage:!1,displayNoKeyphrasForTrackingMessage:!1,errors:[]};const Q=(0,E.compose)([(0,k.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushNoKeyphraseMessage:s,hasWincherNoKeyphrase:r,getFocusKeyphraseErrors:o}=e("yoast-seo/editor");return{keyword:t(),displayNoKeyphraseMessage:s(),displayNoKeyphrasForTrackingMessage:r(),errors:o()}})),(0,k.withDispatch)((e=>{const{setFocusKeyword:t,setMarkerPauseStatus:s}=e("yoast-seo/editor");return{onFocusKeywordChange:t,onFocusKeyword:()=>s(!0),onBlurKeyword:()=>s(!1)}}))])(V);function Z(e){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Z(e)}function J(e,t,s){return r=function(e,t){if("object"!=Z(e)||!e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var r=s.call(e,"string");if("object"!=Z(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==Z(r)?r:String(r))in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e;var r}const X=window.yoast.analysis;function ee(e,t=""){const s=e.getIdentifier(),r={score:e.score,rating:X.interpreters.scoreToRating(e.score),hasMarks:e.hasMarks(),marker:e.getMarker(),id:s,text:e.text,markerId:t.length>0?`${t}:${s}`:s,hasBetaBadge:e.hasBetaBadge(),hasJumps:e.hasJumps(),hasAIFixes:e.hasAIFixes(),editFieldName:e.editFieldName};return"ok"===r.rating&&(r.rating="OK"),r}function te(e,t){switch(e.rating){case"error":t.errorsResults.push(e);break;case"feedback":t.considerationsResults.push(e);break;case"bad":t.problemsResults.push(e);break;case"OK":t.improvementsResults.push(e);break;case"good":t.goodResults.push(e)}return t}function se(e){switch(e){case"loading":return{icon:"loading-spinner",color:C.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:C.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:C.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:C.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:C.colors.$color_ok};default:return{icon:"seo-score-bad",color:C.colors.$color_red}}}function re(e,t=""){let s={errorsResults:[],problemsResults:[],improvementsResults:[],goodResults:[],considerationsResults:[]};if(!e)return s;for(let r=0;r<e.length;r++){const o=e[r];o.text&&(s=te(ee(o,t),s))}return s}function oe({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,l.createPortal)(t,s):null}oe.propTypes={target:i().oneOfType([i().string,i().object]).isRequired,children:i().node.isRequired};const ne=({target:e,scoreIndicator:s})=>(0,t.createElement)(oe,{target:e},(0,t.createElement)(o.SvgIcon,{...se(s)}));ne.propTypes={target:i().string.isRequired,scoreIndicator:i().string.isRequired};const ae=ne,ie=window.yoast.analysisReport,le=window.yoast.uiLibrary,ce=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),de=window.wp.hooks,ue=(d().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,d().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`),pe=(d()(o.Icon)`
	float: ${(0,n.getDirectionalStyle)("right","left")};
	margin: ${(0,n.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),me=e=>{const{title:s,className:o,showYoastIcon:n,additionalClassName:a,...i}=e,l=n?(0,t.createElement)("span",{className:"yoast-icon"}):null;return(0,t.createElement)(r.Modal,{title:s,className:`${o} ${a}`,icon:l,...i},e.children)};me.propTypes={title:i().string,className:i().string,showYoastIcon:i().bool,children:i().oneOfType([i().node,i().arrayOf(i().node)]),additionalClassName:i().string},me.defaultProps={title:"Yoast SEO",className:pe,showYoastIcon:!0,children:null,additionalClassName:""};const he=me,ye=()=>[(0,s.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,s.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,s.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,s.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,s.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,s.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,s.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,s.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,s.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,s.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,s.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,s.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],ge=(e,t)=>{try{return(0,l.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}},we=d().div`
  padding: 25px 32px 32px;
  color: #303030;
`,be=d().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,fe=d().span`
  display: block;
  margin-top: 4px;
`,Ee=d().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,ke=d().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,xe=d().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,_e=d().div`
  text-align: center;
`,ve=d().a`
  width: 100%;
`,Re=(0,n.makeOutboundLink)(ve);class Ne extends l.Component{constructor(e){super(e)}createBenefitsList(e){return e.length>0&&(0,t.createElement)(be,{role:"list"},e.map(((e,s)=>(0,t.createElement)("li",{key:`upsell-benefit-${s}`},ge(e,{strong:(0,t.createElement)("strong",null)})))))}render(){const e=(0,k.select)("yoast-seo/editor").isPromotionActive("black-friday-2024-promotion");return(0,t.createElement)(l.Fragment,null,e&&(0,t.createElement)("div",{className:"yst-flex  yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,t.createElement)("div",{className:"yst-mx-auto"},(0,s.__)("30% OFF - BLACK FRIDAY","wordpress-seo"))),(0,t.createElement)(we,null,(0,t.createElement)(Ee,null,this.props.title),(0,t.createElement)(ke,null,this.props.description),(0,t.createElement)(_e,null,(0,t.createElement)(Re,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,t.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,t.createElement)(fe,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,t.createElement)(xe,null),(0,t.createElement)(Ee,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}Ne.propTypes={title:i().node,benefits:i().array,benefitsTitle:i().node,description:i().node,upsellButton:i().object,upsellButtonText:i().string.isRequired,upsellButtonLabel:i().string,upsellButtonHasCaret:i().bool},Ne.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const Se=Ne,Le=(0,s.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Be=e=>{const{locationContext:r}=(0,x.useRootContext)(),o=(0,_.addQueryArgs)(wpseoAdminL10n[e.buyLink],{context:r});return(0,t.createElement)(Se,{title:(0,s.__)("Get more help with writing content that ranks","wordpress-seo"),description:e.description,benefitsTitle:(0,s.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,s.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:ye(),upsellButtonText:(0,s.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,s.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:o,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,s.__)("1 year of premium support and updates included!","wordpress-seo")})};Be.propTypes={buyLink:i().string.isRequired,description:i().string},Be.defaultProps={description:Le};const Ce=Be;class Ie extends l.Component{constructor(e){super(e);const t=this.props.results;this.state={mappedResults:{}},null!==t&&(this.state={mappedResults:re(t,this.props.keywordKey)}),this.handleMarkButtonClick=this.handleMarkButtonClick.bind(this),this.handleEditButtonClick=this.handleEditButtonClick.bind(this),this.handleResultsChange=this.handleResultsChange.bind(this),this.renderHighlightingUpsell=this.renderHighlightingUpsell.bind(this),this.createMarkButton=this.createMarkButton.bind(this)}componentDidUpdate(e){null!==this.props.results&&this.props.results!==e.results&&this.setState({mappedResults:re(this.props.results,this.props.keywordKey)})}createMarkButton({ariaLabel:e,id:s,className:r,status:n,onClick:a,isPressed:i}){return(0,t.createElement)(l.Fragment,null,(0,t.createElement)(o.IconButtonToggle,{marksButtonStatus:n,className:r,onClick:a,id:s,icon:"eye",pressed:i,ariaLabel:e}),this.props.shouldUpsellHighlighting&&(0,t.createElement)("div",{className:"yst-root"},(0,t.createElement)(le.Badge,{className:"yst-absolute yst-px-[3px] yst-py-[3px] yst--end-[6.5px] yst--top-[6.5px]",size:"small",variant:"upsell"},(0,t.createElement)(ce,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",role:"img","aria-hidden":!0,focusable:!1}))))}deactivateMarker(){this.props.setActiveMarker(null),this.props.setMarkerPauseStatus(!1),this.removeMarkers()}activateMarker(e,t){this.props.setActiveMarker(e),t()}handleMarkButtonClick(e,t){const s=this.props.keywordKey.length>0?`${this.props.keywordKey}:${e}`:e;this.props.activeAIFixesButton&&this.props.setActiveAIFixesButton(null),s===this.props.activeMarker?this.deactivateMarker():this.activateMarker(s,t)}handleResultsChange(e,t,s){const r=this.props.keywordKey.length>0?`${this.props.keywordKey}:${e}`:e;r===this.props.activeMarker&&(s?(0,P.isUndefined)(t)||this.activateMarker(r,t):this.deactivateMarker())}focusOnKeyphraseField(e){const t=this.props.keywordKey,s=""===t?"focus-keyword-input-"+e:"yoast-keyword-input-"+t+"-"+e,r=document.getElementById(s);r.focus(),r.scrollIntoView({behavior:"auto",block:"center",inline:"center"})}focusOnGooglePreviewField(e,t){let s;s="metaDescriptionKeyword"===e||"metaDescriptionLength"===e?"description":"titleWidth"===e||"keyphraseInSEOTitle"===e?"title":"slug";const r=document.getElementById("yoast-google-preview-"+s+"-"+t);r.focus(),r.scrollIntoView({behavior:"auto",block:"center",inline:"center"})}handleEditButtonClick(e){const t=this.props.location;"functionWordsInKeyphrase"!==e&&"keyphraseLength"!==e?(["metaDescriptionKeyword","metaDescriptionLength","titleWidth","keyphraseInSEOTitle","slugKeyword"].includes(e)&&this.handleGooglePreviewFocus(t,e),(0,de.doAction)("yoast.focus.input",e)):this.focusOnKeyphraseField(t)}handleGooglePreviewFocus(e,t){if("sidebar"===e)document.getElementById("yoast-search-appearance-modal-open-button").click(),setTimeout((()=>this.focusOnGooglePreviewField(t,"modal")),500);else{const s=document.getElementById("yoast-snippet-editor-metabox");s&&"false"===s.getAttribute("aria-expanded")?(s.click(),setTimeout((()=>this.focusOnGooglePreviewField(t,e)),100)):this.focusOnGooglePreviewField(t,e)}}removeMarkers(){window.YoastSEO.analysis.applyMarks(new X.Paper("",{}),[])}renderHighlightingUpsell(e,r){const o=(0,s.__)("Highlight areas of improvement in your text, no more searching for a needle in a haystack, straight to optimizing! Now also in Elementor!","wordpress-seo");return e&&(0,t.createElement)(he,{title:(0,s.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:r,additionalClassName:"",className:`${pe} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-highlighting-modal",shouldCloseOnClickOutside:!0},(0,t.createElement)(ue,null,(0,t.createElement)(Ce,{buyLink:this.props.highlightingUpsellLink,description:o})))}render(){const{mappedResults:e}=this.state,{errorsResults:r,improvementsResults:o,goodResults:n,considerationsResults:a,problemsResults:i}=e,{upsellResults:c,resultCategoryLabels:d}=this.props,u={errors:(0,s.__)("Errors","wordpress-seo"),problems:(0,s.__)("Problems","wordpress-seo"),improvements:(0,s.__)("Improvements","wordpress-seo"),considerations:(0,s.__)("Considerations","wordpress-seo"),goodResults:(0,s.__)("Good results","wordpress-seo")},p=Object.assign(u,d);let m=this.props.marksButtonStatus;return"enabled"===m&&this.props.shortcodesForParsing.length>0&&(m="disabled"),(0,t.createElement)(l.Fragment,null,(0,t.createElement)(ie.ContentAnalysis,{errorsResults:r,problemsResults:i,upsellResults:c,improvementsResults:o,considerationsResults:a,goodResults:n,activeMarker:this.props.activeMarker,onMarkButtonClick:this.handleMarkButtonClick,onEditButtonClick:this.handleEditButtonClick,marksButtonClassName:this.props.marksButtonClassName,editButtonClassName:this.props.editButtonClassName,marksButtonStatus:m,headingLevel:3,keywordKey:this.props.keywordKey,isPremium:this.props.isPremium,resultCategoryLabels:p,onResultChange:this.handleResultsChange,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderAIOptimizeButton:this.props.renderAIOptimizeButton,renderHighlightingUpsell:this.renderHighlightingUpsell,markButtonFactory:this.createMarkButton}))}}Ie.propTypes={results:i().array,upsellResults:i().array,marksButtonClassName:i().string,editButtonClassName:i().string,marksButtonStatus:i().oneOf(["enabled","disabled","hidden"]),setActiveMarker:i().func.isRequired,setMarkerPauseStatus:i().func.isRequired,setActiveAIFixesButton:i().func.isRequired,activeMarker:i().string,activeAIFixesButton:i().string,keywordKey:i().string,location:i().string,isPremium:i().bool,resultCategoryLabels:i().shape({errors:i().string,problems:i().string,improvements:i().string,considerations:i().string,goodResults:i().string}),shortcodesForParsing:i().array,shouldUpsellHighlighting:i().bool,highlightingUpsellLink:i().string,renderAIOptimizeButton:i().func},Ie.defaultProps={results:null,upsellResults:[],marksButtonStatus:"enabled",marksButtonClassName:"",editButtonClassName:"",activeMarker:null,activeAIFixesButton:null,keywordKey:"",location:"",isPremium:!1,resultCategoryLabels:{},shortcodesForParsing:[],shouldUpsellHighlighting:!1,highlightingUpsellLink:"",renderAIOptimizeButton:()=>{}};const Pe=Ie,Te=(0,E.compose)([(0,k.withSelect)((e=>{const{getActiveMarker:t,getIsPremium:s,getShortcodesForParsing:r,getActiveAIFixesButton:o}=e("yoast-seo/editor");return{activeMarker:t(),isPremium:s(),shortcodesForParsing:r(),activeAIFixesButton:o()}})),(0,k.withDispatch)((e=>{const{setActiveMarker:t,setMarkerPauseStatus:s,setActiveAIFixesButton:r}=e("yoast-seo/editor");return{setActiveMarker:t,setMarkerPauseStatus:s,setActiveAIFixesButton:r}}))])(Pe);function Ae(e){return(0,P.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,s.__)("Not available","wordpress-seo"),screenReaderReadabilityText:(0,s.__)("Not available","wordpress-seo"),screenReaderInclusiveLanguageText:(0,s.__)("Not available","wordpress-seo")};case"bad":return{className:"bad",screenReaderText:(0,s.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,s.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,s.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,s.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,s.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,s.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,s.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,s.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,s.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(X.interpreters.scoreToRating(e))}function Me({target:e,children:s}){return(0,t.createElement)(oe,{target:e},s)}function Oe(){return(0,P.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}Me.propTypes={target:i().string.isRequired,children:i().node.isRequired};i().string.isRequired;const $e=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),Fe=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),qe=({learnMoreLink:e,thumbnail:r,wistiaEmbedPermission:o,upsellLink:n,isProductCopy:a,title:i,upsellLabel:l,newToText:c,bundleNote:d,ctbId:u})=>{const{onClose:p,initialFocus:m}=(0,le.useModalContext)(),h={a:(0,t.createElement)(rt,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,t.createElement)(Fe,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,t.createElement)("div",{className:"yst-relative yst-w-full"},(0,t.createElement)(Ct,{videoId:"vmrahpfjxp",thumbnail:r,wistiaEmbedPermission:o}),(0,t.createElement)(le.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,t.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,t.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,t.createElement)("span",{className:"yst-logo-icon"}),c))),(0,t.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,t.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,t.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},i),(0,t.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},ge(a?(0,s.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,s.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,s.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,s.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,t.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,t.createElement)(le.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:m,"data-action":"load-nfd-ctb","data-ctb-id":u},(0,t.createElement)($e,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),l,(0,t.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,s.__)("(Opens in a new browser tab)","wordpress-seo")))),d,(0,t.createElement)(le.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,s.__)("Close","wordpress-seo"))))};qe.propTypes={learnMoreLink:i().string.isRequired,upsellLink:i().string.isRequired,thumbnail:i().shape({src:i().string.isRequired,width:i().string,height:i().string}).isRequired,wistiaEmbedPermission:i().shape({value:i().bool.isRequired,status:i().string.isRequired,set:i().func.isRequired}).isRequired,title:i().string,upsellLabel:i().string,newToText:i().string,isProductCopy:i().bool,bundleNote:i().oneOfType([i().string,i().element]),ctbId:i().string},qe.defaultProps={title:(0,s.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,s.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,s.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const Ue=({learnMoreLink:e,thumbnail:r,wistiaEmbedPermission:o,upsellLink:n,upsellLabel:a,newToText:i,bundleNote:l,ctbId:c})=>{const{onClose:d,initialFocus:u}=(0,le.useModalContext)(),p={a:(0,t.createElement)(rt,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,t.createElement)(Fe,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,t.createElement)("br",null)};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,t.createElement)("div",{className:"yst-relative yst-w-full"},(0,t.createElement)(Ct,{videoId:"vun9z1dpfh",thumbnail:r,wistiaEmbedPermission:o}),(0,t.createElement)(le.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,s.__)("Beta","wordpress-seo-premium"))),(0,t.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,t.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,t.createElement)("span",{className:"yst-logo-icon"}),i))),(0,t.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,t.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,t.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,s.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,s.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,t.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},ge((0,s.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,s.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),p))),(0,t.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,t.createElement)(le.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:u,"data-action":"load-nfd-ctb","data-ctb-id":c},(0,t.createElement)($e,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),a,(0,t.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,s.__)("(Opens in a new browser tab)","wordpress-seo")))),l,(0,t.createElement)(le.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:d},(0,s.__)("Close","wordpress-seo"))))};Ue.propTypes={learnMoreLink:i().string.isRequired,upsellLink:i().string.isRequired,thumbnail:i().shape({src:i().string.isRequired,width:i().string,height:i().string}).isRequired,wistiaEmbedPermission:i().shape({value:i().bool.isRequired,status:i().string.isRequired,set:i().func.isRequired}).isRequired,upsellLabel:i().string,newToText:i().string,bundleNote:i().oneOfType([i().string,i().element]),ctbId:i().string},Ue.defaultProps={upsellLabel:(0,s.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,s.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const Ke=({handleRefreshClick:e,supportLink:r})=>(0,t.createElement)("div",{className:"yst-flex yst-gap-2"},(0,t.createElement)(le.Button,{onClick:e},(0,s.__)("Refresh this page","wordpress-seo")),(0,t.createElement)(le.Button,{variant:"secondary",as:"a",href:r,target:"_blank",rel:"noopener"},(0,s.__)("Contact support","wordpress-seo")));Ke.propTypes={handleRefreshClick:i().func.isRequired,supportLink:i().string.isRequired};const ze=({handleRefreshClick:e,supportLink:r})=>(0,t.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,t.createElement)(le.Button,{className:"yst-order-last",onClick:e},(0,s.__)("Refresh this page","wordpress-seo")),(0,t.createElement)(le.Button,{variant:"secondary",as:"a",href:r,target:"_blank",rel:"noopener"},(0,s.__)("Contact support","wordpress-seo")));ze.propTypes={handleRefreshClick:i().func.isRequired,supportLink:i().string.isRequired};const He=({error:e,children:r})=>(0,t.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,t.createElement)(le.Title,null,(0,s.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,t.createElement)("p",null,(0,s.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,t.createElement)(le.Alert,{variant:"error"},(null==e?void 0:e.message)||(0,s.__)("Undefined error message.","wordpress-seo")),(0,t.createElement)("p",null,(0,s.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),r);He.propTypes={error:i().object.isRequired,children:i().node},He.VerticalButtons=ze,He.HorizontalButtons=Ke;var je;function Ye(){return Ye=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Ye.apply(this,arguments)}i().string,i().node.isRequired,i().node.isRequired,i().node,i().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const We=e=>t.createElement("svg",Ye({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),je||(je=t.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),De=window.ReactDOM;var Ge,Ve,Qe;(Ve=Ge||(Ge={})).Pop="POP",Ve.Push="PUSH",Ve.Replace="REPLACE",function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(Qe||(Qe={})),new Set(["lazy","caseSensitive","path","id","index","children"]),Error;const Ze=["post","put","patch","delete"],Je=(new Set(Ze),["get",...Ze]);new Set(Je),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred"),t.Component,t.startTransition,new Promise((()=>{})),t.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}var Xe,et,tt,st;new Map,t.startTransition,De.flushSync,t.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(st=Xe||(Xe={})).UseScrollRestoration="useScrollRestoration",st.UseSubmit="useSubmit",st.UseSubmitFetcher="useSubmitFetcher",st.UseFetcher="useFetcher",st.useViewTransitionState="useViewTransitionState",(tt=et||(et={})).UseFetcher="useFetcher",tt.UseFetchers="useFetchers",tt.UseScrollRestoration="useScrollRestoration",i().string.isRequired,i().string;const rt=({href:e,children:r,...o})=>(0,t.createElement)(le.Link,{target:"_blank",rel:"noopener noreferrer",...o,href:e},r,(0,t.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,s.__)("(Opens in a new browser tab)","wordpress-seo")));rt.propTypes={href:i().string.isRequired,children:i().node},rt.defaultProps={children:null};const ot=t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var nt,at,it;function lt(){return lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},lt.apply(this,arguments)}const ct=e=>t.createElement("svg",lt({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),nt||(nt=t.createElement("defs",null,t.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),at||(at=t.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),it||(it=t.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),t.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function dt(){return dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},dt.apply(this,arguments)}const ut=e=>t.createElement("svg",dt({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),t.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var pt,mt,ht,yt,gt,wt,bt,ft,Et;function kt(){return kt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},kt.apply(this,arguments)}const xt=e=>t.createElement("svg",kt({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),pt||(pt=t.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),mt||(mt=t.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),ht||(ht=t.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),yt||(yt=t.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),gt||(gt=t.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),wt||(wt=t.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),bt||(bt=t.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),ft||(ft=t.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),Et||(Et=t.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),_t=({link:e,linkProps:r,isPromotionActive:o})=>{let n=(0,l.useMemo)((()=>(0,s.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),a=ge((0,s.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,s.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,t.createElement)("span",{className:"yst-whitespace-nowrap"})});const i=o("black-friday-2024-promotion");return i&&(n=(0,l.useMemo)((()=>(0,s.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),a=ge((0,s.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,s.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,t.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,t.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,t.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,t.createElement)(xt,null)),i&&(0,t.createElement)("div",{className:"sidebar__sale_banner_container"},(0,t.createElement)("div",{className:"sidebar__sale_banner"},(0,t.createElement)("span",{className:"banner_text"},(0,s.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,t.createElement)(le.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},a),(0,t.createElement)("p",{className:"yst-mt-2"},n),(0,t.createElement)(le.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...r},(0,t.createElement)("span",null,i?(0,s.__)("Buy now","wordpress-seo"):a),(0,t.createElement)(ot,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,t.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,s.__)("30-day money back guarantee.","wordpress-seo")),(0,t.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,t.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,t.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,s.__)("Read reviews from real users","wordpress-seo")),(0,t.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,t.createElement)(We,{className:"yst-w-5 yst-h-5"}),(0,t.createElement)("span",{className:"yst-flex yst-gap-1"},(0,t.createElement)(ut,{className:"yst-w-5 yst-h-5"}),(0,t.createElement)(ut,{className:"yst-w-5 yst-h-5"}),(0,t.createElement)(ut,{className:"yst-w-5 yst-h-5"}),(0,t.createElement)(ut,{className:"yst-w-5 yst-h-5"}),(0,t.createElement)(ct,{className:"yst-w-5 yst-h-5"})),(0,t.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};_t.propTypes={link:i().string.isRequired,linkProps:i().object,isPromotionActive:i().func},_t.defaultProps={linkProps:{},isPromotionActive:P.noop};const vt=({premiumLink:e,premiumUpsellConfig:r,isPromotionActive:o})=>{const n=o("black-friday-2024-promotion");return(0,t.createElement)(le.Paper,{as:"div",className:"xl:yst-max-w-3xl"},n&&(0,t.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,t.createElement)("div",null,(0,s.__)("30% OFF","wordpress-seo")),(0,t.createElement)("div",null,(0,s.__)("BLACK FRIDAY","wordpress-seo"))),(0,t.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,t.createElement)(le.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,s.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,s.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,t.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},ye().map(((e,s)=>(0,t.createElement)("li",{key:`upsell-benefit-${s}`},ge(e,{strong:(0,t.createElement)("span",{className:"yst-font-semibold"})}))))),(0,t.createElement)(le.Button,{as:"a",variant:"upsell",size:"extra-large",href:e,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...r},n?(0,s.__)("Claim your 30% off now!","wordpress-seo"):(0,s.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,s.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,t.createElement)(ot,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};vt.propTypes={premiumLink:i().string.isRequired,premiumUpsellConfig:i().object,isPromotionActive:i().func},vt.defaultProps={premiumUpsellConfig:{},isPromotionActive:P.noop},i().string.isRequired,i().object.isRequired,i().string.isRequired,i().func.isRequired,t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),i().bool.isRequired,i().func,i().func,i().string.isRequired,i().string.isRequired,i().string.isRequired,i().string.isRequired;const Rt=window.yoast.reactHelmet,Nt="loading",St="showPlay",Lt="askPermission",Bt="isPlaying",Ct=({videoId:e,thumbnail:r,wistiaEmbedPermission:o})=>{const[n,a]=(0,l.useState)(o.value?Bt:St),i=(0,l.useCallback)((()=>a(Bt)),[a]),c=(0,l.useCallback)((()=>{o.value?i():a(Lt)}),[o.value,i,a]),d=(0,l.useCallback)((()=>a(St)),[a]),u=(0,l.useCallback)((()=>{o.set(!0),i()}),[o.set,i]);return(0,t.createElement)(t.Fragment,null,o.value&&(0,t.createElement)(Rt.Helmet,null,(0,t.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,t.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},n===St&&(0,t.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:c},(0,t.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...r})),n===Lt&&(0,t.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,t.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},o.status===Nt&&(0,t.createElement)(le.Spinner,null),o.status!==Nt&&(0,s.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,s.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,t.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,t.createElement)(le.Button,{type:"button",variant:"secondary",onClick:d,disabled:o.status===Nt},(0,s.__)("Deny","wordpress-seo")),(0,t.createElement)(le.Button,{type:"button",variant:"primary",onClick:u,disabled:o.status===Nt},(0,s.__)("Allow","wordpress-seo")))),o.value&&n===Bt&&(0,t.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===e&&(0,t.createElement)(le.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,t.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};Ct.propTypes={videoId:i().string.isRequired,thumbnail:i().shape({src:i().string.isRequired,width:i().string,height:i().string}).isRequired,wistiaEmbedPermission:i().shape({value:i().bool.isRequired,status:i().string.isRequired,set:i().func.isRequired}).isRequired},t.forwardRef((function(e,s){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},e),t.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),i().bool.isRequired,i().func.isRequired,i().func,i().string;const It="yoast-seo/editor",Pt="yoast-seo/editor",Tt=()=>{const e=(e=>{const r=(0,k.useSelect)((e=>e(It).getIsPremium()),[]),o=(0,k.useSelect)((e=>e(It).getIsWooSeoActive()),[]),n=(0,k.useSelect)((e=>e(It).getIsWooCommerceActive()),[]),a=(0,k.useSelect)((e=>e(It).getIsProduct()),[]),i=(0,k.useSelect)((e=>e(It).getIsProductTerm()),[]),l={upsellLink:e.premium};if(n&&(a||i)){const n=(0,s.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,s.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");l.newToText=(0,s.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,s.__)("New in %1$s","wordpress-seo"),n),r?(l.upsellLabel=(0,s.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,s.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),l.upsellLink=e.woo,l.ctbId="5b32250e-e6f0-44ae-ad74-3cefc8e427f9"):o||(l.upsellLabel=`${(0,s.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,s.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,l.bundleNote=(0,t.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${n}`),l.upsellLink=e.bundle,l.ctbId="c7e7baa1-2020-420c-a427-89701700b607")}return l})({premium:(0,k.useSelect)((e=>e(Pt).selectLink("https://yoa.st/ai-fix-assessments-upsell")),[]),bundle:(0,k.useSelect)((e=>e(Pt).selectLink("https://yoa.st/ai-fix-assessments-upsell-woo-seo-premium-bundle")),[]),woo:(0,k.useSelect)((e=>e(Pt).selectLink("https://yoa.st/ai-fix-assessments-upsell-woo-seo")),[])}),r=(0,k.useSelect)((e=>e(Pt).selectLink("https://yoa.st/ai-fix-assessments-upsell-learn-more")),[]),o=(0,k.useSelect)((e=>e(Pt).selectImageLink("ai-fix-assessments-thumbnail.png")),[]),n=(0,l.useMemo)((()=>({src:o,width:"432",height:"244"})),[o]),a=(0,k.useSelect)((e=>e(Pt).selectWistiaEmbedPermissionValue()),[]),i=(0,k.useSelect)((e=>e(Pt).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:c}=(0,k.useDispatch)(Pt),d=(0,l.useMemo)((()=>({value:a,status:i,set:c})),[a,i,c]);return(0,t.createElement)(Ue,{learnMoreLink:r,thumbnail:n,wistiaEmbedPermission:d,...e})},At=e=>{let t=[...e];return e.forEach((e=>{e.innerBlocks&&e.innerBlocks.length>0&&(t=[...t,...At(e.innerBlocks)])})),t};P.noop,P.noop,P.noop,window.yoast.externals.redux;const Mt=({id:e,isPremium:r})=>{const n=e+"AIFixes",[a,,,i,c]=(0,le.useToggleState)(!1),{activeMarker:d,activeAIButtonId:u,editorType:p,isWooSeoUpsellPost:m}=(0,k.useSelect)((e=>({activeMarker:e("yoast-seo/editor").getActiveMarker(),activeAIButtonId:e("yoast-seo/editor").getActiveAIFixesButton(),editorType:e("yoast-seo/editor").getEditorType(),isWooSeoUpsellPost:e("yoast-seo/editor").getIsWooSeoUpsell()})),[]),h=(()=>{const e=(0,k.useSelect)((e=>e("yoast-seo/editor").getEditorType()),[]);return"blockEditor"===e?(0,k.useSelect)((e=>e("core/edit-post").getEditorMode()),[]):"classicEditor"===e?function(){const e=document.getElementById("wp-content-wrap");return!!e&&e.classList.contains("html-active")}()?"text":"visual":""})(),y=!r||m,{setActiveAIFixesButton:g,setActiveMarker:w,setMarkerPauseStatus:b,setMarkerStatus:f}=(0,k.useDispatch)("yoast-seo/editor"),E=(0,l.useRef)(null),[x,_]=(0,l.useState)(""),v=(0,s.__)("Optimize with AI","wordpress-seo"),R=(0,s.__)("Please switch to the visual editor to optimize with AI.","wordpress-seo"),N=u===n,{isEnabled:S,ariaLabel:L}=(0,k.useSelect)((e=>{if(null!==u&&!N)return{isEnabled:!1,ariaLabel:null};const t=e("yoast-seo/editor").getDisabledAIFixesButtons();if(Object.keys(t).includes(n))return{isEnabled:!1,ariaLabel:t[n]};if("visual"!==h)return{isEnabled:!1,ariaLabel:R};if("blockEditor"===p){const t=At(e("core/block-editor").getBlocks()).every((t=>"visual"===e("core/block-editor").getBlockMode(t.clientId)));return{isEnabled:t,ariaLabel:t?v:R}}return{isEnabled:!0,ariaLabel:v}}),[N,u,h]),B=()=>{d&&(w(null),b(!1),window.YoastSEO.analysis.applyMarks(new X.Paper("",{}),[])),n===u?(g(null),f("enabled")):(g(n),f("disabled")),_("")},C=(0,l.useCallback)((()=>{y?i():((0,de.doAction)("yoast.ai.fixAssessments",n),B())}),[B,i]),I=(0,l.useCallback)((()=>{L&&_("yoast-tooltip yoast-tooltip-multiline "+(S?"yoast-tooltip-w":"yoast-tooltip-nw"))}),[S,L]),P=(0,l.useCallback)((()=>{_("")}),[]);return(0,t.createElement)(o.IconAIFixesButton,{onClick:C,ariaLabel:L,onPointerEnter:I,onPointerLeave:P,id:n,className:`ai-button ${x}`,pressed:N,disabled:!S},y&&(0,t.createElement)(ce,{className:"yst-fixes-button__lock-icon yst-text-amber-900"}),(0,t.createElement)(o.SparklesIcon,{pressed:N}),a&&(0,t.createElement)(le.Modal,{className:"yst-introduction-modal",isOpen:a,onClose:c,initialFocus:E},(0,t.createElement)(le.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl yst-introduction-modal-panel"},(0,t.createElement)(Tt,{onClose:c,focusElementRef:E}))))};Mt.propTypes={id:i().string.isRequired,isPremium:i().bool},Mt.defaultProps={isPremium:!1};const Ot=Mt,$t=(e,t,s)=>{const r=document.body.classList.contains("elementor-editor-active"),o=!t&&!r,n="classicEditor"===(0,k.select)("yoast-seo/editor").getEditorType();return e&&o&&!s&&!n},Ft=d().span`
	font-size: 1em;
	font-weight: bold;
	margin: 0 0 8px;
	display: block;
`,qt=d().div`
	padding: 16px;
`,Ut=d()(I)`
	margin: -8px 0 -4px 4px;
`;class Kt extends l.Component{constructor(...e){super(...e),J(this,"renderAIOptimizeButton",((e,s)=>{const{isElementor:r,isAiFeatureEnabled:o,isTerm:n}=this.props,a=Oe().isPremium;if(!a||o)return $t(e,r,n)&&(0,t.createElement)(Ot,{id:s,isPremium:a})}))}renderResults(e){return(0,t.createElement)(l.Fragment,null,(0,t.createElement)(Ft,null,(0,s.__)("Analysis results","wordpress-seo"),(0,t.createElement)(Ut,{href:wpseoAdminL10n["shortlinks.readability_analysis_info"],className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,s.__)("Learn more about the readability analysis","wordpress-seo")))),(0,t.createElement)(Te,{results:this.props.results,upsellResults:e,marksButtonClassName:"yoast-tooltip yoast-tooltip-w",marksButtonStatus:this.props.marksButtonStatus,highlightingUpsellLink:"shortlinks.upsell.sidebar.highlighting_readability_analysis",shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderAIOptimizeButton:this.renderAIOptimizeButton}))}getUpsellResults(e,t){let r=wpseoAdminL10n["shortlinks.upsell.metabox.word_complexity"];return"sidebar"===e&&(r=wpseoAdminL10n["shortlinks.upsell.sidebar.word_complexity"]),r=(0,_.addQueryArgs)(r,{context:t}),function(){const e=X.helpers.getLanguagesWithWordComplexity(),t=window.wpseoScriptData.metabox.contentLocale,s=X.languageProcessing.getLanguage(t);return e.includes(s)}()?[{score:0,rating:"upsell",hasMarks:!1,id:"wordComplexity",text:(0,s.sprintf)(
/* Translators: %1$s is a span tag that adds styling to 'Word complexity', %2$s is a closing span tag.
       %3$s is an anchor tag with a link to yoast.com, %4$s is a closing anchor tag.*/
(0,s.__)("%1$sWord complexity%2$s: Is your vocabulary suited for a larger audience? %3$sYoast SEO Premium will tell you!%4$s","wordpress-seo"),"<span style='text-decoration: underline'>","</span>",`<a href="${r}" data-action="load-nfd-ctb" data-ctb-id="f6a84663-465f-4cb5-8ba5-f7a6d72224b2" target="_blank">`,"</a>"),markerId:"wordComplexity"}]:[]}render(){const e=Ae(this.props.overallScore);return(0,P.isNil)(this.props.overallScore)&&(e.className="loading"),(0,t.createElement)(x.LocationConsumer,null,(r=>(0,t.createElement)(x.RootContext.Consumer,null,(({locationContext:o})=>{let n=[];return this.props.shouldUpsell&&(n=this.getUpsellResults(r,o)),"sidebar"===r?(0,t.createElement)(w,{title:(0,s.__)("Readability analysis","wordpress-seo"),titleScreenReaderText:e.screenReaderReadabilityText,prefixIcon:se(e.className),prefixIconCollapsed:se(e.className),id:`yoast-readability-analysis-collapsible-${r}`},this.renderResults(n)):"metabox"===r?(0,t.createElement)(Me,{target:"wpseo-metabox-readability-root"},(0,t.createElement)(qt,null,(0,t.createElement)(ae,{target:"wpseo-readability-score-icon",scoreIndicator:e.className}),this.renderResults(n))):void 0}))))}}Kt.propTypes={results:i().array.isRequired,marksButtonStatus:i().string.isRequired,overallScore:i().number,shouldUpsell:i().bool,shouldUpsellHighlighting:i().bool,isAiFeatureEnabled:i().bool,isElementor:i().bool,isTerm:i().bool},Kt.defaultProps={overallScore:null,shouldUpsell:!1,shouldUpsellHighlighting:!1,isAiFeatureEnabled:!1,isElementor:!1,isTerm:!1};const zt=(0,k.withSelect)((e=>{const{getReadabilityResults:t,getMarkButtonStatus:s,getIsElementorEditor:r,getIsAiFeatureEnabled:o,getIsTerm:n}=e("yoast-seo/editor");return{...t(),marksButtonStatus:s(),isElementor:r(),isAiFeatureEnabled:o(),isTerm:n()}}))(Kt),Ht=d().p`
	color: ${C.colors.$color_upsell_text};
	margin: 0;
	padding-right: 8px;
`,jt=d().div`
	font-size: 1em;
	display: flex;
	flex-direction: ${e=>"horizontal"===e.alignment?"row":"column"};
	${(0,n.getDirectionalStyle)("border-left","border-right")}: 4px solid ${C.colors.$color_pink_dark};
	margin: 16px 0;
	padding: 0 0 0 8px;
	max-width: 600px;

	> ${Ht} {
		margin-bottom: ${e=>"vertical"===e.alignment&&"16px"};
	}
`,Yt=d()(o.SvgIcon)`
	margin: ${(0,n.getDirectionalStyle)("0 0 0 4px","0 4px 0 0")};
	transform: ${(0,n.getDirectionalStyle)("rotate(0deg)","rotate(180deg)")};
`,Wt=(0,n.makeOutboundLink)(o.UpsellLinkButton),Dt=e=>{const{alignment:r,url:o}=e;return(0,t.createElement)(jt,{alignment:r},(0,t.createElement)(Ht,null,(0,s.sprintf)(/* translators: %s expands to Yoast SEO Premium */
(0,s.__)("%s looks at more than just your main keyword. It analyzes different word forms, plurals, and past tenses. This helps your website perform even better in searches!","wordpress-seo"),"Yoast SEO Premium")),(0,t.createElement)("div",null,(0,t.createElement)(Wt,{href:o,className:"UpsellLinkButton","data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,s.sprintf)(/* translators: %s expands to Premium */
(0,s.__)("Go %s!","wordpress-seo"),"Premium"),(0,t.createElement)(Yt,{icon:"arrow-right",size:"8px",color:C.colors.$color_black}))))};Dt.propTypes={alignment:i().oneOf(["horizontal","vertical"]),url:i().string.isRequired},Dt.defaultProps={alignment:"vertical"};const Gt=Dt,Vt=e=>(0,t.createElement)(Se,{title:(0,s.__)("Write more natural and engaging content","wordpress-seo"),description:(0,s.sprintf)(/* translators: %s expands to "Yoast SEO Premium" */
(0,s.__)("Synonyms help users understand your copy better. It’s easier to read for both users and Google. In %s, you can add synonyms for your focus keyphrase, and we’ll help you optimize for them.","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:(0,s.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,s.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:ye(),upsellButtonText:(0,s.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,s.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:e.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,s.__)("1 year free support and updates included!","wordpress-seo")});Vt.propTypes={buyLink:i().string.isRequired};const Qt=Vt,Zt=e=>(0,t.createElement)(Se,{title:(0,s.__)("Reach a wider audience","wordpress-seo"),description:(0,s.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:(0,s.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,s.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:ye(),upsellButtonText:(0,s.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,s.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:e.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,s.__)("1 year free support and updates included!","wordpress-seo")});Zt.propTypes={buyLink:i().string.isRequired};const Jt=Zt,Xt=d().button`
	// Increase specificity to override WP rules.
	&& {
		display: flex;
		align-items: center;
	}

	.yoast-svg-icon {
		margin: 1px 7px 0 0;
		fill: currentColor;
	}
`,es={open:(0,s.__)("Open","wordpress-seo"),heading:"",closeIconButton:(0,s.__)("Close","wordpress-seo"),closeButton:""},ts=e=>{const[s,r]=(0,l.useState)(!1),n=Object.assign({},es,e.labels),a=(0,l.useCallback)((()=>r(!1)),[]),i=(0,l.useCallback)((()=>r(!0)),[]);return(0,t.createElement)(l.Fragment,null,(0,t.createElement)(Xt,{type:"button",onClick:i,className:`${e.classes.openButton} yoast-modal__button-open`},e.openButtonIcon&&(0,t.createElement)(o.SvgIcon,{icon:e.openButtonIcon,size:"13px"}),n.open),s&&(0,t.createElement)(he,{onRequestClose:a,className:e.className,title:n.heading},e.children))};ts.propTypes={openButtonIcon:i().string,labels:i().shape({open:i().string,modalAriaLabel:i().string.isRequired,heading:i().string,closeIconButton:i().string,closeButton:i().string}).isRequired,classes:i().shape({openButton:i().string,closeIconButton:i().string,closeButton:i().string}),className:i().string,children:i().any.isRequired},ts.defaultProps={className:pe,openButtonIcon:"",classes:{}};const ss=ts;function rs({location:e}){return(0,t.createElement)(r.Slot,{name:`yoast-synonyms-${e}`})}rs.propTypes={location:i().string.isRequired};const os=d().span`
	font-size: 1em;
	font-weight: bold;
	margin: 1.5em 0 1em;
	display: block;
`;class ns extends l.Component{constructor(...e){super(...e),J(this,"renderAIOptimizeButton",((e,s)=>{const{isElementor:r,isAiFeatureEnabled:o,isPremium:n,isTerm:a}=this.props;if(!n||o)return $t(e,r,a)&&(0,t.createElement)(Ot,{id:s,isPremium:n})}))}renderSynonymsUpsell(e,r){const o={className:`${pe} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,classes:{openButton:"wpseo-keyword-synonyms button-link"},labels:{open:"+ "+(0,s.__)("Add synonyms","wordpress-seo"),modalAriaLabel:(0,s.__)("Add synonyms","wordpress-seo"),heading:(0,s.__)("Add synonyms","wordpress-seo")}},n=wpseoAdminL10n["sidebar"===e.toLowerCase()?"shortlinks.upsell.sidebar.focus_keyword_synonyms_button":"shortlinks.upsell.metabox.focus_keyword_synonyms_button"];return(0,t.createElement)(ss,{...o},(0,t.createElement)(ue,null,(0,t.createElement)(Qt,{buyLink:(0,_.addQueryArgs)(n,{context:r})})))}renderMultipleKeywordsUpsell(e,r){const o={className:`${pe} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,classes:{openButton:"wpseo-multiple-keywords button-link"},labels:{open:"+ "+(0,s.__)("Add related keyphrase","wordpress-seo"),modalAriaLabel:(0,s.__)("Add related keyphrases","wordpress-seo"),heading:(0,s.__)("Add related keyphrases","wordpress-seo")}},n=wpseoAdminL10n["sidebar"===e.toLowerCase()?"shortlinks.upsell.sidebar.focus_keyword_additional_button":"shortlinks.upsell.metabox.focus_keyword_additional_button"];return(0,t.createElement)(ss,{...o},(0,t.createElement)(ue,null,(0,t.createElement)(Jt,{buyLink:(0,_.addQueryArgs)(n,{context:r})})))}renderWordFormsUpsell(e,s){let r="sidebar"===e?wpseoAdminL10n["shortlinks.upsell.sidebar.morphology_upsell_sidebar"]:wpseoAdminL10n["shortlinks.upsell.sidebar.morphology_upsell_metabox"];return r=(0,_.addQueryArgs)(r,{context:s}),(0,t.createElement)(Gt,{url:r,alignment:"sidebar"===e?"vertical":"horizontal"})}renderTabIcon(e,s){return"metabox"!==e?null:(0,t.createElement)(ae,{target:"wpseo-seo-score-icon",scoreIndicator:s})}getUpsellResults(e,t){let r=wpseoAdminL10n["shortlinks.upsell.metabox.keyphrase_distribution"];return"sidebar"===e&&(r=wpseoAdminL10n["shortlinks.upsell.sidebar.keyphrase_distribution"]),r=(0,_.addQueryArgs)(r,{context:t}),[{score:0,rating:"upsell",hasMarks:!1,hasJumps:!1,id:"keyphraseDistribution",text:(0,s.sprintf)(
/* Translators: %1$s is a span tag that adds styling to 'Keyphrase distribution', %2$s is a closing span tag.
     %3%s is an anchor tag with a link to yoast.com, %4$s is a closing anchor tag.*/
(0,s.__)("%1$sKeyphrase distribution%2$s: Have you evenly distributed your focus keyphrase throughout the whole text? %3$sYoast SEO Premium will tell you!%4$s","wordpress-seo"),"<span style='text-decoration: underline'>","</span>",`<a href="${r}" data-action="load-nfd-ctb" data-ctb-id="f6a84663-465f-4cb5-8ba5-f7a6d72224b2" target="_blank">`,"</a>"),markerId:"keyphraseDistribution"}]}render(){const e=Ae(this.props.overallScore),{isPremium:r}=this.props;return"loading"!==e.className&&""===this.props.keyword&&(e.className="na",e.screenReaderReadabilityText=(0,s.__)("Enter a focus keyphrase to calculate the SEO score","wordpress-seo")),(0,t.createElement)(x.LocationConsumer,null,(o=>(0,t.createElement)(x.RootContext.Consumer,null,(({locationContext:n})=>{const a="metabox"===o?y:w;let i=[];return this.props.shouldUpsell&&(i=this.getUpsellResults(o,n)),(0,t.createElement)(l.Fragment,null,(0,t.createElement)(a,{title:r?(0,s.__)("Premium SEO analysis","wordpress-seo"):(0,s.__)("SEO analysis","wordpress-seo"),titleScreenReaderText:e.screenReaderReadabilityText,prefixIcon:se(e.className),prefixIconCollapsed:se(e.className),subTitle:this.props.keyword,id:`yoast-seo-analysis-collapsible-${o}`},(0,t.createElement)(rs,{location:o}),this.props.shouldUpsell&&(0,t.createElement)(l.Fragment,null,this.renderSynonymsUpsell(o,n),this.renderMultipleKeywordsUpsell(o,n)),this.props.shouldUpsellWordFormRecognition&&this.renderWordFormsUpsell(o,n),(0,t.createElement)(os,null,(0,s.__)("Analysis results","wordpress-seo")),(0,t.createElement)(Te,{results:this.props.results,upsellResults:i,marksButtonClassName:"yoast-tooltip yoast-tooltip-w",editButtonClassName:"yoast-tooltip yoast-tooltip-w",marksButtonStatus:this.props.marksButtonStatus,location:o,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,highlightingUpsellLink:"shortlinks.upsell.sidebar.highlighting_seo_analysis",renderAIOptimizeButton:this.renderAIOptimizeButton})),this.renderTabIcon(o,e.className))}))))}}ns.propTypes={results:i().array,marksButtonStatus:i().string,keyword:i().string,shouldUpsell:i().bool,shouldUpsellWordFormRecognition:i().bool,overallScore:i().number,shouldUpsellHighlighting:i().bool,isElementor:i().bool,isAiFeatureEnabled:i().bool,isPremium:i().bool,isTerm:i().bool},ns.defaultProps={results:[],marksButtonStatus:null,keyword:"",shouldUpsell:!1,shouldUpsellWordFormRecognition:!1,overallScore:null,shouldUpsellHighlighting:!1,isElementor:!1,isAiFeatureEnabled:!1,isPremium:!1,isTerm:!1};const as=(0,k.withSelect)(((e,t)=>{const{getFocusKeyphrase:s,getMarksButtonStatus:r,getResultsForKeyword:o,getIsElementorEditor:n,getIsPremium:a,getIsAiFeatureEnabled:i,getIsTerm:l}=e("yoast-seo/editor"),c=s();return{...o(c),marksButtonStatus:t.hideMarksButtons?"disabled":r(),keyword:c,isElementor:n(),isPremium:a(),isAiFeatureEnabled:i(),isTerm:l()}}))(ns);function is(){const e=Oe();return(0,P.get)(e,"multilingualPluginActive",!1)}const ls=d().span`
	font-size: 1em;
	font-weight: bold;
	margin: 0 0 8px;
	display: block;
`,cs=d().div`
	padding: 16px;
`,ds=d()(I)`
	margin: -8px 0 -4px 4px;
`,us=d().p`
	min-height: 24px;
	margin: 12px 0 0 0;
	padding: 0;
	display: flex;
	align-items: flex-start;
`,ps=d()(o.SvgIcon)`
	margin: 3px 11px 0 0; // icon 13 + 11 right margin = 24 for the 8px grid.
`,ms=e=>{const r=wpseoAdminL10n["shortlinks.inclusive_language_analysis_info"];function n(){return(0,t.createElement)(l.Fragment,null,(0,t.createElement)(ls,null,(0,s.__)("Analysis results","wordpress-seo"),(0,t.createElement)(ds,{href:r,className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,s.__)("Learn more about the inclusive language analysis","wordpress-seo")))),(0,t.createElement)(Te,{results:e.results,marksButtonClassName:"yoast-tooltip yoast-tooltip-w",marksButtonStatus:e.marksButtonStatus,resultCategoryLabels:{problems:(0,s.__)("Non-inclusive","wordpress-seo"),improvements:(0,s.__)("Potentially non-inclusive","wordpress-seo")},highlightingUpsellLink:"shortlinks.upsell.sidebar.highlighting_inclusive_analysis",shouldUpsellHighlighting:e.shouldUpsellHighlighting}))}const a=ge((0,s.sprintf)(/* Translators: %1$s expands to a link on yoast.com, %2$s expands to the anchor end tag. */
(0,s.__)("%1$sInclusive language%2$s: We haven't detected any potentially non-inclusive phrases. Great work!","wordpress-seo"),"<a>","</a>"),{a:(0,t.createElement)("a",{href:r,target:"_blank",rel:"noreferrer"})});function i(){const e=(0,s.__)("We noticed that you are using a multilingual plugin. Please be aware that this analysis feedback is intended only for texts written in English.","wordpress-seo");return(0,t.createElement)(o.Alert,{type:"info"},e)}function c(){return(0,t.createElement)(l.Fragment,null,(0,t.createElement)(ls,null,(0,s.__)("Analysis results","wordpress-seo"),(0,t.createElement)(ds,{href:r,className:"dashicons"},(0,t.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,s.__)("Learn more about the inclusive language analysis","wordpress-seo")))),(0,t.createElement)(us,null,(0,t.createElement)(ps,{icon:"circle",color:"#7ad03a",size:"13px"}),(0,t.createElement)("span",null,a)))}const d=Ae(e.overallScore);return(0,P.isNil)(e.overallScore)&&(d.className="loading"),(0,t.createElement)(x.LocationConsumer,null,(r=>{return"sidebar"===r?(o=e.results,a=d,(0,t.createElement)(w,{title:(0,s.__)("Inclusive language","wordpress-seo"),titleScreenReaderText:a.screenReaderInclusiveLanguageText,prefixIcon:se(a.className),prefixIconCollapsed:se(a.className),id:"yoast-inclusive-language-analysis-collapsible-sidebar"},is()?i():null,o.length>=1?n():c())):"metabox"===r?function(e,s){return(0,t.createElement)(oe,{target:"wpseo-metabox-inclusive-language-root"},(0,t.createElement)(cs,null,(0,t.createElement)(ae,{target:"wpseo-inclusive-language-score-icon",scoreIndicator:s.className}),is()?i():null,e.length>=1?n():c()))}(e.results,d):void 0;var o,a}))};ms.propTypes={results:i().array,marksButtonStatus:i().oneOf(["enabled","disabled","hidden"]).isRequired,overallScore:i().number,shouldUpsellHighlighting:i().bool},ms.defaultProps={results:[],overallScore:null,shouldUpsellHighlighting:!1};const hs=(0,k.withSelect)((e=>{const{getInclusiveLanguageResults:t,getMarkButtonStatus:s}=e("yoast-seo/editor");return{...t(),marksButtonStatus:s()}}))(ms);window.yoast=window.yoast||{},window.yoast.externals=window.yoast.externals||{},window.yoast.externals.components={CollapsibleCornerstone:f,KeywordInput:Q,ReadabilityAnalysis:zt,SeoAnalysis:as,InclusiveLanguageAnalysis:hs}})();