(()=>{var e={1206:function(e){e.exports=function(e){var t={};function s(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,s),a.l=!0,a.exports}return s.m=e,s.c=t,s.d=function(e,t,r){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(s.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)s.d(r,a,function(t){return e[t]}.bind(null,a));return r},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="",s(s.s=90)}({17:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r=s(18),a=function(){function e(){}return e.getFirstMatch=function(e,t){var s=t.match(e);return s&&s.length>0&&s[1]||""},e.getSecondMatch=function(e,t){var s=t.match(e);return s&&s.length>1&&s[2]||""},e.matchAndReturnConst=function(e,t,s){if(e.test(t))return s},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,s,r){void 0===r&&(r=!1);var a=e.getVersionPrecision(t),o=e.getVersionPrecision(s),n=Math.max(a,o),i=0,l=e.map([t,s],(function(t){var s=n-e.getVersionPrecision(t),r=t+new Array(s+1).join(".0");return e.map(r.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(r&&(i=n-Math.min(a,o)),n-=1;n>=i;){if(l[0][n]>l[1][n])return 1;if(l[0][n]===l[1][n]){if(n===i)return 0;n-=1}else if(l[0][n]<l[1][n])return-1}},e.map=function(e,t){var s,r=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(s=0;s<e.length;s+=1)r.push(t(e[s]));return r},e.find=function(e,t){var s,r;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(s=0,r=e.length;s<r;s+=1){var a=e[s];if(t(a,s))return a}},e.assign=function(e){for(var t,s,r=e,a=arguments.length,o=new Array(a>1?a-1:0),n=1;n<a;n++)o[n-1]=arguments[n];if(Object.assign)return Object.assign.apply(Object,[e].concat(o));var i=function(){var e=o[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){r[t]=e[t]}))};for(t=0,s=o.length;t<s;t+=1)i();return e},e.getBrowserAlias=function(e){return r.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return r.BROWSER_MAP[e]||""},e}();t.default=a,e.exports=t.default},18:function(e,t,s){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r,a=(r=s(91))&&r.__esModule?r:{default:r},o=s(18);function n(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var i=function(){function e(){}var t,s;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new a.default(e,t)},e.parse=function(e){return new a.default(e).getResult()},t=e,s=[{key:"BROWSER_MAP",get:function(){return o.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return o.ENGINE_MAP}},{key:"OS_MAP",get:function(){return o.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return o.PLATFORMS_MAP}}],null&&n(t.prototype,null),s&&n(t,s),e}();t.default=i,e.exports=t.default},91:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r=l(s(92)),a=l(s(93)),o=l(s(94)),n=l(s(95)),i=l(s(17));function l(e){return e&&e.__esModule?e:{default:e}}var c=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=i.default.find(r.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=i.default.find(a.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=i.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=i.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return i.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,s={},r=0,a={},o=0;if(Object.keys(e).forEach((function(t){var n=e[t];"string"==typeof n?(a[t]=n,o+=1):"object"==typeof n&&(s[t]=n,r+=1)})),r>0){var n=Object.keys(s),l=i.default.find(n,(function(e){return t.isOS(e)}));if(l){var c=this.satisfies(s[l]);if(void 0!==c)return c}var d=i.default.find(n,(function(e){return t.isPlatform(e)}));if(d){var u=this.satisfies(s[d]);if(void 0!==u)return u}}if(o>0){var p=Object.keys(a),m=i.default.find(p,(function(e){return t.isBrowser(e,!0)}));if(void 0!==m)return this.compareVersion(a[m])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var s=this.getBrowserName().toLowerCase(),r=e.toLowerCase(),a=i.default.getBrowserTypeByAlias(r);return t&&a&&(r=a.toLowerCase()),r===s},t.compareVersion=function(e){var t=[0],s=e,r=!1,a=this.getBrowserVersion();if("string"==typeof a)return">"===e[0]||"<"===e[0]?(s=e.substr(1),"="===e[1]?(r=!0,s=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?s=e.substr(1):"~"===e[0]&&(r=!0,s=e.substr(1)),t.indexOf(i.default.compareVersions(a,s,r))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=c,e.exports=t.default},92:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r,a=(r=s(17))&&r.__esModule?r:{default:r},o=/version\/(\d+(\.?_?\d+)+)/i,n=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},s=a.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},s=a.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},s=a.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},s=a.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},s=a.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},s=a.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},s=a.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},s=a.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},s=a.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},s=a.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},s=a.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},s=a.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},s=a.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},s=a.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},s=a.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return s&&(t.version=s),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},s=a.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},s=a.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},s=a.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},s=a.default.getFirstMatch(o,e)||a.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},s=a.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},s=a.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},s=a.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},s=a.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},s=a.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},s=a.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},s=a.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},s=a.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},s=a.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:function(e){var t=!e.test(/like android/i),s=e.test(/android/i);return t&&s},describe:function(e){var t={name:"Android Browser"},s=a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},s=a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},s=a.default.getFirstMatch(o,e);return s&&(t.version=s),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:a.default.getFirstMatch(t,e),version:a.default.getSecondMatch(t,e)}}}];t.default=n,e.exports=t.default},93:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r,a=(r=s(17))&&r.__esModule?r:{default:r},o=s(18),n=[{test:[/Roku\/DVP/],describe:function(e){var t=a.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:o.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=a.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=a.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),s=a.default.getWindowsVersionName(t);return{name:o.OS_MAP.Windows,version:t,versionName:s}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:o.OS_MAP.iOS},s=a.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return s&&(t.version=s),t}},{test:[/macintosh/i],describe:function(e){var t=a.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),s=a.default.getMacOSVersionName(t),r={name:o.OS_MAP.MacOS,version:t};return s&&(r.versionName=s),r}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=a.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:o.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),s=e.test(/android/i);return t&&s},describe:function(e){var t=a.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),s=a.default.getAndroidVersionName(t),r={name:o.OS_MAP.Android,version:t};return s&&(r.versionName=s),r}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=a.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),s={name:o.OS_MAP.WebOS};return t&&t.length&&(s.version=t),s}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=a.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||a.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||a.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:o.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=a.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=a.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:o.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:o.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=a.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:o.OS_MAP.PlayStation4,version:t}}}];t.default=n,e.exports=t.default},94:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r,a=(r=s(17))&&r.__esModule?r:{default:r},o=s(18),n=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=a.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",s={type:o.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(s.model=t),s}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),s=e.test(/like (ipod|iphone)/i);return t&&!s},describe:function(e){var t=a.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:o.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:o.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:o.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:o.PLATFORMS_MAP.tv}}}];t.default=n,e.exports=t.default},95:function(e,t,s){"use strict";t.__esModule=!0,t.default=void 0;var r,a=(r=s(17))&&r.__esModule?r:{default:r},o=s(18),n=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:o.ENGINE_MAP.Blink};var t=a.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:o.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:o.ENGINE_MAP.Trident},s=a.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:o.ENGINE_MAP.Presto},s=a.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:function(e){var t=e.test(/gecko/i),s=e.test(/like gecko/i);return t&&!s},describe:function(e){var t={name:o.ENGINE_MAP.Gecko},s=a.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:o.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:o.ENGINE_MAP.WebKit},s=a.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return s&&(t.version=s),t}}];t.default=n,e.exports=t.default}})},4184:(e,t)=>{var s;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var o=typeof s;if("string"===o||"number"===o)e.push(s);else if(Array.isArray(s)&&s.length){var n=a.apply(null,s);n&&e.push(n)}else if("object"===o)for(var i in s)r.call(s,i)&&s[i]&&e.push(i)}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(s=function(){return a}.apply(t,[]))||(e.exports=s)}()},6214:(e,t,s)=>{"use strict";var r=s(9864),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},n={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function l(e){return r.isMemo(e)?n:i[e.$$typeof]||a}i[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0};var c=Object.defineProperty,d=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(t,s,r){if("string"!=typeof s){if(f){var a=m(s);a&&a!==f&&e(t,a,r)}var n=d(s);u&&(n=n.concat(u(s)));for(var i=l(t),h=l(s),y=0;y<n.length;++y){var _=n[y];if(!(o[_]||r&&r[_]||h&&h[_]||i&&i[_])){var w=p(s,_);try{c(t,_,w)}catch(e){}}}return t}return t}},667:e=>{"use strict";var t=Array.isArray,s=Object.keys,r=Object.prototype.hasOwnProperty,a="undefined"!=typeof Element;function o(e,n){if(e===n)return!0;if(e&&n&&"object"==typeof e&&"object"==typeof n){var i,l,c,d=t(e),u=t(n);if(d&&u){if((l=e.length)!=n.length)return!1;for(i=l;0!=i--;)if(!o(e[i],n[i]))return!1;return!0}if(d!=u)return!1;var p=e instanceof Date,m=n instanceof Date;if(p!=m)return!1;if(p&&m)return e.getTime()==n.getTime();var f=e instanceof RegExp,h=n instanceof RegExp;if(f!=h)return!1;if(f&&h)return e.toString()==n.toString();var y=s(e);if((l=y.length)!==s(n).length)return!1;for(i=l;0!=i--;)if(!r.call(n,y[i]))return!1;if(a&&e instanceof Element&&n instanceof Element)return e===n;for(i=l;0!=i--;)if(!("_owner"===(c=y[i])&&e.$$typeof||o(e[c],n[c])))return!1;return!0}return e!=e&&n!=n}e.exports=function(e,t){try{return o(e,t)}catch(e){if(e.message&&e.message.match(/stack|recursion/i)||-2146828260===e.number)return console.warn("Warning: react-fast-compare does not handle circular references.",e.name,e.message),!1;throw e}}},5760:e=>{"use strict";function t(e){this._maxSize=e,this.clear()}t.prototype.clear=function(){this._size=0,this._values=Object.create(null)},t.prototype.get=function(e){return this._values[e]},t.prototype.set=function(e,t){return this._size>=this._maxSize&&this.clear(),e in this._values||this._size++,this._values[e]=t};var s=/[^.^\]^[]+|(?=\[\]|\.\.)/g,r=/^\d+$/,a=/^\d/,o=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,n=/^\s*(['"]?)(.*?)(\1)\s*$/,i=new t(512),l=new t(512),c=new t(512);function d(e){return i.get(e)||i.set(e,u(e).map((function(e){return e.replace(n,"$2")})))}function u(e){return e.match(s)||[""]}function p(e){return"string"==typeof e&&e&&-1!==["'",'"'].indexOf(e.charAt(0))}function m(e){return!p(e)&&(function(e){return e.match(a)&&!e.match(r)}(e)||function(e){return o.test(e)}(e))}e.exports={Cache:t,split:u,normalizePath:d,setter:function(e){var t=d(e);return l.get(e)||l.set(e,(function(e,s){for(var r=0,a=t.length,o=e;r<a-1;){var n=t[r];if("__proto__"===n||"constructor"===n||"prototype"===n)return e;o=o[t[r++]]}o[t[r]]=s}))},getter:function(e,t){var s=d(e);return c.get(e)||c.set(e,(function(e){for(var r=0,a=s.length;r<a;){if(null==e&&t)return;e=e[s[r++]]}return e}))},join:function(e){return e.reduce((function(e,t){return e+(p(t)||r.test(t)?"["+t+"]":(e?".":"")+t)}),"")},forEach:function(e,t,s){!function(e,t,s){var r,a,o,n,i=e.length;for(a=0;a<i;a++)(r=e[a])&&(m(r)&&(r='"'+r+'"'),o=!(n=p(r))&&/^\d+$/.test(r),t.call(s,r,n,o,a,e))}(Array.isArray(e)?e:u(e),t,s)}}},8133:(e,t,s)=>{"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},o=function(){function e(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,s,r){return s&&e(t.prototype,s),r&&e(t,r),t}}(),n=c(s(9196)),i=c(s(5890)),l=c(s(4184));function c(e){return e&&e.__esModule?e:{default:e}}function d(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}var u={animating:"rah-animating",animatingUp:"rah-animating--up",animatingDown:"rah-animating--down",animatingToHeightZero:"rah-animating--to-height-zero",animatingToHeightAuto:"rah-animating--to-height-auto",animatingToHeightSpecific:"rah-animating--to-height-specific",static:"rah-static",staticHeightZero:"rah-static--height-zero",staticHeightAuto:"rah-static--height-auto",staticHeightSpecific:"rah-static--height-specific"},p=["animateOpacity","animationStateClasses","applyInlineTransitions","children","contentClassName","delay","duration","easing","height","onAnimationEnd","onAnimationStart"];function m(e){for(var t=arguments.length,s=Array(t>1?t-1:0),r=1;r<t;r++)s[r-1]=arguments[r];if(!s.length)return e;for(var a={},o=Object.keys(e),n=0;n<o.length;n++){var i=o[n];-1===s.indexOf(i)&&(a[i]=e[i])}return a}function f(e){e.forEach((function(e){return cancelAnimationFrame(e)}))}function h(e){return!isNaN(parseFloat(e))&&isFinite(e)}function y(e){return"string"==typeof e&&e.search("%")===e.length-1&&h(e.substr(0,e.length-1))}function _(e,t){e&&"function"==typeof e&&e(t)}var w=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var s=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));s.animationFrameIDs=[];var r="auto",o="visible";h(e.height)?(r=e.height<0||"0"===e.height?0:e.height,o="hidden"):y(e.height)&&(r="0%"===e.height?0:e.height,o="hidden"),s.animationStateClasses=a({},u,e.animationStateClasses);var n=s.getStaticStateClasses(r);return s.state={animationStateClasses:n,height:r,overflow:o,shouldUseTransitions:!1},s}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"componentDidMount",value:function(){var e=this.state.height;this.contentElement&&this.contentElement.style&&this.hideContent(e)}},{key:"componentDidUpdate",value:function(e,t){var s,r,a=this,o=this.props,n=o.delay,i=o.duration,c=o.height,u=o.onAnimationEnd,p=o.onAnimationStart;if(this.contentElement&&c!==e.height){var m;this.showContent(t.height),this.contentElement.style.overflow="hidden";var w=this.contentElement.offsetHeight;this.contentElement.style.overflow="";var g=i+n,b=null,v={height:null,overflow:"hidden"},E="auto"===t.height;h(c)?(b=c<0||"0"===c?0:c,v.height=b):y(c)?(b="0%"===c?0:c,v.height=b):(b=w,v.height="auto",v.overflow=null),E&&(v.height=b,b=w);var x=(0,l.default)((d(m={},this.animationStateClasses.animating,!0),d(m,this.animationStateClasses.animatingUp,"auto"===e.height||c<e.height),d(m,this.animationStateClasses.animatingDown,"auto"===c||c>e.height),d(m,this.animationStateClasses.animatingToHeightZero,0===v.height),d(m,this.animationStateClasses.animatingToHeightAuto,"auto"===v.height),d(m,this.animationStateClasses.animatingToHeightSpecific,v.height>0),m)),k=this.getStaticStateClasses(v.height);this.setState({animationStateClasses:x,height:b,overflow:"hidden",shouldUseTransitions:!E}),clearTimeout(this.timeoutID),clearTimeout(this.animationClassesTimeoutID),E?(v.shouldUseTransitions=!0,f(this.animationFrameIDs),this.animationFrameIDs=(s=function(){a.setState(v),_(p,{newHeight:v.height})},(r=[])[0]=requestAnimationFrame((function(){r[1]=requestAnimationFrame((function(){s()}))})),r),this.animationClassesTimeoutID=setTimeout((function(){a.setState({animationStateClasses:k,shouldUseTransitions:!1}),a.hideContent(v.height),_(u,{newHeight:v.height})}),g)):(_(p,{newHeight:b}),this.timeoutID=setTimeout((function(){v.animationStateClasses=k,v.shouldUseTransitions=!1,a.setState(v),"auto"!==c&&a.hideContent(b),_(u,{newHeight:b})}),g))}}},{key:"componentWillUnmount",value:function(){f(this.animationFrameIDs),clearTimeout(this.timeoutID),clearTimeout(this.animationClassesTimeoutID),this.timeoutID=null,this.animationClassesTimeoutID=null,this.animationStateClasses=null}},{key:"showContent",value:function(e){0===e&&(this.contentElement.style.display="")}},{key:"hideContent",value:function(e){0===e&&(this.contentElement.style.display="none")}},{key:"getStaticStateClasses",value:function(e){var t;return(0,l.default)((d(t={},this.animationStateClasses.static,!0),d(t,this.animationStateClasses.staticHeightZero,0===e),d(t,this.animationStateClasses.staticHeightSpecific,e>0),d(t,this.animationStateClasses.staticHeightAuto,"auto"===e),t))}},{key:"render",value:function(){var e,t=this,s=this.props,r=s.animateOpacity,o=s.applyInlineTransitions,i=s.children,c=s.className,u=s.contentClassName,f=s.delay,h=s.duration,y=s.easing,_=s.id,w=s.style,g=this.state,b=g.height,v=g.overflow,E=g.animationStateClasses,x=g.shouldUseTransitions,k=a({},w,{height:b,overflow:v||w.overflow});x&&o&&(k.transition="height "+h+"ms "+y+" "+f+"ms",w.transition&&(k.transition=w.transition+", "+k.transition),k.WebkitTransition=k.transition);var S={};r&&(S.transition="opacity "+h+"ms "+y+" "+f+"ms",S.WebkitTransition=S.transition,0===b&&(S.opacity=0));var L=(0,l.default)((d(e={},E,!0),d(e,c,c),e)),T=void 0!==this.props["aria-hidden"]?this.props["aria-hidden"]:0===b;return n.default.createElement("div",a({},m.apply(void 0,[this.props].concat(p)),{"aria-hidden":T,className:L,id:_,style:k}),n.default.createElement("div",{className:u,style:S,ref:function(e){return t.contentElement=e}},i))}}]),t}(n.default.Component);w.propTypes={"aria-hidden":i.default.bool,animateOpacity:i.default.bool,animationStateClasses:i.default.object,applyInlineTransitions:i.default.bool,children:i.default.any.isRequired,className:i.default.string,contentClassName:i.default.string,delay:i.default.number,duration:i.default.number,easing:i.default.string,height:function(e,t,s){var a=e[t];return"number"==typeof a&&a>=0||y(a)||"auto"===a?null:new TypeError('value "'+a+'" of type "'+(void 0===a?"undefined":r(a))+'" is invalid type for '+t+" in "+s+'. It needs to be a positive number, string "auto" or percentage string (e.g. "15%").')},id:i.default.string,onAnimationEnd:i.default.func,onAnimationStart:i.default.func,style:i.default.object},w.defaultProps={animateOpacity:!1,animationStateClasses:u,applyInlineTransitions:!0,duration:250,delay:0,easing:"ease",style:{}},t.Z=w},591:e=>{for(var t=[],s=0;s<256;++s)t[s]=(s+256).toString(16).substr(1);e.exports=function(e,s){var r=s||0,a=t;return[a[e[r++]],a[e[r++]],a[e[r++]],a[e[r++]],"-",a[e[r++]],a[e[r++]],"-",a[e[r++]],a[e[r++]],"-",a[e[r++]],a[e[r++]],"-",a[e[r++]],a[e[r++]],a[e[r++]],a[e[r++]],a[e[r++]],a[e[r++]]].join("")}},9176:e=>{var t="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto);if(t){var s=new Uint8Array(16);e.exports=function(){return t(s),s}}else{var r=new Array(16);e.exports=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),r[t]=e>>>((3&t)<<3)&255;return r}}},3409:(e,t,s)=>{var r=s(9176),a=s(591);e.exports=function(e,t,s){var o=t&&s||0;"string"==typeof e&&(t="binary"===e?new Array(16):null,e=null);var n=(e=e||{}).random||(e.rng||r)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t)for(var i=0;i<16;++i)t[o+i]=n[i];return t||a(n)}},9921:(e,t)=>{"use strict";var s="function"==typeof Symbol&&Symbol.for,r=s?Symbol.for("react.element"):60103,a=s?Symbol.for("react.portal"):60106,o=s?Symbol.for("react.fragment"):60107,n=s?Symbol.for("react.strict_mode"):60108,i=s?Symbol.for("react.profiler"):60114,l=s?Symbol.for("react.provider"):60109,c=s?Symbol.for("react.context"):60110,d=s?Symbol.for("react.async_mode"):60111,u=s?Symbol.for("react.concurrent_mode"):60111,p=s?Symbol.for("react.forward_ref"):60112,m=s?Symbol.for("react.suspense"):60113,f=s?Symbol.for("react.suspense_list"):60120,h=s?Symbol.for("react.memo"):60115,y=s?Symbol.for("react.lazy"):60116,_=s?Symbol.for("react.block"):60121,w=s?Symbol.for("react.fundamental"):60117,g=s?Symbol.for("react.responder"):60118,b=s?Symbol.for("react.scope"):60119;function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case d:case u:case o:case i:case n:case m:return e;default:switch(e=e&&e.$$typeof){case c:case p:case y:case h:case l:return e;default:return t}}case a:return t}}}function E(e){return v(e)===u}t.AsyncMode=d,t.ConcurrentMode=u,t.ContextConsumer=c,t.ContextProvider=l,t.Element=r,t.ForwardRef=p,t.Fragment=o,t.Lazy=y,t.Memo=h,t.Portal=a,t.Profiler=i,t.StrictMode=n,t.Suspense=m,t.isAsyncMode=function(e){return E(e)||v(e)===d},t.isConcurrentMode=E,t.isContextConsumer=function(e){return v(e)===c},t.isContextProvider=function(e){return v(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return v(e)===p},t.isFragment=function(e){return v(e)===o},t.isLazy=function(e){return v(e)===y},t.isMemo=function(e){return v(e)===h},t.isPortal=function(e){return v(e)===a},t.isProfiler=function(e){return v(e)===i},t.isStrictMode=function(e){return v(e)===n},t.isSuspense=function(e){return v(e)===m},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===u||e===i||e===n||e===m||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===h||e.$$typeof===l||e.$$typeof===c||e.$$typeof===p||e.$$typeof===w||e.$$typeof===g||e.$$typeof===b||e.$$typeof===_)},t.typeOf=v},9864:(e,t,s)=>{"use strict";e.exports=s(9921)},5251:(e,t,s)=>{"use strict";var r=s(9196);Symbol.for("react.element"),Symbol.for("react.fragment"),Object.prototype.hasOwnProperty,r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner},5893:(e,t,s)=>{"use strict";s(5251)},4633:e=>{function t(e,t){var s=e.length,r=new Array(s),a={},o=s,n=function(e){for(var t=new Map,s=0,r=e.length;s<r;s++){var a=e[s];t.has(a[0])||t.set(a[0],new Set),t.has(a[1])||t.set(a[1],new Set),t.get(a[0]).add(a[1])}return t}(t),i=function(e){for(var t=new Map,s=0,r=e.length;s<r;s++)t.set(e[s],s);return t}(e);for(t.forEach((function(e){if(!i.has(e[0])||!i.has(e[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")}));o--;)a[o]||l(e[o],o,new Set);return r;function l(e,t,o){if(o.has(e)){var c;try{c=", node was:"+JSON.stringify(e)}catch(e){c=""}throw new Error("Cyclic dependency"+c)}if(!i.has(e))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(e));if(!a[t]){a[t]=!0;var d=n.get(e)||new Set;if(t=(d=Array.from(d)).length){o.add(e);do{var u=d[--t];l(u,i.get(u),o)}while(t);o.delete(e)}r[--s]=e}}}e.exports=function(e){return t(function(e){for(var t=new Set,s=0,r=e.length;s<r;s++){var a=e[s];t.add(a[0]),t.add(a[1])}return Array.from(t)}(e),e)},e.exports.array=t},4530:(e,t)=>{var s;!function(){"use strict";var r={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var o=typeof s;if("string"===o||"number"===o)e.push(s);else if(Array.isArray(s)){if(s.length){var n=a.apply(null,s);n&&e.push(n)}}else if("object"===o){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)r.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(s=function(){return a}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React},5890:e=>{"use strict";e.exports=window.yoast.propTypes}},t={};function s(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,s),o.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=s(9196),t=s.n(e);const r=window.wp.components,a=window.wp.data,o=window.wp.domReady;var n=s.n(o);const i=window.wp.element,l=window.yoast.uiLibrary;var c=s(667),d=s.n(c),u=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===p}(e)}(e)},p="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function m(e,t){return!1!==t.clone&&t.isMergeableObject(e)?h((s=e,Array.isArray(s)?[]:{}),e,t):e;var s}function f(e,t,s){return e.concat(t).map((function(e){return m(e,s)}))}function h(e,t,s){(s=s||{}).arrayMerge=s.arrayMerge||f,s.isMergeableObject=s.isMergeableObject||u;var r=Array.isArray(t);return r===Array.isArray(e)?r?s.arrayMerge(e,t,s):function(e,t,s){var r={};return s.isMergeableObject(e)&&Object.keys(e).forEach((function(t){r[t]=m(e[t],s)})),Object.keys(t).forEach((function(a){s.isMergeableObject(t[a])&&e[a]?r[a]=h(e[a],t[a],s):r[a]=m(t[a],s)})),r}(e,t,s):m(t,s)}h.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,s){return h(e,s,t)}),{})};const y=h,_=window.lodash.isPlainObject;var w=s.n(_);const g=window.lodash.clone;var b=s.n(g);const v=window.lodash.toPath;var E=s.n(v);const x=function(e,t){};var k=s(6214),S=s.n(k);const L=window.lodash.cloneDeep;var T=s.n(L);function F(){return F=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},F.apply(this,arguments)}function $(e,t){if(null==e)return{};var s,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)s=o[r],t.indexOf(s)>=0||(a[s]=e[s]);return a}function P(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var R=function(e){return Array.isArray(e)&&0===e.length},N=function(e){return"function"==typeof e},O=function(e){return null!==e&&"object"==typeof e},C=function(e){return String(Math.floor(Number(e)))===e},A=function(e){return"[object String]"===Object.prototype.toString.call(e)},I=function(t){return 0===e.Children.count(t)},M=function(e){return O(e)&&N(e.then)};function D(e,t,s,r){void 0===r&&(r=0);for(var a=E()(t);e&&r<a.length;)e=e[a[r++]];return void 0===e?s:e}function B(e,t,s){for(var r=b()(e),a=r,o=0,n=E()(t);o<n.length-1;o++){var i=n[o],l=D(e,n.slice(0,o+1));if(l&&(O(l)||Array.isArray(l)))a=a[i]=b()(l);else{var c=n[o+1];a=a[i]=C(c)&&Number(c)>=0?[]:{}}}return(0===o?e:a)[n[o]]===s?e:(void 0===s?delete a[n[o]]:a[n[o]]=s,0===o&&void 0===s&&delete r[n[o]],r)}function U(e,t,s,r){void 0===s&&(s=new WeakMap),void 0===r&&(r={});for(var a=0,o=Object.keys(e);a<o.length;a++){var n=o[a],i=e[n];O(i)?s.get(i)||(s.set(i,!0),r[n]=Array.isArray(i)?[]:{},U(i,t,s,r[n])):r[n]=t}return r}var j=(0,e.createContext)(void 0);j.displayName="FormikContext";var V=j.Provider,z=j.Consumer;function q(){var t=(0,e.useContext)(j);return t||x(!1),t}function W(e,t){switch(t.type){case"SET_VALUES":return F({},e,{values:t.payload});case"SET_TOUCHED":return F({},e,{touched:t.payload});case"SET_ERRORS":return d()(e.errors,t.payload)?e:F({},e,{errors:t.payload});case"SET_STATUS":return F({},e,{status:t.payload});case"SET_ISSUBMITTING":return F({},e,{isSubmitting:t.payload});case"SET_ISVALIDATING":return F({},e,{isValidating:t.payload});case"SET_FIELD_VALUE":return F({},e,{values:B(e.values,t.payload.field,t.payload.value)});case"SET_FIELD_TOUCHED":return F({},e,{touched:B(e.touched,t.payload.field,t.payload.value)});case"SET_FIELD_ERROR":return F({},e,{errors:B(e.errors,t.payload.field,t.payload.value)});case"RESET_FORM":return F({},e,t.payload);case"SET_FORMIK_STATE":return t.payload(e);case"SUBMIT_ATTEMPT":return F({},e,{touched:U(e.values,!0),isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":case"SUBMIT_SUCCESS":return F({},e,{isSubmitting:!1});default:return e}}var H={},Y={};function G(t){var s=t.validateOnChange,r=void 0===s||s,a=t.validateOnBlur,o=void 0===a||a,n=t.validateOnMount,i=void 0!==n&&n,l=t.isInitialValid,c=t.enableReinitialize,u=void 0!==c&&c,p=t.onSubmit,m=$(t,["validateOnChange","validateOnBlur","validateOnMount","isInitialValid","enableReinitialize","onSubmit"]),f=F({validateOnChange:r,validateOnBlur:o,validateOnMount:i,onSubmit:p},m),h=(0,e.useRef)(f.initialValues),_=(0,e.useRef)(f.initialErrors||H),w=(0,e.useRef)(f.initialTouched||Y),g=(0,e.useRef)(f.initialStatus),b=(0,e.useRef)(!1),v=(0,e.useRef)({});(0,e.useEffect)((function(){return b.current=!0,function(){b.current=!1}}),[]);var E=(0,e.useReducer)(W,{values:f.initialValues,errors:f.initialErrors||H,touched:f.initialTouched||Y,status:f.initialStatus,isSubmitting:!1,isValidating:!1,submitCount:0}),x=E[0],k=E[1],S=(0,e.useCallback)((function(e,t){return new Promise((function(s,r){var a=f.validate(e,t);null==a?s(H):M(a)?a.then((function(e){s(e||H)}),(function(e){r(e)})):s(a)}))}),[f.validate]),L=(0,e.useCallback)((function(e,t){var s=f.validationSchema,r=N(s)?s(t):s,a=t&&r.validateAt?r.validateAt(t,e):function(e,t,s,r){void 0===s&&(s=!1),void 0===r&&(r={});var a=Z(e);return t[s?"validateSync":"validate"](a,{abortEarly:!1,context:r})}(e,r);return new Promise((function(e,t){a.then((function(){e(H)}),(function(s){"ValidationError"===s.name?e(function(e){var t={};if(e.inner){if(0===e.inner.length)return B(t,e.path,e.message);var s=e.inner,r=Array.isArray(s),a=0;for(s=r?s:s[Symbol.iterator]();;){var o;if(r){if(a>=s.length)break;o=s[a++]}else{if((a=s.next()).done)break;o=a.value}var n=o;D(t,n.path)||(t=B(t,n.path,n.message))}}return t}(s)):t(s)}))}))}),[f.validationSchema]),T=(0,e.useCallback)((function(e,t){return new Promise((function(s){return s(v.current[e].validate(t))}))}),[]),P=(0,e.useCallback)((function(e){var t=Object.keys(v.current).filter((function(e){return N(v.current[e].validate)})),s=t.length>0?t.map((function(t){return T(t,D(e,t))})):[Promise.resolve("DO_NOT_DELETE_YOU_WILL_BE_FIRED")];return Promise.all(s).then((function(e){return e.reduce((function(e,s,r){return"DO_NOT_DELETE_YOU_WILL_BE_FIRED"===s||s&&(e=B(e,t[r],s)),e}),{})}))}),[T]),R=(0,e.useCallback)((function(e){return Promise.all([P(e),f.validationSchema?L(e):{},f.validate?S(e):{}]).then((function(e){var t=e[0],s=e[1],r=e[2];return y.all([t,s,r],{arrayMerge:J})}))}),[f.validate,f.validationSchema,P,S,L]),C=X((function(e){return void 0===e&&(e=x.values),k({type:"SET_ISVALIDATING",payload:!0}),R(e).then((function(e){return b.current&&(k({type:"SET_ISVALIDATING",payload:!1}),k({type:"SET_ERRORS",payload:e})),e}))}));(0,e.useEffect)((function(){i&&!0===b.current&&d()(h.current,f.initialValues)&&C(h.current)}),[i,C]);var I=(0,e.useCallback)((function(e){var t=e&&e.values?e.values:h.current,s=e&&e.errors?e.errors:_.current?_.current:f.initialErrors||{},r=e&&e.touched?e.touched:w.current?w.current:f.initialTouched||{},a=e&&e.status?e.status:g.current?g.current:f.initialStatus;h.current=t,_.current=s,w.current=r,g.current=a;var o=function(){k({type:"RESET_FORM",payload:{isSubmitting:!!e&&!!e.isSubmitting,errors:s,touched:r,status:a,values:t,isValidating:!!e&&!!e.isValidating,submitCount:e&&e.submitCount&&"number"==typeof e.submitCount?e.submitCount:0}})};if(f.onReset){var n=f.onReset(x.values,de);M(n)?n.then(o):o()}else o()}),[f.initialErrors,f.initialStatus,f.initialTouched]);(0,e.useEffect)((function(){!0!==b.current||d()(h.current,f.initialValues)||(u&&(h.current=f.initialValues,I()),i&&C(h.current))}),[u,f.initialValues,I,i,C]),(0,e.useEffect)((function(){u&&!0===b.current&&!d()(_.current,f.initialErrors)&&(_.current=f.initialErrors||H,k({type:"SET_ERRORS",payload:f.initialErrors||H}))}),[u,f.initialErrors]),(0,e.useEffect)((function(){u&&!0===b.current&&!d()(w.current,f.initialTouched)&&(w.current=f.initialTouched||Y,k({type:"SET_TOUCHED",payload:f.initialTouched||Y}))}),[u,f.initialTouched]),(0,e.useEffect)((function(){u&&!0===b.current&&!d()(g.current,f.initialStatus)&&(g.current=f.initialStatus,k({type:"SET_STATUS",payload:f.initialStatus}))}),[u,f.initialStatus,f.initialTouched]);var U=X((function(e){if(v.current[e]&&N(v.current[e].validate)){var t=D(x.values,e),s=v.current[e].validate(t);return M(s)?(k({type:"SET_ISVALIDATING",payload:!0}),s.then((function(e){return e})).then((function(t){k({type:"SET_FIELD_ERROR",payload:{field:e,value:t}}),k({type:"SET_ISVALIDATING",payload:!1})}))):(k({type:"SET_FIELD_ERROR",payload:{field:e,value:s}}),Promise.resolve(s))}return f.validationSchema?(k({type:"SET_ISVALIDATING",payload:!0}),L(x.values,e).then((function(e){return e})).then((function(t){k({type:"SET_FIELD_ERROR",payload:{field:e,value:t[e]}}),k({type:"SET_ISVALIDATING",payload:!1})}))):Promise.resolve()})),j=(0,e.useCallback)((function(e,t){var s=t.validate;v.current[e]={validate:s}}),[]),V=(0,e.useCallback)((function(e){delete v.current[e]}),[]),z=X((function(e,t){return k({type:"SET_TOUCHED",payload:e}),(void 0===t?o:t)?C(x.values):Promise.resolve()})),q=(0,e.useCallback)((function(e){k({type:"SET_ERRORS",payload:e})}),[]),G=X((function(e,t){var s=N(e)?e(x.values):e;return k({type:"SET_VALUES",payload:s}),(void 0===t?r:t)?C(s):Promise.resolve()})),K=(0,e.useCallback)((function(e,t){k({type:"SET_FIELD_ERROR",payload:{field:e,value:t}})}),[]),Q=X((function(e,t,s){return k({type:"SET_FIELD_VALUE",payload:{field:e,value:t}}),(void 0===s?r:s)?C(B(x.values,e,t)):Promise.resolve()})),ee=(0,e.useCallback)((function(e,t){var s,r=t,a=e;if(!A(e)){e.persist&&e.persist();var o=e.target?e.target:e.currentTarget,n=o.type,i=o.name,l=o.id,c=o.value,d=o.checked,u=(o.outerHTML,o.options),p=o.multiple;r=t||i||l,a=/number|range/.test(n)?(s=parseFloat(c),isNaN(s)?"":s):/checkbox/.test(n)?function(e,t,s){if("boolean"==typeof e)return Boolean(t);var r=[],a=!1,o=-1;if(Array.isArray(e))r=e,a=(o=e.indexOf(s))>=0;else if(!s||"true"==s||"false"==s)return Boolean(t);return t&&s&&!a?r.concat(s):a?r.slice(0,o).concat(r.slice(o+1)):r}(D(x.values,r),d,c):u&&p?function(e){return Array.from(e).filter((function(e){return e.selected})).map((function(e){return e.value}))}(u):c}r&&Q(r,a)}),[Q,x.values]),te=X((function(e){if(A(e))return function(t){return ee(t,e)};ee(e)})),se=X((function(e,t,s){return void 0===t&&(t=!0),k({type:"SET_FIELD_TOUCHED",payload:{field:e,value:t}}),(void 0===s?o:s)?C(x.values):Promise.resolve()})),re=(0,e.useCallback)((function(e,t){e.persist&&e.persist();var s=e.target,r=s.name,a=s.id,o=(s.outerHTML,t||r||a);se(o,!0)}),[se]),ae=X((function(e){if(A(e))return function(t){return re(t,e)};re(e)})),oe=(0,e.useCallback)((function(e){N(e)?k({type:"SET_FORMIK_STATE",payload:e}):k({type:"SET_FORMIK_STATE",payload:function(){return e}})}),[]),ne=(0,e.useCallback)((function(e){k({type:"SET_STATUS",payload:e})}),[]),ie=(0,e.useCallback)((function(e){k({type:"SET_ISSUBMITTING",payload:e})}),[]),le=X((function(){return k({type:"SUBMIT_ATTEMPT"}),C().then((function(e){var t=e instanceof Error;if(!t&&0===Object.keys(e).length){var s;try{if(void 0===(s=ue()))return}catch(e){throw e}return Promise.resolve(s).then((function(e){return b.current&&k({type:"SUBMIT_SUCCESS"}),e})).catch((function(e){if(b.current)throw k({type:"SUBMIT_FAILURE"}),e}))}if(b.current&&(k({type:"SUBMIT_FAILURE"}),t))throw e}))})),ce=X((function(e){e&&e.preventDefault&&N(e.preventDefault)&&e.preventDefault(),e&&e.stopPropagation&&N(e.stopPropagation)&&e.stopPropagation(),le().catch((function(e){console.warn("Warning: An unhandled error was caught from submitForm()",e)}))})),de={resetForm:I,validateForm:C,validateField:U,setErrors:q,setFieldError:K,setFieldTouched:se,setFieldValue:Q,setStatus:ne,setSubmitting:ie,setTouched:z,setValues:G,setFormikState:oe,submitForm:le},ue=X((function(){return p(x.values,de)})),pe=X((function(e){e&&e.preventDefault&&N(e.preventDefault)&&e.preventDefault(),e&&e.stopPropagation&&N(e.stopPropagation)&&e.stopPropagation(),I()})),me=(0,e.useCallback)((function(e){return{value:D(x.values,e),error:D(x.errors,e),touched:!!D(x.touched,e),initialValue:D(h.current,e),initialTouched:!!D(w.current,e),initialError:D(_.current,e)}}),[x.errors,x.touched,x.values]),fe=(0,e.useCallback)((function(e){return{setValue:function(t,s){return Q(e,t,s)},setTouched:function(t,s){return se(e,t,s)},setError:function(t){return K(e,t)}}}),[Q,se,K]),he=(0,e.useCallback)((function(e){var t=O(e),s=t?e.name:e,r=D(x.values,s),a={name:s,value:r,onChange:te,onBlur:ae};if(t){var o=e.type,n=e.value,i=e.as,l=e.multiple;"checkbox"===o?void 0===n?a.checked=!!r:(a.checked=!(!Array.isArray(r)||!~r.indexOf(n)),a.value=n):"radio"===o?(a.checked=r===n,a.value=n):"select"===i&&l&&(a.value=a.value||[],a.multiple=!0)}return a}),[ae,te,x.values]),ye=(0,e.useMemo)((function(){return!d()(h.current,x.values)}),[h.current,x.values]),_e=(0,e.useMemo)((function(){return void 0!==l?ye?x.errors&&0===Object.keys(x.errors).length:!1!==l&&N(l)?l(f):l:x.errors&&0===Object.keys(x.errors).length}),[l,ye,x.errors,f]);return F({},x,{initialValues:h.current,initialErrors:_.current,initialTouched:w.current,initialStatus:g.current,handleBlur:ae,handleChange:te,handleReset:pe,handleSubmit:ce,resetForm:I,setErrors:q,setFormikState:oe,setFieldTouched:se,setFieldValue:Q,setFieldError:K,setStatus:ne,setSubmitting:ie,setTouched:z,setValues:G,submitForm:le,validateForm:C,validateField:U,isValid:_e,dirty:ye,unregisterField:V,registerField:j,getFieldProps:he,getFieldMeta:me,getFieldHelpers:fe,validateOnBlur:o,validateOnChange:r,validateOnMount:i})}function K(t){var s=G(t),r=t.component,a=t.children,o=t.render,n=t.innerRef;return(0,e.useImperativeHandle)(n,(function(){return s})),(0,e.createElement)(V,{value:s},r?(0,e.createElement)(r,s):o?o(s):a?N(a)?a(s):I(a)?null:e.Children.only(a):null)}function Z(e){var t=Array.isArray(e)?[]:{};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var r=String(s);!0===Array.isArray(e[r])?t[r]=e[r].map((function(e){return!0===Array.isArray(e)||w()(e)?Z(e):""!==e?e:void 0})):w()(e[r])?t[r]=Z(e[r]):t[r]=""!==e[r]?e[r]:void 0}return t}function J(e,t,s){var r=e.slice();return t.forEach((function(t,a){if(void 0===r[a]){var o=!1!==s.clone&&s.isMergeableObject(t);r[a]=o?y(Array.isArray(t)?[]:{},t,s):t}else s.isMergeableObject(t)?r[a]=y(e[a],t,s):-1===e.indexOf(t)&&r.push(t)})),r}var Q="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?e.useLayoutEffect:e.useEffect;function X(t){var s=(0,e.useRef)(t);return Q((function(){s.current=t})),(0,e.useCallback)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return s.current.apply(void 0,t)}),[])}function ee(t){var s=q(),r=s.getFieldProps,a=s.getFieldMeta,o=s.getFieldHelpers,n=s.registerField,i=s.unregisterField,l=O(t)?t:{name:t},c=l.name,d=l.validate;return(0,e.useEffect)((function(){return c&&n(c,{validate:d}),function(){c&&i(c)}}),[n,i,c,d]),c||x(!1),[r(l),a(c),o(c)]}function te(t){var s=t.validate,r=t.name,a=t.render,o=t.children,n=t.as,i=t.component,l=$(t,["validate","name","render","children","as","component"]),c=$(q(),["validate","validationSchema"]),d=c.registerField,u=c.unregisterField;(0,e.useEffect)((function(){return d(r,{validate:s}),function(){u(r)}}),[d,u,r,s]);var p=c.getFieldProps(F({name:r},l)),m=c.getFieldMeta(r),f={field:p,form:c};if(a)return a(F({},f,{meta:m}));if(N(o))return o(F({},f,{meta:m}));if(i){if("string"==typeof i){var h=l.innerRef,y=$(l,["innerRef"]);return(0,e.createElement)(i,F({ref:h},p,y),o)}return(0,e.createElement)(i,F({field:p,form:c},l),o)}var _=n||"input";if("string"==typeof _){var w=l.innerRef,g=$(l,["innerRef"]);return(0,e.createElement)(_,F({ref:w},p,g),o)}return(0,e.createElement)(_,F({},p,l),o)}var se=(0,e.forwardRef)((function(t,s){var r=t.action,a=$(t,["action"]),o=null!=r?r:"#",n=q(),i=n.handleReset,l=n.handleSubmit;return(0,e.createElement)("form",Object.assign({onSubmit:l,ref:s,onReset:i,action:o},a))}));function re(t){var s=function(s){return(0,e.createElement)(z,null,(function(r){return r||x(!1),(0,e.createElement)(t,Object.assign({},s,{formik:r}))}))},r=t.displayName||t.name||t.constructor&&t.constructor.name||"Component";return s.WrappedComponent=t,s.displayName="FormikConnect("+r+")",S()(s,t)}se.displayName="Form";var ae=function(e,t,s){var r=oe(e);return r.splice(t,0,s),r},oe=function(e){if(e){if(Array.isArray(e))return[].concat(e);var t=Object.keys(e).map((function(e){return parseInt(e)})).reduce((function(e,t){return t>e?t:e}),0);return Array.from(F({},e,{length:t+1}))}return[]},ne=function(t){function s(e){var s;return(s=t.call(this,e)||this).updateArrayField=function(e,t,r){var a=s.props,o=a.name;(0,a.formik.setFormikState)((function(s){var a="function"==typeof r?r:e,n="function"==typeof t?t:e,i=B(s.values,o,e(D(s.values,o))),l=r?a(D(s.errors,o)):void 0,c=t?n(D(s.touched,o)):void 0;return R(l)&&(l=void 0),R(c)&&(c=void 0),F({},s,{values:i,errors:r?B(s.errors,o,l):s.errors,touched:t?B(s.touched,o,c):s.touched})}))},s.push=function(e){return s.updateArrayField((function(t){return[].concat(oe(t),[T()(e)])}),!1,!1)},s.handlePush=function(e){return function(){return s.push(e)}},s.swap=function(e,t){return s.updateArrayField((function(s){return function(e,t,s){var r=oe(e),a=r[t];return r[t]=r[s],r[s]=a,r}(s,e,t)}),!0,!0)},s.handleSwap=function(e,t){return function(){return s.swap(e,t)}},s.move=function(e,t){return s.updateArrayField((function(s){return function(e,t,s){var r=oe(e),a=r[t];return r.splice(t,1),r.splice(s,0,a),r}(s,e,t)}),!0,!0)},s.handleMove=function(e,t){return function(){return s.move(e,t)}},s.insert=function(e,t){return s.updateArrayField((function(s){return ae(s,e,t)}),(function(t){return ae(t,e,null)}),(function(t){return ae(t,e,null)}))},s.handleInsert=function(e,t){return function(){return s.insert(e,t)}},s.replace=function(e,t){return s.updateArrayField((function(s){return function(e,t,s){var r=oe(e);return r[t]=s,r}(s,e,t)}),!1,!1)},s.handleReplace=function(e,t){return function(){return s.replace(e,t)}},s.unshift=function(e){var t=-1;return s.updateArrayField((function(s){var r=s?[e].concat(s):[e];return t<0&&(t=r.length),r}),(function(e){var s=e?[null].concat(e):[null];return t<0&&(t=s.length),s}),(function(e){var s=e?[null].concat(e):[null];return t<0&&(t=s.length),s})),t},s.handleUnshift=function(e){return function(){return s.unshift(e)}},s.handleRemove=function(e){return function(){return s.remove(e)}},s.handlePop=function(){return function(){return s.pop()}},s.remove=s.remove.bind(P(s)),s.pop=s.pop.bind(P(s)),s}var r,a;a=t,(r=s).prototype=Object.create(a.prototype),r.prototype.constructor=r,r.__proto__=a;var o=s.prototype;return o.componentDidUpdate=function(e){this.props.validateOnChange&&this.props.formik.validateOnChange&&!d()(D(e.formik.values,e.name),D(this.props.formik.values,this.props.name))&&this.props.formik.validateForm(this.props.formik.values)},o.remove=function(e){var t;return this.updateArrayField((function(s){var r=s?oe(s):[];return t||(t=r[e]),N(r.splice)&&r.splice(e,1),r}),!0,!0),t},o.pop=function(){var e;return this.updateArrayField((function(t){var s=t;return e||(e=s&&s.pop&&s.pop()),s}),!0,!0),e},o.render=function(){var t={push:this.push,pop:this.pop,swap:this.swap,move:this.move,insert:this.insert,replace:this.replace,unshift:this.unshift,remove:this.remove,handlePush:this.handlePush,handlePop:this.handlePop,handleSwap:this.handleSwap,handleMove:this.handleMove,handleInsert:this.handleInsert,handleReplace:this.handleReplace,handleUnshift:this.handleUnshift,handleRemove:this.handleRemove},s=this.props,r=s.component,a=s.render,o=s.children,n=s.name,i=F({},t,{form:$(s.formik,["validate","validationSchema"]),name:n});return r?(0,e.createElement)(r,i):a?a(i):o?"function"==typeof o?o(i):I(o)?null:e.Children.only(o):null},s}(e.Component);ne.defaultProps={validateOnChange:!0};var ie=re(ne);const le=window.lodash,ce=window.ReactDOM;function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},de.apply(this,arguments)}var ue;!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(ue||(ue={}));const pe="popstate";function me(e,t){if(!1===e||null==e)throw new Error(t)}function fe(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function he(e,t){return{usr:e.state,key:e.key,idx:t}}function ye(e,t,s,r){return void 0===s&&(s=null),de({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?we(t):t,{state:s,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function _e(e){let{pathname:t="/",search:s="",hash:r=""}=e;return s&&"?"!==s&&(t+="?"===s.charAt(0)?s:"?"+s),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function we(e){let t={};if(e){let s=e.indexOf("#");s>=0&&(t.hash=e.substr(s),e=e.substr(0,s));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var ge;function be(e,t,s){return void 0===s&&(s="/"),function(e,t,s,r){let a=Ce(("string"==typeof t?we(t):t).pathname||"/",s);if(null==a)return null;let o=ve(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let s=e.length===t.length&&e.slice(0,-1).every(((e,s)=>e===t[s]));return s?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let n=null;for(let e=0;null==n&&e<o.length;++e){let t=Oe(a);n=Re(o[e],t,r)}return n}(e,t,s,!1)}function ve(e,t,s,r){void 0===t&&(t=[]),void 0===s&&(s=[]),void 0===r&&(r="");let a=(e,a,o)=>{let n={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};n.relativePath.startsWith("/")&&(me(n.relativePath.startsWith(r),'Absolute route path "'+n.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),n.relativePath=n.relativePath.slice(r.length));let i=De([r,n.relativePath]),l=s.concat(n);e.children&&e.children.length>0&&(me(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),ve(e.children,t,l,i)),(null!=e.path||e.index)&&t.push({path:i,score:Pe(i,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var s;if(""!==e.path&&null!=(s=e.path)&&s.includes("?"))for(let s of Ee(e.path))a(e,t,s);else a(e,t)})),t}function Ee(e){let t=e.split("/");if(0===t.length)return[];let[s,...r]=t,a=s.endsWith("?"),o=s.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let n=Ee(r.join("/")),i=[];return i.push(...n.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...n),i.map((t=>e.startsWith("/")&&""===t?"/":t))}!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(ge||(ge={})),new Set(["lazy","caseSensitive","path","id","index","children"]);const xe=/^:[\w-]+$/,ke=3,Se=2,Le=1,Te=10,Fe=-2,$e=e=>"*"===e;function Pe(e,t){let s=e.split("/"),r=s.length;return s.some($e)&&(r+=Fe),t&&(r+=Se),s.filter((e=>!$e(e))).reduce(((e,t)=>e+(xe.test(t)?ke:""===t?Le:Te)),r)}function Re(e,t,s){void 0===s&&(s=!1);let{routesMeta:r}=e,a={},o="/",n=[];for(let e=0;e<r.length;++e){let i=r[e],l=e===r.length-1,c="/"===o?t:t.slice(o.length)||"/",d=Ne({path:i.relativePath,caseSensitive:i.caseSensitive,end:l},c),u=i.route;if(!d&&l&&s&&!r[r.length-1].route.index&&(d=Ne({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},c)),!d)return null;Object.assign(a,d.params),n.push({params:a,pathname:De([o,d.pathname]),pathnameBase:Be(De([o,d.pathnameBase])),route:u}),"/"!==d.pathnameBase&&(o=De([o,d.pathnameBase]))}return n}function Ne(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[s,r]=function(e,t,s){void 0===t&&(t=!1),void 0===s&&(s=!0),fe("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,s)=>(r.push({paramName:t,isOptional:null!=s}),s?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}(e.path,e.caseSensitive,e.end),a=t.match(s);if(!a)return null;let o=a[0],n=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce(((e,t,s)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[s]||"";n=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=i[s];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:n,pattern:e}}function Oe(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return fe(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Ce(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let s=t.endsWith("/")?t.length-1:t.length,r=e.charAt(s);return r&&"/"!==r?null:e.slice(s)||"/"}function Ae(e,t,s,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+s+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Ie(e,t){let s=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?s.map(((e,t)=>t===s.length-1?e.pathname:e.pathnameBase)):s.map((e=>e.pathnameBase))}function Me(e,t,s,r){let a;void 0===r&&(r=!1),"string"==typeof e?a=we(e):(a=de({},e),me(!a.pathname||!a.pathname.includes("?"),Ae("?","pathname","search",a)),me(!a.pathname||!a.pathname.includes("#"),Ae("#","pathname","hash",a)),me(!a.search||!a.search.includes("#"),Ae("#","search","hash",a)));let o,n=""===e||""===a.pathname,i=n?"/":a.pathname;if(null==i)o=s;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:s,search:r="",hash:a=""}="string"==typeof e?we(e):e,o=s?s.startsWith("/")?s:function(e,t){let s=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?s.length>1&&s.pop():"."!==e&&s.push(e)})),s.length>1?s.join("/"):"/"}(s,t):t;return{pathname:o,search:Ue(r),hash:je(a)}}(a,o),c=i&&"/"!==i&&i.endsWith("/"),d=(n||"."===i)&&s.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const De=e=>e.join("/").replace(/\/\/+/g,"/"),Be=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ue=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",je=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;const Ve=["post","put","patch","delete"],ze=(new Set(Ve),["get",...Ve]);function qe(){return qe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},qe.apply(this,arguments)}new Set(ze),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred");const We=e.createContext(null),He=e.createContext(null),Ye=e.createContext(null),Ge=e.createContext(null),Ke=e.createContext({outlet:null,matches:[],isDataRoute:!1}),Ze=e.createContext(null);function Je(){return null!=e.useContext(Ge)}function Qe(){return Je()||me(!1),e.useContext(Ge).location}function Xe(t){e.useContext(Ye).static||e.useLayoutEffect(t)}function et(){let{isDataRoute:t}=e.useContext(Ke);return t?function(){let{router:t}=function(t){let s=e.useContext(We);return s||me(!1),s}(it.UseNavigateStable),s=ct(lt.UseNavigateStable),r=e.useRef(!1);return Xe((()=>{r.current=!0})),e.useCallback((function(e,a){void 0===a&&(a={}),r.current&&("number"==typeof e?t.navigate(e):t.navigate(e,qe({fromRouteId:s},a)))}),[t,s])}():function(){Je()||me(!1);let t=e.useContext(We),{basename:s,future:r,navigator:a}=e.useContext(Ye),{matches:o}=e.useContext(Ke),{pathname:n}=Qe(),i=JSON.stringify(Ie(o,r.v7_relativeSplatPath)),l=e.useRef(!1);return Xe((()=>{l.current=!0})),e.useCallback((function(e,r){if(void 0===r&&(r={}),!l.current)return;if("number"==typeof e)return void a.go(e);let o=Me(e,JSON.parse(i),n,"path"===r.relative);null==t&&"/"!==s&&(o.pathname="/"===o.pathname?s:De([s,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)}),[s,a,i,n,t])}()}function tt(t,s){let{relative:r}=void 0===s?{}:s,{future:a}=e.useContext(Ye),{matches:o}=e.useContext(Ke),{pathname:n}=Qe(),i=JSON.stringify(Ie(o,a.v7_relativeSplatPath));return e.useMemo((()=>Me(t,JSON.parse(i),n,"path"===r)),[t,i,n,r])}function st(t,s,r,a){Je()||me(!1);let{navigator:o}=e.useContext(Ye),{matches:n}=e.useContext(Ke),i=n[n.length-1],l=i?i.params:{},c=(i&&i.pathname,i?i.pathnameBase:"/");i&&i.route;let d,u=Qe();if(s){var p;let e="string"==typeof s?we(s):s;"/"===c||(null==(p=e.pathname)?void 0:p.startsWith(c))||me(!1),d=e}else d=u;let m=d.pathname||"/",f=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");f="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let h=be(t,{pathname:f}),y=function(t,s,r,a){var o;if(void 0===s&&(s=[]),void 0===r&&(r=null),void 0===a&&(a=null),null==t){var n;if(!r)return null;if(r.errors)t=r.matches;else{if(!(null!=(n=a)&&n.v7_partialHydration&&0===s.length&&!r.initialized&&r.matches.length>0))return null;t=r.matches}}let i=t,l=null==(o=r)?void 0:o.errors;if(null!=l){let e=i.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||me(!1),i=i.slice(0,Math.min(i.length,e+1))}let c=!1,d=-1;if(r&&a&&a.v7_partialHydration)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(d=e),t.route.id){let{loaderData:e,errors:s}=r,a=t.route.loader&&void 0===e[t.route.id]&&(!s||void 0===s[t.route.id]);if(t.route.lazy||a){c=!0,i=d>=0?i.slice(0,d+1):[i[0]];break}}}return i.reduceRight(((t,a,o)=>{let n,u=!1,p=null,m=null;var f;r&&(n=l&&a.route.id?l[a.route.id]:void 0,p=a.route.errorElement||at,c&&(d<0&&0===o?(dt[f="route-fallback"]||(dt[f]=!0),u=!0,m=null):d===o&&(u=!0,m=a.route.hydrateFallbackElement||null)));let h=s.concat(i.slice(0,o+1)),y=()=>{let s;return s=n?p:u?m:a.route.Component?e.createElement(a.route.Component,null):a.route.element?a.route.element:t,e.createElement(nt,{match:a,routeContext:{outlet:t,matches:h,isDataRoute:null!=r},children:s})};return r&&(a.route.ErrorBoundary||a.route.errorElement||0===o)?e.createElement(ot,{location:r.location,revalidation:r.revalidation,component:p,error:n,children:y(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):y()}),null)}(h&&h.map((e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:De([c,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:De([c,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),n,r,a);return s&&y?e.createElement(Ge.Provider,{value:{location:qe({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:ue.Pop}},y):y}function rt(){let t=function(){var t;let s=e.useContext(Ze),r=function(t){let s=e.useContext(He);return s||me(!1),s}(lt.UseRouteError),a=ct(lt.UseRouteError);return void 0!==s?s:null==(t=r.errors)?void 0:t[a]}(),s=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),r=t instanceof Error?t.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},s),r?e.createElement("pre",{style:a},r):null,null)}const at=e.createElement(rt,null);class ot extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?e.createElement(Ke.Provider,{value:this.props.routeContext},e.createElement(Ze.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function nt(t){let{routeContext:s,match:r,children:a}=t,o=e.useContext(We);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),e.createElement(Ke.Provider,{value:s},a)}var it=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(it||{}),lt=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(lt||{});function ct(t){let s=function(t){let s=e.useContext(Ke);return s||me(!1),s}(),r=s.matches[s.matches.length-1];return r.route.id||me(!1),r.route.id}const dt={};function ut(t){let{to:s,replace:r,state:a,relative:o}=t;Je()||me(!1);let{future:n,static:i}=e.useContext(Ye),{matches:l}=e.useContext(Ke),{pathname:c}=Qe(),d=et(),u=Me(s,Ie(l,n.v7_relativeSplatPath),c,"path"===o),p=JSON.stringify(u);return e.useEffect((()=>d(JSON.parse(p),{replace:r,state:a,relative:o})),[d,p,o,r,a]),null}function pt(e){me(!1)}function mt(t){let{basename:s="/",children:r=null,location:a,navigationType:o=ue.Pop,navigator:n,static:i=!1,future:l}=t;Je()&&me(!1);let c=s.replace(/^\/*/,"/"),d=e.useMemo((()=>({basename:c,navigator:n,static:i,future:qe({v7_relativeSplatPath:!1},l)})),[c,l,n,i]);"string"==typeof a&&(a=we(a));let{pathname:u="/",search:p="",hash:m="",state:f=null,key:h="default"}=a,y=e.useMemo((()=>{let e=Ce(u,c);return null==e?null:{location:{pathname:e,search:p,hash:m,state:f,key:h},navigationType:o}}),[c,u,p,m,f,h,o]);return null==y?null:e.createElement(Ye.Provider,{value:d},e.createElement(Ge.Provider,{children:r,value:y}))}function ft(e){let{children:t,location:s}=e;return st(ht(t),s)}function ht(t,s){void 0===s&&(s=[]);let r=[];return e.Children.forEach(t,((t,a)=>{if(!e.isValidElement(t))return;let o=[...s,a];if(t.type===e.Fragment)return void r.push.apply(r,ht(t.props.children,o));t.type!==pt&&me(!1),t.props.index&&t.props.children&&me(!1);let n={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(n.children=ht(t.props.children,o)),r.push(n)})),r}function yt(){return yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},yt.apply(this,arguments)}e.startTransition,new Promise((()=>{})),e.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const _t=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","unstable_viewTransition"];try{window.__reactRouterVersion="6"}catch(cs){}new Map;const wt=e.startTransition;function gt(t){let{basename:s,children:r,future:a,window:o}=t,n=e.useRef();var i;null==n.current&&(n.current=(void 0===(i={window:o,v5Compat:!0})&&(i={}),function(e,t,s,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,n=a.history,i=ue.Pop,l=null,c=d();function d(){return(n.state||{idx:null}).idx}function u(){i=ue.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:i,location:m.location,delta:t})}function p(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,s="string"==typeof e?e:_e(e);return s=s.replace(/ $/,"%20"),me(t,"No window.location.(origin|href) available to create URL for href: "+s),new URL(s,t)}null==c&&(c=0,n.replaceState(de({},n.state,{idx:c}),""));let m={get action(){return i},get location(){return e(a,n)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(pe,u),l=e,()=>{a.removeEventListener(pe,u),l=null}},createHref:e=>t(a,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i=ue.Push;let r=ye(m.location,e,t);s&&s(r,e),c=d()+1;let u=he(r,c),p=m.createHref(r);try{n.pushState(u,"",p)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;a.location.assign(p)}o&&l&&l({action:i,location:m.location,delta:1})},replace:function(e,t){i=ue.Replace;let r=ye(m.location,e,t);s&&s(r,e),c=d();let a=he(r,c),u=m.createHref(r);n.replaceState(a,"",u),o&&l&&l({action:i,location:m.location,delta:0})},go:e=>n.go(e)};return m}((function(e,t){let{pathname:s="/",search:r="",hash:a=""}=we(e.location.hash.substr(1));return s.startsWith("/")||s.startsWith(".")||(s="/"+s),ye("",{pathname:s,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let s=e.document.querySelector("base"),r="";if(s&&s.getAttribute("href")){let t=e.location.href,s=t.indexOf("#");r=-1===s?t:t.slice(0,s)}return r+"#"+("string"==typeof t?t:_e(t))}),(function(e,t){fe("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")}),i)));let l=n.current,[c,d]=e.useState({action:l.action,location:l.location}),{v7_startTransition:u}=a||{},p=e.useCallback((e=>{u&&wt?wt((()=>d(e))):d(e)}),[d,u]);return e.useLayoutEffect((()=>l.listen(p)),[l,p]),e.createElement(mt,{basename:s,children:r,location:c.location,navigationType:c.action,navigator:l,future:a})}ce.flushSync,e.useId;const bt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,vt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Et=e.forwardRef((function(t,s){let r,{onClick:a,relative:o,reloadDocument:n,replace:i,state:l,target:c,to:d,preventScrollReset:u,unstable_viewTransition:p}=t,m=function(e,t){if(null==e)return{};var s,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)s=o[r],t.indexOf(s)>=0||(a[s]=e[s]);return a}(t,_t),{basename:f}=e.useContext(Ye),h=!1;if("string"==typeof d&&vt.test(d)&&(r=d,bt))try{let e=new URL(window.location.href),t=d.startsWith("//")?new URL(e.protocol+d):new URL(d),s=Ce(t.pathname,f);t.origin===e.origin&&null!=s?d=s+t.search+t.hash:h=!0}catch(e){}let y=function(t,s){let{relative:r}=void 0===s?{}:s;Je()||me(!1);let{basename:a,navigator:o}=e.useContext(Ye),{hash:n,pathname:i,search:l}=tt(t,{relative:r}),c=i;return"/"!==a&&(c="/"===i?a:De([a,i])),o.createHref({pathname:c,search:l,hash:n})}(d,{relative:o}),_=function(t,s){let{target:r,replace:a,state:o,preventScrollReset:n,relative:i,unstable_viewTransition:l}=void 0===s?{}:s,c=et(),d=Qe(),u=tt(t,{relative:i});return e.useCallback((e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,r)){e.preventDefault();let s=void 0!==a?a:_e(d)===_e(u);c(t,{replace:s,state:o,preventScrollReset:n,relative:i,unstable_viewTransition:l})}}),[d,c,u,a,o,r,t,n,i,l])}(d,{replace:i,state:l,target:c,preventScrollReset:u,relative:o,unstable_viewTransition:p});return e.createElement("a",yt({},m,{href:r||y,onClick:h||n?a:function(e){a&&a(e),e.defaultPrevented||_(e)},ref:s,target:c}))}));var xt,kt;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(xt||(xt={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(kt||(kt={}));const St=window.yoast.styledComponents,Lt=window.wp.i18n,Tt=window.yoast.reduxJsToolkit,Ft="adminUrl",$t=(0,Tt.createSlice)({name:Ft,initialState:"",reducers:{setAdminUrl:(e,{payload:t})=>t}}),Pt=($t.getInitialState,{selectAdminUrl:e=>(0,le.get)(e,Ft,"")});Pt.selectAdminLink=(0,Tt.createSelector)([Pt.selectAdminUrl,(e,t)=>t],((e,t="")=>{try{return new URL(t,e).href}catch(t){return e}})),$t.actions,$t.reducer;const Rt=window.wp.url,Nt="linkParams",Ot=(0,Tt.createSlice)({name:Nt,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),Ct=Ot.getInitialState,At={selectLinkParam:(e,t,s={})=>(0,le.get)(e,`${Nt}.${t}`,s),selectLinkParams:e=>(0,le.get)(e,Nt,{})};At.selectLink=(0,Tt.createSelector)([At.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,Rt.addQueryArgs)(t,{...e,...s})));const It=Ot.actions,Mt=Ot.reducer,Dt="notifications",Bt=(0,Tt.createSlice)({name:Dt,initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:r,description:a})=>({payload:{id:e||(0,Tt.nanoid)(),variant:t,size:s,title:r||"",description:a}})},removeNotification:(e,{payload:t})=>(0,le.omit)(e,t)}}),Ut=Bt.getInitialState,jt={selectNotifications:e=>(0,le.get)(e,Dt,{}),selectNotification:(e,t)=>(0,le.get)(e,[Dt,t],null)},Vt=Bt.actions,zt=Bt.reducer,qt="pluginUrl",Wt=(0,Tt.createSlice)({name:qt,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),Ht=(Wt.getInitialState,{selectPluginUrl:e=>(0,le.get)(e,qt,"")});Ht.selectImageLink=(0,Tt.createSelector)([Ht.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,le.trimEnd)(e,"/"),(0,le.trim)(t,"/"),(0,le.trimStart)(s,"/")].join("/"))),Wt.actions,Wt.reducer;const Yt=window.wp.apiFetch;var Gt=s.n(Yt);const Kt="request",Zt="success",Jt="error",Qt="idle",Xt="loading",es="success",ts="error",ss="showPlay",rs="askPermission",as="isPlaying",os="wistiaEmbedPermission",ns=(0,Tt.createSlice)({name:os,initialState:{value:!1,status:Qt,error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${os}/${Kt}`,(e=>{e.status=Xt})),e.addCase(`${os}/${Zt}`,((e,{payload:t})=>{e.status=es,e.value=Boolean(t&&t.value)})),e.addCase(`${os}/${Jt}`,((e,{payload:t})=>{e.status=ts,e.value=Boolean(t&&t.value),e.error={code:(0,le.get)(t,"error.code",500),message:(0,le.get)(t,"error.message","Unknown")}}))}});function is(...e){return e.filter(Boolean).join(" ")}function ls(e,t,...s){if(e in t){let r=t[e];return"function"==typeof r?r(...s):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,ls),r}ns.getInitialState,ns.actions,ns.reducer;var cs,ds,us=((ds=us||{})[ds.None=0]="None",ds[ds.RenderStrategy=1]="RenderStrategy",ds[ds.Static=2]="Static",ds),ps=((cs=ps||{})[cs.Unmount=0]="Unmount",cs[cs.Hidden=1]="Hidden",cs);function ms({ourProps:e,theirProps:t,slot:s,defaultTag:r,features:a,visible:o=!0,name:n}){let i=hs(t,e);if(o)return fs(i,s,r,n);let l=null!=a?a:0;if(2&l){let{static:e=!1,...t}=i;if(e)return fs(t,s,r,n)}if(1&l){let{unmount:e=!0,...t}=i;return ls(e?0:1,{0:()=>null,1:()=>fs({...t,hidden:!0,style:{display:"none"}},s,r,n)})}return fs(i,s,r,n)}function fs(t,s={},r,a){var o;let{as:n=r,children:i,refName:l="ref",...c}=ws(t,["unmount","static"]),d=void 0!==t.ref?{[l]:t.ref}:{},u="function"==typeof i?i(s):i;c.className&&"function"==typeof c.className&&(c.className=c.className(s));let p={};if(s){let e=!1,t=[];for(let[r,a]of Object.entries(s))"boolean"==typeof a&&(e=!0),!0===a&&t.push(r);e&&(p["data-headlessui-state"]=t.join(" "))}if(n===e.Fragment&&Object.keys(_s(c)).length>0){if(!(0,e.isValidElement)(u)||Array.isArray(u)&&u.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${a} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"));let t=is(null==(o=u.props)?void 0:o.className,c.className),s=t?{className:t}:{};return(0,e.cloneElement)(u,Object.assign({},hs(u.props,_s(ws(c,["ref"]))),p,d,function(...e){return{ref:e.every((e=>null==e))?void 0:t=>{for(let s of e)null!=s&&("function"==typeof s?s(t):s.current=t)}}}(u.ref,d.ref),s))}return(0,e.createElement)(n,Object.assign({},ws(c,["ref"]),n!==e.Fragment&&d,n!==e.Fragment&&p),u)}function hs(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},s={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=s[e]||(s[e]=[]),s[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(s).map((e=>[e,void 0]))));for(let e in s)Object.assign(t,{[e](t,...r){let a=s[e];for(let e of a){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;e(t,...r)}}});return t}function ys(t){var s;return Object.assign((0,e.forwardRef)(t),{displayName:null!=(s=t.displayName)?s:t.name})}function _s(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function ws(e,t=[]){let s=Object.assign({},e);for(let e of t)e in s&&delete s[e];return s}let gs=(0,e.createContext)(null);gs.displayName="OpenClosedContext";var bs=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(bs||{});function vs(){return(0,e.useContext)(gs)}function Es({value:t,children:s}){return e.createElement(gs.Provider,{value:t},s)}var xs=Object.defineProperty,ks=(e,t,s)=>(((e,t,s)=>{t in e?xs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s})(e,"symbol"!=typeof t?t+"":t,s),s);let Ss=new class{constructor(){ks(this,"current",this.detect()),ks(this,"handoffState","pending"),ks(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},Ls=(t,s)=>{Ss.isServer?(0,e.useEffect)(t,s):(0,e.useLayoutEffect)(t,s)};function Ts(){let t=(0,e.useRef)(!1);return Ls((()=>(t.current=!0,()=>{t.current=!1})),[]),t}function Fs(t){let s=(0,e.useRef)(t);return Ls((()=>{s.current=t}),[t]),s}function $s(){let[t,s]=(0,e.useState)(Ss.isHandoffComplete);return t&&!1===Ss.isHandoffComplete&&s(!1),(0,e.useEffect)((()=>{!0!==t&&s(!0)}),[t]),(0,e.useEffect)((()=>Ss.handoff()),[]),t}let Ps=function(t){let s=Fs(t);return e.useCallback(((...e)=>s.current(...e)),[s])},Rs=Symbol();function Ns(...t){let s=(0,e.useRef)(t);(0,e.useEffect)((()=>{s.current=t}),[t]);let r=Ps((e=>{for(let t of s.current)null!=t&&("function"==typeof t?t(e):t.current=e)}));return t.every((e=>null==e||(null==e?void 0:e[Rs])))?void 0:r}function Os(){let e=[],t=[],s={enqueue(e){t.push(e)},addEventListener:(e,t,r,a)=>(e.addEventListener(t,r,a),s.add((()=>e.removeEventListener(t,r,a)))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return s.add((()=>cancelAnimationFrame(t)))},nextFrame:(...e)=>s.requestAnimationFrame((()=>s.requestAnimationFrame(...e))),setTimeout(...e){let t=setTimeout(...e);return s.add((()=>clearTimeout(t)))},microTask(...e){let t={current:!0};return function(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}((()=>{t.current&&e[0]()})),s.add((()=>{t.current=!1}))},add:t=>(e.push(t),()=>{let s=e.indexOf(t);if(s>=0){let[t]=e.splice(s,1);t()}}),dispose(){for(let t of e.splice(0))t()},async workQueue(){for(let e of t.splice(0))await e()}};return s}function Cs(e,...t){e&&t.length>0&&e.classList.add(...t)}function As(e,...t){e&&t.length>0&&e.classList.remove(...t)}function Is(){let[t]=(0,e.useState)(Os);return(0,e.useEffect)((()=>()=>t.dispose()),[t]),t}function Ms({container:e,direction:t,classes:s,onStart:r,onStop:a}){let o=Ts(),n=Is(),i=Fs(t);Ls((()=>{let t=Os();n.add(t.dispose);let l=e.current;if(l&&"idle"!==i.current&&o.current)return t.dispose(),r.current(i.current),t.add(function(e,t,s,r){let a=s?"enter":"leave",o=Os(),n=void 0!==r?function(e){let t={called:!1};return(...s)=>{if(!t.called)return t.called=!0,e(...s)}}(r):()=>{};"enter"===a&&(e.removeAttribute("hidden"),e.style.display="");let i=ls(a,{enter:()=>t.enter,leave:()=>t.leave}),l=ls(a,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),c=ls(a,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return As(e,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),Cs(e,...i,...c),o.nextFrame((()=>{As(e,...c),Cs(e,...l),function(e,t){let s=Os();if(!e)return s.dispose;let{transitionDuration:r,transitionDelay:a}=getComputedStyle(e),[o,n]=[r,a].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t}));if(o+n!==0){let r=s.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(t(),r())}))}else t();s.add((()=>t())),s.dispose}(e,(()=>(As(e,...i),Cs(e,...t.entered),n())))})),o.dispose}(l,s.current,"enter"===i.current,(()=>{t.dispose(),a.current(i.current)}))),t.dispose}),[t])}function Ds(e=""){return e.split(" ").filter((e=>e.trim().length>1))}let Bs=(0,e.createContext)(null);Bs.displayName="TransitionContext";var Us=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Us||{});let js=(0,e.createContext)(null);function Vs(e){return"children"in e?Vs(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function zs(t,s){let r=Fs(t),a=(0,e.useRef)([]),o=Ts(),n=Is(),i=Ps(((e,t=ps.Hidden)=>{let s=a.current.findIndex((({el:t})=>t===e));-1!==s&&(ls(t,{[ps.Unmount](){a.current.splice(s,1)},[ps.Hidden](){a.current[s].state="hidden"}}),n.microTask((()=>{var e;!Vs(a)&&o.current&&(null==(e=r.current)||e.call(r))})))})),l=Ps((e=>{let t=a.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):a.current.push({el:e,state:"visible"}),()=>i(e,ps.Unmount)})),c=(0,e.useRef)([]),d=(0,e.useRef)(Promise.resolve()),u=(0,e.useRef)({enter:[],leave:[],idle:[]}),p=Ps(((e,t,r)=>{c.current.splice(0),s&&(s.chains.current[t]=s.chains.current[t].filter((([t])=>t!==e))),null==s||s.chains.current[t].push([e,new Promise((e=>{c.current.push(e)}))]),null==s||s.chains.current[t].push([e,new Promise((e=>{Promise.all(u.current[t].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===t?d.current=d.current.then((()=>null==s?void 0:s.wait.current)).then((()=>r(t))):r(t)})),m=Ps(((e,t,s)=>{Promise.all(u.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=c.current.shift())||e()})).then((()=>s(t)))}));return(0,e.useMemo)((()=>({children:a,register:l,unregister:i,onStart:p,onStop:m,wait:d,chains:u})),[l,i,a,p,m,u,d])}function qs(){}js.displayName="NestingContext";let Ws=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Hs(e){var t;let s={};for(let r of Ws)s[r]=null!=(t=e[r])?t:qs;return s}let Ys=us.RenderStrategy,Gs=ys((function(t,s){let{beforeEnter:r,afterEnter:a,beforeLeave:o,afterLeave:n,enter:i,enterFrom:l,enterTo:c,entered:d,leave:u,leaveFrom:p,leaveTo:m,...f}=t,h=(0,e.useRef)(null),y=Ns(h,s),_=f.unmount?ps.Unmount:ps.Hidden,{show:w,appear:g,initial:b}=function(){let t=(0,e.useContext)(Bs);if(null===t)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}(),[v,E]=(0,e.useState)(w?"visible":"hidden"),x=function(){let t=(0,e.useContext)(js);if(null===t)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}(),{register:k,unregister:S}=x,L=(0,e.useRef)(null);(0,e.useEffect)((()=>k(h)),[k,h]),(0,e.useEffect)((()=>{if(_===ps.Hidden&&h.current)return w&&"visible"!==v?void E("visible"):ls(v,{hidden:()=>S(h),visible:()=>k(h)})}),[v,h,k,S,w,_]);let T=Fs({enter:Ds(i),enterFrom:Ds(l),enterTo:Ds(c),entered:Ds(d),leave:Ds(u),leaveFrom:Ds(p),leaveTo:Ds(m)}),F=function(t){let s=(0,e.useRef)(Hs(t));return(0,e.useEffect)((()=>{s.current=Hs(t)}),[t]),s}({beforeEnter:r,afterEnter:a,beforeLeave:o,afterLeave:n}),$=$s();(0,e.useEffect)((()=>{if($&&"visible"===v&&null===h.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[h,v,$]);let P=b&&!g,R=!$||P||L.current===w?"idle":w?"enter":"leave",N=Ps((e=>ls(e,{enter:()=>F.current.beforeEnter(),leave:()=>F.current.beforeLeave(),idle:()=>{}}))),O=Ps((e=>ls(e,{enter:()=>F.current.afterEnter(),leave:()=>F.current.afterLeave(),idle:()=>{}}))),C=zs((()=>{E("hidden"),S(h)}),x);Ms({container:h,classes:T,direction:R,onStart:Fs((e=>{C.onStart(h,e,N)})),onStop:Fs((e=>{C.onStop(h,e,O),"leave"===e&&!Vs(C)&&(E("hidden"),S(h))}))}),(0,e.useEffect)((()=>{!P||(_===ps.Hidden?L.current=null:L.current=w)}),[w,P,v]);let A=f,I={ref:y};return g&&w&&Ss.isServer&&(A={...A,className:is(f.className,...T.current.enter,...T.current.enterFrom)}),e.createElement(js.Provider,{value:C},e.createElement(Es,{value:ls(v,{visible:bs.Open,hidden:bs.Closed})},ms({ourProps:I,theirProps:A,defaultTag:"div",features:Ys,visible:"visible"===v,name:"Transition.Child"})))})),Ks=ys((function(t,s){let{show:r,appear:a=!1,unmount:o,...n}=t,i=(0,e.useRef)(null),l=Ns(i,s);$s();let c=vs();if(void 0===r&&null!==c&&(r=ls(c,{[bs.Open]:!0,[bs.Closed]:!1})),![!0,!1].includes(r))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,u]=(0,e.useState)(r?"visible":"hidden"),p=zs((()=>{u("hidden")})),[m,f]=(0,e.useState)(!0),h=(0,e.useRef)([r]);Ls((()=>{!1!==m&&h.current[h.current.length-1]!==r&&(h.current.push(r),f(!1))}),[h,r]);let y=(0,e.useMemo)((()=>({show:r,appear:a,initial:m})),[r,a,m]);(0,e.useEffect)((()=>{if(r)u("visible");else if(Vs(p)){let e=i.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&u("hidden")}else u("hidden")}),[r,p]);let _={unmount:o};return e.createElement(js.Provider,{value:p},e.createElement(Bs.Provider,{value:y},ms({ourProps:{..._,as:e.Fragment,children:e.createElement(Gs,{ref:l,..._,...n})},theirProps:{},defaultTag:e.Fragment,features:Ys,visible:"visible"===d,name:"Transition"})))})),Zs=ys((function(t,s){let r=null!==(0,e.useContext)(Bs),a=null!==vs();return e.createElement(e.Fragment,null,!r&&a?e.createElement(Ks,{ref:s,...t}):e.createElement(Gs,{ref:s,...t}))})),Js=Object.assign(Ks,{Child:Zs,Root:Ks});const Qs=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"}))})),Xs=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"}))})),er=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"}))})),tr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"}))})),sr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"}))})),rr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}))}));var ar=s(4530),or=s.n(ar),nr=s(5890),ir=s.n(nr);const lr=(e,t)=>{try{return(0,i.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}},cr=({link:t})=>{const s=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %1$s expands to "Yoast SEO" academy, which is a clickable link. */
(0,Lt.__)("Want to learn SEO from Team Yoast? Check out our %1$s!","wordpress-seo"),"<link/>"),{link:(0,e.createElement)("a",{href:t,target:"_blank",rel:"noopener"},"Yoast SEO academy")})),[]);return(0,e.createElement)(l.Paper,{as:"div",className:"yst-p-6 yst-space-y-3"},(0,e.createElement)(l.Title,{as:"h2",size:"4",className:"yst-text-base yst-text-primary-500"},(0,Lt.__)("Learn SEO","wordpress-seo")),(0,e.createElement)("p",null,s,(0,e.createElement)("br",null),(0,Lt.__)("We have both free and premium online courses to learn everything you need to know about SEO.","wordpress-seo")),(0,e.createElement)(l.Link,{href:t,className:"yst-block",target:"_blank",rel:"noopener"},(0,Lt.sprintf)(/* translators: %1$s expands to "Yoast SEO academy". */
(0,Lt.__)("Check out %1$s","wordpress-seo"),"Yoast SEO academy")))};cr.propTypes={link:ir().string.isRequired};const dr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),ur=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),pr=({learnMoreLink:t,thumbnail:s,wistiaEmbedPermission:r,upsellLink:a,isProductCopy:o,title:n,upsellLabel:i,newToText:c,bundleNote:d,ctbId:u})=>{const{onClose:p,initialFocus:m}=(0,l.useModalContext)(),f={a:(0,e.createElement)(xr,{href:t,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,e.createElement)(ur,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,e.createElement)("div",{className:"yst-relative yst-w-full"},(0,e.createElement)(Kr,{videoId:"vmrahpfjxp",thumbnail:s,wistiaEmbedPermission:r}),(0,e.createElement)(l.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,e.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,e.createElement)("span",{className:"yst-logo-icon"}),c))),(0,e.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,e.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},n),(0,e.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},lr(o?(0,Lt.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,Lt.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,Lt.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,Lt.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),f))),(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,e.createElement)(l.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:a,target:"_blank",ref:m,"data-action":"load-nfd-ctb","data-ctb-id":u},(0,e.createElement)(dr,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),i,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,Lt.__)("(Opens in a new browser tab)","wordpress-seo")))),d,(0,e.createElement)(l.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,Lt.__)("Close","wordpress-seo"))))};pr.propTypes={learnMoreLink:ir().string.isRequired,upsellLink:ir().string.isRequired,thumbnail:ir().shape({src:ir().string.isRequired,width:ir().string,height:ir().string}).isRequired,wistiaEmbedPermission:ir().shape({value:ir().bool.isRequired,status:ir().string.isRequired,set:ir().func.isRequired}).isRequired,title:ir().string,upsellLabel:ir().string,newToText:ir().string,isProductCopy:ir().bool,bundleNote:ir().oneOfType([ir().string,ir().element]),ctbId:ir().string},pr.defaultProps={title:(0,Lt.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,Lt.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const mr=({learnMoreLink:t,thumbnail:s,wistiaEmbedPermission:r,upsellLink:a,upsellLabel:o,newToText:n,bundleNote:i,ctbId:c})=>{const{onClose:d,initialFocus:u}=(0,l.useModalContext)(),p={a:(0,e.createElement)(xr,{href:t,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,e.createElement)(ur,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,e.createElement)("br",null)};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,e.createElement)("div",{className:"yst-relative yst-w-full"},(0,e.createElement)(Kr,{videoId:"vun9z1dpfh",thumbnail:s,wistiaEmbedPermission:r}),(0,e.createElement)(l.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,Lt.__)("Beta","wordpress-seo-premium"))),(0,e.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,e.createElement)("span",{className:"yst-logo-icon"}),n))),(0,e.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,e.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,Lt.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,Lt.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,e.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},lr((0,Lt.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,Lt.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),p))),(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,e.createElement)(l.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:a,target:"_blank",ref:u,"data-action":"load-nfd-ctb","data-ctb-id":c},(0,e.createElement)(dr,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),o,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,Lt.__)("(Opens in a new browser tab)","wordpress-seo")))),i,(0,e.createElement)(l.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:d},(0,Lt.__)("Close","wordpress-seo"))))};mr.propTypes={learnMoreLink:ir().string.isRequired,upsellLink:ir().string.isRequired,thumbnail:ir().shape({src:ir().string.isRequired,width:ir().string,height:ir().string}).isRequired,wistiaEmbedPermission:ir().shape({value:ir().bool.isRequired,status:ir().string.isRequired,set:ir().func.isRequired}).isRequired,upsellLabel:ir().string,newToText:ir().string,bundleNote:ir().oneOfType([ir().string,ir().element]),ctbId:ir().string},mr.defaultProps={upsellLabel:(0,Lt.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const fr=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-flex yst-gap-2"},(0,e.createElement)(l.Button,{onClick:t},(0,Lt.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(l.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,Lt.__)("Contact support","wordpress-seo")));fr.propTypes={handleRefreshClick:ir().func.isRequired,supportLink:ir().string.isRequired};const hr=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,e.createElement)(l.Button,{className:"yst-order-last",onClick:t},(0,Lt.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(l.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,Lt.__)("Contact support","wordpress-seo")));hr.propTypes={handleRefreshClick:ir().func.isRequired,supportLink:ir().string.isRequired};const yr=({error:t,children:s})=>(0,e.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,e.createElement)(l.Title,null,(0,Lt.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,e.createElement)("p",null,(0,Lt.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,e.createElement)(l.Alert,{variant:"error"},(null==t?void 0:t.message)||(0,Lt.__)("Undefined error message.","wordpress-seo")),(0,e.createElement)("p",null,(0,Lt.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),s);yr.propTypes={error:ir().object.isRequired,children:ir().node},yr.VerticalButtons=hr,yr.HorizontalButtons=fr;const _r={variant:{lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}},wr=({id:t,children:s,title:r,description:a=null,variant:o="2xl"})=>(0,e.createElement)("section",{id:t,className:_r.variant[o].grid},(0,e.createElement)("div",{className:_r.variant[o].col1},(0,e.createElement)("div",{className:"yst-max-w-screen-sm"},(0,e.createElement)(l.Title,{as:"h2",size:"4"},r),a&&(0,e.createElement)("p",{className:"yst-mt-2"},a))),(0,e.createElement)("fieldset",{className:`yst-min-w-0 yst-mt-8 ${_r.variant[o].col2}`},(0,e.createElement)("legend",{className:"yst-sr-only"},r),(0,e.createElement)("div",{className:"yst-space-y-8"},s)));var gr;function br(){return br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},br.apply(this,arguments)}wr.propTypes={id:ir().string,children:ir().node.isRequired,title:ir().node.isRequired,description:ir().node,variant:ir().oneOf(Object.keys(_r.variant))};const vr=t=>e.createElement("svg",br({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},t),gr||(gr=e.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),Er=({to:t,idSuffix:s="",...r})=>{const a=(0,i.useMemo)((()=>(0,le.replace)((0,le.replace)(`link-${t}`,"/","-"),"--","-")),[t]);return(0,e.createElement)(l.SidebarNavigation.SubmenuItem,{as:Et,pathProp:"to",id:`${a}${s}`,to:t,...r})};Er.propTypes={to:ir().string.isRequired,idSuffix:ir().string};const xr=({href:t,children:s,...r})=>(0,e.createElement)(l.Link,{target:"_blank",rel:"noopener noreferrer",...r,href:t},s,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,Lt.__)("(Opens in a new browser tab)","wordpress-seo")));xr.propTypes={href:ir().string.isRequired,children:ir().node},xr.defaultProps={children:null};const kr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var Sr,Lr,Tr;function Fr(){return Fr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Fr.apply(this,arguments)}const $r=t=>e.createElement("svg",Fr({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},t),Sr||(Sr=e.createElement("defs",null,e.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),Lr||(Lr=e.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),Tr||(Tr=e.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),e.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function Pr(){return Pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Pr.apply(this,arguments)}const Rr=t=>e.createElement("svg",Pr({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},t),e.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var Nr,Or,Cr,Ar,Ir,Mr,Dr,Br,Ur;function jr(){return jr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},jr.apply(this,arguments)}const Vr=t=>e.createElement("svg",jr({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},t),Nr||(Nr=e.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),Or||(Or=e.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),Cr||(Cr=e.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),Ar||(Ar=e.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),Ir||(Ir=e.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),Mr||(Mr=e.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),Dr||(Dr=e.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),Br||(Br=e.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),Ur||(Ur=e.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),zr=({link:t,linkProps:s,isPromotionActive:r})=>{let a=(0,i.useMemo)((()=>(0,Lt.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),o=lr((0,Lt.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,Lt.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,e.createElement)("span",{className:"yst-whitespace-nowrap"})});const n=r("black-friday-2024-promotion");return n&&(a=(0,i.useMemo)((()=>(0,Lt.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),o=lr((0,Lt.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,Lt.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,e.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,e.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,e.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,e.createElement)(Vr,null)),n&&(0,e.createElement)("div",{className:"sidebar__sale_banner_container"},(0,e.createElement)("div",{className:"sidebar__sale_banner"},(0,e.createElement)("span",{className:"banner_text"},(0,Lt.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,e.createElement)(l.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},o),(0,e.createElement)("p",{className:"yst-mt-2"},a),(0,e.createElement)(l.Button,{as:"a",variant:"upsell",href:t,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...s},(0,e.createElement)("span",null,n?(0,Lt.__)("Buy now","wordpress-seo"):o),(0,e.createElement)(kr,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,e.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,Lt.__)("30-day money back guarantee.","wordpress-seo")),(0,e.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,e.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,e.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,Lt.__)("Read reviews from real users","wordpress-seo")),(0,e.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,e.createElement)(vr,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)("span",{className:"yst-flex yst-gap-1"},(0,e.createElement)(Rr,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(Rr,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(Rr,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(Rr,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)($r,{className:"yst-w-5 yst-h-5"})),(0,e.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};zr.propTypes={link:ir().string.isRequired,linkProps:ir().object,isPromotionActive:ir().func},zr.defaultProps={linkProps:{},isPromotionActive:le.noop};const qr=({premiumLink:t,premiumUpsellConfig:s,isPromotionActive:r})=>{const a=r("black-friday-2024-promotion");return(0,e.createElement)(l.Paper,{as:"div",className:"xl:yst-max-w-3xl"},a&&(0,e.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,e.createElement)("div",null,(0,Lt.__)("30% OFF","wordpress-seo")),(0,e.createElement)("div",null,(0,Lt.__)("BLACK FRIDAY","wordpress-seo"))),(0,e.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,e.createElement)(l.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,Lt.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,Lt.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,e.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},[(0,Lt.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,Lt.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,Lt.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,Lt.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,Lt.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,Lt.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,Lt.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,Lt.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,Lt.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,Lt.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,Lt.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,Lt.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")].map(((t,s)=>(0,e.createElement)("li",{key:`upsell-benefit-${s}`},lr(t,{strong:(0,e.createElement)("span",{className:"yst-font-semibold"})}))))),(0,e.createElement)(l.Button,{as:"a",variant:"upsell",size:"extra-large",href:t,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...s},a?(0,Lt.__)("Claim your 30% off now!","wordpress-seo"):(0,Lt.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,Lt.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,e.createElement)(kr,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};qr.propTypes={premiumLink:ir().string.isRequired,premiumUpsellConfig:ir().object,isPromotionActive:ir().func},qr.defaultProps={premiumUpsellConfig:{},isPromotionActive:le.noop};const Wr=({premiumLink:t,premiumUpsellConfig:s,academyLink:r,isPromotionActive:a})=>(0,e.createElement)("div",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 min-[783px]:yst-grid-cols-1 lg:yst-grid-cols-2 xl:yst-grid-cols-1 yst-gap-4"},(0,e.createElement)(zr,{link:t,linkProps:s,isPromotionActive:a}),(0,e.createElement)(cr,{link:r}));Wr.propTypes={premiumLink:ir().string.isRequired,premiumUpsellConfig:ir().object.isRequired,academyLink:ir().string.isRequired,isPromotionActive:ir().func.isRequired};const Hr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),Yr=({isOpen:t,onClose:s=le.noop,onDiscard:r=le.noop,title:a,description:o,dismissLabel:n,discardLabel:i})=>{const c=(0,l.useSvgAria)();return(0,e.createElement)(l.Modal,{isOpen:t,onClose:s},(0,e.createElement)(l.Modal.Panel,{closeButtonScreenReaderText:(0,Lt.__)("Close","wordpress-seo")},(0,e.createElement)("div",{className:"sm:yst-flex sm:yst-items-start"},(0,e.createElement)("div",{className:"yst-mx-auto yst-flex-shrink-0 yst-flex yst-items-center yst-justify-center yst-h-12 yst-w-12 yst-rounded-full yst-bg-red-100 sm:yst-mx-0 sm:yst-h-10 sm:yst-w-10"},(0,e.createElement)(Hr,{className:"yst-h-6 yst-w-6 yst-text-red-600",...c})),(0,e.createElement)("div",{className:"yst-mt-3 yst-text-center sm:yst-mt-0 sm:yst-ms-4 sm:yst-text-start"},(0,e.createElement)(l.Modal.Title,{className:"yst-text-lg yst-leading-6 yst-font-medium yst-text-slate-900 yst-mb-3"},a),(0,e.createElement)(l.Modal.Description,{className:"yst-text-sm yst-text-slate-500"},o))),(0,e.createElement)("div",{className:"yst-flex yst-flex-col sm:yst-flex-row-reverse yst-gap-3 yst-mt-6"},(0,e.createElement)(l.Button,{type:"button",variant:"error",onClick:r,className:"yst-block"},i),(0,e.createElement)(l.Button,{type:"button",variant:"secondary",onClick:s,className:"yst-block"},n))))};Yr.propTypes={isOpen:ir().bool.isRequired,onClose:ir().func,onDiscard:ir().func,title:ir().string.isRequired,description:ir().string.isRequired,dismissLabel:ir().string.isRequired,discardLabel:ir().string.isRequired};const Gr=window.yoast.reactHelmet,Kr=({videoId:t,thumbnail:s,wistiaEmbedPermission:r})=>{const[a,o]=(0,i.useState)(r.value?as:ss),n=(0,i.useCallback)((()=>o(as)),[o]),c=(0,i.useCallback)((()=>{r.value?n():o(rs)}),[r.value,n,o]),d=(0,i.useCallback)((()=>o(ss)),[o]),u=(0,i.useCallback)((()=>{r.set(!0),n()}),[r.set,n]);return(0,e.createElement)(e.Fragment,null,r.value&&(0,e.createElement)(Gr.Helmet,null,(0,e.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,e.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},a===ss&&(0,e.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:c},(0,e.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...s})),a===rs&&(0,e.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,e.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},r.status===Xt&&(0,e.createElement)(l.Spinner,null),r.status!==Xt&&(0,Lt.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,Lt.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,e.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,e.createElement)(l.Button,{type:"button",variant:"secondary",onClick:d,disabled:r.status===Xt},(0,Lt.__)("Deny","wordpress-seo")),(0,e.createElement)(l.Button,{type:"button",variant:"primary",onClick:u,disabled:r.status===Xt},(0,Lt.__)("Allow","wordpress-seo")))),r.value&&a===as&&(0,e.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===t&&(0,e.createElement)(l.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==t&&(0,e.createElement)("div",{className:`wistia_embed wistia_async_${t} videoFoam=true`}))))};var Zr,Jr;function Qr(){return Qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Qr.apply(this,arguments)}Kr.propTypes={videoId:ir().string.isRequired,thumbnail:ir().shape({src:ir().string.isRequired,width:ir().string,height:ir().string}).isRequired,wistiaEmbedPermission:ir().shape({value:ir().bool.isRequired,status:ir().string.isRequired,set:ir().func.isRequired}).isRequired};const Xr=t=>e.createElement("svg",Qr({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",className:"yoast-logo_svg__w-40",viewBox:"0 0 842 224"},t),Zr||(Zr=e.createElement("path",{fill:"#a61e69",d:"M166.55 54.09c-38.69 0-54.17 25.97-54.17 54.88s15.25 56.02 54.17 56.02 54.07-27.19 54-54.26c-.09-32.97-16.77-56.65-54-56.65Zm-23.44 56.52c.94-38.69 30.66-38.65 40.59-24.79 9.05 12.63 10.9 55.81-17.14 55.5-12.92-.14-23.06-8.87-23.44-30.71Zm337.25 27.55V82.11h20.04V57.78h-20.04V28.39h-30.95v29.39h-15.7v24.33h15.7v52.87c0 30.05 20.95 47.91 43.06 51.61l9.24-24.88c-12.89-1.63-21.23-11.27-21.35-23.54Zm-156.15-8.87V87.16c0-1.54-.1-2.98-.25-4.39-2.68-34.04-51.02-33.97-88.46-20.9l10.82 21.78c24.38-11.58 38.97-8.59 44.07-2.89.13.15.26.29.38.45.01.02.03.04.04.06 2.6 3.51 1.98 9.05 1.98 13.41-31.86 0-65.77 4.23-65.77 39.17 0 26.56 33.28 43.65 68.06 18.33l5.16 12.45h29.81c-2.66-14.62-5.85-27.14-5.85-35.34Zm-31.18-.23c-24.51 27.43-46.96 1.61-23.97-9.65 6.77-2.31 15.95-2.41 23.97-2.41v12.06Zm78.75-44.17c0-10.38 16.61-15.23 42.82-3.27l9.06-22.01c-35.27-10.66-83.44-11.62-83.75 25.28-.15 17.68 11.19 27.19 27.52 33.26 11.31 4.2 27.64 6.38 27.59 15.39-.06 11.77-25.38 13.57-48.42-2.26l-9.31 23.87c31.43 15.64 89.87 16.08 89.56-23.12-.31-38.76-55.08-32.11-55.08-47.14ZM99.3 1 54.44 125.61 32.95 58.32H1l35.78 91.89a33.49 33.49 0 0 1 0 24.33c-4 10.25-10.65 19.03-26.87 21.21v27.24c31.58 0 48.65-19.41 63.88-61.96L133.48 1H99.3ZM598.64 139.05c0 8.17-2.96 14.58-8.87 19.23-5.91 4.65-14.07 6.98-24.47 6.98s-18.92-1.61-25.54-4.84v-14.2c4.19 1.97 8.65 3.52 13.37 4.65 4.72 1.13 9.11 1.7 13.18 1.7 5.95 0 10.35-1.13 13.18-3.39 2.83-2.26 4.25-5.3 4.25-9.11 0-3.43-1.3-6.35-3.9-8.74-2.6-2.39-7.97-5.22-16.1-8.48-8.39-3.39-14.3-7.27-17.74-11.63-3.44-4.36-5.16-9.59-5.16-15.71 0-7.67 2.72-13.7 8.18-18.1 5.45-4.4 12.77-6.6 21.95-6.6s17.57 1.93 26.29 5.78l-4.78 12.26c-8.18-3.43-15.47-5.15-21.89-5.15-4.87 0-8.55 1.06-11.07 3.17-2.52 2.12-3.77 4.91-3.77 8.39 0 2.39.5 4.43 1.51 6.13s2.66 3.3 4.97 4.81c2.3 1.51 6.46 3.5 12.45 5.97 6.75 2.81 11.7 5.43 14.85 7.86 3.15 2.43 5.45 5.18 6.92 8.23 1.46 3.06 2.2 6.66 2.2 10.81Zm68.53 24.96h-52.02V72.12h52.02v12.7h-36.99v25.01h34.66v12.57h-34.66v28.85h36.99v12.76Zm100.24-46.07c0 14.96-3.74 26.59-11.23 34.88-7.49 8.3-18.08 12.44-31.8 12.44s-24.54-4.12-31.99-12.35c-7.44-8.23-11.17-19.93-11.17-35.1s3.74-26.82 11.23-34.95c7.49-8.13 18.17-12.19 32.05-12.19s24.24 4.13 31.7 12.38c7.47 8.26 11.2 19.88 11.2 34.88Zm-70.2 0c0 11.31 2.29 19.89 6.86 25.74 4.57 5.85 11.35 8.77 20.32 8.77s15.67-2.89 20.22-8.67c4.55-5.78 6.82-14.39 6.82-25.83s-2.25-19.82-6.76-25.64-11.23-8.74-20.16-8.74-15.82 2.91-20.41 8.74c-4.59 5.82-6.89 14.37-6.89 25.64Z"})),Jr||(Jr=e.createElement("path",{fill:"#77b227",d:"m790.45 165.35 36.05-94.96H840l-36.02 94.96h-13.53z"})));e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),ir().bool.isRequired,ir().func.isRequired,ir().func,ir().string;const ea=({name:e})=>{const t=oa("selectPreference",[],"isNetworkAdmin"),s=oa("selectPreference",[],"isMainSite"),r=(0,i.useMemo)((()=>"wpseo.tracking"===e&&!t&&!s),[e,t,s]),a=(0,i.useMemo)((()=>(0,le.get)(window,`wpseoScriptData.disabledSettings.${e}`,"")),[]),o=(0,i.useMemo)((()=>{if(r)return(0,Lt.__)("Unavailable for sub-sites","wordpress-seo");switch(a){case"multisite":return(0,Lt.__)("Unavailable for multisites","wordpress-seo");case"network":return(0,Lt.__)("Network disabled","wordpress-seo");case"language":return(0,Lt.__)("Only available for English sites","wordpress-seo");default:return""}}),[a,r]);return{isDisabled:(0,i.useMemo)((()=>!(0,le.isEmpty)(o)),[o]),message:o,disabledSetting:a}},ta="@yoast/settings",sa=()=>(0,a.useDispatch)(ta);var ra=s(1206),aa=s.n(ra);const oa=(e,t=[],...s)=>(0,a.useSelect)((t=>{var r,a;return null===(r=(a=t(ta))[e])||void 0===r?void 0:r.call(a,...s)}),t),na=({id:t,children:s,title:r,description:a=null})=>{const o=oa("selectPreference",[],"isPremium");return(0,e.createElement)(wr,{id:t,title:r,description:a,variant:o?"xl":"2xl"},s)};na.propTypes={id:ir().string,children:ir().node.isRequired,title:ir().node.isRequired,description:ir().node};const ia=na;var la=s(8133);const ca=({children:t})=>{const{isSubmitting:s,status:r,dirty:a,resetForm:o,initialValues:n}=q(),c=oa("selectIsMediaLoading"),d=(0,i.useMemo)((()=>(0,le.includes)((0,le.values)(r),!0)),[r]),[u,,,p,m]=(0,l.useToggleState)(!1),f=(0,i.useCallback)((()=>{m(),o({values:n})}),[o,n,m]);return(0,e.createElement)(se,{className:"yst-flex yst-flex-col yst-h-full"},(0,e.createElement)("div",{className:"yst-flex-grow yst-p-8"},t),(0,e.createElement)("footer",{className:"yst-sticky yst-bottom-0 yst-z-10"},(0,e.createElement)(la.Z,{easing:"ease-in-out",duration:300,height:a?"auto":0,animateOpacity:!0},(0,e.createElement)("div",{className:"yst-bg-slate-50 yst-border-slate-200 yst-border-t yst-rounded-b-lg"},(0,e.createElement)("div",{className:"yst-flex yst-align-middle yst-space-x-3 rtl:yst-space-x-reverse yst-p-8"},(0,e.createElement)(l.Button,{id:"button-submit-settings",type:"submit",isLoading:s,disabled:s||c||d},(0,Lt.__)("Save changes","wordpress-seo")),(0,e.createElement)(l.Button,{id:"button-undo-settings",type:"button",variant:"secondary",disabled:!a,onClick:p},(0,Lt.__)("Discard changes","wordpress-seo")),(0,e.createElement)(Yr,{isOpen:u,onClose:m,title:(0,Lt.__)("Discard all changes","wordpress-seo"),description:(0,Lt.__)("You are about to discard all unsaved changes. All of your settings will be reset to the point where you last saved. Are you sure you want to do this?","wordpress-seo"),onDiscard:f,dismissLabel:(0,Lt.__)("No, continue editing","wordpress-seo"),discardLabel:(0,Lt.__)("Yes, discard changes","wordpress-seo")}))))))};ca.propTypes={children:ir().node.isRequired};const da=ca,ua=t=>{const[s,,{setTouched:r,setValue:a}]=ee({...t,type:"checkbox"}),o=(0,i.useCallback)((e=>{r(!0,!1),a(!e)}),[t.name]);return(0,e.createElement)(l.ToggleField,{...s,checked:(0,le.isUndefined)(t.checked)?!s.checked:!t.checked,onChange:o,...t})};ua.propTypes={name:ir().string.isRequired,checked:ir().bool};const pa=ua,ma=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"}))})),fa={variant:{square:"yst-h-48 yst-w-48",landscape:"yst-h-48 yst-w-96",portrait:"yst-h-96 yst-w-48"}},ha=({label:t="",description:s="",icon:r=ma,disabled:a=!1,isDummy:o=!1,libraryType:n="image",variant:c="landscape",id:d,mediaUrlName:u,mediaIdName:p,fallbackMediaId:m="0",previewLabel:f="",selectLabel:h=(0,Lt.__)("Select image","wordpress-seo"),replaceLabel:y=(0,Lt.__)("Replace image","wordpress-seo"),removeLabel:_=(0,Lt.__)("Remove image","wordpress-seo"),className:w=""})=>{const{values:g,setFieldValue:b,setFieldTouched:v,errors:E}=q(),[x,k]=(0,i.useState)(null),S=(0,i.useMemo)((()=>(0,le.get)(window,"wp.media",null)),[]),L=(0,i.useMemo)((()=>(0,le.get)(g,p,"")),[g,p]),T=oa("selectMediaById",[L],L),F=oa("selectIsMediaError"),$=oa("selectMediaById",[m],m),{fetchMedia:P,addOneMedia:R}=sa(),N=(0,i.useMemo)((()=>(0,le.get)(E,p,"")),[E,p]),O=(0,i.useMemo)((()=>a||o),[o,a]),{ids:C,describedBy:A}=(0,l.useDescribedBy)(`field-${d}-id`,{description:s,error:N}),I=(0,i.useMemo)((()=>L>0?T:m>0?$:null),[L,T,m,$]),M=(0,i.useMemo)((()=>(0,le.join)((0,le.map)((null==T?void 0:T.sizes)||(null==$?void 0:$.sizes),(e=>`${null==e?void 0:e.url} ${null==e?void 0:e.width}w`)),", ")),[T,$]),D=(0,i.useCallback)((()=>{o||null==x||x.open()}),[o,x]),B=(0,i.useCallback)((()=>{o||(v(u,!0,!1),b(u,"",!1),v(p,!0,!1),b(p,""))}),[o,v,b,u,p]),U=(0,i.useCallback)((()=>{var e,t,s;if(o)return;const r=(null===(e=x.state())||void 0===e||null===(t=e.get("selection"))||void 0===t||null===(s=t.first())||void 0===s?void 0:s.toJSON())||{};v(u,!0,!1),b(u,r.url,!1),v(p,!0,!1),b(p,r.id),R(r)}),[o,x,v,b,u,p]);return(0,i.useEffect)((()=>{S&&k(S({title:t,multiple:!1,library:{type:n}}))}),[S,t,n,k]),(0,i.useEffect)((()=>(null==x||x.on("select",U),()=>null==x?void 0:x.off("select",U))),[x,U]),(0,i.useEffect)((()=>{L&&!T&&P([L]),m&&!$&&P([m])}),[]),(0,e.createElement)("fieldset",{id:d,className:"yst-min-w-0 yst-w-96 yst-max-w-full"},(0,e.createElement)(te,{type:"hidden",name:p,id:`input-${d}-id`,"aria-describedby":A,disabled:O}),(0,e.createElement)(te,{type:"hidden",name:u,id:`input-${d}-url`,"aria-describedby":A,disabled:O}),t&&(0,e.createElement)(l.Label,{as:"legend",className:or()("yst-mb-2",O&&"yst-opacity-50 yst-cursor-not-allowed")},t),(0,e.createElement)("button",{type:"button",id:`button-${d}-preview`,onClick:D,className:or()("yst-overflow-hidden yst-flex yst-justify-center yst-items-center yst-max-w-full yst-rounded-md yst-mb-4 yst-border-slate-300 focus:yst-outline-none focus:yst-ring-2 focus:yst-ring-offset-2 focus:yst-ring-primary-500",!o&&I?"yst-bg-slate-50 yst-border":"yst-border-2 yst-border-dashed",O&&"yst-opacity-50 yst-cursor-not-allowed",fa.variant[c],w),disabled:O},!o&&I?(0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:"yst-sr-only"},y),(0,e.createElement)("img",{src:null==I?void 0:I.url,alt:(null==I?void 0:I.alt)||"",srcSet:M,sizes:"landscape"===c?"24rem":"12rem",width:"landscape"===c?"24rem":"12rem",loading:"lazy",decoding:"async",className:"yst-object-cover yst-object-center yst-min-h-full yst-min-w-full"})):(0,e.createElement)("div",{className:"yst-w-48 yst-max-w-full"},(0,e.createElement)("span",{className:"yst-sr-only"},h),(0,e.createElement)(r,{className:"yst-mx-auto yst-h-12 yst-w-12 yst-text-slate-400 yst-stroke-1"}),f&&(0,e.createElement)("p",{className:"yst-text-xs yst-text-slate-600 yst-text-center yst-mt-1 yst-px-8"},f))),(0,e.createElement)("div",{className:"yst-flex yst-gap-1"},!o&&L>0?(0,e.createElement)(l.Button,{id:`button-${d}-replace`,variant:"secondary",onClick:D,disabled:O},y):(0,e.createElement)(l.Button,{id:`button-${d}-select`,variant:"secondary",onClick:D,disabled:O},h),!o&&L>0&&(0,e.createElement)(l.Link,{id:`button-${d}-remove`,as:"button",type:"button",variant:"error",onClick:B,className:or()("yst-px-3 yst-py-2 yst-rounded-md",O&&"yst-opacity-50 yst-cursor-not-allowed"),disabled:O},_)),N&&(0,e.createElement)("p",{id:C.error,className:"yst-mt-2 yst-text-sm yst-text-red-600"},N),F&&(0,e.createElement)("p",{className:"yst-mt-2 yst-text-sm yst-text-red-600"},(0,Lt.__)("Failed to retrieve media.","wordpress-seo")),s&&(0,e.createElement)("p",{id:C.description,className:or()("yst-mt-2",O&&"yst-opacity-50 yst-cursor-not-allowed")},s))};ha.propTypes={label:ir().string,description:ir().node,icon:ir().elementType,disabled:ir().bool,isDummy:ir().bool,libraryType:ir().string,variant:ir().oneOf((0,le.keys)(fa.variant)),id:ir().string.isRequired,mediaUrlName:ir().string.isRequired,mediaIdName:ir().string.isRequired,fallbackMediaId:ir().string,previewLabel:ir().node,selectLabel:ir().string,replaceLabel:ir().string,removeLabel:ir().string,className:ir().string};const ya=ha,_a=window.yoast.replacementVariableEditor,wa=({className:t="",disabled:s=!1,...r})=>{const[a,o]=(0,i.useState)(null),[n,,{setTouched:l,setValue:c}]=ee(r),d=(0,i.useCallback)((e=>{l(!0,!1),c(e)}),[r.name]),u=(0,i.useCallback)((()=>null==a?void 0:a.focus()),[a]),p=(0,i.useMemo)((()=>{var e;return(null!==(e=n.value)&&void 0!==e&&e.match(/%%\w+%%$/)?`${n.value} `:n.value)||""}),[n.value]);return(0,e.createElement)("div",{className:t},(0,e.createElement)(_a.ReplacementVariableEditor,{...n,content:p,onChange:d,editorRef:o,onFocus:u,isDisabled:s,...r}))};wa.propTypes={name:ir().string.isRequired,disabled:ir().bool,className:ir().string};const ga=wa,ba=t=>{const[{value:s,...r},,{setTouched:a,setValue:o}]=ee(t),n=(0,i.useMemo)((()=>(0,le.reduce)((0,le.isString)(s)&&(null==s?void 0:s.split(","))||[],((e,t)=>{const s=(0,le.trim)(t);return s?[...e,s]:e}),[])),[s]),c=(0,i.useCallback)((e=>{a(!0,!1),o([...n,e].join(","))}),[a,o,n]),d=(0,i.useCallback)((e=>{a(!0,!1),o([...n.slice(0,e),...n.slice(e+1)].join(","))}),[a,o,n]),u=(0,i.useCallback)((e=>{a(!0,!1),o(e.join(","))}),[a,o]);return(0,e.createElement)(l.TagField,{...r,tags:n,onAddTag:c,onRemoveTag:d,onSetTags:u,...t})};ba.propTypes={name:ir().string.isRequired};const va=ba,Ea=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"}))}));let xa;const ka=({children:t,className:s=""})=>(0,e.createElement)("div",{className:or()("yst-flex yst-items-center yst-justify-center yst-gap-2 yst-py-2 yst-px-3",s)},t);ka.propTypes={children:ir().node.isRequired,className:ir().string};const Sa=({name:t,id:s,className:r="",...a})=>{const o=oa("selectPreference",[],"siteRepresentsPerson",{}),n=oa("selectUsersWith",[o],o),{addManyUsers:c}=sa(),[{value:d,...u},,{setTouched:p,setValue:m}]=ee({type:"select",name:t,id:s,...a}),[f,h]=(0,i.useState)(Qt),[y,_]=(0,i.useState)([]),w=oa("selectPreference",[],"canCreateUsers",!1),g=oa("selectPreference",[],"createUserUrl",""),b=(0,i.useMemo)((()=>{const e=(0,le.values)(n);return(0,le.find)(e,["id",d])}),[d,n]),v=(0,i.useCallback)((0,le.debounce)((async e=>{try{var t,s;h(Xt),xa&&(null===(s=xa)||void 0===s||s.abort()),xa=new AbortController;const r=await Gt()({path:`/wp/v2/users?${(0,Rt.buildQueryString)({search:e,per_page:20})}`,signal:null===(t=xa)||void 0===t?void 0:t.signal});_((0,le.map)(r,"id")),c(r),h(es)}catch(e){if(e instanceof DOMException&&"AbortError"===e.name)return;_([]),h(ts),console.error(e.message)}}),200),[_,c,h]),E=(0,i.useCallback)((e=>{p(!0,!1),m(e)}),[m]),x=(0,i.useCallback)((e=>v(e.target.value)),[v]);return(0,i.useEffect)((()=>{v("")}),[]),(0,e.createElement)(l.AutocompleteField,{...u,name:t,id:s,value:b?d:0,onChange:E,placeholder:(0,Lt.__)("Select a user…","wordpress-seo"),selectedLabel:null==b?void 0:b.name,onQueryChange:x,className:r,...a},(0,e.createElement)(e.Fragment,null,(f===Qt||f===es)&&(0,e.createElement)(e.Fragment,null,(0,le.isEmpty)(y)?(0,e.createElement)(ka,null,(0,Lt.__)("No users found.","wordpress-seo")):(0,le.map)(y,(t=>{const s=null==n?void 0:n[t];return s?(0,e.createElement)(l.AutocompleteField.Option,{key:null==s?void 0:s.id,value:null==s?void 0:s.id},null==s?void 0:s.name):null})),w&&(0,e.createElement)("li",{className:"yst-sticky yst-inset-x-0 yst-bottom-0 yst-group"},(0,e.createElement)("a",{id:`link-create_user-${s}`,href:g,target:"_blank",rel:"noreferrer",className:"yst-relative yst-w-full yst-flex yst-items-center yst-py-4 yst-px-3 yst-gap-2 yst-no-underline yst-text-sm yst-text-start yst-bg-white yst-text-slate-700 group-hover:yst-text-white group-hover:yst-bg-primary-500 yst-border-t yst-border-slate-200"},(0,e.createElement)(Ea,{className:"yst-w-5 yst-h-5 yst-text-slate-400 group-hover:yst-text-white"}),(0,e.createElement)("span",null,(0,Lt.__)("Add new user…","wordpress-seo"))))),f===Xt&&(0,e.createElement)(ka,null,(0,e.createElement)(l.Spinner,{variant:"primary"}),(0,Lt.__)("Searching users…","wordpress-seo")),f===ts&&(0,e.createElement)(ka,{className:"yst-text-red-600"},(0,Lt.__)("Failed to retrieve users.","wordpress-seo"))))};Sa.propTypes={name:ir().string.isRequired,id:ir().string.isRequired,className:ir().string};const La=Sa,Ta=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"}))})),Fa=({children:t,className:s=""})=>(0,e.createElement)("div",{className:or()("yst-flex yst-items-center yst-justify-center yst-gap-2 yst-py-2 yst-px-3",s)},t);Fa.propTypes={children:ir().node.isRequired,className:ir().string};const $a=({name:t,id:s,...r})=>{const a=oa("selectPreference",[],"siteBasicsPolicies",{}),o=oa("selectPagesWith",[a],(0,le.values)(a)),{fetchPages:n}=sa(),[{value:c,...d},,{setTouched:u,setValue:p}]=ee({type:"select",name:t,id:s,...r}),[m,f]=(0,i.useState)(Qt),[h,y]=(0,i.useState)([]),_=oa("selectPreference",[],"canCreatePages",!1),w=oa("selectPreference",[],"createPageUrl",""),g=(0,i.useMemo)((()=>{const e=(0,le.values)(o);return(0,le.find)(e,["id",c])}),[c,o]),b=(0,i.useCallback)((0,le.debounce)((async e=>{try{f(Xt);const t=await n({search:e});y((0,le.map)(t.payload,"id")),f(es)}catch(e){if(e instanceof DOMException&&"AbortError"===e.name)return;y([]),f(ts)}}),200),[y,f,n]),v=(0,i.useCallback)((e=>{u(!0,!1),p(e)}),[p,u]),E=(0,i.useCallback)((e=>b(e.target.value)),[b]),x=(0,i.useMemo)((()=>(0,le.isEmpty)(h)?(0,le.map)(o,"id"):h),[h,o]),k=(0,i.useMemo)((()=>m===es&&(0,le.isEmpty)(h)),[h,m]);return(0,e.createElement)(l.AutocompleteField,{...d,name:t,id:s,value:g?c:0,onChange:v,placeholder:(0,Lt.__)("None","wordpress-seo"),selectedLabel:null==g?void 0:g.name,onQueryChange:E,nullable:!0
/* translators: Hidden accessibility text. */,clearButtonScreenReaderText:(0,Lt.__)("Clear selection","wordpress-seo"),...r},(0,e.createElement)(e.Fragment,null,(m===Qt||m===es)&&(0,e.createElement)(e.Fragment,null,k?(0,e.createElement)(Fa,null,(0,Lt.__)("No pages found.","wordpress-seo")):(0,le.map)(x,(t=>{const s=null==o?void 0:o[t];return s?(0,e.createElement)(l.AutocompleteField.Option,{key:null==s?void 0:s.id,value:null==s?void 0:s.id},null==s?void 0:s.name):null})),_&&(0,e.createElement)("li",{className:"yst-sticky yst-inset-x-0 yst-bottom-0 yst-group"},(0,e.createElement)("a",{id:`link-create_page-${s}`,href:w,target:"_blank",rel:"noreferrer",className:"yst-relative yst-w-full yst-flex yst-items-center yst-py-4 yst-px-3 yst-gap-2 yst-no-underline yst-text-sm yst-text-left yst-bg-white yst-text-slate-700 group-hover:yst-text-white group-hover:yst-bg-primary-500 yst-border-t yst-border-slate-200"},(0,e.createElement)(Ta,{className:"yst-w-5 yst-h-5 yst-text-slate-400 group-hover:yst-text-white"}),(0,e.createElement)("span",null,(0,Lt.__)("Add new page…","wordpress-seo"))))),m===Xt&&(0,e.createElement)(Fa,null,(0,e.createElement)(l.Spinner,{variant:"primary"}),(0,Lt.__)("Searching pages…","wordpress-seo")),m===ts&&(0,e.createElement)(Fa,{className:"yst-text-red-600"},(0,Lt.__)("Failed to retrieve pages.","wordpress-seo"))))};$a.propTypes={name:ir().string.isRequired,id:ir().string.isRequired};const Pa=$a,Ra=({as:t,transformValue:s=le.identity,...r})=>{const[a,,{setTouched:o,setValue:n}]=ee(r),l=(0,i.useCallback)((e=>{o(!0,!1),n(s(e))}),[r.name]);return(0,e.createElement)(t,{...a,onChange:l,...r})};Ra.propTypes={as:ir().elementType.isRequired,name:ir().string.isRequired,transformValue:ir().func};const Na=Ra,Oa=({name:t,id:s,options:r,...a})=>{const[o,,{setTouched:n,setValue:c}]=ee({type:"select",name:t,id:s,...a}),[d,u]=(0,i.useState)(""),p=e=>{var t;e&&null!==(t=r.find((t=>t.value===e)))&&void 0!==t&&t.label?u(r.find((t=>t.value===e)).label):u(e)},m=(0,i.useCallback)((e=>{c(e),p(e)}),[c,n]),f=(0,i.useCallback)((e=>{c(e.target.value),p(e.target.value)}),[c]);return(0,i.useEffect)((()=>{p(o.value)}),[]),(0,e.createElement)(l.AutocompleteField,{...o,name:t,id:s,selectedLabel:d,onChange:m,onQueryChange:f,...a},r&&r.map((t=>(0,e.createElement)(l.AutocompleteField.Option,{key:t.value,value:t.value},t.label))))};Oa.propTypes={name:ir().string.isRequired,id:ir().string.isRequired,options:ir().array},Oa.defaultProps={options:[]};const Ca=Oa,Aa=t=>{const s=({name:s,...r})=>{const{isDisabled:a,message:o}=ea({name:s});return a?(0,e.createElement)("div",null,(0,e.createElement)(l.Badge,{variant:"plain",size:"small",className:"yst-mb-2"},o),(0,e.createElement)(t,{name:s,...r,disabled:!0})):(0,e.createElement)(t,{name:s,...r})};return s.propTypes={name:ir().string.isRequired},s},Ia=t=>{const s=({name:s,isDummy:r=!1,...a})=>{const o=oa("selectDefaultSettingValue",[s],s);return r?(0,e.createElement)(t,{name:s,...a,disabled:!0,value:o,onChange:le.noop,checked:o,content:o}):(0,e.createElement)(t,{name:s,...a})};return s.propTypes={name:ir().string.isRequired,isDummy:ir().bool},s},Ma=t=>{const s=({name:s,isDummy:r=!1,...a})=>{const o=oa("selectDefaultSettingValue",[s],s);return r?(0,e.createElement)(t,{name:s,...a,disabled:!0,value:o,onChange:le.noop}):(0,e.createElement)(t,{name:s,...a})};return s.propTypes={name:ir().string.isRequired,isDummy:ir().bool},s},Da=t=>{const s=({name:s,...r})=>{const{isTouched:a,error:o}=(({name:e})=>{const{touched:t,errors:s}=q();return{isTouched:(0,i.useMemo)((()=>(0,le.get)(t,e,!1)),[t]),error:(0,i.useMemo)((()=>(0,le.get)(s,e,"")),[s])}})({name:s});return(0,e.createElement)(t,{name:s,validation:{variant:"error",message:a&&o},...r})};return s.propTypes={name:ir().string.isRequired},s},Ba=Da(te),Ua=(e,t="")=>(0,le.reduce)(e,((e,s,r)=>{const a=(0,le.join)((0,le.filter)([t,r],(0,le.flowRight)(Boolean,le.toString)),".");return(0,le.isObject)(s)||(0,le.isArray)(s)?{...e,...Ua(s,a)}:{...e,[a]:s}}),{}),ja=({id:t,onDismiss:s,...r})=>{const{errors:a}=q(),o=oa("selectSearchIndex"),n=(0,i.useMemo)((()=>Ua(a)),[a]);return(0,e.createElement)(l.Notifications.Notification,{key:t,id:t,onDismiss:s,...r},(0,e.createElement)("ul",{className:"yst-list-disc yst-mt-1 yst-ms-4 yst-space-y-2"},(0,le.map)(n,((t,s)=>t&&(0,e.createElement)("li",{key:s},(0,e.createElement)(Et,{to:`${(0,le.get)(o,`${s}.route`,"404")}#${(0,le.get)(o,`${s}.fieldId`,"")}`},`${(0,le.get)(o,`${s}.routeLabel`,"")} - ${(0,le.get)(o,`${s}.fieldLabel`,"")}`),": ",t)))))};ja.propTypes={id:ir().string.isRequired,onDismiss:ir().func};const Va=()=>{(()=>{const{isValid:e,errors:t,isSubmitting:s}=q(),{addNotification:r,removeNotification:a}=sa(),o=oa("selectNotification",[],"validation-errors");(0,i.useEffect)((()=>{e&&o&&a("validation-errors")}),[e,o]),(0,i.useEffect)((()=>{s&&!e&&r({id:"validation-errors",variant:"error",size:"large",title:(0,Lt.__)("Oh no! It seems your form contains invalid data. Please review the following fields:","wordpress-seo")})}),[s,t,e])})(),(()=>{const{removeNotification:e}=sa(),t=oa("selectNotifications"),s=oa("selectPostTypes"),r=oa("selectTaxonomies");(0,i.useEffect)((()=>{const a=(0,le.some)(s,["isNew",!0]),o=(0,le.some)(r,["isNew",!0]);!t["new-content-type"]||a||o||e("new-content-type")}),[s,r])})();const{removeNotification:t}=sa(),s=oa("selectNotifications"),r=(0,i.useMemo)((()=>(0,le.map)(s,(e=>({...e,onDismiss:t,autoDismiss:"success"===e.variant?5e3:null,
/* translators: Hidden accessibility text. */
dismissScreenReaderLabel:(0,Lt.__)("Dismiss","wordpress-seo")})))),[s]);return(0,e.createElement)(l.Notifications,{notifications:r,position:"bottom-left"},r.map((t=>"validation-errors"===t.id?(0,e.createElement)(ja,{key:t.id,...t}):(0,e.createElement)(l.Notifications.Notification,{key:t.id,...t}))))},za=({name:t,disabled:s=!1})=>{const r=oa("selectPreference",[],"isNewsSeoActive"),a=oa("selectLink",[],"https://yoa.st/get-news-settings"),{values:o}=q(),n=(0,i.useMemo)((()=>"NewsArticle"===(0,le.get)(o,`wpseo_titles.schema-article-type-${t}`,"")),[t,o]);return r?null:(0,e.createElement)(la.Z,{easing:"ease-in-out",duration:300,height:n&&!s?"auto":0,animateOpacity:!0},(0,e.createElement)(l.Alert,{className:"yst-mt-8",variant:"info",role:"status"},(0,Lt.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,Lt.__)("Are you publishing news articles? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")," ",(0,e.createElement)("a",{id:"link-get-news-seo",href:a,target:"_blank",rel:"noopener noreferrer"},(0,Lt.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,Lt.__)("Get the %s plugin now!","wordpress-seo"),"Yoast News SEO"))))};za.propTypes={name:ir().string.isRequired,disabled:ir().bool};const qa=za,Wa=({isEnabled:t,
/* translators: %1$s expands to an opening emphasis tag. %2$s expands to a closing emphasis tag. */
text:s=(0,Lt.__)("The %1$ssocial image%2$s, %1$ssocial title%2$s and %1$ssocial description%2$s require Open Graph data, which is currently disabled in the ‘Social sharing’ section in %3$sSite features%4$s.","wordpress-seo")})=>{const r=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(s,"<em>","</em>","<link>","</link>"),{em:(0,e.createElement)("em",null),link:(0,e.createElement)(Et,{to:"/site-features#section-social-sharing"})})),[]);return t?null:(0,e.createElement)(l.Alert,{variant:"info",className:"yst-mb-6"},r)};Wa.propTypes={isEnabled:ir().bool.isRequired,text:ir().string};const Ha=Wa;var Ya={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",whiteSpace:"nowrap",padding:0,width:"1px",position:"absolute"},Ga=function(e){var s=e.message,r=e["aria-live"];return t().createElement("div",{style:Ya,role:"log","aria-live":r},s||"")};Ga.propTypes={};const Ka=Ga;function Za(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var Ja=function(e){function s(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s);for(var a=arguments.length,o=Array(a),n=0;n<a;n++)o[n]=arguments[n];return t=r=Za(this,e.call.apply(e,[this].concat(o))),r.state={assertiveMessage1:"",assertiveMessage2:"",politeMessage1:"",politeMessage2:"",oldPolitemessage:"",oldPoliteMessageId:"",oldAssertiveMessage:"",oldAssertiveMessageId:"",setAlternatePolite:!1,setAlternateAssertive:!1},Za(r,t)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,e),s.getDerivedStateFromProps=function(e,t){var s=t.oldPolitemessage,r=t.oldPoliteMessageId,a=t.oldAssertiveMessage,o=t.oldAssertiveMessageId,n=e.politeMessage,i=e.politeMessageId,l=e.assertiveMessage,c=e.assertiveMessageId;return s!==n||r!==i?{politeMessage1:t.setAlternatePolite?"":n,politeMessage2:t.setAlternatePolite?n:"",oldPolitemessage:n,oldPoliteMessageId:i,setAlternatePolite:!t.setAlternatePolite}:a!==l||o!==c?{assertiveMessage1:t.setAlternateAssertive?"":l,assertiveMessage2:t.setAlternateAssertive?l:"",oldAssertiveMessage:l,oldAssertiveMessageId:c,setAlternateAssertive:!t.setAlternateAssertive}:null},s.prototype.render=function(){var e=this.state,s=e.assertiveMessage1,r=e.assertiveMessage2,a=e.politeMessage1,o=e.politeMessage2;return t().createElement("div",null,t().createElement(Ka,{"aria-live":"assertive",message:s}),t().createElement(Ka,{"aria-live":"assertive",message:r}),t().createElement(Ka,{"aria-live":"polite",message:a}),t().createElement(Ka,{"aria-live":"polite",message:o}))},s}(e.Component);Ja.propTypes={};const Qa=Ja;function Xa(){console.warn("Announcement failed, LiveAnnouncer context is missing")}const eo=t().createContext({announceAssertive:Xa,announcePolite:Xa}),to=function(e){function s(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,e.call(this,t));return r.announcePolite=function(e,t){r.setState({announcePoliteMessage:e,politeMessageId:t||""})},r.announceAssertive=function(e,t){r.setState({announceAssertiveMessage:e,assertiveMessageId:t||""})},r.state={announcePoliteMessage:"",politeMessageId:"",announceAssertiveMessage:"",assertiveMessageId:"",updateFunctions:{announcePolite:r.announcePolite,announceAssertive:r.announceAssertive}},r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(s,e),s.prototype.render=function(){var e=this.state,s=e.announcePoliteMessage,r=e.politeMessageId,a=e.announceAssertiveMessage,o=e.assertiveMessageId,n=e.updateFunctions;return t().createElement(eo.Provider,{value:n},this.props.children,t().createElement(Qa,{assertiveMessage:a,assertiveMessageId:o,politeMessage:s,politeMessageId:r}))},s}(e.Component);var so=s(3409),ro=s.n(so);function ao(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var oo=function(e){function t(){var s,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var a=arguments.length,o=Array(a),n=0;n<a;n++)o[n]=arguments[n];return s=r=ao(this,e.call.apply(e,[this].concat(o))),r.announce=function(){var e=r.props,t=e.message,s=e["aria-live"],a=e.announceAssertive,o=e.announcePolite;"assertive"===s&&a(t||"",ro()()),"polite"===s&&o(t||"",ro()())},ao(r,s)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t.prototype.componentDidMount=function(){this.announce()},t.prototype.componentDidUpdate=function(e){this.props.message!==e.message&&this.announce()},t.prototype.componentWillUnmount=function(){var e=this.props,t=e.clearOnUnmount,s=e.announceAssertive,r=e.announcePolite;!0!==t&&"true"!==t||(s(""),r(""))},t.prototype.render=function(){return null},t}(e.Component);oo.propTypes={};const no=oo;var io=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},lo=function(e){return t().createElement(eo.Consumer,null,(function(s){return t().createElement(no,io({},s,e))}))};lo.propTypes={};const co=lo;const uo=({children:t,title:s,description:r})=>{const a=(({prefix:e="",postfix:t=""}={})=>e+oa("selectPreference",[],"documentTitle")+t)({prefix:`${s} ‹ `}),o=(0,Lt.sprintf)(/* translators: 1: Settings' section title, 2: Yoast SEO */
(0,Lt.__)("%1$s Settings - %2$s","wordpress-seo"),s,"Yoast SEO");return(0,e.createElement)(to,null,(0,e.createElement)(co,{message:o,"aria-live":"polite"}),(0,e.createElement)(Gr.Helmet,null,(0,e.createElement)("title",null,a)),(0,e.createElement)("header",{className:"yst-p-8 yst-border-b yst-border-slate-200"},(0,e.createElement)("div",{className:"yst-max-w-screen-sm"},(0,e.createElement)(l.Title,null,s),r&&(0,e.createElement)("p",{className:"yst-text-tiny yst-mt-3"},r))),t)};uo.propTypes={children:ir().node.isRequired,title:ir().string.isRequired,description:ir().node};const po=uo;function mo(t,s){let[r,a]=(0,e.useState)(t),o=Fs(t);return Ls((()=>a(o.current)),[o,a,...s]),r}var fo;let ho=null!=(fo=e.useId)?fo:function(){let t=$s(),[s,r]=e.useState(t?()=>Ss.nextId():null);return Ls((()=>{null===s&&r(Ss.nextId())}),[s]),null!=s?""+s:void 0};function yo(e){return Ss.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let _o=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var wo,go=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(go||{}),bo=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(bo||{}),vo=((wo=vo||{})[wo.Previous=-1]="Previous",wo[wo.Next=1]="Next",wo),Eo=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Eo||{});function xo(t,s,r){let a=Fs(s);(0,e.useEffect)((()=>{function e(e){a.current(e)}return document.addEventListener(t,e,r),()=>document.removeEventListener(t,e,r)}),[t,r])}function ko(e){var t;if(e.type)return e.type;let s=null!=(t=e.as)?t:"button";return"string"==typeof s&&"button"===s.toLowerCase()?"button":void 0}function So(t,s){let[r,a]=(0,e.useState)((()=>ko(t)));return Ls((()=>{a(ko(t))}),[t.type,t.as]),Ls((()=>{r||!s.current||s.current instanceof HTMLButtonElement&&!s.current.hasAttribute("type")&&a("button")}),[r,s]),r}["textarea","input"].join(",");var Lo=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(Lo||{});function To(e={},t=null,s=[]){for(let[r,a]of Object.entries(e))$o(s,Fo(t,r),a);return s}function Fo(e,t){return e?e+"["+t+"]":t}function $o(e,t,s){if(Array.isArray(s))for(let[r,a]of s.entries())$o(e,Fo(t,r.toString()),a);else s instanceof Date?e.push([t,s.toISOString()]):"boolean"==typeof s?e.push([t,s?"1":"0"]):"string"==typeof s?e.push([t,s]):"number"==typeof s?e.push([t,`${s}`]):null==s?e.push([t,""]):To(s,t,e)}var Po=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Po||{});let Ro=ys((function(e,t){let{features:s=1,...r}=e;return ms({ourProps:{ref:t,"aria-hidden":2==(2&s)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...4==(4&s)&&2!=(2&s)&&{display:"none"}}},theirProps:r,slot:{},defaultTag:"div",name:"Hidden"})}));var No=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(No||{});function Oo(t,s){let r=(0,e.useRef)([]),a=Ps(t);(0,e.useEffect)((()=>{let e=[...r.current];for(let[t,o]of s.entries())if(r.current[t]!==o){let t=a(s,e);return r.current=s,t}}),[a,...s])}function Co(e){return[e.screenX,e.screenY]}var Ao=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ao||{}),Io=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(Io||{}),Mo=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Mo||{}),Do=(e=>(e[e.OpenCombobox=0]="OpenCombobox",e[e.CloseCombobox=1]="CloseCombobox",e[e.GoToOption=2]="GoToOption",e[e.RegisterOption=3]="RegisterOption",e[e.UnregisterOption=4]="UnregisterOption",e[e.RegisterLabel=5]="RegisterLabel",e))(Do||{});function Bo(e,t=(e=>e)){let s=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,r=function(e,t=(e=>e)){return e.slice().sort(((e,s)=>{let r=t(e),a=t(s);if(null===r||null===a)return 0;let o=r.compareDocumentPosition(a);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}(t(e.options.slice()),(e=>e.dataRef.current.domRef.current)),a=s?r.indexOf(s):null;return-1===a&&(a=null),{options:r,activeOptionIndex:a}}let Uo={1:e=>e.dataRef.current.disabled||1===e.comboboxState?e:{...e,activeOptionIndex:null,comboboxState:1},0(e){if(e.dataRef.current.disabled||0===e.comboboxState)return e;let t=e.activeOptionIndex,{isSelected:s}=e.dataRef.current,r=e.options.findIndex((e=>s(e.dataRef.current.value)));return-1!==r&&(t=r),{...e,comboboxState:0,activeOptionIndex:t}},2(e,t){var s;if(e.dataRef.current.disabled||e.dataRef.current.optionsRef.current&&!e.dataRef.current.optionsPropsRef.current.static&&1===e.comboboxState)return e;let r=Bo(e);if(null===r.activeOptionIndex){let e=r.options.findIndex((e=>!e.dataRef.current.disabled));-1!==e&&(r.activeOptionIndex=e)}let a=function(e,t){let s=t.resolveItems();if(s.length<=0)return null;let r=t.resolveActiveIndex(),a=null!=r?r:-1,o=(()=>{switch(e.focus){case 0:return s.findIndex((e=>!t.resolveDisabled(e)));case 1:{let e=s.slice().reverse().findIndex(((e,s,r)=>!(-1!==a&&r.length-s-1>=a||t.resolveDisabled(e))));return-1===e?e:s.length-1-e}case 2:return s.findIndex(((e,s)=>!(s<=a||t.resolveDisabled(e))));case 3:{let e=s.slice().reverse().findIndex((e=>!t.resolveDisabled(e)));return-1===e?e:s.length-1-e}case 4:return s.findIndex((s=>t.resolveId(s)===e.id));case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}})();return-1===o?r:o}(t,{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,activeOptionIndex:a,activationTrigger:null!=(s=t.trigger)?s:1}},3:(e,t)=>{let s={id:t.id,dataRef:t.dataRef},r=Bo(e,(e=>[...e,s]));null===e.activeOptionIndex&&e.dataRef.current.isSelected(t.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(s));let a={...e,...r,activationTrigger:1};return e.dataRef.current.__demoMode&&void 0===e.dataRef.current.value&&(a.activeOptionIndex=0),a},4:(e,t)=>{let s=Bo(e,(e=>{let s=e.findIndex((e=>e.id===t.id));return-1!==s&&e.splice(s,1),e}));return{...e,...s,activationTrigger:1}},5:(e,t)=>({...e,labelId:t.id})},jo=(0,e.createContext)(null);function Vo(t){let s=(0,e.useContext)(jo);if(null===s){let e=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,Vo),e}return s}jo.displayName="ComboboxActionsContext";let zo=(0,e.createContext)(null);function qo(t){let s=(0,e.useContext)(zo);if(null===s){let e=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,qo),e}return s}function Wo(e,t){return ls(t.type,Uo,e,t)}zo.displayName="ComboboxDataContext";let Ho=e.Fragment,Yo=ys((function(t,s){let{value:r,defaultValue:a,onChange:o,name:n,by:i=((e,t)=>e===t),disabled:l=!1,__demoMode:c=!1,nullable:d=!1,multiple:u=!1,...p}=t,[m=(u?[]:void 0),f]=function(t,s,r){let[a,o]=(0,e.useState)(r),n=void 0!==t,i=(0,e.useRef)(n),l=(0,e.useRef)(!1),c=(0,e.useRef)(!1);return!n||i.current||l.current?!n&&i.current&&!c.current&&(c.current=!0,i.current=n,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(l.current=!0,i.current=n,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[n?t:a,Ps((e=>(n||o(e),null==s?void 0:s(e))))]}(r,o,a),[h,y]=(0,e.useReducer)(Wo,{dataRef:(0,e.createRef)(),comboboxState:c?0:1,options:[],activeOptionIndex:null,activationTrigger:1,labelId:null}),_=(0,e.useRef)(!1),w=(0,e.useRef)({static:!1,hold:!1}),g=(0,e.useRef)(null),b=(0,e.useRef)(null),v=(0,e.useRef)(null),E=(0,e.useRef)(null),x=Ps("string"==typeof i?(e,t)=>{let s=i;return(null==e?void 0:e[s])===(null==t?void 0:t[s])}:i),k=(0,e.useCallback)((e=>ls(S.mode,{1:()=>m.some((t=>x(t,e))),0:()=>x(m,e)})),[m]),S=(0,e.useMemo)((()=>({...h,optionsPropsRef:w,labelRef:g,inputRef:b,buttonRef:v,optionsRef:E,value:m,defaultValue:a,disabled:l,mode:u?1:0,get activeOptionIndex(){if(_.current&&null===h.activeOptionIndex&&h.options.length>0){let e=h.options.findIndex((e=>!e.dataRef.current.disabled));if(-1!==e)return e}return h.activeOptionIndex},compare:x,isSelected:k,nullable:d,__demoMode:c})),[m,a,l,u,d,c,h]);Ls((()=>{h.dataRef.current=S}),[S]),function(t,s,r=!0){let a=(0,e.useRef)(!1);function o(e,r){if(!a.current||e.defaultPrevented)return;let o=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(t),n=r(e);if(null!==n&&n.getRootNode().contains(n)){for(let t of o){if(null===t)continue;let s=t instanceof HTMLElement?t:t.current;if(null!=s&&s.contains(n)||e.composed&&e.composedPath().includes(s))return}return!function(e,t=0){var s;return e!==(null==(s=yo(e))?void 0:s.body)&&ls(t,{0:()=>e.matches(_o),1(){let t=e;for(;null!==t;){if(t.matches(_o))return!0;t=t.parentElement}return!1}})}(n,Eo.Loose)&&-1!==n.tabIndex&&e.preventDefault(),s(e,n)}}(0,e.useEffect)((()=>{requestAnimationFrame((()=>{a.current=r}))}),[r]);let n=(0,e.useRef)(null);xo("mousedown",(e=>{var t,s;a.current&&(n.current=(null==(s=null==(t=e.composedPath)?void 0:t.call(e))?void 0:s[0])||e.target)}),!0),xo("click",(e=>{!n.current||(o(e,(()=>n.current)),n.current=null)}),!0),xo("blur",(e=>o(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}([S.buttonRef,S.inputRef,S.optionsRef],(()=>A.closeCombobox()),0===S.comboboxState);let L=(0,e.useMemo)((()=>({open:0===S.comboboxState,disabled:l,activeIndex:S.activeOptionIndex,activeOption:null===S.activeOptionIndex?null:S.options[S.activeOptionIndex].dataRef.current.value,value:m})),[S,l,m]),T=Ps((e=>{let t=S.options.find((t=>t.id===e));!t||C(t.dataRef.current.value)})),F=Ps((()=>{if(null!==S.activeOptionIndex){let{dataRef:e,id:t}=S.options[S.activeOptionIndex];C(e.current.value),A.goToOption(Lo.Specific,t)}})),$=Ps((()=>{y({type:0}),_.current=!0})),P=Ps((()=>{y({type:1}),_.current=!1})),R=Ps(((e,t,s)=>(_.current=!1,e===Lo.Specific?y({type:2,focus:Lo.Specific,id:t,trigger:s}):y({type:2,focus:e,trigger:s})))),N=Ps(((e,t)=>(y({type:3,id:e,dataRef:t}),()=>y({type:4,id:e})))),O=Ps((e=>(y({type:5,id:e}),()=>y({type:5,id:null})))),C=Ps((e=>ls(S.mode,{0:()=>null==f?void 0:f(e),1(){let t=S.value.slice(),s=t.findIndex((t=>x(t,e)));return-1===s?t.push(e):t.splice(s,1),null==f?void 0:f(t)}}))),A=(0,e.useMemo)((()=>({onChange:C,registerOption:N,registerLabel:O,goToOption:R,closeCombobox:P,openCombobox:$,selectActiveOption:F,selectOption:T})),[]),I=null===s?{}:{ref:s},M=(0,e.useRef)(null),D=Is();return(0,e.useEffect)((()=>{!M.current||void 0!==a&&D.addEventListener(M.current,"reset",(()=>{C(a)}))}),[M,C]),e.createElement(jo.Provider,{value:A},e.createElement(zo.Provider,{value:S},e.createElement(Es,{value:ls(S.comboboxState,{0:bs.Open,1:bs.Closed})},null!=n&&null!=m&&To({[n]:m}).map((([t,s],r)=>e.createElement(Ro,{features:Po.Hidden,ref:0===r?e=>{var t;M.current=null!=(t=null==e?void 0:e.closest("form"))?t:null}:void 0,..._s({key:t,as:"input",type:"hidden",hidden:!0,readOnly:!0,name:t,value:s})}))),ms({ourProps:I,theirProps:p,slot:L,defaultTag:Ho,name:"Combobox"}))))})),Go=ys((function(t,s){var r,a,o,n;let i=ho(),{id:l=`headlessui-combobox-input-${i}`,onChange:c,displayValue:d,type:u="text",...p}=t,m=qo("Combobox.Input"),f=Vo("Combobox.Input"),h=Ns(m.inputRef,s),y=(0,e.useRef)(!1),_=Is();var w;Oo((([e,t],[s,r])=>{y.current||!m.inputRef.current||(0===r&&1===t||e!==s)&&(m.inputRef.current.value=e)}),["function"==typeof d&&void 0!==m.value?null!=(w=d(m.value))?w:"":"string"==typeof m.value?m.value:"",m.comboboxState]),Oo((([e],[t])=>{if(0===e&&1===t){let e=m.inputRef.current;if(!e)return;let t=e.value,{selectionStart:s,selectionEnd:r,selectionDirection:a}=e;e.value="",e.value=t,null!==a?e.setSelectionRange(s,r,a):e.setSelectionRange(s,r)}}),[m.comboboxState]);let g=(0,e.useRef)(!1),b=Ps((()=>{g.current=!0})),v=Ps((()=>{setTimeout((()=>{g.current=!1}))})),E=Ps((e=>{switch(y.current=!0,e.key){case No.Backspace:case No.Delete:if(0!==m.mode||!m.nullable)return;let t=e.currentTarget;_.requestAnimationFrame((()=>{""===t.value&&(f.onChange(null),m.optionsRef.current&&(m.optionsRef.current.scrollTop=0),f.goToOption(Lo.Nothing))}));break;case No.Enter:if(y.current=!1,0!==m.comboboxState||g.current)return;if(e.preventDefault(),e.stopPropagation(),null===m.activeOptionIndex)return void f.closeCombobox();f.selectActiveOption(),0===m.mode&&f.closeCombobox();break;case No.ArrowDown:return y.current=!1,e.preventDefault(),e.stopPropagation(),ls(m.comboboxState,{0:()=>{f.goToOption(Lo.Next)},1:()=>{f.openCombobox()}});case No.ArrowUp:return y.current=!1,e.preventDefault(),e.stopPropagation(),ls(m.comboboxState,{0:()=>{f.goToOption(Lo.Previous)},1:()=>{f.openCombobox(),_.nextFrame((()=>{m.value||f.goToOption(Lo.Last)}))}});case No.Home:if(e.shiftKey)break;return y.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(Lo.First);case No.PageUp:return y.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(Lo.First);case No.End:if(e.shiftKey)break;return y.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(Lo.Last);case No.PageDown:return y.current=!1,e.preventDefault(),e.stopPropagation(),f.goToOption(Lo.Last);case No.Escape:return y.current=!1,0!==m.comboboxState?void 0:(e.preventDefault(),m.optionsRef.current&&!m.optionsPropsRef.current.static&&e.stopPropagation(),f.closeCombobox());case No.Tab:if(y.current=!1,0!==m.comboboxState)return;0===m.mode&&f.selectActiveOption(),f.closeCombobox()}})),x=Ps((e=>{f.openCombobox(),null==c||c(e)})),k=Ps((()=>{y.current=!1})),S=mo((()=>{if(m.labelId)return[m.labelId].join(" ")}),[m.labelId]),L=(0,e.useMemo)((()=>({open:0===m.comboboxState,disabled:m.disabled})),[m]);return ms({ourProps:{ref:h,id:l,role:"combobox",type:u,"aria-controls":null==(r=m.optionsRef.current)?void 0:r.id,"aria-expanded":m.disabled?void 0:0===m.comboboxState,"aria-activedescendant":null===m.activeOptionIndex||null==(a=m.options[m.activeOptionIndex])?void 0:a.id,"aria-multiselectable":1===m.mode||void 0,"aria-labelledby":S,"aria-autocomplete":"list",defaultValue:null!=(n=null!=(o=t.defaultValue)?o:void 0!==m.defaultValue?null==d?void 0:d(m.defaultValue):null)?n:m.defaultValue,disabled:m.disabled,onCompositionStart:b,onCompositionEnd:v,onKeyDown:E,onChange:x,onBlur:k},theirProps:p,slot:L,defaultTag:"input",name:"Combobox.Input"})})),Ko=ys((function(t,s){var r;let a=qo("Combobox.Button"),o=Vo("Combobox.Button"),n=Ns(a.buttonRef,s),i=ho(),{id:l=`headlessui-combobox-button-${i}`,...c}=t,d=Is(),u=Ps((e=>{switch(e.key){case No.ArrowDown:return e.preventDefault(),e.stopPropagation(),1===a.comboboxState&&o.openCombobox(),d.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})}));case No.ArrowUp:return e.preventDefault(),e.stopPropagation(),1===a.comboboxState&&(o.openCombobox(),d.nextFrame((()=>{a.value||o.goToOption(Lo.Last)}))),d.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})}));case No.Escape:return 0!==a.comboboxState?void 0:(e.preventDefault(),a.optionsRef.current&&!a.optionsPropsRef.current.static&&e.stopPropagation(),o.closeCombobox(),d.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})})));default:return}})),p=Ps((e=>{if(function(e){let t=e.parentElement,s=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(s=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(s))&&r}(e.currentTarget))return e.preventDefault();0===a.comboboxState?o.closeCombobox():(e.preventDefault(),o.openCombobox()),d.nextFrame((()=>{var e;return null==(e=a.inputRef.current)?void 0:e.focus({preventScroll:!0})}))})),m=mo((()=>{if(a.labelId)return[a.labelId,l].join(" ")}),[a.labelId,l]),f=(0,e.useMemo)((()=>({open:0===a.comboboxState,disabled:a.disabled,value:a.value})),[a]);return ms({ourProps:{ref:n,id:l,type:So(t,a.buttonRef),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":null==(r=a.optionsRef.current)?void 0:r.id,"aria-expanded":a.disabled?void 0:0===a.comboboxState,"aria-labelledby":m,disabled:a.disabled,onClick:p,onKeyDown:u},theirProps:c,slot:f,defaultTag:"button",name:"Combobox.Button"})})),Zo=ys((function(t,s){let r=ho(),{id:a=`headlessui-combobox-label-${r}`,...o}=t,n=qo("Combobox.Label"),i=Vo("Combobox.Label"),l=Ns(n.labelRef,s);Ls((()=>i.registerLabel(a)),[a]);let c=Ps((()=>{var e;return null==(e=n.inputRef.current)?void 0:e.focus({preventScroll:!0})})),d=(0,e.useMemo)((()=>({open:0===n.comboboxState,disabled:n.disabled})),[n]);return ms({ourProps:{ref:l,id:a,onClick:c},theirProps:o,slot:d,defaultTag:"label",name:"Combobox.Label"})})),Jo=us.RenderStrategy|us.Static,Qo=ys((function(t,s){let r=ho(),{id:a=`headlessui-combobox-options-${r}`,hold:o=!1,...n}=t,i=qo("Combobox.Options"),l=Ns(i.optionsRef,s),c=vs(),d=null!==c?c===bs.Open:0===i.comboboxState;Ls((()=>{var e;i.optionsPropsRef.current.static=null!=(e=t.static)&&e}),[i.optionsPropsRef,t.static]),Ls((()=>{i.optionsPropsRef.current.hold=o}),[i.optionsPropsRef,o]),function({container:t,accept:s,walk:r,enabled:a=!0}){let o=(0,e.useRef)(s),n=(0,e.useRef)(r);(0,e.useEffect)((()=>{o.current=s,n.current=r}),[s,r]),Ls((()=>{if(!t||!a)return;let e=yo(t);if(!e)return;let s=o.current,r=n.current,i=Object.assign((e=>s(e)),{acceptNode:s}),l=e.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,i,!1);for(;l.nextNode();)r(l.currentNode)}),[t,a,o,n])}({container:i.optionsRef.current,enabled:0===i.comboboxState,accept:e=>"option"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let u=mo((()=>{var e,t;return null!=(t=i.labelId)?t:null==(e=i.buttonRef.current)?void 0:e.id}),[i.labelId,i.buttonRef.current]);return ms({ourProps:{"aria-labelledby":u,role:"listbox",id:a,ref:l},theirProps:n,slot:(0,e.useMemo)((()=>({open:0===i.comboboxState})),[i]),defaultTag:"ul",features:Jo,visible:d,name:"Combobox.Options"})})),Xo=ys((function(t,s){var r,a;let o=ho(),{id:n=`headlessui-combobox-option-${o}`,disabled:i=!1,value:l,...c}=t,d=qo("Combobox.Option"),u=Vo("Combobox.Option"),p=null!==d.activeOptionIndex&&d.options[d.activeOptionIndex].id===n,m=d.isSelected(l),f=(0,e.useRef)(null),h=Fs({disabled:i,value:l,domRef:f,textValue:null==(a=null==(r=f.current)?void 0:r.textContent)?void 0:a.toLowerCase()}),y=Ns(s,f),_=Ps((()=>u.selectOption(n)));Ls((()=>u.registerOption(n,h)),[h,n]);let w=(0,e.useRef)(!d.__demoMode);Ls((()=>{if(!d.__demoMode)return;let e=Os();return e.requestAnimationFrame((()=>{w.current=!0})),e.dispose}),[]),Ls((()=>{if(0!==d.comboboxState||!p||!w.current||0===d.activationTrigger)return;let e=Os();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=f.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[f,p,d.comboboxState,d.activationTrigger,d.activeOptionIndex]);let g=Ps((e=>{if(i)return e.preventDefault();_(),0===d.mode&&u.closeCombobox()})),b=Ps((()=>{if(i)return u.goToOption(Lo.Nothing);u.goToOption(Lo.Specific,n)})),v=function(){let t=(0,e.useRef)([-1,-1]);return{wasMoved(e){let s=Co(e);return(t.current[0]!==s[0]||t.current[1]!==s[1])&&(t.current=s,!0)},update(e){t.current=Co(e)}}}(),E=Ps((e=>v.update(e))),x=Ps((e=>{!v.wasMoved(e)||i||p||u.goToOption(Lo.Specific,n,0)})),k=Ps((e=>{!v.wasMoved(e)||i||!p||d.optionsPropsRef.current.hold||u.goToOption(Lo.Nothing)})),S=(0,e.useMemo)((()=>({active:p,selected:m,disabled:i})),[p,m,i]);return ms({ourProps:{id:n,ref:y,role:"option",tabIndex:!0===i?void 0:-1,"aria-disabled":!0===i||void 0,"aria-selected":m,disabled:void 0,onClick:g,onFocus:b,onPointerEnter:E,onMouseEnter:E,onPointerMove:x,onMouseMove:x,onPointerLeave:k,onMouseLeave:k},theirProps:c,slot:S,defaultTag:"li",name:"Combobox.Option"})})),en=Object.assign(Yo,{Input:Go,Button:Ko,Label:Zo,Options:Qo,Option:Xo});const tn=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"}))})),sn=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"}))}));function rn(){return rn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},rn.apply(this,arguments)}function an(e,t){(null==t||t>e.length)&&(t=e.length);for(var s=0,r=new Array(t);s<t;s++)r[s]=e[s];return r}s(5893);var on=["shift","alt","meta","mod"],nn={esc:"escape",return:"enter",left:"arrowleft",up:"arrowup",right:"arrowright",down:"arrowdown",1:"digit1",2:"digit2",3:"digit3",4:"digit4",5:"digit5",6:"digit6",7:"digit7",8:"digit8",9:"digit9"};function ln(e,t){return void 0===t&&(t=","),"string"==typeof e?e.split(t):e}function cn(e,t){void 0===t&&(t="+");var s=e.toLocaleLowerCase().split(t).map((function(e){return e.trim()})).map((function(e){return nn[e]||e}));return rn({},{alt:s.includes("alt"),shift:s.includes("shift"),meta:s.includes("meta"),mod:s.includes("mod")},{keys:s.filter((function(e){return!on.includes(e)}))})}function dn(e,t){var s=e.target;void 0===t&&(t=!1);var r=s&&s.tagName;return t instanceof Array?Boolean(r&&t&&t.some((function(e){return e.toLowerCase()===r.toLowerCase()}))):Boolean(r&&t&&!0===t)}var un=(0,e.createContext)(void 0),pn=(0,e.createContext)({hotkeys:[],enabledScopes:[],toggleScope:function(){},enableScope:function(){},disableScope:function(){}});function mn(e,t){return e&&t&&"object"==typeof e&&"object"==typeof t?Object.keys(e).length===Object.keys(t).length&&Object.keys(e).reduce((function(s,r){return s&&mn(e[r],t[r])}),!0):e===t}var fn=function(e){e.stopPropagation(),e.preventDefault(),e.stopImmediatePropagation()},hn="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,yn=new Set;function wn(t,s,r,a){var o=(0,e.useRef)(null),n=r instanceof Array?a instanceof Array?void 0:a:r,i=r instanceof Array?r:a instanceof Array?a:[],l=(0,e.useCallback)(s,[].concat(i)),c=function(t){var s=(0,e.useRef)(void 0);return mn(s.current,t)||(s.current=t),s.current}(n),d=(0,e.useContext)(pn).enabledScopes,u=(0,e.useContext)(un);return hn((function(){if(!1!==(null==c?void 0:c.enabled)&&(e=d,s=null==c?void 0:c.scopes,0===e.length&&s?(console.warn('A hotkey has the "scopes" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'),1):!s||e.some((function(e){return s.includes(e)}))||e.includes("*"))){var e,s,r=function(e){var s;dn(e,["input","textarea","select"])&&!dn(e,null==c?void 0:c.enableOnFormTags)||(null===o.current||document.activeElement===o.current||o.current.contains(document.activeElement)?(null==(s=e.target)||!s.isContentEditable||null!=c&&c.enableOnContentEditable)&&ln(t,null==c?void 0:c.splitKey).forEach((function(t){var s,r=cn(t,null==c?void 0:c.combinationKey);if(function(e,t,s){var r=t.alt,a=t.meta,o=t.mod,n=t.shift,i=t.keys,l=e.altKey,c=e.ctrlKey,d=e.metaKey,u=e.shiftKey,p=e.key,m=e.code.toLowerCase().replace("key",""),f=p.toLowerCase();if(l!==r&&"alt"!==f)return!1;if(u!==n&&"shift"!==f)return!1;if(o){if(!d&&!c)return!1}else if(d!==a&&c!==a&&"meta"!==m&&"ctrl"!==m)return!1;return!(!i||1!==i.length||!i.includes(f)&&!i.includes(m))||(i?i.every((function(e){return s.has(e)})):!i)}(e,r,yn)||null!=(s=r.keys)&&s.includes("*")){if(function(e,t,s){("function"==typeof s&&s(e,t)||!0===s)&&e.preventDefault()}(e,r,null==c?void 0:c.preventDefault),!function(e,t,s){return"function"==typeof s?s(e,t):!0===s||void 0===s}(e,r,null==c?void 0:c.enabled))return void fn(e);l(e,r)}})):fn(e))},a=function(e){void 0!==e.key&&(yn.add(e.key.toLowerCase()),(void 0===(null==c?void 0:c.keydown)&&!0!==(null==c?void 0:c.keyup)||null!=c&&c.keydown)&&r(e))},n=function(e){void 0!==e.key&&("meta"!==e.key.toLowerCase()?yn.delete(e.key.toLowerCase()):yn.clear(),null!=c&&c.keyup&&r(e))};return(o.current||document).addEventListener("keyup",n),(o.current||document).addEventListener("keydown",a),u&&ln(t,null==c?void 0:c.splitKey).forEach((function(e){return u.addHotkey(cn(e,null==c?void 0:c.combinationKey))})),function(){(o.current||document).removeEventListener("keyup",n),(o.current||document).removeEventListener("keydown",a),u&&ln(t,null==c?void 0:c.splitKey).forEach((function(e){return u.removeHotkey(cn(e,null==c?void 0:c.combinationKey))}))}}}),[t,l,c,d]),o}var gn=new Set;"undefined"!=typeof window&&window.addEventListener("DOMContentLoaded",(function(){document.addEventListener("keydown",(function(e){var t;void 0!==e.key&&(t=e.key,(Array.isArray(t)?t:[t]).forEach((function(e){return gn.add(cn(e))})))})),document.addEventListener("keyup",(function(e){var t;void 0!==e.key&&(t=e.key,(Array.isArray(t)?t:[t]).forEach((function(e){for(var t,s=cn(e),r=function(e,t){var s="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(s)return(s=s.call(e)).next.bind(s);if(Array.isArray(e)||(s=function(e,t){if(e){if("string"==typeof e)return an(e,t);var s=Object.prototype.toString.call(e).slice(8,-1);return"Object"===s&&e.constructor&&(s=e.constructor.name),"Map"===s||"Set"===s?Array.from(e):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?an(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){s&&(e=s);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(gn);!(t=r()).done;){var a,o=t.value;null!=(a=o.keys)&&a.every((function(e){var t;return null==(t=s.keys)?void 0:t.includes(e)}))&&gn.delete(o)}})))}))}));const bn=(e,t)=>{try{return e.toLocaleLowerCase(t)}catch(t){return console.error(t.message),e}},vn=({name:e,label:t,route:s,hasArchive:r},{userLocale:a})=>({[`title-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-title-${e}`,fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[]},[`metadesc-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-metadesc-${e}`,fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[]},[`noindex-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-noindex-${e}`,fieldLabel:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. Posts.
(0,Lt.__)("Show %1$s in search results","wordpress-seo"),bn(t,a)),keywords:[]},[`display-metabox-pt-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-display-metabox-pt-${e}`,fieldLabel:(0,Lt.__)("Enable SEO controls and assessments","wordpress-seo"),keywords:[]},[`schema-page-type-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-schema-page-type-${e}`,fieldLabel:(0,Lt.__)("Page type","wordpress-seo"),keywords:[(0,Lt.__)("Schema","wordpress-seo"),(0,Lt.__)("Structured data","wordpress-seo")]},[`schema-article-type-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-schema-article-type-${e}`,fieldLabel:(0,Lt.__)("Article type","wordpress-seo"),keywords:[(0,Lt.__)("Schema","wordpress-seo"),(0,Lt.__)("Structured data","wordpress-seo")]},[`page-analyse-extra-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-page-analyse-extra-${e}`,fieldLabel:(0,Lt.__)("Add custom fields to page analysis","wordpress-seo"),keywords:[]},..."attachment"!==e&&{[`social-title-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-social-title-${e}`,fieldLabel:(0,Lt.__)("Social title","wordpress-seo"),keywords:[]},[`social-description-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-social-description-${e}`,fieldLabel:(0,Lt.__)("Social description","wordpress-seo"),keywords:[]},[`social-image-id-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`button-wpseo_titles-social-image-${e}-preview`,fieldLabel:(0,Lt.__)("Social image","wordpress-seo"),keywords:[]}},...r&&{[`title-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-title-ptarchive-${e}`,fieldLabel:(0,Lt.__)("Archive SEO title","wordpress-seo"),keywords:[]},[`metadesc-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-metadesc-ptarchive-${e}`,fieldLabel:(0,Lt.__)("Archive meta description","wordpress-seo"),keywords:[]},[`bctitle-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-bctitle-ptarchive-${e}`,fieldLabel:(0,Lt.__)("Archive breadcrumbs title","wordpress-seo"),keywords:[]},[`noindex-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-noindex-ptarchive-${e}`,fieldLabel:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. Posts.
(0,Lt.__)("Show the archive for %1$s in search results","wordpress-seo"),bn(t,a)),keywords:[]},[`social-title-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-social-title-ptarchive-${e}`,fieldLabel:(0,Lt.__)("Archive social title","wordpress-seo"),keywords:[]},[`social-description-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-social-description-ptarchive-${e}`,fieldLabel:(0,Lt.__)("Archive social description","wordpress-seo"),keywords:[]},[`social-image-id-ptarchive-${e}`]:{route:`/post-type/${s}`,routeLabel:t,fieldId:`button-wpseo_titles-social-image-ptarchive-${e}-preview`,fieldLabel:(0,Lt.__)("Archive social image","wordpress-seo"),keywords:[]}}}),En=({name:e,label:t,route:s},{userLocale:r})=>({[`title-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-title-tax-${e}`,fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[]},[`metadesc-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-metadesc-tax-${e}`,fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[]},[`display-metabox-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-display-metabox-tax-${e}`,fieldLabel:(0,Lt.sprintf)(/* translators: %1$s expands to "Yoast SEO". %2$s expands to the taxonomy plural, e.g. Categories. */
(0,Lt.__)("Enable %1$s for %2$s","wordpress-seo"),"Yoast SEO",bn(t,r)),keywords:[]},[`display-metabox-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-display-metabox-tax-${e}`,fieldLabel:(0,Lt.__)("Enable SEO controls and assessments","wordpress-seo"),keywords:[]},[`noindex-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-noindex-tax-${e}`,fieldLabel:(0,Lt.sprintf)(
// translators: %1$s expands to the taxonomy plural, e.g. Categories.
(0,Lt.__)("Show %1$s in search results","wordpress-seo"),bn(t,r)),keywords:[]},[`social-title-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-social-title-tax-${e}`,fieldLabel:(0,Lt.__)("Social title","wordpress-seo"),keywords:[]},[`social-description-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`input-wpseo_titles-social-description-tax-${e}`,fieldLabel:(0,Lt.__)("Social description","wordpress-seo"),keywords:[]},[`social-image-id-tax-${e}`]:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:`button-wpseo_titles-social-image-tax-${e}-preview`,fieldLabel:(0,Lt.__)("Social image","wordpress-seo"),keywords:[]},..."category"===e&&{stripcategorybase:{route:`/taxonomy/${s}`,routeLabel:t,fieldId:"input-wpseo_titles-stripcategorybase",fieldLabel:(0,Lt.__)("Show the categories prefix in the slug","wordpress-seo"),keywords:[]}}}),xn=(e,t,{userLocale:s}={})=>({blogdescription:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-blogdescription",fieldLabel:(0,Lt.__)("Tagline","wordpress-seo"),keywords:[]},wpseo:{keyword_analysis_active:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-keyword_analysis_active",fieldLabel:(0,Lt.__)("SEO analysis","wordpress-seo"),keywords:[]},content_analysis_active:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-content_analysis_active",fieldLabel:(0,Lt.__)("Readability analysis","wordpress-seo"),keywords:[]},inclusive_language_analysis_active:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-inclusive_language_analysis_active",fieldLabel:(0,Lt.__)("Inclusive language analysis","wordpress-seo"),keywords:[]},enable_metabox_insights:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_metabox_insights",fieldLabel:(0,Lt.__)("Insights","wordpress-seo"),keywords:[]},enable_cornerstone_content:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_cornerstone_content",fieldLabel:(0,Lt.__)("Cornerstone content","wordpress-seo"),keywords:[]},enable_text_link_counter:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_text_link_counter",fieldLabel:(0,Lt.__)("Text link counter","wordpress-seo"),keywords:[]},enable_link_suggestions:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_link_suggestions",fieldLabel:(0,Lt.__)("Link suggestions","wordpress-seo"),keywords:[]},enable_enhanced_slack_sharing:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_enhanced_slack_sharing",fieldLabel:(0,Lt.__)("Slack sharing","wordpress-seo"),keywords:[(0,Lt.__)("Share","wordpress-seo")]},enable_admin_bar_menu:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_admin_bar_menu",fieldLabel:(0,Lt.__)("Admin bar menu","wordpress-seo"),keywords:[]},enable_headless_rest_endpoints:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_headless_rest_endpoints",fieldLabel:(0,Lt.__)("REST API endpoint","wordpress-seo"),keywords:[]},enable_xml_sitemap:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_xml_sitemap",fieldLabel:(0,Lt.__)("XML sitemaps","wordpress-seo"),keywords:[]},enable_index_now:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_index_now",fieldLabel:(0,Lt.__)("IndexNow","wordpress-seo"),keywords:[(0,Lt.__)("Index Now","wordpress-seo")]},enable_ai_generator:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_ai_generator",fieldLabel:(0,Lt.__)("AI title & description generator","wordpress-seo"),keywords:[(0,Lt.__)("AI generator","wordpress-seo"),(0,Lt.__)("Artificial intelligence","wordpress-seo"),(0,Lt.__)("SEO title","wordpress-seo"),(0,Lt.__)("meta title","wordpress-seo"),(0,Lt.__)("meta description","wordpress-seo"),(0,Lt.__)("suggestions","wordpress-seo")]},disableadvanced_meta:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo-disableadvanced_meta",fieldLabel:(0,Lt.__)("Restrict advanced settings for authors","wordpress-seo"),keywords:[]},tracking:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo-tracking",fieldLabel:(0,Lt.__)("Usage tracking","wordpress-seo"),keywords:[]},publishing_principles_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-publishing_principles_id",fieldLabel:(0,Lt.__)("Publishing principles","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},ownership_funding_info_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-ownership_funding_info_id",fieldLabel:(0,Lt.__)("Ownership / Funding info","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},actionable_feedback_policy_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-actionable_feedback_policy_id",fieldLabel:(0,Lt.__)("Actionable feedback policy","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},corrections_policy_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-corrections_policy_id",fieldLabel:(0,Lt.__)("Corrections policy","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},ethics_policy_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-ethics_policy_id",fieldLabel:(0,Lt.__)("Ethics policy","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},diversity_policy_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-diversity_policy_id",fieldLabel:(0,Lt.__)("Diversity policy","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},diversity_staffing_report_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-diversity_staffing_report_id",fieldLabel:(0,Lt.__)("Diversity staffing report","wordpress-seo"),keywords:[(0,Lt.__)("Publishing policies","wordpress-seo")]},baiduverify:{route:"/site-connections",routeLabel:(0,Lt.__)("Site connections","wordpress-seo"),fieldId:"input-wpseo-baiduverify",fieldLabel:(0,Lt.__)("Baidu","wordpress-seo"),keywords:[(0,Lt.__)("Webmaster","wordpress-seo")]},googleverify:{route:"/site-connections",routeLabel:(0,Lt.__)("Site connections","wordpress-seo"),fieldId:"input-wpseo-googleverify",fieldLabel:(0,Lt.__)("Google","wordpress-seo"),keywords:[(0,Lt.__)("Webmaster","wordpress-seo"),(0,Lt.__)("Google search console","wordpress-seo"),"gsc"]},msverify:{route:"/site-connections",routeLabel:(0,Lt.__)("Site connections","wordpress-seo"),fieldId:"input-wpseo-msverify",fieldLabel:(0,Lt.__)("Bing","wordpress-seo"),keywords:[(0,Lt.__)("Webmaster","wordpress-seo")]},yandexverify:{route:"/site-connections",routeLabel:(0,Lt.__)("Site connections","wordpress-seo"),fieldId:"input-wpseo-yandexverify",fieldLabel:(0,Lt.__)("Yandex","wordpress-seo"),keywords:[(0,Lt.__)("Webmaster","wordpress-seo")]},remove_shortlinks:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_shortlinks",fieldLabel:(0,Lt.__)("Remove shortlinks","wordpress-seo"),keywords:[]},remove_rest_api_links:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_rest_api_links",fieldLabel:(0,Lt.__)("Remove REST API links","wordpress-seo"),keywords:[]},remove_rsd_wlw_links:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_rsd_wlw_links",fieldLabel:(0,Lt.__)("Remove RSD / WLW links","wordpress-seo"),keywords:[]},remove_oembed_links:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_oembed_links",fieldLabel:(0,Lt.__)("Remove oEmbed links","wordpress-seo"),keywords:[]},remove_generator:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_generator",fieldLabel:(0,Lt.__)("Remove generator tag","wordpress-seo"),keywords:[]},remove_pingback_header:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_pingback_header",fieldLabel:(0,Lt.__)("Pingback HTTP header","wordpress-seo"),keywords:[]},remove_powered_by_header:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_powered_by_header",fieldLabel:(0,Lt.__)("Remove powered by HTTP header","wordpress-seo"),keywords:[]},remove_feed_global:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_global",fieldLabel:(0,Lt.__)("Remove global feed","wordpress-seo"),keywords:[]},remove_feed_global_comments:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_global_comments",fieldLabel:(0,Lt.__)("Remove global comment feeds","wordpress-seo"),keywords:[]},remove_feed_post_comments:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_post_comments",fieldLabel:(0,Lt.__)("Remove post comments feeds","wordpress-seo"),keywords:[]},remove_feed_authors:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_authors",fieldLabel:(0,Lt.__)("Remove post authors feeds","wordpress-seo"),keywords:[]},remove_feed_post_types:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_post_types",fieldLabel:(0,Lt.__)("Remove post type feeds","wordpress-seo"),keywords:[]},remove_feed_categories:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_categories",fieldLabel:(0,Lt.__)("Remove category feeds","wordpress-seo"),keywords:[]},remove_feed_tags:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_tags",fieldLabel:(0,Lt.__)("Remove tag feeds","wordpress-seo"),keywords:[]},remove_feed_custom_taxonomies:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_custom_taxonomies",fieldLabel:(0,Lt.__)("Remove custom taxonomy feeds","wordpress-seo"),keywords:[]},remove_feed_search:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_feed_search",fieldLabel:(0,Lt.__)("Remove search results feeds","wordpress-seo"),keywords:[]},remove_atom_rdf_feeds:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_atom_rdf_feeds",fieldLabel:(0,Lt.__)("Remove Atom/RDF feeds","wordpress-seo"),keywords:[]},remove_emoji_scripts:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-remove_emoji_scripts",fieldLabel:(0,Lt.__)("Remove emoji scripts","wordpress-seo"),keywords:[]},deny_wp_json_crawling:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-deny_wp_json_crawling",fieldLabel:(0,Lt.__)("Remove WP-JSON API","wordpress-seo"),keywords:["robots"]},deny_adsbot_crawling:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-deny_adsbot_crawling",fieldLabel:(0,Lt.__)("Prevent Google AdsBot from crawling","wordpress-seo"),keywords:["robots"]},deny_ccbot_crawling:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-deny_ccbot_crawling",fieldLabel:(0,Lt.__)("Prevent Common Crawl CCBot from crawling","wordpress-seo"),keywords:["robots"]},deny_google_extended_crawling:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-deny_google_extended_crawling",fieldLabel:(0,Lt.__)("Prevent Google Gemini and Vertex AI bots from crawling","wordpress-seo"),keywords:["robots"]},deny_gptbot_crawling:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-deny_gptbot_crawling",fieldLabel:(0,Lt.__)("Prevent OpenAI GPTBot from crawling","wordpress-seo"),keywords:["robots","chatgpt"]},search_cleanup:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-search_cleanup",fieldLabel:(0,Lt.__)("Filter search terms","wordpress-seo"),keywords:[]},search_character_limit:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-search_character_limit",fieldLabel:(0,Lt.__)("Max number of characters to allow in searches","wordpress-seo"),keywords:[]},search_cleanup_emoji:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-search_cleanup_emoji",fieldLabel:(0,Lt.__)("Filter searches with emojis and other special characters","wordpress-seo"),keywords:[]},search_cleanup_patterns:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-search_cleanup_patterns",fieldLabel:(0,Lt.__)("Filter searches with common spam patterns","wordpress-seo"),keywords:[]},redirect_search_pretty_urls:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-redirect_search_pretty_urls",fieldLabel:(0,Lt.__)("Redirect pretty URLs to ‘raw’ formats","wordpress-seo"),keywords:[]},deny_search_crawling:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-deny_search_crawling",fieldLabel:(0,Lt.__)("Prevent crawling of internal site search URLs","wordpress-seo"),keywords:["robots"]},clean_campaign_tracking_urls:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-clean_campaign_tracking_urls",fieldLabel:(0,Lt.__)("Optimize Google Analytics utm tracking parameters","wordpress-seo"),keywords:[]},clean_permalinks:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-clean_permalinks",fieldLabel:(0,Lt.__)("Remove unregistered URL parameters","wordpress-seo"),keywords:[]},clean_permalinks_extra_variables:{route:"/crawl-optimization",routeLabel:(0,Lt.__)("Crawl optimization","wordpress-seo"),fieldId:"input-wpseo-clean_permalinks_extra_variables",fieldLabel:(0,Lt.__)("Additional URL parameters to allow","wordpress-seo"),keywords:[]},enable_llms_txt:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo-enable_llms_txt",fieldLabel:(0,Lt.__)("llms.txt","wordpress-seo"),keywords:[]}},wpseo_titles:{website_name:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-website_name",fieldLabel:(0,Lt.__)("Website name","wordpress-seo"),keywords:[]},alternate_website_name:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-alternate_website_name",fieldLabel:(0,Lt.__)("Alternate website name","wordpress-seo"),keywords:[]},forcerewritetitles:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-forcerewritetitle",fieldLabel:(0,Lt.__)("Force rewrite titles","wordpress-seo"),keywords:[]},separator:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"input-wpseo_titles-separator-sc-dash",fieldLabel:(0,Lt.__)("Title separator","wordpress-seo"),keywords:[(0,Lt.__)("Divider","wordpress-seo")]},company_or_person:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-company_or_person-company",fieldLabel:(0,Lt.__)("Organization/person","wordpress-seo"),keywords:[(0,Lt.__)("Schema","wordpress-seo"),(0,Lt.__)("Structured data","wordpress-seo")]},company_name:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-company_name",fieldLabel:(0,Lt.__)("Organization name","wordpress-seo"),keywords:[]},company_alternate_name:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-company_alternate_name",fieldLabel:(0,Lt.__)("Alternate organization name","wordpress-seo"),keywords:[]},company_logo_id:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"button-wpseo_titles-company_logo-preview",fieldLabel:(0,Lt.__)("Organization logo","wordpress-seo"),keywords:[(0,Lt.__)("Image","wordpress-seo")]},company_or_person_user_id:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-company_or_person_user_id",fieldLabel:(0,Lt.__)("User","wordpress-seo"),keywords:[]},person_logo_id:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"button-wpseo_titles-person_logo-preview",fieldLabel:(0,Lt.__)("Personal logo or avatar","wordpress-seo"),keywords:[(0,Lt.__)("Image","wordpress-seo")]},organization_description:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-description",fieldLabel:(0,Lt.__)("Organization description","wordpress-seo"),keywords:[]},organization_email:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-email",fieldLabel:(0,Lt.__)("Organization email address","wordpress-seo"),keywords:[]},organization_phone:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-phone",fieldLabel:(0,Lt.__)("Organization phone number","wordpress-seo"),keywords:[]},organization_legal_name:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-legal-name",fieldLabel:(0,Lt.__)("Organization's legal name","wordpress-seo"),keywords:[]},organization_funding_date:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-founding-date",fieldLabel:(0,Lt.__)("Organization's founding date","wordpress-seo"),keywords:[]},organization_number_employees:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-number-employees",fieldLabel:(0,Lt.__)("Number of employees","wordpress-seo"),keywords:[]},organization_vat_id:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-vat-id",fieldLabel:(0,Lt.__)("VAT ID","wordpress-seo"),keywords:[]},organization_tax_id:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-tax-id",fieldLabel:(0,Lt.__)("Tax ID","wordpress-seo"),keywords:[]},organization_iso:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-iso",fieldLabel:(0,Lt.__)("ISO 6523","wordpress-seo"),keywords:[]},organization_duns:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-duns",fieldLabel:(0,Lt.__)("DUNS","wordpress-seo"),keywords:[]},organization_leicode:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-leicode",fieldLabel:(0,Lt.__)("LEI code","wordpress-seo"),keywords:[(0,Lt.__)("leicode","wordpress-seo")]},organization_naics:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_titles-org-naics",fieldLabel:(0,Lt.__)("NAICS","wordpress-seo"),keywords:[]},"title-home-wpseo":{route:"/homepage",routeLabel:(0,Lt.__)("Homepage","wordpress-seo"),fieldId:"input-wpseo_titles-title-home-wpseo",fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[]},"metadesc-home-wpseo":{route:"/homepage",routeLabel:(0,Lt.__)("Homepage","wordpress-seo"),fieldId:"input-wpseo_titles-metadesc-home-wpseo",fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[]},open_graph_frontpage_image_id:{route:"/homepage",routeLabel:(0,Lt.__)("Homepage","wordpress-seo"),fieldId:"button-wpseo_titles-open_graph_frontpage_image-preview",fieldLabel:(0,Lt.__)("Social image","wordpress-seo"),keywords:[]},open_graph_frontpage_title:{route:"/homepage",routeLabel:(0,Lt.__)("Homepage","wordpress-seo"),fieldId:"input-wpseo_titles-open_graph_frontpage_title",fieldLabel:(0,Lt.__)("Social title","wordpress-seo"),keywords:[]},open_graph_frontpage_desc:{route:"/homepage",routeLabel:(0,Lt.__)("Homepage","wordpress-seo"),fieldId:"input-wpseo_titles-open_graph_frontpage_desc",fieldLabel:(0,Lt.__)("Social description","wordpress-seo"),keywords:[]},"breadcrumbs-sep":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-sep",fieldLabel:(0,Lt.__)("Separator between breadcrumbs","wordpress-seo"),keywords:[(0,Lt.__)("Divider","wordpress-seo"),(0,Lt.__)("Separator","wordpress-seo")]},"breadcrumbs-home":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-home",fieldLabel:(0,Lt.__)("Anchor text for the homepage","wordpress-seo"),keywords:[]},"breadcrumbs-prefix":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-prefix",fieldLabel:(0,Lt.__)("Prefix for the breadcrumb path","wordpress-seo"),keywords:[]},"breadcrumbs-archiveprefix":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-archiveprefix",fieldLabel:(0,Lt.__)("Prefix for archive breadcrumbs","wordpress-seo"),keywords:[]},"breadcrumbs-searchprefix":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-searchprefix",fieldLabel:(0,Lt.__)("Prefix for search page breadcrumbs","wordpress-seo"),keywords:[]},"breadcrumbs-404crumb":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-404crumb",fieldLabel:(0,Lt.__)("Breadcrumb for 404 page","wordpress-seo"),keywords:[]},"breadcrumbs-display-blog-page":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-display-blog-page",fieldLabel:(0,Lt.__)("Show blog page in breadcrumbs","wordpress-seo"),keywords:[]},"breadcrumbs-boldlast":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-boldlast",fieldLabel:(0,Lt.__)("Bold the last page","wordpress-seo"),keywords:[]},"breadcrumbs-enable":{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:"input-wpseo_titles-breadcrumbs-enable",fieldLabel:(0,Lt.__)("Enable breadcrumbs for your theme","wordpress-seo"),keywords:[]},...(0,le.reduce)(e,((e,r)=>{const a=(0,le.filter)(t,(e=>(0,le.includes)(e.postTypes,r.name)));return(0,le.isEmpty)(a)?e:{...e,[`post_types-${r.name}-maintax`]:{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:`input-wpseo_titles-post_types-${r.name}-maintax`,
// translators: %1$s expands to the post type plural, e.g. posts.
fieldLabel:(0,Lt.sprintf)((0,Lt.__)("Breadcrumbs for %1$s","wordpress-seo"),bn(r.label,s)),keywords:[]}}}),{}),...(0,le.reduce)(t,((e,t)=>({...e,[`taxonomy-${t.name}-ptparent`]:{route:"/breadcrumbs",routeLabel:(0,Lt.__)("Breadcrumbs","wordpress-seo"),fieldId:`input-wpseo_titles-taxonomy-${t.name}-ptparent`,
// translators: %1$s expands to the taxonomy plural, e.g. categories.
fieldLabel:(0,Lt.sprintf)((0,Lt.__)("Breadcrumbs for %1$s","wordpress-seo"),bn(t.label,s)),keywords:[]}})),{}),"disable-author":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-disable-author",fieldLabel:(0,Lt.__)("Enable author archives","wordpress-seo"),keywords:[]},"noindex-author-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-noindex-author-wpseo",fieldLabel:(0,Lt.__)("Show author archives in search results","wordpress-seo"),keywords:[]},"noindex-author-noposts-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-noindex-author-noposts-wpseo",fieldLabel:(0,Lt.__)("Show archives for authors without posts in search results","wordpress-seo"),keywords:[]},"title-author-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-title-author-wpseo",fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[]},"metadesc-author-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-metadesc-author-wpseo",fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[]},"social-image-id-author-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"button-wpseo_titles-social-image-author-wpseo-preview",fieldLabel:(0,Lt.__)("Social image","wordpress-seo"),keywords:[]},"social-title-author-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-social-title-author-wpseo",fieldLabel:(0,Lt.__)("Social title","wordpress-seo"),keywords:[]},"social-description-author-wpseo":{route:"/author-archives",routeLabel:(0,Lt.__)("Author archives","wordpress-seo"),fieldId:"input-wpseo_titles-social-description-author-wpseo",fieldLabel:(0,Lt.__)("Social description","wordpress-seo"),keywords:[]},"disable-date":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"input-wpseo_titles-disable-date",fieldLabel:(0,Lt.__)("Enable date archives","wordpress-seo"),keywords:[]},"noindex-archive-wpseo":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"input-wpseo_titles-noindex-archive-wpseo",fieldLabel:(0,Lt.__)("Show date archives in search results","wordpress-seo"),keywords:[]},"title-archive-wpseo":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"input-wpseo_titles-title-archive-wpseo",fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[]},"metadesc-archive-wpseo":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"input-wpseo_titles-metadesc-archive-wpseo",fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[]},"social-image-id-archive-wpseo":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"button-wpseo_titles-social-image-archive-wpseo-preview",fieldLabel:(0,Lt.__)("Social image","wordpress-seo"),keywords:[]},"social-title-archive-wpseo":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"input-wpseo_titles-social-title-archive-wpseo",fieldLabel:(0,Lt.__)("Social title","wordpress-seo"),keywords:[]},"social-description-archive-wpseo":{route:"/date-archives",routeLabel:(0,Lt.__)("Date archives","wordpress-seo"),fieldId:"input-wpseo_titles-social-description-archive-wpseo",fieldLabel:(0,Lt.__)("Social description","wordpress-seo"),keywords:[]},"title-search-wpseo":{route:"/special-pages",routeLabel:(0,Lt.__)("Special pages","wordpress-seo"),fieldId:"input-wpseo_titles-title-search-wpseo",fieldLabel:(0,Lt.__)("Search pages title","wordpress-seo"),keywords:[]},"title-404-wpseo":{route:"/special-pages",routeLabel:(0,Lt.__)("Special pages","wordpress-seo"),fieldId:"input-wpseo_titles-title-404-wpseo",fieldLabel:(0,Lt.__)("404 pages title","wordpress-seo"),keywords:[]},"disable-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-disable-attachment",fieldLabel:(0,Lt.__)("Media pages","wordpress-seo"),keywords:[(0,Lt.__)("Attachment","wordpress-seo"),(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"noindex-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-noindex-attachment",fieldLabel:(0,Lt.__)("Show media pages in search results","wordpress-seo"),keywords:[(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"title-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-title-attachment",fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"metadesc-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-metadesc-attachment",fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"schema-page-type-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-schema-page-type-attachment",fieldLabel:(0,Lt.__)("Page type","wordpress-seo"),keywords:[(0,Lt.__)("Schema","wordpress-seo"),(0,Lt.__)("Structured data","wordpress-seo"),(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"schema-article-type-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-schema-article-type-attachment",fieldLabel:(0,Lt.__)("Article type","wordpress-seo"),keywords:[(0,Lt.__)("Schema","wordpress-seo"),(0,Lt.__)("Structured data","wordpress-seo"),(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"display-metabox-pt-attachment":{route:"/media-pages",routeLabel:(0,Lt.__)("Media pages","wordpress-seo"),fieldId:"input-wpseo_titles-display-metabox-pt-attachment",fieldLabel:(0,Lt.__)("Enable SEO controls and assessments","wordpress-seo"),keywords:[(0,Lt.__)("Image","wordpress-seo"),(0,Lt.__)("Video","wordpress-seo"),(0,Lt.__)("PDF","wordpress-seo"),(0,Lt.__)("File","wordpress-seo")]},"disable-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"input-wpseo_titles-disable-post_format",fieldLabel:(0,Lt.__)("Enable format-based archives","wordpress-seo"),keywords:[]},"noindex-tax-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"input-wpseo_titles-noindex-tax-post_format",fieldLabel:(0,Lt.__)("Show format archives in search results","wordpress-seo"),keywords:[]},"title-tax-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"input-wpseo_titles-title-tax-post_format",fieldLabel:(0,Lt.__)("SEO title","wordpress-seo"),keywords:[]},"metadesc-tax-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"input-wpseo_titles-metadesc-tax-post_format",fieldLabel:(0,Lt.__)("Meta description","wordpress-seo"),keywords:[]},"social-image-id-tax-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"button-wpseo_titles-social-image-tax-post_format-preview",fieldLabel:(0,Lt.__)("Social image","wordpress-seo"),keywords:[]},"social-title-tax-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"input-wpseo_titles-social-title-tax-post_format",fieldLabel:(0,Lt.__)("Social title","wordpress-seo"),keywords:[]},"social-description-tax-post_format":{route:"/format-archives",routeLabel:(0,Lt.__)("Format archives","wordpress-seo"),fieldId:"input-wpseo_titles-social-description-tax-post_format",fieldLabel:(0,Lt.__)("Social description","wordpress-seo"),keywords:[]},rssbefore:{route:"/rss",routeLabel:"RSS",fieldId:"input-wpseo_titles-rssbefore",fieldLabel:(0,Lt.__)("Content to put before each post in the feed","wordpress-seo"),keywords:[]},rssafter:{route:"/rss",routeLabel:"RSS",fieldId:"input-wpseo_titles-rssafter",fieldLabel:(0,Lt.__)("Content to put after each post in the feed","wordpress-seo"),keywords:[]},...(0,le.reduce)((0,le.omit)(e,["attachment"]),((e,t)=>({...e,...vn(t,{userLocale:s})})),{}),...(0,le.reduce)((0,le.omit)(t,["post_format"]),((e,t)=>({...e,...En(t,{userLocale:s})})),{})},wpseo_social:{opengraph:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo_social-opengraph",fieldLabel:(0,Lt.__)("Open Graph data","wordpress-seo"),keywords:[(0,Lt.__)("Social","wordpress-seo"),(0,Lt.__)("OpenGraph","wordpress-seo"),(0,Lt.__)("Facebook","wordpress-seo"),(0,Lt.__)("Share","wordpress-seo")]},twitter:{route:"/site-features",routeLabel:(0,Lt.__)("Site features","wordpress-seo"),fieldId:"card-wpseo_social-twitter",fieldLabel:(0,Lt.__)("X card data","wordpress-seo"),keywords:[(0,Lt.__)("Social","wordpress-seo"),(0,Lt.__)("Share","wordpress-seo"),(0,Lt.__)("Tweet","wordpress-seo"),(0,Lt.__)("Twitter","wordpress-seo")]},og_default_image_id:{route:"/site-basics",routeLabel:(0,Lt.__)("Site basics","wordpress-seo"),fieldId:"button-wpseo_social-og_default_image-preview",fieldLabel:(0,Lt.__)("Site image","wordpress-seo"),keywords:[]},pinterestverify:{route:"/site-connections",routeLabel:(0,Lt.__)("Site connections","wordpress-seo"),fieldId:"input-wpseo_social-pinterestverify",fieldLabel:(0,Lt.__)("Pinterest","wordpress-seo"),keywords:[(0,Lt.__)("Webmaster","wordpress-seo")]},facebook_site:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_social-facebook_site",fieldLabel:(0,Lt.__)("Organization Facebook","wordpress-seo"),keywords:[(0,Lt.__)("Social","wordpress-seo"),(0,Lt.__)("Open Graph","wordpress-seo"),(0,Lt.__)("OpenGraph","wordpress-seo"),(0,Lt.__)("Share","wordpress-seo")]},twitter_site:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_social-twitter_site",fieldLabel:(0,Lt.__)("Organization X","wordpress-seo"),keywords:[(0,Lt.__)("Social","wordpress-seo"),(0,Lt.__)("Share","wordpress-seo"),(0,Lt.__)("Tweet","wordpress-seo"),(0,Lt.__)("Twitter","wordpress-seo")]},mastodon_url:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"input-wpseo_social-mastodon_url",fieldLabel:(0,Lt.__)("Organization Mastodon","wordpress-seo"),keywords:[(0,Lt.__)("Social","wordpress-seo"),(0,Lt.__)("Share","wordpress-seo")]},other_social_urls:{route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:"fieldset-wpseo_social-other_social_urls",fieldLabel:(0,Lt.__)("Other social profiles","wordpress-seo"),keywords:[],...(0,le.times)(25,(e=>({route:"/site-representation",routeLabel:(0,Lt.__)("Site representation","wordpress-seo"),fieldId:`input-wpseo_social-other_social_urls-${e}`,
// translators: %1$s expands to array index + 1.
fieldLabel:(0,Lt.sprintf)((0,Lt.__)("Other profile %1$s","wordpress-seo"),e+1)})))}}}),kn=async e=>{const{endpoint:t,nonce:s}=(0,le.get)(window,"wpseoScriptData",{}),r=new FormData;r.set("option_page","wpseo_page_settings"),r.set("_wp_http_referer","admin.php?page=wpseo_page_settings_saved"),r.set("action","update"),r.set("_wpnonce",s),(0,le.forEach)(e,((e,t)=>{(0,le.isObject)(e)?(0,le.forEach)(e,((e,s)=>{(0,le.isArray)(e)?(0,le.forEach)(e,((e,a)=>r.set(`${t}[${s}][${a}]`,e))):r.set(`${t}[${s}]`,e)})):r.set(t,e)}));try{const e=await fetch(t,{method:"POST",body:new URLSearchParams(r)}),s=await e.text();if((0,le.includes)(s,"{{ yoast-success: false }}"))throw new Error("Yoast options invalid.");if(!e.url.endsWith("settings-updated=true"))throw new Error("WordPress options save did not get to the end.")}catch(e){throw new Error(e.message)}},Sn=async(e,{resetForm:t})=>{const{addNotification:s}=(0,a.dispatch)(ta),{selectPreference:r}=(0,a.select)(ta),o=r("canManageOptions",!1);try{return await Promise.all([kn(o?e:(0,le.omit)(e,["blogdescription"]))]),s({variant:"success",title:(0,Lt.__)("Great! Your settings were saved successfully.","wordpress-seo")}),t({values:e}),!0}catch(e){return s({id:"submit-error",variant:"error",title:(0,Lt.__)("Oops! Something went wrong while saving.","wordpress-seo")}),console.error("Error while saving:",e.message),!1}};var Ln,Tn;try{Ln=Map}catch(e){}try{Tn=Set}catch(e){}function Fn(e,t,s){if(!e||"object"!=typeof e||"function"==typeof e)return e;if(e.nodeType&&"cloneNode"in e)return e.cloneNode(!0);if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp)return new RegExp(e);if(Array.isArray(e))return e.map($n);if(Ln&&e instanceof Ln)return new Map(Array.from(e.entries()));if(Tn&&e instanceof Tn)return new Set(Array.from(e.values()));if(e instanceof Object){t.push(e);var r=Object.create(e);for(var a in s.push(r),e){var o=t.findIndex((function(t){return t===e[a]}));r[a]=o>-1?s[o]:Fn(e[a],t,s)}return r}return e}function $n(e){return Fn(e,[],[])}const Pn=Object.prototype.toString,Rn=Error.prototype.toString,Nn=RegExp.prototype.toString,On="undefined"!=typeof Symbol?Symbol.prototype.toString:()=>"",Cn=/^Symbol\((.*)\)(.*)$/;function An(e,t=!1){if(null==e||!0===e||!1===e)return""+e;const s=typeof e;if("number"===s)return function(e){return e!=+e?"NaN":0===e&&1/e<0?"-0":""+e}(e);if("string"===s)return t?`"${e}"`:e;if("function"===s)return"[Function "+(e.name||"anonymous")+"]";if("symbol"===s)return On.call(e).replace(Cn,"Symbol($1)");const r=Pn.call(e).slice(8,-1);return"Date"===r?isNaN(e.getTime())?""+e:e.toISOString(e):"Error"===r||e instanceof Error?"["+Rn.call(e)+"]":"RegExp"===r?Nn.call(e):null}function In(e,t){let s=An(e,t);return null!==s?s:JSON.stringify(e,(function(e,s){let r=An(this[e],t);return null!==r?r:s}),2)}let Mn={default:"${path} is invalid",required:"${path} is a required field",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:e,type:t,value:s,originalValue:r})=>{let a=null!=r&&r!==s,o=`${e} must be a \`${t}\` type, but the final value was: \`${In(s,!0)}\``+(a?` (cast from the value \`${In(r,!0)}\`).`:".");return null===s&&(o+='\n If "null" is intended as an empty value be sure to mark the schema as `.nullable()`'),o},defined:"${path} must be defined"},Dn={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},Bn={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},Un={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},jn={noUnknown:"${path} field has unspecified keys: ${unknown}"},Vn={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"};Object.assign(Object.create(null),{mixed:Mn,string:Dn,number:Bn,date:Un,object:jn,array:Vn,boolean:{isValue:"${path} field must be ${value}"}});const zn=window.lodash.has;var qn=s.n(zn);const Wn=e=>e&&e.__isYupSchema__,Hn=class{constructor(e,t){if(this.fn=void 0,this.refs=e,this.refs=e,"function"==typeof t)return void(this.fn=t);if(!qn()(t,"is"))throw new TypeError("`is:` is required for `when()` conditions");if(!t.then&&!t.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:s,then:r,otherwise:a}=t,o="function"==typeof s?s:(...e)=>e.every((e=>e===s));this.fn=function(...e){let t=e.pop(),s=e.pop(),n=o(...e)?r:a;if(n)return"function"==typeof n?n(s):s.concat(n.resolve(t))}}resolve(e,t){let s=this.refs.map((e=>e.getValue(null==t?void 0:t.value,null==t?void 0:t.parent,null==t?void 0:t.context))),r=this.fn.apply(e,s.concat(e,t));if(void 0===r||r===e)return e;if(!Wn(r))throw new TypeError("conditions must return a schema object");return r.resolve(t)}};function Yn(e){return null==e?[]:[].concat(e)}function Gn(){return Gn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Gn.apply(this,arguments)}let Kn=/\$\{\s*(\w+)\s*\}/g;class Zn extends Error{static formatError(e,t){const s=t.label||t.path||"this";return s!==t.path&&(t=Gn({},t,{path:s})),"string"==typeof e?e.replace(Kn,((e,s)=>In(t[s]))):"function"==typeof e?e(t):e}static isError(e){return e&&"ValidationError"===e.name}constructor(e,t,s,r){super(),this.value=void 0,this.path=void 0,this.type=void 0,this.errors=void 0,this.params=void 0,this.inner=void 0,this.name="ValidationError",this.value=t,this.path=s,this.type=r,this.errors=[],this.inner=[],Yn(e).forEach((e=>{Zn.isError(e)?(this.errors.push(...e.errors),this.inner=this.inner.concat(e.inner.length?e.inner:e)):this.errors.push(e)})),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0],Error.captureStackTrace&&Error.captureStackTrace(this,Zn)}}function Jn(e,t){let{endEarly:s,tests:r,args:a,value:o,errors:n,sort:i,path:l}=e,c=(e=>{let t=!1;return(...s)=>{t||(t=!0,e(...s))}})(t),d=r.length;const u=[];if(n=n||[],!d)return n.length?c(new Zn(n,o,l)):c(null,o);for(let e=0;e<r.length;e++)(0,r[e])(a,(function(e){if(e){if(!Zn.isError(e))return c(e,o);if(s)return e.value=o,c(e,o);u.push(e)}if(--d<=0){if(u.length&&(i&&u.sort(i),n.length&&u.push(...n),n=u),n.length)return void c(new Zn(n,o,l),o);c(null,o)}}))}const Qn=window.lodash.mapValues;var Xn=s.n(Qn),ei=s(5760);class ti{constructor(e,t={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,"string"!=typeof e)throw new TypeError("ref must be a string, got: "+e);if(this.key=e.trim(),""===e)throw new TypeError("ref must be a non-empty string");this.isContext="$"===this.key[0],this.isValue="."===this.key[0],this.isSibling=!this.isContext&&!this.isValue;let s=this.isContext?"$":this.isValue?".":"";this.path=this.key.slice(s.length),this.getter=this.path&&(0,ei.getter)(this.path,!0),this.map=t.map}getValue(e,t,s){let r=this.isContext?s:this.isValue?e:t;return this.getter&&(r=this.getter(r||{})),this.map&&(r=this.map(r)),r}cast(e,t){return this.getValue(e,null==t?void 0:t.parent,null==t?void 0:t.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(e){return e&&e.__isYupRef}}function si(){return si=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},si.apply(this,arguments)}function ri(e){function t(t,s){let{value:r,path:a="",label:o,options:n,originalValue:i,sync:l}=t,c=function(e,t){if(null==e)return{};var s,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)s=o[r],t.indexOf(s)>=0||(a[s]=e[s]);return a}(t,["value","path","label","options","originalValue","sync"]);const{name:d,test:u,params:p,message:m}=e;let{parent:f,context:h}=n;function y(e){return ti.isRef(e)?e.getValue(r,f,h):e}function _(e={}){const t=Xn()(si({value:r,originalValue:i,label:o,path:e.path||a},p,e.params),y),s=new Zn(Zn.formatError(e.message||m,t),r,t.path,e.type||d);return s.params=t,s}let w,g=si({path:a,parent:f,type:d,createError:_,resolve:y,options:n,originalValue:i},c);if(l){try{var b;if(w=u.call(g,r,g),"function"==typeof(null==(b=w)?void 0:b.then))throw new Error(`Validation test of type: "${g.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`)}catch(e){return void s(e)}Zn.isError(w)?s(w):w?s(null,w):s(_())}else try{Promise.resolve(u.call(g,r,g)).then((e=>{Zn.isError(e)?s(e):e?s(null,e):s(_())})).catch(s)}catch(e){s(e)}}return t.OPTIONS=e,t}function ai(e,t,s,r=s){let a,o,n;return t?((0,ei.forEach)(t,((i,l,c)=>{let d=l?(e=>e.substr(0,e.length-1).substr(1))(i):i;if((e=e.resolve({context:r,parent:a,value:s})).innerType){let r=c?parseInt(d,10):0;if(s&&r>=s.length)throw new Error(`Yup.reach cannot resolve an array item at index: ${i}, in the path: ${t}. because there is no value at that index. `);a=s,s=s&&s[r],e=e.innerType}if(!c){if(!e.fields||!e.fields[d])throw new Error(`The schema does not contain the path: ${t}. (failed at: ${n} which is a type: "${e._type}")`);a=s,s=s&&s[d],e=e.fields[d]}o=d,n=l?"["+i+"]":"."+i})),{schema:e,parent:a,parentPath:o}):{parent:a,parentPath:t,schema:e}}ti.prototype.__isYupRef=!0;class oi{constructor(){this.list=void 0,this.refs=void 0,this.list=new Set,this.refs=new Map}get size(){return this.list.size+this.refs.size}describe(){const e=[];for(const t of this.list)e.push(t);for(const[,t]of this.refs)e.push(t.describe());return e}toArray(){return Array.from(this.list).concat(Array.from(this.refs.values()))}resolveAll(e){return this.toArray().reduce(((t,s)=>t.concat(ti.isRef(s)?e(s):s)),[])}add(e){ti.isRef(e)?this.refs.set(e.key,e):this.list.add(e)}delete(e){ti.isRef(e)?this.refs.delete(e.key):this.list.delete(e)}clone(){const e=new oi;return e.list=new Set(this.list),e.refs=new Map(this.refs),e}merge(e,t){const s=this.clone();return e.list.forEach((e=>s.add(e))),e.refs.forEach((e=>s.add(e))),t.list.forEach((e=>s.delete(e))),t.refs.forEach((e=>s.delete(e))),s}}function ni(){return ni=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},ni.apply(this,arguments)}class ii{constructor(e){this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this._typeError=void 0,this._whitelist=new oi,this._blacklist=new oi,this.exclusiveTests=Object.create(null),this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation((()=>{this.typeError(Mn.notType)})),this.type=(null==e?void 0:e.type)||"mixed",this.spec=ni({strip:!1,strict:!1,abortEarly:!0,recursive:!0,nullable:!1,presence:"optional"},null==e?void 0:e.spec)}get _type(){return this.type}_typeCheck(e){return!0}clone(e){if(this._mutate)return e&&Object.assign(this.spec,e),this;const t=Object.create(Object.getPrototypeOf(this));return t.type=this.type,t._typeError=this._typeError,t._whitelistError=this._whitelistError,t._blacklistError=this._blacklistError,t._whitelist=this._whitelist.clone(),t._blacklist=this._blacklist.clone(),t.exclusiveTests=ni({},this.exclusiveTests),t.deps=[...this.deps],t.conditions=[...this.conditions],t.tests=[...this.tests],t.transforms=[...this.transforms],t.spec=$n(ni({},this.spec,e)),t}label(e){let t=this.clone();return t.spec.label=e,t}meta(...e){if(0===e.length)return this.spec.meta;let t=this.clone();return t.spec.meta=Object.assign(t.spec.meta||{},e[0]),t}withMutation(e){let t=this._mutate;this._mutate=!0;let s=e(this);return this._mutate=t,s}concat(e){if(!e||e===this)return this;if(e.type!==this.type&&"mixed"!==this.type)throw new TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${e.type}`);let t=this,s=e.clone();const r=ni({},t.spec,s.spec);return s.spec=r,s._typeError||(s._typeError=t._typeError),s._whitelistError||(s._whitelistError=t._whitelistError),s._blacklistError||(s._blacklistError=t._blacklistError),s._whitelist=t._whitelist.merge(e._whitelist,e._blacklist),s._blacklist=t._blacklist.merge(e._blacklist,e._whitelist),s.tests=t.tests,s.exclusiveTests=t.exclusiveTests,s.withMutation((t=>{e.tests.forEach((e=>{t.test(e.OPTIONS)}))})),s.transforms=[...t.transforms,...s.transforms],s}isType(e){return!(!this.spec.nullable||null!==e)||this._typeCheck(e)}resolve(e){let t=this;if(t.conditions.length){let s=t.conditions;t=t.clone(),t.conditions=[],t=s.reduce(((t,s)=>s.resolve(t,e)),t),t=t.resolve(e)}return t}cast(e,t={}){let s=this.resolve(ni({value:e},t)),r=s._cast(e,t);if(void 0!==e&&!1!==t.assert&&!0!==s.isType(r)){let a=In(e),o=In(r);throw new TypeError(`The value of ${t.path||"field"} could not be cast to a value that satisfies the schema type: "${s._type}". \n\nattempted value: ${a} \n`+(o!==a?`result of cast: ${o}`:""))}return r}_cast(e,t){let s=void 0===e?e:this.transforms.reduce(((t,s)=>s.call(this,t,e,this)),e);return void 0===s&&(s=this.getDefault()),s}_validate(e,t={},s){let{sync:r,path:a,from:o=[],originalValue:n=e,strict:i=this.spec.strict,abortEarly:l=this.spec.abortEarly}=t,c=e;i||(c=this._cast(c,ni({assert:!1},t)));let d={value:c,path:a,options:t,originalValue:n,schema:this,label:this.spec.label,sync:r,from:o},u=[];this._typeError&&u.push(this._typeError);let p=[];this._whitelistError&&p.push(this._whitelistError),this._blacklistError&&p.push(this._blacklistError),Jn({args:d,value:c,path:a,sync:r,tests:u,endEarly:l},(e=>{e?s(e,c):Jn({tests:this.tests.concat(p),args:d,path:a,sync:r,value:c,endEarly:l},s)}))}validate(e,t,s){let r=this.resolve(ni({},t,{value:e}));return"function"==typeof s?r._validate(e,t,s):new Promise(((s,a)=>r._validate(e,t,((e,t)=>{e?a(e):s(t)}))))}validateSync(e,t){let s;return this.resolve(ni({},t,{value:e}))._validate(e,ni({},t,{sync:!0}),((e,t)=>{if(e)throw e;s=t})),s}isValid(e,t){return this.validate(e,t).then((()=>!0),(e=>{if(Zn.isError(e))return!1;throw e}))}isValidSync(e,t){try{return this.validateSync(e,t),!0}catch(e){if(Zn.isError(e))return!1;throw e}}_getDefault(){let e=this.spec.default;return null==e?e:"function"==typeof e?e.call(this):$n(e)}getDefault(e){return this.resolve(e||{})._getDefault()}default(e){return 0===arguments.length?this._getDefault():this.clone({default:e})}strict(e=!0){let t=this.clone();return t.spec.strict=e,t}_isPresent(e){return null!=e}defined(e=Mn.defined){return this.test({message:e,name:"defined",exclusive:!0,test:e=>void 0!==e})}required(e=Mn.required){return this.clone({presence:"required"}).withMutation((t=>t.test({message:e,name:"required",exclusive:!0,test(e){return this.schema._isPresent(e)}})))}notRequired(){let e=this.clone({presence:"optional"});return e.tests=e.tests.filter((e=>"required"!==e.OPTIONS.name)),e}nullable(e=!0){return this.clone({nullable:!1!==e})}transform(e){let t=this.clone();return t.transforms.push(e),t}test(...e){let t;if(t=1===e.length?"function"==typeof e[0]?{test:e[0]}:e[0]:2===e.length?{name:e[0],test:e[1]}:{name:e[0],message:e[1],test:e[2]},void 0===t.message&&(t.message=Mn.default),"function"!=typeof t.test)throw new TypeError("`test` is a required parameters");let s=this.clone(),r=ri(t),a=t.exclusive||t.name&&!0===s.exclusiveTests[t.name];if(t.exclusive&&!t.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return t.name&&(s.exclusiveTests[t.name]=!!t.exclusive),s.tests=s.tests.filter((e=>{if(e.OPTIONS.name===t.name){if(a)return!1;if(e.OPTIONS.test===r.OPTIONS.test)return!1}return!0})),s.tests.push(r),s}when(e,t){Array.isArray(e)||"string"==typeof e||(t=e,e=".");let s=this.clone(),r=Yn(e).map((e=>new ti(e)));return r.forEach((e=>{e.isSibling&&s.deps.push(e.key)})),s.conditions.push(new Hn(r,t)),s}typeError(e){let t=this.clone();return t._typeError=ri({message:e,name:"typeError",test(e){return!(void 0!==e&&!this.schema.isType(e))||this.createError({params:{type:this.schema._type}})}}),t}oneOf(e,t=Mn.oneOf){let s=this.clone();return e.forEach((e=>{s._whitelist.add(e),s._blacklist.delete(e)})),s._whitelistError=ri({message:t,name:"oneOf",test(e){if(void 0===e)return!0;let t=this.schema._whitelist,s=t.resolveAll(this.resolve);return!!s.includes(e)||this.createError({params:{values:t.toArray().join(", "),resolved:s}})}}),s}notOneOf(e,t=Mn.notOneOf){let s=this.clone();return e.forEach((e=>{s._blacklist.add(e),s._whitelist.delete(e)})),s._blacklistError=ri({message:t,name:"notOneOf",test(e){let t=this.schema._blacklist,s=t.resolveAll(this.resolve);return!s.includes(e)||this.createError({params:{values:t.toArray().join(", "),resolved:s}})}}),s}strip(e=!0){let t=this.clone();return t.spec.strip=e,t}describe(){const e=this.clone(),{label:t,meta:s}=e.spec,r={meta:s,label:t,type:e.type,oneOf:e._whitelist.describe(),notOneOf:e._blacklist.describe(),tests:e.tests.map((e=>({name:e.OPTIONS.name,params:e.OPTIONS.params}))).filter(((e,t,s)=>s.findIndex((t=>t.name===e.name))===t))};return r}}ii.prototype.__isYupSchema__=!0;for(const e of["validate","validateSync"])ii.prototype[`${e}At`]=function(t,s,r={}){const{parent:a,parentPath:o,schema:n}=ai(this,t,s,r.context);return n[e](a&&a[o],ni({},r,{parent:a,path:t}))};for(const e of["equals","is"])ii.prototype[e]=ii.prototype.oneOf;for(const e of["not","nope"])ii.prototype[e]=ii.prototype.notOneOf;ii.prototype.optional=ii.prototype.notRequired;ii.prototype;const li=e=>null==e;let ci=/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i,di=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,ui=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,pi=e=>li(e)||e===e.trim(),mi={}.toString();function fi(){return new hi}class hi extends ii{constructor(){super({type:"string"}),this.withMutation((()=>{this.transform((function(e){if(this.isType(e))return e;if(Array.isArray(e))return e;const t=null!=e&&e.toString?e.toString():e;return t===mi?e:t}))}))}_typeCheck(e){return e instanceof String&&(e=e.valueOf()),"string"==typeof e}_isPresent(e){return super._isPresent(e)&&!!e.length}length(e,t=Dn.length){return this.test({message:t,name:"length",exclusive:!0,params:{length:e},test(t){return li(t)||t.length===this.resolve(e)}})}min(e,t=Dn.min){return this.test({message:t,name:"min",exclusive:!0,params:{min:e},test(t){return li(t)||t.length>=this.resolve(e)}})}max(e,t=Dn.max){return this.test({name:"max",exclusive:!0,message:t,params:{max:e},test(t){return li(t)||t.length<=this.resolve(e)}})}matches(e,t){let s,r,a=!1;return t&&("object"==typeof t?({excludeEmptyString:a=!1,message:s,name:r}=t):s=t),this.test({name:r||"matches",message:s||Dn.matches,params:{regex:e},test:t=>li(t)||""===t&&a||-1!==t.search(e)})}email(e=Dn.email){return this.matches(ci,{name:"email",message:e,excludeEmptyString:!0})}url(e=Dn.url){return this.matches(di,{name:"url",message:e,excludeEmptyString:!0})}uuid(e=Dn.uuid){return this.matches(ui,{name:"uuid",message:e,excludeEmptyString:!1})}ensure(){return this.default("").transform((e=>null===e?"":e))}trim(e=Dn.trim){return this.transform((e=>null!=e?e.trim():e)).test({message:e,name:"trim",test:pi})}lowercase(e=Dn.lowercase){return this.transform((e=>li(e)?e:e.toLowerCase())).test({message:e,name:"string_case",exclusive:!0,test:e=>li(e)||e===e.toLowerCase()})}uppercase(e=Dn.uppercase){return this.transform((e=>li(e)?e:e.toUpperCase())).test({message:e,name:"string_case",exclusive:!0,test:e=>li(e)||e===e.toUpperCase()})}}function yi(){return new _i}fi.prototype=hi.prototype;class _i extends ii{constructor(){super({type:"number"}),this.withMutation((()=>{this.transform((function(e){let t=e;if("string"==typeof t){if(t=t.replace(/\s/g,""),""===t)return NaN;t=+t}return this.isType(t)?t:parseFloat(t)}))}))}_typeCheck(e){return e instanceof Number&&(e=e.valueOf()),"number"==typeof e&&!(e=>e!=+e)(e)}min(e,t=Bn.min){return this.test({message:t,name:"min",exclusive:!0,params:{min:e},test(t){return li(t)||t>=this.resolve(e)}})}max(e,t=Bn.max){return this.test({message:t,name:"max",exclusive:!0,params:{max:e},test(t){return li(t)||t<=this.resolve(e)}})}lessThan(e,t=Bn.lessThan){return this.test({message:t,name:"max",exclusive:!0,params:{less:e},test(t){return li(t)||t<this.resolve(e)}})}moreThan(e,t=Bn.moreThan){return this.test({message:t,name:"min",exclusive:!0,params:{more:e},test(t){return li(t)||t>this.resolve(e)}})}positive(e=Bn.positive){return this.moreThan(0,e)}negative(e=Bn.negative){return this.lessThan(0,e)}integer(e=Bn.integer){return this.test({name:"integer",message:e,test:e=>li(e)||Number.isInteger(e)})}truncate(){return this.transform((e=>li(e)?e:0|e))}round(e){var t;let s=["ceil","floor","round","trunc"];if("trunc"===(e=(null==(t=e)?void 0:t.toLowerCase())||"round"))return this.truncate();if(-1===s.indexOf(e.toLowerCase()))throw new TypeError("Only valid options for round() are: "+s.join(", "));return this.transform((t=>li(t)?t:Math[e](t)))}}yi.prototype=_i.prototype;var wi=/^(\d{4}|[+\-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,\.](\d{1,}))?)?(?:(Z)|([+\-])(\d{2})(?::?(\d{2}))?)?)?$/;let gi=new Date("");function bi(){return new vi}class vi extends ii{constructor(){super({type:"date"}),this.withMutation((()=>{this.transform((function(e){return this.isType(e)?e:(e=function(e){var t,s,r=[1,4,5,6,7,10,11],a=0;if(s=wi.exec(e)){for(var o,n=0;o=r[n];++n)s[o]=+s[o]||0;s[2]=(+s[2]||1)-1,s[3]=+s[3]||1,s[7]=s[7]?String(s[7]).substr(0,3):0,void 0!==s[8]&&""!==s[8]||void 0!==s[9]&&""!==s[9]?("Z"!==s[8]&&void 0!==s[9]&&(a=60*s[10]+s[11],"+"===s[9]&&(a=0-a)),t=Date.UTC(s[1],s[2],s[3],s[4],s[5]+a,s[6],s[7])):t=+new Date(s[1],s[2],s[3],s[4],s[5],s[6],s[7])}else t=Date.parse?Date.parse(e):NaN;return t}(e),isNaN(e)?gi:new Date(e))}))}))}_typeCheck(e){return t=e,"[object Date]"===Object.prototype.toString.call(t)&&!isNaN(e.getTime());var t}prepareParam(e,t){let s;if(ti.isRef(e))s=e;else{let r=this.cast(e);if(!this._typeCheck(r))throw new TypeError(`\`${t}\` must be a Date or a value that can be \`cast()\` to a Date`);s=r}return s}min(e,t=Un.min){let s=this.prepareParam(e,"min");return this.test({message:t,name:"min",exclusive:!0,params:{min:e},test(e){return li(e)||e>=this.resolve(s)}})}max(e,t=Un.max){let s=this.prepareParam(e,"max");return this.test({message:t,name:"max",exclusive:!0,params:{max:e},test(e){return li(e)||e<=this.resolve(s)}})}}vi.INVALID_DATE=gi,bi.prototype=vi.prototype,bi.INVALID_DATE=gi;const Ei=window.lodash.snakeCase;var xi=s.n(Ei);const ki=window.lodash.camelCase;var Si=s.n(ki);const Li=window.lodash.mapKeys;var Ti=s.n(Li),Fi=s(4633),$i=s.n(Fi);function Pi(e,t){let s=1/0;return e.some(((e,r)=>{var a;if(-1!==(null==(a=t.path)?void 0:a.indexOf(e)))return s=r,!0})),s}function Ri(e){return(t,s)=>Pi(e,t)-Pi(e,s)}function Ni(){return Ni=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Ni.apply(this,arguments)}let Oi=e=>"[object Object]"===Object.prototype.toString.call(e);const Ci=Ri([]);class Ai extends ii{constructor(e){super({type:"object"}),this.fields=Object.create(null),this._sortErrors=Ci,this._nodes=[],this._excludedEdges=[],this.withMutation((()=>{this.transform((function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(t){e=null}return this.isType(e)?e:null})),e&&this.shape(e)}))}_typeCheck(e){return Oi(e)||"function"==typeof e}_cast(e,t={}){var s;let r=super._cast(e,t);if(void 0===r)return this.getDefault();if(!this._typeCheck(r))return r;let a=this.fields,o=null!=(s=t.stripUnknown)?s:this.spec.noUnknown,n=this._nodes.concat(Object.keys(r).filter((e=>-1===this._nodes.indexOf(e)))),i={},l=Ni({},t,{parent:i,__validating:t.__validating||!1}),c=!1;for(const e of n){let s=a[e],n=qn()(r,e);if(s){let a,o=r[e];l.path=(t.path?`${t.path}.`:"")+e,s=s.resolve({value:o,context:t.context,parent:i});let n="spec"in s?s.spec:void 0,d=null==n?void 0:n.strict;if(null==n?void 0:n.strip){c=c||e in r;continue}a=t.__validating&&d?r[e]:s.cast(r[e],l),void 0!==a&&(i[e]=a)}else n&&!o&&(i[e]=r[e]);i[e]!==r[e]&&(c=!0)}return c?i:r}_validate(e,t={},s){let r=[],{sync:a,from:o=[],originalValue:n=e,abortEarly:i=this.spec.abortEarly,recursive:l=this.spec.recursive}=t;o=[{schema:this,value:n},...o],t.__validating=!0,t.originalValue=n,t.from=o,super._validate(e,t,((e,c)=>{if(e){if(!Zn.isError(e)||i)return void s(e,c);r.push(e)}if(!l||!Oi(c))return void s(r[0]||null,c);n=n||c;let d=this._nodes.map((e=>(s,r)=>{let a=-1===e.indexOf(".")?(t.path?`${t.path}.`:"")+e:`${t.path||""}["${e}"]`,i=this.fields[e];i&&"validate"in i?i.validate(c[e],Ni({},t,{path:a,from:o,strict:!0,parent:c,originalValue:n[e]}),r):r(null)}));Jn({sync:a,tests:d,value:c,errors:r,endEarly:i,sort:this._sortErrors,path:t.path},s)}))}clone(e){const t=super.clone(e);return t.fields=Ni({},this.fields),t._nodes=this._nodes,t._excludedEdges=this._excludedEdges,t._sortErrors=this._sortErrors,t}concat(e){let t=super.concat(e),s=t.fields;for(let[e,t]of Object.entries(this.fields)){const r=s[e];void 0===r?s[e]=t:r instanceof ii&&t instanceof ii&&(s[e]=t.concat(r))}return t.withMutation((()=>t.shape(s,this._excludedEdges)))}getDefaultFromShape(){let e={};return this._nodes.forEach((t=>{const s=this.fields[t];e[t]="default"in s?s.getDefault():void 0})),e}_getDefault(){return"default"in this.spec?super._getDefault():this._nodes.length?this.getDefaultFromShape():void 0}shape(e,t=[]){let s=this.clone(),r=Object.assign(s.fields,e);return s.fields=r,s._sortErrors=Ri(Object.keys(r)),t.length&&(Array.isArray(t[0])||(t=[t]),s._excludedEdges=[...s._excludedEdges,...t]),s._nodes=function(e,t=[]){let s=[],r=new Set,a=new Set(t.map((([e,t])=>`${e}-${t}`)));function o(e,t){let o=(0,ei.split)(e)[0];r.add(o),a.has(`${t}-${o}`)||s.push([t,o])}for(const t in e)if(qn()(e,t)){let s=e[t];r.add(t),ti.isRef(s)&&s.isSibling?o(s.path,t):Wn(s)&&"deps"in s&&s.deps.forEach((e=>o(e,t)))}return $i().array(Array.from(r),s).reverse()}(r,s._excludedEdges),s}pick(e){const t={};for(const s of e)this.fields[s]&&(t[s]=this.fields[s]);return this.clone().withMutation((e=>(e.fields={},e.shape(t))))}omit(e){const t=this.clone(),s=t.fields;t.fields={};for(const t of e)delete s[t];return t.withMutation((()=>t.shape(s)))}from(e,t,s){let r=(0,ei.getter)(e,!0);return this.transform((a=>{if(null==a)return a;let o=a;return qn()(a,e)&&(o=Ni({},a),s||delete o[e],o[t]=r(a)),o}))}noUnknown(e=!0,t=jn.noUnknown){"string"==typeof e&&(t=e,e=!0);let s=this.test({name:"noUnknown",exclusive:!0,message:t,test(t){if(null==t)return!0;const s=function(e,t){let s=Object.keys(e.fields);return Object.keys(t).filter((e=>-1===s.indexOf(e)))}(this.schema,t);return!e||0===s.length||this.createError({params:{unknown:s.join(", ")}})}});return s.spec.noUnknown=e,s}unknown(e=!0,t=jn.noUnknown){return this.noUnknown(!e,t)}transformKeys(e){return this.transform((t=>t&&Ti()(t,((t,s)=>e(s)))))}camelCase(){return this.transformKeys(Si())}snakeCase(){return this.transformKeys(xi())}constantCase(){return this.transformKeys((e=>xi()(e).toUpperCase()))}describe(){let e=super.describe();return e.fields=Xn()(this.fields,(e=>e.describe())),e}}function Ii(e){return new Ai(e)}function Mi(){return Mi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Mi.apply(this,arguments)}function Di(e){return new Bi(e)}Ii.prototype=Ai.prototype;class Bi extends ii{constructor(e){super({type:"array"}),this.innerType=void 0,this.innerType=e,this.withMutation((()=>{this.transform((function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(t){e=null}return this.isType(e)?e:null}))}))}_typeCheck(e){return Array.isArray(e)}get _subType(){return this.innerType}_cast(e,t){const s=super._cast(e,t);if(!this._typeCheck(s)||!this.innerType)return s;let r=!1;const a=s.map(((e,s)=>{const a=this.innerType.cast(e,Mi({},t,{path:`${t.path||""}[${s}]`}));return a!==e&&(r=!0),a}));return r?a:s}_validate(e,t={},s){var r,a;let o=[],n=t.sync,i=t.path,l=this.innerType,c=null!=(r=t.abortEarly)?r:this.spec.abortEarly,d=null!=(a=t.recursive)?a:this.spec.recursive,u=null!=t.originalValue?t.originalValue:e;super._validate(e,t,((e,r)=>{if(e){if(!Zn.isError(e)||c)return void s(e,r);o.push(e)}if(!d||!l||!this._typeCheck(r))return void s(o[0]||null,r);u=u||r;let a=new Array(r.length);for(let e=0;e<r.length;e++){let s=r[e],o=`${t.path||""}[${e}]`,n=Mi({},t,{path:o,strict:!0,parent:r,index:e,originalValue:u[e]});a[e]=(e,t)=>l.validate(s,n,t)}Jn({sync:n,path:i,value:r,errors:o,endEarly:c,tests:a},s)}))}clone(e){const t=super.clone(e);return t.innerType=this.innerType,t}concat(e){let t=super.concat(e);return t.innerType=this.innerType,e.innerType&&(t.innerType=t.innerType?t.innerType.concat(e.innerType):e.innerType),t}of(e){let t=this.clone();if(!Wn(e))throw new TypeError("`array.of()` sub-schema must be a valid yup schema not: "+In(e));return t.innerType=e,t}length(e,t=Vn.length){return this.test({message:t,name:"length",exclusive:!0,params:{length:e},test(t){return li(t)||t.length===this.resolve(e)}})}min(e,t){return t=t||Vn.min,this.test({message:t,name:"min",exclusive:!0,params:{min:e},test(t){return li(t)||t.length>=this.resolve(e)}})}max(e,t){return t=t||Vn.max,this.test({message:t,name:"max",exclusive:!0,params:{max:e},test(t){return li(t)||t.length<=this.resolve(e)}})}ensure(){return this.default((()=>[])).transform(((e,t)=>this._typeCheck(e)?e:null==t?[]:[].concat(t)))}compact(e){let t=e?(t,s,r)=>!e(t,s,r):e=>!!e;return this.transform((e=>null!=e?e.filter(t):e))}describe(){let e=super.describe();return this.innerType&&(e.innerType=this.innerType.describe()),e}nullable(e=!0){return super.nullable(e)}defined(){return super.defined()}required(e){return super.required(e)}}function Ui(e,t,s){if(!e||!Wn(e.prototype))throw new TypeError("You must provide a yup schema constructor function");if("string"!=typeof t)throw new TypeError("A Method name must be provided");if("function"!=typeof s)throw new TypeError("Method function must be provided");e.prototype[t]=s}Di.prototype=Bi.prototype;const ji=/^[A-Za-z0-9_-]+$/,Vi=/^[A-Fa-f0-9_-]+$/,zi=/^[A-Za-z0-9_]{1,25}$/,qi=/^https?:\/\/(?:www\.)?(?:twitter|x)\.com\/(?<handle>[A-Za-z0-9_]{1,25})\/?$/,Wi=["image/jpeg","image/png","image/webp","image/gif"];Ui(yi,"isMediaTypeImage",(function(){return this.test("isMediaTypeImage",(0,Lt.__)("The selected file is not an image.","wordpress-seo"),(e=>{if(!e)return!0;const t=(0,a.select)(ta).selectMediaById(e);return!t||"image"===(null==t?void 0:t.type)}))})),Ui(yi,"isMediaMimeTypeAllowed",(function(){return this.test("isMediaMimeTypeAllowed",(0,Lt.__)("The selected media type is not valid. Supported types are: JPG, PNG, WEBP and GIF.","wordpress-seo"),(e=>{const t=(0,a.select)(ta).selectMediaById(e);return!t||Wi.includes(t.mime)}))})),Ui(fi,"isValidTwitterUrlOrHandle",(function(){return this.test("isValidTwitterUrlOrHandle",(0,Lt.__)("The profile is not valid. Please use only 1–25 letters, numbers and underscores or enter a valid X URL.","wordpress-seo"),(e=>!e||zi.test(e)||qi.test(e)))}));const Hi=(e,t)=>Ii().shape({wpseo:Ii().shape({baiduverify:fi().matches(ji,(0,Lt.__)("The verification code is not valid. Please use only letters, numbers, underscores and dashes.","wordpress-seo")),googleverify:fi().matches(ji,(0,Lt.__)("The verification code is not valid. Please use only letters, numbers, underscores and dashes.","wordpress-seo")),msverify:fi().matches(Vi,(0,Lt.__)("The verification code is not valid. Please use only the letters A to F, numbers, underscores and dashes.","wordpress-seo")),yandexverify:fi().matches(Vi,(0,Lt.__)("The verification code is not valid. Please use only the letters A to F, numbers, underscores and dashes.","wordpress-seo")),search_character_limit:yi().required((0,Lt.__)("Please enter a number between 1 and 50.","wordpress-seo")).min(1,(0,Lt.__)("The number you've entered is not between 1 and 50.","wordpress-seo")).max(50,(0,Lt.__)("The number you've entered is not between 1 and 50.","wordpress-seo"))}),wpseo_social:Ii().shape({og_default_image_id:yi().isMediaTypeImage(),facebook_site:fi().url((0,Lt.__)("The profile is not valid. Please enter a valid URL.","wordpress-seo")),mastodon_url:fi().url((0,Lt.__)("The profile is not valid. Please enter a valid URL.","wordpress-seo")),twitter_site:fi().isValidTwitterUrlOrHandle(),other_social_urls:Di().of(fi().url((0,Lt.__)("The profile is not valid. Please enter a valid URL.","wordpress-seo"))),pinterestverify:fi().matches(Vi,(0,Lt.__)("The verification code is not valid. Please use only the letters A to F, numbers, underscores and dashes.","wordpress-seo"))}),wpseo_titles:Ii().shape({open_graph_frontpage_image_id:yi().isMediaTypeImage(),company_logo_id:yi().isMediaTypeImage(),person_logo_id:yi().isMediaTypeImage(),...(0,le.reduce)(e,((e,{name:t,hasArchive:s})=>({...e,..."attachment"!==t&&{[`social-image-id-${t}`]:yi().isMediaTypeImage().isMediaMimeTypeAllowed()},...s&&{[`social-image-id-ptarchive-${t}`]:yi().isMediaTypeImage().isMediaMimeTypeAllowed()}})),{}),...(0,le.reduce)(t,((e,{name:t})=>({...e,[`social-image-id-tax-${t}`]:yi().isMediaTypeImage().isMediaMimeTypeAllowed()})),{}),"social-image-id-author-wpseo":yi().isMediaTypeImage().isMediaMimeTypeAllowed(),"social-image-id-archive-wpseo":yi().isMediaTypeImage().isMediaMimeTypeAllowed(),"social-image-id-tax-post_format":yi().isMediaTypeImage().isMediaMimeTypeAllowed()})}),Yi=new RegExp(/^input-wpseo_titles-(post_types|taxonomy)-(?<name>\S+)-(maintax|ptparent)$/is),Gi={fieldId:"DUMMY_ITEM"},Ki=({fieldId:t,fieldLabel:s})=>{const{isPostTypeOrTaxonomyBreadcrumbSetting:r,postTypeOrTaxonomyName:a}=(0,i.useMemo)((()=>{var e;const s=Yi.exec(t);return{isPostTypeOrTaxonomyBreadcrumbSetting:Boolean(s),postTypeOrTaxonomyName:null==s||null===(e=s.groups)||void 0===e?void 0:e.name}}),[t,Yi]);return r?(0,e.createElement)(e.Fragment,null,s,a&&(0,e.createElement)(l.Code,{className:"yst-ml-2 rtl:yst-mr-2 group-hover:yst-bg-primary-200 group-hover:yst-text-primary-800"},a)):s};Ki.propTypes={fieldId:ir().string.isRequired,fieldLabel:ir().string.isRequired};const Zi=({title:t,children:s})=>(0,e.createElement)("div",{className:"yst-border-t yst-border-slate-100 yst-p-6 yst-py-12 yst-space-3 yst-text-center yst-text-sm"},(0,e.createElement)("span",{className:"yst-block yst-font-semibold yst-text-slate-900"},t),s);Zi.propTypes={title:ir().node.isRequired,children:ir().node.isRequired};const Ji=({buttonId:t="button-search",modalId:s="modal-search"})=>{const[r,,,a,o]=(0,l.useToggleState)(!1),[n,c]=(0,i.useState)(""),d=oa("selectPreference",[],"userLocale"),u=oa("selectQueryableSearchIndex"),[p,m]=(0,i.useState)([]),f=(0,l.useSvgAria)(),h=et(),y=(0,i.useRef)(null),{platform:_,os:w}=(0,i.useMemo)((()=>{var e,t;return aa().parse(null===(e=window)||void 0===e||null===(t=e.navigator)||void 0===t?void 0:t.userAgent)}),[]),{isMobileMenuOpen:g,setMobileMenuOpen:b}=(0,l.useNavigationContext)(),[v,E]=(0,i.useState)(""),x=(0,i.useMemo)((()=>{switch(d){case"ko-KR":case"zh-CN":case"zh-HK":case"zh-TW":return 1;default:return 2}}),[d]);wn("meta+k",(e=>{e.preventDefault(),"desktop"!==(null==_?void 0:_.type)||r||g||a()}),{enableOnFormTags:!0,enableOnContentEditable:!0},[r,a,_,g]);const k=(0,i.useCallback)((({route:e,fieldId:t})=>{t!==Gi.fieldId&&(b(!1),o(),c(""),m([]),h(`${e}#${t}`))}),[o,c,b]),S=(0,i.useCallback)((0,le.debounce)((e=>{const t=(0,le.trim)(e);if(E(""),t.length<x)return E((0,Lt.__)("Search","wordpress-seo")),!1;const s=(0,le.split)(bn(t,d)," "),r=(0,le.reduce)(u,((e,t)=>{const r=(0,le.reduce)(s,((e,s)=>(0,le.includes)(null==t?void 0:t.keywords,s)?++e:e),0);return 0===r?e:[...e,{...t,hits:r}]}),[]),a=r.sort(((e,t)=>t.hits-e.hits)),o=(0,le.groupBy)(a,"route"),n=(0,le.values)(o).sort(((e,t)=>{const s=(0,le.reduce)(e,((e,t)=>(0,le.max)([e,t.hits])),0);return(0,le.reduce)(t,((e,t)=>(0,le.max)([e,t.hits])),0)-s}));(0,le.isEmpty)(r)?E((0,Lt.__)("No results found","wordpress-seo")):E((0,Lt.sprintf)(/* translators: %d expands to the number of results found. */
(0,Lt._n)("%d result found, use up and down arrow keys to navigate","%d results found, use up and down arrow keys to navigate",r.length,"wordpress-seo"),r.length)),m(n)}),100),[u,d,E]),L=(0,i.useCallback)((e=>{c(e.target.value),S(e.target.value)}),[c,S]),T=(0,i.useCallback)((({active:e})=>or()("yst-group yst-block yst-no-underline yst-text-sm  yst-select-none yst-py-3 yst-px-4 hover:yst-bg-primary-600 hover:yst-text-white focus:yst-bg-primary-600 focus:yst-text-white",e?"yst-text-white yst-bg-primary-600":"yst-text-slate-800")),[]);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("button",{id:t,type:"button",className:"yst-w-full yst-flex yst-items-center yst-bg-white yst-text-sm yst-leading-6 yst-text-slate-500 yst-rounded-md yst-border yst-border-slate-300 yst-shadow-sm yst-py-1.5 yst-pl-2 yst-pr-3 focus:yst-outline-none focus:yst-ring-2 focus:yst-ring-offset-2 focus:yst-ring-primary-500",onClick:a},(0,e.createElement)(tn,{className:"yst-flex-none yst-w-5 yst-h-5 yst-me-3 yst-text-slate-400",...f}),(0,e.createElement)("span",{className:"yst-overflow-hidden yst-whitespace-nowrap yst-text-ellipsis"},n||(0,Lt.__)("Quick search…","wordpress-seo")),"desktop"===(null==_?void 0:_.type)&&(0,e.createElement)("span",{className:"yst-ms-auto yst-flex-none yst-text-xs yst-font-semibold yst-text-slate-400"},"macOS"===(null==w?void 0:w.name)?(0,Lt.__)("⌘K","wordpress-seo"):(0,Lt.__)("CtrlK","wordpress-seo"))),(0,e.createElement)(l.Modal,{id:s,onClose:o,isOpen:r,initialFocus:y,position:"top-center","aria-label":(0,Lt.__)("Search","wordpress-seo")},(0,e.createElement)(l.Modal.Panel,{hasCloseButton:!1},(0,e.createElement)(to,null,v&&(0,e.createElement)(co,{message:v,"aria-live":"polite"})),(0,e.createElement)(en,{as:"div",className:"yst--m-6",onChange:k},(0,e.createElement)("div",{className:"yst-relative"},(0,e.createElement)(tn,{className:"yst-pointer-events-none yst-absolute yst-top-3.5 yst-start-4 yst-h-5 yst-w-5 yst-text-slate-400",...f}),(0,e.createElement)(en.Input,{ref:y,id:"input-search",placeholder:(0,Lt.__)("Search…","wordpress-seo"),"aria-label":(0,Lt.__)("Search","wordpress-seo"),value:n,onChange:L,className:"yst-h-12 yst-w-full yst-border-0 yst-rounded-lg sm:yst-text-sm yst-bg-transparent yst-px-11 yst-text-slate-800 yst-placeholder-slate-500 focus:yst-outline-none focus:yst-ring-inset focus:yst-ring-2 focus:yst-ring-primary-500 focus:yst-border-primary-500"}),(0,e.createElement)("div",{className:"yst-modal__close"},(0,e.createElement)("button",{type:"button",onClick:o,className:"yst-modal__close-button"},(0,e.createElement)("span",{className:"yst-sr-only"},(0,Lt.__)("Close","wordpress-seo")),(0,e.createElement)(sn,{className:"yst-h-6 yst-w-6",...f})))),n.length>=x&&!(0,le.isEmpty)(p)&&(0,e.createElement)(en.Options,{static:!0,className:"yst-max-h-[calc(90vh-10rem)] yst-scroll-pt-11 yst-scroll-pb-2 yst-space-y-2 yst-overflow-y-auto yst-pb-2"},(0,le.map)(p,((t,s)=>{var r;return(0,e.createElement)("div",{key:(null==t||null===(r=t[0])||void 0===r?void 0:r.route)||`group-${s}`,role:"presentation"},(0,e.createElement)(l.Title,{id:`group-${s}-title`,as:"h4",size:"5",className:"yst-bg-slate-100 yst-font-semibold yst-py-3 yst-px-4",role:"presentation","aria-hidden":"true"},(0,le.first)(t).routeLabel),(0,e.createElement)("div",{role:"presentation"},(0,le.map)(t,(t=>(0,e.createElement)(en.Option,{key:t.fieldId,value:t,className:T},(0,e.createElement)(Ki,{...t}))))))}))),n.length<x&&(0,e.createElement)(Zi,{title:(0,Lt.__)("Search","wordpress-seo")},(0,e.createElement)("p",{className:"yst-text-slate-500"},(0,Lt.sprintf)(/* translators: %d expands to the minimum number of characters needed (numerical). */
(0,Lt.__)("Please enter a search term with at least %d characters.","wordpress-seo"),x))),n.length>=x&&(0,le.isEmpty)(p)&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Zi,{title:(0,Lt.__)("No results found","wordpress-seo")},(0,e.createElement)("p",{className:"yst-text-slate-500"},(0,Lt.__)("We couldn’t find anything with that term.","wordpress-seo"))),(0,e.createElement)(en.Options,{className:"yst-visible-"},(0,e.createElement)(en.Option,{value:Gi})))))))};Ji.propTypes={buttonId:ir().string,modalId:ir().string};const Qi=Ji,Xi=({error:t})=>{const s=(0,i.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),r=oa("selectLink",[],"https://yoa.st/settings-error-support");return(0,e.createElement)(yr,{error:t},(0,e.createElement)(yr.HorizontalButtons,{supportLink:r,handleRefreshClick:s}))};function el(t,s,r=""){return lr(t,{a:(0,e.createElement)("a",{id:r,href:s,target:"_blank",rel:"noopener noreferrer"})})}Xi.propTypes={error:ir().object.isRequired};const tl=(t=>{const s=({name:s,isDummy:r=!1,...a})=>{const o=oa("selectDefaultSettingValue",[s],s);return r?(0,e.createElement)(t,{name:s,...a,disabled:!0,value:o,onChange:le.noop,tags:[],onAddTag:le.noop,onRemoveTag:le.noop}):(0,e.createElement)(t,{name:s,...a})};return s.propTypes={name:ir().string.isRequired,isDummy:ir().bool},s})(va),sl=Ia(ga),rl=({name:t,label:s,singularLabel:r,hasArchive:a,hasSchemaArticleType:o,isNew:n})=>{const c=oa("selectReplacementVariablesFor",[t],t,"custom_post_type"),d=oa("selectUpsellSettingsAsProps"),u=oa("selectRecommendedReplacementVariablesFor",[t],t,"custom_post_type"),p=oa("selectReplacementVariablesFor",[t],`${t}_archive`,"custom-post-type_archive"),m=oa("selectRecommendedReplacementVariablesFor",[t],`${t}_archive`,"custom-post-type_archive"),f=oa("selectPreference",[],"isPremium"),h=oa("selectLink",[],"https://yoa.st/4cr"),y=oa("selectArticleTypeValuesFor",[t],t),_=oa("selectPageTypeValuesFor",[t],t),w=oa("selectPreference",[],"isWooCommerceActive"),g=oa("selectPreference",[],"hasWooCommerceShopPage"),b=oa("selectPreference",[],"editWooCommerceShopPageUrl"),v=oa("selectPreference",[],"wooCommerceShopPageSettingUrl"),E=oa("selectPreference",[],"userLocale"),x=oa("selectLink",[],"https://yoa.st/show-x"),k=oa("selectLink",[],"https://yoa.st/4e0"),S=oa("selectLink",[],"https://yoa.st/get-custom-fields"),L=oa("selectLink",[],"https://yoa.st/post-type-schema"),{updatePostTypeReviewStatus:T}=sa(),F=oa("selectPreference",[],"isWooCommerceSEOActive")&&"product"===t,$=(0,Lt.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,Lt.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO");(0,i.useEffect)((()=>{n&&T(t)}),[t,T]);const P=(0,i.useMemo)((()=>bn(s,E)),[s,E]),R=(0,i.useMemo)((()=>bn(r,E)),[r,E]),N=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),O=(0,i.useMemo)((()=>w&&"product"===t),[t,w]),C=(0,i.useMemo)((()=>g?el((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("You can edit the SEO metadata for this custom type on the %1$sShop page%2$s.","wordpress-seo"),"<a>","</a>"),b,"link-edit-woocommerce-shop-page"):el((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("You haven't set a Shop page in your WooCommerce settings. %1$sPlease do this first%2$s.","wordpress-seo"),"<a>","</a>"),v,"link-woocommerce-shop-page-setting")),[g,v,b]),A=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <em> tags.
(0,Lt.__)("You can add multiple custom fields and separate them by using %1$senter%2$s or %1$scomma%2$s.","wordpress-seo"),"<em>","</em>"),{em:(0,e.createElement)("em",null)})),[]),I=(0,i.useMemo)((()=>el((0,Lt.sprintf)(
/* translators: %1$s expands to the post type plural, e.g. posts.
   * %2$s and %3$s expand to opening and closing anchor tag. %4$s expands to "Yoast SEO".
   */
(0,Lt.__)("Determine how your %1$s should be described by default in %2$syour site's Schema.org markup%3$s. You can always change the settings for individual %1$s in the %4$s sidebar or metabox.","wordpress-seo"),P,"<a>","</a>","Yoast SEO"),L,"link-post-type-schema")),[P,L]),{values:M}=q(),{opengraph:D}=M.wpseo_social,{"breadcrumbs-enable":B}=M.wpseo_titles;return(0,e.createElement)(po,{title:s,description:(0,Lt.sprintf)(/* translators: %1$s expands to the post type plural, e.g. posts. */
(0,Lt.__)("Determine how your %1$s should look in search engines and on social media.","wordpress-seo"),P)},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts. %2$s expands to "Yoast SEO".
(0,Lt.__)("Determine what your %1$s should look like in the search results by default. You can always customize the settings for individual %1$s in the %2$s sidebar or metabox.","wordpress-seo"),P,"Yoast SEO")},(0,e.createElement)(pa,{name:`wpseo_titles.noindex-${t}`,id:`input-wpseo_titles-noindex-${t}`,label:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts.
(0,Lt.__)("Show %1$s in search results","wordpress-seo"),P),description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts.
(0,Lt.__)("Disabling this means that %1$s will not be indexed by search engines and will be excluded from XML sitemaps.","wordpress-seo"),P)," ",(0,e.createElement)(l.Link,{href:x,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about the search results settings","wordpress-seo")),"."),className:"yst-max-w-sm"}),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ga,{type:"title",name:`wpseo_titles.title-${t}`,fieldId:`input-wpseo_titles-title-${t}`,label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:u}),(0,e.createElement)(ga,{type:"description",name:`wpseo_titles.metadesc-${t}`,fieldId:`input-wpseo_titles-metadesc-${t}`,label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:u,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)("div",{className:"yst-flex yst-items-center yst-gap-1.5"},(0,e.createElement)("span",null,(0,Lt.__)("Social media appearance","wordpress-seo")),f&&(0,e.createElement)(l.Badge,{variant:"upsell"},"Premium")),description:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts. %2$s expands to "Yoast SEO".
(0,Lt.__)("Determine how your %1$s should look on social media by default. You can always customize the settings for individual %1$s in the %2$s sidebar or metabox.","wordpress-seo"),P,"Yoast SEO")},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!f,variant:"card",cardLink:k,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...d},(0,e.createElement)(Ha,{isEnabled:!f||D}),(0,e.createElement)(ya,{id:`wpseo_titles-social-image-${t}`,label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:N,mediaUrlName:`wpseo_titles.social-image-url-${t}`,mediaIdName:`wpseo_titles.social-image-id-${t}`,disabled:!D,isDummy:!f}),(0,e.createElement)(sl,{type:"title",name:`wpseo_titles.social-title-${t}`,fieldId:`input-wpseo_titles-social-title-${t}`,label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:u,disabled:!D,isDummy:!f}),(0,e.createElement)(sl,{type:"description",name:`wpseo_titles.social-description-${t}`,fieldId:`input-wpseo_titles-social-description-${t}`,label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:u,className:"yst-replacevar--description",disabled:!D,isDummy:!f}))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Schema","wordpress-seo"),description:I},(0,e.createElement)(Na,{as:l.SelectField,type:"select",name:`wpseo_titles.schema-page-type-${t}`,id:`input-wpseo_titles-schema-page-type-${t}`,label:(0,Lt.__)("Page type","wordpress-seo"),options:F?_.filter((({value:e})=>"ItemPage"===e)):_,disabled:F,className:"yst-max-w-sm",description:F?$:null}),o&&(0,e.createElement)("div",null,(0,e.createElement)(Na,{as:l.SelectField,type:"select",name:`wpseo_titles.schema-article-type-${t}`,id:`input-wpseo_titles-schema-article-type-${t}`,label:(0,Lt.__)("Article type","wordpress-seo"),options:y,className:"yst-max-w-sm"}),(0,e.createElement)(qa,{name:t}))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Additional settings","wordpress-seo")},(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:`wpseo_titles.display-metabox-pt-${t}`,id:`input-wpseo_titles-display-metabox-pt-${t}`,label:(0,Lt.__)("Enable SEO controls and assessments","wordpress-seo"),description:(0,Lt.__)("Show or hide our tools and controls in the content editor.","wordpress-seo"),className:"yst-max-w-sm"}),(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!f,variant:"card",cardLink:S,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...d},(0,e.createElement)(tl,{name:`wpseo_titles.page-analyse-extra-${t}`,id:`input-wpseo_titles-page-analyse-extra-${t}`,label:(0,Lt.__)("Add custom fields to page analysis","wordpress-seo"),labelSuffix:f&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium"),description:(0,e.createElement)(e.Fragment,null,A,(0,e.createElement)("br",null),(0,e.createElement)(l.Link,{id:`link-custom-fields-page-analysis-${t}`,href:h,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about our custom field analysis","wordpress-seo")),"."),isDummy:!f}))),a&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)("hr",{className:"yst-my-16"}),(0,e.createElement)("div",{className:"yst-mb-8"},(0,e.createElement)(l.Title,{as:"h2",className:"yst-mb-2"},(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. Posts.
(0,Lt.__)("%1$s archive","wordpress-seo"),s)),(0,e.createElement)("p",{className:"yst-text-tiny"},O&&C,!O&&(0,Lt.sprintf)(
// translators: %1$s expands to the post type singular, e.g. post.
(0,Lt.__)("These settings are specifically for optimizing your %1$s archive.","wordpress-seo"),R))),!O&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts.
(0,Lt.__)("Determine how your %1$s archive should look in search engines.","wordpress-seo"),P)},(0,e.createElement)(pa,{name:`wpseo_titles.noindex-ptarchive-${t}`,id:`input-wpseo_titles-noindex-ptarchive-${t}`,label:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts.
(0,Lt.__)("Show the archive for %1$s in search results","wordpress-seo"),P),description:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts.
(0,Lt.__)("Disabling this means that the archive for %1$s will not be indexed by search engines and will be excluded from XML sitemaps.","wordpress-seo"),P),className:"yst-max-w-sm"}),(0,e.createElement)(ga,{type:"title",name:`wpseo_titles.title-ptarchive-${t}`,fieldId:`input-wpseo_titles-title-ptarchive-${t}`,label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:p,recommendedReplacementVariables:m}),(0,e.createElement)(ga,{type:"description",name:`wpseo_titles.metadesc-ptarchive-${t}`,fieldId:`input-wpseo_titles-metadesc-ptarchive-${t}`,label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:p,recommendedReplacementVariables:m,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)("div",{className:"yst-flex yst-items-center yst-gap-1.5"},(0,e.createElement)("span",null,(0,Lt.__)("Social media appearance","wordpress-seo")),f&&(0,e.createElement)(l.Badge,{variant:"upsell"},"Premium")),description:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. posts.
(0,Lt.__)("Determine how your %1$s archive should look on social media.","wordpress-seo"),P)},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!f,variant:"card",cardLink:k,cardText:(0,Lt.sprintf)(
// translators: %1$s expands to Premium.
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...d},(0,e.createElement)(ya,{id:`wpseo_titles-social-image-ptarchive-${t}`,label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:N,mediaUrlName:`wpseo_titles.social-image-url-ptarchive-${t}`,mediaIdName:`wpseo_titles.social-image-id-ptarchive-${t}`,disabled:!D,isDummy:!f}),(0,e.createElement)(sl,{type:"title",name:`wpseo_titles.social-title-ptarchive-${t}`,fieldId:`input-wpseo_titles-social-title-ptarchive-${t}`,label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:p,recommendedReplacementVariables:m,disabled:!D,isDummy:!f}),(0,e.createElement)(sl,{type:"description",name:`wpseo_titles.social-description-ptarchive-${t}`,fieldId:`input-wpseo_titles-social-description-ptarchive-${t}`,label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:p,recommendedReplacementVariables:m,className:"yst-replacevar--description",disabled:!D,isDummy:!f}))),B&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Additional settings","wordpress-seo")},(0,e.createElement)(te,{as:l.TextField,type:"text",name:`wpseo_titles.bctitle-ptarchive-${t}`,id:`input-wpseo_titles-bctitle-ptarchive-${t}`,label:(0,Lt.__)("Breadcrumbs title","wordpress-seo")}))))))))};rl.propTypes={name:ir().string.isRequired,label:ir().string.isRequired,singularLabel:ir().string.isRequired,hasArchive:ir().bool.isRequired,hasSchemaArticleType:ir().bool.isRequired,isNew:ir().bool.isRequired};const al=rl,ol=Ia(ga),nl=({name:t,label:s,postTypes:r,showUi:a,isNew:o})=>{const n=oa("selectPostTypes",[r],r),c=oa("selectUpsellSettingsAsProps"),d=oa("selectReplacementVariablesFor",[t],t,"term-in-custom-taxonomy"),u=oa("selectRecommendedReplacementVariablesFor",[t],t,"term-in-custom-taxonomy"),p=oa("selectLink",[],"https://yoa.st/show-x"),m=oa("selectPreference",[],"isPremium"),f=oa("selectPreference",[],"userLocale"),h=oa("selectPreference",[],"editTaxonomyUrl"),y=oa("selectLink",[],"https://yoa.st/4e0"),_=(0,i.useMemo)((()=>bn(s,f)),[s,f]),w=(0,i.useMemo)((()=>(0,le.values)(n)),[n]),g=(0,i.useMemo)((()=>(0,le.initial)(w)),[w]),b=(0,i.useMemo)((()=>(0,le.last)(w)),[w]),{updateTaxonomyReviewStatus:v}=sa();(0,i.useEffect)((()=>{o&&v(t)}),[t,v]);const E=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),x=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %s expands to <code>/category/</code> */
(0,Lt.__)("Category URLs in WordPress contain a prefix, usually %s. Show or hide that prefix in category URLs.","wordpress-seo"),"<code />"),{code:(0,e.createElement)(l.Code,null,"/category/")})),[]),k=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s and %2$s expand to post type plurals in code blocks, e.g. Posts Pages and Custom Post Type.
   */
(0,Lt.__)("This taxonomy is used for %1$s and %2$s.","wordpress-seo"),"<code1 />","<code2 />"),{code1:(0,e.createElement)(e.Fragment,null,(0,le.map)(g,((t,s)=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.Code,{key:null==t?void 0:t.name},null==t?void 0:t.label),s<g.length-1&&" ")))),code2:(0,e.createElement)(l.Code,null,null==b?void 0:b.label)})),[s,g,b]),S=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to the post type plural in code block, e.g. Posts.
   */
(0,Lt.__)("This taxonomy is used for %2$s.","wordpress-seo"),s,"<code />"),{code:(0,e.createElement)(l.Code,null,null==b?void 0:b.label)})),[s,b]),{values:L}=q(),{opengraph:T}=L.wpseo_social,F=(0,i.useMemo)((()=>g.length>1?k:S),[g,k,S]),$=(0,i.useCallback)((()=>a&&(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:`wpseo_titles.display-metabox-tax-${t}`,id:`input-wpseo_titles-display-metabox-tax-${t}`,label:(0,Lt.__)("Enable SEO controls and assessments","wordpress-seo"),description:(0,Lt.__)("Show or hide our tools and controls in the content editor.","wordpress-seo"),className:"yst-max-w-sm"})),[a,t]),P=(0,i.useCallback)((()=>"category"===t&&(0,e.createElement)(pa,{name:"wpseo_titles.stripcategorybase",id:"input-wpseo_titles-stripcategorybase",label:(0,Lt.__)("Show the categories prefix in the slug","wordpress-seo"),description:x,className:"yst-max-w-sm"})),[t,x]),R=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
     * translators: %1$s expands to the name of the taxonomy.
     */
(0,Lt.__)("The name of this category is %1$s.","wordpress-seo"),"<link />"),{link:(0,e.createElement)(l.Link,{href:`${h}?taxonomy=${t}`},t)})),[t]);return(0,e.createElement)(po,{title:s,description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(/* translators: %1$s expands to the taxonomy plural, e.g. categories. */
(0,Lt.__)("Determine how your %1$s should look in search engines and on social media.","wordpress-seo"),_),(0,e.createElement)("br",null),(0,le.isEmpty)(w)?R:F)},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to the post type plural, e.g. Posts. %2$s expands to "Yoast SEO".
(0,Lt.__)("Determine what your %1$s should look like in the search results by default. You can always customize the settings for individual %1$s in the %2$s metabox.","wordpress-seo"),_,"Yoast SEO")},(0,e.createElement)(pa,{name:`wpseo_titles.noindex-tax-${t}`,id:`input-wpseo_titles-noindex-tax-${t}`,label:(0,Lt.sprintf)(
// translators: %1$s expands to the taxonomy plural, e.g. Categories.
(0,Lt.__)("Show %1$s in search results","wordpress-seo"),_),description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(
// translators: %1$s expands to the taxonomy plural, e.g. Categories.
(0,Lt.__)("Disabling this means that archive pages for %1$s will not be indexed by search engines and will be excluded from XML sitemaps.","wordpress-seo"),_)," ",(0,e.createElement)(l.Link,{href:p,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about the search results settings","wordpress-seo")),"."),className:"yst-max-w-sm"}),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ga,{type:"title",name:`wpseo_titles.title-tax-${t}`,fieldId:`input-wpseo_titles-title-tax-${t}`,label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u}),(0,e.createElement)(ga,{type:"description",name:`wpseo_titles.metadesc-tax-${t}`,fieldId:`input-wpseo_titles-metadesc-tax-${t}`,label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)("div",{className:"yst-flex yst-items-center yst-gap-1.5"},(0,e.createElement)("span",null,(0,Lt.__)("Social media appearance","wordpress-seo")),m&&(0,e.createElement)(l.Badge,{variant:"upsell"},"Premium")),description:(0,Lt.sprintf)(
// translators: %1$s expands to the taxonomy plural, e.g. Categories. %2$s expand to Yoast SEO.
(0,Lt.__)("Determine how your %1$s should look on social media by default. You can always customize the settings for individual %1$s in the %2$s metabox.","wordpress-seo"),_,"Yoast SEO")},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!m,variant:"card",cardLink:y,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...c},(0,e.createElement)(Ha,{isEnabled:!m||T}),(0,e.createElement)(ya,{id:`wpseo_titles-social-image-tax-${t}`,label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:E,mediaUrlName:`wpseo_titles.social-image-url-tax-${t}`,mediaIdName:`wpseo_titles.social-image-id-tax-${t}`,disabled:!T,isDummy:!m}),(0,e.createElement)(ol,{type:"title",name:`wpseo_titles.social-title-tax-${t}`,fieldId:`input-wpseo_titles-social-title-tax-${t}`,label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,disabled:!T,isDummy:!m}),(0,e.createElement)(ol,{type:"description",name:`wpseo_titles.social-description-tax-${t}`,fieldId:`input-wpseo_titles-social-description-tax-${t}`,label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,className:"yst-replacevar--description",disabled:!T,isDummy:!m}))),(a||"category"===t)&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Additional settings","wordpress-seo")},$(),P())))))};nl.propTypes={name:ir().string.isRequired,label:ir().string.isRequired,postTypes:ir().arrayOf(ir().string).isRequired,showUi:ir().bool.isRequired,isNew:ir().bool.isRequired};const il=nl,ll=Ia(ga),cl=()=>{const t=(0,Lt.__)("Author archives","wordpress-seo"),s=(0,Lt.__)("Author archive","wordpress-seo"),r=oa("selectPreference",[],"userLocale"),a=(0,i.useMemo)((()=>bn(t,r)),[t,r]),o=(0,i.useMemo)((()=>bn(s,r)),[s,r]),n=oa("selectUpsellSettingsAsProps"),c=oa("selectReplacementVariablesFor",[],"author_archives","custom-post-type_archive"),d=oa("selectRecommendedReplacementVariablesFor",[],"author_archives","custom-post-type_archive"),u=oa("selectLink",[],"https://yoa.st/duplicate-content"),p=oa("selectLink",[],"https://yoa.st/show-x"),m=oa("selectPreference",[],"isPremium"),f=oa("selectExampleUrl",[],"/author/example/"),h=oa("selectLink",[],"https://yoa.st/4e0"),y=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),_=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to "author archive".
   * %2$s expands to an example URL, e.g. https://example.com/author/example/.
   * %3$s and %4$s expand to opening and closing <a> tags.
   */
(0,Lt.__)("If you're running a one author blog, the %1$s (e.g. %2$s) will be exactly the same as your homepage. This is what's called a %3$sduplicate content problem%4$s. If this is the case on your site, you can choose to either disable it (which makes it redirect to the homepage), or prevent it from showing up in search results.","wordpress-seo"),o,"<exampleUrl />","<a>","</a>"),{exampleUrl:(0,e.createElement)(l.Code,null,f),a:(0,e.createElement)("a",{href:u,target:"_blank",rel:"noopener"})})),[]),{values:w}=q(),{opengraph:g}=w.wpseo_social,{"disable-author":b,"noindex-author-wpseo":v,"noindex-author-noposts-wpseo":E}=w.wpseo_titles;return(0,e.createElement)(po,{title:t,description:_},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(pa,{name:"wpseo_titles.disable-author",id:"input-wpseo_titles-disable-author",label:(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Enable %1$s","wordpress-seo"),a),description:(0,Lt.sprintf)(
// translators: %1$s expands to "author archive".
(0,Lt.__)("Disabling this will redirect the %1$s to your site's homepage.","wordpress-seo"),o),className:"yst-max-w-sm"}),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Determine how your %1$s should look in search engines.","wordpress-seo"),a)},(0,e.createElement)(pa,{name:"wpseo_titles.noindex-author-wpseo",id:"input-wpseo_titles-noindex-author-wpseo",label:(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Show %1$s in search results","wordpress-seo"),a),description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Disabling this means that %1$s will not be indexed by search engines and will be excluded from XML sitemaps.","wordpress-seo"),a)," ",(0,e.createElement)(l.Link,{href:p,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about the search results settings","wordpress-seo")),"."),disabled:b,checked:!b&&!v,className:"yst-max-w-sm"}),(0,e.createElement)(pa,{name:"wpseo_titles.noindex-author-noposts-wpseo",id:"input-wpseo_titles-noindex-author-noposts-wpseo",label:(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Show %1$s without posts in search results","wordpress-seo"),a),description:(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Disabling this means that %1$s without any posts will not be indexed by search engines and will be excluded from XML sitemaps.","wordpress-seo"),a),checked:!b&&!v&&!E,disabled:b||v,className:"yst-max-w-sm"}),(0,e.createElement)(ga,{type:"title",name:"wpseo_titles.title-author-wpseo",fieldId:"input-wpseo_titles-title-author-wpseo",label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:d,disabled:b}),(0,e.createElement)(ga,{type:"description",name:"wpseo_titles.metadesc-author-wpseo",fieldId:"input-wpseo_titles-metadesc-author-wpseo",label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:d,disabled:b,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)("div",{className:"yst-flex yst-items-center yst-gap-1.5"},(0,e.createElement)("span",null,(0,Lt.__)("Social media appearance","wordpress-seo")),m&&(0,e.createElement)(l.Badge,{variant:"upsell"},"Premium")),description:(0,Lt.sprintf)(
// translators: %1$s expands to "author archives".
(0,Lt.__)("Determine how your %1$s should look on social media.","wordpress-seo"),a)},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!m,variant:"card",cardLink:h,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...n},(0,e.createElement)(Ha,{isEnabled:!m||g}),(0,e.createElement)(ya,{id:"wpseo_titles-social-image-author-wpseo",label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:y,mediaUrlName:"wpseo_titles.social-image-url-author-wpseo",mediaIdName:"wpseo_titles.social-image-id-author-wpseo",disabled:b||!g,isDummy:!m}),(0,e.createElement)(ll,{type:"title",name:"wpseo_titles.social-title-author-wpseo",fieldId:"input-wpseo_titles-social-title-author-wpseo",label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:d,disabled:b||!g,isDummy:!m}),(0,e.createElement)(ll,{type:"description",name:"wpseo_titles.social-description-author-wpseo",fieldId:"input-wpseo_titles-social-description-author-wpseo",label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:c,recommendedReplacementVariables:d,className:"yst-replacevar--description",disabled:b||!g,isDummy:!m}))))))},dl=()=>{const t=oa("selectLink",[],"https://yoa.st/header-breadcrumbs"),s=oa("selectLink",[],"https://yoa.st/breadcrumbs"),r=oa("selectBreadcrumbsForPostTypes"),a=oa("selectBreadcrumbsForTaxonomies"),o=oa("selectHasPageForPosts");return(0,e.createElement)(po,{title:(0,Lt.__)("Breadcrumbs","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,Lt.__)("Configure the appearance and behavior of %1$syour breadcrumbs%2$s.","wordpress-seo"),"<a>","</a>"),t,"link-header-breadcrumbs")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Breadcrumb appearance","wordpress-seo"),description:(0,Lt.__)("Choose the general appearance of your breadcrumbs.","wordpress-seo")},(0,e.createElement)(te,{as:l.TextField,type:"text",name:"wpseo_titles.breadcrumbs-sep",id:"input-wpseo_titles-breadcrumbs-sep",label:(0,Lt.__)("Separator between breadcrumbs","wordpress-seo"),placeholder:(0,Lt.__)("Add separator","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextField,type:"text",name:"wpseo_titles.breadcrumbs-home",id:"input-wpseo_titles-breadcrumbs-home",label:(0,Lt.__)("Anchor text for the Homepage","wordpress-seo"),placeholder:(0,Lt.__)("Add anchor text","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextField,type:"text",name:"wpseo_titles.breadcrumbs-prefix",id:"input-wpseo_titles-breadcrumbs-prefix",label:(0,Lt.__)("Prefix for the breadcrumb path","wordpress-seo"),placeholder:(0,Lt.__)("Add prefix","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextField,type:"text",name:"wpseo_titles.breadcrumbs-archiveprefix",id:"input-wpseo_titles-breadcrumbs-archiveprefix",label:(0,Lt.__)("Prefix for archive breadcrumbs","wordpress-seo"),placeholder:(0,Lt.__)("Add prefix","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextField,type:"text",name:"wpseo_titles.breadcrumbs-searchprefix",id:"input-wpseo_titles-breadcrumbs-searchprefix",label:(0,Lt.__)("Prefix for search page breadcrumbs","wordpress-seo"),placeholder:(0,Lt.__)("Add prefix","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextField,type:"text",name:"wpseo_titles.breadcrumbs-404crumb",id:"input-wpseo_titles-breadcrumbs-404crumb",label:(0,Lt.__)("Breadcrumb for 404 page","wordpress-seo"),placeholder:(0,Lt.__)("Add separator","wordpress-seo")}),o&&(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:"wpseo_titles.breadcrumbs-display-blog-page",id:"input-wpseo_titles-breadcrumbs-display-blog-page",label:(0,Lt.__)("Show blog page in breadcrumbs","wordpress-seo"),className:"yst-max-w-sm"}),(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:"wpseo_titles.breadcrumbs-boldlast",id:"input-wpseo_titles-breadcrumbs-boldlast",label:(0,Lt.__)("Bold the last page","wordpress-seo"),className:"yst-max-w-sm"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Breadcrumbs for post types","wordpress-seo"),description:(0,Lt.__)("Choose which Taxonomy you wish to show in the breadcrumbs for Post types.","wordpress-seo")},(0,le.map)(r,((t,s)=>(0,e.createElement)(Na,{key:s,as:l.SelectField,name:`wpseo_titles.post_types-${s}-maintax`,id:`input-wpseo_titles-post_types-${s}-maintax`,label:t.label,labelSuffix:(0,e.createElement)(l.Code,{className:"yst-ml-2 rtl:yst-mr-2"},s),options:t.options,className:"yst-max-w-sm"})))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Breadcrumbs for taxonomies","wordpress-seo"),description:(0,Lt.__)("Choose which Post type you wish to show in the breadcrumbs for Taxonomies.","wordpress-seo")},(0,le.map)(a,(t=>(0,e.createElement)(Na,{key:t.name,as:l.SelectField,name:`wpseo_titles.taxonomy-${t.name}-ptparent`,id:`input-wpseo_titles-taxonomy-${t.name}-ptparent`,label:t.label,options:t.options,className:"yst-max-w-sm",labelSuffix:(0,e.createElement)(l.Code,{className:"yst-ml-2 rtl:yst-mr-2"},t.name)})))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("How to insert breadcrumbs in your theme","wordpress-seo")},(0,e.createElement)("p",null,el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags. %3$s expands to "Yoast SEO".
(0,Lt.__)("Not sure how to implement the %3$s breadcrumbs on your site? Read %1$sour help article on breadcrumbs implementation%2$s.","wordpress-seo"),"<a>","</a>","Yoast SEO"),s,"link-breadcrumbs-help-article")),(0,e.createElement)("p",null,(0,Lt.__)("You can always choose to enable/disable them for your theme below. This setting will not apply to breadcrumbs inserted through a widget, a block or a shortcode.","wordpress-seo")),(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:"wpseo_titles.breadcrumbs-enable",id:"input-wpseo_titles-breadcrumbs-enable",label:(0,Lt.__)("Enable breadcrumbs for your theme","wordpress-seo"),className:"yst-max-w-sm"})))))},ul=Da(te),pl=Aa(Na),ml=Ma(pl),fl=()=>{const t=oa("selectPreference",[],"isPremium",!1),s=oa("selectPreference",[],"isMultisite",!1),r=oa("selectUpsellSettingsAsProps"),a=oa("selectLink",[],"https://yoa.st/crawl-settings"),o=oa("selectLink",[],"https://yoa.st/permalink-cleanup"),n=oa("selectLink",[],"https://yoa.st/block-unwanted-bots-info"),c=oa("selectLink",[],"https://yoa.st/block-unwanted-bots-upsell"),d=(0,i.useMemo)((()=>(0,Lt.sprintf)(/* translators: %1$s expands to an example within a code tag. */
(0,Lt.__)("E.g., %1$s","wordpress-seo"),"<code/>")),[]),u=(0,i.useMemo)((()=>(0,Lt.sprintf)(/* translators: %1$s and %2$s both expand to an example within a code tag. */
(0,Lt.__)("E.g., %1$s and %2$s","wordpress-seo"),"<code1/>","<code2/>")),[]),p=(0,i.useMemo)((()=>({page:lr((0,Lt.sprintf)(/* translators: %1$s and %2$s are replaced by opening and closing <a> tags. */
(0,Lt.__)("Make your site more efficient and more environmentally friendly by preventing search engines from crawling things they don’t need to, and by removing unused WordPress features. %1$sLearn more about crawl settings and how they could benefit your site%2$s.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)("a",{id:"link-crawl-settings-info",href:a,target:"_blank",rel:"noopener noreferrer"})}),removeUnwantedMetadata:lr((0,Lt.sprintf)(/* translators: %1$s expands to `<head>` within a <code> tag. */
(0,Lt.__)("WordPress adds a lot of links and content to your site's %1$s and HTTP headers. For most websites you can safely disable all of these, which can help to save bytes, electricity, and trees.","wordpress-seo"),"<code/>"),{code:(0,e.createElement)(l.Code,null,"<head>")}),removeShortlinks:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="shortlink" href="https://www.example.com/?p=1" />')}),removeRestApiLinks:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="https://api.w.org/" href="https://www.example.com/wp-json/" />')}),removeRsdWlwLinks:lr(u,{code1:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://www.example.com/xmlrpc.php?rsd" />'),code2:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="https://www.example.com/wp-includes/wlwmanifest.xml" />')}),removeOembedLinks:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="alternate" type="application/json+oembed" href="https://www.example.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fwww.example.com%2Fexample-post%2F" />')}),removeGenerator:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<meta name="generator" content="WordPress 6.0.1" />')}),removePingbackHeader:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},"X-Pingback: https://www.example.com/xmlrpc.php")}),removePoweredByHeader:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},"X-Powered-By: PHP/7.4.1")}),removeFeedGlobal:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="alternate" type="application/rss+xml" title="Example Website - Feed" href="https://www.example.com/feed/" />')}),removeFeedGlobalComments:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="alternate" type="application/rss+xml" title="Example Website - Comments Feed" href="https://www.example.com/comments/feed/" />')}),removeFeedPostComments:lr(d,{code:(0,e.createElement)(l.Code,{variant:"block"},'<link rel="alternate" type="application/rss+xml" title="Example Website - Example post Comments Feed" href="https://www.example.com/example-post/feed/" />')}),removeFeedAuthors: <AUTHORS>
(0,Lt.__)("Lots of web traffic comes from bots crawling the web. Some can benefit your site or business, while other bots don't. Blocking unwanted bots can save energy, help with site performance, and protect copyrighted content. Learn more about %1$swhen to block unwanted bots%2$s.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)(xr,{id:"link-block-unwanted-bots-info",href:n})}),redirectSearchPrettyUrls:lr((0,Lt.sprintf)(/* translators: %1$s, %2$s and %3$s expand to example parts of a URL, surrounded by <code> tags. */
(0,Lt.__)("Consolidates WordPress' multiple site search URL formats into the %1$s syntax. E.g., %2$s will redirect to %3$s","wordpress-seo"),"<code1/>","<code2/>","<code3/>"),{code1:(0,e.createElement)(l.Code,null,"?s="),code2:(0,e.createElement)(l.Code,{variant:"block"},"https://www.example.com/search/cats"),code3:(0,e.createElement)(l.Code,{variant:"block"},"https://www.example.com/?s=cats")}),denySearchCrawling:lr((0,Lt.sprintf)(/* translators: %1$s, %2$s and %3$s expand to example parts of a URL, surrounded by <code> tags. */
(0,Lt.__)("Add a ‘disallow’ rule to your robots.txt file to prevent crawling of URLs like %1$s, %2$s and %3$s.","wordpress-seo"),"<code1/>","<code2/>","<code3/>"),{code1:(0,e.createElement)(l.Code,null,"?s="),code2:(0,e.createElement)(l.Code,null,"/search/"),code3:(0,e.createElement)(l.Code,null,"/page/*/?s=")}),advancedUrlCleanup:lr((0,Lt.sprintf)(/* translators: %1$s expands to an example part of a URL, surrounded by a <code> tag. */
(0,Lt.__)("Users and search engines may often request your URLs whilst using query parameters, like %1$s. These can be helpful for tracking, filtering, and powering advanced functionality - but they come with a performance and SEO ‘cost’. Sites which don’t rely on URL parameters might benefit from using these options.","wordpress-seo"),"<code/>"),{code:(0,e.createElement)(l.Code,null,"?color=red")}),cleanCampaignTrackingUrls:lr((0,Lt.sprintf)(
/**
     * translators:
     * %1$s expands to `<code>utm</code>`.
     * %2$s expands to `<code>#</code>`.
     * %3$s expands to `<code>301</code>`.
     * %4$s and %5$s both expand to an example within a <code> tag.
     */
(0,Lt.__)("Replaces %1$s tracking parameters with the (more performant) %2$s equivalent, via a %3$s redirect. E.g., %4$s will be redirected to %5$s","wordpress-seo"),"<code1/>","<code2/>","<code3/>","<code4/>","<code5/>"),{code1:(0,e.createElement)(l.Code,null,"utm"),code2:(0,e.createElement)(l.Code,null,"#"),code3:(0,e.createElement)(l.Code,null,"301"),code4:(0,e.createElement)(l.Code,{variant:"block"},"https://www.example.com/?utm_medium=organic"),code5:(0,e.createElement)(l.Code,{variant:"block"},"https://www.example.com/#utm_medium=organic")}),cleanPermalinks:lr((0,Lt.sprintf)(
/**
     * translators:
     * %1$s expands to `<code>301</code>`.
     * %2$s and %3$s both expand to an example within a <code> tag.
     */
(0,Lt.__)("Removes unknown URL parameters via a %1$s redirect. E.g., %2$s will be redirected to %3$s","wordpress-seo"),"<code1/>","<code2/>","<code3/>")+(0,Lt.sprintf)(
/**
     * translators:
     * %1$s through %7$s each expand to a parameter name within a <code> tag. For example, <code>gclid</code>.
     */
(0,Lt.__)("Note that the following commonly-used parameters will not be removed: %1$s, %2$s, %3$s, %4$s, %5$s, %6$s, and %7$s.","wordpress-seo"),"<code4/>","<code5/>","<code6/>","<code7/>","<code8/>","<code9/>","<code10/>"),{code1:(0,e.createElement)(l.Code,null,"301"),code2:(0,e.createElement)(l.Code,{variant:"block"},"https://www.example.com/?unknown_parameter=yes"),code3:(0,e.createElement)(l.Code,{variant:"block"},"https://www.example.com"),code4:(0,e.createElement)(l.Code,null,"gclid"),code5:(0,e.createElement)(l.Code,null,"gtm_debug"),code6:(0,e.createElement)(l.Code,null,"utm_campaign"),code7:(0,e.createElement)(l.Code,null,"utm_content"),code8:(0,e.createElement)(l.Code,null,"utm_medium"),code9:(0,e.createElement)(l.Code,null,"utm_source"),code10:(0,e.createElement)(l.Code,null,"utm_term")}),cleanPermalinksExtraVariables:lr((0,Lt.sprintf)(
/**
     * translators:
     * %1$s expands to `<code>unknown_parameter</code>`.
     * %2$s and %3$s both expand to an example within a <code> tag.
     */
(0,Lt.__)("Prevents specific URL parameters from being removed by the above feature. E.g., adding %1$s will prevent %2$s from being redirected to %3$s. You can add multiple parameters and separate them by using enter or a comma.","wordpress-seo"),"<code1/>","<code2/>","<code3/>"),{code1:(0,e.createElement)(l.Code,null,"unknown_parameter"),code2:(0,e.createElement)(l.Code,null,"https://www.example.com/?unknown_parameter=yes"),code3:(0,e.createElement)(l.Code,null,"https://www.example.com")})})),[]),{values:m}=q(),{remove_feed_global_comments:f,remove_feed_post_comments:h,search_cleanup:y,search_cleanup_emoji:_,search_cleanup_patterns:w,clean_permalinks:g}=m.wpseo;return(0,e.createElement)(po,{title:(0,Lt.__)("Crawl optimization","wordpress-seo"),description:p.page},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Remove unwanted metadata","wordpress-seo"),description:p.removeUnwantedMetadata},(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_shortlinks",id:"input-wpseo-remove_shortlinks",label:(0,Lt.__)("Remove shortlinks","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove links to WordPress' internal 'shortlink' URLs for your posts.","wordpress-seo")," ",p.removeShortlinks),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_rest_api_links",id:"input-wpseo-remove_rest_api_links",label:(0,Lt.__)("Remove REST API links","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove links to the location of your site’s REST API endpoints.","wordpress-seo")," ",p.removeRestApiLinks),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_rsd_wlw_links",id:"input-wpseo-remove_rsd_wlw_links",label:(0,Lt.__)("Remove RSD / WLW links","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove links used by external systems for publishing content to your blog.","wordpress-seo")," ",p.removeRsdWlwLinks),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_oembed_links",id:"input-wpseo-remove_oembed_links",label:(0,Lt.__)("Remove oEmbed links","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove links used for embedding your content on other sites.","wordpress-seo")," ",p.removeOembedLinks),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_generator",id:"input-wpseo-remove_generator",label:(0,Lt.__)("Remove generator tag","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove information about the plugins and software used by your site.","wordpress-seo")," ",p.removeGenerator),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_pingback_header",id:"input-wpseo-remove_pingback_header",label:(0,Lt.__)("Pingback HTTP header","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove links which allow others sites to ‘ping’ yours when they link to you.","wordpress-seo")," ",p.removePingbackHeader),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_powered_by_header",id:"input-wpseo-remove_powered_by_header",label:(0,Lt.__)("Remove powered by HTTP header","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove information about the plugins and software used by your site.","wordpress-seo")," ",p.removePoweredByHeader)),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Disable unwanted content formats","wordpress-seo"),description:(0,Lt.__)("WordPress outputs your content in many different formats, across many different URLs (like RSS feeds of your posts and categories). It’s generally good practice to disable the formats you’re not actively using.","wordpress-seo")},(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_global",id:"input-wpseo-remove_feed_global",label:(0,Lt.__)("Remove global feed","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide an overview of your recent posts.","wordpress-seo")," ",p.removeFeedGlobal),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_global_comments",id:"input-wpseo-remove_feed_global_comments",label:(0,Lt.__)("Remove global comment feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide an overview of recent comments on your site.","wordpress-seo")," ",p.removeFeedGlobalComments,(0,Lt.__)("Also disables post comment feeds.","wordpress-seo")),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_post_comments",id:"input-wpseo-remove_feed_post_comments",label:(0,Lt.__)("Remove post comments feeds","wordpress-seo"),disabled:f,checked:f||h,className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about recent comments on each post.","wordpress-seo")," ",p.removeFeedPostComments),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_authors",id:"input-wpseo-remove_feed_authors",label:(0,Lt.__)("Remove post authors feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about recent posts by specific authors.","wordpress-seo")," ",p.removeFeedAuthors),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_post_types",id:"input-wpseo-remove_feed_post_types",label:(0,Lt.__)("Remove post type feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about your recent posts, for each post type.","wordpress-seo")," ",p.removeFeedPostTypes),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_categories",id:"input-wpseo-remove_feed_categories",label:(0,Lt.__)("Remove category feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about your recent posts, for each category.","wordpress-seo")," ",p.removeFeedCategories),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_tags",id:"input-wpseo-remove_feed_tags",label:(0,Lt.__)("Remove tag feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about your recent posts, for each tag.","wordpress-seo")," ",p.removeFeedTags),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_custom_taxonomies",id:"input-wpseo-remove_feed_custom_taxonomies",label:(0,Lt.__)("Remove custom taxonomy feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about your recent posts, for each custom taxonomy.","wordpress-seo")," ",p.removeFeedCustomTaxonomies),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_feed_search",id:"input-wpseo-remove_feed_search",label:(0,Lt.__)("Remove search results feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide information about your search results.","wordpress-seo")," ",p.removeFeedSearch),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_atom_rdf_feeds",id:"input-wpseo-remove_atom_rdf_feeds",label:(0,Lt.__)("Remove Atom / RDF feeds","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Remove URLs which provide alternative (legacy) formats of all of the above.","wordpress-seo")," ",p.removeAtomRdfFeeds)),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Remove unused resources","wordpress-seo"),description:(0,Lt.__)("WordPress loads lots of resources, some of which your site might not need. If you’re not using these, removing them can speed up your pages and save resources.","wordpress-seo")},(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.remove_emoji_scripts",id:"input-wpseo-remove_emoji_scripts",label:(0,Lt.__)("Remove emoji scripts","wordpress-seo"),description:(0,Lt.__)("Remove JavaScript used for converting emoji characters in older browsers.","wordpress-seo"),className:"yst-max-w-2xl"}),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.deny_wp_json_crawling",id:"input-wpseo-deny_wp_json_crawling",label:(0,Lt.__)("Remove WP-JSON API","wordpress-seo"),className:"yst-max-w-2xl"},(0,Lt.__)("Add a ‘disallow’ rule to your robots.txt file to prevent crawling of WordPress' JSON API endpoints.","wordpress-seo")," ",p.denyWpJsonCrawling)),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Block unwanted bots","wordpress-seo"),description:p.blockUnwantedBots},(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.deny_adsbot_crawling",id:"input-wpseo-deny_adsbot_crawling",label:(0,Lt.__)("Prevent Google AdsBot from crawling","wordpress-seo"),description:(0,Lt.__)("Add a ‘disallow’ rule to your robots.txt file to prevent crawling by Google AdsBot. You should only enable this setting if you're not using Google Ads on your site.","wordpress-seo"),className:"yst-max-w-2xl"}),(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!s&&!t,variant:"card",cardLink:c,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...r},(0,e.createElement)(ml,{as:l.ToggleField,type:"checkbox",name:"wpseo.deny_google_extended_crawling",id:"input-wpseo-deny_google_extended_crawling",label:(0,Lt.__)("Prevent Google Gemini and Vertex AI bots from crawling","wordpress-seo"),description:(0,Lt.__)("Add a ‘disallow’ rule to your robots.txt file to prevent crawling by the Google-Extended bot. Enabling this setting won’t prevent Google from indexing your website.","wordpress-seo"),labelSuffix:t&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium"),className:"yst-max-w-2xl",isDummy:!t}),(0,e.createElement)(ml,{as:l.ToggleField,type:"checkbox",name:"wpseo.deny_gptbot_crawling",id:"input-wpseo-deny_gptbot_crawling",label:(0,Lt.__)("Prevent OpenAI GPTBot from crawling","wordpress-seo"),description:(0,Lt.__)("Add a ‘disallow’ rule to your robots.txt file to prevent crawling by OpenAI GPTBot.","wordpress-seo"),labelSuffix:t&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium"),className:"yst-max-w-2xl",isDummy:!t}),(0,e.createElement)(ml,{as:l.ToggleField,type:"checkbox",name:"wpseo.deny_ccbot_crawling",id:"input-wpseo-deny_ccbot_crawling",label:(0,Lt.__)("Prevent Common Crawl CCBot from crawling","wordpress-seo"),description:(0,Lt.__)("Add a ‘disallow’ rule to your robots.txt file to prevent crawling by Common Crawl CCBot.","wordpress-seo"),labelSuffix:t&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium"),className:"yst-max-w-2xl",isDummy:!t}))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Internal site search cleanup","wordpress-seo"),description:(0,Lt.__)("Your internal site search can create lots of confusing URLs for search engines, and can even be used as a way for SEO spammers to attack your site. Most sites will benefit from experimenting with these protections and optimizations, even if you don’t have a search feature in your theme.","wordpress-seo")},(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.search_cleanup",id:"input-wpseo-search_cleanup",label:(0,Lt.__)("Filter search terms","wordpress-seo"),description:(0,Lt.__)("Enables advanced settings for protecting your internal site search URLs.","wordpress-seo"),className:"yst-max-w-2xl"}),(0,e.createElement)(ul,{as:l.TextField,type:"number",name:"wpseo.search_character_limit",id:"input-wpseo-search_character_limit",label:(0,Lt.__)("Max number of characters to allow in searches","wordpress-seo"),description:(0,Lt.__)("Limit the length of internal site search queries to reduce the impact of spam attacks and confusing URLs. Please enter a number between 1 and 50.","wordpress-seo"),disabled:!y}),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.search_cleanup_emoji",id:"input-wpseo-search_cleanup_emoji",label:(0,Lt.__)("Filter searches with emojis and other special characters","wordpress-seo"),description:(0,Lt.__)("Block internal site searches which contain complex and non-alphanumeric characters, as they may be part of a spam attack.","wordpress-seo"),disabled:!y,checked:y&&_,className:"yst-max-w-2xl"}),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.search_cleanup_patterns",id:"input-wpseo-search_cleanup_patterns",label:(0,Lt.__)("Filter searches with common spam patterns","wordpress-seo"),description:(0,Lt.__)("Block internal site searches which match the patterns of known spam attacks.","wordpress-seo"),disabled:!y,checked:y&&w,className:"yst-max-w-2xl"}),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.redirect_search_pretty_urls",id:"input-wpseo-redirect_search_pretty_urls",label:(0,Lt.__)("Redirect pretty URLs to ‘raw’ formats","wordpress-seo"),description:p.redirectSearchPrettyUrls,className:"yst-max-w-2xl"}),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.deny_search_crawling",id:"input-wpseo-deny_search_crawling",label:(0,Lt.__)("Prevent crawling of internal site search URLs","wordpress-seo"),description:p.denySearchCrawling,className:"yst-max-w-2xl"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Advanced: URL cleanup","wordpress-seo"),description:p.advancedUrlCleanup},(0,e.createElement)(l.Alert,{id:"alert-permalink-cleanup-settings",variant:"error"},lr((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,Lt.__)("Warning! These are expert features, so make sure you know what you're doing before using this setting. You might break your site. %1$sRead more about how your site can be affected%2$s.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)("a",{id:"link-permalink-cleanup-info",href:o,target:"_blank",rel:"noopener noreferrer"})})),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.clean_campaign_tracking_urls",id:"input-wpseo-clean_campaign_tracking_urls",label:(0,Lt.__)("Optimize Google Analytics utm tracking parameters","wordpress-seo"),description:p.cleanCampaignTrackingUrls,className:"yst-max-w-2xl"}),(0,e.createElement)(pl,{as:l.ToggleField,type:"checkbox",name:"wpseo.clean_permalinks",id:"input-wpseo-clean_permalinks",label:(0,Lt.__)("Remove unregistered URL parameters","wordpress-seo"),description:p.cleanPermalinks,className:"yst-max-w-2xl"}),(0,e.createElement)(va,{name:"wpseo.clean_permalinks_extra_variables",id:"input-wpseo-clean_permalinks_extra_variables",label:(0,Lt.__)("Additional URL parameters to allow","wordpress-seo"),description:p.cleanPermalinksExtraVariables,disabled:!g})))))},hl=Ia(ga),yl=()=>{const t=(0,Lt.__)("Date archives","wordpress-seo"),s=oa("selectPreference",[],"userLocale"),r=(0,i.useMemo)((()=>bn(t,s)),[t,s]),a=oa("selectUpsellSettingsAsProps"),o=oa("selectReplacementVariablesFor",[],"date_archive","custom-post-type_archive"),n=oa("selectRecommendedReplacementVariablesFor",[],"date_archive","custom-post-type_archive"),c=oa("selectLink",[],"https://yoa.st/show-x"),d=oa("selectPreference",[],"isPremium"),u=oa("selectLink",[],"https://yoa.st/4e0"),p=oa("selectExampleUrl",[],"/2020/"),m=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),f=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to "Date archives".
   * %2$s expands to an example URL, e.g. https://example.com/author/example/.
   * %3$s expands to "date archives".
   */
(0,Lt.__)("%1$s (e.g. %2$s) are based on publication dates. From an SEO perspective, the posts in these archives have no real relation to the other posts except for their publication dates, which doesn’t say much about the content. They could also lead to duplicate content issues. This is why we recommend you to disable %3$s.","wordpress-seo"),t,"<exampleUrl />",r),{exampleUrl:(0,e.createElement)(l.Code,null,p)}))),{values:h}=q(),{opengraph:y}=h.wpseo_social,{"disable-date":_,"noindex-archive-wpseo":w}=h.wpseo_titles;return(0,e.createElement)(po,{title:t,description:f},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)("fieldset",{className:"yst-min-width-0 yst-space-y-8"},(0,e.createElement)(pa,{name:"wpseo_titles.disable-date",id:"input-wpseo_titles-disable-date",label:(0,Lt.sprintf)(
// translators: %1$s expands to "date archives".
(0,Lt.__)("Enable %1$s","wordpress-seo"),r),description:(0,Lt.sprintf)(
// translators: %1$s expands to "Date archives".
(0,Lt.__)("%1$s can cause duplicate content issues. For most sites, we recommend that you disable this setting.","wordpress-seo"),t),className:"yst-max-w-sm"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to "date archives".
(0,Lt.__)("Determine how your %1$s should look in search engines.","wordpress-seo"),r)},(0,e.createElement)(pa,{name:"wpseo_titles.noindex-archive-wpseo",id:"input-wpseo_titles-noindex-archive-wpseo",label:(0,Lt.sprintf)(
// translators: %1$s expands to "date archives".
(0,Lt.__)("Show %1$s in search results","wordpress-seo"),r),description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(
// translators: %1$s expands to "date archives".
(0,Lt.__)("Disabling this means that %1$s will not be indexed by search engines and will be excluded from XML sitemaps. We recommend that you disable this setting.","wordpress-seo"),r)," ",(0,e.createElement)(l.Link,{href:c,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about the search results settings","wordpress-seo")),"."),disabled:_,checked:!_&&!w,className:"yst-max-w-sm"}),(0,e.createElement)(ga,{type:"title",name:"wpseo_titles.title-archive-wpseo",fieldId:"input-wpseo_titles-title-archive-wpseo",label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:o,recommendedReplacementVariables:n,disabled:_}),(0,e.createElement)(ga,{type:"description",name:"wpseo_titles.metadesc-archive-wpseo",fieldId:"input-wpseo_titles-metadesc-archive-wpseo",label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:o,recommendedReplacementVariables:n,disabled:_,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)("div",{className:"yst-flex yst-items-center yst-gap-1.5"},(0,e.createElement)("span",null,(0,Lt.__)("Social media appearance","wordpress-seo")),d&&(0,e.createElement)(l.Badge,{variant:"upsell"},"Premium")),description:(0,Lt.sprintf)(
// translators: %1$s expands to "date archives".
(0,Lt.__)("Determine how your %1$s should look on social media by default.","wordpress-seo"),r)},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!d,variant:"card",cardLink:u,cardText:(0,Lt.sprintf)(
// translators: %1$s expands to Premium.
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...a},(0,e.createElement)(Ha,{isEnabled:!d||y}),(0,e.createElement)(ya,{id:"wpseo_titles-social-image-archive-wpseo",label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:m,mediaUrlName:"wpseo_titles.social-image-url-archive-wpseo",mediaIdName:"wpseo_titles.social-image-id-archive-wpseo",disabled:_||!y,isDummy:!d}),(0,e.createElement)(hl,{type:"title",name:"wpseo_titles.social-title-archive-wpseo",fieldId:"input-wpseo_titles-social-title-archive-wpseo",label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:o,recommendedReplacementVariables:n,disabled:_||!y,isDummy:!d}),(0,e.createElement)(hl,{type:"description",name:"wpseo_titles.social-description-archive-wpseo",fieldId:"input-wpseo_titles-social-description-archive-wpseo",label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:o,recommendedReplacementVariables:n,className:"yst-replacevar--description",disabled:_||!y,isDummy:!d}))))))},_l=Ia(ga),wl=()=>{const{name:t,label:s,singularLabel:r}=oa("selectTaxonomy",[],"post_format"),a=oa("selectPreference",[],"userLocale"),o=(0,i.useMemo)((()=>bn(s,a)),[s,a]),n=(0,i.useMemo)((()=>bn(r,a)),[r,a]),c=oa("selectUpsellSettingsAsProps"),d=oa("selectReplacementVariablesFor",[t],t,"term-in-custom-taxonomy"),u=oa("selectRecommendedReplacementVariablesFor",[t],t,"term-in-custom-taxonomy"),p=oa("selectLink",[],"https://yoa.st/show-x"),m=oa("selectPreference",[],"isPremium"),f=oa("selectLink",[],"https://yoa.st/4e0"),h=oa("selectExampleUrl",[],"/format/example/"),y=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),_=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("(e.g., %1$s)","wordpress-seo"),"<exampleUrl />"),{exampleUrl:(0,e.createElement)(l.Code,null,h)}))),{values:w}=q(),{opengraph:g}=w.wpseo_social,{"disable-post_format":b,"noindex-tax-post_format":v}=w.wpseo_titles;return(0,e.createElement)(po,{title:(0,Lt.__)("Format archives","wordpress-seo"),description:_},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(pa,{name:"wpseo_titles.disable-post_format",id:"input-wpseo_titles-disable-post_format",label:(0,Lt.__)("Enable format-based archives","wordpress-seo"),description:(0,Lt.__)("Format-based archives can cause duplicate content issues. For most sites, we recommend that you disable this setting.","wordpress-seo"),className:"yst-max-w-sm"}),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to "formats". %2$s expands to "Yoast SEO".
(0,Lt.__)("Determine how your %1$s should look in search engines. You can always customize the settings for individual %1$s in the %2$s metabox.","wordpress-seo"),o,"Yoast SEO")},(0,e.createElement)(pa,{name:`wpseo_titles.noindex-tax-${t}`,id:`input-wpseo_titles-noindex-tax-${t}`,label:(0,Lt.sprintf)(
// translators: %1$s expands to "format".
(0,Lt.__)("Show %1$s archives in search results","wordpress-seo"),n),description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(
// translators: %1$s expands to "formats".
(0,Lt.__)("Disabling this means that %1$s will not be indexed by search engines and will be excluded from XML sitemaps. We recommend that you disable this setting.","wordpress-seo"),o)," ",(0,e.createElement)(l.Link,{href:p,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about the search results settings","wordpress-seo")),"."),disabled:b,checked:!b&&!v,className:"yst-max-w-sm"}),(0,e.createElement)(ga,{type:"title",name:`wpseo_titles.title-tax-${t}`,fieldId:`input-wpseo_titles-title-tax-${t}`,label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,disabled:b}),(0,e.createElement)(ga,{type:"description",name:`wpseo_titles.metadesc-tax-${t}`,fieldId:`input-wpseo_titles-metadesc-tax-${t}`,label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,disabled:b,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)("div",{className:"yst-flex yst-items-center yst-gap-1.5"},(0,e.createElement)("span",null,(0,Lt.__)("Social media appearance","wordpress-seo")),m&&(0,e.createElement)(l.Badge,{variant:"upsell"},"Premium")),description:(0,Lt.sprintf)(
// translators: %1$s expands to "formats". %2$s expands to "Yoast SEO".
(0,Lt.__)("Determine how your %1$s should look on social media by default. You can always customize the settings for individual %1$s in the %2$s metabox.","wordpress-seo"),o,"Yoast SEO")},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!m,variant:"card",cardLink:f,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...c},(0,e.createElement)(Ha,{isEnabled:!m||g}),(0,e.createElement)(ya,{id:`wpseo_titles-social-image-tax-${t}`,label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:y,mediaUrlName:`wpseo_titles.social-image-url-tax-${t}`,mediaIdName:`wpseo_titles.social-image-id-tax-${t}`,disabled:b||!g,isDummy:!m}),(0,e.createElement)(_l,{type:"title",name:`wpseo_titles.social-title-tax-${t}`,fieldId:`input-wpseo_titles-social-title-tax-${t}`,label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,disabled:b||!g,isDummy:!m}),(0,e.createElement)(_l,{type:"description",name:`wpseo_titles.social-description-tax-${t}`,fieldId:`input-wpseo_titles-social-description-tax-${t}`,label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:d,recommendedReplacementVariables:u,className:"yst-replacevar--description",disabled:b||!g,isDummy:!m}))))))},gl=()=>{const t=oa("selectReplacementVariablesFor",[],"homepage","page"),s=oa("selectRecommendedReplacementVariablesFor",[],"homepage","page"),r=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),{values:a}=q(),{opengraph:o}=a.wpseo_social;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.__)("Determine how your homepage should look in the search results.","wordpress-seo")},(0,e.createElement)(ga,{type:"title",name:"wpseo_titles.title-home-wpseo",fieldId:"input-wpseo_titles-title-home-wpseo",label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:t,recommendedReplacementVariables:s}),(0,e.createElement)(ga,{type:"description",name:"wpseo_titles.metadesc-home-wpseo",fieldId:"input-wpseo_titles-metadesc-home-wpseo",label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:t,recommendedReplacementVariables:s,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Social media appearance","wordpress-seo"),description:(0,Lt.__)("Determine how your homepage should look on social media.","wordpress-seo")},(0,e.createElement)(Ha,{isEnabled:o
/* translators: %1$s expands to an opening emphasis tag. %2$s expands to a closing emphasis tag. */,text:(0,Lt.__)("The %1$ssocial image%2$s, %1$ssocial title%2$s and %1$ssocial description%2$s require Open Graph data, which is currently disabled in the ‘Social sharing’ section in %3$sSite features%4$s.","wordpress-seo")}),(0,e.createElement)(ya,{id:"wpseo_titles-open_graph_frontpage_image",label:(0,Lt.__)("Social image","wordpress-seo"),previewLabel:r,mediaUrlName:"wpseo_titles.open_graph_frontpage_image",mediaIdName:"wpseo_titles.open_graph_frontpage_image_id",disabled:!o}),(0,e.createElement)(ga,{type:"title",name:"wpseo_titles.open_graph_frontpage_title",fieldId:"input-wpseo_titles-open_graph_frontpage_title",label:(0,Lt.__)("Social title","wordpress-seo"),replacementVariables:t,recommendedReplacementVariables:s,disabled:!o}),(0,e.createElement)(ga,{type:"description",name:"wpseo_titles.open_graph_frontpage_desc",fieldId:"input-wpseo_titles-open_graph_frontpage_desc",label:(0,Lt.__)("Social description","wordpress-seo"),replacementVariables:t,recommendedReplacementVariables:s,className:"yst-replacevar--description",disabled:!o})))},bl=()=>{const t=oa("selectPreference",[],"homepagePageEditUrl"),s=oa("selectPreference",[],"homepagePostsEditUrl"),r=(0,i.useMemo)((()=>el((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("You can determine the title and description for the homepage by %1$sediting the homepage itself%2$s.","wordpress-seo"),"<a>","</a>"),t,"link-homepage-page-edit"))),a=(0,i.useMemo)((()=>el((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("You can determine the title and description for the blog page by %1$sediting the blog page itself%2$s.","wordpress-seo"),"<a>","</a>"),s,"link-homepage-posts-page-edit")));return(0,e.createElement)("div",{className:"yst-max-w-screen-sm"},(0,e.createElement)(l.Alert,null,(0,e.createElement)("p",null,r),s&&(0,e.createElement)("p",{className:"yst-pt-2"},a)))},vl=()=>{const t=oa("selectPreference",[],"homepageIsLatestPosts");return(0,e.createElement)(po,{title:(0,Lt.__)("Homepage","wordpress-seo"),description:(0,Lt.__)("Determine how your homepage should look in the search results and on social media. This is what people probably will see when they search for your brand name.","wordpress-seo")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},t&&(0,e.createElement)(gl,null),!t&&(0,e.createElement)(bl,null))))},El=()=>{const{name:t,label:s,hasSchemaArticleType:r}=oa("selectPostType",[],"attachment"),a=oa("selectPreference",[],"userLocale"),o=(0,i.useMemo)((()=>bn(s,a)),[s,a]),n=oa("selectReplacementVariablesFor",[t],t,"custom_post_type"),c=oa("selectRecommendedReplacementVariablesFor",[t],t,"custom_post_type"),d=oa("selectArticleTypeValuesFor",[t],t),u=oa("selectPageTypeValuesFor",[t],t),p=oa("selectLink",[],"https://yoa.st/media-pages-thin-content"),m=oa("selectLink",[],"https://yoa.st/show-x"),{values:f}=q(),{"disable-attachment":h,"noindex-attachment":y}=f.wpseo_titles,_=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s and %2$s are replaced by opening and closing <a> tags.
   * %3$s expands to "media".
   * %4$s expand to "Yoast SEO".
   * %5$s expand to "WordPress".
   */
(0,Lt.__)("When you upload media (e.g. an image or video), %5$s automatically creates a %3$s page (attachment URL) for it. These pages are quite empty and could cause %1$sthin content problems and lead to excess pages on your site%2$s. Therefore, %4$s disables them by default (and redirects the attachment URL to the media itself).","wordpress-seo"),"<a>","</a>",o,"Yoast SEO","WordPress"),{a:(0,e.createElement)("a",{href:p,target:"_blank",rel:"noopener noreferrer"})})));return(0,e.createElement)(po,{title:(0,Lt.sprintf)(
// translators: %1$s expands to "Media".
(0,Lt.__)("%1$s pages","wordpress-seo"),s),description:_},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)("fieldset",{className:"yst-min-width-0 yst-space-y-8"},(0,e.createElement)(pa,{name:`wpseo_titles.disable-${t}`,id:`input-wpseo_titles-disable-${t}`,label:(0,Lt.sprintf)(
// translators: %1$s expands to "media".
(0,Lt.__)("Enable %1$s pages","wordpress-seo"),o),description:(0,Lt.sprintf)(
// translators: %1$s expands to "media".
(0,Lt.__)("We recommend keeping %1$s pages disabled. This will cause all attachment URLs to be redirected to the media itself.","wordpress-seo"),o),className:"yst-max-w-sm"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Search appearance","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to "media". %3$s expands to "Yoast SEO".
(0,Lt.__)("Determine how your %1$s pages should look in search engines. You can always customize the settings for individual %1$s pages in the %2$s metabox.","wordpress-seo"),o,"Yoast SEO")},(0,e.createElement)(pa,{name:`wpseo_titles.noindex-${t}`,id:`input-wpseo_titles-noindex-${t}`,label:(0,Lt.sprintf)(
// translators: %1$s expands to "media".
(0,Lt.__)("Show %1$s pages in search results","wordpress-seo"),o),description:(0,e.createElement)(e.Fragment,null,(0,Lt.sprintf)(
// translators: %1$s expands to "media".
(0,Lt.__)("Disabling this means that %1$s pages created by WordPress will not be indexed by search engines and will be excluded from XML sitemaps.","wordpress-seo"),o),(0,e.createElement)("br",null),(0,e.createElement)(l.Link,{href:m,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about the search results settings","wordpress-seo")),"."),disabled:h,checked:!h&&!y,className:"yst-max-w-sm"}),(0,e.createElement)(ga,{type:"title",name:`wpseo_titles.title-${t}`,fieldId:`input-wpseo_titles-title-${t}`,label:(0,Lt.__)("SEO title","wordpress-seo"),replacementVariables:n,recommendedReplacementVariables:c,disabled:h}),(0,e.createElement)(ga,{type:"description",name:`wpseo_titles.metadesc-${t}`,fieldId:`input-wpseo_titles-metadesc-${t}`,label:(0,Lt.__)("Meta description","wordpress-seo"),replacementVariables:n,recommendedReplacementVariables:c,disabled:h,className:"yst-replacevar--description"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Schema","wordpress-seo"),description:(0,Lt.sprintf)(
// translators: %1$s expands to "media". %3$s expands to "Yoast SEO".
(0,Lt.__)("Determine how your %1$s pages should be described by default in your site's Schema.org markup. You can always customize the settings for individual %1$s pages in the %2$s metabox.","wordpress-seo"),o,"Yoast SEO")},(0,e.createElement)(Na,{as:l.SelectField,type:"select",name:`wpseo_titles.schema-page-type-${t}`,id:`input-wpseo_titles-schema-page-type-${t}`,label:(0,Lt.__)("Page type","wordpress-seo"),options:u,disabled:h,className:"yst-max-w-sm"}),r&&(0,e.createElement)("div",null,(0,e.createElement)(Na,{as:l.SelectField,type:"select",name:`wpseo_titles.schema-article-type-${t}`,id:`input-wpseo_titles-schema-article-type-${t}`,label:(0,Lt.__)("Article type","wordpress-seo"),options:d,disabled:h,className:"yst-max-w-sm"}),(0,e.createElement)(qa,{name:t,disabled:h}))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Additional settings","wordpress-seo")},(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:`wpseo_titles.display-metabox-pt-${t}`,id:`input-wpseo_titles-display-metabox-pt-${t}`,label:(0,Lt.__)("Enable SEO controls and assessments","wordpress-seo"),description:(0,Lt.__)("Show or hide our tools and controls in the attachment editor.","wordpress-seo"),disabled:h,className:"yst-max-w-sm"})))))},xl=({children:t})=>(0,e.createElement)(l.Table.Cell,{className:"yst-font-medium"},(0,e.createElement)("span",{className:"yst-text-slate-900"},t));xl.propTypes={children:ir().node.isRequired};const kl=()=>(0,e.createElement)(po,{title:(0,Lt.__)("RSS","wordpress-seo")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("RSS feed","wordpress-seo"),description:(0,Lt.__)("Automatically add content to your RSS. This enables you to add links back to your blog and your blog posts, helping search engines identify you as the original source of the content.","wordpress-seo")},(0,e.createElement)(te,{as:l.TextareaField,type:"textarea",rows:4,name:"wpseo_titles.rssbefore",id:"input-wpseo_titles-rssbefore",label:(0,Lt.__)("Content to put before each post in the feed","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextareaField,type:"textarea",rows:4,name:"wpseo_titles.rssafter",id:"input-wpseo_titles-rssafter",label:(0,Lt.__)("Content to put after each post in the feed","wordpress-seo")})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{as:"section",title:(0,Lt.__)("Available variables","wordpress-seo"),description:(0,Lt.__)("You can use the following variables within the content, they will be replaced by the value on the right.","wordpress-seo")},(0,e.createElement)(l.Table,null,(0,e.createElement)(l.Table.Head,null,(0,e.createElement)(l.Table.Row,null,(0,e.createElement)(l.Table.Header,{scope:"col"},(0,Lt.__)("Variable","wordpress-seo")),(0,e.createElement)(l.Table.Header,{scope:"col"},(0,Lt.__)("Description","wordpress-seo")))),(0,e.createElement)(l.Table.Body,null,(0,e.createElement)(l.Table.Row,null,(0,e.createElement)(xl,null,"%%AUTHORLINK%%"),(0,e.createElement)(l.Table.Cell,null,(0,Lt.__)("A link to the archive for the post author, with the author's name as anchor text.","wordpress-seo"))),(0,e.createElement)(l.Table.Row,null,(0,e.createElement)(xl,null,"%%POSTLINK%%"),(0,e.createElement)(l.Table.Cell,null,(0,Lt.__)("A link to the post, with the title as anchor text.","wordpress-seo"))),(0,e.createElement)(l.Table.Row,null,(0,e.createElement)(xl,null,"%%BLOGLINK%%"),(0,e.createElement)(l.Table.Cell,null,(0,Lt.__)("A link to your site, with your site's name as anchor text.","wordpress-seo"))),(0,e.createElement)(l.Table.Row,null,(0,e.createElement)(xl,null,"%%BLOGDESCLINK%%"),(0,e.createElement)(l.Table.Cell,null,(0,Lt.__)("A link to your site, with your site's name and description as anchor text.","wordpress-seo"))))))))),Sl=Aa(l.ToggleField),Ll=Ma(Pa),Tl=()=>{const t=(0,i.useMemo)((()=>(0,le.get)(window,"wpseoScriptData.separators",{})),[]),s=oa("selectPreference",[],"generalSettingsUrl"),r=oa("selectPreference",[],"canManageOptions",!1),a=oa("selectPreference",[],"showForceRewriteTitlesSetting",!1),o=oa("selectLink",[],"https://yoa.st/site-basics-replacement-variables"),n=oa("selectLink",[],"https://yoa.st/usage-tracking-2"),c=oa("selectLink",[],"https://yoa.st/site-policies-learn-more"),d=oa("selectPreference",[],"siteTitle",""),u=oa("selectLink",[],"https://yoa.st/site-policies-upsell"),p=oa("selectPreference",[],"isPremium"),m=oa("selectUpsellSettingsAsProps"),{fetchPages:f}=sa(),h=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("Usage tracking allows us to track some data about your site to improve our plugin. %1$sLearn more about which data we track and why%2$s.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)("a",{id:"link-usage-tracking",href:n,target:"_blank",rel:"noopener"})})),[]),y=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %1$s expands to an opening tag. %2$s expands to a closing tag. */
(0,Lt.__)("Select the pages on your website which contain information about your organizational and publishing policies. Some of these might not apply to your site, and you can select the same page for multiple policies. %1$sLearn more about why setting your site policies is important%2$s.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)("a",{id:"link-site-policies",href:c,target:"_blank",rel:"noopener"})})),[c]),_=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %1$s and %2$s expand to an opening and closing emphasis tag. %3$s and %4$s expand to an opening and closing anchor tag. */
(0,Lt.__)("Set the basic info for your website. You can use %1$stagline%2$s and %1$sseparator%2$s as %3$sreplacement variables%4$s when configuring the search appearance of your content.","wordpress-seo"),"<em>","</em>","<a>","</a>"),{em:(0,e.createElement)("em",null),a:(0,e.createElement)("a",{id:"site-basics-replacement-variables",href:o,target:"_blank",rel:"noopener"})})),[]),w=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(/* translators: %1$s expands to an opening emphasis tag. %2$s expands to a closing emphasis tag. */
(0,Lt.__)("We're sorry, you're not allowed to edit the %1$swebsite name%2$s and %1$stagline%2$s.","wordpress-seo"),"<em>","</em>"),{em:(0,e.createElement)("em",null)})),[]),g=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening strong tag.
   * %2$s expands to a closing strong tag.
   * %3$s expands to the recommended image size.
   */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","1200x675px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})})),[]),b=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening anchor tag.
   * %2$s expands to a closing anchor tag.
   */
(0,Lt.__)("This field updates the %1$stagline in your WordPress settings%2$s.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)("a",{href:`${s}#blogdescription`,target:"_blank",rel:"noopener noreferrer"})})),[]),v=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which describes the editorial principles of your organization. %1$sWhat%2$s do you write about, %1$swho%2$s do you write for, and %1$swhy%2$s?","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),E=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which describes the ownership structure of your organization. It should include information about %1$sfunding%2$s and %1$sgrants%2$s.","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),x=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which describes how your organization collects and responds to %1$sfeedback%2$s, engages with the %1$spublic%2$s, and prioritizes %1$stransparency%2$s.","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),k=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which outlines your procedure for addressing %1$serrors%2$s (e.g., publishing retractions or corrections).","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),S=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which describes the personal, organizational, and corporate %1$sstandards%2$s of %1$sbehavior%2$s expected by your organization.","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),L=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which provides information on your diversity policies for %1$seditorial%2$s content.","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),T=(0,i.useMemo)((()=>lr((0,Lt.sprintf)(
/**
   * translators: %1$s expands to an opening italics tag.
   * %2$s expands to a closing italics tag.
   */
(0,Lt.__)("Select a page which provides information about your diversity policies for %1$sstaffing%2$s, %1$shiring%2$s and %1$semployment%2$s.","wordpress-seo"),"<i>","</i>"),{i:(0,e.createElement)("i",null)})),[]),{values:F}=q(),{opengraph:$}=F.wpseo_social;return(0,i.useEffect)((()=>{f()}),[f]),(0,e.createElement)(po,{title:(0,Lt.__)("Site basics","wordpress-seo"),description:(0,Lt.__)("Configure the basics for your website.","wordpress-seo")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Site info","wordpress-seo"),description:_},!r&&(0,e.createElement)(l.Alert,{variant:"warning",id:"alert-site-defaults-variables",className:"yst-mb-8"},w),(0,e.createElement)("div",{className:"lg:yst-mt-0 lg:yst-col-span-2 yst-space-y-8"},(0,e.createElement)(Ba,{as:l.TextField,name:"wpseo_titles.website_name",id:"input-wpseo_titles-website_name",label:(0,Lt.__)("Website name","wordpress-seo"),placeholder:d}),(0,e.createElement)(Ba,{as:l.TextField,name:"wpseo_titles.alternate_website_name",id:"input-wpseo_titles-alternate_website_name",label:(0,Lt.__)("Alternate website name","wordpress-seo"),description:(0,Lt.__)("Use the alternate website name for acronyms, or a shorter version of your website's name.","wordpress-seo")}),(0,e.createElement)(te,{as:l.TextField,type:"text",name:"blogdescription",id:"input-blogdescription",label:(0,Lt.__)("Tagline","wordpress-seo"),description:r&&b,readOnly:!r})),(0,e.createElement)(l.RadioGroup,{label:(0,Lt.__)("Title separator","wordpress-seo"),variant:"inline-block"},(0,le.map)(t,(({label:t,aria_label:s},r)=>(0,e.createElement)(te,{key:r,as:l.Radio,type:"radio",variant:"inline-block",name:"wpseo_titles.separator",id:`input-wpseo_titles-separator-${r}`,label:t,isLabelDangerousHtml:!0,"aria-label":s,value:r})))),(0,e.createElement)(Ha,{isEnabled:$,text:/* translators: %1$s expands to an opening emphasis tag. %2$s expands to a closing emphasis tag. */
(0,Lt.__)("The %1$sSite image%2$s requires Open Graph data, which is currently disabled in the ‘Social sharing’ section in %3$sSite features%4$s.","wordpress-seo")}),(0,e.createElement)(ya,{id:"wpseo_social-og_default_image",label:(0,Lt.__)("Site image","wordpress-seo"),description:(0,Lt.__)("This image is used as a fallback for posts/pages that don't have any images set.","wordpress-seo"),previewLabel:g,mediaUrlName:"wpseo_social.og_default_image",mediaIdName:"wpseo_social.og_default_image_id",disabled:!$})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("Site preferences","wordpress-seo")},a&&(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:"wpseo_titles.forcerewritetitle",id:"input-wpseo_titles-forcerewritetitle",label:(0,Lt.__)("Force rewrite titles","wordpress-seo"),description:(0,Lt.sprintf)(/* translators: %1$s expands to "Yoast SEO" */
(0,Lt.__)("%1$s has auto-detected whether it needs to force rewrite the titles for your pages, if you think it's wrong and you know what you're doing, you can change the setting here.","wordpress-seo"),"Yoast SEO"),className:"yst-max-w-sm"}),(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:"wpseo.disableadvanced_meta",id:"input-wpseo-disableadvanced_meta",label:(0,Lt.__)("Restrict advanced settings for authors","wordpress-seo"),description:(0,Lt.sprintf)(/* translators: %1$s expands to "Yoast SEO" */
(0,Lt.__)("By default only editors and administrators can access the Advanced and Schema section of the %1$s sidebar or metabox. Disabling this allows access to all users.","wordpress-seo"),"Yoast SEO"),className:"yst-max-w-sm"}),(0,e.createElement)(Na,{as:Sl,type:"checkbox",name:"wpseo.tracking",id:"input-wpseo-tracking",label:(0,Lt.__)("Usage tracking","wordpress-seo"),description:h,className:"yst-max-w-sm"})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)(e.Fragment,null,(0,Lt.__)("Site policies","wordpress-seo"),p&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium")),description:y},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!p,variant:"card",cardLink:u,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...m},(0,e.createElement)(Ll,{name:"wpseo_titles.publishing_principles_id",id:"input-wpseo_titles-publishing_principles_id",label:(0,Lt.__)("Publishing principles","wordpress-seo"),className:"yst-max-w-sm",description:v,isDummy:!p}),(0,e.createElement)(Ll,{name:"wpseo_titles.ownership_funding_info_id",id:"input-wpseo_titles-ownership_funding_info_id",label:(0,Lt.__)("Ownership / Funding info","wordpress-seo"),className:"yst-max-w-sm",description:E,isDummy:!p}),(0,e.createElement)(Ll,{name:"wpseo_titles.actionable_feedback_policy_id",id:"input-wpseo_titles-actionable_feedback_policy_id",label:(0,Lt.__)("Actionable feedback policy","wordpress-seo"),className:"yst-max-w-sm",description:x,isDummy:!p}),(0,e.createElement)(Ll,{name:"wpseo_titles.corrections_policy_id",id:"input-wpseo_titles-corrections_policy_id",label:(0,Lt.__)("Corrections policy","wordpress-seo"),className:"yst-max-w-sm",description:k,isDummy:!p}),(0,e.createElement)(Ll,{name:"wpseo_titles.ethics_policy_id",id:"input-wpseo_titles-ethics_policy_id",label:(0,Lt.__)("Ethics policy","wordpress-seo"),className:"yst-max-w-sm",description:S,isDummy:!p}),(0,e.createElement)(Ll,{name:"wpseo_titles.diversity_policy_id",id:"input-wpseo_titles-diversity_policy_id",label:(0,Lt.__)("Diversity policy","wordpress-seo"),className:"yst-max-w-sm",description:L,isDummy:!p}),(0,e.createElement)(Ll,{name:"wpseo_titles.diversity_staffing_report_id",id:"input-wpseo_titles-diversity_staffing_report_id",label:(0,Lt.__)("Diversity staffing report","wordpress-seo"),className:"yst-max-w-sm",description:T,isDummy:!p}))))))},Fl=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"}))})),$l=({name:t,cardId:s,inputId:r,children:a,imageSrc:o,imageAlt:n,isPremiumFeature:c=!1,isPremiumLink:d="",isBetaFeature:u=!1,isNewFeature:p=!1,hasPremiumBadge:m=!1,title:f})=>{const h=oa("selectPreference",[],"isPremium"),y=oa("selectPluginUrl",[o],o),{isDisabled:_,message:w,disabledSetting:g}=ea({name:t}),{values:b}=q(),v=oa("selectLink",[d],d),E=oa("selectUpsellSettingsAsProps"),x=(0,l.useSvgAria)(),k=(0,i.useMemo)((()=>(0,le.get)(b,t,!1)),[b,t]),S=(0,i.useMemo)((()=>!h&&c),[h,c]),L=(0,i.useMemo)((()=>_||!S&&!k),[_,S,k]),T=(0,i.useMemo)((()=>_||h&&c&&m||u||p&&!h),[_,h,c,u,p]);return(0,e.createElement)(l.Card,{id:s},(0,e.createElement)(l.Card.Header,{className:"yst-h-auto yst-p-0"},(0,e.createElement)("img",{className:or()("yst-w-full yst-transition yst-duration-200",L&&"yst-opacity-50 yst-filter yst-grayscale"),src:y,alt:null!=n?n:"",width:500,height:250,loading:"lazy",decoding:"async"}),T&&(0,e.createElement)("div",{className:"yst-absolute yst-top-2 yst-end-2 yst-flex yst-gap-1.5"},_&&(0,e.createElement)(l.Badge,{size:"small",variant:"plain"},w),h&&c&&m&&(0,e.createElement)(l.Badge,{size:"small",variant:"upsell"},"Premium"),u&&(0,e.createElement)(l.Badge,{size:"small",variant:"info"},"Beta"),p&&!h&&(0,e.createElement)(l.Badge,{size:"small",variant:"info"},"New"))),(0,e.createElement)(l.Card.Content,{className:"yst-flex yst-flex-col yst-gap-3"},(0,e.createElement)(l.Title,{as:"h3"},f),a),(0,e.createElement)(l.Card.Footer,null,!S&&(0,e.createElement)(Na,{as:l.ToggleField,type:"checkbox",name:t,id:r,"aria-label":`${(0,Lt.__)("Enable feature","wordpress-seo")} ${f}`,label:(0,Lt.__)("Enable feature","wordpress-seo"),disabled:_,checked:"language"!==g&&k}),S&&(0,e.createElement)(l.Button,{as:"a",className:"yst-gap-2 yst-w-full yst-px-2",variant:"upsell",href:v,target:"_blank",rel:"noopener",...E},(0,e.createElement)(dr,{className:"yst-w-5 yst-h-5 yst--ms-1 yst-shrink-0",...x}),(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"))))};$l.propTypes={name:ir().string.isRequired,cardId:ir().string.isRequired,inputId:ir().string.isRequired,children:ir().node.isRequired,imageSrc:ir().string.isRequired,imageAlt:ir().string,isPremiumFeature:ir().bool,isBetaFeature:ir().bool,isNewFeature:ir().bool,isPremiumLink:ir().string,hasPremiumBadge:ir().bool,title:ir().string.isRequired};const Pl=({id:t,link:s,ariaLabel:r,...a})=>{const o=oa("selectLink",[s],s);return(0,e.createElement)(l.Link,{id:t,href:o,variant:"primary",className:"yst-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",target:"_blank",rel:"noopener","aria-label":(0,Lt.sprintf)(/* translators: Hidden accessibility text; %s expands to a translated string of this feature, e.g. "SEO analysis". */
(0,Lt.__)("Learn more about %s (Opens in a new browser tab)","wordpress-seo"),r),...a},(0,Lt.__)("Learn more","wordpress-seo"),(0,e.createElement)(kr,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))};Pl.propTypes={id:ir().string.isRequired,link:ir().string.isRequired,ariaLabel:ir().string.isRequired};const Rl=()=>{const t=oa("selectPreference",[],"isPremium"),s=oa("selectPreference",[],"sitemapUrl"),r=oa("selectPreference",[],"llmsTxtUrl"),{values:a,initialValues:o}=q(),{enable_xml_sitemap:n,enable_llms_txt:c}=a.wpseo,{enable_xml_sitemap:d,enable_llms_txt:u}=o.wpseo,p=(0,i.useMemo)((()=>t?"yst-grid yst-gap-6 yst-grid-cols-1 sm:yst-grid-cols-2 min-[783px]:yst-grid-cols-1 lg:yst-grid-cols-2 xl:yst-grid-cols-3 2xl:yst-grid-cols-4":"yst-grid yst-gap-6 yst-grid-cols-1 sm:yst-grid-cols-2 min-[783px]:yst-grid-cols-1 lg:yst-grid-cols-2 2xl:yst-grid-cols-3 min-[1800px]:yst-grid-cols-4"),[t]);return(0,e.createElement)(po,{title:(0,Lt.__)("Site features","wordpress-seo"),description:(0,Lt.__)("Tell us which features you want to use.","wordpress-seo")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-6xl"},(0,e.createElement)("fieldset",{className:"yst-min-w-0"},(0,e.createElement)("legend",{className:"yst-sr-only"},(0,Lt.__)("Writing","wordpress-seo")),(0,e.createElement)("div",{className:"yst-max-w-screen-sm yst-mb-8"},(0,e.createElement)(l.Title,{as:"h2",size:"2"},(0,Lt.__)("Writing","wordpress-seo"))),(0,e.createElement)("div",{className:p},(0,e.createElement)($l,{name:"wpseo.keyword_analysis_active",cardId:"card-wpseo-keyword_analysis_active",inputId:"input-wpseo-keyword_analysis_active",imageSrc:"/images/seo_analysis.png",title:(0,Lt.__)("SEO analysis","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("The SEO analysis offers suggestions to improve the findability of your text and makes sure that your content meets best practices.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-seo-analysis",link:"https://yoa.st/2ak",ariaLabel:(0,Lt.__)("SEO analysis","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.content_analysis_active",cardId:"card-wpseo-content_analysis_active",inputId:"input-wpseo-content_analysis_active",imageSrc:"/images/readability_analysis.png",title:(0,Lt.__)("Readability analysis","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("The readability analysis offers suggestions to improve the structure and style of your text.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-readability-analysis",link:"https://yoa.st/2ao",ariaLabel:(0,Lt.__)("Readability analysis","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.inclusive_language_analysis_active",cardId:"card-wpseo-inclusive_language_analysis_active",inputId:"input-wpseo-inclusive_language_analysis_active",imageSrc:"/images/inclusive_language_analysis.png",title:(0,Lt.__)("Inclusive language analysis","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("The inclusive language analysis offers suggestions to write more inclusive copy, so more people will be able to relate to your content.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-inclusive-language-analysis",link:"https://yoa.st/inclusive-language-feature-learn-more",ariaLabel:(0,Lt.__)("Inclusive language analysis","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_ai_generator",cardId:"card-wpseo-enable_ai_generator",inputId:"input-wpseo-enable_ai_generator",imageSrc:"/images/ai-generator.png",isPremiumFeature:!0,hasPremiumBadge:!1,isBetaFeature:!0,isPremiumLink:"https://yoa.st/get-ai-generator",title:"Yoast AI"},(0,e.createElement)("p",null,(0,Lt.__)("The AI features help you create better content by providing optimization suggestions that you can apply as you wish.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-ai-generator",link:"https://yoa.st/ai-generator-feature",ariaLabel:(0,Lt.__)("AI title & description generator","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_metabox_insights",cardId:"card-wpseo-enable_metabox_insights",inputId:"input-wpseo-enable_metabox_insights",imageSrc:"/images/insights.png",title:(0,Lt.__)("Insights","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Get more insights into what you are writing. What words do you use most often? How much time does it take to read your text? Is your text easy to read?","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-insights",link:"https://yoa.st/4ew",ariaLabel:(0,Lt.__)("Insights","wordpress-seo")})))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)("fieldset",{className:"yst-min-w-0"},(0,e.createElement)("legend",{className:"yst-sr-only"},(0,Lt.__)("Site structure","wordpress-seo")),(0,e.createElement)("div",{className:"yst-max-w-screen-sm yst-mb-8"},(0,e.createElement)(l.Title,{as:"h2",size:"2"},(0,Lt.__)("Site structure","wordpress-seo"))),(0,e.createElement)("div",{className:p},(0,e.createElement)($l,{name:"wpseo.enable_cornerstone_content",cardId:"card-wpseo-enable_cornerstone_content",inputId:"input-wpseo-enable_cornerstone_content",imageSrc:"/images/cornerstone_content.png",title:(0,Lt.__)("Cornerstone content","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Mark and filter your cornerstone content to make sure your most important articles get the attention they deserve. To help you write excellent copy, we’ll assess your text more strictly.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-cornerstone-content",link:"https://yoa.st/dashboard-help-cornerstone",ariaLabel:(0,Lt.__)("Cornerstone content","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_text_link_counter",cardId:"card-wpseo-enable_text_link_counter",inputId:"input-wpseo-enable_text_link_counter",imageSrc:"/images/text_link_counter.png",title:(0,Lt.__)("Text link counter","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Count the number of internal links from and to your posts to improve your site structure.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-text-link-counter",link:"https://yoa.st/2aj",ariaLabel:(0,Lt.__)("Text link counter","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_link_suggestions",cardId:"card-wpseo-enable_link_suggestions",inputId:"input-wpseo-enable_link_suggestions",imageSrc:"/images/link_suggestions.png",isPremiumFeature:!0,hasPremiumBadge:!0,isPremiumLink:"https://yoa.st/get-link-suggestions",title:(0,Lt.__)("Internal linking suggestions","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("No need to figure out what to link to. You get linking suggestions for relevant posts and pages to make your website easier to navigate.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-suggestions-link",link:t?"https://yoa.st/17g":"https://yoa.st/4ev",ariaLabel:(0,Lt.__)("Link suggestions","wordpress-seo")})))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)("fieldset",{id:"section-social-sharing",className:"yst-min-w-0"},(0,e.createElement)("legend",{className:"yst-sr-only"},(0,Lt.__)("Social sharing","wordpress-seo")),(0,e.createElement)("div",{className:"yst-max-w-screen-sm yst-mb-8"},(0,e.createElement)(l.Title,{as:"h2",size:"2",className:"yst-mb-2"},(0,Lt.__)("Social sharing","wordpress-seo"))),(0,e.createElement)("div",{className:p},(0,e.createElement)($l,{name:"wpseo_social.opengraph",cardId:"card-wpseo_social-opengraph",inputId:"input-wpseo_social-opengraph",imageSrc:"/images/open_graph.png",title:(0,Lt.__)("Open Graph data","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Allows for Facebook and other social media to display a preview with images and a text excerpt when a link to your site is shared. Keep this feature enabled to optimize your site for social media.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-open-graph-data",link:"https://yoa.st/site-features-open-graph-data",ariaLabel:(0,Lt.__)("Open Graph data","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo_social.twitter",cardId:"card-wpseo_social-twitter",inputId:"input-wpseo_social-twitter",imageSrc:"/images/twitter_card.png",title:(0,Lt.__)("X card data","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Allows for X to display a preview with images and a text excerpt when a link to your site is shared.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-twitter-card-data",link:"https://yoa.st/site-features-twitter-card-data",ariaLabel:(0,Lt.__)("X card data","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_enhanced_slack_sharing",cardId:"card-wpseo-enable_enhanced_slack_sharing",inputId:"input-wpseo-enable_enhanced_slack_sharing",imageSrc:"/images/slack_sharing.png",title:(0,Lt.__)("Slack sharing","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("This adds an author byline and reading time estimate to the article’s snippet when shared on Slack.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-slack-sharing",link:"https://yoa.st/help-slack-share",ariaLabel:(0,Lt.__)("Slack sharing","wordpress-seo")})))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)("fieldset",{className:"yst-min-w-0"},(0,e.createElement)("legend",{className:"yst-sr-only"},(0,Lt.__)("Tools","wordpress-seo")),(0,e.createElement)("div",{className:"yst-max-w-screen-sm yst-mb-8"},(0,e.createElement)(l.Title,{as:"h2",size:"2"},(0,Lt.__)("Tools","wordpress-seo"))),(0,e.createElement)("div",{className:p},(0,e.createElement)($l,{name:"wpseo.enable_admin_bar_menu",cardId:"card-wpseo-enable_admin_bar_menu",inputId:"input-wpseo-enable_admin_bar_menu",imageSrc:"/images/admin_bar.png",title:(0,Lt.__)("Admin bar menu","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.sprintf)(
// translators: %1$s expands to Yoast.
(0,Lt.__)("The %1$s icon in the top admin bar provides quick access to third-party tools for analyzing pages and makes it easy to see if you have new notifications.","wordpress-seo"),"Yoast")),(0,e.createElement)(Pl,{id:"link-admin-bar",link:"https://yoa.st/site-features-admin-bar",ariaLabel:(0,Lt.__)("Admin bar menu","wordpress-seo")})))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)("fieldset",{className:"yst-min-w-0"},(0,e.createElement)("legend",{className:"yst-sr-only"},(0,Lt.__)("APIs","wordpress-seo")),(0,e.createElement)("div",{className:"yst-max-w-screen-sm yst-mb-8"},(0,e.createElement)(l.Title,{as:"h2",size:"2"},(0,Lt.__)("APIs","wordpress-seo"))),(0,e.createElement)("div",{className:p},(0,e.createElement)($l,{name:"wpseo.enable_headless_rest_endpoints",cardId:"card-wpseo-enable_headless_rest_endpoints",inputId:"input-wpseo-enable_headless_rest_endpoints",imageSrc:"/images/rest_api.png",title:(0,Lt.__)("REST API endpoint","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("This Yoast SEO REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use Yoast SEO for all their SEO meta output.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-rest-api-endpoint",link:"https://yoa.st/site-features-rest-api-endpoint",ariaLabel:(0,Lt.__)("REST API endpoint","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_xml_sitemap",cardId:"card-wpseo-enable_xml_sitemap",inputId:"input-wpseo-enable_xml_sitemap",imageSrc:"/images/xml_sitemaps.png",title:(0,Lt.__)("XML sitemaps","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.sprintf)(
// translators: %1$s expands to "Yoast SEO".
(0,Lt.__)("Enable the %1$s XML sitemaps. A sitemap is a file that lists a website's essential pages to make sure search engines can find and crawl them.","wordpress-seo"),"Yoast SEO")),d&&n&&(0,e.createElement)(l.Button,{as:"a",id:"link-xml-sitemaps",href:s,variant:"secondary",target:"_blank",rel:"noopener",className:"yst-self-start"},(0,Lt.__)("View the XML sitemap","wordpress-seo"),(0,e.createElement)(Fl,{className:"yst--me-1 yst-ms-1 yst-h-5 yst-w-5 yst-text-slate-400 rtl:yst-rotate-[270deg]"})),(0,e.createElement)(Pl,{id:"link-xml-sitemaps-learn-more",link:"https://yoa.st/2a-",ariaLabel:(0,Lt.__)("XML sitemaps","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_index_now",cardId:"card-wpseo-enable_index_now",inputId:"input-wpseo-enable_index_now",imageSrc:"/images/indexnow.png",isPremiumFeature:!0,hasPremiumBadge:!0,isPremiumLink:"https://yoa.st/get-indexnow",title:(0,Lt.__)("IndexNow","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Automatically ping search engines like Bing and Yandex whenever you publish, update or delete a post.","wordpress-seo")),(0,e.createElement)(Pl,{id:"link-index-now",link:"https://yoa.st/index-now-feature",ariaLabel:(0,Lt.__)("IndexNow","wordpress-seo")})),(0,e.createElement)($l,{name:"wpseo.enable_llms_txt",cardId:"card-wpseo-enable_llms_txt",inputId:"input-wpseo-enable_llms_txt",imageSrc:"/images/llms.png",title:(0,Lt.__)("llms.txt","wordpress-seo")},(0,e.createElement)("p",null,(0,Lt.__)("Generate a file that points to your website's most relevant content. Designed to help AI Assistants understand your website better.","wordpress-seo")),u&&c&&(0,e.createElement)(l.Button,{as:"a",id:"link-llms",href:r,variant:"secondary",target:"_blank",rel:"noopener",className:"yst-self-start"},(0,Lt.__)("View the llms.txt file","wordpress-seo"),(0,e.createElement)(Fl,{className:"yst--me-1 yst-ms-1 yst-h-5 yst-w-5 yst-text-slate-400 rtl:yst-rotate-[270deg]"})),(0,e.createElement)(Pl,{id:"link-llms-txt",link:"https://yoa.st/site-features-llmstxt-learn-more",ariaLabel:(0,Lt.__)("llms.txt","wordpress-seo")})))))))},Nl=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"}))})),Ol=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"}))})),Cl=Ia(Ba),Al=Ma(Ca),Il=()=>{const{values:t}=q(),{website_name:s,company_or_person:r,company_or_person_user_id:a,company_name:o,company_logo_id:n}=t.wpseo_titles,{other_social_urls:c}=t.wpseo_social,d=oa("selectUserById",[a],a),u=oa("selectLink",[],"https://yoa.st/1-p"),p=oa("selectLink",[],"https://yoa.st/3r3"),m=oa("selectLink",[],"https://yoa.st/site-representation-org-additional-info-upsell"),f=oa("selectLink",[],"https://yoa.st/site-representation-org-identifiers"),h=oa("selectLink",[],"https://yoa.st/site-representation-organization-person"),y=oa("selectPreference",[],"editUserUrl"),_=oa("selectPreference",[],"isLocalSeoActive"),w=oa("selectPreference",[],"companyOrPersonMessage"),g=oa("selectFallback",[],"siteLogoId"),b=oa("selectCanEditUser",[null==d?void 0:d.id],null==d?void 0:d.id),v=oa("selectPreference",[],"isPremium"),E=oa("selectUpsellSettingsAsProps"),x=oa("selectLink",[],"https://yoa.st/get-mastodon-integration"),k=oa("selectLink",[],"https://yoa.st/site-representation-mastodon"),S=oa("selectPreference",[],"localSeoPageSettingUrl");let L=lr((0,Lt.sprintf)(/* translators: %1$s expands for Yoast Local SEO, %2$s and %3$s expands to a link tags. */
(0,Lt.__)("You have %1$s activated on your site. You can provide your VAT ID and Tax ID in the %2$s‘Business info’ settings%3$s.","wordpress-seo"),"Yoast Local SEO","<a>","</a>"),{a:(0,e.createElement)("a",{href:S,target:"_blank",className:"yst-underline yst-font-medium"})}),T=lr((0,Lt.sprintf)(/* translators: %1$s expands for Yoast Local SEO, %2$s and %3$s expands to a link tags. */
(0,Lt.__)("You have %1$s activated on your site. You can provide your email and phone in the %2$s‘Business info’ settings%3$s.","wordpress-seo"),"Yoast Local SEO","<a>","</a>"),{a:(0,e.createElement)("a",{href:S,target:"_blank",className:"yst-underline yst-font-medium"})});S.includes("wpseo_locations")&&(L=lr((0,Lt.sprintf)(/* translators: %1$s expands for Yoast Local SEO, %2$s and %3$s expands to a link tags. */
(0,Lt.__)("You have %1$s activated on your site, and you've configured your business for multiple locations. This allows you to provide your VAT ID and Tax ID for %2$seach specific location%3$s.","wordpress-seo"),"Yoast Local SEO","<a>","</a>"),{a:(0,e.createElement)("a",{href:S,target:"_blank",className:"yst-underline yst-font-medium"})}),T=lr((0,Lt.sprintf)(/* translators: %1$s expands for Yoast Local SEO, %2$s and %3$s expands to a link tags. */
(0,Lt.__)("You have %1$s activated on your site, and you've configured your business for multiple locations. This allows you to provide your email and phone for %2$seach specific location%3$s.","wordpress-seo"),"Yoast Local SEO","<a>","</a>"),{a:(0,e.createElement)("a",{href:S,target:"_blank",className:"yst-underline yst-font-medium"})}));const F=(0,i.useCallback)((async e=>{var t;await e.push(""),null===(t=document.getElementById(`input-wpseo_social-other_social_urls-${c.length}`))||void 0===t||t.focus()}),[c]);return(0,e.createElement)(po,{title:(0,Lt.__)("Site representation","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,Lt.__)("This info is intended to appear in %1$sGoogle's Knowledge Graph%2$s.","wordpress-seo"),"<a>","</a>"),u,"link-google-knowledge-graph")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Organization/person","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,Lt.__)("Choose whether your site represents an organization or a person. %1$sLearn more about the differences and choosing between Organization and Person%2$s.","wordpress-seo"),"<a>","</a>"),h,"link-site-representation-organization-person")},_&&(0,e.createElement)(l.Alert,{id:"alert-local-seo-company-or-person",variant:"info"},w),(0,e.createElement)(l.RadioGroup,{disabled:_},(0,e.createElement)(te,{as:l.Radio,type:"radio",name:"wpseo_titles.company_or_person",id:"input-wpseo_titles-company_or_person-company",label:(0,Lt.__)("Organization","wordpress-seo"),value:"company",disabled:_}),(0,e.createElement)(te,{as:l.Radio,type:"radio",name:"wpseo_titles.company_or_person",id:"input-wpseo_titles-company_or_person-person",label:(0,Lt.__)("Person","wordpress-seo"),value:"person",disabled:_}))),(0,e.createElement)("section",{className:"yst-space-y-8"}),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)("div",{className:"yst-relative"},(0,e.createElement)(la.Z,{easing:"ease-out",duration:300,delay:300,height:"company"===r?"auto":0,animateOpacity:!0},(0,e.createElement)(ia,{title:(0,Lt.__)("Organization","wordpress-seo"),description:(0,Lt.__)("Please tell us more about your organization. This information will help Google to understand your website, and improve your chance of getting rich results.","wordpress-seo")},(!o||n<1)&&(0,e.createElement)(l.Alert,{id:"alert-organization-name-logo",variant:"info"},el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,Lt.__)("An organization name and logo need to be set for structured data to work properly. Since you haven’t set these yet, we are using the site name and logo as default values. %1$sLearn more about the importance of structured data%2$s.","wordpress-seo"),"<a>","</a>"),p,"link-structured-data")),(0,e.createElement)(te,{as:l.TextField,name:"wpseo_titles.company_name",id:"input-wpseo_titles-company_name",label:(0,Lt.__)("Organization name","wordpress-seo"),placeholder:s}),(0,e.createElement)(te,{as:l.TextField,name:"wpseo_titles.company_alternate_name",id:"input-wpseo_titles-company_alternate_name",label:(0,Lt.__)("Alternate organization name","wordpress-seo"),description:(0,Lt.__)("Use the alternate organization name for acronyms, or a shorter version of your organization's name.","wordpress-seo")}),(0,e.createElement)(ya,{id:"wpseo_titles-company_logo",label:(0,Lt.__)("Organization logo","wordpress-seo"),variant:"square",previewLabel:lr((0,Lt.sprintf)(
/* translators: %1$s expands to an opening strong tag.
       %2$s expands to a closing strong tag.
       %3$s expands to the recommended image size. */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","696x696px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})}),mediaUrlName:"wpseo_titles.company_logo",mediaIdName:"wpseo_titles.company_logo_id",fallbackMediaId:g})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{id:"fieldset-wpseo_social-other_social_urls",title:(0,Lt.__)("Other profiles","wordpress-seo"),description:(0,Lt.__)("Tell us if you have any other profiles on the web that belong to your organization. This can be any number of profiles, like YouTube, LinkedIn, Pinterest, or even Wikipedia.","wordpress-seo")},(0,e.createElement)(Ba,{as:l.TextField,name:"wpseo_social.facebook_site",id:"input-wpseo_social-facebook_site",label:(0,Lt.__)("Facebook","wordpress-seo"),placeholder:(0,Lt.__)("E.g. https://facebook.com/yoast","wordpress-seo")}),(0,e.createElement)(Ba,{as:l.TextField,name:"wpseo_social.twitter_site",id:"input-wpseo_social-twitter_site",label:(0,Lt.__)("X","wordpress-seo"),placeholder:(0,Lt.__)("E.g. https://x.com/yoast","wordpress-seo")}),(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!v,variant:"card",cardLink:x,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...E},(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_social.mastodon_url",id:"input-wpseo_social-mastodon_url",label:(0,Lt.__)("Mastodon","wordpress-seo"),placeholder:(0,Lt.__)("E.g. https://mastodon.social/@yoast","wordpress-seo"),labelSuffix:v&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium"),isDummy:!v,description:(0,e.createElement)(i.Fragment,null,(0,Lt.__)("Get your site verified in your Mastodon profile.","wordpress-seo")," ",(0,e.createElement)(l.Link,{id:"link-wpseo_social-mastodon_url",href:k,target:"_blank",rel:"noopener"},(0,Lt.__)("Read more about how to get your site verified.","wordpress-seo")))})),(0,e.createElement)(ie,{name:"wpseo_social.other_social_urls"},(t=>(0,e.createElement)(i.Fragment,null,c.map(((s,r)=>(0,e.createElement)(Js,{key:`wpseo_social.other_social_urls.${r}`,as:i.Fragment,appear:!0,show:!0,enter:"yst-transition yst-ease-out yst-duration-300",enterFrom:"yst-transform yst-opacity-0",enterTo:"yst-transform yst-opacity-100",leave:"yst-transition yst-ease-out yst-duration-300",leaveFrom:"yst-transform yst-opacity-100",leaveTo:"yst-transform yst-opacity-0"},(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-items-start yst-gap-2"},(0,e.createElement)(Ba,{as:l.TextField,name:`wpseo_social.other_social_urls.${r}`,id:`input-wpseo_social-other_social_urls-${r}`,label:(0,Lt.sprintf)((0,Lt.__)("Other profile %1$s","wordpress-seo"),r+1),placeholder:(0,Lt.__)("E.g. https://example.com/yoast","wordpress-seo"),className:"yst-grow"}),(0,e.createElement)(l.Button,{variant:"secondary",onClick:t.remove.bind(null,r),className:"yst-mt-7 yst-p-2.5","aria-label":(0,Lt.sprintf)((0,Lt.__)("Remove Other profile %1$s","wordpress-seo"),r+1)},(0,e.createElement)(Nl,{className:"yst-h-5 yst-w-5"})))))),(0,e.createElement)(l.Button,{id:"button-add-social-profile",variant:"secondary",onClick:()=>F(t)},(0,e.createElement)(Ol,{className:"yst--ms-1 yst-me-1 yst-h-5 yst-w-5 yst-text-slate-400"}),(0,Lt.__)("Add another profile","wordpress-seo")))))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)(i.Fragment,null,(0,Lt.__)("Additional organization info","wordpress-seo"),v&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium")),description:(0,Lt.__)("Enrich your organization's profile by providing more in-depth information. The more details you share, the better Google understands your website.","wordpress-seo")},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!v,variant:"card",cardLink:m,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...E},(0,e.createElement)(Cl,{as:l.TextareaField,name:"wpseo_titles.org-description",id:"input-wpseo_titles-org-description",label:(0,Lt.__)("Organization description","wordpress-seo"),isDummy:!v,maxLength:2e3}),_&&v&&(0,e.createElement)(l.Alert,{id:"alert-local-seo-vat-or-tax-id",variant:"info"},T),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-email",id:"input-wpseo_titles-org-email",type:"email",label:(0,Lt.__)("Organization email address","wordpress-seo"),isDummy:!v||_}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-phone",id:"input-wpseo_titles-org-phone",label:(0,Lt.__)("Organization phone number","wordpress-seo"),isDummy:!v||_}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-legal-name",id:"input-wpseo_titles-org-legal-name",label:(0,Lt.__)("Organization's legal name","wordpress-seo"),isDummy:!v}),(0,e.createElement)(Cl,{as:l.TextField,className:"yst-w-3/5",name:"wpseo_titles.org-founding-date",id:"input-wpseo_titles-org-founding-date",label:(0,Lt.__)("Organization's founding date","wordpress-seo"),type:"date",isDummy:!v}),(0,e.createElement)(Al,{name:"wpseo_titles.org-number-employees",className:"yst-w-3/5",id:"input-wpseo_titles-org-number-employees",label:(0,Lt.__)("Number of employees","wordpress-seo"),placeholder:(0,Lt.__)("Select a range / Enter a number","wordpress-seo"),isDummy:!v,options:[{value:"",label:"None"},{value:"1-10",label:(0,Lt.__)("1–10 employees","wordpress-seo")},{value:"11-50",label:(0,Lt.__)("11–50 employees","wordpress-seo")},{value:"51-200",label:(0,Lt.__)("51–200 employees","wordpress-seo")},{value:"201-500",label:(0,Lt.__)("201–500 employees","wordpress-seo")},{value:"501-1000",label:(0,Lt.__)("501–1000 employees","wordpress-seo")},{value:"1001-5000",label:(0,Lt.__)("1001–5000 employees","wordpress-seo")},{value:"5001-10000",label:(0,Lt.__)("5001–10000 employees","wordpress-seo")}]}))),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,e.createElement)(i.Fragment,null,(0,Lt.__)("Organization identifiers","wordpress-seo"),v&&(0,e.createElement)(l.Badge,{className:"yst-ms-1.5",size:"small",variant:"upsell"},"Premium")),description:(0,Lt.__)("Please tell us more about your organization’s identifiers. This information will help Google to display accurate and helpful details about your organization.","wordpress-seo")},(0,e.createElement)(l.FeatureUpsell,{shouldUpsell:!v,variant:"card",cardLink:f,cardText:(0,Lt.sprintf)(/* translators: %1$s expands to Premium. */
(0,Lt.__)("Unlock with %1$s","wordpress-seo"),"Premium"),...E},_&&v&&(0,e.createElement)(l.Alert,{id:"alert-local-seo-vat-or-tax-id",variant:"info"},L),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-vat-id",id:"input-wpseo_titles-org-vat-id",label:(0,Lt.__)("VAT ID","wordpress-seo"),isDummy:!v||_}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-tax-id",id:"input-wpseo_titles-org-tax-id",label:(0,Lt.__)("Tax ID","wordpress-seo"),isDummy:!v||_}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-iso",id:"input-wpseo_titles-org-iso",label:(0,Lt.__)("ISO 6523","wordpress-seo"),isDummy:!v}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-duns",id:"input-wpseo_titles-org-duns",label:(0,Lt.__)("DUNS","wordpress-seo"),isDummy:!v}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-leicode",id:"input-wpseo_titles-org-leicode",label:(0,Lt.__)("LEI code","wordpress-seo"),isDummy:!v}),(0,e.createElement)(Cl,{as:l.TextField,name:"wpseo_titles.org-naics",id:"input-wpseo_titles-org-naics",label:(0,Lt.__)("NAICS","wordpress-seo"),isDummy:!v})))),(0,e.createElement)(la.Z,{easing:"ease-out",duration:300,delay:300,height:"person"===r?"auto":0,animateOpacity:!0},(0,e.createElement)(ia,{title:(0,Lt.__)("Personal info","wordpress-seo"),description:(0,Lt.__)("Please tell us more about the person this site represents.","wordpress-seo")},(0,e.createElement)(La,{name:"wpseo_titles.company_or_person_user_id",id:"input-wpseo_titles-company_or_person_user_id",label:(0,Lt.__)("Select a user","wordpress-seo"),className:"yst-max-w-sm"}),!(0,le.isEmpty)(d)&&(0,e.createElement)(l.Alert,{id:"alert-person-user-profile"},b&&lr((0,Lt.sprintf)(
/* translators: %1$s and %2$s are replaced by opening and closing <span> tags.
     %3$s and %4$s are replaced by opening and closing <a> tags.
     %5$s is replaced by the selected user display name. */
(0,Lt.__)("You have selected the user %1$s%5$s%2$s as the person this site represents. Their user profile information will now be used in search results. %3$sUpdate their profile to make sure the information is correct%4$s.","wordpress-seo"),"<strong>","</strong>","<a>","</a>",null==d?void 0:d.name),{strong:(0,e.createElement)("strong",{className:"yst-font-medium"}),a:(0,e.createElement)("a",{id:"link-person-user-profile",href:`${y}?user_id=${null==d?void 0:d.id}`,target:"_blank",rel:"noopener noreferrer"})}),!b&&lr((0,Lt.sprintf)(
/* translators: %1$s and %2$s are replaced by opening and closing <span> tags.
     %3$s is replaced by the selected user display name. */
(0,Lt.__)("You have selected the user %1$s%3$s%2$s as the person this site represents. Their user profile information will now be used in search results. We're sorry, you're not allowed to edit this user's profile. Please contact your admin or %1$s%3$s%2$s to check and/or update the profile.","wordpress-seo"),"<strong>","</strong>",null==d?void 0:d.name),{strong:(0,e.createElement)("strong",{className:"yst-font-medium"})})),(0,e.createElement)(ya,{id:"wpseo_titles-person_logo",label:(0,Lt.__)("Personal logo or avatar","wordpress-seo"),variant:"square",previewLabel:lr((0,Lt.sprintf)(
/* translators: %1$s expands to an opening strong tag.
       %2$s expands to a closing strong tag.
       %3$s expands to the recommended image size. */
(0,Lt.__)("Recommended size for this image is %1$s%3$s%2$s","wordpress-seo"),"<strong>","</strong>","696x696px"),{strong:(0,e.createElement)("strong",{className:"yst-font-semibold"})}),mediaUrlName:"wpseo_titles.person_logo",mediaIdName:"wpseo_titles.person_logo_id",fallbackMediaId:g,disabled:!a})))))))},Ml=()=>{const t=oa("selectReplacementVariablesFor",[],"search","search"),s=oa("selectRecommendedReplacementVariablesFor",[],"search","search"),r=oa("selectReplacementVariablesFor",[],"404","404"),a=oa("selectRecommendedReplacementVariablesFor",[],"404","404");return(0,e.createElement)(po,{title:(0,Lt.__)("Special pages","wordpress-seo")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)(ia,{title:(0,Lt.__)("Internal search pages","wordpress-seo"),description:(0,Lt.__)("Determine how the title of your internal search pages should look in the browser.","wordpress-seo")},(0,e.createElement)(ga,{type:"title",name:"wpseo_titles.title-search-wpseo",fieldId:"input-wpseo_titles-title-search-wpseo",label:(0,Lt.__)("Page title","wordpress-seo"),replacementVariables:t,recommendedReplacementVariables:s})),(0,e.createElement)("hr",{className:"yst-my-8"}),(0,e.createElement)(ia,{title:(0,Lt.__)("404 error pages","wordpress-seo"),description:(0,Lt.__)("Determine how the title of your 404 error pages should look in the browser.","wordpress-seo")},(0,e.createElement)(ga,{type:"title",name:"wpseo_titles.title-404-wpseo",fieldId:"input-wpseo_titles-title-404-wpseo",label:(0,Lt.__)("Page title","wordpress-seo"),replacementVariables:r,recommendedReplacementVariables:a})))))},Dl=/content=(['"])?(?<content>[^'"> ]+)(?:\1|[ />])/,Bl=e=>{var t;const s=e.target.value.match(Dl);return null!=s&&null!==(t=s.groups)&&void 0!==t&&t.content?s.groups.content:e.target.value},Ul=Da(Na),jl=()=>{const t=oa("selectPreference",[],"siteUrl");return(0,e.createElement)(po,{title:(0,Lt.__)("Site connections","wordpress-seo"),description:(0,Lt.__)("Verify your site with different tools. This will add a verification meta tag to your homepage. You can find instructions on how to verify your site for each platform by following the link in the description.","wordpress-seo")},(0,e.createElement)(da,null,(0,e.createElement)("div",{className:"yst-max-w-5xl"},(0,e.createElement)("fieldset",{className:"yst-min-width-0 yst-max-w-screen-sm yst-space-y-8"},(0,e.createElement)(Ul,{as:l.TextField,type:"text",name:"wpseo.baiduverify",id:"input-wpseo-baiduverify",label:(0,Lt.__)("Baidu","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags, respectively.
(0,Lt.__)("Get your verification code in %1$sBaidu Webmaster tools%2$s.","wordpress-seo"),"<a>","</a>"),"https://ziyuan.baidu.com/site","link-baidu-webmaster-tools"),placeholder:(0,Lt.__)("Add verification code","wordpress-seo"),transformValue:Bl}),(0,e.createElement)(Ul,{as:l.TextField,type:"text",name:"wpseo.msverify",id:"input-wpseo-msverify",label:(0,Lt.__)("Bing","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags, respectively.
(0,Lt.__)("Get your verification code in %1$sBing Webmaster tools%2$s.","wordpress-seo"),"<a>","</a>"),`https://www.bing.com/toolbox/webmaster/#/Dashboard/?url=${t}`,"link-bing-webmaster-tools"),placeholder:(0,Lt.__)("Add verification code","wordpress-seo"),transformValue:Bl}),(0,e.createElement)(Ul,{as:l.TextField,type:"text",name:"wpseo.googleverify",id:"input-wpseo-googleverify",label:(0,Lt.__)("Google","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags, respectively.
(0,Lt.__)("Get your verification code in %1$sGoogle Search console%2$s.","wordpress-seo"),"<a>","</a>"),(0,Rt.addQueryArgs)("https://search.google.com/search-console/users",{hl:"en",tid:"alternate",siteUrl:t}),"link-google-search-console"),placeholder:(0,Lt.__)("Add verification code","wordpress-seo"),transformValue:Bl}),(0,e.createElement)(Ul,{as:l.TextField,type:"text",name:"wpseo_social.pinterestverify",id:"input-wpseo_social-pinterestverify",label:(0,Lt.__)("Pinterest","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags, respectively.
(0,Lt.__)("Claim your site over at %1$sPinterest%2$s.","wordpress-seo"),"<a>","</a>"),"https://www.pinterest.com/settings/claim","link-pinterest"),placeholder:(0,Lt.__)("Add verification code","wordpress-seo"),transformValue:Bl}),(0,e.createElement)(Ul,{as:l.TextField,type:"text",name:"wpseo.yandexverify",id:"input-wpseo-yandexverify",label:(0,Lt.__)("Yandex","wordpress-seo"),description:el((0,Lt.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags, respectively.
(0,Lt.__)("Get your verification code in %1$sYandex Webmaster tools%2$s.","wordpress-seo"),"<a>","</a>"),"https://webmaster.yandex.com/sites/add/","link-yandex-webmaster-tools"),placeholder:(0,Lt.__)("Add verification code","wordpress-seo"),transformValue:Bl})))))},Vl=({postTypes:t,taxonomies:s,idSuffix:r=""})=>{const a=(0,l.useSvgAria)(),o=oa("selectPreference",[],"isPremium"),n=(0,i.useCallback)((({show:t,toggle:s,ariaProps:r})=>{const o=(0,i.useMemo)((()=>t?sr:rr),[t]);return(0,e.createElement)("div",{className:"yst-relative"},(0,e.createElement)("hr",{className:"yst-absolute yst-inset-x-0 yst-top-1/2 yst-bg-slate-200"}),(0,e.createElement)("button",{type:"button",className:"yst-relative yst-flex yst-items-center yst-gap-1.5 yst-px-2.5 yst-py-1 yst-mx-auto yst-text-xs yst-font-medium yst-text-slate-700 yst-bg-slate-50 yst-rounded-full yst-border yst-border-slate-300 hover:yst-bg-white hover:yst-text-slate-800 focus:yst-outline-none focus:yst-ring-2 focus:yst-ring-primary-500 focus:yst-ring-offset-2",onClick:s,...r},t?(0,Lt.__)("Show less","wordpress-seo"):(0,Lt.__)("Show more","wordpress-seo"),(0,e.createElement)(o,{className:"yst-h-4 yst-w-4 yst-flex-shrink-0 yst-text-slate-400 group-hover:yst-text-slate-500 yst-stroke-3",...a})))}),[]);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("header",{className:"yst-px-3 yst-mb-6 yst-space-y-6"},(0,e.createElement)(Et,{id:`link-yoast-logo${r}`,to:"/",className:"yst-inline-block yst-rounded-md focus:yst-ring-primary-500","aria-label":"Yoast SEO"+(o?" Premium":"")},(0,e.createElement)(Xr,{className:"yst-w-40",...a})),(0,e.createElement)(Qi,{buttonId:`button-search${r}`})),(0,e.createElement)("div",{className:"yst-px-0.5 yst-space-y-6"},(0,e.createElement)(l.SidebarNavigation.MenuItem,{id:`menu-general${r}`,icon:Qs,label:(0,Lt.__)("General","wordpress-seo")},(0,e.createElement)(Er,{to:"/site-features",label:(0,Lt.__)("Site features","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/site-basics",label:(0,Lt.__)("Site basics","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/site-representation",label:(0,Lt.__)("Site representation","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/site-connections",label:(0,Lt.__)("Site connections","wordpress-seo"),idSuffix:r})),(0,e.createElement)(l.SidebarNavigation.MenuItem,{id:`menu-content-types${r}`,icon:Xs,label:(0,Lt.__)("Content types","wordpress-seo")},(0,e.createElement)(l.ChildrenLimiter,{limit:5,renderButton:n},(0,e.createElement)(Er,{to:"/homepage",label:(0,Lt.__)("Homepage","wordpress-seo"),idSuffix:r}),(0,le.map)(t,(({name:t,route:s,label:a,isNew:o})=>(0,e.createElement)(Er,{key:`link-post-type-${t}`,to:`/post-type/${s}`,label:(0,e.createElement)("span",{className:"yst-inline-flex yst-items-center yst-gap-1.5"},a,o&&(0,e.createElement)(l.Badge,{variant:"info"},(0,Lt.__)("New","wordpress-seo"))),idSuffix:r}))))),(0,e.createElement)(l.SidebarNavigation.MenuItem,{id:`menu-categories-and-tags${r}`,icon:er,label:(0,Lt.__)("Categories & tags","wordpress-seo")},(0,e.createElement)(l.ChildrenLimiter,{limit:5,renderButton:n},(0,le.map)(s,(t=>(0,e.createElement)(Er,{key:`link-taxonomy-${t.name}`,to:`/taxonomy/${t.route}`,label:(0,e.createElement)("span",{className:"yst-inline-flex yst-items-center yst-gap-1.5"},t.label,t.isNew&&(0,e.createElement)(l.Badge,{variant:"info"},(0,Lt.__)("New","wordpress-seo"))),idSuffix:r}))))),(0,e.createElement)(l.SidebarNavigation.MenuItem,{id:`menu-advanced${r}`,icon:tr,label:(0,Lt.__)("Advanced","wordpress-seo"),defaultOpen:!1},(0,e.createElement)(Er,{to:"/crawl-optimization",label:(0,Lt.__)("Crawl optimization","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/breadcrumbs",label:(0,Lt.__)("Breadcrumbs","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/author-archives",label:(0,Lt.__)("Author archives","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/date-archives",label:(0,Lt.__)("Date archives","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/format-archives",label:(0,Lt.__)("Format archives","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/special-pages",label:(0,Lt.__)("Special pages","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/media-pages",label:(0,Lt.__)("Media pages","wordpress-seo"),idSuffix:r}),(0,e.createElement)(Er,{to:"/rss",label:(0,Lt.__)("RSS","wordpress-seo"),idSuffix:r}))))};Vl.propTypes={postTypes:ir().object.isRequired,taxonomies:ir().object.isRequired,idSuffix:ir().string};const zl=()=>{const{pathname:t}=Qe(),s=oa("selectPostTypes"),r=oa("selectTaxonomies"),o=oa("selectPreference",[],"isPremium"),n=oa("selectLink",[],"https://yoa.st/17h"),c=oa("selectLink",[],"https://yoa.st/jj"),d=oa("selectUpsellSettingsAsProps"),u=oa("selectLink",[],"https://yoa.st/3t6"),{isPromotionActive:p}=(0,a.useSelect)(ta);(()=>{const{hash:e,pathname:t,key:s}=Qe();(0,i.useEffect)((()=>{const t=e.replace("#",""),s=document.getElementById(t)||document.querySelector(`[data-id="${t}"]`);if(s)s.scrollIntoView({behavior:"smooth"}),setTimeout((()=>s.focus()),800);else{const e=document.getElementById("yoast-seo-settings");null==e||e.scrollIntoView({behavior:"smooth"})}}),[t,e,s])})();const{dirty:m}=q();return(0,l.useBeforeUnload)(m,(0,Lt.__)("There are unsaved changes on this page. Leaving means that those changes will be lost. Are you sure you want to leave this page?","wordpress-seo")),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Va,null),(0,e.createElement)(l.SidebarNavigation,{activePath:t},(0,e.createElement)(l.SidebarNavigation.Mobile,{openButtonId:"button-open-settings-navigation-mobile",closeButtonId:"button-close-settings-navigation-mobile"
/* translators: Hidden accessibility text. */,openButtonScreenReaderText:(0,Lt.__)("Open settings navigation","wordpress-seo")
/* translators: Hidden accessibility text. */,closeButtonScreenReaderText:(0,Lt.__)("Close settings navigation","wordpress-seo"),"aria-label":(0,Lt.__)("Settings navigation","wordpress-seo")},(0,e.createElement)(Vl,{idSuffix:"-mobile",postTypes:s,taxonomies:r})),(0,e.createElement)("div",{className:"yst-p-4 min-[783px]:yst-p-8 yst-flex yst-gap-4"},(0,e.createElement)("aside",{className:"yst-sidebar yst-sidebar-nav yst-shrink-0 yst-hidden min-[783px]:yst-block yst-pb-6 yst-bottom-0 yst-w-56"},(0,e.createElement)(l.SidebarNavigation.Sidebar,null,(0,e.createElement)(Vl,{postTypes:s,taxonomies:r}))),(0,e.createElement)("div",{className:or()("yst-flex yst-grow yst-flex-wrap",!o&&"xl:yst-pe-[17.5rem]")},(0,e.createElement)("div",{className:"yst-grow yst-max-w-page yst-space-y-6 yst-mb-8 xl:yst-mb-0"},(0,e.createElement)(l.Paper,{as:"main"},(0,e.createElement)(l.ErrorBoundary,{FallbackComponent:Xi},(0,e.createElement)(Js,{key:t,appear:!0,show:!0,enter:"yst-transition-opacity yst-delay-100 yst-duration-300",enterFrom:"yst-opacity-0",enterTo:"yst-opacity-100"},(0,e.createElement)(ft,null,(0,e.createElement)(pt,{path:"author-archives",element:(0,e.createElement)(cl,null)}),(0,e.createElement)(pt,{path:"breadcrumbs",element:(0,e.createElement)(dl,null)}),(0,e.createElement)(pt,{path:"crawl-optimization",element:(0,e.createElement)(fl,null)}),(0,e.createElement)(pt,{path:"date-archives",element:(0,e.createElement)(yl,null)}),(0,e.createElement)(pt,{path:"homepage",element:(0,e.createElement)(vl,null)}),(0,e.createElement)(pt,{path:"format-archives",element:(0,e.createElement)(wl,null)}),(0,e.createElement)(pt,{path:"media-pages",element:(0,e.createElement)(El,null)}),(0,e.createElement)(pt,{path:"rss",element:(0,e.createElement)(kl,null)}),(0,e.createElement)(pt,{path:"site-basics",element:(0,e.createElement)(Tl,null)}),(0,e.createElement)(pt,{path:"site-connections",element:(0,e.createElement)(jl,null)}),(0,e.createElement)(pt,{path:"site-representation",element:(0,e.createElement)(Il,null)}),(0,e.createElement)(pt,{path:"site-features",element:(0,e.createElement)(Rl,null)}),(0,e.createElement)(pt,{path:"special-pages",element:(0,e.createElement)(Ml,null)}),(0,e.createElement)(pt,{path:"post-type"},(0,le.map)(s,(t=>(0,e.createElement)(pt,{key:`route-post-type-${t.name}`,path:t.route,element:(0,e.createElement)(al,{...t})})))),(0,e.createElement)(pt,{path:"taxonomy"},(0,le.map)(r,(t=>(0,e.createElement)(pt,{key:`route-taxonomy-${t.name}`,path:t.route,element:(0,e.createElement)(il,{...t})})))),(0,e.createElement)(pt,{path:"*",element:(0,e.createElement)(ut,{to:"/site-features",replace:!0})}))))),!o&&(0,e.createElement)(qr,{premiumLink:n,premiumUpsellConfig:d,isPromotionActive:p})),!o&&(0,e.createElement)("div",{className:"xl:yst-max-w-3xl xl:yst-fixed xl:yst-end-8 xl:yst-w-[16rem]"},(0,e.createElement)(Wr,{premiumLink:c,premiumUpsellConfig:d,academyLink:u,isPromotionActive:p}))))))},ql=window.yoast.externals.redux,Wl=()=>(0,le.get)(window,"wpseoScriptData.postTypes",{}),Hl="updatePostTypeReviewStatus",Yl=(0,Tt.createSlice)({name:"postTypes",initialState:Wl(),reducers:{},extraReducers:e=>{e.addCase(`${Hl}/result`,((e,{payload:t})=>{e[t].isNew=!1}))}}),Gl={selectPostType:(e,t,s={})=>(0,le.get)(e,`postTypes.${t}`,s),selectAllPostTypes:(e,t=null)=>{const s=(0,le.get)(e,"postTypes",{});return t?(0,le.pick)(s,t):s}};Gl.selectPostTypes=(0,Tt.createSelector)(Gl.selectAllPostTypes,(e=>(0,le.omit)(e,["attachment"])));const Kl={[Hl]:async({payload:e})=>Gt()({path:"/yoast/v1/new-content-type-visibility/dismiss-post-type",method:"POST",data:{post_type_name:e}})},Zl={...Yl.actions,updatePostTypeReviewStatus:function*(e){try{yield{type:Hl,payload:e}}catch(t){console.error(`Error: Failed to remove "New" badge for ${e}, ${t}`)}return{type:`${Hl}/result`,payload:e}}},Jl=Yl.reducer,Ql=()=>{var e;return{...(0,le.get)(window,"wpseoScriptData.preferences",{}),documentTitle:(0,le.defaultTo)(null===(e=document)||void 0===e?void 0:e.title,"")}},Xl=(0,Tt.createSlice)({name:"preferences",initialState:Ql(),reducers:{}}),ec={selectPreference:(e,t,s={})=>(0,le.get)(e,`preferences.${t}`,s),selectPreferences:e=>(0,le.get)(e,"preferences",{})};ec.selectHasPageForPosts=(0,Tt.createSelector)([e=>ec.selectPreference(e,"homepageIsLatestPosts"),e=>ec.selectPreference(e,"homepagePostsEditUrl")],((e,t)=>!e&&!(0,le.isEmpty)(t))),ec.selectCanEditUser=(0,Tt.createSelector)([e=>ec.selectPreference(e,"currentUserId",-1),e=>ec.selectPreference(e,"canEditUsers",!1),(e,t)=>t],((e,t,s)=>e===s||t)),ec.selectExampleUrl=(0,Tt.createSelector)([e=>ec.selectPreference(e,"siteUrl","https://example.com"),(e,t)=>t],((e,t)=>e+t)),ec.selectPluginUrl=(0,Tt.createSelector)([e=>ec.selectPreference(e,"pluginUrl","https://example.com"),(e,t)=>t],((e,t)=>e+t)),ec.selectUpsellSettingsAsProps=(0,Tt.createSelector)([e=>ec.selectPreference(e,"upsellSettings",{}),(e,t="premiumCtbId")=>t],((e,t)=>({"data-action":null==e?void 0:e.actionId,"data-ctb-id":null==e?void 0:e[t]})));const tc=Xl.actions,sc=Xl.reducer,rc=()=>(0,le.reduce)((0,le.get)(window,"wpseoScriptData.taxonomies",{}),((e,{postTypes:t,...s},r)=>({...e,[r]:{...s,postTypes:(0,le.values)(t)}})),{}),ac="updateTaxonomyReviewStatus",oc=(0,Tt.createSlice)({name:"taxonomies",initialState:rc(),reducers:{},extraReducers:e=>{e.addCase(`${ac}/result`,((e,{payload:t})=>{e[t].isNew=!1}))}}),nc={selectTaxonomy:(e,t,s={})=>(0,le.get)(e,`taxonomies.${t}`,s),selectAllTaxonomies:e=>(0,le.get)(e,"taxonomies",{})};nc.selectTaxonomies=(0,Tt.createSelector)(nc.selectAllTaxonomies,(e=>(0,le.omit)(e,["post_format"])));const ic={[ac]:async({payload:e})=>Gt()({path:"/yoast/v1/new-content-type-visibility/dismiss-taxonomy",method:"POST",data:{taxonomy_name:e}})},lc={...oc.actions,updateTaxonomyReviewStatus:function*(e){try{yield{type:ac,payload:e}}catch(t){console.error(`Error: Failed to remove "New" badge for ${e}, ${t}`)}return{type:`${ac}/result`,payload:e}}},cc=oc.reducer,dc={selectBreadcrumbsForPostTypes:(0,Tt.createSelector)([nc.selectAllTaxonomies,Gl.selectAllPostTypes],((e,t)=>{const s={value:0,label:(0,Lt.__)("None","wordpress-seo")},r={};return(0,le.forEach)(t,((t,a)=>{const o=(0,le.filter)(e,(e=>(0,le.includes)(e.postTypes,a)));(0,le.isEmpty)(o)||(r[a]={...t,options:[s,...(0,le.map)(o,(({name:e,label:t})=>({value:e,label:t})))]})})),r})),selectBreadcrumbsForTaxonomies:(0,Tt.createSelector)([nc.selectAllTaxonomies,Gl.selectAllPostTypes,ec.selectHasPageForPosts],((e,t,s)=>{let r=[{value:0,label:(0,Lt.__)("None","wordpress-seo")}];s&&r.push({value:"post",label:(0,Lt.__)("Blog","wordpress-seo")});const a=(0,le.filter)(t,(({hasArchive:e})=>e));return r=r.concat((0,le.map)(a,(({name:e,label:t})=>({value:e,label:t})))),(0,le.mapValues)(e,(e=>({name:e.name,label:e.label,options:r})))}))},uc=()=>({...(0,le.get)(window,"wpseoScriptData.defaultSettingValues",{})}),pc=(0,Tt.createSlice)({name:"defaultSettingValues",initialState:uc(),reducers:{}}),mc={selectDefaultSettingValue:(e,t,s={})=>(0,le.get)(e,`defaultSettingValues.${t}`,s),selectDefaultSettingValues:e=>(0,le.get)(e,"defaultSettingValues",{})},fc=pc.actions,hc=pc.reducer,yc=()=>(0,le.get)(window,"wpseoScriptData.fallbacks",{}),_c=(0,Tt.createSlice)({name:"fallbacks",initialState:yc(),reducers:{}}),wc={selectFallback:(e,t,s={})=>(0,le.get)(e,`fallbacks.${t}`,s),selectFallbacks:e=>(0,le.get)(e,"fallbacks",{})},gc=_c.actions,bc=_c.reducer,vc=(0,Tt.createEntityAdapter)(),Ec=()=>vc.getInitialState({status:Qt,error:""}),xc="fetchMedia",kc=e=>{var t,s;return{id:null==e?void 0:e.id,title:(null==e||null===(t=e.title)||void 0===t?void 0:t.rendered)||(null==e?void 0:e.title),slug:(null==e?void 0:e.slug)||(null==e?void 0:e.name),alt:(null==e?void 0:e.alt_text)||(null==e?void 0:e.alt),url:(null==e?void 0:e.source_url)||(null==e?void 0:e.url),type:(null==e?void 0:e.media_type)||(null==e?void 0:e.type),mime:(null==e?void 0:e.mime_type)||(null==e?void 0:e.mime),author:null==e?void 0:e.author,sizes:(0,le.mapValues)((null==e?void 0:e.sizes)||(null==e||null===(s=e.media_details)||void 0===s?void 0:s.sizes),(e=>({url:(null==e?void 0:e.url)||(null==e?void 0:e.source_url),width:null==e?void 0:e.width,height:null==e?void 0:e.height})),{})}},Sc=(0,Tt.createSlice)({name:"media",initialState:Ec(),reducers:{addOneMedia:{reducer:vc.addOne,prepare:e=>({payload:kc(e)})},addManyMedia:{reducer:vc.addMany,prepare:e=>({payload:(0,le.map)(e,kc)})}},extraReducers:e=>{e.addCase(`${xc}/${Kt}`,(e=>{e.status=Xt})),e.addCase(`${xc}/${Zt}`,((e,t)=>{e.status=es,vc.addMany(e,(0,le.map)(t.payload,kc))})),e.addCase(`${xc}/${Jt}`,((e,t)=>{e.status=ts,e.error=t.payload}))}}),Lc=vc.getSelectors((e=>e.media)),Tc={selectMediaIds:Lc.selectIds,selectMediaById:Lc.selectById,selectIsMediaLoading:e=>(0,le.get)(e,"media.status",Qt)===Xt,selectIsMediaError:e=>(0,le.get)(e,"media.status",Qt)===ts},Fc={...Sc.actions,fetchMedia:function*(e){yield{type:`${xc}/${Kt}`};try{const t=yield{type:xc,payload:{per_page:100,include:e}};return{type:`${xc}/${Zt}`,payload:t}}catch(e){return{type:`${xc}/${Jt}`,payload:e}}}},$c={[xc]:async({payload:e})=>Gt()({path:`/wp/v2/media?${(0,Rt.buildQueryString)(e)}`})},Pc=Sc.reducer,Rc=window.wp.htmlEntities,Nc=(0,Tt.createEntityAdapter)(),Oc="fetchPages",Cc="pages";let Ac;const Ic=e=>{var t;return{id:null==e?void 0:e.id,name:(0,Rc.decodeEntities)((0,le.trim)(null==e?void 0:e.title.rendered))||(null==e?void 0:e.slug)||e.id,slug:null==e?void 0:e.slug,protected:null==e||null===(t=e.content)||void 0===t?void 0:t.protected}},Mc=(0,Tt.createSlice)({name:"pages",initialState:Nc.getInitialState({status:Qt,error:""}),reducers:{addOnePage:{reducer:Nc.addOne,prepare:e=>({payload:Ic(e)})},addManyPages:{reducer:Nc.addMany,prepare:e=>({payload:(0,le.map)(e,Ic)})}},extraReducers:e=>{e.addCase(`${Oc}/${Kt}`,(e=>{e.status=Xt})),e.addCase(`${Oc}/${Zt}`,((e,t)=>{e.status=es,Nc.addMany(e,(0,le.map)(t.payload,Ic))})),e.addCase(`${Oc}/${Jt}`,((e,t)=>{e.status=ts,e.error=t.payload}))}}),Dc=Mc.getInitialState,Bc=Nc.getSelectors((e=>e.pages)),Uc={selectPageIds:Bc.selectIds,selectPageById:Bc.selectById,selectPages:Bc.selectEntities};Uc.selectPagesWith=(0,Tt.createSelector)([Uc.selectPages,(e,t={})=>t],((e,t)=>{const s={};t.forEach((t=>{null!=t&&t.id&&!e[t.id]&&(s[t.id]={...t})}));const r=(0,le.pickBy)(e,(e=>!e.protected));return{...s,...r}}));const jc={...Mc.actions,fetchPages:function*(e){yield{type:`${Oc}/${Kt}`};try{const t=yield{type:Oc,payload:{...e}};return{type:`${Oc}/${Zt}`,payload:t}}catch(e){return{type:`${Oc}/${Jt}`,payload:e}}}},Vc={[Oc]:async({payload:e})=>{var t;return null===(t=Ac)||void 0===t||t.abort(),Ac=new AbortController,Gt()({path:`/wp/v2/pages?${(0,Rt.buildQueryString)(e)}`,signal:Ac.signal})}},zc=Mc.reducer,qc=()=>({recommended:(0,le.get)(window,"wpseoScriptData.replacementVariables.recommended",{}),shared:(0,le.get)(window,"wpseoScriptData.replacementVariables.shared",[]),specific:(0,le.get)(window,"wpseoScriptData.replacementVariables.specific",{}),variables:(0,le.get)(window,"wpseoScriptData.replacementVariables.variables",[])}),Wc=(0,Tt.createSlice)({name:"replacementVariables",initialState:qc(),reducers:{}}),Hc={selectRecommendedReplacementVariables:e=>(0,le.get)(e,"replacementVariables.recommended",{}),selectSharedReplacementVariables:e=>(0,le.get)(e,"replacementVariables.shared",[]),selectSpecificReplacementVariables:e=>(0,le.get)(e,"replacementVariables.specific",{}),selectReplacementVariables:e=>(0,le.get)(e,"replacementVariables.variables",[])};Hc.selectSpecificReplacementVariablesFor=(0,Tt.createSelector)([Hc.selectSharedReplacementVariables,Hc.selectSpecificReplacementVariables,(e,t)=>t,(e,t,s)=>s],((e,t,s,r)=>[...e,...(0,le.get)(t,s,(0,le.get)(t,r,[]))])),Hc.selectReplacementVariablesFor=(0,Tt.createSelector)([Hc.selectReplacementVariables,Hc.selectSpecificReplacementVariablesFor],((e,t)=>(0,le.filter)(e,(({name:e})=>(0,le.includes)(t,e))))),Hc.selectRecommendedReplacementVariablesFor=(0,Tt.createSelector)([Hc.selectRecommendedReplacementVariables,(e,t)=>t,(e,t,s)=>s],((e,t,s)=>(0,le.get)(e,t,(0,le.get)(e,s,[]))));const Yc=Wc.actions,Gc=Wc.reducer,Kc=(e,t)=>{
// translators: %1$s expands to the schema type, e.g. "Web Page" or "Blog Post".
const s=(0,Lt.__)("%1$s (default)","wordpress-seo");return e.map((({label:e,value:r})=>({value:r,label:r===t?(0,Lt.sprintf)(s,e):e})))},Zc=()=>({articleTypes:(0,le.get)(window,"wpseoScriptData.schema.articleTypes",{}),articleTypeDefaults:(0,le.get)(window,"wpseoScriptData.schema.articleTypeDefaults",{}),pageTypes:(0,le.get)(window,"wpseoScriptData.schema.pageTypes",{}),pageTypeDefaults:(0,le.get)(window,"wpseoScriptData.schema.pageTypeDefaults",{})}),Jc=(0,Tt.createSlice)({name:"schema",initialState:Zc(),reducers:{}}),Qc={selectSchema:e=>(0,le.get)(e,"schema",{}),selectArticleTypes:e=>(0,le.get)(e,"schema.articleTypes",{}),selectArticleTypeDefaults:e=>(0,le.get)(e,"schema.articleTypeDefaults",{}),selectPageTypes:e=>(0,le.get)(e,"schema.pageTypes",{}),selectPageTypeDefaults:e=>(0,le.get)(e,"schema.pageTypeDefaults",{})};Qc.selectArticleTypeValues=(0,Tt.createSelector)(Qc.selectArticleTypes,(e=>(0,le.values)(e))),Qc.selectArticleTypeDefault=(0,Tt.createSelector)([Qc.selectArticleTypeDefaults,(e,t)=>t],((e,t)=>(0,le.get)(e,t,"None"))),Qc.selectArticleTypeValuesFor=(0,Tt.createSelector)([Qc.selectArticleTypeValues,Qc.selectArticleTypeDefault],((e,t)=>Kc(e,t))),Qc.selectPageTypeValues=(0,Tt.createSelector)(Qc.selectPageTypes,(e=>(0,le.values)(e))),Qc.selectPageTypeDefault=(0,Tt.createSelector)([Qc.selectPageTypeDefaults,(e,t)=>t],((e,t)=>(0,le.get)(e,t,"WebPage"))),Qc.selectPageTypeValuesFor=(0,Tt.createSelector)([Qc.selectPageTypeValues,Qc.selectPageTypeDefault],((e,t)=>Kc(e,t)));const Xc=Jc.actions,ed=Jc.reducer,td=(e,{userLocale:t})=>bn((0,le.join)([...(0,le.isArray)(null==e?void 0:e.keywords)?e.keywords:[],null==e?void 0:e.routeLabel,null==e?void 0:e.fieldLabel]," "),t),sd=(e,t="",{userLocale:s})=>(0,le.reduce)(e,((e,r,a)=>{const o=(0,le.join)((0,le.filter)([t,a],Boolean),".");return"other_social_urls"===a?{...e,[o]:{route:null==r?void 0:r.route,routeLabel:null==r?void 0:r.routeLabel,fieldId:null==r?void 0:r.fieldId,fieldLabel:null==r?void 0:r.fieldLabel,keywords:td(r,{userLocale:s})}}:null!=r&&r.route?{...e,[o]:{...r,keywords:td(r,{userLocale:s})}}:{...e,...sd(r,o,{userLocale:s})}}),{}),rd=()=>{const e=(0,le.get)(window,"wpseoScriptData.postTypes",{}),t=(0,le.get)(window,"wpseoScriptData.taxonomies",{}),s=(0,le.get)(window,"wpseoScriptData.preferences.userLocale",{});return{index:xn(e,t,{userLocale:s})}},ad=(0,Tt.createSlice)({name:"search",initialState:rd(),reducers:{}}),od={selectSearchIndex:e=>(0,le.get)(e,"search.index",{})};od.selectQueryableSearchIndex=(0,Tt.createSelector)([od.selectSearchIndex,e=>ec.selectPreference(e,"userLocale")],((e,t)=>sd(e,"",{userLocale:t})));const nd=ad.actions,id=ad.reducer,ld=(0,Tt.createEntityAdapter)(),cd="fetchUsers",dd=()=>ld.getInitialState({status:Qt,error:""}),ud=e=>({id:null==e?void 0:e.id,name:(0,le.trim)(null==e?void 0:e.name)||(null==e?void 0:e.slug)||(null==e?void 0:e.id),slug:null==e?void 0:e.slug}),pd=(0,Tt.createSlice)({name:"users",initialState:dd(),reducers:{addOneUser:{reducer:ld.addOne,prepare:e=>({payload:ud(e)})},addManyUsers:{reducer:ld.addMany,prepare:e=>({payload:(0,le.map)(e,ud)})}},extraReducers:e=>{e.addCase(`${cd}/${Kt}`,(e=>{e.status=Xt})),e.addCase(`${cd}/${Zt}`,((e,t)=>{e.status=es,ld.addMany(e,(0,le.map)(t.payload,ud))})),e.addCase(`${cd}/${Jt}`,((e,t)=>{e.status=ts,e.error=t.payload}))}}),md=ld.getSelectors((e=>e.users)),fd={selectUserIds:md.selectIds,selectUserById:md.selectById,selectUsers:md.selectEntities};fd.selectUsersWith=(0,Tt.createSelector)([fd.selectUsers,(e,t={})=>t],((e,t)=>null!=t&&t.id&&!e[t.id]?{...e,[t.id]:{...t}}:e));const hd={...pd.actions,fetchUsers:function*(e){yield{type:`${cd}/${Kt}`};try{const t=yield{type:cd,payload:{...e}};return{type:`${cd}/${Zt}`,payload:t}}catch(e){return{type:`${cd}/${Jt}`,payload:e}}}},yd={[cd]:async({payload:e})=>Gt()({path:`/wp/v2/users?${(0,Rt.buildQueryString)(e)}`})},_d=pd.reducer,{isPromotionActive:wd}=ql.selectors,{currentPromotions:gd}=ql.reducers,{setCurrentPromotions:bd}=ql.actions,vd=({initialState:e={}}={})=>{(0,a.register)((({initialState:e})=>(0,a.createReduxStore)(ta,{actions:{...fc,...gc,...It,...Fc,...Vt,...jc,...Zl,...tc,...Yc,...Xc,...nd,...lc,...hd,setCurrentPromotions:bd},selectors:{...dc,...mc,...wc,...At,...Tc,...jt,...Uc,...Gl,...ec,...Hc,...Qc,...od,...nc,...fd,isPromotionActive:wd},initialState:(0,le.merge)({},{defaultSettingValues:uc(),fallbacks:yc(),[Nt]:Ct(),media:Ec(),[Dt]:Ut(),[Cc]:Dc(),postTypes:Wl(),preferences:Ql(),replacementVariables:qc(),schema:Zc(),search:rd(),taxonomies:rc(),users:dd(),currentPromotions:{promotions:[]}},e),reducer:(0,a.combineReducers)({defaultSettingValues:hc,fallbacks:bc,[Nt]:Mt,media:Pc,[Dt]:zt,[Cc]:zc,postTypes:Jl,preferences:sc,replacementVariables:Gc,schema:ed,search:id,taxonomies:cc,users:_d,currentPromotions:gd}),controls:{...$c,...yd,...Kl,...ic,...Vc}}))({initialState:e}))};n()((()=>{const t=document.getElementById("yoast-seo-settings");if(!t)return;const s=document.createElement("div"),o=s.attachShadow({mode:"open"});document.body.appendChild(s);const n=(0,le.get)(window,"wpseoScriptData.settings",{}),c=(0,le.get)(window,"wpseoScriptData.fallbacks",{}),d=(0,le.get)(window,"wpseoScriptData.postTypes",{}),u=(0,le.get)(window,"wpseoScriptData.taxonomies",{}),p=(0,le.get)(window,"wpseoScriptData.showNewContentTypeNotification",!1)?{"new-content-type":{id:"new-content-type",variant:"info",size:"large",title:(0,Lt.__)("New type of content added to your site!","wordpress-seo"),description:(0,Lt.__)("Please see the “New” badges and review the Search appearance settings.","wordpress-seo")}}:{};vd({initialState:{notifications:p,[Nt]:(0,le.get)(window,"wpseoScriptData.linkParams",{}),currentPromotions:{promotions:(0,le.get)(window,"wpseoScriptData.currentPromotions",[])}}}),(async({settings:e,fallbacks:t})=>{const s=(0,le.get)(e,"wpseo_titles",{}),r=(0,le.filter)([(0,le.get)(e,"wpseo_social.og_default_image_id","0"),(0,le.get)(e,"wpseo_titles.open_graph_frontpage_image_id","0"),(0,le.get)(e,"wpseo_titles.company_logo_id","0"),(0,le.get)(e,"wpseo_titles.person_logo_id","0"),(0,le.get)(t,"siteLogoId","0"),...(0,le.reduce)(s,((e,t,s)=>(0,le.includes)(s,"social-image-id")?[...e,t]:e),[])],Boolean),o=(0,le.chunk)(r,100),{fetchMedia:n}=(0,a.dispatch)(ta);(0,le.forEach)(o,n)})({settings:n,fallbacks:c}),(async({settings:e})=>{const t=(0,le.get)(e,"wpseo_titles.company_or_person_user_id"),{fetchUsers:s}=(0,a.dispatch)(ta);t&&s({include:[t]})})({settings:n}),document.querySelector('[href="#wpbody-content"]').addEventListener("click",(e=>{var t,s;e.preventDefault(),window.outerWidth>782?null===(s=document.getElementById("link-yoast-logo"))||void 0===s||s.focus():null===(t=document.getElementById("button-open-settings-navigation-mobile"))||void 0===t||t.focus()})),document.querySelector('[href="#wp-toolbar"]').addEventListener("click",(e=>{var t;e.preventDefault(),null===(t=document.querySelector("#wp-admin-bar-wp-logo a"))||void 0===t||t.focus()})),(()=>{const e=document.getElementById("wpcontent"),t=document.getElementById("adminmenuwrap");e&&t&&(e.style.minHeight=`${t.offsetHeight}px`)})();const m=(0,a.select)(ta).selectPreference("isRtl",!1);(0,i.render)((0,e.createElement)(l.Root,{context:{isRtl:m}},(0,e.createElement)(St.StyleSheetManager,{target:o},(0,e.createElement)(r.SlotFillProvider,null,(0,e.createElement)(gt,null,(0,e.createElement)(K,{initialValues:n,validationSchema:Hi(d,u),onSubmit:Sn},(0,e.createElement)(zl,null)))))),t)}))})()})();