(()=>{var e={2322:e=>{var t,s,n="",r=function(e){e=e||"polite";var t=document.createElement("div");return t.id="a11y-speak-"+e,t.className="a11y-speak-region",t.setAttribute("style","clip: rect(1px, 1px, 1px, 1px); position: absolute; height: 1px; width: 1px; overflow: hidden; word-wrap: normal;"),t.setAttribute("aria-live",e),t.setAttribute("aria-relevant","additions text"),t.setAttribute("aria-atomic","true"),document.querySelector("body").appendChild(t),t};!function(e){if("complete"===document.readyState||"loading"!==document.readyState&&!document.documentElement.doScroll)return e();document.addEventListener("DOMContentLoaded",e)}((function(){t=document.getElementById("a11y-speak-polite"),s=document.getElementById("a11y-speak-assertive"),null===t&&(t=r("polite")),null===s&&(s=r("assertive"))})),e.exports=function(e,r){!function(){for(var e=document.querySelectorAll(".a11y-speak-region"),t=0;t<e.length;t++)e[t].textContent=""}(),e=e.replace(/<[^<>]+>/g," "),n===e&&(e+=" "),n=e,s&&"assertive"===r?s.textContent=e:t&&(t.textContent=e)}},7084:function(e,t,s){!function(t){"use strict";var s={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,fences:/^ {0,3}(`{3,}|~{3,})([^`~\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?:\n+|$)|$)/,hr:/^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6}) +([^\n]*?)(?: +#+)? *(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3})(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,html:"^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?\\?>\\n*|<![A-Z][\\s\\S]*?>\\n*|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>\\n*|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$))",def:/^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/,nptable:y,table:y,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html)[^\n]+)*)/,text:/^[^\n]+/};function n(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||v.defaults,this.rules=s.normal,this.options.pedantic?this.rules=s.pedantic:this.options.gfm&&(this.rules=s.gfm)}s._label=/(?!\s*\])(?:\\[\[\]]|[^\[\]])+/,s._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/,s.def=u(s.def).replace("label",s._label).replace("title",s._title).getRegex(),s.bullet=/(?:[*+-]|\d{1,9}\.)/,s.item=/^( *)(bull) ?[^\n]*(?:\n(?!\1bull ?)[^\n]*)*/,s.item=u(s.item,"gm").replace(/bull/g,s.bullet).getRegex(),s.list=u(s.list).replace(/bull/g,s.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+s.def.source+")").getRegex(),s._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",s._comment=/<!--(?!-?>)[\s\S]*?-->/,s.html=u(s.html,"i").replace("comment",s._comment).replace("tag",s._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),s.paragraph=u(s._paragraph).replace("hr",s.hr).replace("heading"," {0,3}#{1,6} +").replace("|lheading","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}|~{3,})[^`\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",s._tag).getRegex(),s.blockquote=u(s.blockquote).replace("paragraph",s.paragraph).getRegex(),s.normal=f({},s),s.gfm=f({},s.normal,{nptable:/^ *([^|\n ].*\|.*)\n *([-:]+ *\|[-| :]*)(?:\n((?:.*[^>\n ].*(?:\n|$))*)\n*|$)/,table:/^ *\|(.+)\n *\|?( *[-:]+[-| :]*)(?:\n((?: *[^>\n ].*(?:\n|$))*)\n*|$)/}),s.pedantic=f({},s.normal,{html:u("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",s._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *(?:#+ *)?(?:\n+|$)/,fences:y,paragraph:u(s.normal._paragraph).replace("hr",s.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",s.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()}),n.rules=s,n.lex=function(e,t){return new n(t).lex(e)},n.prototype.lex=function(e){return e=e.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,"\n"),this.token(e,!0)},n.prototype.token=function(e,t){var n,r,i,o,a,l,c,p,u,h,g,m,y,f,_,k;for(e=e.replace(/^ +$/gm,"");e;)if((i=this.rules.newline.exec(e))&&(e=e.substring(i[0].length),i[0].length>1&&this.tokens.push({type:"space"})),i=this.rules.code.exec(e)){var v=this.tokens[this.tokens.length-1];e=e.substring(i[0].length),v&&"paragraph"===v.type?v.text+="\n"+i[0].trimRight():(i=i[0].replace(/^ {4}/gm,""),this.tokens.push({type:"code",codeBlockStyle:"indented",text:this.options.pedantic?i:b(i,"\n")}))}else if(i=this.rules.fences.exec(e))e=e.substring(i[0].length),this.tokens.push({type:"code",lang:i[2]?i[2].trim():i[2],text:i[3]||""});else if(i=this.rules.heading.exec(e))e=e.substring(i[0].length),this.tokens.push({type:"heading",depth:i[1].length,text:i[2]});else if((i=this.rules.nptable.exec(e))&&(l={type:"table",header:w(i[1].replace(/^ *| *\| *$/g,"")),align:i[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:i[3]?i[3].replace(/\n$/,"").split("\n"):[]}).header.length===l.align.length){for(e=e.substring(i[0].length),g=0;g<l.align.length;g++)/^ *-+: *$/.test(l.align[g])?l.align[g]="right":/^ *:-+: *$/.test(l.align[g])?l.align[g]="center":/^ *:-+ *$/.test(l.align[g])?l.align[g]="left":l.align[g]=null;for(g=0;g<l.cells.length;g++)l.cells[g]=w(l.cells[g],l.header.length);this.tokens.push(l)}else if(i=this.rules.hr.exec(e))e=e.substring(i[0].length),this.tokens.push({type:"hr"});else if(i=this.rules.blockquote.exec(e))e=e.substring(i[0].length),this.tokens.push({type:"blockquote_start"}),i=i[0].replace(/^ *> ?/gm,""),this.token(i,t),this.tokens.push({type:"blockquote_end"});else if(i=this.rules.list.exec(e)){for(e=e.substring(i[0].length),c={type:"list_start",ordered:f=(o=i[2]).length>1,start:f?+o:"",loose:!1},this.tokens.push(c),p=[],n=!1,y=(i=i[0].match(this.rules.item)).length,g=0;g<y;g++)h=(l=i[g]).length,~(l=l.replace(/^ *([*+-]|\d+\.) */,"")).indexOf("\n ")&&(h-=l.length,l=this.options.pedantic?l.replace(/^ {1,4}/gm,""):l.replace(new RegExp("^ {1,"+h+"}","gm"),"")),g!==y-1&&(a=s.bullet.exec(i[g+1])[0],(o.length>1?1===a.length:a.length>1||this.options.smartLists&&a!==o)&&(e=i.slice(g+1).join("\n")+e,g=y-1)),r=n||/\n\n(?!\s*$)/.test(l),g!==y-1&&(n="\n"===l.charAt(l.length-1),r||(r=n)),r&&(c.loose=!0),k=void 0,(_=/^\[[ xX]\] /.test(l))&&(k=" "!==l[1],l=l.replace(/^\[[ xX]\] +/,"")),u={type:"list_item_start",task:_,checked:k,loose:r},p.push(u),this.tokens.push(u),this.token(l,!1),this.tokens.push({type:"list_item_end"});if(c.loose)for(y=p.length,g=0;g<y;g++)p[g].loose=!0;this.tokens.push({type:"list_end"})}else if(i=this.rules.html.exec(e))e=e.substring(i[0].length),this.tokens.push({type:this.options.sanitize?"paragraph":"html",pre:!this.options.sanitizer&&("pre"===i[1]||"script"===i[1]||"style"===i[1]),text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(i[0]):d(i[0]):i[0]});else if(t&&(i=this.rules.def.exec(e)))e=e.substring(i[0].length),i[3]&&(i[3]=i[3].substring(1,i[3].length-1)),m=i[1].toLowerCase().replace(/\s+/g," "),this.tokens.links[m]||(this.tokens.links[m]={href:i[2],title:i[3]});else if((i=this.rules.table.exec(e))&&(l={type:"table",header:w(i[1].replace(/^ *| *\| *$/g,"")),align:i[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:i[3]?i[3].replace(/\n$/,"").split("\n"):[]}).header.length===l.align.length){for(e=e.substring(i[0].length),g=0;g<l.align.length;g++)/^ *-+: *$/.test(l.align[g])?l.align[g]="right":/^ *:-+: *$/.test(l.align[g])?l.align[g]="center":/^ *:-+ *$/.test(l.align[g])?l.align[g]="left":l.align[g]=null;for(g=0;g<l.cells.length;g++)l.cells[g]=w(l.cells[g].replace(/^ *\| *| *\| *$/g,""),l.header.length);this.tokens.push(l)}else if(i=this.rules.lheading.exec(e))e=e.substring(i[0].length),this.tokens.push({type:"heading",depth:"="===i[2].charAt(0)?1:2,text:i[1]});else if(t&&(i=this.rules.paragraph.exec(e)))e=e.substring(i[0].length),this.tokens.push({type:"paragraph",text:"\n"===i[1].charAt(i[1].length-1)?i[1].slice(0,-1):i[1]});else if(i=this.rules.text.exec(e))e=e.substring(i[0].length),this.tokens.push({type:"text",text:i[0]});else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0));return this.tokens};var r={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:y,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/,nolink:/^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/,strong:/^__([^\s_])__(?!_)|^\*\*([^\s*])\*\*(?!\*)|^__([^\s][\s\S]*?[^\s])__(?!_)|^\*\*([^\s][\s\S]*?[^\s])\*\*(?!\*)/,em:/^_([^\s_])_(?!_)|^\*([^\s*<\[])\*(?!\*)|^_([^\s<][\s\S]*?[^\s_])_(?!_|[^\spunctuation])|^_([^\s_<][\s\S]*?[^\s])_(?!_|[^\spunctuation])|^\*([^\s<"][\s\S]*?[^\s\*])\*(?!\*|[^\spunctuation])|^\*([^\s*"<\[][\s\S]*?[^\s])\*(?!\*)/,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:y,text:/^(`+|[^`])(?:[\s\S]*?(?:(?=[\\<!\[`*]|\b_|$)|[^ ](?= {2,}\n))|(?= {2,}\n))/};function i(e,t){if(this.options=t||v.defaults,this.links=e,this.rules=r.normal,this.renderer=this.options.renderer||new o,this.renderer.options=this.options,!this.links)throw new Error("Tokens array requires a `links` property.");this.options.pedantic?this.rules=r.pedantic:this.options.gfm&&(this.options.breaks?this.rules=r.breaks:this.rules=r.gfm)}function o(e){this.options=e||v.defaults}function a(){}function l(e){this.tokens=[],this.token=null,this.options=e||v.defaults,this.options.renderer=this.options.renderer||new o,this.renderer=this.options.renderer,this.renderer.options=this.options,this.slugger=new c}function c(){this.seen={}}function d(e,t){if(t){if(d.escapeTest.test(e))return e.replace(d.escapeReplace,(function(e){return d.replacements[e]}))}else if(d.escapeTestNoEncode.test(e))return e.replace(d.escapeReplaceNoEncode,(function(e){return d.replacements[e]}));return e}function p(e){return e.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,(function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""}))}function u(e,t){return e=e.source||e,t=t||"",{replace:function(t,s){return s=(s=s.source||s).replace(/(^|[^\[])\^/g,"$1"),e=e.replace(t,s),this},getRegex:function(){return new RegExp(e,t)}}}function h(e,t,s){if(e){try{var n=decodeURIComponent(p(s)).replace(/[^\w:]/g,"").toLowerCase()}catch(e){return null}if(0===n.indexOf("javascript:")||0===n.indexOf("vbscript:")||0===n.indexOf("data:"))return null}t&&!m.test(s)&&(s=function(e,t){return g[" "+e]||(/^[^:]+:\/*[^/]*$/.test(e)?g[" "+e]=e+"/":g[" "+e]=b(e,"/",!0)),e=g[" "+e],"//"===t.slice(0,2)?e.replace(/:[\s\S]*/,":")+t:"/"===t.charAt(0)?e.replace(/(:\/*[^/]*)[\s\S]*/,"$1")+t:e+t}(t,s));try{s=encodeURI(s).replace(/%25/g,"%")}catch(e){return null}return s}r._punctuation="!\"#$%&'()*+,\\-./:;<=>?@\\[^_{|}~",r.em=u(r.em).replace(/punctuation/g,r._punctuation).getRegex(),r._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,r._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,r._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,r.autolink=u(r.autolink).replace("scheme",r._scheme).replace("email",r._email).getRegex(),r._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,r.tag=u(r.tag).replace("comment",s._comment).replace("attribute",r._attribute).getRegex(),r._label=/(?:\[[^\[\]]*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,r._href=/<(?:\\[<>]?|[^\s<>\\])*>|[^\s\x00-\x1f]*/,r._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,r.link=u(r.link).replace("label",r._label).replace("href",r._href).replace("title",r._title).getRegex(),r.reflink=u(r.reflink).replace("label",r._label).getRegex(),r.normal=f({},r),r.pedantic=f({},r.normal,{strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/,link:u(/^!?\[(label)\]\((.*?)\)/).replace("label",r._label).getRegex(),reflink:u(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",r._label).getRegex()}),r.gfm=f({},r.normal,{escape:u(r.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^~+(?=\S)([\s\S]*?\S)~+/,text:/^(`+|[^`])(?:[\s\S]*?(?:(?=[\\<!\[`*~]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))|(?= {2,}\n|[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))/}),r.gfm.url=u(r.gfm.url,"i").replace("email",r.gfm._extended_email).getRegex(),r.breaks=f({},r.gfm,{br:u(r.br).replace("{2,}","*").getRegex(),text:u(r.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),i.rules=r,i.output=function(e,t,s){return new i(t,s).output(e)},i.prototype.output=function(e){for(var t,s,n,r,o,a,l="";e;)if(o=this.rules.escape.exec(e))e=e.substring(o[0].length),l+=d(o[1]);else if(o=this.rules.tag.exec(e))!this.inLink&&/^<a /i.test(o[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(o[0])&&(this.inLink=!1),!this.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(o[0])?this.inRawBlock=!0:this.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(o[0])&&(this.inRawBlock=!1),e=e.substring(o[0].length),l+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(o[0]):d(o[0]):o[0];else if(o=this.rules.link.exec(e)){var c=_(o[2],"()");if(c>-1){var p=4+o[1].length+c;o[2]=o[2].substring(0,c),o[0]=o[0].substring(0,p).trim(),o[3]=""}e=e.substring(o[0].length),this.inLink=!0,n=o[2],this.options.pedantic?(t=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n))?(n=t[1],r=t[3]):r="":r=o[3]?o[3].slice(1,-1):"",n=n.trim().replace(/^<([\s\S]*)>$/,"$1"),l+=this.outputLink(o,{href:i.escapes(n),title:i.escapes(r)}),this.inLink=!1}else if((o=this.rules.reflink.exec(e))||(o=this.rules.nolink.exec(e))){if(e=e.substring(o[0].length),t=(o[2]||o[1]).replace(/\s+/g," "),!(t=this.links[t.toLowerCase()])||!t.href){l+=o[0].charAt(0),e=o[0].substring(1)+e;continue}this.inLink=!0,l+=this.outputLink(o,t),this.inLink=!1}else if(o=this.rules.strong.exec(e))e=e.substring(o[0].length),l+=this.renderer.strong(this.output(o[4]||o[3]||o[2]||o[1]));else if(o=this.rules.em.exec(e))e=e.substring(o[0].length),l+=this.renderer.em(this.output(o[6]||o[5]||o[4]||o[3]||o[2]||o[1]));else if(o=this.rules.code.exec(e))e=e.substring(o[0].length),l+=this.renderer.codespan(d(o[2].trim(),!0));else if(o=this.rules.br.exec(e))e=e.substring(o[0].length),l+=this.renderer.br();else if(o=this.rules.del.exec(e))e=e.substring(o[0].length),l+=this.renderer.del(this.output(o[1]));else if(o=this.rules.autolink.exec(e))e=e.substring(o[0].length),n="@"===o[2]?"mailto:"+(s=d(this.mangle(o[1]))):s=d(o[1]),l+=this.renderer.link(n,null,s);else if(this.inLink||!(o=this.rules.url.exec(e))){if(o=this.rules.text.exec(e))e=e.substring(o[0].length),this.inRawBlock?l+=this.renderer.text(this.options.sanitize?this.options.sanitizer?this.options.sanitizer(o[0]):d(o[0]):o[0]):l+=this.renderer.text(d(this.smartypants(o[0])));else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0))}else{if("@"===o[2])n="mailto:"+(s=d(o[0]));else{do{a=o[0],o[0]=this.rules._backpedal.exec(o[0])[0]}while(a!==o[0]);s=d(o[0]),n="www."===o[1]?"http://"+s:s}e=e.substring(o[0].length),l+=this.renderer.link(n,null,s)}return l},i.escapes=function(e){return e?e.replace(i.rules._escapes,"$1"):e},i.prototype.outputLink=function(e,t){var s=t.href,n=t.title?d(t.title):null;return"!"!==e[0].charAt(0)?this.renderer.link(s,n,this.output(e[1])):this.renderer.image(s,n,d(e[1]))},i.prototype.smartypants=function(e){return this.options.smartypants?e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…"):e},i.prototype.mangle=function(e){if(!this.options.mangle)return e;for(var t,s="",n=e.length,r=0;r<n;r++)t=e.charCodeAt(r),Math.random()>.5&&(t="x"+t.toString(16)),s+="&#"+t+";";return s},o.prototype.code=function(e,t,s){var n=(t||"").match(/\S*/)[0];if(this.options.highlight){var r=this.options.highlight(e,n);null!=r&&r!==e&&(s=!0,e=r)}return n?'<pre><code class="'+this.options.langPrefix+d(n,!0)+'">'+(s?e:d(e,!0))+"</code></pre>\n":"<pre><code>"+(s?e:d(e,!0))+"</code></pre>"},o.prototype.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},o.prototype.html=function(e){return e},o.prototype.heading=function(e,t,s,n){return this.options.headerIds?"<h"+t+' id="'+this.options.headerPrefix+n.slug(s)+'">'+e+"</h"+t+">\n":"<h"+t+">"+e+"</h"+t+">\n"},o.prototype.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},o.prototype.list=function(e,t,s){var n=t?"ol":"ul";return"<"+n+(t&&1!==s?' start="'+s+'"':"")+">\n"+e+"</"+n+">\n"},o.prototype.listitem=function(e){return"<li>"+e+"</li>\n"},o.prototype.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},o.prototype.paragraph=function(e){return"<p>"+e+"</p>\n"},o.prototype.table=function(e,t){return t&&(t="<tbody>"+t+"</tbody>"),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"},o.prototype.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},o.prototype.tablecell=function(e,t){var s=t.header?"th":"td";return(t.align?"<"+s+' align="'+t.align+'">':"<"+s+">")+e+"</"+s+">\n"},o.prototype.strong=function(e){return"<strong>"+e+"</strong>"},o.prototype.em=function(e){return"<em>"+e+"</em>"},o.prototype.codespan=function(e){return"<code>"+e+"</code>"},o.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},o.prototype.del=function(e){return"<del>"+e+"</del>"},o.prototype.link=function(e,t,s){if(null===(e=h(this.options.sanitize,this.options.baseUrl,e)))return s;var n='<a href="'+d(e)+'"';return t&&(n+=' title="'+t+'"'),n+">"+s+"</a>"},o.prototype.image=function(e,t,s){if(null===(e=h(this.options.sanitize,this.options.baseUrl,e)))return s;var n='<img src="'+e+'" alt="'+s+'"';return t&&(n+=' title="'+t+'"'),n+(this.options.xhtml?"/>":">")},o.prototype.text=function(e){return e},a.prototype.strong=a.prototype.em=a.prototype.codespan=a.prototype.del=a.prototype.text=function(e){return e},a.prototype.link=a.prototype.image=function(e,t,s){return""+s},a.prototype.br=function(){return""},l.parse=function(e,t){return new l(t).parse(e)},l.prototype.parse=function(e){this.inline=new i(e.links,this.options),this.inlineText=new i(e.links,f({},this.options,{renderer:new a})),this.tokens=e.reverse();for(var t="";this.next();)t+=this.tok();return t},l.prototype.next=function(){return this.token=this.tokens.pop(),this.token},l.prototype.peek=function(){return this.tokens[this.tokens.length-1]||0},l.prototype.parseText=function(){for(var e=this.token.text;"text"===this.peek().type;)e+="\n"+this.next().text;return this.inline.output(e)},l.prototype.tok=function(){switch(this.token.type){case"space":return"";case"hr":return this.renderer.hr();case"heading":return this.renderer.heading(this.inline.output(this.token.text),this.token.depth,p(this.inlineText.output(this.token.text)),this.slugger);case"code":return this.renderer.code(this.token.text,this.token.lang,this.token.escaped);case"table":var e,t,s,n,r="",i="";for(s="",e=0;e<this.token.header.length;e++)s+=this.renderer.tablecell(this.inline.output(this.token.header[e]),{header:!0,align:this.token.align[e]});for(r+=this.renderer.tablerow(s),e=0;e<this.token.cells.length;e++){for(t=this.token.cells[e],s="",n=0;n<t.length;n++)s+=this.renderer.tablecell(this.inline.output(t[n]),{header:!1,align:this.token.align[n]});i+=this.renderer.tablerow(s)}return this.renderer.table(r,i);case"blockquote_start":for(i="";"blockquote_end"!==this.next().type;)i+=this.tok();return this.renderer.blockquote(i);case"list_start":i="";for(var o=this.token.ordered,a=this.token.start;"list_end"!==this.next().type;)i+=this.tok();return this.renderer.list(i,o,a);case"list_item_start":i="";var l=this.token.loose,c=this.token.checked,d=this.token.task;for(this.token.task&&(i+=this.renderer.checkbox(c));"list_item_end"!==this.next().type;)i+=l||"text"!==this.token.type?this.tok():this.parseText();return this.renderer.listitem(i,d,c);case"html":return this.renderer.html(this.token.text);case"paragraph":return this.renderer.paragraph(this.inline.output(this.token.text));case"text":return this.renderer.paragraph(this.parseText());default:var u='Token with "'+this.token.type+'" type was not found.';if(!this.options.silent)throw new Error(u);console.log(u)}},c.prototype.slug=function(e){var t=e.toLowerCase().trim().replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-");if(this.seen.hasOwnProperty(t)){var s=t;do{this.seen[s]++,t=s+"-"+this.seen[s]}while(this.seen.hasOwnProperty(t))}return this.seen[t]=0,t},d.escapeTest=/[&<>"']/,d.escapeReplace=/[&<>"']/g,d.replacements={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},d.escapeTestNoEncode=/[<>"']|&(?!#?\w+;)/,d.escapeReplaceNoEncode=/[<>"']|&(?!#?\w+;)/g;var g={},m=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function y(){}function f(e){for(var t,s,n=1;n<arguments.length;n++)for(s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}function w(e,t){var s=e.replace(/\|/g,(function(e,t,s){for(var n=!1,r=t;--r>=0&&"\\"===s[r];)n=!n;return n?"|":" |"})).split(/ \|/),n=0;if(s.length>t)s.splice(t);else for(;s.length<t;)s.push("");for(;n<s.length;n++)s[n]=s[n].trim().replace(/\\\|/g,"|");return s}function b(e,t,s){if(0===e.length)return"";for(var n=0;n<e.length;){var r=e.charAt(e.length-n-1);if(r!==t||s){if(r===t||!s)break;n++}else n++}return e.substr(0,e.length-n)}function _(e,t){if(-1===e.indexOf(t[1]))return-1;for(var s=0,n=0;n<e.length;n++)if("\\"===e[n])n++;else if(e[n]===t[0])s++;else if(e[n]===t[1]&&--s<0)return n;return-1}function k(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function v(e,t,s){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if(s||"function"==typeof t){s||(s=t,t=null),k(t=f({},v.defaults,t||{}));var r,i,o=t.highlight,a=0;try{r=n.lex(e,t)}catch(e){return s(e)}i=r.length;var c=function(e){if(e)return t.highlight=o,s(e);var n;try{n=l.parse(r,t)}catch(t){e=t}return t.highlight=o,e?s(e):s(null,n)};if(!o||o.length<3)return c();if(delete t.highlight,!i)return c();for(;a<r.length;a++)!function(e){"code"!==e.type?--i||c():o(e.text,e.lang,(function(t,s){return t?c(t):null==s||s===e.text?--i||c():(e.text=s,e.escaped=!0,void(--i||c()))}))}(r[a])}else try{return t&&(t=f({},v.defaults,t)),k(t),l.parse(n.lex(e,t),t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",(t||v.defaults).silent)return"<p>An error occurred:</p><pre>"+d(e.message+"",!0)+"</pre>";throw e}}y.exec=y,v.options=v.setOptions=function(e){return f(v.defaults,e),v},v.getDefaults=function(){return{baseUrl:null,breaks:!1,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:new o,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,xhtml:!1}},v.defaults=v.getDefaults(),v.Parser=l,v.parser=l.parse,v.Renderer=o,v.TextRenderer=a,v.Lexer=n,v.lexer=n.lex,v.InlineLexer=i,v.inlineLexer=i.output,v.Slugger=c,v.parse=v,e.exports=v}(this||("undefined"!=typeof window?window:s.g))}},t={};function s(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,s),i.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};s.r(e),s.d(e,{DISMISS_ALERT:()=>Be,NEW_REQUEST:()=>Fe,SNIPPET_EDITOR_FIND_CUSTOM_FIELDS:()=>De,wistiaEmbedPermission:()=>$e});var t={};s.r(t),s.d(t,{addEventHandler:()=>Ot,disableMarkerButtons:()=>At,enableMarkerButtons:()=>It,getContentTinyMce:()=>Pt,isTextViewActive:()=>Mt,isTinyMCEAvailable:()=>Tt,isTinyMCELoaded:()=>Ct,pauseMarkers:()=>Lt,resumeMarkers:()=>Nt,setStore:()=>Rt,termsTmceId:()=>St,tinyMceEventBinder:()=>Dt,tmceId:()=>Et,wpTextViewOnInitCheck:()=>Bt});var n={};s.r(n),s.d(n,{createSEOScoreLabel:()=>zt,createScoresInPublishBox:()=>Vt,initialize:()=>Wt,scrollToCollapsible:()=>Kt,updateScore:()=>Yt});const r=window.wp.domReady;var i=s.n(r);const o=window.jQuery;var a=s.n(o);const l=window.lodash,c=window.React,d=window.wp.components,p=window.wp.data,u=window.wp.element,h=window.wp.hooks,g=window.wp.i18n,m=window.yoast.uiLibrary,y=window.yoast.propTypes;var f=s.n(y);const w=(e,t)=>{try{return(0,u.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}};f().string.isRequired;const b=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),k=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),v=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:n,isProductCopy:r,title:i,upsellLabel:o,newToText:a,bundleNote:l,ctbId:d})=>{const{onClose:p,initialFocus:u}=(0,m.useModalContext)(),h={a:(0,c.createElement)(U,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,c.createElement)(k,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,c.createElement)("div",{className:"yst-relative yst-w-full"},(0,c.createElement)(he,{videoId:"vmrahpfjxp",thumbnail:t,wistiaEmbedPermission:s}),(0,c.createElement)(m.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,c.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,c.createElement)("span",{className:"yst-logo-icon"}),a))),(0,c.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,c.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},i),(0,c.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},w(r?(0,g.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,g.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,g.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,g.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,c.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,c.createElement)(m.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:u,"data-action":"load-nfd-ctb","data-ctb-id":d},(0,c.createElement)(b,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),o,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")))),l,(0,c.createElement)(m.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,g.__)("Close","wordpress-seo"))))};v.propTypes={learnMoreLink:f().string.isRequired,upsellLink:f().string.isRequired,thumbnail:f().shape({src:f().string.isRequired,width:f().string,height:f().string}).isRequired,wistiaEmbedPermission:f().shape({value:f().bool.isRequired,status:f().string.isRequired,set:f().func.isRequired}).isRequired,title:f().string,upsellLabel:f().string,newToText:f().string,isProductCopy:f().bool,bundleNote:f().oneOfType([f().string,f().element]),ctbId:f().string},v.defaultProps={title:(0,g.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const x=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:n,upsellLabel:r,newToText:i,bundleNote:o,ctbId:a})=>{const{onClose:l,initialFocus:d}=(0,m.useModalContext)(),p={a:(0,c.createElement)(U,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,c.createElement)(k,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,c.createElement)("br",null)};return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,c.createElement)("div",{className:"yst-relative yst-w-full"},(0,c.createElement)(he,{videoId:"vun9z1dpfh",thumbnail:t,wistiaEmbedPermission:s}),(0,c.createElement)(m.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,g.__)("Beta","wordpress-seo-premium"))),(0,c.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,c.createElement)("span",{className:"yst-logo-icon"}),i))),(0,c.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,c.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,c.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,g.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,g.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,c.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},w((0,g.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,g.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),p))),(0,c.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,c.createElement)(m.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:d,"data-action":"load-nfd-ctb","data-ctb-id":a},(0,c.createElement)(b,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),r,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")))),o,(0,c.createElement)(m.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:l},(0,g.__)("Close","wordpress-seo"))))};x.propTypes={learnMoreLink:f().string.isRequired,upsellLink:f().string.isRequired,thumbnail:f().shape({src:f().string.isRequired,width:f().string,height:f().string}).isRequired,wistiaEmbedPermission:f().shape({value:f().bool.isRequired,status:f().string.isRequired,set:f().func.isRequired}).isRequired,upsellLabel:f().string,newToText:f().string,bundleNote:f().oneOfType([f().string,f().element]),ctbId:f().string},x.defaultProps={upsellLabel:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const E=({handleRefreshClick:e,supportLink:t})=>(0,c.createElement)("div",{className:"yst-flex yst-gap-2"},(0,c.createElement)(m.Button,{onClick:e},(0,g.__)("Refresh this page","wordpress-seo")),(0,c.createElement)(m.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,g.__)("Contact support","wordpress-seo")));E.propTypes={handleRefreshClick:f().func.isRequired,supportLink:f().string.isRequired};const S=({handleRefreshClick:e,supportLink:t})=>(0,c.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,c.createElement)(m.Button,{className:"yst-order-last",onClick:e},(0,g.__)("Refresh this page","wordpress-seo")),(0,c.createElement)(m.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,g.__)("Contact support","wordpress-seo")));S.propTypes={handleRefreshClick:f().func.isRequired,supportLink:f().string.isRequired};const R=({error:e,children:t})=>(0,c.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,c.createElement)(m.Title,null,(0,g.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,c.createElement)("p",null,(0,g.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,c.createElement)(m.Alert,{variant:"error"},(null==e?void 0:e.message)||(0,g.__)("Undefined error message.","wordpress-seo")),(0,c.createElement)("p",null,(0,g.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),t);R.propTypes={error:f().object.isRequired,children:f().node},R.VerticalButtons=S,R.HorizontalButtons=E;var C;function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},T.apply(this,arguments)}f().string,f().node.isRequired,f().node.isRequired,f().node,f().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const P=e=>c.createElement("svg",T({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),C||(C=c.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),O=window.ReactDOM;var A,I,L;(I=A||(A={})).Pop="POP",I.Push="PUSH",I.Replace="REPLACE",function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(L||(L={})),new Set(["lazy","caseSensitive","path","id","index","children"]),Error;const N=["post","put","patch","delete"],M=(new Set(N),["get",...N]);new Set(M),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred"),c.Component,c.startTransition,new Promise((()=>{})),c.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}var B,D,F,$;new Map,c.startTransition,O.flushSync,c.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,($=B||(B={})).UseScrollRestoration="useScrollRestoration",$.UseSubmit="useSubmit",$.UseSubmitFetcher="useSubmitFetcher",$.UseFetcher="useFetcher",$.useViewTransitionState="useViewTransitionState",(F=D||(D={})).UseFetcher="useFetcher",F.UseFetchers="useFetchers",F.UseScrollRestoration="useScrollRestoration",f().string.isRequired,f().string;const U=({href:e,children:t,...s})=>(0,c.createElement)(m.Link,{target:"_blank",rel:"noopener noreferrer",...s,href:e},t,(0,c.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")));U.propTypes={href:f().string.isRequired,children:f().node},U.defaultProps={children:null};const q=c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var j,z,Y;function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},V.apply(this,arguments)}const K=e=>c.createElement("svg",V({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),j||(j=c.createElement("defs",null,c.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),z||(z=c.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),Y||(Y=c.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),c.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},W.apply(this,arguments)}const H=e=>c.createElement("svg",W({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),c.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var Q,Z,G,J,X,ee,te,se,ne;function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},re.apply(this,arguments)}const ie=e=>c.createElement("svg",re({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),Q||(Q=c.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),Z||(Z=c.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),G||(G=c.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),J||(J=c.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),X||(X=c.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),ee||(ee=c.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),te||(te=c.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),se||(se=c.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),ne||(ne=c.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),oe=({link:e,linkProps:t,isPromotionActive:s})=>{let n=(0,u.useMemo)((()=>(0,g.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),r=w((0,g.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,g.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,c.createElement)("span",{className:"yst-whitespace-nowrap"})});const i=s("black-friday-2024-promotion");return i&&(n=(0,u.useMemo)((()=>(0,g.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),r=w((0,g.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,g.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,c.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,c.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,c.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,c.createElement)(ie,null)),i&&(0,c.createElement)("div",{className:"sidebar__sale_banner_container"},(0,c.createElement)("div",{className:"sidebar__sale_banner"},(0,c.createElement)("span",{className:"banner_text"},(0,g.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,c.createElement)(m.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},r),(0,c.createElement)("p",{className:"yst-mt-2"},n),(0,c.createElement)(m.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...t},(0,c.createElement)("span",null,i?(0,g.__)("Buy now","wordpress-seo"):r),(0,c.createElement)(q,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,c.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,g.__)("30-day money back guarantee.","wordpress-seo")),(0,c.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,c.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,c.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,g.__)("Read reviews from real users","wordpress-seo")),(0,c.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,c.createElement)(P,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)("span",{className:"yst-flex yst-gap-1"},(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(H,{className:"yst-w-5 yst-h-5"}),(0,c.createElement)(K,{className:"yst-w-5 yst-h-5"})),(0,c.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};oe.propTypes={link:f().string.isRequired,linkProps:f().object,isPromotionActive:f().func},oe.defaultProps={linkProps:{},isPromotionActive:l.noop};const ae=({premiumLink:e,premiumUpsellConfig:t,isPromotionActive:s})=>{const n=s("black-friday-2024-promotion");return(0,c.createElement)(m.Paper,{as:"div",className:"xl:yst-max-w-3xl"},n&&(0,c.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,c.createElement)("div",null,(0,g.__)("30% OFF","wordpress-seo")),(0,c.createElement)("div",null,(0,g.__)("BLACK FRIDAY","wordpress-seo"))),(0,c.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,c.createElement)(m.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,g.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,g.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,c.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},[(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")].map(((e,t)=>(0,c.createElement)("li",{key:`upsell-benefit-${t}`},w(e,{strong:(0,c.createElement)("span",{className:"yst-font-semibold"})}))))),(0,c.createElement)(m.Button,{as:"a",variant:"upsell",size:"extra-large",href:e,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...t},n?(0,g.__)("Claim your 30% off now!","wordpress-seo"):(0,g.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,g.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,c.createElement)(q,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};ae.propTypes={premiumLink:f().string.isRequired,premiumUpsellConfig:f().object,isPromotionActive:f().func},ae.defaultProps={premiumUpsellConfig:{},isPromotionActive:l.noop},f().string.isRequired,f().object.isRequired,f().string.isRequired,f().func.isRequired,c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),f().bool.isRequired,f().func,f().func,f().string.isRequired,f().string.isRequired,f().string.isRequired,f().string.isRequired;const le=window.yoast.reactHelmet,ce="loading",de="showPlay",pe="askPermission",ue="isPlaying",he=({videoId:e,thumbnail:t,wistiaEmbedPermission:s})=>{const[n,r]=(0,u.useState)(s.value?ue:de),i=(0,u.useCallback)((()=>r(ue)),[r]),o=(0,u.useCallback)((()=>{s.value?i():r(pe)}),[s.value,i,r]),a=(0,u.useCallback)((()=>r(de)),[r]),l=(0,u.useCallback)((()=>{s.set(!0),i()}),[s.set,i]);return(0,c.createElement)(c.Fragment,null,s.value&&(0,c.createElement)(le.Helmet,null,(0,c.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,c.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},n===de&&(0,c.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:o},(0,c.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...t})),n===pe&&(0,c.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,c.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},s.status===ce&&(0,c.createElement)(m.Spinner,null),s.status!==ce&&(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,g.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,c.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,c.createElement)(m.Button,{type:"button",variant:"secondary",onClick:a,disabled:s.status===ce},(0,g.__)("Deny","wordpress-seo")),(0,c.createElement)(m.Button,{type:"button",variant:"primary",onClick:l,disabled:s.status===ce},(0,g.__)("Allow","wordpress-seo")))),s.value&&n===ue&&(0,c.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===e&&(0,c.createElement)(m.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,c.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};he.propTypes={videoId:f().string.isRequired,thumbnail:f().shape({src:f().string.isRequired,width:f().string,height:f().string}).isRequired,wistiaEmbedPermission:f().shape({value:f().bool.isRequired,status:f().string.isRequired,set:f().func.isRequired}).isRequired},c.forwardRef((function(e,t){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),c.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),f().bool.isRequired,f().func.isRequired,f().func,f().string;const ge="yoast-seo/editor",me=()=>{const e=(e=>{const t=(0,p.useSelect)((e=>e(ge).getIsPremium()),[]),s=(0,p.useSelect)((e=>e(ge).getIsWooSeoActive()),[]),n=(0,p.useSelect)((e=>e(ge).getIsWooCommerceActive()),[]),r=(0,p.useSelect)((e=>e(ge).getIsProduct()),[]),i=(0,p.useSelect)((e=>e(ge).getIsProductTerm()),[]),o={upsellLink:e.premium};if(n&&(r||i)){const n=(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,g.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");o.newToText=(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,g.__)("New in %1$s","wordpress-seo"),n),t?(o.upsellLabel=(0,g.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),o.upsellLink=e.woo,o.ctbId="5b32250e-e6f0-44ae-ad74-3cefc8e427f9"):s||(o.upsellLabel=`${(0,g.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,g.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,o.bundleNote=(0,c.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${n}`),o.upsellLink=e.bundle,o.ctbId="c7e7baa1-2020-420c-a427-89701700b607")}return o})({premium:(0,p.useSelect)((e=>e(ge).selectLink("https://yoa.st/ai-generator-upsell")),[]),bundle:(0,p.useSelect)((e=>e(ge).selectLink("https://yoa.st/ai-generator-upsell-woo-seo-premium-bundle")),[]),woo:(0,p.useSelect)((e=>e(ge).selectLink("https://yoa.st/ai-generator-upsell-woo-seo")),[])}),t=(0,p.useSelect)((e=>e(ge).getIsWooCommerceActive()),[]),s=(0,p.useSelect)((e=>e(ge).getIsProduct()),[]);t&&s&&(e.title=(0,g.__)("Generate product titles & descriptions with AI!","wordpress-seo"),e.isProductCopy=!0);const n=(0,p.useSelect)((e=>e(ge).selectLink("https://yoa.st/ai-generator-learn-more")),[]),r=(0,p.useSelect)((e=>e(ge).selectImageLink("ai-generator-preview.png")),[]),i=(0,u.useMemo)((()=>({src:r,width:"432",height:"244"})),[r]),o=(0,p.useSelect)((e=>e(ge).selectWistiaEmbedPermissionValue()),[]),a=(0,p.useSelect)((e=>e(ge).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:l}=(0,p.useDispatch)(ge),d=(0,u.useMemo)((()=>({value:o,status:a,set:l})),[o,a,l]);return(0,c.createElement)(v,{learnMoreLink:n,thumbnail:i,wistiaEmbedPermission:d,...e})},ye=({fieldId:e})=>{const[t,,,s,n]=(0,m.useToggleState)(!1),r=(0,u.useCallback)((()=>{s()}),[s]),i=(0,u.useRef)(null);return(0,c.createElement)(c.Fragment,null,(0,c.createElement)("button",{type:"button",id:`yst-replacevar__use-ai-button__${e}`,className:"yst-replacevar__use-ai-button-upsell",onClick:r},(0,g.__)("Use AI","wordpress-seo")),(0,c.createElement)(m.Modal,{className:"yst-introduction-modal",isOpen:t,onClose:n,initialFocus:i},(0,c.createElement)(m.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl"},(0,c.createElement)(me,{onClose:n,focusElementRef:i}))))};ye.propTypes={fieldId:f().string.isRequired};const fe="yoast-seo/editor";const we=window.yoast.externals.redux,be=window.yoast.reduxJsToolkit,_e="adminUrl",ke=(0,be.createSlice)({name:_e,initialState:"",reducers:{setAdminUrl:(e,{payload:t})=>t}}),ve=(ke.getInitialState,{selectAdminUrl:e=>(0,l.get)(e,_e,"")});ve.selectAdminLink=(0,be.createSelector)([ve.selectAdminUrl,(e,t)=>t],((e,t="")=>{try{return new URL(t,e).href}catch(t){return e}})),ke.actions,ke.reducer;const xe=window.wp.url,Ee="linkParams",Se=(0,be.createSlice)({name:Ee,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),Re=(Se.getInitialState,{selectLinkParam:(e,t,s={})=>(0,l.get)(e,`${Ee}.${t}`,s),selectLinkParams:e=>(0,l.get)(e,Ee,{})});Re.selectLink=(0,be.createSelector)([Re.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,xe.addQueryArgs)(t,{...e,...s}))),Se.actions,Se.reducer;const Ce=(0,be.createSlice)({name:"notifications",initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:n,description:r})=>({payload:{id:e||(0,be.nanoid)(),variant:t,size:s,title:n||"",description:r}})},removeNotification:(e,{payload:t})=>(0,l.omit)(e,t)}}),Te=(Ce.getInitialState,Ce.actions,Ce.reducer,"pluginUrl"),Pe=(0,be.createSlice)({name:Te,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),Oe=(Pe.getInitialState,{selectPluginUrl:e=>(0,l.get)(e,Te,"")});Oe.selectImageLink=(0,be.createSelector)([Oe.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,l.trimEnd)(e,"/"),(0,l.trim)(t,"/"),(0,l.trimStart)(s,"/")].join("/"))),Pe.actions,Pe.reducer;const Ae=window.wp.apiFetch;var Ie=s.n(Ae);const Le="wistiaEmbedPermission",Ne=(0,be.createSlice)({name:Le,initialState:{value:!1,status:"idle",error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${Le}/request`,(e=>{e.status=ce})),e.addCase(`${Le}/success`,((e,{payload:t})=>{e.status="success",e.value=Boolean(t&&t.value)})),e.addCase(`${Le}/error`,((e,{payload:t})=>{e.status="error",e.value=Boolean(t&&t.value),e.error={code:(0,l.get)(t,"error.code",500),message:(0,l.get)(t,"error.message","Unknown")}}))}}),Me=(Ne.getInitialState,Ne.actions,{[Le]:async({payload:e})=>Ie()({path:"/yoast/v1/wistia_embed_permission",method:"POST",data:{value:Boolean(e)}})});function Be({alertKey:e}){return new Promise((t=>wpseoApi.post("alerts/dismiss",{key:e},(()=>t()))))}function De({query:e,postId:t}){return new Promise((s=>{wpseoApi.get("meta/search",{query:e,post_id:t},(e=>{s(e.meta)}))}))}Ne.reducer;const Fe=async({countryCode:e,keyphrase:t})=>(Ie()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}}),Ie()({path:(0,xe.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:t,country_code:e})})),$e=Me[Le];var Ue=s(2322),qe=s.n(Ue);function je(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor}const ze=window.yoast.analysis,Ye=window.wp.isShallowEqual,Ve=window.wp.api;var Ke={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},We=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,l.defaults)(s,Ke)};We.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},We.prototype.setSource=function(e){this.options.source=e},We.prototype.hasScope=function(){return!(0,l.isEmpty)(this.options.scope)},We.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},We.prototype.inScope=function(e){return!this.hasScope()||(0,l.indexOf)(this.options.scope,e)>-1},We.prototype.hasAlias=function(){return!(0,l.isEmpty)(this.options.aliases)},We.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},We.prototype.getAliases=function(){return this.options.aliases};const He=We,{removeReplacementVariable:Qe,updateReplacementVariable:Ze,refreshSnippetEditor:Ge}=we.actions;var Je=["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc","excerpt"],Xe={},et={},tt=function(e,t){this._app=e,this._app.registerPlugin("replaceVariablePlugin",{status:"ready"}),this._store=t,this.replaceVariables=this.replaceVariables.bind(this),this.registerReplacements(),this.registerModifications(),this.registerEvents(),this.subscribeToGutenberg()};tt.prototype.registerReplacements=function(){this.addReplacement(new He("%%author_first_name%%","author_first_name")),this.addReplacement(new He("%%author_last_name%%","author_last_name")),this.addReplacement(new He("%%category%%","category")),this.addReplacement(new He("%%category_title%%","category_title")),this.addReplacement(new He("%%currentdate%%","currentdate")),this.addReplacement(new He("%%currentday%%","currentday")),this.addReplacement(new He("%%currentmonth%%","currentmonth")),this.addReplacement(new He("%%currenttime%%","currenttime")),this.addReplacement(new He("%%currentyear%%","currentyear")),this.addReplacement(new He("%%date%%","date")),this.addReplacement(new He("%%id%%","id")),this.addReplacement(new He("%%page%%","page")),this.addReplacement(new He("%%permalink%%","permalink")),this.addReplacement(new He("%%post_content%%","post_content")),this.addReplacement(new He("%%post_month%%","post_month")),this.addReplacement(new He("%%post_year%%","post_year")),this.addReplacement(new He("%%searchphrase%%","searchphrase")),this.addReplacement(new He("%%sitedesc%%","sitedesc")),this.addReplacement(new He("%%sitename%%","sitename")),this.addReplacement(new He("%%userid%%","userid")),this.addReplacement(new He("%%focuskw%%","keyword",{source:"app",aliases:["%%keyword%%"]})),this.addReplacement(new He("%%term_description%%","text",{source:"app",scope:["term","category","tag"],aliases:["%%tag_description%%","%%category_description%%"]})),this.addReplacement(new He("%%term_title%%","term_title",{scope:["term"]})),this.addReplacement(new He("%%term_hierarchy%%","term_hierarchy",{scope:["term"]})),this.addReplacement(new He("%%title%%","title",{source:"app",scope:["post","term","page"]})),this.addReplacement(new He("%%parent_title%%","title",{source:"app",scope:["page","category"]})),this.addReplacement(new He("%%excerpt%%","excerpt",{source:"app",scope:["post"],aliases:["%%excerpt_only%%"]})),this.addReplacement(new He("%%primary_category%%","primaryCategory",{source:"app",scope:["post"]})),this.addReplacement(new He("%%sep%%(\\s*%%sep%%)*","sep"))},tt.prototype.registerEvents=function(){const e=wpseoScriptData.analysis.plugins.replaceVars.scope;"post"===e&&jQuery(".categorydiv").each(this.bindTaxonomyEvents.bind(this)),"post"!==e&&"page"!==e||jQuery("#postcustomstuff > #list-table").each(this.bindFieldEvents.bind(this))},tt.prototype.subscribeToGutenberg=function(){if(!je())return;const e={0:""};let t=null;const s=wp.data;s.subscribe((()=>{const n=s.select("core/editor").getEditedPostAttribute("parent");if(void 0!==n&&t!==n)return t=n,n<1?(this._currentParentPageTitle="",void this.declareReloaded()):(0,l.isUndefined)(e[n])?void Ve.loadPromise.done((()=>{new Ve.models.Page({id:n}).fetch().then((t=>{this._currentParentPageTitle=t.title.rendered,e[n]=this._currentParentPageTitle,this.declareReloaded()})).fail((()=>{this._currentParentPageTitle="",this.declareReloaded()}))})):(this._currentParentPageTitle=e[n],void this.declareReloaded())}))},tt.prototype.addReplacement=function(e){Xe[e.placeholder]=e},tt.prototype.removeReplacement=function(e){delete Xe[e.getPlaceholder()]},tt.prototype.registerModifications=function(){var e=this.replaceVariables.bind(this);(0,l.forEach)(Je,function(t){this._app.registerModification(t,e,"replaceVariablePlugin",10)}.bind(this))},tt.prototype.replaceVariables=function(e){return(0,l.isUndefined)(e)||(e=this.parentReplace(e),e=this.replaceCustomTaxonomy(e),e=this.replaceByStore(e),e=this.replacePlaceholders(e)),e},tt.prototype.replaceByStore=function(e){const t=this._store.getState().snippetEditor.replacementVariables;return(0,l.forEach)(t,(t=>{""!==t.value&&(e=e.replace("%%"+t.name+"%%",t.value))})),e},tt.prototype.getReplacementSource=function(e){return"app"===e.source?this._app.rawData:"direct"===e.source?"direct":wpseoScriptData.analysis.plugins.replaceVars.replace_vars},tt.prototype.getReplacement=function(e){var t=this.getReplacementSource(e.options);return!1===e.inScope(wpseoScriptData.analysis.plugins.replaceVars.scope)?"":"direct"===t?e.replacement:t[e.replacement]||""},tt.prototype.replacePlaceholders=function(e){return(0,l.forEach)(Xe,function(t){e=e.replace(new RegExp(t.getPlaceholder(!0),"g"),this.getReplacement(t))}.bind(this)),e},tt.prototype.declareReloaded=function(){this._app.pluginReloaded("replaceVariablePlugin"),this._store.dispatch(Ge())},tt.prototype.getCategoryName=function(e){var t=e.parent("label").clone();return t.children().remove(),t.text().trim()},tt.prototype.parseTaxonomies=function(e,t){(0,l.isUndefined)(et[t])&&(et[t]={});const s=[];(0,l.forEach)(e,function(e){const n=(e=jQuery(e)).val(),r=this.getCategoryName(e),i=e.prop("checked");et[t][n]={label:r,checked:i},i&&-1===s.indexOf(r)&&s.push(r)}.bind(this)),"category"!==t&&(t="ct_"+t),this._store.dispatch(Ze(t,s.join(", ")))},tt.prototype.getAvailableTaxonomies=function(e){var t=jQuery(e).find("input[type=checkbox]"),s=jQuery(e).attr("id").replace("taxonomy-","");t.length>0&&this.parseTaxonomies(t,s),this.declareReloaded()},tt.prototype.bindTaxonomyEvents=function(e,t){(t=jQuery(t)).on("wpListAddEnd",".categorychecklist",this.getAvailableTaxonomies.bind(this,t)),t.on("change","input[type=checkbox]",this.getAvailableTaxonomies.bind(this,t)),this.getAvailableTaxonomies(t)},tt.prototype.replaceCustomTaxonomy=function(e){return(0,l.forEach)(et,function(t,s){var n="%%ct_"+s+"%%";"category"===s&&(n="%%"+s+"%%"),e=e.replace(n,this.getTaxonomyReplaceVar(s))}.bind(this)),e},tt.prototype.getTaxonomyReplaceVar=function(e){var t=[],s=et[e];return!0===(0,l.isUndefined)(s)?"":((0,l.forEach)(s,(function(e){!1!==e.checked&&t.push(e.label)})),jQuery.uniqueSort(t).join(", "))},tt.prototype.parseFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val(),n=jQuery("#"+t.id+"-value").val();const r="cf_"+this.sanitizeCustomFieldNames(s),i=s+" (custom field)";this._store.dispatch(Ze(r,n,i)),this.addReplacement(new He(`%%${r}%%`,n,{source:"direct"}))}.bind(this))},tt.prototype.removeFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val();this.removeReplacement("%%cf_"+this.sanitizeCustomFieldNames(s)+"%%")}.bind(this))},tt.prototype.sanitizeCustomFieldNames=function(e){return e.replace(/\s/g,"_")},tt.prototype.getAvailableFields=function(e){this.removeCustomFields();var t=jQuery(e).find("#the-list > tr:visible[id]");t.length>0&&this.parseFields(t),this.declareReloaded()},tt.prototype.bindFieldEvents=function(e,t){var s=(t=jQuery(t)).find("#the-list");s.on("wpListDelEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("wpListAddEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("input.wpseoCustomFields",".textarea",this.getAvailableFields.bind(this,t)),s.on("click.wpseoCustomFields",".button + .updatemeta",this.getAvailableFields.bind(this,t)),this.getAvailableFields(t)},tt.prototype.removeCustomFields=function(){var e=(0,l.filter)(Xe,(function(e,t){return t.indexOf("%%cf_")>-1}));(0,l.forEach)(e,function(e){this._store.dispatch(Qe((0,l.trim)(e.placeholder,"%%"))),this.removeReplacement(e)}.bind(this))},tt.prototype.parentReplace=function(e){const t=jQuery("#parent_id, #parent").eq(0);return this.hasParentTitle(t)&&(e=e.replace(/%%parent_title%%/,this.getParentTitleReplacement(t))),je()&&!(0,l.isUndefined)(this._currentParentPageTitle)&&(e=e.replace(/%%parent_title%%/,this._currentParentPageTitle)),e},tt.prototype.hasParentTitle=function(e){return!(0,l.isUndefined)(e)&&!(0,l.isUndefined)(e.prop("options"))},tt.prototype.getParentTitleReplacement=function(e){var t=e.find("option:selected").text();return t===(0,g.__)("(no parent)","wordpress-seo")?"":t},tt.ReplaceVar=He;const st=tt,nt=window.wp.blocks,rt=class{constructor(e,t,s){this._registerPlugin=e,this._registerModification=t,this._refreshAnalysis=s,this._reusableBlocks={},this._selectCore=(0,p.select)("core"),this._selectCoreEditor=(0,p.select)("core/editor"),this.reusableBlockChangeListener=this.reusableBlockChangeListener.bind(this),this.parseReusableBlocks=this.parseReusableBlocks.bind(this)}register(){this._registerPlugin("YoastReusableBlocksPlugin",{status:"ready"}),this._registerModification("content",this.parseReusableBlocks,"YoastReusableBlocksPlugin",1),(0,p.subscribe)((0,l.debounce)(this.reusableBlockChangeListener,500))}reusableBlockChangeListener(){const{blocks:e}=this._selectCoreEditor.getPostEdits();if(!e)return;let t=!1;e.forEach((e=>{if(!(0,nt.isReusableBlock)(e))return;const s=this.getBlockContent(e.attributes.ref);this._reusableBlocks[e.attributes.ref]?this._reusableBlocks[e.attributes.ref].content!==s&&(this._reusableBlocks[e.attributes.ref].content=s,t=!0):(this._reusableBlocks[e.attributes.ref]={id:e.attributes.ref,clientId:e.clientId,content:s},t=!0)})),t&&this._refreshAnalysis()}parseReusableBlocks(e){const t=/<!-- wp:block {"ref":(\d+)} \/-->/g;return e.match(t)?e.replace(t,((t,s)=>this._reusableBlocks[s]&&this._reusableBlocks[s].content?this._reusableBlocks[s].content:e)):e}getBlockContent(e){const t=this._selectCore.getEditedEntityRecord("postType","wp_block",e);if(t){if((0,l.isFunction)(t.content))return t.content(t);if(t.blocks)return(0,nt.__unstableSerializeAndClean)(t.blocks);if(t.content)return t.content}return""}},it="[^<>&/\\[\\]\0- =]+?",ot=new RegExp("\\["+it+"( [^\\]]+?)?\\]","g"),at=new RegExp("\\[/"+it+"\\]","g");class lt{constructor({registerPlugin:e,registerModification:t,pluginReady:s,pluginReloaded:n},r){this._registerModification=t,this._pluginReady=s,this._pluginReloaded=n,e("YoastShortcodePlugin",{status:"loading"}),this.bindElementEvents();const i="("+r.join("|")+")";this.shortcodesRegex=new RegExp(i,"g"),this.closingTagRegex=new RegExp("\\[\\/"+i+"\\]","g"),this.nonCaptureRegex=new RegExp("\\["+i+"[^\\]]*?\\]","g"),this.parsedShortcodes=[],this.loadShortcodes(this.declareReady.bind(this))}declareReady(){this._pluginReady("YoastShortcodePlugin"),this.registerModifications()}declareReloaded(){this._pluginReloaded("YoastShortcodePlugin")}registerModifications(){this._registerModification("content",this.replaceShortcodes.bind(this),"YoastShortcodePlugin")}removeUnknownShortCodes(e){return(e=e.replace(ot,"")).replace(at,"")}replaceShortcodes(e){return"string"==typeof e&&this.parsedShortcodes.forEach((({shortcode:t,output:s})=>{e=e.replace(t,s)})),e=this.removeUnknownShortCodes(e)}loadShortcodes(e){const t=this.getUnparsedShortcodes(this.getShortcodes(this.getContentTinyMCE()));if(!(t.length>0))return e();this.parseShortcodes(t,e)}bindElementEvents(){const e=document.querySelector(".wp-editor-area"),t=(0,l.debounce)(this.loadShortcodes.bind(this,this.declareReloaded.bind(this)),500);e&&(e.addEventListener("keyup",t),e.addEventListener("change",t)),"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(e){e.editor.on("change",t),e.editor.on("keyup",t)}))}getContentTinyMCE(){let e=document.querySelector(".wp-editor-area")?document.querySelector(".wp-editor-area").value:"";return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length&&(e=tinyMCE.get("content")?tinyMCE.get("content").getContent():""),e}getUnparsedShortcodes(e){return"object"!=typeof e?(console.error("Failed to get unparsed shortcodes. Expected parameter to be an array, instead received "+typeof e),!1):e.filter((e=>this.isUnparsedShortcode(e)))}isUnparsedShortcode(e){return!this.parsedShortcodes.some((({shortcode:t})=>t===e))}getShortcodes(e){if("string"!=typeof e)return console.error("Failed to get shortcodes. Expected parameter to be a string, instead received"+typeof e),!1;const t=this.matchCapturingShortcodes(e);t.forEach((t=>{e=e.replace(t,"")}));const s=this.matchNonCapturingShortcodes(e);return t.concat(s)}matchCapturingShortcodes(e){const t=(e.match(this.closingTagRegex)||[]).join(" ").match(this.shortcodesRegex)||[];return(0,l.flatten)(t.map((t=>{const s="\\["+t+"[^\\]]*?\\].*?\\[\\/"+t+"\\]";return e.match(new RegExp(s,"g"))||[]})))}matchNonCapturingShortcodes(e){return e.match(this.nonCaptureRegex)||[]}parseShortcodes(e,t){return"function"!=typeof t?(console.error("Failed to parse shortcodes. Expected parameter to be a function, instead received "+typeof t),!1):"object"==typeof e&&e.length>0?void jQuery.post(ajaxurl,{action:"wpseo_filter_shortcodes",_wpnonce:wpseoScriptData.analysis.plugins.shortcodes.wpseo_filter_shortcodes_nonce,data:e},function(e){this.saveParsedShortcodes(e,t)}.bind(this)):t()}saveParsedShortcodes(e,t){const s=JSON.parse(e);this.parsedShortcodes.push(...s),t()}}const ct=lt,{updateShortcodesForParsing:dt}=we.actions;var pt=s(7084),ut=s.n(pt);const ht=class{constructor(e,t){this._registerPlugin=e,this._registerModification=t}register(){this._registerPlugin("YoastMarkdownPlugin",{status:"ready"}),this._registerModification("content",this.parseMarkdown.bind(this),"YoastMarkdownPlugin",1)}parseMarkdown(e){return ut()(e)}},gt="yoastmark";function mt(e,t){return e._properties.position.startOffset>t.length||e._properties.position.endOffset>t.length}function yt(e,t,s){const n=e.dom;let r=e.getContent();if(r=ze.markers.removeMarks(r),(0,l.isEmpty)(s))return void e.setContent(r);r=s[0].hasPosition()?function(e,t){if(!t)return"";for(let s=(e=(0,l.orderBy)(e,(e=>e._properties.position.startOffset),["asc"])).length-1;s>=0;s--){const n=e[s];mt(n,t)||(t=n.applyWithPosition(t))}return t}(s,r):function(e,t,s,n){const{fieldsToMark:r,selectedHTML:i}=ze.languageProcessing.getFieldsToMark(s,n);return(0,l.forEach)(s,(function(t){"acf_content"!==e.id&&(t._properties.marked=ze.languageProcessing.normalizeHTML(t._properties.marked),t._properties.original=ze.languageProcessing.normalizeHTML(t._properties.original)),r.length>0?i.forEach((e=>{const s=t.applyWithReplace(e);n=n.replace(e,s)})):n=t.applyWithReplace(n)})),n}(e,0,s,r),e.setContent(r),function(e){let t=e.getContent();t=t.replace(new RegExp("&lt;yoastmark.+?&gt;","g"),"").replace(new RegExp("&lt;/yoastmark&gt;","g"),""),e.setContent(t)}(e);const i=n.select(gt);(0,l.forEach)(i,(function(e){e.setAttribute("data-mce-bogus","1")}))}function ft(e){return window.test=e,yt.bind(null,e)}const wt="et_pb_main_editor_wrap",bt=class{static isActive(){return!!document.getElementById(wt)}static isTinyMCEHidden(){const e=document.getElementById(wt);return!!e&&e.classList.contains("et_pb_hidden")}listen(e){this.classicEditorContainer=document.getElementById(wt),this.classicEditorContainer&&new MutationObserver((t=>{(0,l.forEach)(t,(t=>{"attributes"===t.type&&"class"===t.attributeName&&(t.target.classList.contains("et_pb_hidden")?e.classicEditorHidden():e.classicEditorShown())}))})).observe(this.classicEditorContainer,{attributes:!0})}},_t=class{static isActive(){return!!window.VCV_I18N}},kt={classicEditorHidden:l.noop,classicEditorShown:l.noop,pageBuilderLoaded:l.noop},vt=class{constructor(){this.determineActivePageBuilders()}determineActivePageBuilders(){bt.isActive()&&(this.diviActive=!0),_t.isActive()&&(this.vcActive=!0)}isPageBuilderActive(){return this.diviActive||this.vcActive}listen(e){this.callbacks=(0,l.defaults)(e,kt),this.diviActive&&(new bt).listen(e)}isClassicEditorHidden(){return!(!this.diviActive||!bt.isTinyMCEHidden())}};let xt;const Et="content",St="description";function Rt(e){xt=e}function Ct(){return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length}function Tt(e){if(!Ct())return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}function Pt(e){let t="";var s;return t=!1===Tt(e)||0==(s=e,null!==document.getElementById(s+"_ifr"))?function(e){return document.getElementById(e)&&document.getElementById(e).value||""}(e):tinyMCE.get(e).getContent(),t}function Ot(e,t,s){"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(n){const r=n.editor;r.id===e&&(0,l.forEach)(t,(function(e){r.on(e,s)}))}))}function At(){(0,l.isUndefined)(xt)||xt.dispatch(we.actions.setMarkerStatus("disabled"))}function It(){(0,l.isUndefined)(xt)||xt.dispatch(we.actions.setMarkerStatus("enabled"))}function Lt(){(0,l.isUndefined)(xt)||xt.dispatch(we.actions.setMarkerPauseStatus(!0))}function Nt(){(0,l.isUndefined)(xt)||xt.dispatch(we.actions.setMarkerPauseStatus(!1))}function Mt(){const e=document.getElementById("wp-content-wrap");return!!e&&e.classList.contains("html-active")}function Bt(){Mt()&&(At(),Ct()&&tinyMCE.on("AddEditor",(function(){It()})))}function Dt(e,t){Ot(t,["input","change","cut","paste"],e),Ot(t,["hide"],At);const s=["show"];(new vt).isPageBuilderActive()||s.push("init"),Ot(t,s,It),Ot("content",["focus"],(function(e){const t=e.target;(function(e){return-1!==e.getContent({format:"raw"}).indexOf("<"+gt)})(t)&&(function(e){ft(e)(null,[])}(t),YoastSEO.app.disableMarkers()),Lt()})),Ot("content",["blur"],(function(){Nt()}))}class Ft{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,l.isString)(e)?(0,l.isUndefined)(t)||(0,l.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,l.isString)(e)?(0,l.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,l.isString)(e)?(0,l.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,n){if(!(0,l.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,l.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,l.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const r={callable:t,origin:s,priority:(0,l.isNumber)(n)?n:10};return(0,l.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(r),!0}_registerAssessment(e,t,s,n){return(0,l.isString)(t)?(0,l.isObject)(s)?(0,l.isString)(n)?(t=n+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+n+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let n=this.modifications[e];return!(0,l.isArray)(n)||n.length<1||(n=this._stripIllegalModifications(n),n.sort(((e,t)=>e.priority-t.priority)),(0,l.forEach)(n,(function(n){const r=n.callable(t,s);typeof r==typeof t?t=r:console.error("Modification with name "+e+" performed by plugin with name "+n.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,l.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,l.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,l.forEach)(this.plugins,(function(e,t){(0,l.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,l.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,l.isUndefined)(this.plugins[e])}}function $t(e,t,s){e("morphology",new ze.Paper("",{keyword:s})).then((e=>{const s=e.result.keyphraseForms;t.dispatch(we.actions.updateWordsToHighlight((0,l.uniq)((0,l.flatten)(s))))})).catch((()=>{t.dispatch(we.actions.updateWordsToHighlight([]))}))}var Ut="score-text",qt="image yoast-logo svg",jt=jQuery;function zt(e,t,s=null){var n,r,i,o,a;if(null!==s)return(0,l.get)(s,t,"");const c=(0,p.select)("yoast-seo/editor").getIsPremium(),d={na:(0,g.__)("Not available","wordpress-seo"),bad:(0,g.__)("Needs improvement","wordpress-seo"),ok:(0,g.__)("OK","wordpress-seo"),good:(0,g.__)("Good","wordpress-seo")},u={keyword:{label:c?(0,g.__)("Premium SEO analysis:","wordpress-seo"):(0,g.__)("SEO analysis:","wordpress-seo"),anchor:"yoast-seo-analysis-collapsible-metabox",status:d},content:{label:(0,g.__)("Readability analysis:","wordpress-seo"),anchor:"yoast-readability-analysis-collapsible-metabox",status:d},"inclusive-language":{label:(0,g.__)("Inclusive language:","wordpress-seo"),anchor:"yoast-inclusive-language-analysis-collapsible-metabox",status:{...d,ok:(0,g.__)("Potentially non-inclusive","wordpress-seo")}}};return null!=u&&null!==(n=u[e])&&void 0!==n&&null!==(r=n.status)&&void 0!==r&&r[t]?`<a href="#${null===(i=u[e])||void 0===i?void 0:i.anchor}">${null===(o=u[e])||void 0===o?void 0:o.label}</a> <strong>${null===(a=u[e])||void 0===a?void 0:a.status[t]}</strong>`:""}function Yt(e,t,s=null){var n=jt("#"+e+"-score"),r=qt+" "+t;n.children(".image").attr("class",r);var i=zt(e,t,s);n.children("."+Ut).html(i)}function Vt(e,t,s=null){const n=jt("<div />",{class:"misc-pub-section yoast yoast-seo-score "+e+"-score",id:e+"-score"}),r=jt("<span />",{class:Ut,html:zt(e,t,s)}),i=jt("<span>").attr("class",qt+" na");n.append(i).append(r),jt("#yoast-seo-publishbox-section").append(n)}function Kt(e){const t=jt("#wpadminbar"),s=jt(e);if(!t||!s)return;const n="fixed"===t.css("position")?t.height():0;jt([document.documentElement,document.body]).animate({scrollTop:s.offset().top-n},1e3),s.trigger("focus"),0===s.parent().siblings().length&&s.trigger("click")}function Wt(){var e="na";wpseoScriptData.metabox.keywordAnalysisActive&&Vt("keyword",e),wpseoScriptData.metabox.contentAnalysisActive&&Vt("content",e),wpseoScriptData.metabox.inclusiveLanguageAnalysisActive&&Vt("inclusive-language",e),jt("#content-score").on("click","[href='#yoast-readability-analysis-collapsible-metabox']",(function(e){e.preventDefault(),document.querySelector("#wpseo-meta-tab-readability").click(),Kt("#wpseo-meta-section-readability")})),jt("#keyword-score").on("click","[href='#yoast-seo-analysis-collapsible-metabox']",(function(e){e.preventDefault(),document.querySelector("#wpseo-meta-tab-content").click(),Kt("#yoast-seo-analysis-collapsible-metabox")})),jt("#inclusive-language-score").on("click","[href='#yoast-inclusive-language-analysis-collapsible-metabox']",(function(e){e.preventDefault(),document.querySelector("#wpseo-meta-tab-inclusive-language").click(),Kt("#wpseo-meta-section-inclusive-language")}))}function Ht(e){var t=jQuery(".yst-traffic-light"),s=t.closest(".wpseo-meta-section-link"),n=jQuery("#wpseo-traffic-light-desc"),r=e.className||"na";t.attr("class","yst-traffic-light "+r),s.attr("aria-describedby","wpseo-traffic-light-desc"),n.length>0?n.text(e.screenReaderText):s.closest("li").append("<span id='wpseo-traffic-light-desc' class='screen-reader-text'>"+e.screenReaderText+"</span>")}function Qt(e){jQuery("#wp-admin-bar-wpseo-menu .wpseo-score-icon").attr("title",e.screenReaderText).attr("class","wpseo-score-icon "+e.className).find(".wpseo-score-text").text(e.screenReaderText)}function Zt(){return(0,l.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}function Gt(){const e=Zt();return(0,l.get)(e,"contentLocale","en_US")}function Jt(){const e=Zt();return!0===(0,l.get)(e,"contentAnalysisActive",!1)}function Xt(){const e=Zt();return!0===(0,l.get)(e,"keywordAnalysisActive",!1)}function es(){const e=Zt();return!0===(0,l.get)(e,"inclusiveLanguageAnalysisActive",!1)}const ts=window.yoast.featureFlag;function ss(){}let ns=!1;function rs(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function is(e,t,s,n,r){if(!ns)return;const i=ze.Paper.parse(t());e.analyze(i).then((o=>{const{result:{seo:a,readability:l,inclusiveLanguage:c}}=o;if(a){const e=a[""];e.results.forEach((e=>{e.getMarker=()=>()=>s(i,e.marks)})),e.results=rs(e.results),n.dispatch(we.actions.setSeoResultsForKeyword(i.getKeyword(),e.results)),n.dispatch(we.actions.setOverallSeoScore(e.score,i.getKeyword())),n.dispatch(we.actions.refreshSnippetEditor()),r.saveScores(e.score,i.getKeyword())}l&&(l.results.forEach((e=>{e.getMarker=()=>()=>s(i,e.marks)})),l.results=rs(l.results),n.dispatch(we.actions.setReadabilityResults(l.results)),n.dispatch(we.actions.setOverallReadabilityScore(l.score)),n.dispatch(we.actions.refreshSnippetEditor()),r.saveContentScore(l.score)),c&&(c.results.forEach((e=>{e.getMarker=()=>()=>s(i,e.marks)})),c.results=rs(c.results),n.dispatch(we.actions.setInclusiveLanguageResults(c.results)),n.dispatch(we.actions.setOverallInclusiveLanguageScore(c.score)),n.dispatch(we.actions.refreshSnippetEditor()),r.saveInclusiveLanguageScore(c.score)),(0,h.doAction)("yoast.analysis.refresh",o,{paper:i,worker:e,collectData:t,applyMarks:s,store:n,dataCollector:r})})).catch(ss)}const os="yoast-measurement-element";function as(e){let t=document.getElementById(os);return t||(t=function(){const e=document.createElement("div");return e.id=os,e.style.position="absolute",e.style.left="-9999em",e.style.top=0,e.style.height=0,e.style.overflow="hidden",e.style.fontFamily="arial, sans-serif",e.style.fontSize="20px",e.style.fontWeight="400",document.body.appendChild(e),e}()),t.innerText=e,t.offsetWidth}const ls=e=>(e=e.filter((e=>e.isValid))).map((e=>{const t=(0,nt.serialize)([e],{isInnerBlocks:!1});return e.blockLength=t&&t.length,e.innerBlocks&&(e.innerBlocks=ls(e.innerBlocks)),e}));function cs(e){return(0,l.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,g.__)("Not available","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("Not available","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Not available","wordpress-seo")};case"bad":return{className:"bad",screenReaderText:(0,g.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,g.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,g.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,g.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,g.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(ze.interpreters.scoreToRating(e))}const{tmceId:ds}=t,ps=jQuery,us=function(e){"object"==typeof CKEDITOR&&console.warn("YoastSEO currently doesn't support ckEditor. The content analysis currently only works with the HTML editor or TinyMCE."),this._data=e.data,this._store=e.store};us.prototype.getData=function(){const e=this._data.getData(),t=this._store.getState();return{keyword:Xt()?this.getKeyword():"",meta:this.getMeta(),text:e.content,title:e.title,url:e.slug,excerpt:e.excerpt,snippetTitle:this.getSnippetTitle(),snippetMeta:this.getSnippetMeta(),snippetCite:this.getSnippetCite(),primaryCategory:this.getPrimaryCategory(),searchUrl:this.getSearchUrl(),postUrl:this.getPostUrl(),permalink:this.getPermalink(),titleWidth:as(this.getSnippetTitle()),metaTitle:(0,l.get)(t,["analysisData","snippet","title"],this.getSnippetTitle()),url:(0,l.get)(t,["snippetEditor","data","slug"],e.slug),meta:this.getMetaDescForAnalysis(t)}},us.prototype.getKeyword=function(){return document.getElementById("yoast_wpseo_focuskw")&&document.getElementById("yoast_wpseo_focuskw").value||""},us.prototype.getMetaDescForAnalysis=function(e){let t=(0,l.get)(e,["analysisData","snippet","description"],this.getSnippetMeta());return""!==wpseoScriptData.metabox.metaDescriptionDate&&(t=wpseoScriptData.metabox.metaDescriptionDate+" - "+t),t},us.prototype.getMeta=function(){return document.getElementById("yoast_wpseo_metadesc")&&document.getElementById("yoast_wpseo_metadesc").value||""},us.prototype.getText=function(){return ze.markers.removeMarks(Pt(ds))},us.prototype.getTitle=function(){return document.getElementById("title")&&document.getElementById("title").value||""},us.prototype.getUrl=function(){const e=(0,p.select)("core/editor");if(e&&e.getCurrentPostAttribute("slug"))return e.getCurrentPostAttribute("slug");var t="",s=ps("#new-post-slug");return 0<s.length?t=s.val():null!==document.getElementById("editable-post-name-full")&&(t=document.getElementById("editable-post-name-full").textContent),t},us.prototype.getExcerpt=function(){var e="";return null!==document.getElementById("excerpt")&&(e=document.getElementById("excerpt")&&document.getElementById("excerpt").value||""),e},us.prototype.getSnippetTitle=function(){return document.getElementById("yoast_wpseo_title")&&document.getElementById("yoast_wpseo_title").value||""},us.prototype.getSnippetMeta=function(){return document.getElementById("yoast_wpseo_metadesc")&&document.getElementById("yoast_wpseo_metadesc").value||""},us.prototype.getSnippetCite=function(){return this.getUrl()||""},us.prototype.getPrimaryCategory=function(){var e="",t=ps("#category-all").find("ul.categorychecklist"),s=t.find("li input:checked");if(1===s.length)return this.getCategoryName(s.parent());var n=t.find(".wpseo-primary-term > label");return n.length?e=this.getCategoryName(n):e},us.prototype.getSearchUrl=function(){return wpseoScriptData.metabox.search_url},us.prototype.getPostUrl=function(){return wpseoScriptData.metabox.post_edit_url},us.prototype.getPermalink=function(){var e=this.getUrl();return wpseoScriptData.metabox.base_url+e},us.prototype.getCategoryName=function(e){var t=e.clone();return t.children().remove(),t.text().trim()},us.prototype.setDataFromSnippet=function(e,t){switch(t){case"snippet_meta":document.getElementById("yoast_wpseo_metadesc").value=e;break;case"snippet_cite":if(this.leavePostNameUntouched)return void(this.leavePostNameUntouched=!1);null!==document.getElementById("post_name")&&(document.getElementById("post_name").value=e),null!==document.getElementById("editable-post-name")&&null!==document.getElementById("editable-post-name-full")&&(document.getElementById("editable-post-name").textContent=e,document.getElementById("editable-post-name-full").textContent=e);break;case"snippet_title":document.getElementById("yoast_wpseo_title").value=e}},us.prototype.saveSnippetData=function(e){this.setDataFromSnippet(e.title,"snippet_title"),this.setDataFromSnippet(e.urlPath,"snippet_cite"),this.setDataFromSnippet(e.metaDesc,"snippet_meta")},us.prototype.bindElementEvents=function(e){this.inputElementEventBinder(e),this.changeElementEventBinder(e)},us.prototype.changeElementEventBinder=function(e){for(var t=["#yoast-wpseo-primary-category",'.categorychecklist input[name="post_category[]"]'],s=0;s<t.length;s++)ps(t[s]).on("change",e)},us.prototype.inputElementEventBinder=function(e){for(var t=["excerpt","content","title"],s=0;s<t.length;s++)null!==document.getElementById(t[s])&&document.getElementById(t[s]).addEventListener("input",e);Dt(e,ds)},us.prototype.saveScores=function(e,t){var s=cs(e);Yt("content",s.className),document.getElementById("yoast_wpseo_linkdex").value=e,""===t&&(s.className="na",s.screenReaderText=(0,g.__)("Enter a focus keyphrase to calculate the SEO score","wordpress-seo")),Ht(s),Qt(s),Yt("keyword",s.className),jQuery(window).trigger("YoastSEO:numericScore",e)},us.prototype.saveContentScore=function(e){var t=cs(e);Yt("content",t.className),Xt()||(Ht(t),Qt(t)),ps("#yoast_wpseo_content_score").val(e)},us.prototype.saveInclusiveLanguageScore=function(e){const t=cs(e);Yt("inclusive-language",t.className),Xt()||Jt()||(Ht(t),Qt(t)),ps("#yoast_wpseo_inclusive_language_score").val(e)};const hs=us;class gs{constructor(){this._callbacks=[],this.register=this.register.bind(this)}register(e){(0,l.isFunction)(e)&&this._callbacks.push(e)}getData(){let e={};return this._callbacks.forEach((t=>{e=(0,l.merge)(e,t())})),e}}window.wp.annotations;const ms=function(e){return(0,l.uniq)((0,l.flatten)(e.map((e=>{if(!(0,l.isUndefined)(e.getFieldsToMark()))return e.getFieldsToMark()}))))},ys=window.wp.richText,fs=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:ws}=ze.helpers.htmlEntities,bs=e=>{let t=0;return(0,l.forEachRight)(e,(e=>{const[s]=e;let n=s.length;/^<\/?br/.test(s)&&(n-=1),t+=n})),t},_s="<yoastmark class='yoast-text-mark'>",ks="</yoastmark>",vs='<yoastmark class="yoast-text-mark">';function xs(e,t,s,n,r){const i=n.clientId,o=(0,ys.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,l.flatMap)(r,(s=>{let r;return r=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,n,r){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),i=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const n="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=n.length,blockEndOffset:t-=n.length}})(t,i,s);t=e.blockStartOffset,i=e.blockEndOffset}if(n.slice(t,i)===r.slice(t,i))return[{startOffset:t,endOffset:i}];const o=((e,t,s)=>{const n=s.slice(0,e),r=s.slice(0,t),i=((e,t,s,n)=>{const r=[...e.matchAll(fs)];s-=bs(r);const i=[...t.matchAll(fs)];return{blockStartOffset:s,blockEndOffset:n-=bs(i)}})(n,r,e,t),o=((e,t,s,n)=>{let r=[...e.matchAll(ws)];return(0,l.forEachRight)(r,(e=>{const[,t]=e;s-=t.length})),r=[...t.matchAll(ws)],(0,l.forEachRight)(r,(e=>{const[,t]=e;n-=t.length})),{blockStartOffset:s,blockEndOffset:n}})(n,r,e=i.blockStartOffset,t=i.blockEndOffset);return{blockStartOffset:e=o.blockStartOffset,blockEndOffset:t=o.blockEndOffset}})(t,i,n);return[{startOffset:o.blockStartOffset,endOffset:o.blockEndOffset}]}return[]}(s,i,n.name,e,o):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),n=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),r=function(e,t,s=!0){const n=[];if(0===e.length)return n;let r,i=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(r=e.indexOf(t,i))>-1;)n.push(r),i=r+t.length;return n}(e,s);if(0===r.length)return[];const i=function(e){let t=e.indexOf(_s);const s=t>=0;s||(t=e.indexOf(vs));let n=null;const r=[];for(;t>=0;){if(n=(e=s?e.replace(_s,""):e.replace(vs,"")).indexOf(ks),n<t)return[];e=e.replace(ks,""),r.push({startOffset:t,endOffset:n}),t=s?e.indexOf(_s):e.indexOf(vs),n=null}return r}(n),o=[];return i.forEach((e=>{r.forEach((n=>{const r=n+e.startOffset;let i=n+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(i=n+s.length),o.push({startOffset:r,endOffset:i})}))})),o}(o,s),r?r.map((e=>({...e,block:i,richTextIdentifier:t}))):[]}))}const Es=e=>e[0].toUpperCase()+e.slice(1),Ss=(e,t,s,n,r)=>(e=e.map((e=>{const i=`${e.id}-${r[0]}`,o=`${e.id}-${r[1]}`,a=Es(r[0]),l=Es(r[1]),c=e[`json${a}`],d=e[`json${l}`],{marksForFirstSection:p,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),h=xs(c,i,s,n,p),g=xs(d,o,s,n,u);return h.concat(g)})),(0,l.flattenDeep)(e)),Rs="yoast";let Cs=[];const Ts={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function Ps(){const e=Cs.shift();e&&((0,p.dispatch)("core/annotations").__experimentalAddAnnotation(e),Os())}function Os(){(0,l.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(Ps,{timeout:1e3}):setTimeout(Ps,150)}const As=(e,t)=>{return(0,l.flatMap)((s=e.name,Ts.hasOwnProperty(s)?Ts[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];return 0===n.length?[]:Ss(n,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];if(n&&0===n.length)return[];const r=[];return"steps"===e.key&&r.push(Ss(n,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),r.push(xs(n,"description",e,t,s))),(0,l.flattenDeep)(r)})(s,e,t):function(e,t,s){const n=e.key,r=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:(s||"").toString()})(t,n);return xs(r,n,e,t,s)}(s,e,t)));var s};function Is(e,t){return(0,l.flatMap)(e,(e=>{const s=function(e){return e.innerBlocks.length>0}(e)?Is(e.innerBlocks,t):[];return As(e,t).concat(s)}))}function Ls(e){Cs=[],(0,p.dispatch)("core/annotations").__experimentalRemoveAnnotationsBySource(Rs);const t=ms(e);if(0===e.length)return;let s=(0,p.select)("core/block-editor").getBlocks();var n;t.length>0&&(s=s.filter((e=>t.some((t=>"core/"+t===e.name))))),n=Is(s,e),Cs=n.map((e=>({blockClientId:e.block,source:Rs,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),Os()}function Ns(e,t){let s;Tt(Et)&&((0,l.isUndefined)(s)&&(s=ft(tinyMCE.get(Et))),s(e,t)),(0,p.select)("core/block-editor")&&(0,l.isFunction)((0,p.select)("core/block-editor").getBlocks)&&(0,p.select)("core/annotations")&&(0,l.isFunction)((0,p.dispatch)("core/annotations").__experimentalAddAnnotation)&&(function(e,t){tinyMCE.editors.map((e=>ft(e))).forEach((s=>s(e,t)))}(e,t),Ls(t)),(0,h.doAction)("yoast.analysis.applyMarks",t)}function Ms(){const e=(0,p.select)("yoast-seo/editor").isMarkingAvailable(),t=(0,p.select)("yoast-seo/editor").getMarkerPauseStatus();return!e||t?l.noop:Ns}var Bs=jQuery;function Ds(e,t,s,n,r){this._scriptUrl=n,this._options={usedKeywords:t.keyword_usage,usedKeywordsPostTypes:t.keyword_usage_post_types,searchUrl:t.search_url,postUrl:t.post_edit_url},this._keywordUsage=t.keyword_usage,this._usedKeywordsPostTypes=t.keyword_usage_post_types,this._postID=Bs("#post_ID, [name=tag_ID]").val(),this._taxonomy=Bs("[name=taxonomy]").val()||"",this._nonce=r,this._ajaxAction=e,this._refreshAnalysis=s,this._initialized=!1}Ds.prototype.init=function(){const{worker:e}=window.YoastSEO.analysis;this.requestKeywordUsage=(0,l.debounce)(this.requestKeywordUsage.bind(this),500),e.loadScript(this._scriptUrl).then((()=>{e.sendMessage("initialize",this._options,"used-keywords-assessment")})).then((()=>{this._initialized=!0,(0,l.isEqual)(this._options.usedKeywords,this._keywordUsage)?this._refreshAnalysis():e.sendMessage("updateKeywordUsage",this._keywordUsage,"used-keywords-assessment").then((()=>this._refreshAnalysis()))})).catch((e=>console.error(e)))},Ds.prototype.setKeyword=function(e){(0,l.has)(this._keywordUsage,e)||this.requestKeywordUsage(e)},Ds.prototype.requestKeywordUsage=function(e){Bs.post(ajaxurl,{action:this._ajaxAction,post_id:this._postID,keyword:e,taxonomy:this._taxonomy,nonce:this._nonce},this.updateKeywordUsage.bind(this,e),"json")},Ds.prototype.updateKeywordUsage=function(e,t){const{worker:s}=window.YoastSEO.analysis,n=t.keyword_usage,r=t.post_types;n&&(0,l.isArray)(n)&&(this._keywordUsage[e]=n,this._usedKeywordsPostTypes[e]=r,this._initialized&&s.sendMessage("updateKeywordUsage",{usedKeywords:this._keywordUsage,usedKeywordsPostTypes:this._usedKeywordsPostTypes},"used-keywords-assessment").then((()=>this._refreshAnalysis())))};const{setFocusKeyword:Fs,updateData:$s,setCornerstoneContent:Us,refreshSnippetEditor:qs,setReadabilityResults:js,setSeoResultsForKeyword:zs}=we.actions;function Ys(e,s,r){if("undefined"==typeof wpseoScriptData)return;let i,o,a,c;const d=new gs;function u(e){return""===e.responseText?o.val():jQuery("<div>"+e.responseText+"</div>").find("#editable-post-name-full").text()}function g(){const e={};return Xt()&&(e.output="does-not-really-exist-but-it-needs-something"),Jt()&&(e.contentOutput="also-does-not-really-exist-but-it-needs-something"),e}function m(e){(0,l.isUndefined)(e.seoAssessorPresenter)||(e.seoAssessorPresenter.render=function(){}),(0,l.isUndefined)(e.contentAssessorPresenter)||(e.contentAssessorPresenter.render=function(){},e.contentAssessorPresenter.renderIndividualRatings=function(){})}let y;function f(e,t){const s=y||"";y=e.getState().analysisData.snippet,!(0,Ye.isShallowEqualObjects)(s,y)&&t()}function w(){return(0,p.select)("core/edit-post").getEditorMode()}jQuery(document).on("ajaxComplete",(function(e,t,n){if("/admin-ajax.php"===n.url.substring(n.url.length-15)&&"string"==typeof n.data&&-1!==n.data.indexOf("action=sample-permalink")){c.leavePostNameUntouched=!0;const e={slug:u(t)};s.dispatch($s(e))}})),function(){if(i=e("#wpseo_meta"),Rt(s),Bt(),function(){const e=new vt;e.isClassicEditorHidden()&&At(),e.vcActive?At():e.listen({classicEditorHidden:()=>{At()},classicEditorShown:()=>{Mt()||It()}})}(),0===i.length)return;c=function(e){const t=new hs({data:e,store:s});return t.leavePostNameUntouched=!1,t}(r),Wt();const u=function(t){const s={elementTarget:[Et,"yoast_wpseo_focuskw_text_input","yoast_wpseo_metadesc","excerpt","editable-post-name","editable-post-name-full"],targets:g(),callbacks:{getData:c.getData.bind(c)},locale:wpseoScriptData.metabox.contentLocale,marker:Ms(),contentAnalysisActive:Jt(),keywordAnalysisActive:Xt(),debouncedRefresh:!1,researcher:new window.yoast.Researcher.default};return Xt()&&(t.dispatch(Fs(e("#yoast_wpseo_focuskw").val())),s.callbacks.saveScores=c.saveScores.bind(c),s.callbacks.updatedKeywordsResults=function(e){const s=t.getState().focusKeyword;t.dispatch(zs(s,e)),t.dispatch(qs())}),Jt()&&(s.callbacks.saveContentScore=c.saveContentScore.bind(c),s.callbacks.updatedContentResults=function(e){t.dispatch(js(e)),t.dispatch(qs())}),o=e("#title"),s}(s);a=new ze.App(u),window.YoastSEO=window.YoastSEO||{},window.YoastSEO.app=a,window.YoastSEO.store=s,window.YoastSEO.analysis={},window.YoastSEO.analysis.worker=function(){const e=(0,l.get)(window,["wpseoScriptData","analysis","worker","url"],"analysis-worker.js"),t=(0,ze.createWorker)(e),s=(0,l.get)(window,["wpseoScriptData","analysis","worker","dependencies"],[]),n=[];for(const e in s){if(!Object.prototype.hasOwnProperty.call(s,e))continue;const t=window.document.getElementById(`${e}-js-translations`);if(!t)continue;const r=t.innerHTML.slice(214),i=r.indexOf(","),o=r.slice(0,i-1);try{const e=JSON.parse(r.slice(i+1,-4));n.push([o,e])}catch(t){console.warn(`Failed to parse translation data for ${e} to send to the Yoast SEO worker`);continue}}return t.postMessage({dependencies:s,translations:n}),new ze.AnalysisWorkerWrapper(t)}(),window.YoastSEO.analysis.collectData=()=>function(e,t,s,n,r){const i=(0,l.cloneDeep)(t.getState());(0,l.merge)(i,s.getData());const o=e.getData();let a=null;r&&(a=r.getBlocks()||[],a=JSON.parse(JSON.stringify(a)),a=ls(a));const c={text:o.content,textTitle:o.title,keyword:i.focusKeyword,synonyms:i.synonyms,description:i.analysisData.snippet.description||i.snippetEditor.data.description,title:i.analysisData.snippet.title||i.snippetEditor.data.title,slug:i.snippetEditor.data.slug,permalink:i.settings.snippetEditor.baseUrl+i.snippetEditor.data.slug,wpBlocks:a,date:i.settings.snippetEditor.date};n.loaded&&(c.title=n._applyModifications("data_page_title",c.title),c.title=n._applyModifications("title",c.title),c.description=n._applyModifications("data_meta_desc",c.description),c.text=n._applyModifications("content",c.text),c.wpBlocks=n._applyModifications("wpBlocks",c.wpBlocks));const d=i.analysisData.snippet.filteredSEOTitle;return c.titleWidth=as(d||i.snippetEditor.data.title),c.locale=Gt(),c.writingDirection=function(){let e="LTR";return Zt().isRtl&&(e="RTL"),e}(),c.shortcodes=window.wpseoScriptData.analysis.plugins.shortcodes?window.wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags:[],c.isFrontPage="1"===(0,l.get)(window,"wpseoScriptData.isFrontPage","0"),ze.Paper.parse((0,h.applyFilters)("yoast.analysis.data",c))}(r,s,d,a.pluggable,(0,p.select)("core/block-editor")),window.YoastSEO.analysis.applyMarks=(e,t)=>Ms()(e,t),window.YoastSEO.app.refresh=(0,l.debounce)((()=>is(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,s,c)),500),window.YoastSEO.app.registerCustomDataCallback=d.register,window.YoastSEO.app.pluggable=new Ft(window.YoastSEO.app.refresh),window.YoastSEO.app.registerPlugin=window.YoastSEO.app.pluggable._registerPlugin,window.YoastSEO.app.pluginReady=window.YoastSEO.app.pluggable._ready,window.YoastSEO.app.pluginReloaded=window.YoastSEO.app.pluggable._reloaded,window.YoastSEO.app.registerModification=window.YoastSEO.app.pluggable._registerModification,window.YoastSEO.app.registerAssessment=(e,t,s)=>{if(!(0,l.isUndefined)(a.seoAssessor))return window.YoastSEO.app.pluggable._registerAssessment(a.defaultSeoAssessor,e,t,s)&&window.YoastSEO.app.pluggable._registerAssessment(a.cornerStoneSeoAssessor,e,t,s)},window.YoastSEO.app.changeAssessorOptions=function(e){window.YoastSEO.analysis.worker.initialize(e).catch(ss),window.YoastSEO.app.refresh()},function(e,t,s){const n=Zt();if(!n.previouslyUsedKeywordActive)return;const r=new Ds("get_focus_keyword_usage_and_post_types",n,e,(0,l.get)(window,["wpseoScriptData","analysis","worker","keywords_assessment_url"],"used-keywords-assessment.js"),(0,l.get)(window,["wpseoScriptData","usedKeywordsNonce"],""));r.init();let i={};s.subscribe((()=>{const e=s.getState()||{};e.focusKeyword!==i.focusKeyword&&(i=e,r.setKeyword(e.focusKeyword))}))}(a.refresh,0,s),s.subscribe(f.bind(null,s,a.refresh)),window.YoastSEO.analyzerArgs=u,window.YoastSEO.wp={},window.YoastSEO.wp.replaceVarsPlugin=new st(a,s),function(e,t){let s=[];s=(0,h.applyFilters)("yoast.analysis.shortcodes",s);const n=wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags;s=s.filter((e=>n.includes(e))),s.length>0&&(t.dispatch(dt(s)),window.YoastSEO.wp.shortcodePlugin=new lt({registerPlugin:e.registerPlugin,registerModification:e.registerModification,pluginReady:e.pluginReady,pluginReloaded:e.pluginReloaded},s))}(a,s),je()&&new rt(a.registerPlugin,a.registerModification,window.YoastSEO.app.refresh).register(),wpseoScriptData.metabox.markdownEnabled&&new ht(a.registerPlugin,a.registerModification).register(),window.YoastSEO.wp._tinyMCEHelper=t,Xt()&&function(t){const s=cs(e("#yoast_wpseo_linkdex").val());Ht(s),Qt(s),t.updateScore("keyword",s.className)}(n),Jt()&&function(t){const s=cs(e("#yoast_wpseo_content_score").val());Qt(s),t.updateScore("content",s.className)}(n),es()&&function(t){const s=cs(e("#yoast_wpseo_inclusive_language_score").val());Qt(s),t.updateScore("inclusive-language",s.className)}(n),window.YoastSEO.analysis.worker.initialize(function(e={}){const t={locale:Gt(),contentAnalysisActive:Jt(),keywordAnalysisActive:Xt(),inclusiveLanguageAnalysisActive:es(),defaultQueryParams:(0,l.get)(window,["wpseoAdminL10n","default_query_params"],{}),logLevel:(0,l.get)(window,["wpseoScriptData","analysis","worker","log_level"],"ERROR"),enabledFeatures:(0,ts.enabledFeatures)()};return(0,l.merge)(t,e)}()).then((()=>{jQuery(window).trigger("YoastSEO:ready")})).catch(ss),c.bindElementEvents((0,l.debounce)((()=>is(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,s,c)),500)),m(a);const y=a.initAssessorPresenters.bind(a);a.initAssessorPresenters=function(){y(),m(a)},r.setRefresh&&r.setRefresh(a.refresh);let b={title:(_=c).getSnippetTitle(),slug:_.getSnippetCite(),description:_.getSnippetMeta()};var _;const k=function(e){const t={};if((0,l.isUndefined)(e))return t;t.title=e.title_template;const s=e.metadesc_template;return(0,l.isEmpty)(s)||(t.description=s),t}(wpseoScriptData.metabox);b=function(e,t){const s={...e};return(0,l.forEach)(t,((t,n)=>{(0,l.has)(e,n)&&""===e[n]&&(s[n]=t)})),s}(b,k),s.dispatch($s(b)),s.dispatch(Us("1"===document.getElementById("yoast_wpseo_is_cornerstone").value));let v=s.getState().focusKeyword;$t(window.YoastSEO.analysis.worker.runResearch,s,v);const x=(0,l.debounce)((()=>{a.refresh()}),50);let E=null;if(s.subscribe((()=>{const t=s.getState().focusKeyword;v!==t&&(v=t,$t(window.YoastSEO.analysis.worker.runResearch,s,v),e("#yoast_wpseo_focuskw").val(v),x());const n=function(e){const t=e.getState().snippetEditor.data;return{title:t.title,slug:t.slug,description:t.description}}(s),r=function(e,t){const s={...e};return(0,l.forEach)(t,((t,n)=>{(0,l.has)(e,n)&&e[n].trim()===t&&(s[n]="")})),s}(n,k);b.title!==n.title&&c.setDataFromSnippet(r.title,"snippet_title"),b.slug!==n.slug&&c.setDataFromSnippet(r.slug,"snippet_cite"),b.description!==n.description&&c.setDataFromSnippet(r.description,"snippet_meta");const i=s.getState();E!==i.isCornerstone&&(E=i.isCornerstone,document.getElementById("yoast_wpseo_is_cornerstone").value=i.isCornerstone,a.changeAssessorOptions({useCornerstone:i.isCornerstone})),b.title=n.title,b.slug=n.slug,b.description=n.description})),je()){let e=w();(0,p.subscribe)((()=>{const t=w();t!==e&&(e=t)}))}ns=!0,window.YoastSEO.app.refresh()}()}window.YoastReplaceVarPlugin=st,window.YoastShortcodePlugin=ct;const Vs=window.yoast.styledComponents;var Ks=s.n(Vs);const Ws=window.wp.compose,Hs=({id:e,value:t,terms:s,label:n,onChange:r})=>{const i=(0,u.useCallback)((e=>{r(parseInt(e,10))}),[r]);return(0,c.createElement)(d.SelectControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,id:e,label:n,value:t,onChange:i,options:s.map((e=>({label:(0,l.unescape)(e.name),value:e.id})))})};Hs.propTypes={terms:f().arrayOf(f().shape({id:f().number.isRequired,name:f().string.isRequired})),onChange:f().func.isRequired,id:f().string,label:f().string,value:f().number};const Qs=Hs,Zs=Ks().div`
	padding-top: 16px;
`;class Gs extends u.Component{constructor(e){super(e),this.onChange=this.onChange.bind(this),this.updateReplacementVariable=this.updateReplacementVariable.bind(this);const{fieldId:t,name:s}=e.taxonomy;this.input=document.getElementById(t),e.setPrimaryTaxonomyId(s,parseInt(this.input.value,10)),this.state={selectedTerms:[],terms:[]}}componentDidMount(){this.fetchTerms()}componentDidUpdate(e,t){if(e.selectedTermIds.length<this.props.selectedTermIds.length){const t=(0,l.difference)(this.props.selectedTermIds,e.selectedTermIds)[0];if(!this.termIsAvailable(t))return void this.fetchTerms()}e.selectedTermIds!==this.props.selectedTermIds&&this.updateSelectedTerms(this.state.terms,this.props.selectedTermIds),t.selectedTerms!==this.state.selectedTerms&&this.handleSelectedTermsChange()}handleSelectedTermsChange(){const{selectedTerms:e}=this.state,{primaryTaxonomyId:t}=this.props;e.find((e=>e.id===t))||this.onChange(e.length?e[0].id:-1)}termIsAvailable(e){return!!this.state.terms.find((t=>t.id===e))}fetchTerms(){const{taxonomy:e}=this.props;e&&(this.fetchRequest=Ie()({path:(0,xe.addQueryArgs)(`/wp/v2/${e.restBase}`,{per_page:-1,orderby:"count",order:"desc",_fields:"id,name"})}),this.fetchRequest.then((e=>{const t=this.state;this.setState({terms:e,selectedTerms:this.getSelectedTerms(e,this.props.selectedTermIds)},(()=>{0===t.terms.length&&this.state.terms.length>0&&this.updateReplacementVariable(this.props.primaryTaxonomyId)}))})))}getSelectedTerms(e,t){return e.filter((e=>t.includes(e.id)))}updateSelectedTerms(e,t){const s=this.getSelectedTerms(e,t);this.setState({selectedTerms:s})}onChange(e){const{name:t}=this.props.taxonomy;this.updateReplacementVariable(e),this.props.setPrimaryTaxonomyId(t,e),this.input.value=-1===e?"":e}updateReplacementVariable(e){if("category"!==this.props.taxonomy.name)return;const t=this.state.selectedTerms.find((t=>t.id===e));this.props.updateReplacementVariable(`primary_${this.props.taxonomy.name}`,t?t.name:"")}render(){const{primaryTaxonomyId:e,taxonomy:t,learnMoreLink:s}=this.props;if(this.state.selectedTerms.length<2)return null;const n=`yoast-primary-${t.name}-picker`;return(0,c.createElement)(Zs,{className:"components-base-control__field"},(0,c.createElement)(Qs,{label:(0,g.sprintf)(/* translators: %s expands to the taxonomy name. */
(0,g.__)("Select the primary %s","wordpress-seo"),t.singularLabel.toLowerCase()),value:e,onChange:this.onChange,id:n,terms:this.state.selectedTerms}),(0,c.createElement)(d.ExternalLink,{className:"yst-inline-block yst-mt-2",href:s},(0,g.__)("Learn more","wordpress-seo"),(0,c.createElement)("span",{className:"screen-reader-text"},(0,g.__)("Learn more about the primary category.","wordpress-seo"))))}}Gs.propTypes={selectedTermIds:f().arrayOf(f().number),primaryTaxonomyId:f().number,setPrimaryTaxonomyId:f().func,updateReplacementVariable:f().func,taxonomy:f().shape({name:f().string,fieldId:f().string,restBase:f().string,singularLabel:f().string}),learnMoreLink:f().string.isRequired},Gs.defaultProps={selectedTermIds:[],primaryTaxonomyId:-1,setPrimaryTaxonomyId:l.noop,updateReplacementVariable:l.noop,taxonomy:{}};const Js=Gs,Xs=(0,Ws.compose)([(0,p.withSelect)(((e,t)=>{const s=e("core/editor"),n=e("yoast-seo/editor"),{taxonomy:r}=t;return{selectedTermIds:s.getEditedPostAttribute(r.restBase)||[],primaryTaxonomyId:n.getPrimaryTaxonomyId(r.name),learnMoreLink:n.selectLink("https://yoa.st/primary-category-more")}})),(0,p.withDispatch)((e=>{const{setPrimaryTaxonomyId:t,updateReplacementVariable:s}=e("yoast-seo/editor");return{setPrimaryTaxonomyId:t,updateReplacementVariable:s}}))])(Js);let en=null,tn=null;const sn=Ks().div`
	margin: 16px 0 8px;
`;class nn extends u.Component{constructor(){super(),en&&tn||(en=(0,l.get)(window.wpseoPrimaryCategoryL10n,"taxonomies",{}),tn=(0,l.values)(en).map((e=>e.name))),this.state={exceptionCaught:!1,error:null}}componentDidCatch(e){this.setState({exceptionCaught:!0,error:e})}taxonomyHasPrimaryTermSupport(){return tn.includes(this.props.slug)}render(){const{slug:e,OriginalComponent:t}=this.props;if(this.state.exceptionCaught){const e=(0,l.get)(this.state,"error.stack");return(0,c.createElement)(u.Fragment,null,(0,c.createElement)(t,{...this.props}),(0,c.createElement)(sn,null,(0,g.sprintf)(/* translators: %s expands to Yoast SEO. */
(0,g.__)("An error occurred loading the %s primary taxonomy picker.","wordpress-seo"),"Yoast SEO")),e&&(0,c.createElement)(d.ClipboardButton,{isLarge:!0,text:e},(0,g.__)("Copy error","wordpress-seo")))}return this.taxonomyHasPrimaryTermSupport()?(0,c.createElement)(u.Fragment,null,(0,c.createElement)(t,{...this.props}),(0,c.createElement)(Xs,{taxonomy:en[e]})):(0,c.createElement)(t,{...this.props})}}nn.propTypes={OriginalComponent:f().func.isRequired,slug:f().string.isRequired};const rn=nn;let on=null;const an=()=>{if(null===on){const e=(0,p.dispatch)("yoast-seo/editor").runAnalysis;on=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new Ft(e)}return on},ln=(e,t,s)=>an().loaded?an()._applyModifications(e,t,s):t;function cn(){const{getAnalysisData:e,getEditorDataTitle:t,getIsFrontPage:s}=(0,p.select)("yoast-seo/editor");let n=e();n={...n,textTitle:t(),isFrontPage:s()};const r=function(e){return e.title=ln("data_page_title",e.title),e.title=ln("title",e.title),e.description=ln("data_meta_desc",e.description),e.text=ln("content",e.text),e}(n);return(0,h.applyFilters)("yoast.analysis.data",r)}(0,l.debounce)((async function(e,t){const{text:s,...n}=t,r=new ze.Paper(s,n);try{const t=await e.analyze(r),{seo:s,readability:n,inclusiveLanguage:i}=t.result;if(s){const e=s[""];e.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(r,e.marks)})),e.results=rs(e.results),(0,p.dispatch)("yoast-seo/editor").setSeoResultsForKeyword(r.getKeyword(),e.results),(0,p.dispatch)("yoast-seo/editor").setOverallSeoScore(e.score,r.getKeyword())}n&&(n.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(r,e.marks)})),n.results=rs(n.results),(0,p.dispatch)("yoast-seo/editor").setReadabilityResults(n.results),(0,p.dispatch)("yoast-seo/editor").setOverallReadabilityScore(n.score)),i&&(i.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(r,e.marks)})),i.results=rs(i.results),(0,p.dispatch)("yoast-seo/editor").setInclusiveLanguageResults(i.results),(0,p.dispatch)("yoast-seo/editor").setOverallInclusiveLanguageScore(i.score)),(0,h.doAction)("yoast.analysis.run",t,{paper:r})}catch(e){}}),500);const dn=()=>{const{getContentLocale:e}=(0,p.select)("yoast-seo/editor"),t=((...e)=>()=>e.map((e=>e())))(e,cn),s=(()=>{const{setEstimatedReadingTime:e,setFleschReadingEase:t,setTextLength:s}=(0,p.dispatch)("yoast-seo/editor"),n=(0,l.get)(window,"YoastSEO.analysis.worker.runResearch",l.noop);return()=>{const r=ze.Paper.parse(cn());n("readingTime",r).then((t=>e(t.result))),n("getFleschReadingScore",r).then((e=>{e.result&&t(e.result)})),n("wordCountInText",r).then((e=>s(e.result)))}})();return setTimeout(s,1500),((e,t)=>{let s=e();return()=>{const n=e();(0,l.isEqual)(n,s)||(s=n,t((0,l.clone)(n)))}})(t,s)};window.wpseoPostScraperL10n=window.wpseoScriptData.metabox,window.wpseoShortcodePluginL10n=window.wpseoScriptData.analysis.plugins.shortcodes,window.YoastSEO=window.YoastSEO||{},i()((()=>{var t;(function(e){function t(e){e&&(e.focus(),e.click())}function s(){if(e(".wpseo-meta-section").length>0){const t=e(".wpseo-meta-section-link");e(".wpseo-metabox-menu li").filter((function(){return"#wpseo-meta-section-content"===e(this).find(".wpseo-meta-section-link").attr("href")})).addClass("active").find("[role='tab']").addClass("yoast-active-tab"),e("#wpseo-meta-section-content, .wpseo-meta-section-react").addClass("active"),t.on("click",(function(s){var n=e(this).attr("id"),r=e(this).attr("href"),i=e(r);s.preventDefault(),e(".wpseo-metabox-menu li").removeClass("active").find("[role='tab']").removeClass("yoast-active-tab"),e(".wpseo-meta-section").removeClass("active"),e(".wpseo-meta-section-react.active").removeClass("active"),"#wpseo-meta-section-content"===r&&e(".wpseo-meta-section-react").addClass("active"),i.addClass("active"),e(this).parent("li").addClass("active").find("[role='tab']").addClass("yoast-active-tab");const o=function(e,t={}){return new CustomEvent("YoastSEO:metaTabChange",{detail:t})}(0,{metaTabId:n});window.dispatchEvent(o),this&&(t.attr({"aria-selected":"false",tabIndex:"-1"}),this.removeAttribute("tabindex"),this.setAttribute("aria-selected","true"))}))}}window.wpseoInitTabs=s,window.wpseo_init_tabs=s,e(".wpseo-meta-section").each((function(t,s){e(s).find(".wpseotab:first").addClass("active")})),window.wpseo_init_tabs(),function(){const s=e(".yoast-aria-tabs"),n=s.find("[role='tab']"),r=s.attr("aria-orientation")||"horizontal";n.attr({"aria-selected":!1,tabIndex:"-1"}),n.filter(".yoast-active-tab").removeAttr("tabindex").attr("aria-selected","true"),n.on("keydown",(function(s){-1!==[32,35,36,37,38,39,40].indexOf(s.which)&&("horizontal"===r&&-1!==[38,40].indexOf(s.which)||"vertical"===r&&-1!==[37,39].indexOf(s.which)||function(s,n){const r=s.which,i=n.index(e(s.target));switch(r){case 32:s.preventDefault(),t(n[i]);break;case 35:s.preventDefault(),t(n[n.length-1]);break;case 36:s.preventDefault(),t(n[0]);break;case 37:case 38:s.preventDefault(),t(n[i-1<0?n.length-1:i-1]);break;case 39:case 40:s.preventDefault(),t(n[i+1===n.length?0:i+1])}}(s,n))}))}()})(a()),"undefined"!=typeof wpseoPrimaryCategoryL10n&&function(e){var t,s,n=wpseoPrimaryCategoryL10n.taxonomies;function r(t){return e("#yoast-wpseo-primary-"+t).val()}function i(t,s){e("#yoast-wpseo-primary-"+t).val(s).trigger("change");const n=(0,p.dispatch)("yoast-seo/editor");if(n){const r=parseInt(s,10);n.setPrimaryTaxonomyId(t,r),"category"===t&&n.updateReplacementVariable("primary_category",function(t){const s=e("#category-all").find(`#category-${t} > label`);if(0===s.length)return"";const n=s.clone();return n.children().remove(),n.text().trim()}(r))}}function o(i){var o,a,l;o=e("#"+i+'checklist input[type="checkbox"]:checked');var c=e("#"+i+"checklist li");c.removeClass("wpseo-term-unchecked wpseo-primary-term wpseo-non-primary-term"),e(".wpseo-primary-category-label").remove(),c.addClass("wpseo-term-unchecked"),o.length<=1||o.each((function(o,c){c=e(c),(a=c.closest("li")).removeClass("wpseo-term-unchecked"),1!==e(c).closest("li").children(".wpseo-make-primary-term").length&&function(s,r){var i,o;i=e(r).closest("label"),o=t({taxonomy:n[s],term:i.text()}),i.after(o)}(i,c),c.val()===r(i)?(a.addClass("wpseo-primary-term"),(l=c.closest("label")).find(".wpseo-primary-category-label").remove(),l.append(s({taxonomy:n[i]}))):a.addClass("wpseo-non-primary-term")}))}function a(t){i(t,e("#"+t+'checklist input[type="checkbox"]:checked:first').val()),o(t)}function d(e){""===r(e)&&a(e)}e.fn.initYstSEOPrimaryCategory=function(){return this.each((function(t,s){const n=e("#"+s.name+"div");var l;o(s.name),n.on("click",'input[type="checkbox"]',(l=s.name,function(){!1===e(this).prop("checked")&&e(this).val()===r(l)&&a(l),d(l),o(l)})),n.on("wpListAddEnd","#"+s.name+"checklist",function(e){return function(){d(e),o(e)}}(s.name)),n.on("click",".wpseo-make-primary-term",function(t){return function(s){var n;n=e(s.currentTarget).siblings("label").find("input"),i(t,n.val()),o(t),n.trigger("focus")}}(s.name))}))},t=wp.template("primary-term-ui"),s=wp.template("primary-term-screen-reader"),e(_.values(n)).initYstSEOPrimaryCategory(),je()&&(0,l.get)(window,"wp.hooks.addFilter",l.noop)("editor.PostTaxonomyType","yoast-seo",(e=>class extends u.Component{render(){return(0,c.createElement)(rn,{OriginalComponent:e,...this.props})}}))}(a());const s=function(){const t=(0,p.registerStore)("yoast-seo/editor",{reducer:(0,p.combineReducers)(we.reducers),selectors:we.selectors,actions:(0,l.pickBy)(we.actions,(e=>"function"==typeof e)),controls:e});return(e=>{e.dispatch(we.actions.setSettings({socialPreviews:{sitewideImage:window.wpseoScriptData.sitewideSocialImage,siteName:window.wpseoScriptData.metabox.site_name,contentImage:window.wpseoScriptData.metabox.first_content_image,twitterCardType:window.wpseoScriptData.metabox.twitterCardType},snippetEditor:{baseUrl:window.wpseoScriptData.metabox.base_url,date:window.wpseoScriptData.metabox.metaDescriptionDate,recommendedReplacementVariables:window.wpseoScriptData.analysis.plugins.replaceVars.recommended_replace_vars,siteIconUrl:window.wpseoScriptData.metabox.siteIconUrl}})),e.dispatch(we.actions.setSEMrushChangeCountry(window.wpseoScriptData.metabox.countryCode)),e.dispatch(we.actions.setSEMrushLoginStatus(window.wpseoScriptData.metabox.SEMrushLoginStatus)),e.dispatch(we.actions.setWincherLoginStatus(window.wpseoScriptData.metabox.wincherLoginStatus,!1)),e.dispatch(we.actions.setWincherWebsiteId(window.wpseoScriptData.metabox.wincherWebsiteId)),e.dispatch(we.actions.setWincherAutomaticKeyphaseTracking(window.wpseoScriptData.metabox.wincherAutoAddKeyphrases)),e.dispatch(we.actions.setDismissedAlerts((0,l.get)(window,"wpseoScriptData.dismissedAlerts",{}))),e.dispatch(we.actions.setCurrentPromotions((0,l.get)(window,"wpseoScriptData.currentPromotions",[]))),e.dispatch(we.actions.setIsPremium(Boolean((0,l.get)(window,"wpseoScriptData.metabox.isPremium",!1)))),e.dispatch(we.actions.setPostId(Number((0,l.get)(window,"wpseoScriptData.postId",null)))),e.dispatch(we.actions.setAdminUrl((0,l.get)(window,"wpseoScriptData.adminUrl",""))),e.dispatch(we.actions.setLinkParams((0,l.get)(window,"wpseoScriptData.linkParams",{}))),e.dispatch(we.actions.setPluginUrl((0,l.get)(window,"wpseoScriptData.pluginUrl",""))),e.dispatch(we.actions.setWistiaEmbedPermissionValue("1"===(0,l.get)(window,"wpseoScriptData.wistiaEmbedPermission",!1)))})(t),t}();window.yoast.initEditorIntegration(s);const n=new window.yoast.EditorData(l.noop,s);n.initialize(window.wpseoScriptData.analysis.plugins.replaceVars.replace_vars,window.wpseoScriptData.analysis.plugins.replaceVars.hidden_replace_vars),Ys(a(),s,n),null!==(t=window.wpseoScriptData)&&void 0!==t&&t.isElementorEditor||function(e){var t,s,n,r=function(e){this._app=e,this.featuredImage=null,this.pluginName="addFeaturedImagePlugin",this.registerPlugin(),this.registerModifications()};function i(){e("#yst_opengraph_image_warning").remove(),s.removeClass("yoast-opengraph-image-notice")}r.prototype.setFeaturedImage=function(e){this.featuredImage=e,this._app.pluginReloaded(this.pluginName)},r.prototype.removeFeaturedImage=function(){this.setFeaturedImage(null)},r.prototype.registerPlugin=function(){this._app.registerPlugin(this.pluginName,{status:"ready"})},r.prototype.registerModifications=function(){this._app.registerModification("content",this.addImageToContent.bind(this),this.pluginName,10)},r.prototype.addImageToContent=function(e){return null!==this.featuredImage&&(e+=this.featuredImage),e};var o=wp.media.featuredImage.frame();if("undefined"==typeof YoastSEO)return;if(t=new r(YoastSEO.app),s=e("#postimagediv"),n=s.find(".hndle"),o.on("select",(function(){var r,a,l;!function(t){var r=t.state().get("selection").first().toJSON();const o=(0,g.__)("SEO issue: The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites.","wordpress-seo");r.width<200||r.height<200?0===e("#yst_opengraph_image_warning").length&&(e('<div id="yst_opengraph_image_warning" class="notice notice-error notice-alt"><p>'+o+"</p></div>").insertAfter(n),s.addClass("yoast-opengraph-image-notice"),qe()(o,"assertive")):i()}(o),l=(a=o.state().get("selection").first()).get("alt"),r='<img src="'+a.get("url")+'" width="'+a.get("width")+'" height="'+a.get("height")+'" alt="'+l+'"/>',t.setFeaturedImage(r)})),s.on("click","#remove-post-thumbnail",(function(){t.removeFeaturedImage(),i()})),void 0!==e("#set-post-thumbnail > img").prop("src")&&t.setFeaturedImage(e("#set-post-thumbnail ").html()),!je())return;let a,l;(0,p.subscribe)((()=>{const e=(0,p.select)("core/editor").getEditedPostAttribute("featured_media");if(function(e){return"number"==typeof e&&e>0}(e)&&(a=(0,p.select)("core").getMedia(e),void 0!==a&&a!==l)){l=a;const e=`<img src="${a.source_url}" alt="${a.alt_text}" >`;t.setFeaturedImage(e)}}))}(a()),function(e){function t(){e("#copy-home-meta-description").on("click",(function(){e("#open_graph_frontpage_desc").val(e("#meta_description").val())}))}function s(){var t=e("#wpseo-conf");if(t.length){var s=t.attr("action").split("#")[0];t.attr("action",s+window.location.hash)}}function n(){var t=window.location.hash.replace("#top#","");-1!==t.search("#top")&&(t=window.location.hash.replace("#top%23","")),""!==t&&"#"!==t.charAt(0)||(t=e(".wpseotab").attr("id")),e("#"+t).addClass("active"),e("#"+t+"-tab").addClass("nav-tab-active").trigger("click")}function r(t){const s=e("#noindex-author-noposts-wpseo-container");t?s.show():s.hide()}e.fn._wpseoIsInViewport=function(){const t=e(this).offset().top,s=t+e(this).outerHeight(),n=e(window).scrollTop(),r=n+e(window).height();return t>n&&s<r},e(window).on("hashchange",(function(){n(),s()})),window.setWPOption=function(t,s,n,r){e.post(ajaxurl,{action:"wpseo_set_option",option:t,newval:s,_wpnonce:r},(function(t){t&&e("#"+n).hide()}))},window.wpseoCopyHomeMeta=t,window.wpseoSetTabHash=s,e(document).ready((function(){s(),"function"==typeof window.wpseoRedirectOldFeaturesTabToNewSettings&&window.wpseoRedirectOldFeaturesTabToNewSettings(),e("#disable-author input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#author-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change");const i=e("#noindex-author-wpseo-off"),o=e("#noindex-author-wpseo-on");i.is(":checked")||r(!1),o.on("change",(()=>{e(this).is(":checked")||r(!1)})),i.on("change",(()=>{e(this).is(":checked")||r(!0)})),e("#disable-date input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#date-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change"),e("#disable-attachment input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#media_settings").toggle("off"===e(this).val())})).trigger("change"),e("#disable-post_format").on("change",(function(){e("#post_format-titles-metas").toggle(e(this).is(":not(:checked)"))})).trigger("change"),e("#wpseo-tabs").find("a").on("click",(function(t){var s,n,r,i=!0;if(s=e(this),n=!!e("#first-time-configuration-tab").filter(".nav-tab-active").length,r=!!s.filter("#first-time-configuration-tab").length,n&&!r&&window.isStepBeingEdited&&(i=confirm((0,g.__)("There are unsaved changes in one or more steps. Leaving means that those changes may not be saved. Are you sure you want to leave?","wordpress-seo"))),i){window.isStepBeingEdited=!1,e("#wpseo-tabs").find("a").removeClass("nav-tab-active"),e(".wpseotab").removeClass("active");var o=e(this).attr("id").replace("-tab",""),a=e("#"+o);a.addClass("active"),e(this).addClass("nav-tab-active"),a.hasClass("nosave")?e("#wpseo-submit-container").hide():e("#wpseo-submit-container").show(),e(window).trigger("yoast-seo-tab-change"),"first-time-configuration"===o?(e(".notice-yoast").slideUp(),e(".yoast_premium_upsell").slideUp(),e("#sidebar-container").hide()):(e(".notice-yoast").slideDown(),e(".yoast_premium_upsell").slideDown(),e("#sidebar-container").show())}else t.preventDefault(),e("#first-time-configuration-tab").trigger("focus")})),e("#yoast-first-time-configuration-notice a").on("click",(function(){e("#first-time-configuration-tab").click()})),e("#company_or_person").on("change",(function(){var t=e(this).val();"company"===t?(e("#knowledge-graph-company").show(),e("#knowledge-graph-person").hide()):"person"===t?(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").show()):(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").hide())})).trigger("change"),e(".switch-yoast-seo input").on("keydown",(function(e){"keydown"===e.type&&13===e.which&&e.preventDefault()})),e("body").on("click","button.toggleable-container-trigger",(t=>{const s=e(t.currentTarget),n=s.parent().siblings(".toggleable-container");n.toggleClass("toggleable-container-hidden"),s.attr("aria-expanded",!n.hasClass("toggleable-container-hidden")).find("span").toggleClass("dashicons-arrow-up-alt2 dashicons-arrow-down-alt2")}));const a=e("#opengraph"),c=e("#wpseo-opengraph-settings");a.length&&c.length&&(c.toggle(a[0].checked),a.on("change",(e=>{c.toggle(e.target.checked)}))),t(),n(),function(){if(!e("#enable_xml_sitemap input[type=radio]").length)return;const t=e("#yoast-seo-sitemaps-disabled-warning");e("#enable_xml_sitemap input[type=radio]").on("change",(function(){"off"===this.value?t.show():t.hide()}))}(),function(){const t=e("#wpseo-submit-container-float"),s=e("#wpseo-submit-container-fixed");if(!t.length||!s.length)return;function n(){t._wpseoIsInViewport()?s.hide():s.show()}e(window).on("resize scroll",(0,l.debounce)(n,100)),e(window).on("yoast-seo-tab-change",n);const r=e(".wpseo-message");r.length&&window.setTimeout((()=>{r.fadeOut()}),5e3),n()}()}))}(a()),(()=>{if((0,p.select)("yoast-seo/editor").getPreference("isInsightsEnabled",!1))(0,p.dispatch)("yoast-seo/editor").loadEstimatedReadingTime(),(0,p.subscribe)((0,l.debounce)(dn(),1500,{maxWait:3e3}))})(),window.wpseoScriptData.postType&&!["attachment"].includes(window.wpseoScriptData.postType)&&(()=>{const e=(0,p.select)(fe).getIsPremium(),t=(0,p.select)(fe).getIsWooSeoUpsell(),s=(0,p.select)(fe).getIsWooSeoUpsellTerm(),n=!e||t||s;(0,h.addFilter)("yoast.replacementVariableEditor.additionalButtons","yoast/yoast-seo-premium/AiGenerator",((e,{fieldId:t})=>(n&&e.push((0,c.createElement)(d.Fill,{name:`yoast.replacementVariableEditor.additionalButtons.${t}`},(0,c.createElement)(ye,{fieldId:t}))),e)))})()}))})()})();