(()=>{var e={6746:(e,t,s)=>{"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=i(s(9196)),a=i(s(9156)),o=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,o,i,d,u,p,m,h,g=[],y={};for(p=0;p<e.length;p++)if("string"!==(u=e[p]).type){if(!t.hasOwnProperty(u.value)||void 0===t[u.value])throw new Error("Invalid interpolation, missing component node: `"+u.value+"`");if("object"!==r(t[u.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+u.value+"`","\n> "+l);if("componentClose"===u.type)throw new Error("Missing opening component token: `"+u.value+"`");if("componentOpen"===u.type){s=t[u.value],i=p;break}g.push(t[u.value])}else g.push(u.value);return s&&(d=function(e,t){var s,r,n=t[e],a=0;for(r=e+1;r<t.length;r++)if((s=t[r]).value===n.value){if("componentOpen"===s.type){a++;continue}if("componentClose"===s.type){if(0===a)return r;a--}}throw new Error("Missing closing component token `"+n.value+"`")}(i,e),m=c(e.slice(i+1,d),t),o=n.default.cloneElement(s,{},m),g.push(o),d<e.length-1&&(h=c(e.slice(d+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,a.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,n=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":r(s))){if(n)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var a=(0,o.default)(t);try{return c(a,s)}catch(e){if(n)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{"use strict";var r=s(9196),n="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,a=s(7942),o=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,u="@@iterator";function p(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,r={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var s,r}function m(e,t,s,r){var a,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===n)return s(r,e,""===t?l+p(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(a=e[y],g+p(a,y),s,r);else{var f=function(e){var t=e&&(d&&e[d]||e[u]);if("function"==typeof t)return t}(e);if(f)for(var b,w=f.call(e),E=0;!(b=w.next()).done;)h+=m(a=b.value,g+p(a,E++),s,r);else if("object"===i){var v=""+e;o(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===v?"object with keys {"+Object.keys(e).join(", ")+"}":v,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,b=w,w=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function E(e,t,s,r){this.result=e,this.keyPrefix=t,this.func=s,this.context=r,this.count=0}function v(e,t,s){var n,o,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,u=c.call(d,t,e.count++);Array.isArray(u)?k(u,i,s,a.thatReturnsArgument):null!=u&&(r.isValidElement(u)&&(n=u,o=l+(!u.key||t&&t.key===u.key?"":g(u.key)+"/")+s,u=r.cloneElement(n,{key:o},void 0!==n.props?n.props.children:void 0)),i.push(u))}function k(e,t,s,r,n){var a="";null!=s&&(a=g(s)+"/");var o=E.getPooled(t,a,r,n);!function(e,t,s){null==e||m(e,"",t,s)}(e,v,o),E.release(o)}E.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,r){var n=this;if(n.instancePool.length){var a=n.instancePool.pop();return n.call(a,e,t,s,r),a}return new n(e,t,s,r)},(f=E).instancePool=[],f.getPooled=y||b,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;o(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;o(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)k(e[s],t,s,a.thatReturnsArgument);return t}},7942:e=>{"use strict";function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{"use strict";e.exports=function(e,t,s,r,n,a,o,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,r,n,a,o,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{"use strict";var r=s(7942);e.exports=r},4530:(e,t)=>{var s;!function(){"use strict";var r={}.hasOwnProperty;function n(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var a=typeof s;if("string"===a||"number"===a)e.push(s);else if(Array.isArray(s)){if(s.length){var o=n.apply(null,s);o&&e.push(o)}}else if("object"===a){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)r.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(n.default=n,e.exports=n):void 0===(s=function(){return n}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React}},t={};function s(r){var n=t[r];if(void 0!==n)return n.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,s),a.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=s(9196),t=s.n(e);const r=window.wp.element,n=window.wp.components,a=window.yoast.propTypes;var o=s.n(a);const i=window.lodash,l=window.yoast.styledComponents;var c=s.n(l);const d=window.yoast.externals.contexts,u=({theme:t,location:s,children:r})=>(0,e.createElement)(d.LocationProvider,{value:s},(0,e.createElement)(l.ThemeProvider,{theme:t},r));u.propTypes={theme:o().object.isRequired,location:o().oneOf(["sidebar","metabox","modal"]).isRequired,children:o().element.isRequired};const p=u,m=window.yoast.uiLibrary,h=window.wp.data;const g=window.wp.i18n,y=(e,t)=>{try{return(0,r.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}};o().string.isRequired;const f=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),b=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),w=({learnMoreLink:t,thumbnail:s,wistiaEmbedPermission:r,upsellLink:n,isProductCopy:a,title:o,upsellLabel:i,newToText:l,bundleNote:c,ctbId:d})=>{const{onClose:u,initialFocus:p}=(0,m.useModalContext)(),h={a:(0,e.createElement)(O,{href:t,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,e.createElement)(b,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,e.createElement)("div",{className:"yst-relative yst-w-full"},(0,e.createElement)(ue,{videoId:"vmrahpfjxp",thumbnail:s,wistiaEmbedPermission:r}),(0,e.createElement)(m.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,e.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,e.createElement)("span",{className:"yst-logo-icon"}),l))),(0,e.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,e.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},o),(0,e.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},y(a?(0,g.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,g.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,g.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,g.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,e.createElement)(m.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:p,"data-action":"load-nfd-ctb","data-ctb-id":d},(0,e.createElement)(f,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),i,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")))),c,(0,e.createElement)(m.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:u},(0,g.__)("Close","wordpress-seo"))))};w.propTypes={learnMoreLink:o().string.isRequired,upsellLink:o().string.isRequired,thumbnail:o().shape({src:o().string.isRequired,width:o().string,height:o().string}).isRequired,wistiaEmbedPermission:o().shape({value:o().bool.isRequired,status:o().string.isRequired,set:o().func.isRequired}).isRequired,title:o().string,upsellLabel:o().string,newToText:o().string,isProductCopy:o().bool,bundleNote:o().oneOfType([o().string,o().element]),ctbId:o().string},w.defaultProps={title:(0,g.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const E=({learnMoreLink:t,thumbnail:s,wistiaEmbedPermission:r,upsellLink:n,upsellLabel:a,newToText:o,bundleNote:i,ctbId:l})=>{const{onClose:c,initialFocus:d}=(0,m.useModalContext)(),u={a:(0,e.createElement)(O,{href:t,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,e.createElement)(b,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,e.createElement)("br",null)};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,e.createElement)("div",{className:"yst-relative yst-w-full"},(0,e.createElement)(ue,{videoId:"vun9z1dpfh",thumbnail:s,wistiaEmbedPermission:r}),(0,e.createElement)(m.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,g.__)("Beta","wordpress-seo-premium"))),(0,e.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,e.createElement)("span",{className:"yst-logo-icon"}),o))),(0,e.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,e.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,g.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,g.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,e.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},y((0,g.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,g.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),u))),(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,e.createElement)(m.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:d,"data-action":"load-nfd-ctb","data-ctb-id":l},(0,e.createElement)(f,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),a,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")))),i,(0,e.createElement)(m.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:c},(0,g.__)("Close","wordpress-seo"))))};E.propTypes={learnMoreLink:o().string.isRequired,upsellLink:o().string.isRequired,thumbnail:o().shape({src:o().string.isRequired,width:o().string,height:o().string}).isRequired,wistiaEmbedPermission:o().shape({value:o().bool.isRequired,status:o().string.isRequired,set:o().func.isRequired}).isRequired,upsellLabel:o().string,newToText:o().string,bundleNote:o().oneOfType([o().string,o().element]),ctbId:o().string},E.defaultProps={upsellLabel:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const v=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-flex yst-gap-2"},(0,e.createElement)(m.Button,{onClick:t},(0,g.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(m.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,g.__)("Contact support","wordpress-seo")));v.propTypes={handleRefreshClick:o().func.isRequired,supportLink:o().string.isRequired};const k=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,e.createElement)(m.Button,{className:"yst-order-last",onClick:t},(0,g.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(m.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,g.__)("Contact support","wordpress-seo")));k.propTypes={handleRefreshClick:o().func.isRequired,supportLink:o().string.isRequired};const _=({error:t,children:s})=>(0,e.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,e.createElement)(m.Title,null,(0,g.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,e.createElement)("p",null,(0,g.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,e.createElement)(m.Alert,{variant:"error"},(null==t?void 0:t.message)||(0,g.__)("Undefined error message.","wordpress-seo")),(0,e.createElement)("p",null,(0,g.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),s);_.propTypes={error:o().object.isRequired,children:o().node},_.VerticalButtons=k,_.HorizontalButtons=v;var x;function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},T.apply(this,arguments)}o().string,o().node.isRequired,o().node.isRequired,o().node,o().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const S=t=>e.createElement("svg",T({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},t),x||(x=e.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),R=window.ReactDOM;var I,C,L;(C=I||(I={})).Pop="POP",C.Push="PUSH",C.Replace="REPLACE",function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(L||(L={})),new Set(["lazy","caseSensitive","path","id","index","children"]),Error;const P=["post","put","patch","delete"],A=(new Set(P),["get",...P]);new Set(A),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred"),e.Component,e.startTransition,new Promise((()=>{})),e.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}var F,q,M,N;new Map,e.startTransition,R.flushSync,e.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(N=F||(F={})).UseScrollRestoration="useScrollRestoration",N.UseSubmit="useSubmit",N.UseSubmitFetcher="useSubmitFetcher",N.UseFetcher="useFetcher",N.useViewTransitionState="useViewTransitionState",(M=q||(q={})).UseFetcher="useFetcher",M.UseFetchers="useFetchers",M.UseScrollRestoration="useScrollRestoration",o().string.isRequired,o().string;const O=({href:t,children:s,...r})=>(0,e.createElement)(m.Link,{target:"_blank",rel:"noopener noreferrer",...r,href:t},s,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,g.__)("(Opens in a new browser tab)","wordpress-seo")));O.propTypes={href:o().string.isRequired,children:o().node},O.defaultProps={children:null};const D=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var $,U,B;function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},W.apply(this,arguments)}const j=t=>e.createElement("svg",W({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},t),$||($=e.createElement("defs",null,e.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),U||(U=e.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),B||(B=e.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),e.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},H.apply(this,arguments)}const Y=t=>e.createElement("svg",H({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},t),e.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var K,z,V,G,Z,X,Q,J,ee;function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},te.apply(this,arguments)}const se=t=>e.createElement("svg",te({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},t),K||(K=e.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),z||(z=e.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),V||(V=e.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),G||(G=e.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),Z||(Z=e.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),X||(X=e.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),Q||(Q=e.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),J||(J=e.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),ee||(ee=e.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),re=({link:t,linkProps:s,isPromotionActive:n})=>{let a=(0,r.useMemo)((()=>(0,g.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),o=y((0,g.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,g.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,e.createElement)("span",{className:"yst-whitespace-nowrap"})});const i=n("black-friday-2024-promotion");return i&&(a=(0,r.useMemo)((()=>(0,g.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),o=y((0,g.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,g.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,e.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,e.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,e.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,e.createElement)(se,null)),i&&(0,e.createElement)("div",{className:"sidebar__sale_banner_container"},(0,e.createElement)("div",{className:"sidebar__sale_banner"},(0,e.createElement)("span",{className:"banner_text"},(0,g.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,e.createElement)(m.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},o),(0,e.createElement)("p",{className:"yst-mt-2"},a),(0,e.createElement)(m.Button,{as:"a",variant:"upsell",href:t,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...s},(0,e.createElement)("span",null,i?(0,g.__)("Buy now","wordpress-seo"):o),(0,e.createElement)(D,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,e.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,g.__)("30-day money back guarantee.","wordpress-seo")),(0,e.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,e.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,e.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,g.__)("Read reviews from real users","wordpress-seo")),(0,e.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,e.createElement)(S,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)("span",{className:"yst-flex yst-gap-1"},(0,e.createElement)(Y,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(Y,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(Y,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(Y,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(j,{className:"yst-w-5 yst-h-5"})),(0,e.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};re.propTypes={link:o().string.isRequired,linkProps:o().object,isPromotionActive:o().func},re.defaultProps={linkProps:{},isPromotionActive:i.noop};const ne=()=>[(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,g.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,g.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],ae=({premiumLink:t,premiumUpsellConfig:s,isPromotionActive:r})=>{const n=r("black-friday-2024-promotion");return(0,e.createElement)(m.Paper,{as:"div",className:"xl:yst-max-w-3xl"},n&&(0,e.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,e.createElement)("div",null,(0,g.__)("30% OFF","wordpress-seo")),(0,e.createElement)("div",null,(0,g.__)("BLACK FRIDAY","wordpress-seo"))),(0,e.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,e.createElement)(m.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,g.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,g.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,e.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},ne().map(((t,s)=>(0,e.createElement)("li",{key:`upsell-benefit-${s}`},y(t,{strong:(0,e.createElement)("span",{className:"yst-font-semibold"})}))))),(0,e.createElement)(m.Button,{as:"a",variant:"upsell",size:"extra-large",href:t,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...s},n?(0,g.__)("Claim your 30% off now!","wordpress-seo"):(0,g.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,g.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,e.createElement)(D,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};ae.propTypes={premiumLink:o().string.isRequired,premiumUpsellConfig:o().object,isPromotionActive:o().func},ae.defaultProps={premiumUpsellConfig:{},isPromotionActive:i.noop},o().string.isRequired,o().object.isRequired,o().string.isRequired,o().func.isRequired,e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),o().bool.isRequired,o().func,o().func,o().string.isRequired,o().string.isRequired,o().string.isRequired,o().string.isRequired;const oe=window.yoast.reactHelmet,ie="loading",le="showPlay",ce="askPermission",de="isPlaying",ue=({videoId:t,thumbnail:s,wistiaEmbedPermission:n})=>{const[a,o]=(0,r.useState)(n.value?de:le),i=(0,r.useCallback)((()=>o(de)),[o]),l=(0,r.useCallback)((()=>{n.value?i():o(ce)}),[n.value,i,o]),c=(0,r.useCallback)((()=>o(le)),[o]),d=(0,r.useCallback)((()=>{n.set(!0),i()}),[n.set,i]);return(0,e.createElement)(e.Fragment,null,n.value&&(0,e.createElement)(oe.Helmet,null,(0,e.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,e.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},a===le&&(0,e.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:l},(0,e.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...s})),a===ce&&(0,e.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,e.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},n.status===ie&&(0,e.createElement)(m.Spinner,null),n.status!==ie&&(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,g.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,e.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,e.createElement)(m.Button,{type:"button",variant:"secondary",onClick:c,disabled:n.status===ie},(0,g.__)("Deny","wordpress-seo")),(0,e.createElement)(m.Button,{type:"button",variant:"primary",onClick:d,disabled:n.status===ie},(0,g.__)("Allow","wordpress-seo")))),n.value&&a===de&&(0,e.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===t&&(0,e.createElement)(m.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==t&&(0,e.createElement)("div",{className:`wistia_embed wistia_async_${t} videoFoam=true`}))))};ue.propTypes={videoId:o().string.isRequired,thumbnail:o().shape({src:o().string.isRequired,width:o().string,height:o().string}).isRequired,wistiaEmbedPermission:o().shape({value:o().bool.isRequired,status:o().string.isRequired,set:o().func.isRequired}).isRequired},e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),o().bool.isRequired,o().func.isRequired,o().func,o().string;const pe=window.yoast.componentsNew,me=window.yoast.styleGuide,he=window.yoast.analysis;function ge(e){switch(e){case"loading":return{icon:"loading-spinner",color:me.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:me.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:me.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:me.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:me.colors.$color_ok};default:return{icon:"seo-score-bad",color:me.colors.$color_red}}}function ye({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,r.createPortal)(t,s):null}ye.propTypes={target:o().oneOfType([o().string,o().object]).isRequired,children:o().node.isRequired};const fe=({target:t,scoreIndicator:s})=>(0,e.createElement)(ye,{target:t},(0,e.createElement)(pe.SvgIcon,{...ge(s)}));fe.propTypes={target:o().string.isRequired,scoreIndicator:o().string.isRequired};const be=fe,we=({error:t})=>{const s=(0,r.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),n=(0,h.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/metabox-error-support")),[]),a=(0,h.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,r.useEffect)((()=>{document.querySelectorAll('[id^="wpseo-meta-tab-"]').forEach((e=>{!function(e){const t=document.querySelector(`#${e}`);null!==t&&(t.style.opacity="0.5",t.style.pointerEvents="none",t.setAttribute("aria-disabled","true"),t.classList.contains("yoast-active-tab")&&t.classList.remove("yoast-active-tab"))}(e.id)}))}),[]),(0,e.createElement)(m.Root,{context:{isRtl:a}},(0,e.createElement)(_,{error:t},(0,e.createElement)(_.HorizontalButtons,{supportLink:n,handleRefreshClick:s}),(0,e.createElement)(be,{target:"wpseo-seo-score-icon",scoreIndicator:"not-set"}),(0,e.createElement)(be,{target:"wpseo-readability-score-icon",scoreIndicator:"not-set"}),(0,e.createElement)(be,{target:"wpseo-inclusive-language-score-icon",scoreIndicator:"not-set"})))};function Ee({theme:t}){return(0,e.createElement)(p,{theme:t,location:"metabox"},(0,e.createElement)(m.ErrorBoundary,{FallbackComponent:we},(0,e.createElement)(n.Slot,{name:"YoastMetabox"},(e=>{return void 0===(t=e).length?t:(0,i.flatten)(t).sort(((e,t)=>void 0===e.props.renderPriority?1:e.props.renderPriority-t.props.renderPriority));var t}))))}we.propTypes={error:o().object.isRequired};const ve=window.wp.compose,ke=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}))}));var _e=s(4530),xe=s.n(_e);const Te=({className:t,...s})=>(0,e.createElement)("span",{className:xe()("yst-grow yst-overflow-hidden yst-overflow-ellipsis yst-whitespace-nowrap yst-font-wp","yst-text-[#555] yst-text-base yst-leading-[normal] yst-subpixel-antialiased yst-text-start",t),...s});Te.displayName="MetaboxButton.Text",Te.propTypes={className:o().string},Te.defaultProps={className:""};const Se=({className:t,...s})=>(0,e.createElement)("button",{type:"button",className:xe()("yst-flex yst-items-center yst-w-full yst-pt-4 yst-pb-4 yst-pe-4 yst-ps-6 yst-space-x-2 rtl:yst-space-x-reverse","yst-border-t yst-border-t-[rgb(0,0,0,0.2)] yst-rounded-none yst-transition-all hover:yst-bg-[#f0f0f0]","focus:yst-outline focus:yst-outline-[1px] focus:yst-outline-[color:#0066cd] focus:-yst-outline-offset-1 focus:yst-shadow-[0_0_3px_rgba(8,74,103,0.8)]",t),...s});Se.propTypes={className:o().string},Se.defaultProps={className:""},Se.Text=Te;const Re=window.yoast.helpers,Ie=c().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,Ce=c().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`,Le=(c()(pe.Icon)`
	float: ${(0,Re.getDirectionalStyle)("right","left")};
	margin: ${(0,Re.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),Pe=t=>{const{title:s,className:r,showYoastIcon:a,additionalClassName:o,...i}=t,l=a?(0,e.createElement)("span",{className:"yoast-icon"}):null;return(0,e.createElement)(n.Modal,{title:s,className:`${r} ${o}`,icon:l,...i},t.children)};Pe.propTypes={title:o().string,className:o().string,showYoastIcon:o().bool,children:o().oneOfType([o().node,o().arrayOf(o().node)]),additionalClassName:o().string},Pe.defaultProps={title:"Yoast SEO",className:Le,showYoastIcon:!0,children:null,additionalClassName:""};const Ae=Pe;var Fe,qe;function Me(){return Me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Me.apply(this,arguments)}const Ne=t=>e.createElement("svg",Me({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 425 456.27"},t),Fe||(Fe=e.createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"})),qe||(qe=e.createElement("path",{stroke:"#000",strokeMiterlimit:10,strokeWidth:3.81,d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z"}))),Oe=t=>(0,e.createElement)("div",{className:"yoast components-panel__body"},(0,e.createElement)("h2",{className:"components-panel__body-title"},(0,e.createElement)("button",{id:t.id,onClick:t.onClick,className:"components-button components-panel__body-toggle",type:"button"},t.prefixIcon&&(0,e.createElement)("span",{className:"yoast-icon-span",style:{fill:`${t.prefixIcon&&t.prefixIcon.color||""}`}},(0,e.createElement)(pe.SvgIcon,{size:t.prefixIcon.size,icon:t.prefixIcon.icon})),(0,e.createElement)("span",{className:"yoast-title-container"},(0,e.createElement)("div",{className:"yoast-title"},t.title),(0,e.createElement)("div",{className:"yoast-subtitle"},t.subTitle)),t.children,t.suffixIcon&&(0,e.createElement)(pe.SvgIcon,{size:t.suffixIcon.size,icon:t.suffixIcon.icon}),t.SuffixHeroIcon))),De=Oe;Oe.propTypes={onClick:o().func.isRequired,title:o().string.isRequired,id:o().string,subTitle:o().string,suffixIcon:o().object,SuffixHeroIcon:o().object,prefixIcon:o().object,children:o().node},Oe.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const $e=window.moment;var Ue=s.n($e),Be=s(6746);const We=(0,Re.makeOutboundLink)(),je=t=>{const s=(0,g.sprintf)(/* translators: %1$d expands to the amount of allowed keyphrases on a free account, %2$s expands to a link to Wincher plans. */
(0,g.__)("You've reached the maximum amount of %1$d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %2$s.","wordpress-seo"),t.limit,"{{updateWincherPlanLink/}}");return(0,e.createElement)(pe.Alert,{type:"error"},(0,Be.Z)({mixedString:s,components:{updateWincherPlanLink:(0,e.createElement)(We,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,g.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,g.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};je.propTypes={limit:o().number},je.defaultProps={limit:10};const He=je,Ye=()=>(0,e.createElement)(pe.Alert,{type:"error"},(0,g.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo")),Ke=window.wp.apiFetch;var ze=s.n(Ke);async function Ve(e,t,s,r=200){try{const n=await e();return!!n&&(n.status===r?t(n):s(n))}catch(e){console.error(e.message)}}async function Ge(e){try{return await ze()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function Ze(e){return(0,i.isArray)(e)||(e=[e]),await Ge({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const Xe=c().p`
	color: ${me.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,Qe=c()(pe.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,Je=c().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,et=c().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,tt=c().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${me.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,st=e=>{const[t,s]=(0,r.useState)(null);return(0,r.useEffect)((()=>{e&&!t&&async function(){return await Ge({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};st.propTypes={limit:o().bool.isRequired};const rt=({limit:t,usage:s,isTitleShortened:r,isFreeAccount:n})=>{const a=(0,g.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,g.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),s,t),o=(0,g.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,g.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),s,t),i=n?a:o,l=(0,g.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,g.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),s,t),c=r?l:i;return(0,e.createElement)(Xe,null,r&&(0,e.createElement)(Qe,{icon:"exclamation-triangle",color:me.colors.$color_pink_dark,size:"14px"}),c)};rt.propTypes={limit:o().number.isRequired,usage:o().number.isRequired,isTitleShortened:o().bool,isFreeAccount:o().bool};const nt=(0,Re.makeOutboundLink)(),at=({discount:t,months:s})=>{const r=(0,e.createElement)(nt,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,g.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,g.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!t||!s)return(0,e.createElement)(et,null,r);const n=100*t,a=(0,g.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,g.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",n+"%",s);return(0,e.createElement)(et,null,(0,Be.Z)({mixedString:a,components:{wincherAccountUpgradeLink:r}}))};at.propTypes={discount:o().number,months:o().number};const ot=({onClose:t,isTitleShortened:s,trackingInfo:n})=>{const a=(()=>{const[e,t]=(0,r.useState)(null);return(0,r.useEffect)((()=>{e||async function(){return await Ge({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===n)return null;const{limit:o,usage:i}=n;if(!(o&&i/o>=.8))return null;const l=Boolean(null==a?void 0:a.discount);return(0,e.createElement)(tt,{isTitleShortened:s},t&&(0,e.createElement)(Je,{type:"button","aria-label":(0,g.__)("Close the upgrade callout","wordpress-seo"),onClick:t},(0,e.createElement)(pe.SvgIcon,{icon:"times-circle",color:me.colors.$color_pink_dark,size:"14px"})),(0,e.createElement)(rt,{...n,isTitleShortened:s,isFreeAccount:l}),(0,e.createElement)(at,{discount:null==a?void 0:a.discount,months:null==a?void 0:a.months}))};ot.propTypes={onClose:o().func,isTitleShortened:o().bool,trackingInfo:o().object};const it=ot,lt=()=>(0,e.createElement)(pe.Alert,{type:"success"},(0,g.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,g.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),ct=()=>(0,e.createElement)(pe.Alert,{type:"info"},(0,g.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,g.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),dt=({data:t,mapChartDataToTableData:s,dataTableCaption:r,dataTableHeaderLabels:n,isDataTableVisuallyHidden:a})=>t.length!==n.length?(0,e.createElement)("p",null,(0,g.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,e.createElement)("div",{className:a?"screen-reader-text":null},(0,e.createElement)("table",null,(0,e.createElement)("caption",null,r),(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,n.map(((t,s)=>(0,e.createElement)("th",{key:s},t))))),(0,e.createElement)("tbody",null,(0,e.createElement)("tr",null,t.map(((t,r)=>(0,e.createElement)("td",{key:r},s(t.y))))))));dt.propTypes={data:o().arrayOf(o().shape({x:o().number,y:o().number})).isRequired,mapChartDataToTableData:o().func,dataTableCaption:o().string.isRequired,dataTableHeaderLabels:o().array.isRequired,isDataTableVisuallyHidden:o().bool},dt.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const ut=dt,pt=({data:t,width:s,height:n,fillColor:a,strokeColor:o,strokeWidth:i,className:l,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:u,isDataTableVisuallyHidden:p})=>{const m=Math.max(1,Math.max(...t.map((e=>e.x)))),h=Math.max(1,Math.max(...t.map((e=>e.y)))),g=n-i,y=t.map((e=>`${e.x/m*s},${g-e.y/h*g+i}`)).join(" "),f=`0,${g+i} `+y+` ${s},${g+i}`;return(0,e.createElement)(r.Fragment,null,(0,e.createElement)("svg",{width:s,height:n,viewBox:`0 0 ${s} ${n}`,className:l,role:"img","aria-hidden":"true",focusable:"false"},(0,e.createElement)("polygon",{fill:a,points:f}),(0,e.createElement)("polyline",{fill:"none",stroke:o,strokeWidth:i,strokeLinejoin:"round",strokeLinecap:"round",points:y})),c&&(0,e.createElement)(ut,{data:t,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:u,isDataTableVisuallyHidden:p}))};pt.propTypes={data:o().arrayOf(o().shape({x:o().number,y:o().number})).isRequired,width:o().number.isRequired,height:o().number.isRequired,fillColor:o().string,strokeColor:o().string,strokeWidth:o().number,className:o().string,mapChartDataToTableData:o().func,dataTableCaption:o().string.isRequired,dataTableHeaderLabels:o().array.isRequired,isDataTableVisuallyHidden:o().bool},pt.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const mt=pt,ht=()=>(0,e.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,g.__)("Tracking the ranking position…","wordpress-seo")," ",(0,e.createElement)(pe.SvgIcon,{icon:"loading-spinner"})),gt=c()(pe.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,yt=c().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,ft=c().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,bt=c().td`
	padding-left: 2px !important;
`,wt=c().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,Et=c().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,vt=c().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,kt=c().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function _t(e){return Math.round(100*e)}function xt({chartData:t}){if((0,i.isEmpty)(t)||(0,i.isEmpty)(t.position))return"?";const s=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,g.sprintf)((0,g._n)("%d day","%d days",e,"wordpress-seo"),e)))}(t),r=t.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,e.createElement)(mt,{width:66,height:24,data:r,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:_t,dataTableCaption:(0,g.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:s})}xt.propTypes={chartData:o().object},xt.defaultProps={chartData:{}};const Tt=({rowData:t})=>{var s;if(null==t||null===(s=t.position)||void 0===s||!s.change)return(0,e.createElement)(xt,{chartData:t});const n=t.position.change<0;return(0,e.createElement)(r.Fragment,null,(0,e.createElement)(xt,{chartData:t}),(0,e.createElement)(yt,{isImproving:n},Math.abs(t.position.change)),(0,e.createElement)(gt,{icon:"caret-right",color:n?"#69AB56":"#DC3332",size:"14px",isImproving:n}))};function St(t){var s;const{keyphrase:n,rowData:a,onTrackKeyphrase:o,onUntrackKeyphrase:l,isFocusKeyphrase:c,isDisabled:d,isLoading:u,isSelected:p,onSelectKeyphrases:m}=t,h=!(0,i.isEmpty)(a),y=!(0,i.isEmpty)(null==a||null===(s=a.position)||void 0===s?void 0:s.history),f=(0,r.useCallback)((()=>{d||(h?l(n,a.id):o(n))}),[n,o,l,h,a,d]),b=(0,r.useCallback)((()=>{m((e=>p?e.filter((e=>e!==n)):e.concat(n)))}),[m,p,n]);return(0,e.createElement)(kt,{isEnabled:h},(0,e.createElement)(ft,null,y&&(0,e.createElement)(pe.Checkbox,{id:"select-"+n,onChange:b,checked:p,label:""})),(0,e.createElement)(bt,null,n,c&&(0,e.createElement)("span",null,"*")),function(t){const{rowData:s,websiteId:n,keyphrase:a,onSelectKeyphrases:o}=t,l=(0,r.useCallback)((()=>{o([a])}),[o,a]),c=!(0,i.isEmpty)(s),d=s&&s.updated_at&&Ue()(s.updated_at)>=Ue()().subtract(7,"days"),u=s?`https://app.wincher.com/websites/${n}/keywords?serp=${s.id}&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast`:null;return c?d?(0,e.createElement)(r.Fragment,null,(0,e.createElement)("td",null,(0,e.createElement)(Et,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(s),(0,e.createElement)(pe.ButtonStyledLink,{variant:"secondary",href:u,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,g.__)("View","wordpress-seo")))),(0,e.createElement)("td",{className:"yoast-table--nopadding"},(0,e.createElement)(vt,{type:"button",onClick:l},(0,e.createElement)(Tt,{rowData:s}))),(0,e.createElement)("td",null,(p=s.updated_at,Ue()(p).fromNow()))):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)(ht,null)):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)("i",null,(0,g.__)("Activate tracking to show the ranking position","wordpress-seo")));var p}(t),(0,e.createElement)(wt,null,function({keyphrase:t,isEnabled:s,toggleAction:r,isLoading:n}){return n?(0,e.createElement)(pe.SvgIcon,{icon:"loading-spinner"}):(0,e.createElement)(pe.Toggle,{id:`toggle-keyphrase-tracking-${t}`,className:"wincher-toggle",isEnabled:s,onSetToggleState:r,showToggleStateLabel:!1})}({keyphrase:n,isEnabled:h,toggleAction:f,isLoading:u})))}Tt.propTypes={rowData:o().object},St.propTypes={rowData:o().object,keyphrase:o().string.isRequired,onTrackKeyphrase:o().func,onUntrackKeyphrase:o().func,isFocusKeyphrase:o().bool,isDisabled:o().bool,isLoading:o().bool,websiteId:o().string,isSelected:o().bool.isRequired,onSelectKeyphrases:o().func.isRequired},St.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const Rt=(0,Re.makeOutboundLink)(),It=c().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,Re.getDirectionalStyle)("right","left")}: 8px;
	}
`,Ct=c().div`
	width: 100%;
	overflow-y: auto;
`,Lt=c().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Pt=c().th`
	padding-left: 2px !important;
`,At=e=>{const t=(0,r.useRef)();return(0,r.useEffect)((()=>{t.current=e})),t.current},Ft=(0,i.debounce)((async function(e=null,t=null,s=null,r){return await Ge({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:r})}),500,{leading:!0}),qt=t=>{const{addTrackedKeyphrase:s,isLoggedIn:n,keyphrases:a,permalink:o,removeTrackedKeyphrase:l,setKeyphraseLimitReached:c,setRequestFailed:d,setRequestSucceeded:u,setTrackedKeyphrases:p,setHasTrackedAll:m,trackAll:h,trackedKeyphrases:y,isNewlyAuthenticated:f,websiteId:b,focusKeyphrase:w,newRequest:E,startAt:v,selectedKeyphrases:k,onSelectKeyphrases:_}=t,x=(0,r.useRef)(),T=(0,r.useRef)(),S=(0,r.useRef)(!1),[R,I]=(0,r.useState)([]),C=(0,r.useCallback)((e=>{const t=e.toLowerCase();return y&&!(0,i.isEmpty)(y)&&y.hasOwnProperty(t)?y[t]:null}),[y]),L=(0,r.useMemo)((()=>async()=>{await Ve((()=>(T.current&&T.current.abort(),T.current="undefined"==typeof AbortController?null:new AbortController,Ft(a,v,o,T.current.signal))),(e=>{u(e),p(e.results)}),(e=>{d(e)}))}),[u,d,p,a,o,v]),P=(0,r.useCallback)((async e=>{const t=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));I((e=>[...e,...t])),await Ve((()=>Ze(t)),(e=>{u(e),s(e.results),L()}),(e=>{400===e.status&&e.limit&&c(e.limit),d(e)}),201),I((e=>(0,i.without)(e,...t)))}),[u,d,c,s,L]),A=(0,r.useCallback)((async(e,t)=>{e=e.toLowerCase(),I((t=>[...t,e])),await Ve((()=>async function(e){return await Ge({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{u(t),l(e)}),(e=>{d(e)})),I((t=>(0,i.without)(t,e)))}),[u,l,d]),F=(0,r.useCallback)((async e=>{E(),await P(e)}),[E,P]),q=At(o),M=At(a),N=At(v),O=o&&v;(0,r.useEffect)((()=>{n&&O&&(o!==q||(0,i.difference)(a,M).length||v!==N)&&L()}),[n,o,q,a,M,L,O,v,N]),(0,r.useEffect)((()=>{if(n&&h&&null!==y){const e=a.filter((e=>!C(e)));e.length&&P(e),m()}}),[n,h,y,P,m,C,a]),(0,r.useEffect)((()=>{f&&!S.current&&(L(),S.current=!0)}),[f,L]),(0,r.useEffect)((()=>{if(n&&!(0,i.isEmpty)(y))return(0,i.filter)(y,(e=>(0,i.isEmpty)(e.updated_at))).length>0&&(x.current=setInterval((()=>{L()}),1e4)),()=>{clearInterval(x.current)}}),[n,y,L]);const D=n&&null===y,$=(0,r.useMemo)((()=>(0,i.isEmpty)(y)?[]:Object.values(y).filter((e=>{var t;return!(0,i.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[y]),U=(0,r.useMemo)((()=>k.length>0&&$.length>0&&$.every((e=>k.includes(e)))),[k,$]),B=(0,r.useCallback)((()=>{_(U?[]:$)}),[_,U,$]),W=(0,r.useMemo)((()=>(0,i.orderBy)(a,[e=>Object.values(y||{}).map((e=>e.keyword)).includes(e)],["desc"])),[a,y]);return a&&!(0,i.isEmpty)(a)&&(0,e.createElement)(r.Fragment,null,(0,e.createElement)(Ct,null,(0,e.createElement)("table",{className:"yoast yoast-table"},(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,(0,e.createElement)(Lt,{isDisabled:0===$.length},(0,e.createElement)(pe.Checkbox,{id:"select-all",onChange:B,checked:U,label:""})),(0,e.createElement)(Pt,{scope:"col",abbr:(0,g.__)("Keyphrase","wordpress-seo")},(0,g.__)("Keyphrase","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,g.__)("Position","wordpress-seo")},(0,g.__)("Position","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,g.__)("Position over time","wordpress-seo")},(0,g.__)("Position over time","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,g.__)("Last updated","wordpress-seo")},(0,g.__)("Last updated","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,g.__)("Tracking","wordpress-seo")},(0,g.__)("Tracking","wordpress-seo")))),(0,e.createElement)("tbody",null,W.map(((t,s)=>(0,e.createElement)(St,{key:`trackable-keyphrase-${s}`,keyphrase:t,onTrackKeyphrase:F,onUntrackKeyphrase:A,rowData:C(t),isFocusKeyphrase:t===w.trim().toLowerCase(),websiteId:b,isDisabled:!n,isLoading:D||R.indexOf(t.toLowerCase())>=0,isSelected:k.includes(t),onSelectKeyphrases:_})))))),(0,e.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,e.createElement)(Rt,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,g.sprintf)(/* translators: %s expands to Wincher */
(0,g.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,e.createElement)(It,null,(0,g.__)("* focus keyphrase","wordpress-seo"))))};qt.propTypes={addTrackedKeyphrase:o().func.isRequired,isLoggedIn:o().bool,isNewlyAuthenticated:o().bool,keyphrases:o().array,newRequest:o().func.isRequired,removeTrackedKeyphrase:o().func.isRequired,setRequestFailed:o().func.isRequired,setKeyphraseLimitReached:o().func.isRequired,setRequestSucceeded:o().func.isRequired,setTrackedKeyphrases:o().func.isRequired,setHasTrackedAll:o().func.isRequired,trackAll:o().bool,trackedKeyphrases:o().object,websiteId:o().string,permalink:o().string.isRequired,focusKeyphrase:o().string,startAt:o().string,selectedKeyphrases:o().arrayOf(o().string).isRequired,onSelectKeyphrases:o().func.isRequired},qt.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const Mt=qt,Nt=(0,ve.compose)([(0,h.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:r,getWincherPermalink:n,getFocusKeyphrase:a,isWincherNewlyAuthenticated:o,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:a(),keyphrases:s(),isLoggedIn:r(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:o(),permalink:n()}})),(0,h.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherSetKeyphraseLimitReached:n,setWincherTrackedKeyphrases:a,setWincherTrackingForKeyphrase:o,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},setKeyphraseLimitReached:e=>{n(e)},addTrackedKeyphrase:e=>{o(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{a(e)},setHasTrackedAll:()=>{i(!1)}}}))])(Mt),Ot=(0,Re.makeOutboundLink)(),Dt=(0,Re.makeOutboundLink)(),$t=()=>{const t=(0,g.sprintf)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
(0,g.__)("With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,e.createElement)("p",null,(0,Be.Z)({mixedString:t,components:{wincherLink:(0,e.createElement)(Ot,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,e.createElement)(Dt,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,g.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},Ut=()=>(0,e.createElement)(pe.Alert,{type:"error"},(0,g.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),Bt=()=>(0,e.createElement)(pe.Alert,{type:"info"},(0,g.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,g.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class Wt{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,r=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,r.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:r}=e;r===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const jt=t=>{const s=(0,g.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,g.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,e.createElement)(pe.Alert,{type:"error",className:t.className},(0,Be.Z)({mixedString:s,components:{reconnectToWincher:(0,e.createElement)("a",{href:"#",onClick:e=>{e.preventDefault(),t.onReconnect()}},(0,g.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,g.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};jt.propTypes={onReconnect:o().func.isRequired,className:o().string},jt.defaultProps={className:""};const Ht=jt,Yt=()=>(0,e.createElement)(pe.Alert,{type:"error"},(0,g.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),Kt=window.yoast["chart.js"],zt="label";function Vt(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function Gt(e,t){e.labels=t}function Zt(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:zt;const r=[];e.datasets=t.map((t=>{const n=e.datasets.find((e=>e[s]===t[s]));return n&&t.data&&!r.includes(n)?(r.push(n),Object.assign(n,t),n):{...t}}))}function Xt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:zt;const s={labels:[],datasets:[]};return Gt(s,e.labels),Zt(s,e.datasets,t),s}function Qt(t,s){const{height:r=150,width:n=300,redraw:a=!1,datasetIdKey:o,type:i,data:l,options:c,plugins:d=[],fallbackContent:u,updateMode:p,...m}=t,h=(0,e.useRef)(null),g=(0,e.useRef)(),y=()=>{h.current&&(g.current=new Kt.Chart(h.current,{type:i,data:Xt(l,o),options:c&&{...c},plugins:d}),Vt(s,g.current))},f=()=>{Vt(s,null),g.current&&(g.current.destroy(),g.current=null)};return(0,e.useEffect)((()=>{!a&&g.current&&c&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(g.current,c)}),[a,c]),(0,e.useEffect)((()=>{!a&&g.current&&Gt(g.current.config.data,l.labels)}),[a,l.labels]),(0,e.useEffect)((()=>{!a&&g.current&&l.datasets&&Zt(g.current.config.data,l.datasets,o)}),[a,l.datasets]),(0,e.useEffect)((()=>{g.current&&(a?(f(),setTimeout(y)):g.current.update(p))}),[a,c,l.labels,l.datasets,p]),(0,e.useEffect)((()=>{g.current&&(f(),setTimeout(y))}),[i]),(0,e.useEffect)((()=>(y(),()=>f())),[]),e.createElement("canvas",Object.assign({ref:h,role:"img",height:r,width:n},m),u)}const Jt=(0,e.forwardRef)(Qt);function es(t,s){return Kt.Chart.register(s),(0,e.forwardRef)(((s,r)=>e.createElement(Jt,Object.assign({},s,{ref:r,type:t}))))}const ts=es("line",Kt.LineController),ss={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};Kt._adapters._date.override("function"==typeof Ue()?{_id:"moment",formats:function(){return ss},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=Ue()(e,t):e instanceof Ue()||(e=Ue()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return Ue()(e).format(t)},add:function(e,t,s){return Ue()(e).add(t,s).valueOf()},diff:function(e,t,s){return Ue()(e).diff(Ue()(t),s)},startOf:function(e,t,s){return e=Ue()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return Ue()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const rs=["top","right","bottom","left"];function ns(e,t,s){const r={};s=s?"-"+s:"";for(let n=0;n<4;n++){const a=rs[n];r[a]=parseFloat(e[t+"-"+a+s])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),Kt.Chart.register(Kt.CategoryScale,Kt.LineController,Kt.LineElement,Kt.PointElement,Kt.LinearScale,Kt.TimeScale,Kt.Legend,Kt.Tooltip);const as=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function os({datasets:t,isChartShown:s,keyphrases:n}){if(!s)return null;const a=(0,r.useMemo)((()=>Object.fromEntries([...n].sort().map(((e,t)=>[e,as[t%as.length]])))),[n]),o=t.map((e=>{const t=a[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,e.createElement)(ts,{height:100,data:{datasets:o},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:i.noop},tooltip:{enabled:!0,callbacks:{title:e=>Ue()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}Kt.Interaction.modes.xPoint=(e,t,s,r)=>{const n=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:r}=t,n=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),a="border-box"===n.boxSizing,o=ns(n,"padding"),i=ns(n,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,r=s&&s.length?s[0]:e,{offsetX:n,offsetY:a}=r;let o,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(n,a,e.target))o=n,i=a;else{const e=t.getBoundingClientRect();o=r.clientX-e.left,i=r.clientY-e.top,l=!0}return{x:o,y:i,box:l}}(e,s),u=o.left+(d&&i.left),p=o.top+(d&&i.top);var m;let{width:h,height:g}=t;return a&&(h-=o.width+i.width,g-=o.height+i.height),{x:Math.round((l-u)/h*s.width/r),y:Math.round((c-p)/g*s.height/r)}}(t,e);let a=[];if(Kt.Interaction.evaluateInteractionItems(e,"x",n,((e,t,s)=>{e.inXRange(n.x,r)&&a.push({element:e,datasetIndex:t,index:s})})),0===a.length)return a;const o=a.reduce(((e,t)=>Math.abs(n.x-e.element.x)<Math.abs(n.x-t.element.x)?e:t)).element.x;return a=a.filter((e=>e.element.x===o)),a.some((e=>Math.abs(e.element.y-n.y)<10))?a:[]},os.propTypes={datasets:o().arrayOf(o().shape({label:o().string.isRequired,data:o().arrayOf(o().shape({datetime:o().string.isRequired,value:o().number.isRequired})).isRequired,selected:o().bool})).isRequired,isChartShown:o().bool.isRequired,keyphrases:o().array.isRequired};const is=({response:t,onLogin:s})=>[401,403,404].includes(t.status)?(0,e.createElement)(Ht,{onReconnect:s}):(0,e.createElement)(Ye,null);is.propTypes={response:o().object.isRequired,onLogin:o().func.isRequired};const ls=({isSuccess:t,response:s,allKeyphrasesMissRanking:r,onLogin:n,keyphraseLimitReached:a,limit:o})=>a?(0,e.createElement)(He,{limit:o}):(0,i.isEmpty)(s)||t?r?(0,e.createElement)(ct,null):null:(0,e.createElement)(is,{response:s,onLogin:n});ls.propTypes={isSuccess:o().bool.isRequired,allKeyphrasesMissRanking:o().bool.isRequired,response:o().object,onLogin:o().func.isRequired,keyphraseLimitReached:o().bool.isRequired,limit:o().number.isRequired},ls.defaultProps={response:{}};let cs=null;const ds=async e=>{if(cs&&!cs.isClosed())return void cs.focus();const{url:t}=await async function(){return await Ge({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();cs=new Wt(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:r,setRequestFailed:n,keyphrases:a,addTrackedKeyphrase:o,setKeyphraseLimitReached:i}=e;await Ve((()=>async function(e){const{code:t,websiteId:s}=e;return await Ge({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),r(e);const l=(Array.isArray(a)?a:[a]).map((e=>e.toLowerCase()));await Ve((()=>Ze(l)),(e=>{r(e),o(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),n(e)}),201);const c=cs.getPopup();c&&c.close()}),(async e=>n(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),cs.createPopup()},us=t=>t.isLoggedIn?null:(0,e.createElement)("p",null,(0,e.createElement)(pe.NewButton,{onClick:t.onLogin,variant:"primary"},(0,g.sprintf)(/* translators: %s expands to Wincher */
(0,g.__)("Connect with %s","wordpress-seo"),"Wincher")));us.propTypes={isLoggedIn:o().bool.isRequired,onLogin:o().func.isRequired};const ps=c().div`
	p {
		margin: 1em 0;
	}
`,ms=c().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,hs=c().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,gs=c().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,ys=c().div`
	margin: 8px 0;
`,fs=Ue().utc().startOf("day"),bs=[{name:(0,g.__)("Last day","wordpress-seo"),value:Ue()(fs).subtract(1,"days").format(),defaultIndex:1},{name:(0,g.__)("Last week","wordpress-seo"),value:Ue()(fs).subtract(1,"week").format(),defaultIndex:2},{name:(0,g.__)("Last month","wordpress-seo"),value:Ue()(fs).subtract(1,"month").format(),defaultIndex:3},{name:(0,g.__)("Last year","wordpress-seo"),value:Ue()(fs).subtract(1,"year").format(),defaultIndex:0}],ws=t=>{const{onSelect:s,selected:r,options:n,isLoggedIn:a}=t;return a?n.length<1?null:(0,e.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==r?void 0:r.value)||n[0].value,onChange:s},n.map((t=>(0,e.createElement)("option",{key:t.name,value:t.value},t.name)))):null};ws.propTypes={onSelect:o().func.isRequired,selected:o().object,options:o().array.isRequired,isLoggedIn:o().bool.isRequired};const Es=t=>{const{trackedKeyphrases:s,isLoggedIn:n,keyphrases:a,shouldTrackAll:o,permalink:l,historyDaysLimit:c}=t;if(!l&&n)return(0,e.createElement)(Yt,null);if(0===a.length)return(0,e.createElement)(Ut,null);const d=Ue()(fs).subtract(c,"days"),u=bs.filter((e=>Ue()(e.value).isSameOrAfter(d))),p=(0,i.orderBy)(u,(e=>e.defaultIndex),"desc")[0],[m,h]=(0,r.useState)(p),[y,f]=(0,r.useState)([]),b=y.length>0,w=(0,ve.usePrevious)(s);(0,r.useEffect)((()=>{if(!(0,i.isEmpty)(s)&&(0,i.difference)(Object.keys(s),Object.keys(w||[])).length){const e=Object.values(s).map((e=>e.keyword));f(e)}}),[s,w]),(0,r.useEffect)((()=>{h(p)}),[null==p?void 0:p.name]);const E=(0,r.useCallback)((e=>{const t=bs.find((t=>t.value===e.target.value));t&&h(t)}),[h]),v=(0,r.useMemo)((()=>(0,i.isEmpty)(y)||(0,i.isEmpty)(s)?[]:Object.values(s).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:y.includes(e.keyword)&&!(0,i.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[y,s]);return(0,e.createElement)(ms,{isDisabled:!n},(0,e.createElement)("p",null,(0,g.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),n&&o&&(0,e.createElement)(Bt,null),(0,e.createElement)(gs,null,(0,e.createElement)(ws,{selected:m,onSelect:E,options:u,isLoggedIn:n})),(0,e.createElement)(ys,null,(0,e.createElement)(os,{isChartShown:b,datasets:v,keyphrases:a})),(0,e.createElement)(Nt,{startAt:null==m?void 0:m.value,selectedKeyphrases:y,onSelectKeyphrases:f,trackedKeyphrases:s}))};function vs(t){const{isNewlyAuthenticated:s,isLoggedIn:n}=t,a=(0,r.useCallback)((()=>{ds(t)}),[ds,t]),o=st(n);return(0,e.createElement)(ps,null,s&&(0,e.createElement)(lt,null),n&&(0,e.createElement)(it,{trackingInfo:o}),(0,e.createElement)(hs,null,(0,g.__)("SEO performance","wordpress-seo"),(0,e.createElement)(pe.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,e.createElement)($t,null),(0,e.createElement)(us,{isLoggedIn:n,onLogin:a}),(0,e.createElement)(ls,{...t,onLogin:a}),(0,e.createElement)(Es,{...t,historyDaysLimit:(null==o?void 0:o.historyDays)||31}))}Es.propTypes={trackedKeyphrases:o().object,keyphrases:o().array.isRequired,isLoggedIn:o().bool.isRequired,shouldTrackAll:o().bool.isRequired,permalink:o().string.isRequired,historyDaysLimit:o().number},vs.propTypes={trackedKeyphrases:o().object,addTrackedKeyphrase:o().func.isRequired,isLoggedIn:o().bool,isNewlyAuthenticated:o().bool,keyphrases:o().array,response:o().object,shouldTrackAll:o().bool,permalink:o().string,historyDaysLimit:o().number},vs.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const ks=(0,ve.compose)([(0,h.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:r,getWincherHistoryDaysLimit:n,getWincherLoginStatus:a,getWincherRequestIsSuccess:o,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:u,shouldWincherAutomaticallyTrackAll:p}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:a(),isNewlyAuthenticated:t(),isSuccess:o(),keyphraseLimitReached:s(),limit:r(),response:i(),shouldTrackAll:p(),permalink:u(),historyDaysLimit:n()}})),(0,h.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherTrackingForKeyphrase:n,setWincherSetKeyphraseLimitReached:a,setWincherLoginStatus:o}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},addTrackedKeyphrase:e=>{n(e)},setKeyphraseLimitReached:e=>{a(e)},onAuthentication:(e,s,r)=>{t(r),o(e,s)}}}))])(vs),_s=c()(ke)`
	width: 18px;
	height: 18px;
	margin: 3px;
`;function xs(e){const{keyphrases:t,onNoKeyphraseSet:s,onOpen:r,location:n}=e;if(!t.length){let e=document.querySelector("#focus-keyword-input-metabox");return e||(e=document.querySelector("#focus-keyword-input-sidebar")),e.focus(),void s()}r(n)}function Ts(t){const{location:s,whichModalOpen:n,shouldCloseOnClickOutside:a}=t,o=(0,r.useCallback)((()=>{xs(t)}),[xs,t]),i=(0,g.__)("Track SEO performance","wordpress-seo"),l=((t=null)=>(0,e.useMemo)((()=>{const e={role:"img","aria-hidden":"true"};return null!==t&&(e.focusable=t?"true":"false"),e}),[t]))();return(0,e.createElement)(r.Fragment,null,n===s&&(0,e.createElement)(Ae,{title:i,onRequestClose:t.onClose,icon:(0,e.createElement)(Ne,null),additionalClassName:"yoast-wincher-seo-performance-modal yoast-gutenberg-modal__no-padding",shouldCloseOnClickOutside:a},(0,e.createElement)(Ie,{className:"yoast-gutenberg-modal__content yoast-wincher-seo-performance-modal__content"},(0,e.createElement)(ks,null))),"sidebar"===s&&(0,e.createElement)(De,{id:`wincher-open-button-${s}`,title:i,SuffixHeroIcon:(0,e.createElement)(_s,{className:"yst-text-slate-500",...l}),onClick:o}),"metabox"===s&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(Se,{id:`wincher-open-button-${s}`,onClick:o},(0,e.createElement)(Se.Text,null,i),(0,e.createElement)(ke,{className:"yst-h-5 yst-w-5 yst-text-slate-500",...l}))))}Ts.propTypes={location:o().string,whichModalOpen:o().oneOf(["none","metabox","sidebar","postpublish"]),shouldCloseOnClickOutside:o().bool,keyphrases:o().array.isRequired,onNoKeyphraseSet:o().func.isRequired,onOpen:o().func.isRequired,onClose:o().func.isRequired},Ts.defaultProps={location:"",whichModalOpen:"none",shouldCloseOnClickOutside:!0};const Ss=(0,ve.compose)([(0,h.withSelect)((e=>{const{getWincherModalOpen:t,getWincherTrackableKeyphrases:s}=e("yoast-seo/editor");return{keyphrases:s(),whichModalOpen:t()}})),(0,h.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherDismissModal:s,setWincherNoKeyphrase:r}=e("yoast-seo/editor");return{onOpen:e=>{t(e)},onClose:()=>{s()},onNoKeyphraseSet:()=>{r()}}}))])(Ts),Rs=window.yoast.externals.components;function Is(){return(0,ve.createHigherOrderComponent)((function(e){return(0,ve.pure)((function(t){const s=(0,r.useContext)(d.LocationContext);return(0,r.createElement)(e,{...t,location:s})}))}),"withLocation")}const Cs=(0,ve.compose)([(0,h.withSelect)((e=>{const{isCornerstoneContent:t}=e("yoast-seo/editor");return{isCornerstone:t(),learnMoreUrl:wpseoAdminL10n["shortlinks.cornerstone_content_info"]}})),(0,h.withDispatch)((e=>{const{toggleCornerstoneContent:t}=e("yoast-seo/editor");return{onChange:t}})),Is()])(Rs.CollapsibleCornerstone),Ls=window.yoast.searchMetadataPreviews,Ps=c()(pe.StyledSection)`
	&${pe.StyledSectionBase} {
		padding: 0;

		& ${pe.StyledHeading} {
			${(0,Re.getDirectionalStyle)("padding-left","padding-right")}: 20px;
			margin-left: ${(0,Re.getDirectionalStyle)("0","20px")};
		}
	}
`,As=({children:t,title:s,icon:r,hasPaperStyle:n,shoppingData:a})=>(0,e.createElement)(Ps,{headingLevel:3,headingText:s,headingIcon:r,headingIconColor:"#555",hasPaperStyle:n,shoppingData:a},t);As.propTypes={children:o().element,title:o().string,icon:o().string,hasPaperStyle:o().bool,shoppingData:o().object},As.defaultProps={hasPaperStyle:!0,shoppingData:null};const Fs=As,qs=window.wp.sanitize,Ms="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE";function Ns(e,t,s="",r=!1){const n="string"==typeof t?(0,Re.decodeHTML)(t):t;return{type:Ms,name:e,value:n,label:s,hidden:r}}function Os(e){return e.charAt(0).toUpperCase()+e.slice(1)}const{stripHTMLTags:Ds}=Re.strings,$s=["slug","content","contentImage","snippetPreviewImageURL"];function Us(e,t="_"){return e.replace(/\s/g,t)}const Bs=(0,i.memoize)(((e,t)=>0===e?i.noop:(0,i.debounce)((s=>t(s,e)),500))),Ws=({link:t,text:s})=>(0,e.createElement)(m.Root,null,(0,e.createElement)("p",null,s),(0,e.createElement)(m.Button,{href:t,as:"a",className:"yst-gap-2 yst-mb-5 yst-mt-2",variant:"upsell",target:"_blank",rel:"noopener"},(0,e.createElement)(f,{className:"yst-w-4 yst-h-4 yst--ms-1 yst-shrink-0"}),(0,g.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO")));Ws.propTypes={link:o().string.isRequired,text:o().string.isRequired};const js=Ws,Hs=function(e,t){let s=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(s=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[s]&&(e.url=e.url.slice(0,s)+e.url.slice(s+1)),function(e){const t=(0,i.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,i.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,i.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],i.identity);return{url:e.url,title:Ds(t(e.title)),description:Ds(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?Ds(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:Ds(s("data_page_title",e.title)),description:Ds(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?Ds(s("data_page_title",e.filteredSEOTitle)):""}}(e)},Ys=(0,ve.compose)([(0,h.withSelect)((function(e){const{getBaseUrlFromSettings:t,getDateFromSettings:s,getFocusKeyphrase:r,getRecommendedReplaceVars:n,getReplaceVars:a,getShoppingData:o,getSiteIconUrlFromSettings:i,getSnippetEditorData:l,getSnippetEditorMode:c,getSnippetEditorPreviewImageUrl:d,getSnippetEditorWordsToHighlight:u,isCornerstoneContent:p,getIsTerm:m,getContentLocale:h,getSiteName:g}=e("yoast-seo/editor"),y=a();return y.forEach((e=>{""!==e.value||["title","excerpt","excerpt_only"].includes(e.name)||(e.value="%%"+e.name+"%%")})),{baseUrl:t(),data:l(),date:s(),faviconSrc:i(),keyword:r(),mobileImageSrc:d(),mode:c(),recommendedReplacementVariables:n(),replacementVariables:y,shoppingData:o(),wordsToHighlight:u(),isCornerstone:p(),isTaxonomy:m(),locale:h(),siteName:g()}})),(0,h.withDispatch)((function(e,t,{select:s}){const{updateData:r,switchMode:n,updateAnalysisData:a,findCustomFields:o}=e("yoast-seo/editor"),i=e("core/editor"),l=s("yoast-seo/editor").getPostId();return{onChange:(e,t)=>{switch(e){case"mode":n(t);break;case"slug":r({slug:t}),i&&i.editPost({slug:t});break;default:r({[e]:t})}},onChangeAnalysisData:a,onReplacementVariableSearchChange:Bs(l,o)}}))])((t=>{const s=(0,h.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/product-google-preview-metabox")),[]),r=(0,h.useSelect)((e=>e("yoast-seo/editor").getIsWooSeoUpsell()),[]),n=(0,g.__)("Want an enhanced Google preview of how your WooCommerce products look in the search results?","wordpress-seo");return(0,e.createElement)(d.LocationConsumer,null,(a=>(0,e.createElement)(Fs,{icon:"eye",hasPaperStyle:t.hasPaperStyle},(0,e.createElement)(e.Fragment,null,r&&(0,e.createElement)(js,{link:s,text:n}),(0,e.createElement)(Ls.SnippetEditor,{...t,descriptionPlaceholder:(0,g.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:Hs,showCloseButton:!1,idSuffix:a})))))})),Ks=(0,h.withSelect)((e=>{const{getWarningMessage:t}=e("yoast-seo/editor");return{message:t()}}))(pe.Warning),zs=window.yoast.featureFlag,Vs=c()(pe.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,Gs=t=>(0,e.createElement)(Vs,{hasPadding:!0,hasSeparator:!0,...t}),Zs=()=>{const t=(0,h.useSelect)((e=>e("yoast-seo/editor").getEstimatedReadingTime()),[]),s=(0,r.useMemo)((()=>(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-estimated_reading_time","")),[]);return(0,e.createElement)(pe.InsightsCard,{amount:t,unit:(0,g._n)("minute","minutes",t,"wordpress-seo"),title:(0,g.__)("Reading time","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about reading time","wordpress-seo")})},Xs=(0,Re.makeOutboundLink)();function Qs(t,s,r){const n=function(e){switch(e){case he.DIFFICULTY.FAIRLY_DIFFICULT:case he.DIFFICULTY.DIFFICULT:case he.DIFFICULTY.VERY_DIFFICULT:return(0,g.__)("Try to make shorter sentences, using less difficult words to improve readability","wordpress-seo");case he.DIFFICULTY.NO_DATA:return(0,g.__)("Continue writing to get insight into the readability of your text!","wordpress-seo");default:return(0,g.__)("Good job!","wordpress-seo")}}(s);return(0,e.createElement)("span",null,function(e,t){return-1===e?(0,g.__)("Your text should be slightly longer to calculate your Flesch reading ease score.","wordpress-seo"):(0,g.sprintf)(
/* Translators: %1$s expands to the numeric Flesch reading ease score,
  %2$s expands to the easiness of reading (e.g. 'easy' or 'very difficult') */
(0,g.__)("The copy scores %1$s in the test, which is considered %2$s to read.","wordpress-seo"),e,function(e){switch(e){case he.DIFFICULTY.NO_DATA:return(0,g.__)("no data","wordpress-seo");case he.DIFFICULTY.VERY_EASY:return(0,g.__)("very easy","wordpress-seo");case he.DIFFICULTY.EASY:return(0,g.__)("easy","wordpress-seo");case he.DIFFICULTY.FAIRLY_EASY:return(0,g.__)("fairly easy","wordpress-seo");case he.DIFFICULTY.OKAY:return(0,g.__)("okay","wordpress-seo");case he.DIFFICULTY.FAIRLY_DIFFICULT:return(0,g.__)("fairly difficult","wordpress-seo");case he.DIFFICULTY.DIFFICULT:return(0,g.__)("difficult","wordpress-seo");case he.DIFFICULTY.VERY_DIFFICULT:return(0,g.__)("very difficult","wordpress-seo")}}(t))}(t,s)," ",s>=he.DIFFICULTY.FAIRLY_DIFFICULT?(0,e.createElement)(Xs,{href:r},n+"."):n)}const Js=()=>{let t=(0,h.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseScore()),[]);const s=(0,r.useMemo)((()=>(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease","")),[]),n=(0,h.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseDifficulty()),[t]),a=(0,r.useMemo)((()=>{const e=(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease_article","");return Qs(t,n,e)}),[t,n]);return-1===t&&(t="?"),(0,e.createElement)(pe.InsightsCard,{amount:t,unit:(0,g.__)("out of 100","wordpress-seo"),title:(0,g.__)("Flesch reading ease","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about Flesch reading ease","wordpress-seo"),description:a})},er=({data:t,itemScreenReaderText:s,className:n,...a})=>{const o=(0,r.useMemo)((()=>{var e,s;return null!==(e=null===(s=(0,i.maxBy)(t,"number"))||void 0===s?void 0:s.number)&&void 0!==e?e:0}),[t]);return(0,e.createElement)("ul",{className:xe()("yoast-data-model",n),...a},t.map((({name:t,number:r})=>(0,e.createElement)("li",{key:`${t}_dataItem`,style:{"--yoast-width":r/o*100+"%"}},t,(0,e.createElement)("span",null,r),s&&(0,e.createElement)("span",{className:"screen-reader-text"},(0,g.sprintf)(s,r))))))};er.propTypes={data:o().arrayOf(o().shape({name:o().string.isRequired,number:o().number.isRequired})),itemScreenReaderText:o().string,className:o().string},er.defaultProps={data:[],itemScreenReaderText:"",className:""};const tr=er,sr=window.wp.url,rr=(0,Re.makeOutboundLink)(),nr=({location:t})=>{const s=(0,h.useSelect)((e=>{var t,s;return null===(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getPreference("isProminentWordsAvailable",!1))||void 0===t||t}),[]),n=(0,h.useSelect)((e=>e("yoast-seo/editor").getPreference("shouldUpsell",!1)),[]),a=(0,r.useMemo)((()=>(0,i.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${t}-prominent_words`,"")),[t]),o=(0,r.useMemo)((()=>{const t=(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-keyword_research_link","");return y((0,g.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,g.__)("Read our %1$sultimate guide to keyword research%2$s to learn more about keyword research and keyword strategy.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)(rr,{href:t})})}),[]),l=(0,r.useMemo)((()=>y((0,g.sprintf)(
// translators: %1$s expands to a starting `b` tag, %1$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,g.__)("With %1$s%3$s%2$s, this section will show you which words occur most often in your text. By checking these prominent words against your intended keyword(s), you'll know how to edit your text to be more focused.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,e.createElement)("b",null)})),[]),c=(0,h.useSelect)((e=>{var t,s;return null!==(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getProminentWords())&&void 0!==t?t:[]}),[]),u=(0,r.useMemo)((()=>{const e=(0,g.sprintf)(
// translators: %1$s expands to Yoast SEO Premium.
(0,g.__)("Get %s to enjoy the benefits of prominent words","wordpress-seo"),"Yoast SEO Premium").split(/\s+/);return e.map(((t,s)=>({name:t,number:e.length-s})))}),[]),p=(0,r.useMemo)((()=>n?u:c.map((({word:e,occurrence:t})=>({name:e,number:t})))),[c,u]);if(!s)return null;const{locationContext:m}=(0,d.useRootContext)();return(0,e.createElement)("div",{className:"yoast-prominent-words"},(0,e.createElement)("div",{className:"yoast-field-group__title"},(0,e.createElement)("b",null,(0,g.__)("Prominent words","wordpress-seo"))),!n&&(0,e.createElement)("p",null,0===p.length?(0,g.__)("Once you add a bit more copy, we'll give you a list of words that occur the most in the content. These give an indication of what your content focuses on.","wordpress-seo"):(0,g.__)("The following words occur the most in the content. These give an indication of what your content focuses on. If the words differ a lot from your topic, you might want to rewrite your content accordingly.","wordpress-seo")),n&&(0,e.createElement)("p",null,l),n&&(0,e.createElement)(rr,{href:(0,sr.addQueryArgs)(a,{context:m}),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2",className:"yoast-button yoast-button-upsell"},(0,g.sprintf)(
// translators: %s expands to `Premium` (part of add-on name).
(0,g.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,e.createElement)("p",null,o),(0,e.createElement)(tr,{data:p,itemScreenReaderText:/* translators: Hidden accessibility text; %d expands to the number of occurrences. */
(0,g.__)("%d occurrences","wordpress-seo"),"aria-label":(0,g.__)("Prominent words","wordpress-seo"),className:n?"yoast-data-model--upsell":null}))};nr.propTypes={location:o().string.isRequired};const ar=nr,or=()=>{const t=(0,h.useSelect)((e=>e("yoast-seo/editor").getTextLength()),[]),s=(0,r.useMemo)((()=>(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-word_count","")),[]);let n=(0,g._n)("word","words",t.count,"wordpress-seo"),a=(0,g.__)("Word count","wordpress-seo"),o=(0,g.__)("Learn more about word count","wordpress-seo");return"character"===t.unit&&(n=(0,g._n)("character","characters",t.count,"wordpress-seo"),a=(0,g.__)("Character count","wordpress-seo"),
/* translators: Hidden accessibility text. */
o=(0,g.__)("Learn more about character count","wordpress-seo")),(0,e.createElement)(pe.InsightsCard,{amount:t.count,unit:n,title:a,linkTo:s,linkText:o})},ir=(0,Re.makeOutboundLink)(),lr=({location:t})=>{const s=(0,r.useMemo)((()=>(0,i.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${t}-text_formality`,"")),[t]),n=(0,r.useMemo)((()=>y((0,g.sprintf)(
// Translators: %1$s expands to a starting `b` tag, %2$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,g.__)("%1$s%3$s%2$s will help you assess the formality level of your text.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,e.createElement)("b",null)})),[]);return(0,e.createElement)(r.Fragment,null,(0,e.createElement)("div",null,(0,e.createElement)("p",null,n),(0,e.createElement)(ir,{href:s,className:"yoast-button yoast-button-upsell"},(0,g.sprintf)(
// Translators: %s expands to `Premium` (part of add-on name).
(0,g.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))))};lr.propTypes={location:o().string.isRequired};const cr=lr;function dr(){return(0,i.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}const ur=({location:t,name:s})=>{const r=(0,h.useSelect)((e=>e("yoast-seo/editor").isFormalitySupported()),[]),a=dr().isPremium,o=a?(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_premium",""):(0,i.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_free",""),l=(0,g.__)("Read more about text formality.","wordpress-seo");return r?(0,e.createElement)("div",{className:"yoast-text-formality"},(0,e.createElement)("div",{className:"yoast-field-group__title"},(0,e.createElement)("b",null,(0,g.__)("Text formality","wordpress-seo")),(0,e.createElement)(pe.HelpIcon,{linkTo:o,linkText:l})),a?(0,e.createElement)(n.Slot,{name:s}):(0,e.createElement)(cr,{location:t})):null};ur.propTypes={location:o().string.isRequired,name:o().string.isRequired};const pr=ur,mr=({location:t})=>{const s=(0,h.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]);return(0,e.createElement)(Gs,{title:(0,g.__)("Insights","wordpress-seo"),id:`yoast-insights-collapsible-${t}`,className:"yoast-insights"},(0,e.createElement)(ar,{location:t}),(0,e.createElement)("div",null,s&&(0,e.createElement)("div",{className:"yoast-insights-row"},(0,e.createElement)(Js,null)),(0,e.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,e.createElement)(Zs,null),(0,e.createElement)(or,null)),(0,zs.isFeatureEnabled)("TEXT_FORMALITY")&&(0,e.createElement)(pr,{location:t,name:"YoastTextFormalityMetabox"})))};mr.propTypes={location:o().string},mr.defaultProps={location:"metabox"};const hr=mr,gr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),yr=c().div`
  padding: 25px 32px 32px;
  color: #303030;
`,fr=c().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,br=c().span`
  display: block;
  margin-top: 4px;
`,wr=c().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Er=c().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,vr=c().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,kr=c().div`
  text-align: center;
`,_r=c().a`
  width: 100%;
`,xr=(0,Re.makeOutboundLink)(_r);class Tr extends r.Component{constructor(e){super(e)}createBenefitsList(t){return t.length>0&&(0,e.createElement)(fr,{role:"list"},t.map(((t,s)=>(0,e.createElement)("li",{key:`upsell-benefit-${s}`},y(t,{strong:(0,e.createElement)("strong",null)})))))}render(){const t=(0,h.select)("yoast-seo/editor").isPromotionActive("black-friday-2024-promotion");return(0,e.createElement)(r.Fragment,null,t&&(0,e.createElement)("div",{className:"yst-flex  yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,e.createElement)("div",{className:"yst-mx-auto"},(0,g.__)("30% OFF - BLACK FRIDAY","wordpress-seo"))),(0,e.createElement)(yr,null,(0,e.createElement)(wr,null,this.props.title),(0,e.createElement)(Er,null,this.props.description),(0,e.createElement)(kr,null,(0,e.createElement)(xr,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,e.createElement)(br,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,e.createElement)(vr,null),(0,e.createElement)(wr,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}Tr.propTypes={title:o().node,benefits:o().array,benefitsTitle:o().node,description:o().node,upsellButton:o().object,upsellButtonText:o().string.isRequired,upsellButtonLabel:o().string,upsellButtonHasCaret:o().bool},Tr.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const Sr=Tr,Rr=()=>{const[t,,,s,r]=(0,m.useToggleState)(!1),{locationContext:n}=(0,d.useRootContext)(),a=(0,m.useSvgAria)(),o=n.includes("sidebar"),i=n.includes("metabox"),l=wpseoAdminL10n[o?"shortlinks.upsell.sidebar.internal_linking_suggestions":"shortlinks.upsell.metabox.internal_linking_suggestions"];return(0,e.createElement)(e.Fragment,null,t&&(0,e.createElement)(Ae,{title:(0,g.__)("Get internal linking suggestions","wordpress-seo"),onRequestClose:r,additionalClassName:"",id:"yoast-internal-linking-suggestions-upsell",className:`${Le} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,e.createElement)(Ce,null,(0,e.createElement)(Sr,{title:(0,g.__)("Rank higher by connecting your content","wordpress-seo"),description:(0,g.sprintf)(/* translators: %s expands to Yoast SEO Premium. */
(0,g.__)("%s automatically suggests to what content you can link with easy drag-and-drop functionality, which is good for your SEO!","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:(0,g.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,g.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:ne(),upsellButtonText:(0,g.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,g.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:(0,sr.addQueryArgs)(l,{context:n}),className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,g.__)("1 year free support and updates included!","wordpress-seo")}))),o&&(0,e.createElement)(De,{id:"yoast-internal-linking-suggestions-sidebar-modal-open-button",title:(0,g.__)("Internal linking suggestions","wordpress-seo"),onClick:s},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(gr,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...a})))),i&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(Se,{id:"yoast-internal-linking-suggestions-metabox-modal-open-button",onClick:s},(0,e.createElement)(Se.Text,null,(0,g.__)("Internal linking suggestions","wordpress-seo")),(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(gr,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...a}),(0,e.createElement)("span",null,"Premium")))))},Ir=({children:t})=>(0,e.createElement)("div",null,t);Ir.propTypes={renderPriority:o().number.isRequired,children:o().node.isRequired};const Cr=Ir,Lr=({noIndex:t,onNoIndexChange:s,editorContext:n,isPrivateBlog:a})=>{const o=(e=>{const t=(0,g.__)("No","wordpress-seo"),s=(0,g.__)("Yes","wordpress-seo"),r=e.noIndex?t:s;return window.wpseoScriptData.isPost?[{name:(0,g.sprintf)(/* translators: %1$s translates to "yes" or "no", %2$s translates to the content type label in plural form */
(0,g.__)("%1$s (current default for %2$s)","wordpress-seo"),r,e.postTypeNamePlural),value:"0"},{name:t,value:"1"},{name:s,value:"2"}]:[{name:(0,g.sprintf)(/* translators: %1$s translates to "yes" or "no", %2$s translates to the content type label in plural form */
(0,g.__)("%1$s (current default for %2$s)","wordpress-seo"),r,e.postTypeNamePlural),value:"default"},{name:s,value:"index"},{name:t,value:"noindex"}]})(n);return(0,e.createElement)(d.LocationConsumer,null,(n=>(0,e.createElement)(r.Fragment,null,a&&(0,e.createElement)(pe.Alert,{type:"warning"},(0,g.__)("Even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect.","wordpress-seo")),(0,e.createElement)(pe.Select,{label:(0,g.__)("Allow search engines to show this content in search results?","wordpress-seo"),onChange:s,id:(0,Re.join)(["yoast-meta-robots-noindex",n]),options:o,selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.allow_search_engines"]
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about the no-index setting on our help page.","wordpress-seo")}))))};Lr.propTypes={noIndex:o().string.isRequired,onNoIndexChange:o().func.isRequired,editorContext:o().object.isRequired,isPrivateBlog:o().bool},Lr.defaultProps={isPrivateBlog:!1};const Pr=({noFollow:t,onNoFollowChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>{const n=(0,Re.join)(["yoast-meta-robots-nofollow",r]);return(0,e.createElement)(pe.RadioButtonGroup,{id:n,options:[{value:"0",label:"Yes"},{value:"1",label:"No"}],label:(0,g.__)("Should search engines follow links on this content?","wordpress-seo"),groupName:n,onChange:s,selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.follow_links"]
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about the no-follow setting on our help page.","wordpress-seo")})}));Pr.propTypes={noFollow:o().string.isRequired,onNoFollowChange:o().func.isRequired};const Ar=({advanced:t,onAdvancedChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>{const n=(0,Re.join)(["yoast-meta-robots-advanced",r]),a=`${n}-input`;return(0,e.createElement)(pe.MultiSelect,{label:(0,g.__)("Meta robots advanced","wordpress-seo"),onChange:s,id:n,inputId:a,options:[{name:(0,g.__)("No Image Index","wordpress-seo"),value:"noimageindex"},{name:(0,g.__)("No Archive","wordpress-seo"),value:"noarchive"},{name:(0,g.__)("No Snippet","wordpress-seo"),value:"nosnippet"}],selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.meta_robots"]
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about advanced meta robots settings on our help page.","wordpress-seo")})}));Ar.propTypes={advanced:o().array.isRequired,onAdvancedChange:o().func.isRequired};const Fr=({breadcrumbsTitle:t,onBreadcrumbsTitleChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>(0,e.createElement)(pe.TextInput,{label:(0,g.__)("Breadcrumbs Title","wordpress-seo"),id:(0,Re.join)(["yoast-breadcrumbs-title",r]),onChange:s,value:t,linkTo:wpseoAdminL10n["shortlinks.advanced.breadcrumbs_title"]
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about the breadcrumbs title setting on our help page.","wordpress-seo")})));Fr.propTypes={breadcrumbsTitle:o().string.isRequired,onBreadcrumbsTitleChange:o().func.isRequired};const qr=({canonical:t,onCanonicalChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>(0,e.createElement)(pe.TextInput,{label:(0,g.__)("Canonical URL","wordpress-seo"),id:(0,Re.join)(["yoast-canonical",r]),onChange:s,value:t,linkTo:"https://yoa.st/canonical-url"
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about canonical URLs on our help page.","wordpress-seo")})));qr.propTypes={canonical:o().string.isRequired,onCanonicalChange:o().func.isRequired};const Mr=t=>{const{noIndex:s,noFollow:n,advanced:a,breadcrumbsTitle:o,canonical:i,onNoIndexChange:l,onNoFollowChange:c,onAdvancedChange:d,onBreadcrumbsTitleChange:u,onCanonicalChange:p,onLoad:m,isLoading:h,editorContext:g,isBreadcrumbsDisabled:y,isPrivateBlog:f}=t;(0,r.useEffect)((()=>{setTimeout((()=>{h&&m()}))}));const b={noIndex:s,onNoIndexChange:l,editorContext:g,isPrivateBlog:f},w={noFollow:n,onNoFollowChange:c},E={advanced:a,onAdvancedChange:d},v={breadcrumbsTitle:o,onBreadcrumbsTitleChange:u},k={canonical:i,onCanonicalChange:p};return h?null:(0,e.createElement)(r.Fragment,null,(0,e.createElement)(Lr,{...b}),g.isPost&&(0,e.createElement)(Pr,{...w}),g.isPost&&(0,e.createElement)(Ar,{...E}),!y&&(0,e.createElement)(Fr,{...v}),(0,e.createElement)(qr,{...k}))};Mr.propTypes={noIndex:o().string.isRequired,canonical:o().string.isRequired,onNoIndexChange:o().func.isRequired,onCanonicalChange:o().func.isRequired,onLoad:o().func.isRequired,isLoading:o().bool.isRequired,editorContext:o().object.isRequired,isBreadcrumbsDisabled:o().bool.isRequired,isPrivateBlog:o().bool,advanced:o().array,onAdvancedChange:o().func,noFollow:o().string,onNoFollowChange:o().func,breadcrumbsTitle:o().string,onBreadcrumbsTitleChange:o().func},Mr.defaultProps={advanced:[],onAdvancedChange:()=>{},noFollow:"",onNoFollowChange:()=>{},breadcrumbsTitle:"",onBreadcrumbsTitleChange:()=>{},isPrivateBlog:!1};const Nr=Mr,Or=(0,ve.compose)([(0,h.withSelect)((e=>{const{getNoIndex:t,getNoFollow:s,getAdvanced:r,getBreadcrumbsTitle:n,getCanonical:a,getIsLoading:o,getEditorContext:i,getPreferences:l}=e("yoast-seo/editor"),{isBreadcrumbsDisabled:c,isPrivateBlog:d}=l();return{noIndex:t(),noFollow:s(),advanced:r(),breadcrumbsTitle:n(),canonical:a(),isLoading:o(),editorContext:i(),isBreadcrumbsDisabled:c,isPrivateBlog:d}})),(0,h.withDispatch)((e=>{const{setNoIndex:t,setNoFollow:s,setAdvanced:r,setBreadcrumbsTitle:n,setCanonical:a,loadAdvancedSettingsData:o}=e("yoast-seo/editor");return{onNoIndexChange:t,onNoFollowChange:s,onAdvancedChange:r,onBreadcrumbsTitleChange:n,onCanonicalChange:a,onLoad:o}}))])(Nr),Dr=c().p`
	color: #606770;
	flex-shrink: 0;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
	padding: 0;
	text-overflow: ellipsis;
	text-transform: uppercase;
	white-space: nowrap;
	margin: 0;
	position: ${e=>"landscape"===e.mode?"relative":"static"};
`,$r=t=>{const{siteUrl:s}=t;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:"screen-reader-text"},s),(0,e.createElement)(Dr,{"aria-hidden":"true"},(0,e.createElement)("span",null,s)))};$r.propTypes={siteUrl:o().string.isRequired};const Ur=$r,Br=window.yoast.socialMetadataForms,Wr=c().img`
	&& {
		max-width: ${e=>e.width}px;
		height: ${e=>e.height}px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: none;
	}
`,jr=c().img`
	&&{
		height: 100%;
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
`,Hr=c().div`
	padding-bottom: ${e=>e.aspectRatio}%;
`,Yr=t=>{const{imageProps:s,width:r,height:n,imageMode:a}=t;return"landscape"===a?(0,e.createElement)(Hr,{aspectRatio:s.aspectRatio},(0,e.createElement)(jr,{src:s.src,alt:s.alt})):(0,e.createElement)(Wr,{src:s.src,alt:s.alt,width:r,height:n,imageProperties:s})};function Kr(e,t,s){return"landscape"===s?{widthRatio:t.width/e.landscapeWidth,heightRatio:t.height/e.landscapeHeight}:"portrait"===s?{widthRatio:t.width/e.portraitWidth,heightRatio:t.height/e.portraitHeight}:{widthRatio:t.width/e.squareWidth,heightRatio:t.height/e.squareHeight}}function zr(e,t){return t.widthRatio<=t.heightRatio?{width:Math.round(e.width/t.widthRatio),height:Math.round(e.height/t.widthRatio)}:{width:Math.round(e.width/t.heightRatio),height:Math.round(e.height/t.heightRatio)}}async function Vr(e,t,s=!1){const r=await function(e){return new Promise(((t,s)=>{const r=new Image;r.onload=()=>{t({width:r.width,height:r.height})},r.onerror=s,r.src=e}))}(e);let n=s?"landscape":"square";"Facebook"===t&&(n=(0,Br.determineFacebookImageMode)(r));const a=function(e){return"Twitter"===e?Br.TWITTER_IMAGE_SIZES:Br.FACEBOOK_IMAGE_SIZES}(t),o=function(e,t,s){return"square"===s&&t.width===t.height?{width:e.squareWidth,height:e.squareHeight}:zr(t,Kr(e,t,s))}(a,r,n);return{mode:n,height:o.height,width:o.width}}async function Gr(e,t,s=!1){try{return{imageProperties:await Vr(e,t,s),status:"loaded"}}catch(e){return{imageProperties:null,status:"errored"}}}Yr.propTypes={imageProps:o().shape({src:o().string.isRequired,alt:o().string.isRequired,aspectRatio:o().number.isRequired}).isRequired,width:o().number.isRequired,height:o().number.isRequired,imageMode:o().string},Yr.defaultProps={imageMode:"landscape"};const Zr=c().div`
	position: relative;
	${e=>"landscape"===e.mode?`max-width: ${e.dimensions.width}`:`min-width: ${e.dimensions.width}; height: ${e.dimensions.height}`};
	overflow: hidden;
	background-color: ${me.colors.$color_white};
`,Xr=c().div`
	box-sizing: border-box;
	max-width: ${Br.FACEBOOK_IMAGE_SIZES.landscapeWidth}px;
	height: ${Br.FACEBOOK_IMAGE_SIZES.landscapeHeight}px;
	background-color: ${me.colors.$color_grey};
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class Qr extends e.Component{constructor(e){super(e),this.state={imageProperties:null,status:"loading"},this.socialMedium="Facebook",this.handleFacebookImage=this.handleFacebookImage.bind(this),this.setState=this.setState.bind(this)}async handleFacebookImage(){try{const e=await Gr(this.props.src,this.socialMedium);this.setState(e),this.props.onImageLoaded(e.imageProperties.mode||"landscape")}catch(e){this.setState(e),this.props.onImageLoaded("landscape")}}componentDidUpdate(e){e.src!==this.props.src&&this.handleFacebookImage()}componentDidMount(){this.handleFacebookImage()}retrieveContainerDimensions(e){switch(e){case"square":return{height:Br.FACEBOOK_IMAGE_SIZES.squareHeight+"px",width:Br.FACEBOOK_IMAGE_SIZES.squareWidth+"px"};case"portrait":return{height:Br.FACEBOOK_IMAGE_SIZES.portraitHeight+"px",width:Br.FACEBOOK_IMAGE_SIZES.portraitWidth+"px"};case"landscape":return{height:Br.FACEBOOK_IMAGE_SIZES.landscapeHeight+"px",width:Br.FACEBOOK_IMAGE_SIZES.landscapeWidth+"px"}}}render(){const{imageProperties:t,status:s}=this.state;if("loading"===s||""===this.props.src||"errored"===s)return(0,e.createElement)(Xr,{onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,g.__)("Select image","wordpress-seo"));const r=this.retrieveContainerDimensions(t.mode);return(0,e.createElement)(Zr,{mode:t.mode,dimensions:r,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave,onClick:this.props.onImageClick},(0,e.createElement)(Yr,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:Br.FACEBOOK_IMAGE_SIZES.aspectRatio},width:t.width,height:t.height,imageMode:t.mode}))}}Qr.propTypes={src:o().string,alt:o().string,onImageLoaded:o().func,onImageClick:o().func,onMouseEnter:o().func,onMouseLeave:o().func},Qr.defaultProps={src:"",alt:"",onImageLoaded:i.noop,onImageClick:i.noop,onMouseEnter:i.noop,onMouseLeave:i.noop};const Jr=Qr,en=c().span`
	line-height: ${20}px;
	min-height : ${20}px;
	color: #1d2129;
	font-weight: 600;
	overflow: hidden;
	font-size: 16px;
	margin: 3px 0 0;
	letter-spacing: normal;
	white-space: normal;
	flex-shrink: 0;
	cursor: pointer;
	display: -webkit-box;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;
`,tn=c().p`
	line-height: ${16}px;
	min-height : ${16}px;
	color: #606770;
	font-size: 14px;
	padding: 0;
	text-overflow: ellipsis;
	margin: 3px 0 0 0;
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;

	@media all and ( max-width: ${e=>e.maxWidth} ) {
		display: none;
	}
`,sn=e=>{switch(e){case"landscape":return"527px";case"square":case"portrait":return"369px";default:return"476px"}},rn=c().div`
	box-sizing: border-box;
	display: flex;
	flex-direction: ${e=>"landscape"===e.mode?"column":"row"};
	background-color: #f2f3f5;
	max-width: 527px;
`,nn=c().div`
	box-sizing: border-box;
	background-color: #f2f3f5;
	margin: 0;
	padding: 10px 12px;
	position: relative;
	border-bottom: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-top: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-right: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border: ${e=>"landscape"===e.mode?"1px solid #dddfe2":""};
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: ${e=>"landscape"===e.mode?"flex-start":"center"};
	font-size: 12px;
	overflow: hidden;
`;class an extends e.Component{constructor(e){super(e),this.state={imageMode:null,maxLineCount:0,descriptionLineCount:0},this.facebookTitleRef=t().createRef(),this.onImageLoaded=this.onImageLoaded.bind(this),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}onImageLoaded(e){this.setState({imageMode:e})}getTitleLineCount(){return this.facebookTitleRef.current.offsetHeight/20}maybeSetMaxLineCount(){const{imageMode:e,maxLineCount:t}=this.state,s="landscape"===e?2:5;s!==t&&this.setState({maxLineCount:s})}maybeSetDescriptionLineCount(){const{descriptionLineCount:e,maxLineCount:t,imageMode:s}=this.state,r=this.getTitleLineCount();let n=t-r;"portrait"===s&&(n=5===r?0:4),n!==e&&this.setState({descriptionLineCount:n})}componentDidUpdate(){this.maybeSetMaxLineCount(),this.maybeSetDescriptionLineCount()}render(){const{imageMode:t,maxLineCount:s,descriptionLineCount:r}=this.state;return(0,e.createElement)(rn,{id:"facebookPreview",mode:t},(0,e.createElement)(Jr,{src:this.props.imageUrl||this.props.imageFallbackUrl,alt:this.props.alt,onImageLoaded:this.onImageLoaded,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,e.createElement)(nn,{mode:t},(0,e.createElement)(Ur,{siteUrl:this.props.siteUrl,mode:t}),(0,e.createElement)(en,{ref:this.facebookTitleRef,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle,lineCount:s},this.props.title),r>0&&(0,e.createElement)(tn,{maxWidth:sn(t),onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription,lineCount:r},this.props.description)))}}an.propTypes={siteUrl:o().string.isRequired,title:o().string.isRequired,description:o().string,imageUrl:o().string,imageFallbackUrl:o().string,alt:o().string,onSelect:o().func,onImageClick:o().func,onMouseHover:o().func},an.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{}};const on=an,ln=c().div`
	text-transform: lowercase;
	color: rgb(83, 100, 113);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	fill: currentcolor;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
`,cn=t=>(0,e.createElement)(ln,null,(0,e.createElement)("span",null,t.siteUrl));cn.propTypes={siteUrl:o().string.isRequired};const dn=cn,un=(e,t=!0)=>e?`\n\t\t\tmax-width: ${Br.TWITTER_IMAGE_SIZES.landscapeWidth}px;\n\t\t\t${t?"border-bottom: 1px solid #E1E8ED;":""}\n\t\t\tborder-radius: 14px 14px 0 0;\n\t\t\t`:`\n\t\twidth: ${Br.TWITTER_IMAGE_SIZES.squareWidth}px;\n\t\t${t?"border-right: 1px solid #E1E8ED;":""}\n\t\tborder-radius: 14px 0 0 14px;\n\t\t`,pn=c().div`
	position: relative;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #e1e8ed;
	flex-shrink: 0;
	${e=>un(e.isLarge)}
`,mn=c().div`
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0;
	padding: 1em;
	text-align: center;
	font-size: 1rem;
	${e=>un(e.isLarge,!1)}
`,hn=c()(mn)`
	${e=>e.isLarge&&`height: ${Br.TWITTER_IMAGE_SIZES.landscapeHeight}px;`}
	border-top-left-radius: 14px;
	${e=>e.isLarge?"border-top-right-radius":"border-bottom-left-radius"}: 14px;
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class gn extends t().Component{constructor(e){super(e),this.state={status:"loading"},this.socialMedium="Twitter",this.handleTwitterImage=this.handleTwitterImage.bind(this),this.setState=this.setState.bind(this)}async handleTwitterImage(){if(null===this.props.src)return;const e=await Gr(this.props.src,this.socialMedium,this.props.isLarge);this.setState(e)}componentDidUpdate(e){e.src!==this.props.src&&this.handleTwitterImage()}componentDidMount(){this.handleTwitterImage()}render(){const{status:t,imageProperties:s}=this.state;return"loading"===t||""===this.props.src||"errored"===t?(0,e.createElement)(hn,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,g.__)("Select image","wordpress-seo")):(0,e.createElement)(pn,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,e.createElement)(Yr,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:Br.TWITTER_IMAGE_SIZES.aspectRatio},width:s.width,height:s.height,imageMode:s.mode}))}}gn.propTypes={isLarge:o().bool.isRequired,src:o().string,alt:o().string,onImageClick:o().func,onMouseEnter:o().func,onMouseLeave:o().func},gn.defaultProps={src:"",alt:"",onMouseEnter:i.noop,onImageClick:i.noop,onMouseLeave:i.noop};const yn=c().div`
	display: flex;
	flex-direction: column;
	padding: 12px;
	justify-content: center;
	margin: 0;
	box-sizing: border-box;
	flex: auto;
	min-width: 0px;
	gap:2px;
	> * {
		line-height:20px;
		min-height:20px;
		font-size:15px;
    }
`,fn=t=>(0,e.createElement)(yn,null,t.children);fn.propTypes={children:o().array.isRequired};const bn=fn,wn=c().p`
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(15, 20, 25);
	cursor: pointer;
`,En=c().p`
	max-height: 55px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(83, 100, 113);
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	@media all and ( max-width: ${Br.TWITTER_IMAGE_SIZES.landscapeWidth}px ) {
		display: none;
	}
`,vn=c().div`
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 400;
	line-height: 20px;
	max-width: 507px;
	border: 1px solid #E1E8ED;
	box-sizing: border-box;
	border-radius: 14px;
	color: #292F33;
	background: #FFFFFF;
	text-overflow: ellipsis;
	display: flex;

	&:hover {
		background: #f5f8fa;
		border: 1px solid rgba(136,153,166,.5);
	}
`,kn=c()(vn)`
	flex-direction: column;
	max-height: 370px;
`,xn=c()(vn)`
	flex-direction: row;
	height: 125px;
`;class Tn extends e.Component{constructor(e){super(e),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}render(){const{isLarge:t,imageUrl:s,imageFallbackUrl:r,alt:n,title:a,description:o,siteUrl:i}=this.props,l=t?kn:xn;return(0,e.createElement)(l,{id:"twitterPreview"},(0,e.createElement)(gn,{src:s||r,alt:n,isLarge:t,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,e.createElement)(bn,null,(0,e.createElement)(dn,{siteUrl:i}),(0,e.createElement)(wn,{onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle},a),(0,e.createElement)(En,{onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription},o)))}}Tn.propTypes={siteUrl:o().string.isRequired,title:o().string.isRequired,description:o().string,isLarge:o().bool,imageUrl:o().string,imageFallbackUrl:o().string,alt:o().string,onSelect:o().func,onImageClick:o().func,onMouseHover:o().func},Tn.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{},isLarge:!0};const Sn=Tn,Rn=window.yoast.replacementVariableEditor;class In extends e.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.SocialPreview="Social"===e.socialMediumName?on:Sn,this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:s,onTitleChange:r,onSelectImageClick:n,onRemoveImageClick:a,socialMediumName:o,imageWarnings:i,siteUrl:l,description:c,descriptionInputPlaceholder:d,descriptionPreviewFallback:u,imageUrl:p,imageFallbackUrl:m,alt:h,title:g,titleInputPlaceholder:y,titlePreviewFallback:f,replacementVariables:b,recommendedReplacementVariables:w,applyReplacementVariables:E,onReplacementVariableSearchChange:v,isPremium:k,isLarge:_,socialPreviewLabel:x,idSuffix:T,activeMetaTabId:S}=this.props,R=E({title:g||f,description:c||u});return(0,e.createElement)(t().Fragment,null,x&&(0,e.createElement)(pe.SimulatedLabel,null,x),(0,e.createElement)(this.SocialPreview,{onMouseHover:this.setHoveredField,onSelect:this.setActiveField,onImageClick:n,siteUrl:l,title:R.title,description:R.description,imageUrl:p,imageFallbackUrl:m,alt:h,isLarge:_,activeMetaTabId:S}),(0,e.createElement)(Br.SocialMetadataPreviewForm,{onDescriptionChange:s,socialMediumName:o,title:g,titleInputPlaceholder:y,onRemoveImageClick:a,imageSelected:!!p,imageUrl:p,imageFallbackUrl:m,onTitleChange:r,onSelectImageClick:n,description:c,descriptionInputPlaceholder:d,imageWarnings:i,replacementVariables:b,recommendedReplacementVariables:w,onReplacementVariableSearchChange:v,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:k,setEditorRef:this.setEditorRef,idSuffix:T}))}}In.propTypes={title:o().string.isRequired,onTitleChange:o().func.isRequired,description:o().string.isRequired,onDescriptionChange:o().func.isRequired,imageUrl:o().string.isRequired,imageFallbackUrl:o().string.isRequired,onSelectImageClick:o().func.isRequired,onRemoveImageClick:o().func.isRequired,socialMediumName:o().string.isRequired,alt:o().string,isPremium:o().bool,imageWarnings:o().array,isLarge:o().bool,siteUrl:o().string,descriptionInputPlaceholder:o().string,titleInputPlaceholder:o().string,descriptionPreviewFallback:o().string,titlePreviewFallback:o().string,replacementVariables:Rn.replacementVariablesShape,recommendedReplacementVariables:Rn.recommendedReplacementVariablesShape,applyReplacementVariables:o().func,onReplacementVariableSearchChange:o().func,socialPreviewLabel:o().string,idSuffix:o().string,activeMetaTabId:o().string},In.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,isLarge:!0,siteUrl:"",descriptionInputPlaceholder:"",titleInputPlaceholder:"",descriptionPreviewFallback:"",titlePreviewFallback:"",alt:"",applyReplacementVariables:e=>e,onReplacementVariableSearchChange:null,socialPreviewLabel:"",idSuffix:"",activeMetaTabId:""};const Cn={},Ln=(e,t,{log:s=console.warn}={})=>{Cn[e]||(Cn[e]=!0,s(t))},Pn=(e,t=i.noop)=>{const s={};for(const r in e)Object.hasOwn(e,r)&&Object.defineProperty(s,r,{set:s=>{e[r]=s,t("set",r,s)},get:()=>(t("get",r),e[r])});return s};Pn({squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},((e,t)=>Ln(`@yoast/social-metadata-previews/TWITTER_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "TWITTER_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`))),Pn({squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}},((e,t)=>Ln(`@yoast/social-metadata-previews/FACEBOOK_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "FACEBOOK_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`)));const An=c().div`
	max-width: calc(527px + 1.5rem);
`,Fn=t=>{const s="X"===t.socialMediumName?(0,g.__)("X share preview","wordpress-seo"):(0,g.__)("Social share preview","wordpress-seo"),{locationContext:r}=(0,m.useRootContext)();return(0,e.createElement)(m.Root,null,(0,e.createElement)(An,null,(0,e.createElement)(m.FeatureUpsell,{shouldUpsell:!0,variant:"card",cardLink:(0,sr.addQueryArgs)(wpseoAdminL10n["shortlinks.upsell.social_preview."+t.socialMediumName.toLowerCase()],{context:r}),cardText:(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,g.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,e.createElement)("div",{className:"yst-grayscale yst-opacity-50"},(0,e.createElement)(m.Label,null,s),(0,e.createElement)(on,{title:"",description:"",siteUrl:"",imageUrl:"",imageFallbackUrl:"",alt:"",onSelect:i.noop,onImageClick:i.noop,onMouseHover:i.noop})))))};Fn.propTypes={socialMediumName:o().oneOf(["Social","Twitter","X"]).isRequired};const qn=Fn;class Mn extends r.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:t,onTitleChange:s,onSelectImageClick:n,onRemoveImageClick:a,socialMediumName:o,imageWarnings:i,description:l,descriptionInputPlaceholder:c,imageUrl:d,imageFallbackUrl:u,alt:p,title:m,titleInputPlaceholder:h,replacementVariables:g,recommendedReplacementVariables:y,onReplacementVariableSearchChange:f,isPremium:b,location:w}=this.props;return(0,e.createElement)(r.Fragment,null,(0,e.createElement)(qn,{socialMediumName:o}),(0,e.createElement)(Br.SocialMetadataPreviewForm,{onDescriptionChange:t,socialMediumName:o,title:m,titleInputPlaceholder:h,onRemoveImageClick:a,imageSelected:!!d,imageUrl:d,imageFallbackUrl:u,imageAltText:p,onTitleChange:s,onSelectImageClick:n,description:l,descriptionInputPlaceholder:c,imageWarnings:i,replacementVariables:g,recommendedReplacementVariables:y,onReplacementVariableSearchChange:f,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:b,setEditorRef:this.setEditorRef,idSuffix:w}))}}Mn.propTypes={title:o().string.isRequired,onTitleChange:o().func.isRequired,description:o().string.isRequired,onDescriptionChange:o().func.isRequired,imageUrl:o().string.isRequired,imageFallbackUrl:o().string,onSelectImageClick:o().func.isRequired,onRemoveImageClick:o().func.isRequired,socialMediumName:o().string.isRequired,isPremium:o().bool,imageWarnings:o().array,descriptionInputPlaceholder:o().string,titleInputPlaceholder:o().string,replacementVariables:Rn.replacementVariablesShape,recommendedReplacementVariables:Rn.recommendedReplacementVariablesShape,onReplacementVariableSearchChange:o().func,location:o().string,alt:o().string},Mn.defaultProps={imageWarnings:[],imageFallbackUrl:"",recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,descriptionInputPlaceholder:"",titleInputPlaceholder:"",onReplacementVariableSearchChange:null,location:"",alt:""};const Nn=Mn,On=(e,t,s)=>{const[n,a]=(0,r.useState)(!1),o=(0,g.sprintf)(
/* Translators: %1$s expands to the jpg format, %2$s expands to the png format,
  %3$s expands to the webp format, %4$s expands to the gif format. */
(0,g.__)("No image was found that we can automatically set as your social image. Please use %1$s, %2$s, %3$s or %4$s formats to ensure it displays correctly on social media.","wordpress-seo"),"JPG","PNG","WEBP","GIF");return(0,r.useEffect)((()=>{a(""===t&&e.toLowerCase().endsWith(".avif"))}),[e,t]),n?[o]:s},Dn=t=>{const[s,a]=(0,r.useState)(""),o=On(t.imageFallbackUrl,t.imageUrl,t.imageWarnings),i=(0,r.useCallback)((e=>{a(e.detail.metaTabId)}),[a]);(0,r.useEffect)((()=>(setTimeout(t.onLoad),window.addEventListener("YoastSEO:metaTabChange",i),()=>{window.removeEventListener("YoastSEO:metaTabChange",i)})),[]);const l={...t,activeMetaTabId:s,imageWarnings:o};return t.isPremium?(0,e.createElement)(n.Slot,{name:`YoastFacebookPremium${t.location.charAt(0).toUpperCase()+t.location.slice(1)}`,fillProps:l}):(0,e.createElement)(Nn,{...l})};Dn.propTypes={isPremium:o().bool.isRequired,onLoad:o().func.isRequired,location:o().string.isRequired,imageFallbackUrl:o().string,imageUrl:o().string,imageWarnings:o().array},Dn.defaultProps={imageFallbackUrl:"",imageUrl:"",imageWarnings:[]};const $n=Dn;function Un(e){(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();var r;e({type:(r=s.attributes).subtype,width:r.width,height:r.height,url:r.url,id:r.id,sizes:r.sizes,alt:r.alt||r.title||r.name})})),t})(e).open()}const Bn=()=>{Un((e=>(0,h.dispatch)("yoast-seo/editor").setFacebookPreviewImage((e=>{const{width:t,height:s}=e,r=(0,Br.determineFacebookImageMode)({width:t,height:s}),n=Br.FACEBOOK_IMAGE_SIZES[r+"Width"],a=Br.FACEBOOK_IMAGE_SIZES[r+"Height"],o=Object.values(e.sizes).find((e=>e.width>=n&&e.height>=a));return{url:o?o.url:e.url,id:e.id,warnings:(0,Re.validateFacebookImage)(e),alt:e.alt||""}})(e))))},Wn=(0,ve.compose)([(0,h.withSelect)((e=>{const{getFacebookDescription:t,getDescription:s,getFacebookTitle:r,getSeoTitle:n,getFacebookImageUrl:a,getImageFallback:o,getFacebookWarnings:i,getRecommendedReplaceVars:l,getReplaceVars:c,getSiteUrl:d,getSeoTitleTemplate:u,getSeoTitleTemplateNoFallback:p,getSocialTitleTemplate:m,getSeoDescriptionTemplate:h,getSocialDescriptionTemplate:g,getReplacedExcerpt:y,getFacebookAltText:f}=e("yoast-seo/editor");return{imageUrl:a(),imageFallbackUrl:o(),recommendedReplacementVariables:l(),replacementVariables:c(),description:t(),descriptionPreviewFallback:g()||s()||h()||y()||"",title:r(),titlePreviewFallback:m()||n()||p()||u()||"",imageWarnings:i(),siteUrl:d(),isPremium:!!dr().isPremium,titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"Social",alt:f()}})),(0,h.withDispatch)(((e,t,{select:s})=>{const{setFacebookPreviewTitle:r,setFacebookPreviewDescription:n,clearFacebookPreviewImage:a,loadFacebookPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:Bn,onRemoveImageClick:a,onDescriptionChange:n,onTitleChange:r,onLoad:o,onReplacementVariableSearchChange:Bs(l,i)}})),Is()])($n),jn=t=>{const s=On(t.imageFallbackUrl,t.imageUrl,t.imageWarnings);(0,r.useEffect)((()=>{setTimeout(t.onLoad)}),[]);const a={...t,imageWarnings:s};return t.isPremium?(0,e.createElement)(n.Slot,{name:`YoastTwitterPremium${t.location.charAt(0).toUpperCase()+t.location.slice(1)}`,fillProps:a}):(0,e.createElement)(Nn,{...a})};jn.propTypes={isPremium:o().bool.isRequired,onLoad:o().func.isRequired,location:o().string.isRequired,imageFallbackUrl:o().string,imageUrl:o().string,imageWarnings:o().array},jn.defaultProps={imageFallbackUrl:"",imageUrl:"",imageWarnings:[]};const Hn=jn,Yn=()=>{Un((e=>(0,h.dispatch)("yoast-seo/editor").setTwitterPreviewImage((e=>{const t="summary"!==(0,i.get)(window,"wpseoScriptData.metabox.twitterCardType")?"landscape":"square",s=Br.TWITTER_IMAGE_SIZES[t+"Width"],r=Br.TWITTER_IMAGE_SIZES[t+"Height"],n=Object.values(e.sizes).find((e=>e.width>=s&&e.height>=r));return{url:n?n.url:e.url,id:e.id,warnings:(0,Re.validateTwitterImage)(e),alt:e.alt||""}})(e))))},Kn=(0,ve.compose)([(0,h.withSelect)((e=>{const{getTwitterDescription:t,getTwitterTitle:s,getTwitterImageUrl:r,getFacebookImageUrl:n,getFacebookTitle:a,getFacebookDescription:o,getDescription:i,getSeoTitle:l,getTwitterWarnings:c,getTwitterImageType:d,getImageFallback:u,getRecommendedReplaceVars:p,getReplaceVars:m,getSiteUrl:h,getSeoTitleTemplate:g,getSeoTitleTemplateNoFallback:y,getSocialTitleTemplate:f,getSeoDescriptionTemplate:b,getSocialDescriptionTemplate:w,getReplacedExcerpt:E,getTwitterAltText:v}=e("yoast-seo/editor");return{imageUrl:r(),imageFallbackUrl:n()||u(),recommendedReplacementVariables:p(),replacementVariables:m(),description:t(),descriptionPreviewFallback:w()||o()||i()||b()||E()||"",title:s(),titlePreviewFallback:f()||a()||l()||y()||g()||"",imageWarnings:c(),siteUrl:h(),isPremium:!!dr().isPremium,isLarge:"summary"!==d(),titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"X",alt:v()}})),(0,h.withDispatch)(((e,t,{select:s})=>{const{setTwitterPreviewTitle:r,setTwitterPreviewDescription:n,clearTwitterPreviewImage:a,loadTwitterPreviewData:o,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:Yn,onRemoveImageClick:a,onDescriptionChange:n,onTitleChange:r,onLoad:o,onReplacementVariableSearchChange:Bs(l,i)}})),Is()])(Hn),zn=c().legend`
	margin: 16px 0;
	padding: 0;
	color: ${me.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,Vn=c().legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${me.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,Gn=c().div`
	padding: 16px;
`,Zn=({useOpenGraphData:t,useTwitterData:s})=>(0,e.createElement)(r.Fragment,null,s&&t&&(0,e.createElement)(r.Fragment,null,(0,e.createElement)(Gs,{hasSeparator:!1
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,title:(0,g.__)("Social media appearance","wordpress-seo"),initialIsOpen:!0},(0,e.createElement)(Vn,null,(0,g.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(Wn,null),(0,e.createElement)(zn,null,(0,g.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),(0,e.createElement)(Gs,{title:(0,g.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,e.createElement)(Kn,null))),t&&!s&&(0,e.createElement)(Gn,null,(0,e.createElement)(Vn,null,(0,g.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(Wn,null)),!t&&s&&(0,e.createElement)(Gn,null,(0,e.createElement)(Vn,null,(0,g.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,e.createElement)(Kn,null)));Zn.propTypes={useOpenGraphData:o().bool.isRequired,useTwitterData:o().bool.isRequired};const Xn=Zn,Qn=(0,h.withSelect)((e=>{const{getPreferences:t}=e("yoast-seo/editor"),{useOpenGraphData:s,useTwitterData:r}=t();return{useOpenGraphData:s,useTwitterData:r}}))(Xn);function Jn({target:t}){return(0,e.createElement)(ye,{target:t},(0,e.createElement)(Qn,null))}Jn.propTypes={target:o().string.isRequired};const ea=(0,Re.makeOutboundLink)(),ta=c().div`
	padding: 16px;
`,sa="yoast-seo/editor";function ra({location:t,show:s}){return s?(0,e.createElement)(pe.Alert,{type:"info"},(0,g.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,g.__)("Are you working on a news article? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")+" ",(0,e.createElement)(ea,{href:window.wpseoAdminL10n[`shortlinks.upsell.${t}.news`]},(0,g.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,g.__)("Buy %s now!","wordpress-seo"),"Yoast News SEO"))):null}ra.propTypes={show:o().bool.isRequired,location:o().string.isRequired};const na=(e,t,s)=>{const r=(0,h.useSelect)((e=>e(sa).getIsProduct()),[]),n=(0,h.useSelect)((e=>e(sa).getIsWooSeoActive()),[]),a=r&&n?{name:(0,g.__)("Item Page","wordpress-seo"),value:"ItemPage"}:e.find((e=>e.value===t));return[{name:(0,g.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s expands to the current site wide default. */
(0,g.__)("Default for %1$s (%2$s)","wordpress-seo"),s,a?a.name:""),value:""},...e]},aa=e=>(0,g.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s and %3$s expand to a link to the Settings page */
(0,g.__)("You can change the default type for %1$s under Content types in the %2$sSettings%3$s.","wordpress-seo"),e,"{{link}}","{{/link}}");o().string.isRequired,o().string.isRequired,o().string.isRequired;const oa=t=>{const s=na(t.pageTypeOptions,t.defaultPageType,t.postTypeName),n=na(t.articleTypeOptions,t.defaultArticleType,t.postTypeName),a=(0,h.useSelect)((e=>e(sa).selectLink("https://yoa.st/product-schema-metabox")),[]),o=(0,h.useSelect)((e=>e(sa).getIsWooSeoUpsell()),[]),[i,l]=(0,r.useState)(t.schemaArticleTypeSelected),c=(0,g.__)("Want your products stand out in search results with rich results like price, reviews and more?","wordpress-seo"),d=(0,h.useSelect)((e=>e(sa).getIsProduct()),[]),u=(0,h.useSelect)((e=>e(sa).getIsWooSeoActive()),[]),p=(0,h.useSelect)((e=>e(sa).selectAdminLink("?page=wpseo_page_settings")),[]),m=d&&u,y=(0,r.useCallback)(((e,t)=>{l(t)}),[i]);return(0,r.useEffect)((()=>{y(null,t.schemaArticleTypeSelected)}),[t.schemaArticleTypeSelected]),(0,e.createElement)(r.Fragment,null,(0,e.createElement)(pe.FieldGroup,{label:(0,g.__)("What type of page or content is this?","wordpress-seo"),linkTo:t.additionalHelpTextLink
/* translators: Hidden accessibility text. */,linkText:(0,g.__)("Learn more about page or content types","wordpress-seo")}),o&&(0,e.createElement)(js,{link:a,text:c}),(0,e.createElement)(pe.Select,{id:(0,Re.join)(["yoast-schema-page-type",t.location]),options:s,label:(0,g.__)("Page type","wordpress-seo"),onChange:t.schemaPageTypeChange,selected:m?"ItemPage":t.schemaPageTypeSelected,disabled:m}),t.showArticleTypeInput&&(0,e.createElement)(pe.Select,{id:(0,Re.join)(["yoast-schema-article-type",t.location]),options:n,label:(0,g.__)("Article type","wordpress-seo"),onChange:t.schemaArticleTypeChange,selected:t.schemaArticleTypeSelected,onOptionFocus:y}),(0,e.createElement)(ra,{location:t.location,show:!t.isNewsEnabled&&(w=i,E=t.defaultArticleType,"NewsArticle"===w||""===w&&"NewsArticle"===E)}),t.displayFooter&&!m&&(0,e.createElement)("p",null,(f=t.postTypeName,b=p,(0,Be.Z)({mixedString:aa(f),components:{link:(0,e.createElement)("a",{href:b,target:"_blank",rel:"noreferrer"})}}))),m&&(0,e.createElement)("p",null,(0,g.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,g.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO")));var f,b,w,E},ia=o().arrayOf(o().shape({name:o().string,value:o().string}));oa.propTypes={schemaPageTypeChange:o().func,schemaPageTypeSelected:o().string,pageTypeOptions:ia.isRequired,schemaArticleTypeChange:o().func,schemaArticleTypeSelected:o().string,articleTypeOptions:ia.isRequired,showArticleTypeInput:o().bool.isRequired,additionalHelpTextLink:o().string.isRequired,helpTextLink:o().string.isRequired,helpTextTitle:o().string.isRequired,helpTextDescription:o().string.isRequired,postTypeName:o().string.isRequired,displayFooter:o().bool,defaultPageType:o().string.isRequired,defaultArticleType:o().string.isRequired,location:o().string.isRequired,isNewsEnabled:o().bool},oa.defaultProps={schemaPageTypeChange:()=>{},schemaPageTypeSelected:null,schemaArticleTypeChange:()=>{},schemaArticleTypeSelected:null,displayFooter:!1,isNewsEnabled:!1};const la=t=>t.isMetabox?(0,r.createPortal)((0,e.createElement)(ta,null,(0,e.createElement)(oa,{...t})),document.getElementById("wpseo-meta-section-schema")):(0,e.createElement)(oa,{...t});la.propTypes={showArticleTypeInput:o().bool,articleTypeLabel:o().string,additionalHelpTextLink:o().string,pageTypeLabel:o().string.isRequired,helpTextLink:o().string.isRequired,helpTextTitle:o().string.isRequired,helpTextDescription:o().string.isRequired,isMetabox:o().bool.isRequired,postTypeName:o().string.isRequired,displayFooter:o().bool,loadSchemaArticleData:o().func.isRequired,loadSchemaPageData:o().func.isRequired,location:o().string.isRequired},la.defaultProps={showArticleTypeInput:!1,articleTypeLabel:"",additionalHelpTextLink:"",displayFooter:!1};const ca=la;class da{static get articleTypeInput(){return document.getElementById("yoast_wpseo_schema_article_type")}static get defaultArticleType(){return da.articleTypeInput.getAttribute("data-default")}static get articleType(){return da.articleTypeInput.value}static set articleType(e){da.articleTypeInput.value=e}static get pageTypeInput(){return document.getElementById("yoast_wpseo_schema_page_type")}static get defaultPageType(){return da.pageTypeInput.getAttribute("data-default")}static get pageType(){return da.pageTypeInput.value}static set pageType(e){da.pageTypeInput.value=e}}const ua=t=>{const s=null!==da.articleTypeInput;(0,r.useEffect)((()=>{t.loadSchemaPageData(),s&&t.loadSchemaArticleData()}),[]);const{pageTypeOptions:n,articleTypeOptions:a}=window.wpseoScriptData.metabox.schema,o={articleTypeLabel:(0,g.__)("Article type","wordpress-seo"),pageTypeLabel:(0,g.__)("Page type","wordpress-seo"),postTypeName:window.wpseoAdminL10n.postTypeNamePlural,helpTextTitle:(0,g.__)("Yoast SEO automatically describes your pages using schema.org","wordpress-seo"),helpTextDescription:(0,g.__)("This helps search engines understand your website and your content. You can change some of your settings for this page below.","wordpress-seo"),showArticleTypeInput:s,pageTypeOptions:n,articleTypeOptions:a},i={...t,...o,...(l=t.location,"metabox"===l?{helpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.page_type"],isMetabox:!0}:{helpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.page_type"],isMetabox:!1})};var l;return(0,e.createElement)(ca,{...i})};ua.propTypes={displayFooter:o().bool.isRequired,schemaPageTypeSelected:o().string.isRequired,schemaArticleTypeSelected:o().string.isRequired,defaultArticleType:o().string.isRequired,defaultPageType:o().string.isRequired,loadSchemaPageData:o().func.isRequired,loadSchemaArticleData:o().func.isRequired,schemaPageTypeChange:o().func.isRequired,schemaArticleTypeChange:o().func.isRequired,location:o().string.isRequired};const pa=(0,ve.compose)([(0,h.withSelect)((e=>{const{getPreferences:t,getPageType:s,getDefaultPageType:r,getArticleType:n,getDefaultArticleType:a}=e("yoast-seo/editor"),{displaySchemaSettingsFooter:o,isNewsEnabled:i}=t();return{displayFooter:o,isNewsEnabled:i,schemaPageTypeSelected:s(),schemaArticleTypeSelected:n(),defaultArticleType:a(),defaultPageType:r()}})),(0,h.withDispatch)((e=>{const{setPageType:t,setArticleType:s,getSchemaPageData:r,getSchemaArticleData:n}=e("yoast-seo/editor");return{loadSchemaPageData:r,loadSchemaArticleData:n,schemaPageTypeChange:t,schemaArticleTypeChange:s}})),Is()])(ua),ma=window.yoast.relatedKeyphraseSuggestions;function ha(e){const{requestLimitReached:t,isSuccess:s,response:r,requestHasData:n,relatedKeyphrases:a}=e;return t?"requestLimitReached":!s&&function(e){return"invalid_json"===(null==e?void 0:e.code)||"fetch_error"===(null==e?void 0:e.code)||!(0,i.isEmpty)(e)&&"error"in e}(r)?"requestFailed":n?function(e){return e&&e.length>=4}(a)?"maxRelatedKeyphrases":void 0:"requestEmpty"}function ga(t){var s,n;const{keyphrase:a="",relatedKeyphrases:o=[],renderAction:i=null,requestLimitReached:l=!1,countryCode:c="us",setCountry:d,newRequest:u,response:p={},isRtl:h=!1,userLocale:g="en_US",isPending:y=!1,isPremium:f=!1,semrushUpsellLink:b="",premiumUpsellLink:w=""}=t,[E,v]=(0,r.useState)(c),k=(0,r.useCallback)((async()=>{u(c,a),v(c)}),[c,a,u]);return(0,e.createElement)(m.Root,{context:{isRtl:h}},!l&&!f&&(0,e.createElement)(ma.PremiumUpsell,{url:w,className:"yst-mb-4"}),!l&&(0,e.createElement)(ma.CountrySelector,{countryCode:c,activeCountryCode:E,onChange:d,onClick:k,className:"yst-mb-4",userLocale:g.split("_")[0]}),!y&&(0,e.createElement)(ma.UserMessage,{variant:ha(t),upsellLink:b}),(0,e.createElement)(ma.KeyphrasesTable,{relatedKeyphrases:o,columnNames:null==p||null===(s=p.results)||void 0===s?void 0:s.columnNames,data:null==p||null===(n=p.results)||void 0===n?void 0:n.rows,isPending:y,renderButton:i,className:"yst-mt-4"}))}ga.propTypes={keyphrase:o().string,relatedKeyphrases:o().array,renderAction:o().func,requestLimitReached:o().bool,countryCode:o().string.isRequired,setCountry:o().func.isRequired,newRequest:o().func.isRequired,response:o().object,isRtl:o().bool,userLocale:o().string,isPending:o().bool,isPremium:o().bool,semrushUpsellLink:o().string,premiumUpsellLink:o().string};const ya=(0,ve.compose)([(0,h.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:r,getSEMrushRequestResponse:n,getSEMrushRequestIsSuccess:a,getSEMrushIsRequestPending:o,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l,getPreference:c,getIsPremium:d,selectLinkParams:u}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:r(),response:n(),isSuccess:a(),isPending:o(),requestHasData:i(),lastRequestKeyphrase:l(),isRtl:c("isRtl",!1),userLocale:c("userLocale","en_US"),isPremium:d(),semrushUpsellLink:(0,sr.addQueryArgs)("https://yoa.st/semrush-prices",u()),premiumUpsellLink:(0,sr.addQueryArgs)("https://yoa.st/413",u())}})),(0,h.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)}}}))])(ga),fa=(0,g.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),ba=t=>{const{locationContext:s}=(0,d.useRootContext)(),r=(0,sr.addQueryArgs)(wpseoAdminL10n[t.buyLink],{context:s});return(0,e.createElement)(Sr,{title:(0,g.__)("Get more help with writing content that ranks","wordpress-seo"),description:t.description,benefitsTitle:(0,g.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,g.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:ne(),upsellButtonText:(0,g.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,g.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:r,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,g.__)("1 year of premium support and updates included!","wordpress-seo")})};ba.propTypes={buyLink:o().string.isRequired,description:o().string},ba.defaultProps={description:fa};const wa=ba,Ea=({location:t})=>{const[s,n]=(0,r.useState)(!1),a=(0,r.useCallback)((()=>n(!1)),[]),o=(0,r.useCallback)((()=>n(!0)),[]),i=(0,m.useSvgAria)();return(0,e.createElement)(r.Fragment,null,s&&(0,e.createElement)(Ae,{title:(0,g.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:a,additionalClassName:"",className:`${Le} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-modal",shouldCloseOnClickOutside:!0},(0,e.createElement)(Ce,null,(0,e.createElement)(wa,{buyLink:`shortlinks.upsell.${t}.premium_seo_analysis_button`}))),"sidebar"===t&&(0,e.createElement)(De,{id:"yoast-premium-seo-analysis-modal-open-button",title:(0,g.__)("Premium SEO analysis","wordpress-seo"),prefixIcon:{icon:"seo-score-none",color:me.colors.$color_grey},onClick:o},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(gr,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...i})))),"metabox"===t&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(Se,{id:"yoast-premium-seo-analysis-metabox-modal-open-button",onClick:o},(0,e.createElement)(pe.SvgIcon,{icon:"seo-score-none",color:me.colors.$color_grey}),(0,e.createElement)(Se.Text,null,(0,g.__)("Premium SEO analysis","wordpress-seo")),(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(gr,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...i}),(0,e.createElement)("span",null,"Premium")))))};Ea.propTypes={location:o().string},Ea.defaultProps={location:"sidebar"};const va=Ea,ka=t=>(0,e.createElement)(Sr,{title:(0,g.__)("Reach a wider audience","wordpress-seo"),description:(0,g.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:(0,g.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,g.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:ne(),upsellButtonText:(0,g.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,g.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:t.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,g.__)("1 year free support and updates included!","wordpress-seo")});ka.propTypes={buyLink:o().string.isRequired};const _a=ka,xa=()=>{const[t,,,s,n]=(0,m.useToggleState)(!1),a=(0,r.useContext)(d.LocationContext),{locationContext:o}=(0,d.useRootContext)(),i=(0,m.useSvgAria)(),l=wpseoAdminL10n["sidebar"===a.toLowerCase()?"shortlinks.upsell.sidebar.additional_button":"shortlinks.upsell.metabox.additional_button"];return(0,e.createElement)(e.Fragment,null,t&&(0,e.createElement)(Ae,{title:(0,g.__)("Add related keyphrases","wordpress-seo"),onRequestClose:n,additionalClassName:"",id:"yoast-additional-keyphrases-modal",className:`${Le} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,e.createElement)(Ce,null,(0,e.createElement)(_a,{buyLink:(0,sr.addQueryArgs)(l,{context:o})}))),"sidebar"===a&&(0,e.createElement)(De,{id:"yoast-additional-keyphrase-modal-open-button",title:(0,g.__)("Add related keyphrase","wordpress-seo"),prefixIcon:{icon:"plus",color:me.colors.$color_grey_medium_dark},onClick:s},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(gr,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...i})))),"metabox"===a&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(Se,{id:"yoast-additional-keyphrase-metabox-modal-open-button",onClick:s},(0,e.createElement)(pe.SvgIcon,{icon:"plus",color:me.colors.$color_grey_medium_dark}),(0,e.createElement)(Se.Text,null,(0,g.__)("Add related keyphrase","wordpress-seo")),(0,e.createElement)(m.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(gr,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...i}),(0,e.createElement)("span",null,"Premium")))))};var Ta,Sa,Ra,Ia,Ca,La,Pa,Aa,Fa,qa,Ma,Na,Oa,Da,$a,Ua,Ba,Wa,ja,Ha,Ya,Ka,za,Va,Ga,Za,Xa,Qa,Ja,eo,to,so,ro,no,ao,oo,io,lo,co,uo,po,mo,ho,go,yo,fo,bo;function wo(){return wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},wo.apply(this,arguments)}const Eo=t=>e.createElement("svg",wo({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 448 360"},t),Ta||(Ta=e.createElement("circle",{cx:226,cy:211,r:149,fill:"#f0ecf0"})),Sa||(Sa=e.createElement("path",{fill:"#fbd2a6",d:"M173.53 189.38s-35.47-5.3-41.78-11c-9.39-24.93-29.61-48-35.47-66.21-.71-2.24 3.72-11.39 3.53-15.41s-5.34-11.64-5.23-14-.09-15.27-.09-15.27l-4.75-.72s-5.13 6.07-3.56 9.87c-1.73-4.19 4.3 7.93.5 9.35 0 0-6-5.94-11.76-8.27s-19.57-3.65-19.57-3.65L43.19 73l-4.42.6L31 69.7l-2.85 5.12 7.53 5.29L40.86 92l17.19 10.2 10.2 10.56 9.86 3.56s26.49 79.67 45 92c17 11.33 37.23 15.92 37.23 15.92z"})),Ra||(Ra=e.createElement("path",{fill:"#a4286a",d:"M270.52 345.13c2.76-14.59 15.94-35.73 30.24-54.58 16.22-21.39 14-79.66-33.19-91.46-17.3-4.32-52.25-1-59.85-3.41C186.54 189 170 187 168 190.17c-5 10.51-7.73 27.81-5.51 36.26 1.18 4.73 3.54 5.91 20.49 13.4-5.12 15-16.35 26.3-22.86 37s7.88 27.2 7.1 33.51c-.48 3.8-4.26 21.13-7.18 34.25a149.47 149.47 0 0 0 110.3 8.66 25.66 25.66 0 0 1 .18-8.12z"})),Ia||(Ia=e.createElement("path",{fill:"#9a5815",d:"M206.76 66.43c-5 14.4-1.42 25.67-3.93 40.74-10 60.34-24.08 43.92-31.44 93.6 7.24-14.19 14.32-15.82 20.63-23.11-.83 3.09-10.25 13.75-8.05 34.81 9.85-8.51 6.35-8.75 11.86-8.54.36 3.25 3.53 3.22-3.59 10.53 2.52.69 17.42-14.32 20.16-12.66s0 5.72-6 7.76c2.15 2.2 30.47-3.87 43.81-14.71 4.93-4 10-13.16 13.38-18.2 7.17-10.62 12.38-24.77 17.71-36.6 8.94-19.87 15.09-39.34 16.11-61.31.53-10.44-3.41-18.44-4.41-28.86-2.57-27.8-67.63-37.26-86.24 16.55z"})),Ca||(Ca=e.createElement("path",{fill:"#efb17c",d:"M277.74 179.06c.62-.79 1.24-1.59 1.84-2.39-.85 2.59-1.52 3.73-1.84 2.39z"})),La||(La=e.createElement("path",{fill:"#fbd2a6",d:"M216.1 206.72c3.69-5.42 8.28-3.35 15.57-8.28 3.76-3.06 1.57-9.46 1.77-11.82 18.25 4.56 37.38-1.18 49.07-16 .62 5.16-2.77 22.27-.2 27 4.73 8.67 13.4 18.92 13.4 18.92-35.47-2.76-63.45 39-89.86 44.54 5.52-28.74-2.36-35.84 10.25-54.36z"})),Pa||(Pa=e.createElement("path",{fill:"#f6b488",d:"m235.21 167.9 53.21-25.23s-3.65 24-6.5 32.72c-64.05 62.66-46.47-7.33-46.71-7.49z"})),Aa||(Aa=e.createElement("path",{fill:"#fbd2a6",d:"M226.86 50.64C215 59.31 206.37 93.21 204 95.57c-19.46 19.47-3.59 41.39-3.94 51.24-.2 5.52-4.14 25.42 5.72 29.36 22.22 8.89 60-3.48 67.19-12.61 13.28-16.75 40.89-94.78 17.74-108.19-7.92-4.58-42.78-20.18-63.85-4.73z"})),Fa||(Fa=e.createElement("path",{fill:"#e5766c",d:"M243.69 143.66c-10.7-6.16-8.56-6.73-19.76-12.71-3.86-2.07-3.94.64-6.32 0-2.91-.79-1.39-2.74-5.37-3.48-6.52-1.21-3.67 3.63-3.15 6 1.32 6.15-8.17 17.3 3.26 21.42 12.65 4.55 21.38-9.41 31.34-11.23z"})),qa||(qa=e.createElement("path",{fill:"#fff",d:"M240.68 143.9c-11.49-5.53-11.65-8.17-24.64-11.69-8.6-2.32-5.53 1-5.69 4.42-.2 4.16-1.26 9.87 4.9 12.66 9 4.09 18.16-6.02 25.43-5.39zm.7-40.9c-.16 1.26-.06 4.9 5.46 8.25 11.43-4.73 16.36-2.56 17-3.33 1.48-1.76-2-8.87-7.88-9.85-5.58-.94-14.14 1.24-14.58 4.93z"})),Ma||(Ma=e.createElement("path",{fill:"#000001",d:"M263.53 108.19c-4.32-4.33-6.85-6.24-12.26-8.21-2.77-1-6.18.18-8.65 1.67a3.65 3.65 0 0 0-1.24 1.23h-.12a3.73 3.73 0 0 1 1-1.52 12.53 12.53 0 0 1 11.93-3c4.73 1 9.43 4.63 9.42 9.82z"})),Na||(Na=e.createElement("circle",{cx:254.13,cy:104.05,r:4.19,fill:"#000001"})),Oa||(Oa=e.createElement("path",{fill:"#fff",d:"M225.26 99.22c-.29 1-6.6 3.45-10.92 1.48-1.15-3.24-5-6.43-5.25-6.71-.5-2.86 5.55-8 10.06-6.3a10.21 10.21 0 0 1 6.11 11.53z"})),Da||(Da=e.createElement("path",{fill:"#000001",d:"M209.29 94.21c-.19-2.34 1.84-4.1 3.65-5.2 7-3.87 13.18 3 12.43 10h-.12c-.14-4-2.38-8.44-6.47-9.11a3.19 3.19 0 0 0-2.42.31c-1.37.85-2.38 2-3.89 2.56-1 .45-1.92.42-3 1.4h-.22z"})),$a||($a=e.createElement("circle",{cx:219.55,cy:95.28,r:4,fill:"#000001"})),Ua||(Ua=e.createElement("path",{fill:"#efb17c",d:"M218.66 120.27a27.32 27.32 0 0 0 4.54 3.45c-2.29-.72-4.28-.69-6.32-2.27-2.53-2-3.39-5.16-.73-7.72 10.24-9.82 12.56-13.82 14.77-24.42-1 12.37-6 17.77-10.63 23.18-2.53 2.97-4.68 5.06-1.63 7.78z"})),Ba||(Ba=e.createElement("path",{fill:"#a57c52",d:"M231.22 69.91c-.67-3.41-8.78-2.83-11.06-1.93-3.48 1.39-6.08 5.22-7.13 8.53 2.9-4.3 6.74-8.12 12.46-6 1.16.42 3.18 2.35 4.48 1.85s1.03-2.2 1.25-2.45zm32.16 8.56c-2.75-1.66-12.24-5.08-12.18.82 2.56.24 5-.19 7.64.95 11.22 4.76 12.77 17.61 12.85 17.86.2-.53.1 1.26.23.7-.02.2.95-12.12-8.54-20.33z"})),Wa||(Wa=e.createElement("path",{fill:"#fbd2a6",d:"M53.43 250.73c6.29 0-.6-.17 7.34 0 1.89.05-2.38-.7 0-.69 4.54-4.2 12.48-.74 20.6-2.45 4.55.35 3.93 1.35 5.59 4.19 4.89 8.38 4.78 14.21 14 19.56 16.42 8.38 66 12.92 88.49 18.86 5.52.83 42.64-20.15 61-23.75 6.51 10.74 11.46 28.68 8.39 34.93-6.54 13.3-57.07 25.4-75.91 25.15C156.47 326.18 94 294 92.2 293c-.94-.57.7-.7-7.68 0s-10.15.72-17.47-1.4c-3-.87-4.61-1.33-6.33-3.54-2 .22-3.39.2-4.78-1-3.15-2.74-4.84-6.61-2.73-10.06h-.12c-3.35-2.48-6.54-7.69-3.08-11.72 1-1.18 6.06-1.94 7.77-2.28-1.58-.29-6.37.19-7.49-.72-3.06-2.5-4.96-11.55 3.14-11.55z"})),ja||(ja=e.createElement("path",{fill:"#a4286a",d:"M303.22 237.52c-9.87-11.88-41.59 8.19-47.8 12.34s-14.89 17.95-14.89 17.95c6 9.43 8.36 31 5.65 46.34l30.51-3s18-15.62 22.59-28.7 6.3-42.54 6.3-42.54"})),Ha||(Ha=e.createElement("path",{fill:"#cb9833",d:"M278.63 31.67c-6.08 0-22.91 4.07-22.93 12.91 0 11 47.9 38.38 16.14 85.85 10.21-.79 10.79-8.12 14.92-14.93-3.66 77-49.38 93.58-40.51 142.25 7.68-25.81 20.3-11.62 38.13-33.84 3.45 4.88 9 18.28-9.46 33.78 50-31.26 57.31-56.6 51.92-95C319.93 113.53 348.7 42 278.63 31.67z"})),Ya||(Ya=e.createElement("path",{fill:"#fbd2a6",d:"M283.64 126.83c-2.42 9.67-8 15.76-1.48 16.46A21.26 21.26 0 0 0 302 132.6c5.17-8.52 3.93-16.44-2.46-18s-13.48 2.56-15.9 12.23z"})),Ka||(Ka=e.createElement("path",{fill:"#efb17c",d:"M38 73.45c1.92 2 4.25 9.21 6.32 10.91 2.25 1.85 5.71 2.12 8.1 4.45 3.66-2 6-8.72 10-9.31-2.59 1.31-4.42 3.5-6.93 4.88-1.42.8-3 1.31-4.38 2.25-2.16-1.46-4.27-1.77-6.26-3.38-2.52-2.02-5.31-8-6.85-9.8z"})),za||(za=e.createElement("path",{fill:"#efb17c",d:"M39 74.4c4.83 1.1 12.52 6.44 15.89 10-3.22-1.34-14.73-6.15-15.89-10zm.62-1.5c6.71-.79 18 1.54 23.29 5.9-3.85-.2-5.42-1.48-9-2.94-4.08-1.69-8.83-2.03-14.29-2.96zm46.43 14.58c-3.72-1.32-10.52-1.13-13.22 3.52 2-1.16 1.84-2.11 4.18-1.72-3.81-4.15 8.16-.74 11.6-.24m-2.78 13.15c.56-3.29-8-7.81-10.58-9.17-6.25-3.29-12.16 1.36-19.33-4.53 5.94 6.1 14.23 2.5 19.55 5.76 3.06 1.88 8.65 6.09 9.35 9.38-.23-.4 1.29-1.44 1.01-1.44z"})),Va||(Va=e.createElement("circle",{cx:38.13,cy:30.03,r:3.14,fill:"#b89ac8"})),Ga||(Ga=e.createElement("circle",{cx:60.26,cy:39.96,r:3.14,fill:"#e31e0c"})),Za||(Za=e.createElement("circle",{cx:50.29,cy:25.63,r:3.14,fill:"#3baa45"})),Xa||(Xa=e.createElement("circle",{cx:22.19,cy:19.21,r:3.14,fill:"#2ca9e1"})),Qa||(Qa=e.createElement("circle",{cx:22.19,cy:30.03,r:3.14,fill:"#e31e0c"})),Ja||(Ja=e.createElement("circle",{cx:26.86,cy:8.28,r:3.14,fill:"#3baa45"})),eo||(eo=e.createElement("circle",{cx:49.32,cy:39.99,r:3.14,fill:"#e31e0c"})),to||(to=e.createElement("circle",{cx:63.86,cy:59.52,r:3.14,fill:"#f8ad39"})),so||(so=e.createElement("circle",{cx:50.88,cy:50.72,r:3.14,fill:"#3baa45"})),ro||(ro=e.createElement("circle",{cx:63.47,cy:76.17,r:3.14,fill:"#e31e0c"})),no||(no=e.createElement("circle",{cx:38.34,cy:14.83,r:3.14,fill:"#2ca9e1"})),ao||(ao=e.createElement("circle",{cx:44.44,cy:5.92,r:3.14,fill:"#f8ad39"})),oo||(oo=e.createElement("circle",{cx:57.42,cy:10.24,r:3.14,fill:"#e31e0c"})),io||(io=e.createElement("circle",{cx:66.81,cy:12.4,r:3.14,fill:"#2ca9e1"})),lo||(lo=e.createElement("circle",{cx:77.95,cy:5.14,r:3.14,fill:"#b89ac8"})),co||(co=e.createElement("circle",{cx:77.95,cy:30.34,r:3.14,fill:"#e31e0c"})),uo||(uo=e.createElement("circle",{cx:80.97,cy:16.55,r:3.14,fill:"#f8ad39"})),po||(po=e.createElement("circle",{cx:62.96,cy:27.27,r:3.14,fill:"#3baa45"})),mo||(mo=e.createElement("circle",{cx:75.36,cy:48.67,r:3.14,fill:"#2ca9e1"})),ho||(ho=e.createElement("circle",{cx:76.11,cy:65.31,r:3.14,fill:"#3baa45"})),go||(go=e.createElement("path",{fill:"#71b026",d:"M78.58 178.43C54.36 167.26 32 198.93 5 198.93c19.56 20.49 63.53 1.52 69 15.5 1.48-14.01 4.11-30.9 4.58-36z"})),yo||(yo=e.createElement("path",{fill:"#074a67",d:"M67.75 251.08c0-4.65 10.13-72.65 10.13-72.65h2.8l-9.09 72.3z"})),fo||(fo=e.createElement("ellipse",{cx:255.38,cy:103.18,fill:"#fff",rx:1.84,ry:1.77})),bo||(bo=e.createElement("ellipse",{cx:221.24,cy:94.75,fill:"#fff",rx:1.84,ry:1.77}))),vo=(0,ve.compose)([(0,h.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,h.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))]),ko=({children:t,id:s,hasIcon:r=!0,title:n,image:a=null,isAlertDismissed:o,onDismissed:i})=>o?null:(0,e.createElement)("div",{id:s,className:"notice-yoast yoast is-dismissible yoast-webinar-dashboard yoast-general-page-notices"},(0,e.createElement)("div",{className:"notice-yoast__container"},(0,e.createElement)("div",null,(0,e.createElement)("div",{className:"notice-yoast__header"},r&&(0,e.createElement)("span",{className:"yoast-icon"}),(0,e.createElement)("h2",{className:"notice-yoast__header-heading yoast-notice-migrated-header"},n)),(0,e.createElement)("div",{className:"notice-yoast-content"},(0,e.createElement)("p",null,t))),a&&(0,e.createElement)(a,{height:"60"})),(0,e.createElement)("button",{type:"button",className:"notice-dismiss",onClick:i},(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,g.__)("Dismiss this notice.","wordpress-seo"))));ko.propTypes={children:o().node.isRequired,id:o().string.isRequired,hasIcon:o().bool,title:o().any.isRequired,image:o().elementType,isAlertDismissed:o().bool.isRequired,onDismissed:o().func.isRequired};const _o=vo(ko),xo=({store:t="yoast-seo/editor",image:s=null,title:r,promoId:n,alertKey:a,children:o,...i})=>(0,h.select)(t).isPromotionActive(n)&&(0,e.createElement)(_o,{alertKey:a,store:t,id:a,title:r,image:s,...i},o);xo.propTypes={store:o().string,image:o().elementType,title:o().any.isRequired,promoId:o().string.isRequired,alertKey:o().string.isRequired,children:o().node};const To=({store:t="yoast-seo/editor",location:s="sidebar",...r})=>{const n=(0,h.useSelect)((e=>e(t).getIsPremium()),[t]),a=(0,h.useSelect)((e=>e(t).selectLinkParams()),[t]),o="sidebar"===s?(0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium */
(0,g.__)("Now with 30%% OFF: %1$s","wordpress-seo"),"Yoast SEO Premium"):y((0,g.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to a link on yoast.com, %3$s expands to the anchor end tag. */
(0,g.__)("Now with 30%% OFF: %1$s %2$sBuy now!%3$s","wordpress-seo"),"Yoast SEO Premium","<a>","</a>"),{a:(0,e.createElement)("a",{href:(0,sr.addQueryArgs)("https://yoa.st/black-friday-sale",a),target:"_blank",rel:"noreferrer"})});return n?null:(0,e.createElement)(xo,{id:`black-friday-2024-promotion-${s}`,promoId:"black-friday-2024-promotion",alertKey:"black-friday-2024-promotion",store:t,title:o,...r},(0,e.createElement)("span",{className:"yoast-bf-sale-badge"},(0,g.__)("BLACK FRIDAY SALE","wordpress-seo")," "),"sidebar"===s&&(0,e.createElement)("a",{className:"yst-block yst--mb-[1em]",href:(0,sr.addQueryArgs)("https://yoa.st/black-friday-sale",a),target:"_blank",rel:"noreferrer"},(0,g.__)("Buy now!","wordpress-seo")))};To.propTypes={store:o().string,location:o().oneOf(["sidebar","metabox"])};const So=t=>s=>!(()=>{var e,t;const s=(0,h.select)("yoast-seo/editor").getIsPremium(),r=(0,h.select)("yoast-seo/editor").getWarningMessage();return(s&&null!==(e=null===(t=(0,h.select)("yoast-seo-premium/editor"))||void 0===t?void 0:t.getMetaboxWarning())&&void 0!==e?e:[]).length>0||r.length>0})()&&(0,e.createElement)(t,{...s}),Ro=So((()=>{const t=(0,h.useSelect)((e=>e("yoast-seo/editor").selectLinkParams()),[]),s=(0,g.sprintf)(/* translators: %1$s expands to 'WooCommerce'. */
(0,g.__)("Is your %1$s store ready for Black Friday?","wordpress-seo"),"WooCommerce");return(0,e.createElement)(xo,{id:"black-friday-2023-product-editor-checklist",alertKey:"black-friday-2023-product-editor-checklist",promoId:"black-friday-2023-checklist",store:"yoast-seo/editor",title:s,image:Eo},y((0,g.sprintf)(/* translators: %1$s expands to a 'strong' start tag, %2$s to a 'strong' end tag. */
(0,g.__)("The Yoast %1$sultimate Black Friday checklist%2$s helps you prepare in time, so you can boost your results during this sale.","wordpress-seo"),"<strong>","</strong>"),{strong:(0,e.createElement)("strong",null)})," ",(0,e.createElement)("a",{href:(0,sr.addQueryArgs)("https://yoa.st/black-friday-checklist",t),target:"_blank",rel:"noreferrer"},(0,g.__)("Get the checklist and start optimizing now!","wordpress-seo")))})),Io=So(To);function Co({settings:t}){const{isTerm:s,isProduct:a,isWooCommerceActive:o}=(0,h.useSelect)((e=>({isTerm:e("yoast-seo/editor").getIsTerm(),isProduct:e("yoast-seo/editor").getIsProduct(),isWooCommerceActive:e("yoast-seo/editor").getIsWooCommerceActive()})),[]),i=a&&o;return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor&&(()=>{const{editorMode:e,activeAIButtonId:t}=(0,h.useSelect)((e=>({editorMode:e("core/edit-post").getEditorMode(),activeAIButtonId:e("yoast-seo/editor").getActiveAIFixesButton()})),[]),{setMarkerStatus:s}=(0,h.useDispatch)("yoast-seo/editor");(0,r.useEffect)((()=>(s("visual"===e&&t||"text"===e?"disabled":"enabled"),()=>{s("disabled")})),[e,t])})(),(0,e.createElement)(r.Fragment,null,(0,e.createElement)(n.Fill,{name:"YoastMetabox"},(0,e.createElement)(Cr,{key:"warning",renderPriority:1},(0,e.createElement)(Ks,null)),(0,e.createElement)(Cr,{key:"time-constrained-notification",renderPriority:2},i&&(0,e.createElement)(Ro,null),(0,e.createElement)(Io,{image:null,hasIcon:!1,location:"metabox"})),t.isKeywordAnalysisActive&&(0,e.createElement)(Cr,{key:"keyword-input",renderPriority:8},(0,e.createElement)(Rs.KeywordInput,{isSEMrushIntegrationActive:t.isSEMrushIntegrationActive}),!window.wpseoScriptData.metabox.isPremium&&(0,e.createElement)(n.Fill,{name:"YoastRelatedKeyphrases"},(0,e.createElement)(ya,null))),(0,e.createElement)(Cr,{key:"search-appearance",renderPriority:9},(0,e.createElement)(Gs,{id:"yoast-snippet-editor-metabox",title:(0,g.__)("Search appearance","wordpress-seo"),initialIsOpen:!0},(0,e.createElement)(Ys,{hasPaperStyle:!1}))),t.isContentAnalysisActive&&(0,e.createElement)(Cr,{key:"readability-analysis",renderPriority:10},(0,e.createElement)(Rs.ReadabilityAnalysis,{shouldUpsell:t.shouldUpsell})),t.isKeywordAnalysisActive&&(0,e.createElement)(Cr,{key:"seo-analysis",renderPriority:20},(0,e.createElement)(r.Fragment,null,(0,e.createElement)(Rs.SeoAnalysis,{shouldUpsell:t.shouldUpsell,shouldUpsellWordFormRecognition:t.isWordFormRecognitionActive}),t.shouldUpsell&&(0,e.createElement)(va,{location:"metabox"}))),t.isInclusiveLanguageAnalysisActive&&(0,e.createElement)(Cr,{key:"inclusive-language-analysis",renderPriority:21},(0,e.createElement)(Rs.InclusiveLanguageAnalysis,null)),t.isKeywordAnalysisActive&&(0,e.createElement)(Cr,{key:"additional-keywords-upsell",renderPriority:22},t.shouldUpsell&&(0,e.createElement)(xa,null)),t.isKeywordAnalysisActive&&t.isWincherIntegrationActive&&(0,e.createElement)(Cr,{key:"wincher-seo-performance",renderPriority:23},(0,e.createElement)(Ss,{location:"metabox"})),t.shouldUpsell&&!s&&(0,e.createElement)(Cr,{key:"internal-linking-suggestions-upsell",renderPriority:25},(0,e.createElement)(Rr,null)),t.isCornerstoneActive&&(0,e.createElement)(Cr,{key:"cornerstone",renderPriority:30},(0,e.createElement)(Cs,null)),t.displayAdvancedTab&&(0,e.createElement)(Cr,{key:"advanced",renderPriority:40},(0,e.createElement)(Gs,{id:"collapsible-advanced-settings",title:(0,g.__)("Advanced","wordpress-seo")},(0,e.createElement)(Or,null))),t.displaySchemaSettings&&(0,e.createElement)(Cr,{key:"schema",renderPriority:50},(0,e.createElement)(pa,null)),(0,e.createElement)(Cr,{key:"social",renderPriority:-1},(0,e.createElement)(Jn,{target:"wpseo-section-social"})),t.isInsightsEnabled&&(0,e.createElement)(Cr,{key:"insights",renderPriority:52},(0,e.createElement)(hr,{location:"metabox"}))))}Co.propTypes={settings:o().object.isRequired};const Lo=(0,ve.compose)([(0,h.withSelect)(((e,t)=>{const{getPreferences:s}=e("yoast-seo/editor");return{settings:s(),store:t.store}}))])(Co);function Po({target:t,store:s,theme:r}){return(0,e.createElement)(ye,{target:t},(0,e.createElement)(Ee,{store:s,theme:r}),(0,e.createElement)(Lo,{store:s,theme:r}))}Po.propTypes={target:o().string.isRequired,store:o().object,theme:o().object};const Ao=[];let Fo=null;class qo extends r.Component{constructor(e){super(e),this.state={registeredComponents:[]}}registerComponent(e,t){this.setState({registeredComponents:[...this.state.registeredComponents,{key:e,Component:t}]})}render(){return this.state.registeredComponents.map((({Component:t,key:s})=>(0,e.createElement)(t,{key:s})))}}function Mo(e,t){null===Fo||null===Fo.current?Ao.push({key:e,Component:t}):Fo.current.registerComponent(e,t)}const No=window.yoast.externals.redux,Oo=window.jQuery;var Do=s.n(Oo);function $o(e){let t="";var s;return t=!1===function(e){if("undefined"==typeof tinyMCE||void 0===tinyMCE.editors||0===tinyMCE.editors.length)return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}(e)||0==(s=e,null!==document.getElementById(s+"_ifr"))?function(e){return document.getElementById(e)&&document.getElementById(e).value||""}(e):tinyMCE.get(e).getContent(),t}i.noop,i.noop,i.noop;const{removeMarks:Uo}=he.markers,{updateReplacementVariable:Bo,updateData:Wo,hideReplacementVariables:jo,setContentImage:Ho,setEditorDataContent:Yo,setEditorDataTitle:Ko,setEditorDataExcerpt:zo,setEditorDataImageUrl:Vo,setEditorDataSlug:Go}=No.actions;window.yoast=window.yoast||{},window.yoast.initEditorIntegration=function(t){window.YoastSEO=window.YoastSEO||{},window.YoastSEO._registerReactComponent=Mo,function(t){const s=dr();Fo=(0,r.createRef)();const a={isRtl:s.isRtl};(0,r.render)((0,e.createElement)(n.SlotFillProvider,null,(0,e.createElement)(d.Root,{context:{locationContext:"classic-metabox"}},(0,e.createElement)(Po,{target:"wpseo-metabox-root",store:t,theme:a})),(0,e.createElement)(qo,{ref:Fo})),document.getElementById("wpseo-metabox-root")),Ao.forEach((e=>{Fo.current.registerComponent(e.key,e.Component)}))}(t)},window.yoast.EditorData=class{constructor(e,t,s="content"){this._refresh=e,this._store=t,this._tinyMceId=s,this._previousData={},this._previousEditorData={},this.updateReplacementData=this.updateReplacementData.bind(this),this.refreshYoastSEO=this.refreshYoastSEO.bind(this)}initialize(e,t=[]){const s=this.getInitialData(e);var r,n;r=s,n=this._store,(0,i.forEach)(r,((e,t)=>{$s.includes(t)||n.dispatch(Ns(t,e))})),this._store.dispatch(jo(t)),this._previousEditorData.content=s.content,this._store.dispatch(Yo(s.content)),this._previousEditorData.contentImage=s.contentImage,this._store.dispatch(Ho(s.contentImage)),this.setImageInSnippetPreview(s.snippetPreviewImageURL||s.contentImage),this._previousEditorData.slug=s.slug,this._store.dispatch(Go(s.slug)),this.updateReplacementData({target:{value:s.title}},"title"),this.updateReplacementData({target:{value:s.excerpt}},"excerpt"),this.updateReplacementData({target:{value:s.excerpt_only}},"excerpt_only"),this.subscribeToElements(),this.subscribeToStore(),this.subscribeToSnippetPreviewImage(),this.subscribeToTinyMceEditor(),this.subscribeToSlug()}subscribeToTinyMceEditor(){const e=e=>{if((0,i.isString)(e)||(e=this.getContent()),this._previousEditorData.content===e)return;if(this._previousEditorData.content=e,this._store.dispatch(Yo(e)),this.featuredImageIsSet)return;const t=this.getContentImage(e);this._previousEditorData.contentImage!==t&&(this._previousEditorData.contentImage=t,this._store.dispatch(Ho(t)),this.setImageInSnippetPreview(t))};Do()(document).on("tinymce-editor-init",((t,s)=>{s.id===this._tinyMceId&&(e(this.getContent()),["input","change","cut","paste"].forEach((t=>s.on(t,(0,i.debounce)(e,1e3)))))}));const t=document.getElementById("attachment_content");t&&(e(t.value),t.addEventListener("input",(t=>e(t.target.value))))}subscribeToSlug(){const e=e=>{this._previousEditorData.slug!==e&&(this._previousEditorData.slug=e,this._store.dispatch(Go(e)),this._store.dispatch(Wo({slug:e})))},t=document.getElementById("slug");t&&t.addEventListener("input",(t=>e(t.target.value)));const s=document.getElementById("post_name");s&&s.addEventListener("input",(t=>e(t.target.value)));const r=document.getElementById("edit-slug-buttons");r&&new MutationObserver(((t,s)=>t.forEach((t=>{t.addedNodes.forEach((t=>{var r,n;if(null==t||null===(r=t.classList)||void 0===r||!r.contains("edit-slug"))return;const a=null===(n=document.getElementById("editable-post-name-full"))||void 0===n?void 0:n.innerText;a&&(e(a),s.disconnect(),this.subscribeToSlug())}))})))).observe(r,{childList:!0})}subscribeToSnippetPreviewImage(){if((0,i.isUndefined)(wp.media)||(0,i.isUndefined)(wp.media.featuredImage))return;Do()("#postimagediv").on("click","#remove-post-thumbnail",(()=>{this.featuredImageIsSet=!1,this.setImageInSnippetPreview(this.getContentImage(this.getContent()))}));const e=wp.media.featuredImage.frame();var t,s,r;e.on("select",(()=>{const t=e.state().get("selection").first().attributes.url;t&&(this.featuredImageIsSet=!0,this.setImageInSnippetPreview(t))})),t=this._tinyMceId,s=["init"],r=()=>{const e=this.getContentImage(this.getContent()),t=this.getFeaturedImage()||e||"";this._store.dispatch(Ho(e)),this.setImageInSnippetPreview(t)},"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(e){const n=e.editor;n.id===t&&(0,i.forEach)(s,(function(e){n.on(e,r)}))}))}getFeaturedImage(){const e=Do()("#set-post-thumbnail img").attr("src");return e?(this.featuredImageIsSet=!0,e):(this.featuredImageIsSet=!1,null)}setImageInSnippetPreview(e){this._store.dispatch(Vo(e)),this._store.dispatch(Wo({snippetPreviewImageURL:e}))}getContentImage(e){if(this.featuredImageIsSet)return"";const t=he.languageProcessing.imageInText(e);if(0===t.length)return"";const s=Do().parseHTML(t.join(""));for(const e of s)if(e.src)return e.src;return""}getTitle(){const e=document.getElementById("title")||document.getElementById("name");return e&&e.value||""}getExcerpt(e=!0){const t=document.getElementById("excerpt"),s=t&&t.value||"",r="ja"===function(){const e=dr();return(0,i.get)(e,"contentLocale","en_US")}()?80:156;return""!==s||!1===e?s:function(e,t=156){return(e=(e=(0,qs.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}(this.getContent(),r)}getSlug(){let e="";const t=document.getElementById("new-post-slug")||document.getElementById("slug");return t?e=t.value:null!==document.getElementById("editable-post-name-full")&&(e=document.getElementById("editable-post-name-full").textContent),e}getContent(){return Uo($o(this._tinyMceId))}subscribeToElements(){this.subscribeToInputElement("title","title"),this.subscribeToInputElement("excerpt","excerpt"),this.subscribeToInputElement("excerpt","excerpt_only")}subscribeToInputElement(e,t){const s=document.getElementById(e);s&&s.addEventListener("input",(e=>{this.updateReplacementData(e,t)}))}updateReplacementData(e,t){let s=e.target.value;if("excerpt"===t&&""===s&&(s=this.getExcerpt()),this._previousEditorData[t]!==s){switch(this._previousEditorData[t]=s,t){case"title":this._store.dispatch(Ko(s));break;case"excerpt":this._store.dispatch(zo(s))}this._store.dispatch(Bo(t,s))}}isShallowEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e.hasOwnProperty(s)&&(!(s in t)||e[s]!==t[s]))return!1;return!0}refreshYoastSEO(){const e=this.getData();!this.isShallowEqual(this._previousData,e)&&(this.handleEditorChange(e),this._previousData=e,window.YoastSEO&&window.YoastSEO.app&&window.YoastSEO.app.refresh())}handleEditorChange(e){this._previousData.excerpt!==e.excerpt&&(this._store.dispatch(Bo("excerpt",e.excerpt)),this._store.dispatch(Bo("excerpt_only",e.excerpt_only))),this._previousData.snippetPreviewImageURL!==e.snippetPreviewImageURL&&this.setImageInSnippetPreview(e.snippetPreviewImageURL),this._previousData.slug!==e.slug&&this._store.dispatch(Go(e.slug)),this._previousData.title!==e.title&&this._store.dispatch(Ko(e.title))}subscribeToStore(){this.subscriber=(0,i.debounce)(this.refreshYoastSEO,500),this._store.subscribe(this.subscriber)}getInitialData(e){e=function(e,t){if(!e.custom_taxonomies)return e;const s={};return(0,i.forEach)(e.custom_taxonomies,((e,t)=>{const{name:r,label:n,descriptionName:a,descriptionLabel:o}=function(e){const t=Us(e);return{name:"ct_"+t,label:Os(e+" (custom taxonomy)"),descriptionName:"ct_desc_"+t,descriptionLabel:Os(e+" description (custom taxonomy)")}}(t),i="string"==typeof e.name?(0,Re.decodeHTML)(e.name):e.name,l="string"==typeof e.description?(0,Re.decodeHTML)(e.description):e.description;s[r]={value:i,label:n},s[a]={value:l,label:o}})),t.dispatch(function(e){return{type:"SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLES_BATCH",updatedVariables:e}}(s)),(0,i.omit)({...e},"custom_taxonomies")}(e=function(e,t){return e.custom_fields?((0,i.forEach)(e.custom_fields,((e,s)=>{const{name:r,label:n}=function(e){return{name:"cf_"+Us(e),label:Os(e+" (custom field)")}}(s);t.dispatch(Ns(r,e,n))})),(0,i.omit)({...e},"custom_fields")):e}(e,this._store),this._store);const t=this.getContent(),s=this.getFeaturedImage();return{...e,title:this.getTitle(),excerpt:this.getExcerpt(),excerpt_only:this.getExcerpt(!1),slug:this.getSlug(),content:t,snippetPreviewImageURL:s,contentImage:this.getContentImage(t)}}getData(){return{...this._store.getState().snippetEditor.data,title:this.getTitle(),content:this.getContent(),excerpt:this.getExcerpt(),excerpt_only:this.getExcerpt(!1)}}}})()})();