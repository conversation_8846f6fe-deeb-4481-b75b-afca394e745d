(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var n in s)e.o(s,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:s[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{DISMISS_ALERT:()=>A,NEW_REQUEST:()=>M,SNIPPET_EDITOR_FIND_CUSTOM_FIELDS:()=>N,wistiaEmbedPermission:()=>I});const s=window.wp.domReady;var n=e.n(s);const i=window.jQuery;var o=e.n(i);const a=window.lodash,r=window.wp.i18n;const l=window.wp.data,c=window.yoast.externals.redux,d=window.yoast.reduxJsToolkit,p="adminUrl",u=(0,d.createSlice)({name:p,initialState:"",reducers:{setAdminUrl:(e,{payload:t})=>t}}),h=(u.getInitialState,{selectAdminUrl:e=>(0,a.get)(e,p,"")});h.selectAdminLink=(0,d.createSelector)([h.selectAdminUrl,(e,t)=>t],((e,t="")=>{try{return new URL(t,e).href}catch(t){return e}})),u.actions,u.reducer;const m=window.wp.url,g="linkParams",y=(0,d.createSlice)({name:g,initialState:{},reducers:{setLinkParams:(e,{payload:t})=>t}}),w=(y.getInitialState,{selectLinkParam:(e,t,s={})=>(0,a.get)(e,`${g}.${t}`,s),selectLinkParams:e=>(0,a.get)(e,g,{})});w.selectLink=(0,d.createSelector)([w.selectLinkParams,(e,t)=>t,(e,t,s={})=>s],((e,t,s)=>(0,m.addQueryArgs)(t,{...e,...s}))),y.actions,y.reducer;const f=(0,d.createSlice)({name:"notifications",initialState:{},reducers:{addNotification:{reducer:(e,{payload:t})=>{e[t.id]={id:t.id,variant:t.variant,size:t.size,title:t.title,description:t.description}},prepare:({id:e,variant:t="info",size:s="default",title:n,description:i})=>({payload:{id:e||(0,d.nanoid)(),variant:t,size:s,title:n||"",description:i}})},removeNotification:(e,{payload:t})=>(0,a.omit)(e,t)}}),b=(f.getInitialState,f.actions,f.reducer,"pluginUrl"),_=(0,d.createSlice)({name:b,initialState:"",reducers:{setPluginUrl:(e,{payload:t})=>t}}),E=(_.getInitialState,{selectPluginUrl:e=>(0,a.get)(e,b,"")});E.selectImageLink=(0,d.createSelector)([E.selectPluginUrl,(e,t,s="images")=>s,(e,t)=>t],((e,t,s)=>[(0,a.trimEnd)(e,"/"),(0,a.trim)(t,"/"),(0,a.trimStart)(s,"/")].join("/"))),_.actions,_.reducer;const v=window.wp.apiFetch;var S=e.n(v);const k="loading",x="showPlay",R="askPermission",O="isPlaying",P="wistiaEmbedPermission",T=(0,d.createSlice)({name:P,initialState:{value:!1,status:"idle",error:{}},reducers:{setWistiaEmbedPermissionValue:(e,{payload:t})=>{e.value=Boolean(t)}},extraReducers:e=>{e.addCase(`${P}/request`,(e=>{e.status=k})),e.addCase(`${P}/success`,((e,{payload:t})=>{e.status="success",e.value=Boolean(t&&t.value)})),e.addCase(`${P}/error`,((e,{payload:t})=>{e.status="error",e.value=Boolean(t&&t.value),e.error={code:(0,a.get)(t,"error.code",500),message:(0,a.get)(t,"error.message","Unknown")}}))}}),C=(T.getInitialState,T.actions,{[P]:async({payload:e})=>S()({path:"/yoast/v1/wistia_embed_permission",method:"POST",data:{value:Boolean(e)}})});function A({alertKey:e}){return new Promise((t=>wpseoApi.post("alerts/dismiss",{key:e},(()=>t()))))}function N({query:e,postId:t}){return new Promise((s=>{wpseoApi.get("meta/search",{query:e,post_id:t},(e=>{s(e.meta)}))}))}T.reducer;const M=async({countryCode:e,keyphrase:t})=>(S()({path:"yoast/v1/semrush/country_code",method:"POST",data:{country_code:e}}),S()({path:(0,m.addQueryArgs)("/yoast/v1/semrush/related_keyphrases",{keyphrase:t,country_code:e})})),I=C[P];const L=window.yoast.analysis,F=window.wp.isShallowEqual,B="yoastmark";function D(e,t){return e._properties.position.startOffset>t.length||e._properties.position.endOffset>t.length}function U(e,t,s){const n=e.dom;let i=e.getContent();if(i=L.markers.removeMarks(i),(0,a.isEmpty)(s))return void e.setContent(i);i=s[0].hasPosition()?function(e,t){if(!t)return"";for(let s=(e=(0,a.orderBy)(e,(e=>e._properties.position.startOffset),["asc"])).length-1;s>=0;s--){const n=e[s];D(n,t)||(t=n.applyWithPosition(t))}return t}(s,i):function(e,t,s,n){const{fieldsToMark:i,selectedHTML:o}=L.languageProcessing.getFieldsToMark(s,n);return(0,a.forEach)(s,(function(t){"acf_content"!==e.id&&(t._properties.marked=L.languageProcessing.normalizeHTML(t._properties.marked),t._properties.original=L.languageProcessing.normalizeHTML(t._properties.original)),i.length>0?o.forEach((e=>{const s=t.applyWithReplace(e);n=n.replace(e,s)})):n=t.applyWithReplace(n)})),n}(e,0,s,i),e.setContent(i),function(e){let t=e.getContent();t=t.replace(new RegExp("&lt;yoastmark.+?&gt;","g"),"").replace(new RegExp("&lt;/yoastmark&gt;","g"),""),e.setContent(t)}(e);const o=n.select(B);(0,a.forEach)(o,(function(e){e.setAttribute("data-mce-bogus","1")}))}function j(e){return window.test=e,U.bind(null,e)}const Y="et_pb_main_editor_wrap",q=class{static isActive(){return!!document.getElementById(Y)}static isTinyMCEHidden(){const e=document.getElementById(Y);return!!e&&e.classList.contains("et_pb_hidden")}listen(e){this.classicEditorContainer=document.getElementById(Y),this.classicEditorContainer&&new MutationObserver((t=>{(0,a.forEach)(t,(t=>{"attributes"===t.type&&"class"===t.attributeName&&(t.target.classList.contains("et_pb_hidden")?e.classicEditorHidden():e.classicEditorShown())}))})).observe(this.classicEditorContainer,{attributes:!0})}},$=class{static isActive(){return!!window.VCV_I18N}},z={classicEditorHidden:a.noop,classicEditorShown:a.noop,pageBuilderLoaded:a.noop},K=class{constructor(){this.determineActivePageBuilders()}determineActivePageBuilders(){q.isActive()&&(this.diviActive=!0),$.isActive()&&(this.vcActive=!0)}isPageBuilderActive(){return this.diviActive||this.vcActive}listen(e){this.callbacks=(0,a.defaults)(e,z),this.diviActive&&(new q).listen(e)}isClassicEditorHidden(){return!(!this.diviActive||!q.isTinyMCEHidden())}};let W;const V="content",H="description";function Q(e){if("undefined"==typeof tinyMCE||void 0===tinyMCE.editors||0===tinyMCE.editors.length)return!1;const t=tinyMCE.get(e);return null!==t&&!t.isHidden()}function G(e,t,s){"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(n){const i=n.editor;i.id===e&&(0,a.forEach)(t,(function(e){i.on(e,s)}))}))}function J(){(0,a.isUndefined)(W)||W.dispatch(c.actions.setMarkerStatus("disabled"))}function Z(){(0,a.isUndefined)(W)||W.dispatch(c.actions.setMarkerStatus("enabled"))}class X{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,a.isString)(e)?(0,a.isUndefined)(t)||(0,a.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,a.isString)(e)?(0,a.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,a.isString)(e)?(0,a.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,n){if(!(0,a.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,a.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,a.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const i={callable:t,origin:s,priority:(0,a.isNumber)(n)?n:10};return(0,a.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(i),!0}_registerAssessment(e,t,s,n){return(0,a.isString)(t)?(0,a.isObject)(s)?(0,a.isString)(n)?(t=n+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+n+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+n+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let n=this.modifications[e];return!(0,a.isArray)(n)||n.length<1||(n=this._stripIllegalModifications(n),n.sort(((e,t)=>e.priority-t.priority)),(0,a.forEach)(n,(function(n){const i=n.callable(t,s);typeof i==typeof t?t=i:console.error("Modification with name "+e+" performed by plugin with name "+n.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,a.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,a.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,a.forEach)(this.plugins,(function(e,t){(0,a.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,a.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,a.isUndefined)(this.plugins[e])}}function ee(e,t,s){e("morphology",new L.Paper("",{keyword:s})).then((e=>{const s=e.result.keyphraseForms;t.dispatch(c.actions.updateWordsToHighlight((0,a.uniq)((0,a.flatten)(s))))})).catch((()=>{t.dispatch(c.actions.updateWordsToHighlight([]))}))}const te=window.wp.api;function se(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor}var ne={source:"wpseoScriptData.analysis.plugins.replaceVars",scope:[],aliases:[]},ie=function(e,t,s){this.placeholder=e,this.replacement=t,this.options=(0,a.defaults)(s,ne)};ie.prototype.getPlaceholder=function(e){return(e=e||!1)&&this.hasAlias()?this.placeholder+"|"+this.getAliases().join("|"):this.placeholder},ie.prototype.setSource=function(e){this.options.source=e},ie.prototype.hasScope=function(){return!(0,a.isEmpty)(this.options.scope)},ie.prototype.addScope=function(e){this.hasScope()||(this.options.scope=[]),this.options.scope.push(e)},ie.prototype.inScope=function(e){return!this.hasScope()||(0,a.indexOf)(this.options.scope,e)>-1},ie.prototype.hasAlias=function(){return!(0,a.isEmpty)(this.options.aliases)},ie.prototype.addAlias=function(e){this.hasAlias()||(this.options.aliases=[]),this.options.aliases.push(e)},ie.prototype.getAliases=function(){return this.options.aliases};const oe=ie,{removeReplacementVariable:ae,updateReplacementVariable:re,refreshSnippetEditor:le}=c.actions;var ce=["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc","excerpt"],de={},pe={},ue=function(e,t){this._app=e,this._app.registerPlugin("replaceVariablePlugin",{status:"ready"}),this._store=t,this.replaceVariables=this.replaceVariables.bind(this),this.registerReplacements(),this.registerModifications(),this.registerEvents(),this.subscribeToGutenberg()};ue.prototype.registerReplacements=function(){this.addReplacement(new oe("%%author_first_name%%","author_first_name")),this.addReplacement(new oe("%%author_last_name%%","author_last_name")),this.addReplacement(new oe("%%category%%","category")),this.addReplacement(new oe("%%category_title%%","category_title")),this.addReplacement(new oe("%%currentdate%%","currentdate")),this.addReplacement(new oe("%%currentday%%","currentday")),this.addReplacement(new oe("%%currentmonth%%","currentmonth")),this.addReplacement(new oe("%%currenttime%%","currenttime")),this.addReplacement(new oe("%%currentyear%%","currentyear")),this.addReplacement(new oe("%%date%%","date")),this.addReplacement(new oe("%%id%%","id")),this.addReplacement(new oe("%%page%%","page")),this.addReplacement(new oe("%%permalink%%","permalink")),this.addReplacement(new oe("%%post_content%%","post_content")),this.addReplacement(new oe("%%post_month%%","post_month")),this.addReplacement(new oe("%%post_year%%","post_year")),this.addReplacement(new oe("%%searchphrase%%","searchphrase")),this.addReplacement(new oe("%%sitedesc%%","sitedesc")),this.addReplacement(new oe("%%sitename%%","sitename")),this.addReplacement(new oe("%%userid%%","userid")),this.addReplacement(new oe("%%focuskw%%","keyword",{source:"app",aliases:["%%keyword%%"]})),this.addReplacement(new oe("%%term_description%%","text",{source:"app",scope:["term","category","tag"],aliases:["%%tag_description%%","%%category_description%%"]})),this.addReplacement(new oe("%%term_title%%","term_title",{scope:["term"]})),this.addReplacement(new oe("%%term_hierarchy%%","term_hierarchy",{scope:["term"]})),this.addReplacement(new oe("%%title%%","title",{source:"app",scope:["post","term","page"]})),this.addReplacement(new oe("%%parent_title%%","title",{source:"app",scope:["page","category"]})),this.addReplacement(new oe("%%excerpt%%","excerpt",{source:"app",scope:["post"],aliases:["%%excerpt_only%%"]})),this.addReplacement(new oe("%%primary_category%%","primaryCategory",{source:"app",scope:["post"]})),this.addReplacement(new oe("%%sep%%(\\s*%%sep%%)*","sep"))},ue.prototype.registerEvents=function(){const e=wpseoScriptData.analysis.plugins.replaceVars.scope;"post"===e&&jQuery(".categorydiv").each(this.bindTaxonomyEvents.bind(this)),"post"!==e&&"page"!==e||jQuery("#postcustomstuff > #list-table").each(this.bindFieldEvents.bind(this))},ue.prototype.subscribeToGutenberg=function(){if(!se())return;const e={0:""};let t=null;const s=wp.data;s.subscribe((()=>{const n=s.select("core/editor").getEditedPostAttribute("parent");if(void 0!==n&&t!==n)return t=n,n<1?(this._currentParentPageTitle="",void this.declareReloaded()):(0,a.isUndefined)(e[n])?void te.loadPromise.done((()=>{new te.models.Page({id:n}).fetch().then((t=>{this._currentParentPageTitle=t.title.rendered,e[n]=this._currentParentPageTitle,this.declareReloaded()})).fail((()=>{this._currentParentPageTitle="",this.declareReloaded()}))})):(this._currentParentPageTitle=e[n],void this.declareReloaded())}))},ue.prototype.addReplacement=function(e){de[e.placeholder]=e},ue.prototype.removeReplacement=function(e){delete de[e.getPlaceholder()]},ue.prototype.registerModifications=function(){var e=this.replaceVariables.bind(this);(0,a.forEach)(ce,function(t){this._app.registerModification(t,e,"replaceVariablePlugin",10)}.bind(this))},ue.prototype.replaceVariables=function(e){return(0,a.isUndefined)(e)||(e=this.parentReplace(e),e=this.replaceCustomTaxonomy(e),e=this.replaceByStore(e),e=this.replacePlaceholders(e)),e},ue.prototype.replaceByStore=function(e){const t=this._store.getState().snippetEditor.replacementVariables;return(0,a.forEach)(t,(t=>{""!==t.value&&(e=e.replace("%%"+t.name+"%%",t.value))})),e},ue.prototype.getReplacementSource=function(e){return"app"===e.source?this._app.rawData:"direct"===e.source?"direct":wpseoScriptData.analysis.plugins.replaceVars.replace_vars},ue.prototype.getReplacement=function(e){var t=this.getReplacementSource(e.options);return!1===e.inScope(wpseoScriptData.analysis.plugins.replaceVars.scope)?"":"direct"===t?e.replacement:t[e.replacement]||""},ue.prototype.replacePlaceholders=function(e){return(0,a.forEach)(de,function(t){e=e.replace(new RegExp(t.getPlaceholder(!0),"g"),this.getReplacement(t))}.bind(this)),e},ue.prototype.declareReloaded=function(){this._app.pluginReloaded("replaceVariablePlugin"),this._store.dispatch(le())},ue.prototype.getCategoryName=function(e){var t=e.parent("label").clone();return t.children().remove(),t.text().trim()},ue.prototype.parseTaxonomies=function(e,t){(0,a.isUndefined)(pe[t])&&(pe[t]={});const s=[];(0,a.forEach)(e,function(e){const n=(e=jQuery(e)).val(),i=this.getCategoryName(e),o=e.prop("checked");pe[t][n]={label:i,checked:o},o&&-1===s.indexOf(i)&&s.push(i)}.bind(this)),"category"!==t&&(t="ct_"+t),this._store.dispatch(re(t,s.join(", ")))},ue.prototype.getAvailableTaxonomies=function(e){var t=jQuery(e).find("input[type=checkbox]"),s=jQuery(e).attr("id").replace("taxonomy-","");t.length>0&&this.parseTaxonomies(t,s),this.declareReloaded()},ue.prototype.bindTaxonomyEvents=function(e,t){(t=jQuery(t)).on("wpListAddEnd",".categorychecklist",this.getAvailableTaxonomies.bind(this,t)),t.on("change","input[type=checkbox]",this.getAvailableTaxonomies.bind(this,t)),this.getAvailableTaxonomies(t)},ue.prototype.replaceCustomTaxonomy=function(e){return(0,a.forEach)(pe,function(t,s){var n="%%ct_"+s+"%%";"category"===s&&(n="%%"+s+"%%"),e=e.replace(n,this.getTaxonomyReplaceVar(s))}.bind(this)),e},ue.prototype.getTaxonomyReplaceVar=function(e){var t=[],s=pe[e];return!0===(0,a.isUndefined)(s)?"":((0,a.forEach)(s,(function(e){!1!==e.checked&&t.push(e.label)})),jQuery.uniqueSort(t).join(", "))},ue.prototype.parseFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val(),n=jQuery("#"+t.id+"-value").val();const i="cf_"+this.sanitizeCustomFieldNames(s),o=s+" (custom field)";this._store.dispatch(re(i,n,o)),this.addReplacement(new oe(`%%${i}%%`,n,{source:"direct"}))}.bind(this))},ue.prototype.removeFields=function(e){jQuery(e).each(function(e,t){var s=jQuery("#"+t.id+"-key").val();this.removeReplacement("%%cf_"+this.sanitizeCustomFieldNames(s)+"%%")}.bind(this))},ue.prototype.sanitizeCustomFieldNames=function(e){return e.replace(/\s/g,"_")},ue.prototype.getAvailableFields=function(e){this.removeCustomFields();var t=jQuery(e).find("#the-list > tr:visible[id]");t.length>0&&this.parseFields(t),this.declareReloaded()},ue.prototype.bindFieldEvents=function(e,t){var s=(t=jQuery(t)).find("#the-list");s.on("wpListDelEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("wpListAddEnd.wpseoCustomFields",this.getAvailableFields.bind(this,t)),s.on("input.wpseoCustomFields",".textarea",this.getAvailableFields.bind(this,t)),s.on("click.wpseoCustomFields",".button + .updatemeta",this.getAvailableFields.bind(this,t)),this.getAvailableFields(t)},ue.prototype.removeCustomFields=function(){var e=(0,a.filter)(de,(function(e,t){return t.indexOf("%%cf_")>-1}));(0,a.forEach)(e,function(e){this._store.dispatch(ae((0,a.trim)(e.placeholder,"%%"))),this.removeReplacement(e)}.bind(this))},ue.prototype.parentReplace=function(e){const t=jQuery("#parent_id, #parent").eq(0);return this.hasParentTitle(t)&&(e=e.replace(/%%parent_title%%/,this.getParentTitleReplacement(t))),se()&&!(0,a.isUndefined)(this._currentParentPageTitle)&&(e=e.replace(/%%parent_title%%/,this._currentParentPageTitle)),e},ue.prototype.hasParentTitle=function(e){return!(0,a.isUndefined)(e)&&!(0,a.isUndefined)(e.prop("options"))},ue.prototype.getParentTitleReplacement=function(e){var t=e.find("option:selected").text();return t===(0,r.__)("(no parent)","wordpress-seo")?"":t},ue.ReplaceVar=oe;const he=ue,me=window.wp.hooks,ge="[^<>&/\\[\\]\0- =]+?",ye=new RegExp("\\["+ge+"( [^\\]]+?)?\\]","g"),we=new RegExp("\\[/"+ge+"\\]","g");class fe{constructor({registerPlugin:e,registerModification:t,pluginReady:s,pluginReloaded:n},i){this._registerModification=t,this._pluginReady=s,this._pluginReloaded=n,e("YoastShortcodePlugin",{status:"loading"}),this.bindElementEvents();const o="("+i.join("|")+")";this.shortcodesRegex=new RegExp(o,"g"),this.closingTagRegex=new RegExp("\\[\\/"+o+"\\]","g"),this.nonCaptureRegex=new RegExp("\\["+o+"[^\\]]*?\\]","g"),this.parsedShortcodes=[],this.loadShortcodes(this.declareReady.bind(this))}declareReady(){this._pluginReady("YoastShortcodePlugin"),this.registerModifications()}declareReloaded(){this._pluginReloaded("YoastShortcodePlugin")}registerModifications(){this._registerModification("content",this.replaceShortcodes.bind(this),"YoastShortcodePlugin")}removeUnknownShortCodes(e){return(e=e.replace(ye,"")).replace(we,"")}replaceShortcodes(e){return"string"==typeof e&&this.parsedShortcodes.forEach((({shortcode:t,output:s})=>{e=e.replace(t,s)})),e=this.removeUnknownShortCodes(e)}loadShortcodes(e){const t=this.getUnparsedShortcodes(this.getShortcodes(this.getContentTinyMCE()));if(!(t.length>0))return e();this.parseShortcodes(t,e)}bindElementEvents(){const e=document.querySelector(".wp-editor-area"),t=(0,a.debounce)(this.loadShortcodes.bind(this,this.declareReloaded.bind(this)),500);e&&(e.addEventListener("keyup",t),e.addEventListener("change",t)),"undefined"!=typeof tinyMCE&&"function"==typeof tinyMCE.on&&tinyMCE.on("addEditor",(function(e){e.editor.on("change",t),e.editor.on("keyup",t)}))}getContentTinyMCE(){let e=document.querySelector(".wp-editor-area")?document.querySelector(".wp-editor-area").value:"";return"undefined"!=typeof tinyMCE&&void 0!==tinyMCE.editors&&0!==tinyMCE.editors.length&&(e=tinyMCE.get("content")?tinyMCE.get("content").getContent():""),e}getUnparsedShortcodes(e){return"object"!=typeof e?(console.error("Failed to get unparsed shortcodes. Expected parameter to be an array, instead received "+typeof e),!1):e.filter((e=>this.isUnparsedShortcode(e)))}isUnparsedShortcode(e){return!this.parsedShortcodes.some((({shortcode:t})=>t===e))}getShortcodes(e){if("string"!=typeof e)return console.error("Failed to get shortcodes. Expected parameter to be a string, instead received"+typeof e),!1;const t=this.matchCapturingShortcodes(e);t.forEach((t=>{e=e.replace(t,"")}));const s=this.matchNonCapturingShortcodes(e);return t.concat(s)}matchCapturingShortcodes(e){const t=(e.match(this.closingTagRegex)||[]).join(" ").match(this.shortcodesRegex)||[];return(0,a.flatten)(t.map((t=>{const s="\\["+t+"[^\\]]*?\\].*?\\[\\/"+t+"\\]";return e.match(new RegExp(s,"g"))||[]})))}matchNonCapturingShortcodes(e){return e.match(this.nonCaptureRegex)||[]}parseShortcodes(e,t){return"function"!=typeof t?(console.error("Failed to parse shortcodes. Expected parameter to be a function, instead received "+typeof t),!1):"object"==typeof e&&e.length>0?void jQuery.post(ajaxurl,{action:"wpseo_filter_shortcodes",_wpnonce:wpseoScriptData.analysis.plugins.shortcodes.wpseo_filter_shortcodes_nonce,data:e},function(e){this.saveParsedShortcodes(e,t)}.bind(this)):t()}saveParsedShortcodes(e,t){const s=JSON.parse(e);this.parsedShortcodes.push(...s),t()}}const be=fe,{updateShortcodesForParsing:_e}=c.actions;function Ee(e){var t=jQuery(".yst-traffic-light"),s=t.closest(".wpseo-meta-section-link"),n=jQuery("#wpseo-traffic-light-desc"),i=e.className||"na";t.attr("class","yst-traffic-light "+i),s.attr("aria-describedby","wpseo-traffic-light-desc"),n.length>0?n.text(e.screenReaderText):s.closest("li").append("<span id='wpseo-traffic-light-desc' class='screen-reader-text'>"+e.screenReaderText+"</span>")}function ve(e){jQuery("#wp-admin-bar-wpseo-menu .wpseo-score-icon").attr("title",e.screenReaderText).attr("class","wpseo-score-icon "+e.className).find(".wpseo-score-text").text(e.screenReaderText)}function Se(){return(0,a.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}function ke(){const e=Se();return(0,a.get)(e,"contentLocale","en_US")}function xe(){const e=Se();return!0===(0,a.get)(e,"contentAnalysisActive",!1)}function Re(){const e=Se();return!0===(0,a.get)(e,"keywordAnalysisActive",!1)}function Oe(){const e=Se();return!0===(0,a.get)(e,"inclusiveLanguageAnalysisActive",!1)}const Pe=window.yoast.featureFlag;function Te(){}let Ce=!1;function Ae(e){return e.sort(((e,t)=>e._identifier.localeCompare(t._identifier)))}function Ne(e,t,s,n,i){if(!Ce)return;const o=L.Paper.parse(t());e.analyze(o).then((a=>{const{result:{seo:r,readability:l,inclusiveLanguage:d}}=a;if(r){const e=r[""];e.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),e.results=Ae(e.results),n.dispatch(c.actions.setSeoResultsForKeyword(o.getKeyword(),e.results)),n.dispatch(c.actions.setOverallSeoScore(e.score,o.getKeyword())),n.dispatch(c.actions.refreshSnippetEditor()),i.saveScores(e.score,o.getKeyword())}l&&(l.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),l.results=Ae(l.results),n.dispatch(c.actions.setReadabilityResults(l.results)),n.dispatch(c.actions.setOverallReadabilityScore(l.score)),n.dispatch(c.actions.refreshSnippetEditor()),i.saveContentScore(l.score)),d&&(d.results.forEach((e=>{e.getMarker=()=>()=>s(o,e.marks)})),d.results=Ae(d.results),n.dispatch(c.actions.setInclusiveLanguageResults(d.results)),n.dispatch(c.actions.setOverallInclusiveLanguageScore(d.score)),n.dispatch(c.actions.refreshSnippetEditor()),i.saveInclusiveLanguageScore(d.score)),(0,me.doAction)("yoast.analysis.refresh",a,{paper:o,worker:e,collectData:t,applyMarks:s,store:n,dataCollector:i})})).catch(Te)}const Me=window.wp.blocks,Ie="yoast-measurement-element";function Le(e){let t=document.getElementById(Ie);return t||(t=function(){const e=document.createElement("div");return e.id=Ie,e.style.position="absolute",e.style.left="-9999em",e.style.top=0,e.style.height=0,e.style.overflow="hidden",e.style.fontFamily="arial, sans-serif",e.style.fontSize="20px",e.style.fontWeight="400",document.body.appendChild(e),e}()),t.innerText=e,t.offsetWidth}const Fe=e=>(e=e.filter((e=>e.isValid))).map((e=>{const t=(0,Me.serialize)([e],{isInnerBlocks:!1});return e.blockLength=t&&t.length,e.innerBlocks&&(e.innerBlocks=Fe(e.innerBlocks)),e}));function Be(e){return(0,a.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,r.__)("Not available","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("Not available","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Not available","wordpress-seo")};case"bad":return{className:"bad",screenReaderText:(0,r.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,r.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,r.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,r.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,r.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(L.interpreters.scoreToRating(e))}const De=jQuery,Ue=function(e){"object"==typeof CKEDITOR&&console.warn("YoastSEO currently doesn't support ckEditor. The content analysis currently only works with the HTML editor or TinyMCE."),this._store=e.store};Ue.prototype.getData=function(){const e={title:this.getSnippetTitle(),keyword:Re()?this.getKeyword():"",text:this.getText(),permalink:this.getPermalink(),snippetCite:this.getSnippetCite(),snippetTitle:this.getSnippetTitle(),snippetMeta:this.getSnippetMeta(),name:this.getName(),baseUrl:this.getBaseUrl(),pageTitle:this.getSnippetTitle(),titleWidth:Le(this.getSnippetTitle())},t=this._store.getState();return{...e,metaTitle:(0,a.get)(t,["analysisData","snippet","title"],this.getSnippetTitle()),url:(0,a.get)(t,["snippetEditor","data","slug"],this.getSlug()),meta:(0,a.get)(t,["analysisData","snippet","description"],this.getSnippetMeta())}},Ue.prototype.getKeyword=function(){return document.getElementById("hidden_wpseo_focuskw").value},Ue.prototype.getText=function(){return function(e){let t="";var s;return t=!1===Q(e)||0==(s=e,null!==document.getElementById(s+"_ifr"))?function(e){return document.getElementById(e)&&document.getElementById(e).value||""}(e):tinyMCE.get(e).getContent(),t}(H)},Ue.prototype.getSlug=function(){return document.getElementById("slug").value},Ue.prototype.getPermalink=function(){const e=this.getSlug();return this.getBaseUrl()+e+"/"},Ue.prototype.getSnippetCite=function(){return this.getSlug()},Ue.prototype.getSnippetTitle=function(){return document.getElementById("hidden_wpseo_title").value},Ue.prototype.getSnippetMeta=function(){const e=document.getElementById("hidden_wpseo_desc");return e?e.value:""},Ue.prototype.getName=function(){return document.getElementById("name").value},Ue.prototype.getBaseUrl=function(){return wpseoScriptData.metabox.base_url},Ue.prototype.setDataFromSnippet=function(e,t){switch(t){case"snippet_meta":document.getElementById("hidden_wpseo_desc").value=e;break;case"snippet_cite":document.getElementById("slug").value=e;break;case"snippet_title":document.getElementById("hidden_wpseo_title").value=e}},Ue.prototype.saveSnippetData=function(e){this.setDataFromSnippet(e.title,"snippet_title"),this.setDataFromSnippet(e.urlPath,"snippet_cite"),this.setDataFromSnippet(e.metaDesc,"snippet_meta")},Ue.prototype.bindElementEvents=function(e){this.inputElementEventBinder(e)},Ue.prototype.inputElementEventBinder=function(e){const t=["name",H,"slug","wpseo_focuskw"];for(let s=0;s<t.length;s++)null!==document.getElementById(t[s])&&document.getElementById(t[s]).addEventListener("input",e);!function(e,t){G(t,["input","change","cut","paste"],e),G(t,["hide"],J);const s=["show"];(new K).isPageBuilderActive()||s.push("init"),G(t,s,Z),G("content",["focus"],(function(e){const t=e.target;(function(e){return-1!==e.getContent({format:"raw"}).indexOf("<"+B)})(t)&&(function(e){j(e)(null,[])}(t),YoastSEO.app.disableMarkers()),(0,a.isUndefined)(W)||W.dispatch(c.actions.setMarkerPauseStatus(!0))})),G("content",["blur"],(function(){(0,a.isUndefined)(W)||W.dispatch(c.actions.setMarkerPauseStatus(!1))}))}(e,H)},Ue.prototype.saveScores=function(e){const t=Be(e);document.getElementById("hidden_wpseo_linkdex").value=e,jQuery(window).trigger("YoastSEO:numericScore",e),Ee(t),ve(t)},Ue.prototype.saveContentScore=function(e){const t=Be(e);Re()||(Ee(t),ve(t)),De("#hidden_wpseo_content_score").val(e)},Ue.prototype.saveInclusiveLanguageScore=function(e){const t=Be(e);Re()||xe()||(Ee(t),ve(t)),De("#hidden_wpseo_inclusive_language_score").val(e)};const je=Ue;class Ye{constructor(){this._callbacks=[],this.register=this.register.bind(this)}register(e){(0,a.isFunction)(e)&&this._callbacks.push(e)}getData(){let e={};return this._callbacks.forEach((t=>{e=(0,a.merge)(e,t())})),e}}window.wp.annotations;const qe=function(e){return(0,a.uniq)((0,a.flatten)(e.map((e=>{if(!(0,a.isUndefined)(e.getFieldsToMark()))return e.getFieldsToMark()}))))},$e=window.wp.richText,ze=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:Ke}=L.helpers.htmlEntities,We=e=>{let t=0;return(0,a.forEachRight)(e,(e=>{const[s]=e;let n=s.length;/^<\/?br/.test(s)&&(n-=1),t+=n})),t},Ve="<yoastmark class='yoast-text-mark'>",He="</yoastmark>",Qe='<yoastmark class="yoast-text-mark">';function Ge(e,t,s,n,i){const o=n.clientId,r=(0,$e.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,a.flatMap)(i,(s=>{let i;return i=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,n,i){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),o=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const n="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=n.length,blockEndOffset:t-=n.length}})(t,o,s);t=e.blockStartOffset,o=e.blockEndOffset}if(n.slice(t,o)===i.slice(t,o))return[{startOffset:t,endOffset:o}];const r=((e,t,s)=>{const n=s.slice(0,e),i=s.slice(0,t),o=((e,t,s,n)=>{const i=[...e.matchAll(ze)];s-=We(i);const o=[...t.matchAll(ze)];return{blockStartOffset:s,blockEndOffset:n-=We(o)}})(n,i,e,t),r=((e,t,s,n)=>{let i=[...e.matchAll(Ke)];return(0,a.forEachRight)(i,(e=>{const[,t]=e;s-=t.length})),i=[...t.matchAll(Ke)],(0,a.forEachRight)(i,(e=>{const[,t]=e;n-=t.length})),{blockStartOffset:s,blockEndOffset:n}})(n,i,e=o.blockStartOffset,t=o.blockEndOffset);return{blockStartOffset:e=r.blockStartOffset,blockEndOffset:t=r.blockEndOffset}})(t,o,n);return[{startOffset:r.blockStartOffset,endOffset:r.blockEndOffset}]}return[]}(s,o,n.name,e,r):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),n=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),i=function(e,t,s=!0){const n=[];if(0===e.length)return n;let i,o=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(i=e.indexOf(t,o))>-1;)n.push(i),o=i+t.length;return n}(e,s);if(0===i.length)return[];const o=function(e){let t=e.indexOf(Ve);const s=t>=0;s||(t=e.indexOf(Qe));let n=null;const i=[];for(;t>=0;){if(n=(e=s?e.replace(Ve,""):e.replace(Qe,"")).indexOf(He),n<t)return[];e=e.replace(He,""),i.push({startOffset:t,endOffset:n}),t=s?e.indexOf(Ve):e.indexOf(Qe),n=null}return i}(n),a=[];return o.forEach((e=>{i.forEach((n=>{const i=n+e.startOffset;let o=n+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(o=n+s.length),a.push({startOffset:i,endOffset:o})}))})),a}(r,s),i?i.map((e=>({...e,block:o,richTextIdentifier:t}))):[]}))}const Je=e=>e[0].toUpperCase()+e.slice(1),Ze=(e,t,s,n,i)=>(e=e.map((e=>{const o=`${e.id}-${i[0]}`,a=`${e.id}-${i[1]}`,r=Je(i[0]),l=Je(i[1]),c=e[`json${r}`],d=e[`json${l}`],{marksForFirstSection:p,marksForSecondSection:u}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),h=Ge(c,o,s,n,p),m=Ge(d,a,s,n,u);return h.concat(m)})),(0,a.flattenDeep)(e)),Xe="yoast";let et=[];const tt={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function st(){const e=et.shift();e&&((0,l.dispatch)("core/annotations").__experimentalAddAnnotation(e),nt())}function nt(){(0,a.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(st,{timeout:1e3}):setTimeout(st,150)}const it=(e,t)=>{return(0,a.flatMap)((s=e.name,tt.hasOwnProperty(s)?tt[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];return 0===n.length?[]:Ze(n,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const n=t.attributes[e.key];if(n&&0===n.length)return[];const i=[];return"steps"===e.key&&i.push(Ze(n,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),i.push(Ge(n,"description",e,t,s))),(0,a.flattenDeep)(i)})(s,e,t):function(e,t,s){const n=e.key,i=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:(s||"").toString()})(t,n);return Ge(i,n,e,t,s)}(s,e,t)));var s};function ot(e,t){return(0,a.flatMap)(e,(e=>{const s=function(e){return e.innerBlocks.length>0}(e)?ot(e.innerBlocks,t):[];return it(e,t).concat(s)}))}function at(e){et=[],(0,l.dispatch)("core/annotations").__experimentalRemoveAnnotationsBySource(Xe);const t=qe(e);if(0===e.length)return;let s=(0,l.select)("core/block-editor").getBlocks();var n;t.length>0&&(s=s.filter((e=>t.some((t=>"core/"+t===e.name))))),n=ot(s,e),et=n.map((e=>({blockClientId:e.block,source:Xe,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),nt()}function rt(e,t){let s;Q(V)&&((0,a.isUndefined)(s)&&(s=j(tinyMCE.get(V))),s(e,t)),(0,l.select)("core/block-editor")&&(0,a.isFunction)((0,l.select)("core/block-editor").getBlocks)&&(0,l.select)("core/annotations")&&(0,a.isFunction)((0,l.dispatch)("core/annotations").__experimentalAddAnnotation)&&(function(e,t){tinyMCE.editors.map((e=>j(e))).forEach((s=>s(e,t)))}(e,t),at(t)),(0,me.doAction)("yoast.analysis.applyMarks",t)}var lt=jQuery;function ct(e,t,s,n,i){this._scriptUrl=n,this._options={usedKeywords:t.keyword_usage,usedKeywordsPostTypes:t.keyword_usage_post_types,searchUrl:t.search_url,postUrl:t.post_edit_url},this._keywordUsage=t.keyword_usage,this._usedKeywordsPostTypes=t.keyword_usage_post_types,this._postID=lt("#post_ID, [name=tag_ID]").val(),this._taxonomy=lt("[name=taxonomy]").val()||"",this._nonce=i,this._ajaxAction=e,this._refreshAnalysis=s,this._initialized=!1}ct.prototype.init=function(){const{worker:e}=window.YoastSEO.analysis;this.requestKeywordUsage=(0,a.debounce)(this.requestKeywordUsage.bind(this),500),e.loadScript(this._scriptUrl).then((()=>{e.sendMessage("initialize",this._options,"used-keywords-assessment")})).then((()=>{this._initialized=!0,(0,a.isEqual)(this._options.usedKeywords,this._keywordUsage)?this._refreshAnalysis():e.sendMessage("updateKeywordUsage",this._keywordUsage,"used-keywords-assessment").then((()=>this._refreshAnalysis()))})).catch((e=>console.error(e)))},ct.prototype.setKeyword=function(e){(0,a.has)(this._keywordUsage,e)||this.requestKeywordUsage(e)},ct.prototype.requestKeywordUsage=function(e){lt.post(ajaxurl,{action:this._ajaxAction,post_id:this._postID,keyword:e,taxonomy:this._taxonomy,nonce:this._nonce},this.updateKeywordUsage.bind(this,e),"json")},ct.prototype.updateKeywordUsage=function(e,t){const{worker:s}=window.YoastSEO.analysis,n=t.keyword_usage,i=t.post_types;n&&(0,a.isArray)(n)&&(this._keywordUsage[e]=n,this._usedKeywordsPostTypes[e]=i,this._initialized&&s.sendMessage("updateKeywordUsage",{usedKeywords:this._keywordUsage,usedKeywordsPostTypes:this._usedKeywordsPostTypes},"used-keywords-assessment").then((()=>this._refreshAnalysis())))};const{refreshSnippetEditor:dt,updateData:pt,setFocusKeyword:ut,setCornerstoneContent:ht,setMarkerStatus:mt,setReadabilityResults:gt,setSeoResultsForKeyword:yt}=c.actions;function wt(e,t,s){var n,i;const o=new Ye;function r(){const e={slug:i.val()};window.YoastSEO.store.dispatch(pt(e))}function c(e){(0,a.isUndefined)(e.seoAssessorPresenter)||(e.seoAssessorPresenter.render=function(){}),(0,a.isUndefined)(e.contentAssessorPresenter)||(e.contentAssessorPresenter.render=function(){},e.contentAssessorPresenter.renderIndividualRatings=function(){})}let d;function p(e,t){const s=d||"";d=e.getState().analysisData.snippet,!(0,F.isShallowEqualObjects)(s,d)&&t()}!function(){var d,u,h,m,g,y,w,f,b;h=jQuery(".term-description-wrap").find("td"),m=jQuery(".term-description-wrap").find("label"),g=h.find("textarea").val(),y=document.getElementById("wp-description-wrap"),w=h.find("p"),h.html(""),h.append(y).append(w),document.getElementById("description").value=g,m.replaceWith(m.html()),u=new je({store:t}),d={elementTarget:[H,"yoast_wpseo_focuskw","yoast_wpseo_metadesc","excerpt","editable-post-name","editable-post-name-full"],targets:(f={},Re()&&(f.output="does-not-really-exist-but-it-needs-something"),xe()&&(f.contentOutput="also-does-not-really-exist-but-it-needs-something"),f),callbacks:{getData:u.getData.bind(u)},locale:wpseoScriptData.metabox.contentLocale,contentAnalysisActive:xe(),keywordAnalysisActive:Re(),debouncedRefresh:!1,researcher:new window.yoast.Researcher.default},Re()&&(t.dispatch(ut(u.getKeyword())),d.callbacks.saveScores=u.saveScores.bind(u),d.callbacks.updatedKeywordsResults=function(e){const s=t.getState().focusKeyword;t.dispatch(yt(s,e)),t.dispatch(dt())}),xe()&&(t.dispatch(mt("hidden")),d.callbacks.saveContentScore=u.saveContentScore.bind(u),d.callbacks.updatedContentResults=function(e){t.dispatch(gt(e)),t.dispatch(dt())}),n=new L.App(d),window.YoastSEO=window.YoastSEO||{},window.YoastSEO.app=n,window.YoastSEO.store=t,window.YoastSEO.analysis={},window.YoastSEO.analysis.worker=function(){const e=(0,a.get)(window,["wpseoScriptData","analysis","worker","url"],"analysis-worker.js"),t=(0,L.createWorker)(e),s=(0,a.get)(window,["wpseoScriptData","analysis","worker","dependencies"],[]),n=[];for(const e in s){if(!Object.prototype.hasOwnProperty.call(s,e))continue;const t=window.document.getElementById(`${e}-js-translations`);if(!t)continue;const i=t.innerHTML.slice(214),o=i.indexOf(","),a=i.slice(0,o-1);try{const e=JSON.parse(i.slice(o+1,-4));n.push([a,e])}catch(t){console.warn(`Failed to parse translation data for ${e} to send to the Yoast SEO worker`);continue}}return t.postMessage({dependencies:s,translations:n}),new L.AnalysisWorkerWrapper(t)}(),window.YoastSEO.analysis.collectData=()=>function(e,t,s,n,i){const o=(0,a.cloneDeep)(t.getState());(0,a.merge)(o,s.getData());const r=e.getData();let l=null;i&&(l=i.getBlocks()||[],l=JSON.parse(JSON.stringify(l)),l=Fe(l));const c={text:r.content,textTitle:r.title,keyword:o.focusKeyword,synonyms:o.synonyms,description:o.analysisData.snippet.description||o.snippetEditor.data.description,title:o.analysisData.snippet.title||o.snippetEditor.data.title,slug:o.snippetEditor.data.slug,permalink:o.settings.snippetEditor.baseUrl+o.snippetEditor.data.slug,wpBlocks:l,date:o.settings.snippetEditor.date};n.loaded&&(c.title=n._applyModifications("data_page_title",c.title),c.title=n._applyModifications("title",c.title),c.description=n._applyModifications("data_meta_desc",c.description),c.text=n._applyModifications("content",c.text),c.wpBlocks=n._applyModifications("wpBlocks",c.wpBlocks));const d=o.analysisData.snippet.filteredSEOTitle;return c.titleWidth=Le(d||o.snippetEditor.data.title),c.locale=ke(),c.writingDirection=function(){let e="LTR";return Se().isRtl&&(e="RTL"),e}(),c.shortcodes=window.wpseoScriptData.analysis.plugins.shortcodes?window.wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags:[],c.isFrontPage="1"===(0,a.get)(window,"wpseoScriptData.isFrontPage","0"),L.Paper.parse((0,me.applyFilters)("yoast.analysis.data",c))}(s,window.YoastSEO.store,o,window.YoastSEO.app.pluggable),window.YoastSEO.analysis.applyMarks=(e,t)=>function(){const e=(0,l.select)("yoast-seo/editor").isMarkingAvailable(),t=(0,l.select)("yoast-seo/editor").getMarkerPauseStatus();return!e||t?a.noop:rt}()(e,t),window.YoastSEO.app.refresh=(0,a.debounce)((()=>Ne(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,window.YoastSEO.store,u)),500),window.YoastSEO.app.registerCustomDataCallback=o.register,window.YoastSEO.app.pluggable=new X(window.YoastSEO.app.refresh),window.YoastSEO.app.registerPlugin=window.YoastSEO.app.pluggable._registerPlugin,window.YoastSEO.app.pluginReady=window.YoastSEO.app.pluggable._ready,window.YoastSEO.app.pluginReloaded=window.YoastSEO.app.pluggable._reloaded,window.YoastSEO.app.registerModification=window.YoastSEO.app.pluggable._registerModification,window.YoastSEO.app.registerAssessment=(e,t,s)=>{if(!(0,a.isUndefined)(n.seoAssessor))return window.YoastSEO.app.pluggable._registerAssessment(n.defaultSeoAssessor,e,t,s)&&window.YoastSEO.app.pluggable._registerAssessment(n.cornerStoneSeoAssessor,e,t,s)},window.YoastSEO.app.changeAssessorOptions=function(e){window.YoastSEO.analysis.worker.initialize(e).catch(Te),window.YoastSEO.app.refresh()},function(e,t,s){const n=Se();if(!n.previouslyUsedKeywordActive)return;const i=new ct("get_term_keyword_usage",n,e,(0,a.get)(window,["wpseoScriptData","analysis","worker","keywords_assessment_url"],"used-keywords-assessment.js"),(0,a.get)(window,["wpseoScriptData","usedKeywordsNonce"],""));i.init();let o={};s.subscribe((()=>{const e=s.getState()||{};e.focusKeyword!==o.focusKeyword&&(o=e,i.setKeyword(e.focusKeyword))}))}(n.refresh,0,t),t.subscribe(p.bind(null,t,n.refresh)),Re()&&(n.seoAssessor=new L.TaxonomyAssessor(n.config.researcher),n.seoAssessorPresenter.assessor=n.seoAssessor),window.YoastSEO.wp={},window.YoastSEO.wp.replaceVarsPlugin=new he(n,t),function(e,t){let s=[];s=(0,me.applyFilters)("yoast.analysis.shortcodes",s);const n=wpseoScriptData.analysis.plugins.shortcodes.wpseo_shortcode_tags;s=s.filter((e=>n.includes(e))),s.length>0&&(t.dispatch(_e(s)),window.YoastSEO.wp.shortcodePlugin=new fe({registerPlugin:e.registerPlugin,registerModification:e.registerModification,pluginReady:e.pluginReady,pluginReloaded:e.pluginReloaded},s))}(n,t),window.YoastSEO.analyzerArgs=d,(i=e("#slug")).on("change",r),u.bindElementEvents((0,a.debounce)((()=>Ne(window.YoastSEO.analysis.worker,window.YoastSEO.analysis.collectData,window.YoastSEO.analysis.applyMarks,window.YoastSEO.store,u)),500)),Re()&&(Ee(b=Be(e("#hidden_wpseo_linkdex").val())),ve(b)),xe()&&function(){var t=Be(e("#hidden_wpseo_content_score").val());Ee(t),ve(t)}(),Oe()&&function(){const t=Be(e("#hidden_wpseo_inclusive_language_score").val());Ee(t),ve(t)}(),window.YoastSEO.analysis.worker.initialize(function(e={}){const t={locale:ke(),contentAnalysisActive:xe(),keywordAnalysisActive:Re(),inclusiveLanguageAnalysisActive:Oe(),defaultQueryParams:(0,a.get)(window,["wpseoAdminL10n","default_query_params"],{}),logLevel:(0,a.get)(window,["wpseoScriptData","analysis","worker","log_level"],"ERROR"),enabledFeatures:(0,Pe.enabledFeatures)()};return(0,a.merge)(t,e)}({useTaxonomy:!0})).then((()=>{jQuery(window).trigger("YoastSEO:ready")})).catch(Te),c(n);const _=n.initAssessorPresenters.bind(n);n.initAssessorPresenters=function(){_(),c(n)};let E={title:(v=u).getSnippetTitle(),slug:v.getSnippetCite(),description:v.getSnippetMeta()};var v;!function(e){const s=document.getElementById("hidden_wpseo_is_cornerstone");let n="1"===s.value;t.dispatch(ht(n)),e.changeAssessorOptions({useCornerstone:n}),t.subscribe((()=>{const i=t.getState();i.isCornerstone!==n&&(n=i.isCornerstone,s.value=n?"1":"0",e.changeAssessorOptions({useCornerstone:n}))}))}(n);const S=function(e){const t={};if((0,a.isUndefined)(e))return t;t.title=e.title_template;const s=e.metadesc_template;return(0,a.isEmpty)(s)||(t.description=s),t}(wpseoScriptData.metabox);E=function(e,t){const s={...e};return(0,a.forEach)(t,((t,n)=>{(0,a.has)(e,n)&&""===e[n]&&(s[n]=t)})),s}(E,S),t.dispatch(pt(E));let k=t.getState().focusKeyword;ee(window.YoastSEO.analysis.worker.runResearch,window.YoastSEO.store,k);const x=(0,a.debounce)((()=>{n.refresh()}),50);t.subscribe((()=>{const e=t.getState().focusKeyword;k!==e&&(k=e,ee(window.YoastSEO.analysis.worker.runResearch,window.YoastSEO.store,k),document.getElementById("hidden_wpseo_focuskw").value=k,x());const s=function(e){const t=e.getState().snippetEditor.data;return{title:t.title,slug:t.slug,description:t.description}}(t),n=function(e,t){const s={...e};return(0,a.forEach)(t,((t,n)=>{(0,a.has)(e,n)&&e[n].trim()===t&&(s[n]="")})),s}(s,S);E.title!==s.title&&u.setDataFromSnippet(n.title,"snippet_title"),E.slug!==s.slug&&u.setDataFromSnippet(n.slug,"snippet_cite"),E.description!==s.description&&u.setDataFromSnippet(n.description,"snippet_meta"),E.title=s.title,E.slug=s.slug,E.description=s.description})),Ce=!0,window.YoastSEO.app.refresh()}()}window.yoastHideMarkers=!0,window.YoastReplaceVarPlugin=he,window.YoastShortcodePlugin=be;let ft=null;const bt=()=>{if(null===ft){const e=(0,l.dispatch)("yoast-seo/editor").runAnalysis;ft=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new X(e)}return ft},_t=(e,t,s)=>bt().loaded?bt()._applyModifications(e,t,s):t;function Et(){const{getAnalysisData:e,getEditorDataTitle:t,getIsFrontPage:s}=(0,l.select)("yoast-seo/editor");let n=e();n={...n,textTitle:t(),isFrontPage:s()};const i=function(e){return e.title=_t("data_page_title",e.title),e.title=_t("title",e.title),e.description=_t("data_meta_desc",e.description),e.text=_t("content",e.text),e}(n);return(0,me.applyFilters)("yoast.analysis.data",i)}(0,a.debounce)((async function(e,t){const{text:s,...n}=t,i=new L.Paper(s,n);try{const t=await e.analyze(i),{seo:s,readability:n,inclusiveLanguage:o}=t.result;if(s){const e=s[""];e.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),e.results=Ae(e.results),(0,l.dispatch)("yoast-seo/editor").setSeoResultsForKeyword(i.getKeyword(),e.results),(0,l.dispatch)("yoast-seo/editor").setOverallSeoScore(e.score,i.getKeyword())}n&&(n.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),n.results=Ae(n.results),(0,l.dispatch)("yoast-seo/editor").setReadabilityResults(n.results),(0,l.dispatch)("yoast-seo/editor").setOverallReadabilityScore(n.score)),o&&(o.results.forEach((e=>{e.getMarker=()=>()=>window.YoastSEO.analysis.applyMarks(i,e.marks)})),o.results=Ae(o.results),(0,l.dispatch)("yoast-seo/editor").setInclusiveLanguageResults(o.results),(0,l.dispatch)("yoast-seo/editor").setOverallInclusiveLanguageScore(o.score)),(0,me.doAction)("yoast.analysis.run",t,{paper:i})}catch(e){}}),500);const vt=()=>{const{getContentLocale:e}=(0,l.select)("yoast-seo/editor"),t=((...e)=>()=>e.map((e=>e())))(e,Et),s=(()=>{const{setEstimatedReadingTime:e,setFleschReadingEase:t,setTextLength:s}=(0,l.dispatch)("yoast-seo/editor"),n=(0,a.get)(window,"YoastSEO.analysis.worker.runResearch",a.noop);return()=>{const i=L.Paper.parse(Et());n("readingTime",i).then((t=>e(t.result))),n("getFleschReadingScore",i).then((e=>{e.result&&t(e.result)})),n("wordCountInText",i).then((e=>s(e.result)))}})();return setTimeout(s,1500),((e,t)=>{let s=e();return()=>{const n=e();(0,a.isEqual)(n,s)||(s=n,t((0,a.clone)(n)))}})(t,s)},St=window.React,kt=window.wp.components,xt=window.wp.element,Rt=window.yoast.uiLibrary,Ot=window.yoast.propTypes;var Pt=e.n(Ot);const Tt=(e,t)=>{try{return(0,xt.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}};Pt().string.isRequired;const Ct=St.forwardRef((function(e,t){return St.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),St.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),At=St.forwardRef((function(e,t){return St.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),St.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),Nt=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:n,isProductCopy:i,title:o,upsellLabel:a,newToText:l,bundleNote:c,ctbId:d})=>{const{onClose:p,initialFocus:u}=(0,Rt.useModalContext)(),h={a:(0,St.createElement)(Gt,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,St.createElement)(At,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,St.createElement)(St.Fragment,null,(0,St.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,St.createElement)("div",{className:"yst-relative yst-w-full"},(0,St.createElement)(bs,{videoId:"vmrahpfjxp",thumbnail:t,wistiaEmbedPermission:s}),(0,St.createElement)(Rt.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,St.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,St.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,St.createElement)("span",{className:"yst-logo-icon"}),l))),(0,St.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,St.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,St.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},o),(0,St.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},Tt(i?(0,r.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,r.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,r.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,r.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,St.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,St.createElement)(Rt.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:u,"data-action":"load-nfd-ctb","data-ctb-id":d},(0,St.createElement)(Ct,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),a,(0,St.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,r.__)("(Opens in a new browser tab)","wordpress-seo")))),c,(0,St.createElement)(Rt.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,r.__)("Close","wordpress-seo"))))};Nt.propTypes={learnMoreLink:Pt().string.isRequired,upsellLink:Pt().string.isRequired,thumbnail:Pt().shape({src:Pt().string.isRequired,width:Pt().string,height:Pt().string}).isRequired,wistiaEmbedPermission:Pt().shape({value:Pt().bool.isRequired,status:Pt().string.isRequired,set:Pt().func.isRequired}).isRequired,title:Pt().string,upsellLabel:Pt().string,newToText:Pt().string,isProductCopy:Pt().bool,bundleNote:Pt().oneOfType([Pt().string,Pt().element]),ctbId:Pt().string},Nt.defaultProps={title:(0,r.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,r.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const Mt=({learnMoreLink:e,thumbnail:t,wistiaEmbedPermission:s,upsellLink:n,upsellLabel:i,newToText:o,bundleNote:a,ctbId:l})=>{const{onClose:c,initialFocus:d}=(0,Rt.useModalContext)(),p={a:(0,St.createElement)(Gt,{href:e,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,St.createElement)(At,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,St.createElement)("br",null)};return(0,St.createElement)(St.Fragment,null,(0,St.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,St.createElement)("div",{className:"yst-relative yst-w-full"},(0,St.createElement)(bs,{videoId:"vun9z1dpfh",thumbnail:t,wistiaEmbedPermission:s}),(0,St.createElement)(Rt.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,r.__)("Beta","wordpress-seo-premium"))),(0,St.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,St.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,St.createElement)("span",{className:"yst-logo-icon"}),o))),(0,St.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,St.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,St.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,r.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,r.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,St.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},Tt((0,r.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,r.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),p))),(0,St.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,St.createElement)(Rt.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:n,target:"_blank",ref:d,"data-action":"load-nfd-ctb","data-ctb-id":l},(0,St.createElement)(Ct,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),i,(0,St.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,r.__)("(Opens in a new browser tab)","wordpress-seo")))),a,(0,St.createElement)(Rt.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:c},(0,r.__)("Close","wordpress-seo"))))};Mt.propTypes={learnMoreLink:Pt().string.isRequired,upsellLink:Pt().string.isRequired,thumbnail:Pt().shape({src:Pt().string.isRequired,width:Pt().string,height:Pt().string}).isRequired,wistiaEmbedPermission:Pt().shape({value:Pt().bool.isRequired,status:Pt().string.isRequired,set:Pt().func.isRequired}).isRequired,upsellLabel:Pt().string,newToText:Pt().string,bundleNote:Pt().oneOfType([Pt().string,Pt().element]),ctbId:Pt().string},Mt.defaultProps={upsellLabel:(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,r.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const It=({handleRefreshClick:e,supportLink:t})=>(0,St.createElement)("div",{className:"yst-flex yst-gap-2"},(0,St.createElement)(Rt.Button,{onClick:e},(0,r.__)("Refresh this page","wordpress-seo")),(0,St.createElement)(Rt.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,r.__)("Contact support","wordpress-seo")));It.propTypes={handleRefreshClick:Pt().func.isRequired,supportLink:Pt().string.isRequired};const Lt=({handleRefreshClick:e,supportLink:t})=>(0,St.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,St.createElement)(Rt.Button,{className:"yst-order-last",onClick:e},(0,r.__)("Refresh this page","wordpress-seo")),(0,St.createElement)(Rt.Button,{variant:"secondary",as:"a",href:t,target:"_blank",rel:"noopener"},(0,r.__)("Contact support","wordpress-seo")));Lt.propTypes={handleRefreshClick:Pt().func.isRequired,supportLink:Pt().string.isRequired};const Ft=({error:e,children:t})=>(0,St.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,St.createElement)(Rt.Title,null,(0,r.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,St.createElement)("p",null,(0,r.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,St.createElement)(Rt.Alert,{variant:"error"},(null==e?void 0:e.message)||(0,r.__)("Undefined error message.","wordpress-seo")),(0,St.createElement)("p",null,(0,r.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),t);Ft.propTypes={error:Pt().object.isRequired,children:Pt().node},Ft.VerticalButtons=Lt,Ft.HorizontalButtons=It;var Bt;function Dt(){return Dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Dt.apply(this,arguments)}Pt().string,Pt().node.isRequired,Pt().node.isRequired,Pt().node,Pt().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const Ut=e=>St.createElement("svg",Dt({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},e),Bt||(Bt=St.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),jt=window.ReactDOM;var Yt,qt,$t;(qt=Yt||(Yt={})).Pop="POP",qt.Push="PUSH",qt.Replace="REPLACE",function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}($t||($t={})),new Set(["lazy","caseSensitive","path","id","index","children"]),Error;const zt=["post","put","patch","delete"],Kt=(new Set(zt),["get",...zt]);new Set(Kt),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred"),St.Component,St.startTransition,new Promise((()=>{})),St.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}var Wt,Vt,Ht,Qt;new Map,St.startTransition,jt.flushSync,St.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(Qt=Wt||(Wt={})).UseScrollRestoration="useScrollRestoration",Qt.UseSubmit="useSubmit",Qt.UseSubmitFetcher="useSubmitFetcher",Qt.UseFetcher="useFetcher",Qt.useViewTransitionState="useViewTransitionState",(Ht=Vt||(Vt={})).UseFetcher="useFetcher",Ht.UseFetchers="useFetchers",Ht.UseScrollRestoration="useScrollRestoration",Pt().string.isRequired,Pt().string;const Gt=({href:e,children:t,...s})=>(0,St.createElement)(Rt.Link,{target:"_blank",rel:"noopener noreferrer",...s,href:e},t,(0,St.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,r.__)("(Opens in a new browser tab)","wordpress-seo")));Gt.propTypes={href:Pt().string.isRequired,children:Pt().node},Gt.defaultProps={children:null};const Jt=St.forwardRef((function(e,t){return St.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),St.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var Zt,Xt,es;function ts(){return ts=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},ts.apply(this,arguments)}const ss=e=>St.createElement("svg",ts({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),Zt||(Zt=St.createElement("defs",null,St.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),Xt||(Xt=St.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),es||(es=St.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),St.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function ns(){return ns=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},ns.apply(this,arguments)}const is=e=>St.createElement("svg",ns({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},e),St.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var os,as,rs,ls,cs,ds,ps,us,hs;function ms(){return ms=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},ms.apply(this,arguments)}const gs=e=>St.createElement("svg",ms({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},e),os||(os=St.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),as||(as=St.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),rs||(rs=St.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),ls||(ls=St.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),cs||(cs=St.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),ds||(ds=St.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),ps||(ps=St.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),us||(us=St.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),hs||(hs=St.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),ys=({link:e,linkProps:t,isPromotionActive:s})=>{let n=(0,xt.useMemo)((()=>(0,r.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),i=Tt((0,r.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,r.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,St.createElement)("span",{className:"yst-whitespace-nowrap"})});const o=s("black-friday-2024-promotion");return o&&(n=(0,xt.useMemo)((()=>(0,r.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),i=Tt((0,r.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,r.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,St.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,St.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,St.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,St.createElement)(gs,null)),o&&(0,St.createElement)("div",{className:"sidebar__sale_banner_container"},(0,St.createElement)("div",{className:"sidebar__sale_banner"},(0,St.createElement)("span",{className:"banner_text"},(0,r.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,St.createElement)(Rt.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},i),(0,St.createElement)("p",{className:"yst-mt-2"},n),(0,St.createElement)(Rt.Button,{as:"a",variant:"upsell",href:e,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...t},(0,St.createElement)("span",null,o?(0,r.__)("Buy now","wordpress-seo"):i),(0,St.createElement)(Jt,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,St.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,r.__)("30-day money back guarantee.","wordpress-seo")),(0,St.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,St.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,St.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,r.__)("Read reviews from real users","wordpress-seo")),(0,St.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,St.createElement)(Ut,{className:"yst-w-5 yst-h-5"}),(0,St.createElement)("span",{className:"yst-flex yst-gap-1"},(0,St.createElement)(is,{className:"yst-w-5 yst-h-5"}),(0,St.createElement)(is,{className:"yst-w-5 yst-h-5"}),(0,St.createElement)(is,{className:"yst-w-5 yst-h-5"}),(0,St.createElement)(is,{className:"yst-w-5 yst-h-5"}),(0,St.createElement)(ss,{className:"yst-w-5 yst-h-5"})),(0,St.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};ys.propTypes={link:Pt().string.isRequired,linkProps:Pt().object,isPromotionActive:Pt().func},ys.defaultProps={linkProps:{},isPromotionActive:a.noop};const ws=({premiumLink:e,premiumUpsellConfig:t,isPromotionActive:s})=>{const n=s("black-friday-2024-promotion");return(0,St.createElement)(Rt.Paper,{as:"div",className:"xl:yst-max-w-3xl"},n&&(0,St.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,St.createElement)("div",null,(0,r.__)("30% OFF","wordpress-seo")),(0,St.createElement)("div",null,(0,r.__)("BLACK FRIDAY","wordpress-seo"))),(0,St.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,St.createElement)(Rt.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,r.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,r.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,St.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},[(0,r.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,r.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,r.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,r.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,r.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,r.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,r.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,r.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,r.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,r.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,r.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,r.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")].map(((e,t)=>(0,St.createElement)("li",{key:`upsell-benefit-${t}`},Tt(e,{strong:(0,St.createElement)("span",{className:"yst-font-semibold"})}))))),(0,St.createElement)(Rt.Button,{as:"a",variant:"upsell",size:"extra-large",href:e,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...t},n?(0,r.__)("Claim your 30% off now!","wordpress-seo"):(0,r.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,r.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,St.createElement)(Jt,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};ws.propTypes={premiumLink:Pt().string.isRequired,premiumUpsellConfig:Pt().object,isPromotionActive:Pt().func},ws.defaultProps={premiumUpsellConfig:{},isPromotionActive:a.noop},Pt().string.isRequired,Pt().object.isRequired,Pt().string.isRequired,Pt().func.isRequired,St.forwardRef((function(e,t){return St.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},e),St.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),Pt().bool.isRequired,Pt().func,Pt().func,Pt().string.isRequired,Pt().string.isRequired,Pt().string.isRequired,Pt().string.isRequired;const fs=window.yoast.reactHelmet,bs=({videoId:e,thumbnail:t,wistiaEmbedPermission:s})=>{const[n,i]=(0,xt.useState)(s.value?O:x),o=(0,xt.useCallback)((()=>i(O)),[i]),a=(0,xt.useCallback)((()=>{s.value?o():i(R)}),[s.value,o,i]),l=(0,xt.useCallback)((()=>i(x)),[i]),c=(0,xt.useCallback)((()=>{s.set(!0),o()}),[s.set,o]);return(0,St.createElement)(St.Fragment,null,s.value&&(0,St.createElement)(fs.Helmet,null,(0,St.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,St.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},n===x&&(0,St.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:a},(0,St.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...t})),n===R&&(0,St.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,St.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},s.status===k&&(0,St.createElement)(Rt.Spinner,null),s.status!==k&&(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,r.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,St.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,St.createElement)(Rt.Button,{type:"button",variant:"secondary",onClick:l,disabled:s.status===k},(0,r.__)("Deny","wordpress-seo")),(0,St.createElement)(Rt.Button,{type:"button",variant:"primary",onClick:c,disabled:s.status===k},(0,r.__)("Allow","wordpress-seo")))),s.value&&n===O&&(0,St.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===e&&(0,St.createElement)(Rt.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==e&&(0,St.createElement)("div",{className:`wistia_embed wistia_async_${e} videoFoam=true`}))))};bs.propTypes={videoId:Pt().string.isRequired,thumbnail:Pt().shape({src:Pt().string.isRequired,width:Pt().string,height:Pt().string}).isRequired,wistiaEmbedPermission:Pt().shape({value:Pt().bool.isRequired,status:Pt().string.isRequired,set:Pt().func.isRequired}).isRequired},St.forwardRef((function(e,t){return St.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:t},e),St.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),Pt().bool.isRequired,Pt().func.isRequired,Pt().func,Pt().string;const _s="yoast-seo/editor",Es=()=>{const e=(e=>{const t=(0,l.useSelect)((e=>e(_s).getIsPremium()),[]),s=(0,l.useSelect)((e=>e(_s).getIsWooSeoActive()),[]),n=(0,l.useSelect)((e=>e(_s).getIsWooCommerceActive()),[]),i=(0,l.useSelect)((e=>e(_s).getIsProduct()),[]),o=(0,l.useSelect)((e=>e(_s).getIsProductTerm()),[]),a={upsellLink:e.premium};if(n&&(i||o)){const n=(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to Yoast WooCommerce SEO. */
(0,r.__)("%1$s + %2$s","wordpress-seo"),"Yoast SEO Premium","Yoast WooCommerce SEO");a.newToText=(0,r.sprintf)(/* translators: %1$s expands to Yoast SEO Premium and Yoast WooCommerce SEO. */
(0,r.__)("New in %1$s","wordpress-seo"),n),t?(a.upsellLabel=(0,r.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,r.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO"),a.upsellLink=e.woo,a.ctbId="5b32250e-e6f0-44ae-ad74-3cefc8e427f9"):s||(a.upsellLabel=`${(0,r.sprintf)(/* translators: %1$s expands to Woo Premium bundle. */
(0,r.__)("Unlock with the %1$s","wordpress-seo"),"Woo Premium bundle")}*`,a.bundleNote=(0,St.createElement)("div",{className:"yst-text-xs yst-text-slate-500 yst-mt-2"},`*${n}`),a.upsellLink=e.bundle,a.ctbId="c7e7baa1-2020-420c-a427-89701700b607")}return a})({premium:(0,l.useSelect)((e=>e(_s).selectLink("https://yoa.st/ai-generator-upsell")),[]),bundle:(0,l.useSelect)((e=>e(_s).selectLink("https://yoa.st/ai-generator-upsell-woo-seo-premium-bundle")),[]),woo:(0,l.useSelect)((e=>e(_s).selectLink("https://yoa.st/ai-generator-upsell-woo-seo")),[])}),t=(0,l.useSelect)((e=>e(_s).getIsWooCommerceActive()),[]),s=(0,l.useSelect)((e=>e(_s).getIsProduct()),[]);t&&s&&(e.title=(0,r.__)("Generate product titles & descriptions with AI!","wordpress-seo"),e.isProductCopy=!0);const n=(0,l.useSelect)((e=>e(_s).selectLink("https://yoa.st/ai-generator-learn-more")),[]),i=(0,l.useSelect)((e=>e(_s).selectImageLink("ai-generator-preview.png")),[]),o=(0,xt.useMemo)((()=>({src:i,width:"432",height:"244"})),[i]),a=(0,l.useSelect)((e=>e(_s).selectWistiaEmbedPermissionValue()),[]),c=(0,l.useSelect)((e=>e(_s).selectWistiaEmbedPermissionStatus()),[]),{setWistiaEmbedPermission:d}=(0,l.useDispatch)(_s),p=(0,xt.useMemo)((()=>({value:a,status:c,set:d})),[a,c,d]);return(0,St.createElement)(Nt,{learnMoreLink:n,thumbnail:o,wistiaEmbedPermission:p,...e})},vs=({fieldId:e})=>{const[t,,,s,n]=(0,Rt.useToggleState)(!1),i=(0,xt.useCallback)((()=>{s()}),[s]),o=(0,xt.useRef)(null);return(0,St.createElement)(St.Fragment,null,(0,St.createElement)("button",{type:"button",id:`yst-replacevar__use-ai-button__${e}`,className:"yst-replacevar__use-ai-button-upsell",onClick:i},(0,r.__)("Use AI","wordpress-seo")),(0,St.createElement)(Rt.Modal,{className:"yst-introduction-modal",isOpen:t,onClose:n,initialFocus:o},(0,St.createElement)(Rt.Modal.Panel,{className:"yst-max-w-lg yst-p-0 yst-rounded-3xl"},(0,St.createElement)(Es,{onClose:n,focusElementRef:o}))))};vs.propTypes={fieldId:Pt().string.isRequired};const Ss="yoast-seo/editor";n()((()=>{window.wpseoTermScraperL10n=window.wpseoScriptData.metabox,function(e){function t(){e("#copy-home-meta-description").on("click",(function(){e("#open_graph_frontpage_desc").val(e("#meta_description").val())}))}function s(){var t=e("#wpseo-conf");if(t.length){var s=t.attr("action").split("#")[0];t.attr("action",s+window.location.hash)}}function n(){var t=window.location.hash.replace("#top#","");-1!==t.search("#top")&&(t=window.location.hash.replace("#top%23","")),""!==t&&"#"!==t.charAt(0)||(t=e(".wpseotab").attr("id")),e("#"+t).addClass("active"),e("#"+t+"-tab").addClass("nav-tab-active").trigger("click")}function i(t){const s=e("#noindex-author-noposts-wpseo-container");t?s.show():s.hide()}e.fn._wpseoIsInViewport=function(){const t=e(this).offset().top,s=t+e(this).outerHeight(),n=e(window).scrollTop(),i=n+e(window).height();return t>n&&s<i},e(window).on("hashchange",(function(){n(),s()})),window.setWPOption=function(t,s,n,i){e.post(ajaxurl,{action:"wpseo_set_option",option:t,newval:s,_wpnonce:i},(function(t){t&&e("#"+n).hide()}))},window.wpseoCopyHomeMeta=t,window.wpseoSetTabHash=s,e(document).ready((function(){s(),"function"==typeof window.wpseoRedirectOldFeaturesTabToNewSettings&&window.wpseoRedirectOldFeaturesTabToNewSettings(),e("#disable-author input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#author-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change");const o=e("#noindex-author-wpseo-off"),l=e("#noindex-author-wpseo-on");o.is(":checked")||i(!1),l.on("change",(()=>{e(this).is(":checked")||i(!1)})),o.on("change",(()=>{e(this).is(":checked")||i(!0)})),e("#disable-date input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#date-archives-titles-metas-content").toggle("off"===e(this).val())})).trigger("change"),e("#disable-attachment input[type='radio']").on("change",(function(){e(this).is(":checked")&&e("#media_settings").toggle("off"===e(this).val())})).trigger("change"),e("#disable-post_format").on("change",(function(){e("#post_format-titles-metas").toggle(e(this).is(":not(:checked)"))})).trigger("change"),e("#wpseo-tabs").find("a").on("click",(function(t){var s,n,i,o=!0;if(s=e(this),n=!!e("#first-time-configuration-tab").filter(".nav-tab-active").length,i=!!s.filter("#first-time-configuration-tab").length,n&&!i&&window.isStepBeingEdited&&(o=confirm((0,r.__)("There are unsaved changes in one or more steps. Leaving means that those changes may not be saved. Are you sure you want to leave?","wordpress-seo"))),o){window.isStepBeingEdited=!1,e("#wpseo-tabs").find("a").removeClass("nav-tab-active"),e(".wpseotab").removeClass("active");var a=e(this).attr("id").replace("-tab",""),l=e("#"+a);l.addClass("active"),e(this).addClass("nav-tab-active"),l.hasClass("nosave")?e("#wpseo-submit-container").hide():e("#wpseo-submit-container").show(),e(window).trigger("yoast-seo-tab-change"),"first-time-configuration"===a?(e(".notice-yoast").slideUp(),e(".yoast_premium_upsell").slideUp(),e("#sidebar-container").hide()):(e(".notice-yoast").slideDown(),e(".yoast_premium_upsell").slideDown(),e("#sidebar-container").show())}else t.preventDefault(),e("#first-time-configuration-tab").trigger("focus")})),e("#yoast-first-time-configuration-notice a").on("click",(function(){e("#first-time-configuration-tab").click()})),e("#company_or_person").on("change",(function(){var t=e(this).val();"company"===t?(e("#knowledge-graph-company").show(),e("#knowledge-graph-person").hide()):"person"===t?(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").show()):(e("#knowledge-graph-company").hide(),e("#knowledge-graph-person").hide())})).trigger("change"),e(".switch-yoast-seo input").on("keydown",(function(e){"keydown"===e.type&&13===e.which&&e.preventDefault()})),e("body").on("click","button.toggleable-container-trigger",(t=>{const s=e(t.currentTarget),n=s.parent().siblings(".toggleable-container");n.toggleClass("toggleable-container-hidden"),s.attr("aria-expanded",!n.hasClass("toggleable-container-hidden")).find("span").toggleClass("dashicons-arrow-up-alt2 dashicons-arrow-down-alt2")}));const c=e("#opengraph"),d=e("#wpseo-opengraph-settings");c.length&&d.length&&(d.toggle(c[0].checked),c.on("change",(e=>{d.toggle(e.target.checked)}))),t(),n(),function(){if(!e("#enable_xml_sitemap input[type=radio]").length)return;const t=e("#yoast-seo-sitemaps-disabled-warning");e("#enable_xml_sitemap input[type=radio]").on("change",(function(){"off"===this.value?t.show():t.hide()}))}(),function(){const t=e("#wpseo-submit-container-float"),s=e("#wpseo-submit-container-fixed");if(!t.length||!s.length)return;function n(){t._wpseoIsInViewport()?s.hide():s.show()}e(window).on("resize scroll",(0,a.debounce)(n,100)),e(window).on("yoast-seo-tab-change",n);const i=e(".wpseo-message");i.length&&window.setTimeout((()=>{i.fadeOut()}),5e3),n()}()}))}(o()),function(e){function t(e){e&&(e.focus(),e.click())}function s(){if(e(".wpseo-meta-section").length>0){const t=e(".wpseo-meta-section-link");e(".wpseo-metabox-menu li").filter((function(){return"#wpseo-meta-section-content"===e(this).find(".wpseo-meta-section-link").attr("href")})).addClass("active").find("[role='tab']").addClass("yoast-active-tab"),e("#wpseo-meta-section-content, .wpseo-meta-section-react").addClass("active"),t.on("click",(function(s){var n=e(this).attr("id"),i=e(this).attr("href"),o=e(i);s.preventDefault(),e(".wpseo-metabox-menu li").removeClass("active").find("[role='tab']").removeClass("yoast-active-tab"),e(".wpseo-meta-section").removeClass("active"),e(".wpseo-meta-section-react.active").removeClass("active"),"#wpseo-meta-section-content"===i&&e(".wpseo-meta-section-react").addClass("active"),o.addClass("active"),e(this).parent("li").addClass("active").find("[role='tab']").addClass("yoast-active-tab");const a=function(e,t={}){return new CustomEvent("YoastSEO:metaTabChange",{detail:t})}(0,{metaTabId:n});window.dispatchEvent(a),this&&(t.attr({"aria-selected":"false",tabIndex:"-1"}),this.removeAttribute("tabindex"),this.setAttribute("aria-selected","true"))}))}}window.wpseoInitTabs=s,window.wpseo_init_tabs=s,e(".wpseo-meta-section").each((function(t,s){e(s).find(".wpseotab:first").addClass("active")})),window.wpseo_init_tabs(),function(){const s=e(".yoast-aria-tabs"),n=s.find("[role='tab']"),i=s.attr("aria-orientation")||"horizontal";n.attr({"aria-selected":!1,tabIndex:"-1"}),n.filter(".yoast-active-tab").removeAttr("tabindex").attr("aria-selected","true"),n.on("keydown",(function(s){-1!==[32,35,36,37,38,39,40].indexOf(s.which)&&("horizontal"===i&&-1!==[38,40].indexOf(s.which)||"vertical"===i&&-1!==[37,39].indexOf(s.which)||function(s,n){const i=s.which,o=n.index(e(s.target));switch(i){case 32:s.preventDefault(),t(n[o]);break;case 35:s.preventDefault(),t(n[n.length-1]);break;case 36:s.preventDefault(),t(n[0]);break;case 37:case 38:s.preventDefault(),t(n[o-1<0?n.length-1:o-1]);break;case 39:case 40:s.preventDefault(),t(n[o+1===n.length?0:o+1])}}(s,n))}))}()}(o());const e=function(){const e=(0,l.registerStore)("yoast-seo/editor",{reducer:(0,l.combineReducers)(c.reducers),selectors:c.selectors,actions:(0,a.pickBy)(c.actions,(e=>"function"==typeof e)),controls:t});return(e=>{e.dispatch(c.actions.setSettings({socialPreviews:{sitewideImage:window.wpseoScriptData.sitewideSocialImage,siteName:window.wpseoScriptData.metabox.site_name,contentImage:window.wpseoScriptData.metabox.first_content_image,twitterCardType:window.wpseoScriptData.metabox.twitterCardType},snippetEditor:{baseUrl:window.wpseoScriptData.metabox.base_url,date:window.wpseoScriptData.metabox.metaDescriptionDate,recommendedReplacementVariables:window.wpseoScriptData.analysis.plugins.replaceVars.recommended_replace_vars,siteIconUrl:window.wpseoScriptData.metabox.siteIconUrl}})),e.dispatch(c.actions.setSEMrushChangeCountry(window.wpseoScriptData.metabox.countryCode)),e.dispatch(c.actions.setSEMrushLoginStatus(window.wpseoScriptData.metabox.SEMrushLoginStatus)),e.dispatch(c.actions.setWincherLoginStatus(window.wpseoScriptData.metabox.wincherLoginStatus,!1)),e.dispatch(c.actions.setWincherWebsiteId(window.wpseoScriptData.metabox.wincherWebsiteId)),e.dispatch(c.actions.setWincherAutomaticKeyphaseTracking(window.wpseoScriptData.metabox.wincherAutoAddKeyphrases)),e.dispatch(c.actions.setDismissedAlerts((0,a.get)(window,"wpseoScriptData.dismissedAlerts",{}))),e.dispatch(c.actions.setCurrentPromotions((0,a.get)(window,"wpseoScriptData.currentPromotions",[]))),e.dispatch(c.actions.setIsPremium(Boolean((0,a.get)(window,"wpseoScriptData.metabox.isPremium",!1)))),e.dispatch(c.actions.setPostId(Number((0,a.get)(window,"wpseoScriptData.postId",null)))),e.dispatch(c.actions.setAdminUrl((0,a.get)(window,"wpseoScriptData.adminUrl",""))),e.dispatch(c.actions.setLinkParams((0,a.get)(window,"wpseoScriptData.linkParams",{}))),e.dispatch(c.actions.setPluginUrl((0,a.get)(window,"wpseoScriptData.pluginUrl",""))),e.dispatch(c.actions.setWistiaEmbedPermissionValue("1"===(0,a.get)(window,"wpseoScriptData.wistiaEmbedPermission",!1)))})(e),e}();window.yoast.initEditorIntegration(e);const s=new window.yoast.EditorData(a.noop,e,H);s.initialize(window.wpseoScriptData.analysis.plugins.replaceVars.replace_vars),wt(o(),e,s),(()=>{if((0,l.select)("yoast-seo/editor").getPreference("isInsightsEnabled",!1))(0,l.dispatch)("yoast-seo/editor").loadEstimatedReadingTime(),(0,l.subscribe)((0,a.debounce)(vt(),1500,{maxWait:3e3}))})(),(()=>{const e=(0,l.select)(Ss).getIsPremium(),t=(0,l.select)(Ss).getIsWooSeoUpsell(),s=(0,l.select)(Ss).getIsWooSeoUpsellTerm(),n=!e||t||s;(0,me.addFilter)("yoast.replacementVariableEditor.additionalButtons","yoast/yoast-seo-premium/AiGenerator",((e,{fieldId:t})=>(n&&e.push((0,St.createElement)(kt.Fill,{name:`yoast.replacementVariableEditor.additionalButtons.${t}`},(0,St.createElement)(vs,{fieldId:t}))),e)))})()}))})();