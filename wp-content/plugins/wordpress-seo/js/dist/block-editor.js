(()=>{var e={6746:(e,t,s)=>{"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=i(s(9196)),n=i(s(9156)),a=i(s(6743));function i(e){return e&&e.__esModule?e:{default:e}}var l=void 0;function c(e,t){var s,a,i,d,u,p,m,h,g=[],y={};for(p=0;p<e.length;p++)if("string"!==(u=e[p]).type){if(!t.hasOwnProperty(u.value)||void 0===t[u.value])throw new Error("Invalid interpolation, missing component node: `"+u.value+"`");if("object"!==r(t[u.value]))throw new Error("Invalid interpolation, component node must be a ReactElement or null: `"+u.value+"`","\n> "+l);if("componentClose"===u.type)throw new Error("Missing opening component token: `"+u.value+"`");if("componentOpen"===u.type){s=t[u.value],i=p;break}g.push(t[u.value])}else g.push(u.value);return s&&(d=function(e,t){var s,r,o=t[e],n=0;for(r=e+1;r<t.length;r++)if((s=t[r]).value===o.value){if("componentOpen"===s.type){n++;continue}if("componentClose"===s.type){if(0===n)return r;n--}}throw new Error("Missing closing component token `"+o.value+"`")}(i,e),m=c(e.slice(i+1,d),t),a=o.default.cloneElement(s,{},m),g.push(a),d<e.length-1&&(h=c(e.slice(d+1),t),g=g.concat(h))),1===g.length?g[0]:(g.forEach((function(e,t){e&&(y["interpolation-child-"+t]=e)})),(0,n.default)(y))}t.Z=function(e){var t=e.mixedString,s=e.components,o=e.throwErrors;if(l=t,!s)return t;if("object"!==(void 0===s?"undefined":r(s))){if(o)throw new Error("Interpolation Error: unable to process `"+t+"` because components is not an object");return t}var n=(0,a.default)(t);try{return c(n,s)}catch(e){if(o)throw new Error("Interpolation Error: unable to process `"+t+"` because of error `"+e.message+"`");return t}}},6743:e=>{"use strict";function t(e){return e.match(/^\{\{\//)?{type:"componentClose",value:e.replace(/\W/g,"")}:e.match(/\/\}\}$/)?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.match(/^\{\{/)?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}e.exports=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(t)}},9156:(e,t,s)=>{"use strict";var r=s(9196),o="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,n=s(7942),a=s(9179),i=s(397),l=".",c=":",d="function"==typeof Symbol&&Symbol.iterator,u="@@iterator";function p(e,t){return e&&"object"==typeof e&&null!=e.key?(s=e.key,r={"=":"=0",":":"=2"},"$"+(""+s).replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var s,r}function m(e,t,s,r){var n,i=typeof e;if("undefined"!==i&&"boolean"!==i||(e=null),null===e||"string"===i||"number"===i||"object"===i&&e.$$typeof===o)return s(r,e,""===t?l+p(e,0):t),1;var h=0,g=""===t?l:t+c;if(Array.isArray(e))for(var y=0;y<e.length;y++)h+=m(n=e[y],g+p(n,y),s,r);else{var f=function(e){var t=e&&(d&&e[d]||e[u]);if("function"==typeof t)return t}(e);if(f)for(var b,w=f.call(e),E=0;!(b=w.next()).done;)h+=m(n=b.value,g+p(n,E++),s,r);else if("object"===i){var k=""+e;a(!1,"Objects are not valid as a React child (found: %s).%s","[object Object]"===k?"object with keys {"+Object.keys(e).join(", ")+"}":k,"")}}return h}var h=/\/+/g;function g(e){return(""+e).replace(h,"$&/")}var y,f,b=w,w=function(e){var t=this;if(t.instancePool.length){var s=t.instancePool.pop();return t.call(s,e),s}return new t(e)};function E(e,t,s,r){this.result=e,this.keyPrefix=t,this.func=s,this.context=r,this.count=0}function k(e,t,s){var o,a,i=e.result,l=e.keyPrefix,c=e.func,d=e.context,u=c.call(d,t,e.count++);Array.isArray(u)?v(u,i,s,n.thatReturnsArgument):null!=u&&(r.isValidElement(u)&&(o=u,a=l+(!u.key||t&&t.key===u.key?"":g(u.key)+"/")+s,u=r.cloneElement(o,{key:a},void 0!==o.props?o.props.children:void 0)),i.push(u))}function v(e,t,s,r,o){var n="";null!=s&&(n=g(s)+"/");var a=E.getPooled(t,n,r,o);!function(e,t,s){null==e||m(e,"",t,s)}(e,k,a),E.release(a)}E.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},y=function(e,t,s,r){var o=this;if(o.instancePool.length){var n=o.instancePool.pop();return o.call(n,e,t,s,r),n}return new o(e,t,s,r)},(f=E).instancePool=[],f.getPooled=y||b,f.poolSize||(f.poolSize=10),f.release=function(e){var t=this;a(e instanceof t,"Trying to release an instance into a pool of a different type."),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},e.exports=function(e){if("object"!=typeof e||!e||Array.isArray(e))return i(!1,"React.addons.createFragment only accepts a single object. Got: %s",e),e;if(r.isValidElement(e))return i(!1,"React.addons.createFragment does not accept a ReactElement without a wrapper object."),e;a(1!==e.nodeType,"React.addons.createFragment(...): Encountered an invalid child; DOM elements are not valid children of React components.");var t=[];for(var s in e)v(e[s],t,s,n.thatReturnsArgument);return t}},7942:e=>{"use strict";function t(e){return function(){return e}}var s=function(){};s.thatReturns=t,s.thatReturnsFalse=t(!1),s.thatReturnsTrue=t(!0),s.thatReturnsNull=t(null),s.thatReturnsThis=function(){return this},s.thatReturnsArgument=function(e){return e},e.exports=s},9179:e=>{"use strict";e.exports=function(e,t,s,r,o,n,a,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[s,r,o,n,a,i],d=0;(l=new Error(t.replace(/%s/g,(function(){return c[d++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},397:(e,t,s)=>{"use strict";var r=s(7942);e.exports=r},4530:(e,t)=>{var s;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var s=arguments[t];if(s){var n=typeof s;if("string"===n||"number"===n)e.push(s);else if(Array.isArray(s)){if(s.length){var a=o.apply(null,s);a&&e.push(a)}}else if("object"===n){if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]")){e.push(s.toString());continue}for(var i in s)r.call(s,i)&&s[i]&&e.push(i)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(s=function(){return o}.apply(t,[]))||(e.exports=s)}()},9196:e=>{"use strict";e.exports=window.React}},t={};function s(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,s),n.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=s(9196),t=s.n(e);const r=window.wp.blocks,o=window.wp.data,n=window.wp.editPost,a=window.wp.element,i=window.wp.i18n,l=window.wp.plugins,c=window.wp.richText,d=window.yoast.externals.contexts,u=window.yoast.externals.redux,p=window.lodash;function m(){return(0,p.get)(window,"wpseoScriptData.metabox",{intl:{},isRtl:!1})}const h=window.yoast.propTypes;var g=s.n(h);const y=window.yoast.styledComponents;var f=s.n(y);const b=f().svg`
	width: ${e=>e.size}px;
	height: ${e=>e.size}px;
	&&& path {
		fill: ${e=>e.color};
	}
	&&& circle.yoast-icon-readability-score {
		fill: ${e=>e.readabilityScoreColor};
		display: ${e=>e.isContentAnalysisActive?"inline":"none"};
	}
	
	&&& circle.yoast-icon-seo-score {
		fill: ${e=>e.seoScoreColor};
		display: ${e=>e.isKeywordAnalysisActive?"inline":"none"};
	}
`,w=function(t){return(0,e.createElement)(b,{...t,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 646.66 456.27"},(0,e.createElement)("path",{d:"M73,405.26a68.53,68.53,0,0,1-12.82-4c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92q-2.64-2-5.08-4.19a68.26,68.26,0,0,1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24q-1.84-2.73-3.44-5.64a68.26,68.26,0,0,1-8.29-32.55V142.13a68.29,68.29,0,0,1,8.29-32.55,58.6,58.6,0,0,1,3.44-5.64,57.53,57.53,0,0,1,4-5.27A69.64,69.64,0,0,1,48.56,85.42,56.06,56.06,0,0,1,54.2,82,67.78,67.78,0,0,1,73,75.09,69.79,69.79,0,0,1,86.75,73.7H256.41L263,55.39H86.75A86.84,86.84,0,0,0,0,142.13V338.22A86.83,86.83,0,0,0,86.75,425H98.07V406.65H86.75A68.31,68.31,0,0,1,73,405.26ZM368.55,60.85l-1.41-.53L360.73,77.5l1.41.53a68.58,68.58,0,0,1,8.66,4,58.65,58.65,0,0,1,5.65,3.43A69.49,69.49,0,0,1,391,98.67c1.4,1.68,2.72,3.46,3.95,5.27s2.39,3.72,3.44,5.64a68.32,68.32,0,0,1,8.29,32.55V406.65H233.55l-.44.76c-3.07,5.37-6.26,10.48-9.49,15.19L222,425H425V142.13A87.19,87.19,0,0,0,368.55,60.85Z",fill:"#000001"}),(0,e.createElement)("path",{d:"M303.66,0l-96.8,268.87-47.58-149H101.1l72.72,186.78a73.61,73.61,0,0,1,0,53.73c-7.07,18.07-19.63,39.63-54.36,46l-1.56.29v49.57l2-.08c29-1.14,51.57-10.72,70.89-30.14,19.69-19.79,36.55-50.52,53-96.68L366.68,0Z",fill:"#000001"}),(0,e.createElement)("circle",{className:"yoast-icon-readability-score",cx:"561.26",cy:"142.43",r:"85.04",fill:"#000001",stroke:"#181716",strokeMiterlimit:"10",strokeWidth:"0.72"}),(0,e.createElement)("circle",{className:"yoast-icon-seo-score",cx:"561.26",cy:"341.96",r:"85.04",fill:"#000001",stroke:"#181716",strokeMiterlimit:"10",strokeWidth:"0.72"}))};w.propTypes={readabilityScoreColor:g().string,isContentAnalysisActive:g().bool,seoScoreColor:g().string,isKeywordAnalysisActive:g().bool,size:g().number,color:g().string},w.defaultProps={readabilityScoreColor:"#000000",isContentAnalysisActive:!1,seoScoreColor:"#000000",isKeywordAnalysisActive:!1,size:20,color:"#000001"};const E=w,k=window.wp.components;function v(e){return void 0===e.length?e:(0,p.flatten)(e).sort(((e,t)=>void 0===e.props.renderPriority?1:e.props.renderPriority-t.props.renderPriority))}const _=({theme:t,location:s,children:r})=>(0,e.createElement)(d.LocationProvider,{value:s},(0,e.createElement)(y.ThemeProvider,{theme:t},r));_.propTypes={theme:g().object.isRequired,location:g().oneOf(["sidebar","metabox","modal"]).isRequired,children:g().element.isRequired};const x=_,T=window.yoast.uiLibrary;const S=(e,t)=>{try{return(0,a.createInterpolateElement)(e,t)}catch(t){return console.error("Error in translation for:",e,t),e}};g().string.isRequired;const R=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),C=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),I=({learnMoreLink:t,thumbnail:s,wistiaEmbedPermission:r,upsellLink:o,isProductCopy:n,title:a,upsellLabel:l,newToText:c,bundleNote:d,ctbId:u})=>{const{onClose:p,initialFocus:m}=(0,T.useModalContext)(),h={a:(0,e.createElement)(Y,{href:t,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,e.createElement)(C,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,e.createElement)("div",{className:"yst-relative yst-w-full"},(0,e.createElement)(ke,{videoId:"vmrahpfjxp",thumbnail:s,wistiaEmbedPermission:r}),(0,e.createElement)(T.Badge,{className:"yst-absolute yst-top-2 yst-end-4",variant:"info"},"Beta")),(0,e.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,e.createElement)("span",{className:"yst-logo-icon"}),c))),(0,e.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,e.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},a),(0,e.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},S(n?(0,i.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,i.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for product titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"):(0,i.sprintf)(/* translators: %1$s and %2$s are anchor tags; %3$s is the arrow icon. */
(0,i.__)("Let AI do some of the thinking for you and help you save time. Get high-quality suggestions for titles and meta descriptions to make your content rank high and look good on social media. %1$sLearn more%2$s%3$s","wordpress-seo"),"<a>","<ArrowNarrowRightIcon />","</a>"),h))),(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-mt-10"},(0,e.createElement)(T.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:o,target:"_blank",ref:m,"data-action":"load-nfd-ctb","data-ctb-id":u},(0,e.createElement)(R,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),l,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,i.__)("(Opens in a new browser tab)","wordpress-seo")))),d,(0,e.createElement)(T.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:p},(0,i.__)("Close","wordpress-seo"))))};I.propTypes={learnMoreLink:g().string.isRequired,upsellLink:g().string.isRequired,thumbnail:g().shape({src:g().string.isRequired,width:g().string,height:g().string}).isRequired,wistiaEmbedPermission:g().shape({value:g().bool.isRequired,status:g().string.isRequired,set:g().func.isRequired}).isRequired,title:g().string,upsellLabel:g().string,newToText:g().string,isProductCopy:g().bool,bundleNote:g().oneOfType([g().string,g().element]),ctbId:g().string},I.defaultProps={title:(0,i.__)("Use AI to write your titles & meta descriptions!","wordpress-seo"),upsellLabel:(0,i.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,i.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",isProductCopy:!1,bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const L=({learnMoreLink:t,thumbnail:s,wistiaEmbedPermission:r,upsellLink:o,upsellLabel:n,newToText:a,bundleNote:l,ctbId:c})=>{const{onClose:d,initialFocus:u}=(0,T.useModalContext)(),p={a:(0,e.createElement)(Y,{href:t,className:"yst-inline-flex yst-items-center yst-gap-1 yst-no-underline yst-font-medium",variant:"primary"}),ArrowNarrowRightIcon:(0,e.createElement)(C,{className:"yst-w-4 yst-h-4 rtl:yst-rotate-180"}),br:(0,e.createElement)("br",null)};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"yst-px-10 yst-pt-10 yst-introduction-gradient yst-text-center"},(0,e.createElement)("div",{className:"yst-relative yst-w-full"},(0,e.createElement)(ke,{videoId:"vun9z1dpfh",thumbnail:s,wistiaEmbedPermission:r}),(0,e.createElement)(T.Badge,{className:"yst-absolute yst-end-4 yst-text-center yst-justify-center",variant:"info",style:{top:"-8px"}},(0,i.__)("Beta","wordpress-seo-premium"))),(0,e.createElement)("div",{className:"yst-mt-6 yst-text-xs yst-font-medium yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("span",{className:"yst-introduction-modal-uppercase yst-flex yst-gap-2 yst-items-center"},(0,e.createElement)("span",{className:"yst-logo-icon"}),a))),(0,e.createElement)("div",{className:"yst-px-10 yst-pb-4 yst-flex yst-flex-col yst-items-center"},(0,e.createElement)("div",{className:"yst-mt-4 yst-mx-1.5 yst-text-center"},(0,e.createElement)("h3",{className:"yst-text-slate-900 yst-text-lg yst-font-medium"},(0,i.sprintf)(/* translators: %s: Expands to "Yoast AI" */
(0,i.__)("Optimize your SEO content with %s","wordpress-seo"),"Yoast AI")),(0,e.createElement)("div",{className:"yst-mt-2 yst-text-slate-600 yst-text-sm"},S((0,i.sprintf)(/* translators: %1$s is a break tag; %2$s and %3$s are anchor tags; %4$s is the arrow icon. */
(0,i.__)("Make content editing a breeze! Optimize your SEO content with quick, actionable suggestions at the click of a button.%1$s%2$sLearn more%3$s%4$s","wordpress-seo"),"<br/>","<a>","<ArrowNarrowRightIcon />","</a>"),p))),(0,e.createElement)("div",{className:"yst-w-full yst-flex yst-mt-6"},(0,e.createElement)(T.Button,{as:"a",className:"yst-grow",size:"extra-large",variant:"upsell",href:o,target:"_blank",ref:u,"data-action":"load-nfd-ctb","data-ctb-id":c},(0,e.createElement)(R,{className:"yst--ms-1 yst-me-2 yst-h-5 yst-w-5"}),n,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,i.__)("(Opens in a new browser tab)","wordpress-seo")))),l,(0,e.createElement)(T.Button,{as:"a",className:"yst-mt-4",variant:"tertiary",onClick:d},(0,i.__)("Close","wordpress-seo"))))};L.propTypes={learnMoreLink:g().string.isRequired,upsellLink:g().string.isRequired,thumbnail:g().shape({src:g().string.isRequired,width:g().string,height:g().string}).isRequired,wistiaEmbedPermission:g().shape({value:g().bool.isRequired,status:g().string.isRequired,set:g().func.isRequired}).isRequired,upsellLabel:g().string,newToText:g().string,bundleNote:g().oneOfType([g().string,g().element]),ctbId:g().string},L.defaultProps={upsellLabel:(0,i.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,i.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),newToText:"Yoast SEO Premium",bundleNote:"",ctbId:"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"};const P=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-flex yst-gap-2"},(0,e.createElement)(T.Button,{onClick:t},(0,i.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(T.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,i.__)("Contact support","wordpress-seo")));P.propTypes={handleRefreshClick:g().func.isRequired,supportLink:g().string.isRequired};const A=({handleRefreshClick:t,supportLink:s})=>(0,e.createElement)("div",{className:"yst-grid yst-grid-cols-1 yst-gap-y-2"},(0,e.createElement)(T.Button,{className:"yst-order-last",onClick:t},(0,i.__)("Refresh this page","wordpress-seo")),(0,e.createElement)(T.Button,{variant:"secondary",as:"a",href:s,target:"_blank",rel:"noopener"},(0,i.__)("Contact support","wordpress-seo")));A.propTypes={handleRefreshClick:g().func.isRequired,supportLink:g().string.isRequired};const F=({error:t,children:s})=>(0,e.createElement)("div",{role:"alert",className:"yst-max-w-screen-sm yst-p-8 yst-space-y-4"},(0,e.createElement)(T.Title,null,(0,i.__)("Something went wrong. An unexpected error occurred.","wordpress-seo")),(0,e.createElement)("p",null,(0,i.__)("We're very sorry, but it seems like the following error has interrupted our application:","wordpress-seo")),(0,e.createElement)(T.Alert,{variant:"error"},(null==t?void 0:t.message)||(0,i.__)("Undefined error message.","wordpress-seo")),(0,e.createElement)("p",null,(0,i.__)("Unfortunately, this means that any unsaved changes in this section will be lost. You can try and refresh this page to resolve the problem. If this error still occurs, please get in touch with our support team, and we'll get you all the help you need!","wordpress-seo")),s);F.propTypes={error:g().object.isRequired,children:g().node},F.VerticalButtons=A,F.HorizontalButtons=P;var O;function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},N.apply(this,arguments)}g().string,g().node.isRequired,g().node.isRequired,g().node,g().oneOf(Object.keys({lg:{grid:"yst-grid lg:yst-grid-cols-3 lg:yst-gap-12",col1:"yst-col-span-1",col2:"lg:yst-mt-0 lg:yst-col-span-2"},xl:{grid:"yst-grid xl:yst-grid-cols-3 xl:yst-gap-12",col1:"yst-col-span-1",col2:"xl:yst-mt-0 xl:yst-col-span-2"},"2xl":{grid:"yst-grid 2xl:yst-grid-cols-3 2xl:yst-gap-12",col1:"yst-col-span-1",col2:"2xl:yst-mt-0 2xl:yst-col-span-2"}}));const M=t=>e.createElement("svg",N({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1000 1000"},t),O||(O=e.createElement("path",{fill:"#fff",d:"M500 0C223.9 0 0 223.9 0 500s223.9 500 500 500 500-223.9 500-500S776.1 0 500 0Zm87.2 412.4c0-21.9 4.3-40.2 13.1-54.4s24-27.1 45.9-38.2l10.1-4.9c17.8-9 22.4-16.7 22.4-26 0-11.1-9.5-19.1-25-19.1-18.3 0-32.2 9.5-41.8 28.9l-24.7-24.8c5.4-11.6 14.1-20.9 25.8-28.1a70.8 70.8 0 0 1 38.9-11.1c17.8 0 33.3 4.6 45.9 14.2s19.4 22.7 19.4 39.4c0 26.6-15 42.9-43.1 57.3l-15.7 8c-16.8 8.5-25.1 16-27.4 29.4h85.4v35.4H587.2Zm-82.1 373.3c-157.8 0-285.7-127.9-285.7-285.7s127.9-285.7 285.7-285.7a286.4 286.4 0 0 1 55.9 5.5l-55.9 116.9c-90 0-163.3 73.3-163.3 163.3s73.3 163.3 163.3 163.3a162.8 162.8 0 0 0 106.4-39.6l61.8 107.2a283.9 283.9 0 0 1-168.2 54.8ZM705 704.1l-70.7-122.5H492.9l70.7-122.4H705l70.7 122.4Z"}))),q=window.ReactDOM;var D,$,U;($=D||(D={})).Pop="POP",$.Push="PUSH",$.Replace="REPLACE",function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(U||(U={})),new Set(["lazy","caseSensitive","path","id","index","children"]),Error;const B=["post","put","patch","delete"],W=(new Set(B),["get",...B]);new Set(W),new Set([301,302,303,307,308]),new Set([307,308]),Symbol("deferred"),e.Component,e.startTransition,new Promise((()=>{})),e.Component,new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(e){}var j,H,K,z;new Map,e.startTransition,q.flushSync,e.useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(z=j||(j={})).UseScrollRestoration="useScrollRestoration",z.UseSubmit="useSubmit",z.UseSubmitFetcher="useSubmitFetcher",z.UseFetcher="useFetcher",z.useViewTransitionState="useViewTransitionState",(K=H||(H={})).UseFetcher="useFetcher",K.UseFetchers="useFetchers",K.UseScrollRestoration="useScrollRestoration",g().string.isRequired,g().string;const Y=({href:t,children:s,...r})=>(0,e.createElement)(T.Link,{target:"_blank",rel:"noopener noreferrer",...r,href:t},s,(0,e.createElement)("span",{className:"yst-sr-only"},/* translators: Hidden accessibility text. */
(0,i.__)("(Opens in a new browser tab)","wordpress-seo")));Y.propTypes={href:g().string.isRequired,children:g().node},Y.defaultProps={children:null};const V=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))}));var G,Z,X;function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},Q.apply(this,arguments)}const J=t=>e.createElement("svg",Q({xmlns:"http://www.w3.org/2000/svg",id:"star-rating-half_svg__Layer_1","data-name":"Layer 1",viewBox:"0 0 500 475.53"},t),G||(G=e.createElement("defs",null,e.createElement("style",null,".star-rating-half_svg__cls-1{fill:#fbbf24}"))),Z||(Z=e.createElement("path",{d:"M250 392.04 98.15 471.87l29-169.09L4.3 183.03l169.77-24.67L250 4.52l75.93 153.84 169.77 24.67-122.85 119.75 29 169.09L250 392.04z",className:"star-rating-half_svg__cls-1"})),X||(X=e.createElement("path",{d:"m250 9.04 73.67 149.27.93 1.88 2.08.3 164.72 23.94-119.19 116.19-1.51 1.47.36 2.07 28.14 164.06-147.34-77.46-1.86-1-1.86 1-147.34 77.46 28.14-164.06.36-2.07-1.51-1.47L8.6 184.43l164.72-23.9 2.08-.3.93-1.88L250 9.04m0-9-77.25 156.49L0 181.64l125 121.89-29.51 172L250 394.3l154.51 81.23-29.51-172 125-121.89-172.75-25.11L250 0Z",className:"star-rating-half_svg__cls-1"})),e.createElement("path",{d:"m500 181.64-172.75-25.11L250 0v394.3l154.51 81.23L375 303.48l125-121.84z",style:{fill:"#f3f4f6"}}));function ee(){return ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},ee.apply(this,arguments)}const te=t=>e.createElement("svg",ee({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",viewBox:"0 0 500 475.53"},t),e.createElement("path",{d:"m250 0 77.25 156.53L500 181.64 375 303.48l29.51 172.05L250 394.3 95.49 475.53 125 303.48 0 181.64l172.75-25.11L250 0z",style:{fill:"#fbbf24"}}));var se,re,oe,ne,ae,ie,le,ce,de;function ue(){return ue=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},ue.apply(this,arguments)}const pe=t=>e.createElement("svg",ue({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 500 500"},t),se||(se=e.createElement("path",{fill:"#a4286a",d:"M80 0h340a80 80 0 0 1 80 80v420H80a80 80 0 0 1-80-80V80A80 80 0 0 1 80 0z"})),re||(re=e.createElement("path",{fill:"#6c2548",d:"M437.61 2 155.89 500H500V80a80 80 0 0 0-62.39-78z"})),oe||(oe=e.createElement("path",{fill:"#fff",d:"M74.4 337.3v34.9c21.6-.9 38.5-8 52.8-22.5s27.4-38 39.9-72.9l92.6-248h-44.8L140.3 236l-37-116.2h-41l54.4 139.8a57.54 57.54 0 0 1 0 41.8c-5.5 14.2-15.4 30.9-42.3 35.9z"})),ne||(ne=e.createElement("circle",{cx:368.33,cy:124.68,r:97.34,fill:"#9fda4f",transform:"rotate(-45 368.335 124.68)"})),ae||(ae=e.createElement("path",{fill:"#77b227",d:"m416.2 39.93-95.74 169.51A97.34 97.34 0 1 0 416.2 39.93z"})),ie||(ie=e.createElement("path",{fill:"#fec228",d:"m294.78 254.75-.15-.08-.13-.07a63.6 63.6 0 0 0-62.56 110.76h.13a63.6 63.6 0 0 0 62.71-110.67z"})),le||(le=e.createElement("path",{fill:"#f49a00",d:"m294.5 254.59-62.56 110.76a63.6 63.6 0 1 0 62.56-110.76z"})),ce||(ce=e.createElement("path",{fill:"#ff4e47",d:"M222.31 450.07A38.16 38.16 0 0 0 203 416.83a38.18 38.18 0 1 0 19.41 33.27z"})),de||(de=e.createElement("path",{fill:"#ed261f",d:"m202.9 416.8-37.54 66.48a38.17 38.17 0 0 0 37.54-66.48z"}))),me=({link:t,linkProps:s,isPromotionActive:r})=>{let o=(0,a.useMemo)((()=>(0,i.__)("Use AI to generate titles and meta descriptions, automatically redirect deleted pages, get 24/7 support, and much, much more!","wordpress-seo")),[]),n=S((0,i.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,i.__)("%1$sGet%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,e.createElement)("span",{className:"yst-whitespace-nowrap"})});const l=r("black-friday-2024-promotion");return l&&(o=(0,a.useMemo)((()=>(0,i.__)("If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)","wordpress-seo")),[]),n=S((0,i.sprintf)(/* translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s expands to "Yoast SEO Premium". */
(0,i.__)("%1$sBuy%2$s %3$s","wordpress-seo"),"<nowrap>","</nowrap>","Yoast SEO Premium"),{nowrap:(0,e.createElement)("span",{className:"yst-whitespace-nowrap"})})),(0,e.createElement)("div",{className:"yst-p-6 yst-rounded-lg yst-text-white yst-bg-primary-500 yst-shadow"},(0,e.createElement)("figure",{className:"yst-logo-square yst-w-16 yst-h-16 yst-mx-auto yst-overflow-hidden yst-border yst-border-white yst-rounded-xl yst-rounded-br-none yst-relative yst-z-10 yst-mt-[-2.6rem]"},(0,e.createElement)(pe,null)),l&&(0,e.createElement)("div",{className:"sidebar__sale_banner_container"},(0,e.createElement)("div",{className:"sidebar__sale_banner"},(0,e.createElement)("span",{className:"banner_text"},(0,i.__)("30% OFF - BLACK FRIDAY","wordpress-seo")))),(0,e.createElement)(T.Title,{as:"h2",className:"yst-mt-6 yst-text-base yst-font-extrabold yst-text-white"},n),(0,e.createElement)("p",{className:"yst-mt-2"},o),(0,e.createElement)(T.Button,{as:"a",variant:"upsell",href:t,target:"_blank",rel:"noopener",className:"yst-flex yst-justify-center yst-gap-2 yst-mt-4 focus:yst-ring-offset-primary-500",...s},(0,e.createElement)("span",null,l?(0,i.__)("Buy now","wordpress-seo"):n),(0,e.createElement)(V,{className:"yst-w-4 yst-h-4 yst-icon-rtl"})),(0,e.createElement)("p",{className:"yst-text-center yst-text-xs yst-mx-2 yst-font-light yst-leading-5 yst-mt-2"},(0,i.__)("30-day money back guarantee.","wordpress-seo")),(0,e.createElement)("hr",{className:"yst-border-t yst-border-primary-300 yst-my-4"}),(0,e.createElement)("a",{className:"yst-block yst-mt-4 yst-no-underline",href:"https://www.g2.com/products/yoast-yoast/reviews",target:"_blank",rel:"noopener noreferrer"},(0,e.createElement)("span",{className:"yst-font-medium yst-text-white hover:yst-underline"},(0,i.__)("Read reviews from real users","wordpress-seo")),(0,e.createElement)("span",{className:"yst-flex yst-gap-2 yst-mt-2 yst-items-center"},(0,e.createElement)(M,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)("span",{className:"yst-flex yst-gap-1"},(0,e.createElement)(te,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(te,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(te,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(te,{className:"yst-w-5 yst-h-5"}),(0,e.createElement)(J,{className:"yst-w-5 yst-h-5"})),(0,e.createElement)("span",{className:"yst-text-sm yst-font-semibold yst-text-white"},"4.6 / 5"))))};me.propTypes={link:g().string.isRequired,linkProps:g().object,isPromotionActive:g().func},me.defaultProps={linkProps:{},isPromotionActive:p.noop};const he=()=>[(0,i.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,i.__)("%1$sAI%2$s: Better SEO titles and meta descriptions, faster.","wordpress-seo"),"<strong>","</strong>"),(0,i.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,i.__)("%1$sMultiple keywords%2$s: Rank higher for more searches.","wordpress-seo"),"<strong>","</strong>"),(0,i.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,i.__)("%1$sSuper fast%2$s internal linking suggestions.","wordpress-seo"),"<strong>","</strong>"),(0,i.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,i.__)("%1$sNo more broken links%2$s: Automatic redirect manager.","wordpress-seo"),"<strong>","</strong>"),(0,i.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,i.__)("%1$sAppealing social previews%2$s people actually want to click on.","wordpress-seo"),"<strong>","</strong>"),(0,i.sprintf)(/* translators: %1$s expands to a strong opening tag, %2$s expands to a strong closing tag. */
(0,i.__)("%1$s24/7 support%2$s: Also on evenings and weekends.","wordpress-seo"),"<strong>","</strong>")],ge=({premiumLink:t,premiumUpsellConfig:s,isPromotionActive:r})=>{const o=r("black-friday-2024-promotion");return(0,e.createElement)(T.Paper,{as:"div",className:"xl:yst-max-w-3xl"},o&&(0,e.createElement)("div",{className:"yst-rounded-t-lg yst-h-9 yst-flex yst-justify-between yst-items-center yst-bg-black yst-text-amber-300 yst-px-4 yst-text-lg yst-border-b yst-border-amber-300 yst-border-solid yst-font-semibold"},(0,e.createElement)("div",null,(0,i.__)("30% OFF","wordpress-seo")),(0,e.createElement)("div",null,(0,i.__)("BLACK FRIDAY","wordpress-seo"))),(0,e.createElement)("div",{className:"yst-p-6 yst-flex yst-flex-col"},(0,e.createElement)(T.Title,{as:"h2",size:"4",className:"yst-text-xl yst-text-primary-500"},(0,i.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,i.__)("Upgrade to %s","wordpress-seo"),"Yoast SEO Premium")),(0,e.createElement)("ul",{className:"yst-grid yst-grid-cols-1 sm:yst-grid-cols-2 yst-gap-x-6 yst-list-disc yst-ps-[1em] yst-list-outside yst-text-slate-800 yst-mt-6"},he().map(((t,s)=>(0,e.createElement)("li",{key:`upsell-benefit-${s}`},S(t,{strong:(0,e.createElement)("span",{className:"yst-font-semibold"})}))))),(0,e.createElement)(T.Button,{as:"a",variant:"upsell",size:"extra-large",href:t,className:"yst-gap-2 yst-mt-4",target:"_blank",rel:"noopener",...s},o?(0,i.__)("Claim your 30% off now!","wordpress-seo"):(0,i.sprintf)(/* translators: %s expands to "Yoast SEO" Premium */
(0,i.__)("Explore %s now!","wordpress-seo"),"Yoast SEO Premium"),(0,e.createElement)(V,{className:"yst-w-4 yst-h-4 yst-icon-rtl"}))))};ge.propTypes={premiumLink:g().string.isRequired,premiumUpsellConfig:g().object,isPromotionActive:g().func},ge.defaultProps={premiumUpsellConfig:{},isPromotionActive:p.noop},g().string.isRequired,g().object.isRequired,g().string.isRequired,g().func.isRequired,e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"}))})),g().bool.isRequired,g().func,g().func,g().string.isRequired,g().string.isRequired,g().string.isRequired,g().string.isRequired;const ye=window.yoast.reactHelmet,fe="loading",be="showPlay",we="askPermission",Ee="isPlaying",ke=({videoId:t,thumbnail:s,wistiaEmbedPermission:r})=>{const[o,n]=(0,a.useState)(r.value?Ee:be),l=(0,a.useCallback)((()=>n(Ee)),[n]),c=(0,a.useCallback)((()=>{r.value?l():n(we)}),[r.value,l,n]),d=(0,a.useCallback)((()=>n(be)),[n]),u=(0,a.useCallback)((()=>{r.set(!0),l()}),[r.set,l]);return(0,e.createElement)(e.Fragment,null,r.value&&(0,e.createElement)(ye.Helmet,null,(0,e.createElement)("script",{src:"https://fast.wistia.com/assets/external/E-v1.js",async:!0})),(0,e.createElement)("div",{className:"yst-relative yst-w-full yst-h-0 yst-pt-[56.25%] yst-overflow-hidden yst-rounded-md yst-drop-shadow-md yst-bg-white"},o===be&&(0,e.createElement)("button",{type:"button",className:"yst-absolute yst-inset-0 yst-button yst-p-0 yst-border-none yst-bg-white yst-transition-opacity yst-duration-1000 yst-opacity-100",onClick:c},(0,e.createElement)("img",{className:"yst-w-full yst-h-auto",alt:"",loading:"lazy",decoding:"async",...s})),o===we&&(0,e.createElement)("div",{className:"yst-absolute yst-inset-0 yst-flex yst-flex-col yst-items-center yst-justify-center yst-bg-white"},(0,e.createElement)("p",{className:"yst-max-w-xs yst-mx-auto yst-text-center"},r.status===fe&&(0,e.createElement)(T.Spinner,null),r.status!==fe&&(0,i.sprintf)(/* translators: %1$s expands to Yoast SEO. %2$s expands to Wistia. */
(0,i.__)("To see this video, you need to allow %1$s to load embedded videos from %2$s.","wordpress-seo"),"Yoast SEO","Wistia")),(0,e.createElement)("div",{className:"yst-flex yst-mt-6 yst-gap-x-4"},(0,e.createElement)(T.Button,{type:"button",variant:"secondary",onClick:d,disabled:r.status===fe},(0,i.__)("Deny","wordpress-seo")),(0,e.createElement)(T.Button,{type:"button",variant:"primary",onClick:u,disabled:r.status===fe},(0,i.__)("Allow","wordpress-seo")))),r.value&&o===Ee&&(0,e.createElement)("div",{className:"yst-absolute yst-w-full yst-h-full yst-top-0 yst-right-0"},null===t&&(0,e.createElement)(T.Spinner,{className:"yst-h-full yst-mx-auto"}),null!==t&&(0,e.createElement)("div",{className:`wistia_embed wistia_async_${t} videoFoam=true`}))))};ke.propTypes={videoId:g().string.isRequired,thumbnail:g().shape({src:g().string.isRequired,width:g().string,height:g().string}).isRequired,wistiaEmbedPermission:g().shape({value:g().bool.isRequired,status:g().string.isRequired,set:g().func.isRequired}).isRequired},e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z",clipRule:"evenodd"}))})),g().bool.isRequired,g().func.isRequired,g().func,g().string;const ve=window.yoast.componentsNew,_e=window.yoast.styleGuide,xe=window.yoast.analysis;function Te(e){switch(e){case"loading":return{icon:"loading-spinner",color:_e.colors.$color_green_medium_light};case"not-set":return{icon:"seo-score-none",color:_e.colors.$color_score_icon};case"noindex":return{icon:"seo-score-none",color:_e.colors.$color_noindex};case"good":return{icon:"seo-score-good",color:_e.colors.$color_green_medium};case"ok":return{icon:"seo-score-ok",color:_e.colors.$color_ok};default:return{icon:"seo-score-bad",color:_e.colors.$color_red}}}function Se({target:e,children:t}){let s=e;return"string"==typeof e&&(s=document.getElementById(e)),s?(0,a.createPortal)(t,s):null}Se.propTypes={target:g().oneOfType([g().string,g().object]).isRequired,children:g().node.isRequired};const Re=({target:t,scoreIndicator:s})=>(0,e.createElement)(Se,{target:t},(0,e.createElement)(ve.SvgIcon,{...Te(s)}));Re.propTypes={target:g().string.isRequired,scoreIndicator:g().string.isRequired};const Ce=Re,Ie=({error:t})=>{const s=(0,a.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),r=(0,o.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/metabox-error-support")),[]),n=(0,o.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,a.useEffect)((()=>{document.querySelectorAll('[id^="wpseo-meta-tab-"]').forEach((e=>{!function(e){const t=document.querySelector(`#${e}`);null!==t&&(t.style.opacity="0.5",t.style.pointerEvents="none",t.setAttribute("aria-disabled","true"),t.classList.contains("yoast-active-tab")&&t.classList.remove("yoast-active-tab"))}(e.id)}))}),[]),(0,e.createElement)(T.Root,{context:{isRtl:n}},(0,e.createElement)(F,{error:t},(0,e.createElement)(F.HorizontalButtons,{supportLink:r,handleRefreshClick:s}),(0,e.createElement)(Ce,{target:"wpseo-seo-score-icon",scoreIndicator:"not-set"}),(0,e.createElement)(Ce,{target:"wpseo-readability-score-icon",scoreIndicator:"not-set"}),(0,e.createElement)(Ce,{target:"wpseo-inclusive-language-score-icon",scoreIndicator:"not-set"})))};function Le({theme:t}){return(0,e.createElement)(x,{theme:t,location:"metabox"},(0,e.createElement)(T.ErrorBoundary,{FallbackComponent:Ie},(0,e.createElement)(k.Slot,{name:"YoastMetabox"},(e=>v(e)))))}Ie.propTypes={error:g().object.isRequired};const Pe=window.wp.compose,Ae=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}))})),Fe=(t=null)=>(0,e.useMemo)((()=>{const e={role:"img","aria-hidden":"true"};return null!==t&&(e.focusable=t?"true":"false"),e}),[t]);var Oe=s(4530),Ne=s.n(Oe);const Me=({className:t,...s})=>(0,e.createElement)("span",{className:Ne()("yst-grow yst-overflow-hidden yst-overflow-ellipsis yst-whitespace-nowrap yst-font-wp","yst-text-[#555] yst-text-base yst-leading-[normal] yst-subpixel-antialiased yst-text-start",t),...s});Me.displayName="MetaboxButton.Text",Me.propTypes={className:g().string},Me.defaultProps={className:""};const qe=({className:t,...s})=>(0,e.createElement)("button",{type:"button",className:Ne()("yst-flex yst-items-center yst-w-full yst-pt-4 yst-pb-4 yst-pe-4 yst-ps-6 yst-space-x-2 rtl:yst-space-x-reverse","yst-border-t yst-border-t-[rgb(0,0,0,0.2)] yst-rounded-none yst-transition-all hover:yst-bg-[#f0f0f0]","focus:yst-outline focus:yst-outline-[1px] focus:yst-outline-[color:#0066cd] focus:-yst-outline-offset-1 focus:yst-shadow-[0_0_3px_rgba(8,74,103,0.8)]",t),...s});qe.propTypes={className:g().string},qe.defaultProps={className:""},qe.Text=Me;const De=window.yoast.helpers,$e=f().div`
	min-width: 600px;

	@media screen and ( max-width: 680px ) {
		min-width: 0;
		width: 86vw;
	}
`,Ue=f().div`
	@media screen and ( min-width: 600px ) {
		max-width: 420px;
	}
`,Be=(f()(ve.Icon)`
	float: ${(0,De.getDirectionalStyle)("right","left")};
	margin: ${(0,De.getDirectionalStyle)("0 0 16px 16px","0 16px 16px 0")};

	&& {
		width: 150px;
		height: 150px;

		@media screen and ( max-width: 680px ) {
			width: 80px;
			height: 80px;
		}
	}
`,"yoast yoast-gutenberg-modal"),We=t=>{const{title:s,className:r,showYoastIcon:o,additionalClassName:n,...a}=t,i=o?(0,e.createElement)("span",{className:"yoast-icon"}):null;return(0,e.createElement)(k.Modal,{title:s,className:`${r} ${n}`,icon:i,...a},t.children)};We.propTypes={title:g().string,className:g().string,showYoastIcon:g().bool,children:g().oneOfType([g().node,g().arrayOf(g().node)]),additionalClassName:g().string},We.defaultProps={title:"Yoast SEO",className:Be,showYoastIcon:!0,children:null,additionalClassName:""};const je=We;var He,Ke;function ze(){return ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},ze.apply(this,arguments)}const Ye=t=>e.createElement("svg",ze({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 425 456.27"},t),He||(He=e.createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"})),Ke||(Ke=e.createElement("path",{stroke:"#000",strokeMiterlimit:10,strokeWidth:3.81,d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z"}))),Ve=t=>(0,e.createElement)("div",{className:"yoast components-panel__body"},(0,e.createElement)("h2",{className:"components-panel__body-title"},(0,e.createElement)("button",{id:t.id,onClick:t.onClick,className:"components-button components-panel__body-toggle",type:"button"},t.prefixIcon&&(0,e.createElement)("span",{className:"yoast-icon-span",style:{fill:`${t.prefixIcon&&t.prefixIcon.color||""}`}},(0,e.createElement)(ve.SvgIcon,{size:t.prefixIcon.size,icon:t.prefixIcon.icon})),(0,e.createElement)("span",{className:"yoast-title-container"},(0,e.createElement)("div",{className:"yoast-title"},t.title),(0,e.createElement)("div",{className:"yoast-subtitle"},t.subTitle)),t.children,t.suffixIcon&&(0,e.createElement)(ve.SvgIcon,{size:t.suffixIcon.size,icon:t.suffixIcon.icon}),t.SuffixHeroIcon))),Ge=Ve;Ve.propTypes={onClick:g().func.isRequired,title:g().string.isRequired,id:g().string,subTitle:g().string,suffixIcon:g().object,SuffixHeroIcon:g().object,prefixIcon:g().object,children:g().node},Ve.defaultProps={id:"",suffixIcon:null,SuffixHeroIcon:null,prefixIcon:null,subTitle:"",children:null};const Ze=window.moment;var Xe=s.n(Ze),Qe=s(6746);const Je=(0,De.makeOutboundLink)(),et=t=>{const s=(0,i.sprintf)(/* translators: %1$d expands to the amount of allowed keyphrases on a free account, %2$s expands to a link to Wincher plans. */
(0,i.__)("You've reached the maximum amount of %1$d keyphrases you can add to your Wincher account. If you wish to add more keyphrases, please %2$s.","wordpress-seo"),t.limit,"{{updateWincherPlanLink/}}");return(0,e.createElement)(ve.Alert,{type:"error"},(0,Qe.Z)({mixedString:s,components:{updateWincherPlanLink:(0,e.createElement)(Je,{href:wpseoAdminGlobalL10n["links.wincher.pricing"]},(0,i.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,i.__)("upgrade your %s plan","wordpress-seo"),"Wincher"))}}))};et.propTypes={limit:g().number},et.defaultProps={limit:10};const tt=et,st=()=>(0,e.createElement)(ve.Alert,{type:"error"},(0,i.__)("Something went wrong while tracking the ranking position(s) of your page. Please try again later.","wordpress-seo")),rt=window.wp.apiFetch;var ot=s.n(rt);async function nt(e,t,s,r=200){try{const o=await e();return!!o&&(o.status===r?t(o):s(o))}catch(e){console.error(e.message)}}async function at(e){try{return await ot()(e)}catch(e){return e.error&&e.status?e:e instanceof Response&&await e.json()}}async function it(e){return(0,p.isArray)(e)||(e=[e]),await at({path:"yoast/v1/wincher/keyphrases/track",method:"POST",data:{keyphrases:e}})}const lt=f().p`
	color: ${_e.colors.$color_pink_dark};
	font-size: 14px;
	font-weight: 700;
	margin: 13px 0 10px;
`,ct=f()(ve.SvgIcon)`
	margin-right: 5px;
	vertical-align: middle;
`,dt=f().button`
	position: absolute;
	top: 9px;
	right: 9px;
	border: none;
    background: none;
    cursor: pointer;
`,ut=f().p`
	font-size: 13px;
	font-weight: 500;
	margin: 10px 0 13px;
`,pt=f().div`
	position: relative;
	background: ${e=>e.isTitleShortened?"#F5F7F7":"transparent"};
	border: 1px solid #C7C7C7;
	border-left: 4px solid${_e.colors.$color_pink_dark};
	padding: 0 16px;
	margin-bottom: 1.5em;
`,mt=e=>{const[t,s]=(0,a.useState)(null);return(0,a.useEffect)((()=>{e&&!t&&async function(){return await at({path:"yoast/v1/wincher/account/limit",method:"GET"})}().then((e=>s(e)))}),[t]),t};mt.propTypes={limit:g().bool.isRequired};const ht=({limit:t,usage:s,isTitleShortened:r,isFreeAccount:o})=>{const n=(0,i.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,i.__)("Your are tracking %1$s out of %2$s keyphrases included in your free account.","wordpress-seo"),s,t),a=(0,i.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,i.__)("Your are tracking %1$s out of %2$s keyphrases included in your account.","wordpress-seo"),s,t),l=o?n:a,c=(0,i.sprintf)(
/* Translators: %1$s expands to the number of used keywords.
   * %2$s expands to the account keywords limit.
   */
(0,i.__)("Keyphrases tracked: %1$s/%2$s","wordpress-seo"),s,t),d=r?c:l;return(0,e.createElement)(lt,null,r&&(0,e.createElement)(ct,{icon:"exclamation-triangle",color:_e.colors.$color_pink_dark,size:"14px"}),d)};ht.propTypes={limit:g().number.isRequired,usage:g().number.isRequired,isTitleShortened:g().bool,isFreeAccount:g().bool};const gt=(0,De.makeOutboundLink)(),yt=({discount:t,months:s})=>{const r=(0,e.createElement)(gt,{href:wpseoAdminGlobalL10n["links.wincher.upgrade"],style:{fontWeight:600}},(0,i.sprintf)(/* Translators: %s : Expands to "Wincher". */
(0,i.__)("Click here to upgrade your %s plan","wordpress-seo"),"Wincher"));if(!t||!s)return(0,e.createElement)(ut,null,r);const o=100*t,n=(0,i.sprintf)(
/* Translators: %1$s expands to upgrade account link.
   * %2$s expands to the upgrade discount value.
   * %3$s expands to the upgrade discount duration e.g. 2 months.
   */
(0,i.__)("%1$s and get an exclusive %2$s discount for %3$s month(s).","wordpress-seo"),"{{wincherAccountUpgradeLink/}}",o+"%",s);return(0,e.createElement)(ut,null,(0,Qe.Z)({mixedString:n,components:{wincherAccountUpgradeLink:r}}))};yt.propTypes={discount:g().number,months:g().number};const ft=({onClose:t,isTitleShortened:s,trackingInfo:r})=>{const o=(()=>{const[e,t]=(0,a.useState)(null);return(0,a.useEffect)((()=>{e||async function(){return await at({path:"yoast/v1/wincher/account/upgrade-campaign",method:"GET"})}().then((e=>t(e)))}),[e]),e})();if(null===r)return null;const{limit:n,usage:l}=r;if(!(n&&l/n>=.8))return null;const c=Boolean(null==o?void 0:o.discount);return(0,e.createElement)(pt,{isTitleShortened:s},t&&(0,e.createElement)(dt,{type:"button","aria-label":(0,i.__)("Close the upgrade callout","wordpress-seo"),onClick:t},(0,e.createElement)(ve.SvgIcon,{icon:"times-circle",color:_e.colors.$color_pink_dark,size:"14px"})),(0,e.createElement)(ht,{...r,isTitleShortened:s,isFreeAccount:c}),(0,e.createElement)(yt,{discount:null==o?void 0:o.discount,months:null==o?void 0:o.months}))};ft.propTypes={onClose:g().func,isTitleShortened:g().bool,trackingInfo:g().object};const bt=ft,wt=()=>(0,e.createElement)(ve.Alert,{type:"success"},(0,i.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,i.__)("You have successfully connected to %s! You can now track the SEO performance for the keyphrase(s) of this page.","wordpress-seo"),"Wincher")),Et=()=>(0,e.createElement)(ve.Alert,{type:"info"},(0,i.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,i.__)("%s is currently tracking the ranking position(s) of your page. This may take a few minutes. Please wait or check back later.","wordpress-seo"),"Wincher")),kt=({data:t,mapChartDataToTableData:s,dataTableCaption:r,dataTableHeaderLabels:o,isDataTableVisuallyHidden:n})=>t.length!==o.length?(0,e.createElement)("p",null,(0,i.__)("The number of headers and header labels don't match.","wordpress-seo")):(0,e.createElement)("div",{className:n?"screen-reader-text":null},(0,e.createElement)("table",null,(0,e.createElement)("caption",null,r),(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,o.map(((t,s)=>(0,e.createElement)("th",{key:s},t))))),(0,e.createElement)("tbody",null,(0,e.createElement)("tr",null,t.map(((t,r)=>(0,e.createElement)("td",{key:r},s(t.y))))))));kt.propTypes={data:g().arrayOf(g().shape({x:g().number,y:g().number})).isRequired,mapChartDataToTableData:g().func,dataTableCaption:g().string.isRequired,dataTableHeaderLabels:g().array.isRequired,isDataTableVisuallyHidden:g().bool},kt.defaultProps={mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const vt=kt,_t=({data:t,width:s,height:r,fillColor:o,strokeColor:n,strokeWidth:i,className:l,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:u,isDataTableVisuallyHidden:p})=>{const m=Math.max(1,Math.max(...t.map((e=>e.x)))),h=Math.max(1,Math.max(...t.map((e=>e.y)))),g=r-i,y=t.map((e=>`${e.x/m*s},${g-e.y/h*g+i}`)).join(" "),f=`0,${g+i} `+y+` ${s},${g+i}`;return(0,e.createElement)(a.Fragment,null,(0,e.createElement)("svg",{width:s,height:r,viewBox:`0 0 ${s} ${r}`,className:l,role:"img","aria-hidden":"true",focusable:"false"},(0,e.createElement)("polygon",{fill:o,points:f}),(0,e.createElement)("polyline",{fill:"none",stroke:n,strokeWidth:i,strokeLinejoin:"round",strokeLinecap:"round",points:y})),c&&(0,e.createElement)(vt,{data:t,mapChartDataToTableData:c,dataTableCaption:d,dataTableHeaderLabels:u,isDataTableVisuallyHidden:p}))};_t.propTypes={data:g().arrayOf(g().shape({x:g().number,y:g().number})).isRequired,width:g().number.isRequired,height:g().number.isRequired,fillColor:g().string,strokeColor:g().string,strokeWidth:g().number,className:g().string,mapChartDataToTableData:g().func,dataTableCaption:g().string.isRequired,dataTableHeaderLabels:g().array.isRequired,isDataTableVisuallyHidden:g().bool},_t.defaultProps={fillColor:null,strokeColor:"#000000",strokeWidth:1,className:"",mapChartDataToTableData:null,isDataTableVisuallyHidden:!0};const xt=_t,Tt=()=>(0,e.createElement)("p",{className:"yoast-wincher-seo-performance-modal__loading-message"},(0,i.__)("Tracking the ranking position…","wordpress-seo")," ",(0,e.createElement)(ve.SvgIcon,{icon:"loading-spinner"})),St=f()(ve.SvgIcon)`
	margin-left: 2px;
	flex-shrink: 0;
	rotate: ${e=>e.isImproving?"-90deg":"90deg"};
`,Rt=f().span`
	color: ${e=>e.isImproving?"#69AB56":"#DC3332"};
	font-size: 13px;
	font-weight: 600;
	line-height: 20px;
	margin-right: 2px;
	margin-left: 12px;
`,Ct=f().td`
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,It=f().td`
	padding-left: 2px !important;
`,Lt=f().td.attrs({className:"yoast-table--nopadding"})`
	& > div {
		justify-content: center;
	}
`,Pt=f().div`
	display: flex;
	align-items: center;
	& > a {
		box-sizing: border-box;
	}
`,At=f().button`
	background: none;
	color: inherit;
	border: none;
	padding: 0;
	font: inherit;
	cursor: pointer;
	outline: inherit;
    display: flex;
    align-items: center;
`,Ft=f().tr`
	background-color: ${e=>e.isEnabled?"#FFFFFF":"#F9F9F9"} !important;
`;function Ot(e){return Math.round(100*e)}function Nt({chartData:t}){if((0,p.isEmpty)(t)||(0,p.isEmpty)(t.position))return"?";const s=function(e){return Array.from({length:e.position.history.length},((e,t)=>t+1)).map((e=>(0,i.sprintf)((0,i._n)("%d day","%d days",e,"wordpress-seo"),e)))}(t),r=t.position.history.map(((e,t)=>({x:t,y:101-e.value})));return(0,e.createElement)(xt,{width:66,height:24,data:r,strokeWidth:1.8,strokeColor:"#498afc",fillColor:"#ade3fc",mapChartDataToTableData:Ot,dataTableCaption:(0,i.__)("Keyphrase position in the last 90 days on a scale from 0 to 100.","wordpress-seo"),dataTableHeaderLabels:s})}Nt.propTypes={chartData:g().object},Nt.defaultProps={chartData:{}};const Mt=({rowData:t})=>{var s;if(null==t||null===(s=t.position)||void 0===s||!s.change)return(0,e.createElement)(Nt,{chartData:t});const r=t.position.change<0;return(0,e.createElement)(a.Fragment,null,(0,e.createElement)(Nt,{chartData:t}),(0,e.createElement)(Rt,{isImproving:r},Math.abs(t.position.change)),(0,e.createElement)(St,{icon:"caret-right",color:r?"#69AB56":"#DC3332",size:"14px",isImproving:r}))};function qt(t){var s;const{keyphrase:r,rowData:o,onTrackKeyphrase:n,onUntrackKeyphrase:l,isFocusKeyphrase:c,isDisabled:d,isLoading:u,isSelected:m,onSelectKeyphrases:h}=t,g=!(0,p.isEmpty)(o),y=!(0,p.isEmpty)(null==o||null===(s=o.position)||void 0===s?void 0:s.history),f=(0,a.useCallback)((()=>{d||(g?l(r,o.id):n(r))}),[r,n,l,g,o,d]),b=(0,a.useCallback)((()=>{h((e=>m?e.filter((e=>e!==r)):e.concat(r)))}),[h,m,r]);return(0,e.createElement)(Ft,{isEnabled:g},(0,e.createElement)(Ct,null,y&&(0,e.createElement)(ve.Checkbox,{id:"select-"+r,onChange:b,checked:m,label:""})),(0,e.createElement)(It,null,r,c&&(0,e.createElement)("span",null,"*")),function(t){const{rowData:s,websiteId:r,keyphrase:o,onSelectKeyphrases:n}=t,l=(0,a.useCallback)((()=>{n([o])}),[n,o]),c=!(0,p.isEmpty)(s),d=s&&s.updated_at&&Xe()(s.updated_at)>=Xe()().subtract(7,"days"),u=s?`https://app.wincher.com/websites/${r}/keywords?serp=${s.id}&utm_medium=plugin&utm_source=yoast&referer=yoast&partner=yoast`:null;return c?d?(0,e.createElement)(a.Fragment,null,(0,e.createElement)("td",null,(0,e.createElement)(Pt,null,function(e){return!e||!e.position||e.position.value>100?"> 100":e.position.value}(s),(0,e.createElement)(ve.ButtonStyledLink,{variant:"secondary",href:u,style:{height:28,marginLeft:12},rel:"noopener",target:"_blank"},(0,i.__)("View","wordpress-seo")))),(0,e.createElement)("td",{className:"yoast-table--nopadding"},(0,e.createElement)(At,{type:"button",onClick:l},(0,e.createElement)(Mt,{rowData:s}))),(0,e.createElement)("td",null,(m=s.updated_at,Xe()(m).fromNow()))):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)(Tt,null)):(0,e.createElement)("td",{className:"yoast-table--nopadding",colSpan:"3"},(0,e.createElement)("i",null,(0,i.__)("Activate tracking to show the ranking position","wordpress-seo")));var m}(t),(0,e.createElement)(Lt,null,function({keyphrase:t,isEnabled:s,toggleAction:r,isLoading:o}){return o?(0,e.createElement)(ve.SvgIcon,{icon:"loading-spinner"}):(0,e.createElement)(ve.Toggle,{id:`toggle-keyphrase-tracking-${t}`,className:"wincher-toggle",isEnabled:s,onSetToggleState:r,showToggleStateLabel:!1})}({keyphrase:r,isEnabled:g,toggleAction:f,isLoading:u})))}Mt.propTypes={rowData:g().object},qt.propTypes={rowData:g().object,keyphrase:g().string.isRequired,onTrackKeyphrase:g().func,onUntrackKeyphrase:g().func,isFocusKeyphrase:g().bool,isDisabled:g().bool,isLoading:g().bool,websiteId:g().string,isSelected:g().bool.isRequired,onSelectKeyphrases:g().func.isRequired},qt.defaultProps={rowData:{},onTrackKeyphrase:()=>{},onUntrackKeyphrase:()=>{},isFocusKeyphrase:!1,isDisabled:!1,isLoading:!1,websiteId:""};const Dt=(0,De.makeOutboundLink)(),$t=f().span`
	display: block;
	font-style: italic;

	@media (min-width: 782px) {
		display: inline;
		position: absolute;
		${(0,De.getDirectionalStyle)("right","left")}: 8px;
	}
`,Ut=f().div`
	width: 100%;
	overflow-y: auto;
`,Bt=f().th`
	pointer-events: ${e=>e.isDisabled?"none":"initial"};
	padding-right: 0 !important;

	& > div {
		margin: 0px;
	}
`,Wt=f().th`
	padding-left: 2px !important;
`,jt=e=>{const t=(0,a.useRef)();return(0,a.useEffect)((()=>{t.current=e})),t.current},Ht=(0,p.debounce)((async function(e=null,t=null,s=null,r){return await at({path:"yoast/v1/wincher/keyphrases",method:"POST",data:{keyphrases:e,permalink:s,startAt:t},signal:r})}),500,{leading:!0}),Kt=t=>{const{addTrackedKeyphrase:s,isLoggedIn:r,keyphrases:o,permalink:n,removeTrackedKeyphrase:l,setKeyphraseLimitReached:c,setRequestFailed:d,setRequestSucceeded:u,setTrackedKeyphrases:m,setHasTrackedAll:h,trackAll:g,trackedKeyphrases:y,isNewlyAuthenticated:f,websiteId:b,focusKeyphrase:w,newRequest:E,startAt:k,selectedKeyphrases:v,onSelectKeyphrases:_}=t,x=(0,a.useRef)(),T=(0,a.useRef)(),S=(0,a.useRef)(!1),[R,C]=(0,a.useState)([]),I=(0,a.useCallback)((e=>{const t=e.toLowerCase();return y&&!(0,p.isEmpty)(y)&&y.hasOwnProperty(t)?y[t]:null}),[y]),L=(0,a.useMemo)((()=>async()=>{await nt((()=>(T.current&&T.current.abort(),T.current="undefined"==typeof AbortController?null:new AbortController,Ht(o,k,n,T.current.signal))),(e=>{u(e),m(e.results)}),(e=>{d(e)}))}),[u,d,m,o,n,k]),P=(0,a.useCallback)((async e=>{const t=(Array.isArray(e)?e:[e]).map((e=>e.toLowerCase()));C((e=>[...e,...t])),await nt((()=>it(t)),(e=>{u(e),s(e.results),L()}),(e=>{400===e.status&&e.limit&&c(e.limit),d(e)}),201),C((e=>(0,p.without)(e,...t)))}),[u,d,c,s,L]),A=(0,a.useCallback)((async(e,t)=>{e=e.toLowerCase(),C((t=>[...t,e])),await nt((()=>async function(e){return await at({path:"yoast/v1/wincher/keyphrases/untrack",method:"DELETE",data:{keyphraseID:e}})}(t)),(t=>{u(t),l(e)}),(e=>{d(e)})),C((t=>(0,p.without)(t,e)))}),[u,l,d]),F=(0,a.useCallback)((async e=>{E(),await P(e)}),[E,P]),O=jt(n),N=jt(o),M=jt(k),q=n&&k;(0,a.useEffect)((()=>{r&&q&&(n!==O||(0,p.difference)(o,N).length||k!==M)&&L()}),[r,n,O,o,N,L,q,k,M]),(0,a.useEffect)((()=>{if(r&&g&&null!==y){const e=o.filter((e=>!I(e)));e.length&&P(e),h()}}),[r,g,y,P,h,I,o]),(0,a.useEffect)((()=>{f&&!S.current&&(L(),S.current=!0)}),[f,L]),(0,a.useEffect)((()=>{if(r&&!(0,p.isEmpty)(y))return(0,p.filter)(y,(e=>(0,p.isEmpty)(e.updated_at))).length>0&&(x.current=setInterval((()=>{L()}),1e4)),()=>{clearInterval(x.current)}}),[r,y,L]);const D=r&&null===y,$=(0,a.useMemo)((()=>(0,p.isEmpty)(y)?[]:Object.values(y).filter((e=>{var t;return!(0,p.isEmpty)(null==e||null===(t=e.position)||void 0===t?void 0:t.history)})).map((e=>e.keyword))),[y]),U=(0,a.useMemo)((()=>v.length>0&&$.length>0&&$.every((e=>v.includes(e)))),[v,$]),B=(0,a.useCallback)((()=>{_(U?[]:$)}),[_,U,$]),W=(0,a.useMemo)((()=>(0,p.orderBy)(o,[e=>Object.values(y||{}).map((e=>e.keyword)).includes(e)],["desc"])),[o,y]);return o&&!(0,p.isEmpty)(o)&&(0,e.createElement)(a.Fragment,null,(0,e.createElement)(Ut,null,(0,e.createElement)("table",{className:"yoast yoast-table"},(0,e.createElement)("thead",null,(0,e.createElement)("tr",null,(0,e.createElement)(Bt,{isDisabled:0===$.length},(0,e.createElement)(ve.Checkbox,{id:"select-all",onChange:B,checked:U,label:""})),(0,e.createElement)(Wt,{scope:"col",abbr:(0,i.__)("Keyphrase","wordpress-seo")},(0,i.__)("Keyphrase","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Position","wordpress-seo")},(0,i.__)("Position","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Position over time","wordpress-seo")},(0,i.__)("Position over time","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Last updated","wordpress-seo")},(0,i.__)("Last updated","wordpress-seo")),(0,e.createElement)("th",{scope:"col",abbr:(0,i.__)("Tracking","wordpress-seo")},(0,i.__)("Tracking","wordpress-seo")))),(0,e.createElement)("tbody",null,W.map(((t,s)=>(0,e.createElement)(qt,{key:`trackable-keyphrase-${s}`,keyphrase:t,onTrackKeyphrase:F,onUntrackKeyphrase:A,rowData:I(t),isFocusKeyphrase:t===w.trim().toLowerCase(),websiteId:b,isDisabled:!r,isLoading:D||R.indexOf(t.toLowerCase())>=0,isSelected:v.includes(t),onSelectKeyphrases:_})))))),(0,e.createElement)("p",{style:{marginBottom:0,position:"relative"}},(0,e.createElement)(Dt,{href:wpseoAdminGlobalL10n["links.wincher.login"]},(0,i.sprintf)(/* translators: %s expands to Wincher */
(0,i.__)("Get more insights over at %s","wordpress-seo"),"Wincher")),(0,e.createElement)($t,null,(0,i.__)("* focus keyphrase","wordpress-seo"))))};Kt.propTypes={addTrackedKeyphrase:g().func.isRequired,isLoggedIn:g().bool,isNewlyAuthenticated:g().bool,keyphrases:g().array,newRequest:g().func.isRequired,removeTrackedKeyphrase:g().func.isRequired,setRequestFailed:g().func.isRequired,setKeyphraseLimitReached:g().func.isRequired,setRequestSucceeded:g().func.isRequired,setTrackedKeyphrases:g().func.isRequired,setHasTrackedAll:g().func.isRequired,trackAll:g().bool,trackedKeyphrases:g().object,websiteId:g().string,permalink:g().string.isRequired,focusKeyphrase:g().string,startAt:g().string,selectedKeyphrases:g().arrayOf(g().string).isRequired,onSelectKeyphrases:g().func.isRequired},Kt.defaultProps={isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],trackAll:!1,websiteId:"",focusKeyphrase:""};const zt=Kt,Yt=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getWincherWebsiteId:t,getWincherTrackableKeyphrases:s,getWincherLoginStatus:r,getWincherPermalink:o,getFocusKeyphrase:n,isWincherNewlyAuthenticated:a,shouldWincherTrackAll:i}=e("yoast-seo/editor");return{focusKeyphrase:n(),keyphrases:s(),isLoggedIn:r(),trackAll:i(),websiteId:t(),isNewlyAuthenticated:a(),permalink:o()}})),(0,o.withDispatch)((e=>{const{setWincherNewRequest:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherSetKeyphraseLimitReached:o,setWincherTrackedKeyphrases:n,setWincherTrackingForKeyphrase:a,setWincherTrackAllKeyphrases:i,unsetWincherTrackingForKeyphrase:l}=e("yoast-seo/editor");return{newRequest:()=>{t()},setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},setKeyphraseLimitReached:e=>{o(e)},addTrackedKeyphrase:e=>{a(e)},removeTrackedKeyphrase:e=>{l(e)},setTrackedKeyphrases:e=>{n(e)},setHasTrackedAll:()=>{i(!1)}}}))])(zt),Vt=(0,De.makeOutboundLink)(),Gt=(0,De.makeOutboundLink)(),Zt=()=>{const t=(0,i.sprintf)(/* translators: %1$s expands to a link to Wincher, %2$s expands to a link to the keyphrase tracking article on Yoast.com */
(0,i.__)("With %1$s you can track the ranking position of your page in the search results based on your keyphrase(s). %2$s","wordpress-seo"),"{{wincherLink/}}","{{wincherReadMoreLink/}}");return(0,e.createElement)("p",null,(0,Qe.Z)({mixedString:t,components:{wincherLink:(0,e.createElement)(Vt,{href:wpseoAdminGlobalL10n["links.wincher.website"]},"Wincher"),wincherReadMoreLink:(0,e.createElement)(Gt,{href:wpseoAdminL10n["shortlinks.wincher.seo_performance"]},(0,i.__)("Read more about keyphrase tracking with Wincher","wordpress-seo"))}}))},Xt=()=>(0,e.createElement)(ve.Alert,{type:"error"},(0,i.__)("No keyphrase has been set. Please set a keyphrase first.","wordpress-seo")),Qt=()=>(0,e.createElement)(ve.Alert,{type:"info"},(0,i.sprintf)(/* translators: %s: Expands to "Wincher". */
(0,i.__)("Automatic tracking of keyphrases is enabled. Your keyphrase(s) will automatically be tracked by %s when you publish your post.","wordpress-seo"),"Wincher"));class Jt{constructor(e,t={},s={}){this.url=e,this.origin=new URL(e).origin,this.eventHandlers=Object.assign({success:{type:"",callback:()=>{}},error:{type:"",callback:()=>{}}},t),this.options=Object.assign({height:570,width:340,title:""},s),this.popup=null,this.createPopup=this.createPopup.bind(this),this.messageHandler=this.messageHandler.bind(this),this.getPopup=this.getPopup.bind(this)}createPopup(){const{height:e,width:t,title:s}=this.options,r=["top="+(window.top.outerHeight/2+window.top.screenY-e/2),"left="+(window.top.outerWidth/2+window.top.screenX-t/2),"width="+t,"height="+e,"resizable=1","scrollbars=1","status=0"];this.popup&&!this.popup.closed||(this.popup=window.open(this.url,s,r.join(","))),this.popup&&this.popup.focus(),window.addEventListener("message",this.messageHandler,!1)}async messageHandler(e){const{data:t,source:s,origin:r}=e;r===this.origin&&this.popup===s&&(t.type===this.eventHandlers.success.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.success.callback(t)),t.type===this.eventHandlers.error.type&&(this.popup.close(),window.removeEventListener("message",this.messageHandler,!1),await this.eventHandlers.error.callback(t)))}getPopup(){return this.popup}isClosed(){return!this.popup||this.popup.closed}focus(){this.isClosed()||this.popup.focus()}}const es=t=>{const s=(0,i.sprintf)(/* translators: %s expands to a link to open the Wincher login popup. */
(0,i.__)("It seems like something went wrong when retrieving your website's data. Please %s and try again.","wordpress-seo"),"{{reconnectToWincher/}}","Wincher");return(0,e.createElement)(ve.Alert,{type:"error",className:t.className},(0,Qe.Z)({mixedString:s,components:{reconnectToWincher:(0,e.createElement)("a",{href:"#",onClick:e=>{e.preventDefault(),t.onReconnect()}},(0,i.sprintf)(/* translators: %s : Expands to "Wincher". */
(0,i.__)("reconnect to %s","wordpress-seo"),"Wincher"))}}))};es.propTypes={onReconnect:g().func.isRequired,className:g().string},es.defaultProps={className:""};const ts=es,ss=()=>(0,e.createElement)(ve.Alert,{type:"error"},(0,i.__)("Before you can track your SEO performance make sure to set either the post’s title and save it as a draft or manually set the post’s slug.","wordpress-seo")),rs=window.yoast["chart.js"],os="label";function ns(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function as(e,t){e.labels=t}function is(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:os;const r=[];e.datasets=t.map((t=>{const o=e.datasets.find((e=>e[s]===t[s]));return o&&t.data&&!r.includes(o)?(r.push(o),Object.assign(o,t),o):{...t}}))}function ls(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:os;const s={labels:[],datasets:[]};return as(s,e.labels),is(s,e.datasets,t),s}function cs(t,s){const{height:r=150,width:o=300,redraw:n=!1,datasetIdKey:a,type:i,data:l,options:c,plugins:d=[],fallbackContent:u,updateMode:p,...m}=t,h=(0,e.useRef)(null),g=(0,e.useRef)(),y=()=>{h.current&&(g.current=new rs.Chart(h.current,{type:i,data:ls(l,a),options:c&&{...c},plugins:d}),ns(s,g.current))},f=()=>{ns(s,null),g.current&&(g.current.destroy(),g.current=null)};return(0,e.useEffect)((()=>{!n&&g.current&&c&&function(e,t){const s=e.options;s&&t&&Object.assign(s,t)}(g.current,c)}),[n,c]),(0,e.useEffect)((()=>{!n&&g.current&&as(g.current.config.data,l.labels)}),[n,l.labels]),(0,e.useEffect)((()=>{!n&&g.current&&l.datasets&&is(g.current.config.data,l.datasets,a)}),[n,l.datasets]),(0,e.useEffect)((()=>{g.current&&(n?(f(),setTimeout(y)):g.current.update(p))}),[n,c,l.labels,l.datasets,p]),(0,e.useEffect)((()=>{g.current&&(f(),setTimeout(y))}),[i]),(0,e.useEffect)((()=>(y(),()=>f())),[]),e.createElement("canvas",Object.assign({ref:h,role:"img",height:r,width:o},m),u)}const ds=(0,e.forwardRef)(cs);function us(t,s){return rs.Chart.register(s),(0,e.forwardRef)(((s,r)=>e.createElement(ds,Object.assign({},s,{ref:r,type:t}))))}const ps=us("line",rs.LineController),ms={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};rs._adapters._date.override("function"==typeof Xe()?{_id:"moment",formats:function(){return ms},parse:function(e,t){return"string"==typeof e&&"string"==typeof t?e=Xe()(e,t):e instanceof Xe()||(e=Xe()(e)),e.isValid()?e.valueOf():null},format:function(e,t){return Xe()(e).format(t)},add:function(e,t,s){return Xe()(e).add(t,s).valueOf()},diff:function(e,t,s){return Xe()(e).diff(Xe()(t),s)},startOf:function(e,t,s){return e=Xe()(e),"isoWeek"===t?(s=Math.trunc(Math.min(Math.max(0,s),6)),e.isoWeekday(s).startOf("day").valueOf()):e.startOf(t).valueOf()},endOf:function(e,t){return Xe()(e).endOf(t).valueOf()}}:{}),Math.PI,Number.POSITIVE_INFINITY,Math.log10,Math.sign,"undefined"==typeof window||window.requestAnimationFrame,new Map,Object.create(null),Object.create(null),Number.EPSILON;const hs=["top","right","bottom","left"];function gs(e,t,s){const r={};s=s?"-"+s:"";for(let o=0;o<4;o++){const n=hs[o];r[n]=parseFloat(e[t+"-"+n+s])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}!function(){let e=!1;try{const t={get passive(){return e=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}}(),rs.Chart.register(rs.CategoryScale,rs.LineController,rs.LineElement,rs.PointElement,rs.LinearScale,rs.TimeScale,rs.Legend,rs.Tooltip);const ys=["#ff983b","#ffa3f7","#3798ff","#ff3b3b","#acce81","#b51751","#3949ab","#26c6da","#ccb800","#de66ff","#4db6ac","#ffab91","#45f5f1","#77f210","#90a4ae","#ffd54f","#006b5e","#8ec7d2","#b1887c","#cc9300"];function fs({datasets:t,isChartShown:s,keyphrases:r}){if(!s)return null;const o=(0,a.useMemo)((()=>Object.fromEntries([...r].sort().map(((e,t)=>[e,ys[t%ys.length]])))),[r]),n=t.map((e=>{const t=o[e.label];return{...e,data:e.data.map((({datetime:e,value:t})=>({x:e,y:t}))),lineTension:0,pointRadius:1,pointHoverRadius:4,borderWidth:2,pointHitRadius:6,backgroundColor:t,borderColor:t}})).filter((e=>!1!==e.selected));return(0,e.createElement)(ps,{height:100,data:{datasets:n},options:{plugins:{legend:{display:!0,position:"bottom",labels:{color:"black",usePointStyle:!0,boxHeight:7,boxWidth:7},onClick:p.noop},tooltip:{enabled:!0,callbacks:{title:e=>Xe()(e[0].raw.x).utc().format("YYYY-MM-DD")},titleAlign:"center",mode:"xPoint",position:"nearest",usePointStyle:!0,boxHeight:7,boxWidth:7,boxPadding:2}},scales:{x:{bounds:"ticks",type:"time",time:{unit:"day",minUnit:"day"},grid:{display:!1},ticks:{autoSkipPadding:50,maxRotation:0,color:"black"}},y:{bounds:"ticks",offset:!0,reverse:!0,ticks:{precision:0,color:"black"},max:101}}}})}rs.Interaction.modes.xPoint=(e,t,s,r)=>{const o=function(e,t){if("native"in e)return e;const{canvas:s,currentDevicePixelRatio:r}=t,o=(m=s).ownerDocument.defaultView.getComputedStyle(m,null),n="border-box"===o.boxSizing,a=gs(o,"padding"),i=gs(o,"border","width"),{x:l,y:c,box:d}=function(e,t){const s=e.touches,r=s&&s.length?s[0]:e,{offsetX:o,offsetY:n}=r;let a,i,l=!1;if(((e,t,s)=>(e>0||t>0)&&(!s||!s.shadowRoot))(o,n,e.target))a=o,i=n;else{const e=t.getBoundingClientRect();a=r.clientX-e.left,i=r.clientY-e.top,l=!0}return{x:a,y:i,box:l}}(e,s),u=a.left+(d&&i.left),p=a.top+(d&&i.top);var m;let{width:h,height:g}=t;return n&&(h-=a.width+i.width,g-=a.height+i.height),{x:Math.round((l-u)/h*s.width/r),y:Math.round((c-p)/g*s.height/r)}}(t,e);let n=[];if(rs.Interaction.evaluateInteractionItems(e,"x",o,((e,t,s)=>{e.inXRange(o.x,r)&&n.push({element:e,datasetIndex:t,index:s})})),0===n.length)return n;const a=n.reduce(((e,t)=>Math.abs(o.x-e.element.x)<Math.abs(o.x-t.element.x)?e:t)).element.x;return n=n.filter((e=>e.element.x===a)),n.some((e=>Math.abs(e.element.y-o.y)<10))?n:[]},fs.propTypes={datasets:g().arrayOf(g().shape({label:g().string.isRequired,data:g().arrayOf(g().shape({datetime:g().string.isRequired,value:g().number.isRequired})).isRequired,selected:g().bool})).isRequired,isChartShown:g().bool.isRequired,keyphrases:g().array.isRequired};const bs=({response:t,onLogin:s})=>[401,403,404].includes(t.status)?(0,e.createElement)(ts,{onReconnect:s}):(0,e.createElement)(st,null);bs.propTypes={response:g().object.isRequired,onLogin:g().func.isRequired};const ws=({isSuccess:t,response:s,allKeyphrasesMissRanking:r,onLogin:o,keyphraseLimitReached:n,limit:a})=>n?(0,e.createElement)(tt,{limit:a}):(0,p.isEmpty)(s)||t?r?(0,e.createElement)(Et,null):null:(0,e.createElement)(bs,{response:s,onLogin:o});ws.propTypes={isSuccess:g().bool.isRequired,allKeyphrasesMissRanking:g().bool.isRequired,response:g().object,onLogin:g().func.isRequired,keyphraseLimitReached:g().bool.isRequired,limit:g().number.isRequired},ws.defaultProps={response:{}};let Es=null;const ks=async e=>{if(Es&&!Es.isClosed())return void Es.focus();const{url:t}=await async function(){return await at({path:"yoast/v1/wincher/authorization-url",method:"GET"})}();Es=new Jt(t,{success:{type:"wincher:oauth:success",callback:t=>(async(e,t)=>{const{onAuthentication:s,setRequestSucceeded:r,setRequestFailed:o,keyphrases:n,addTrackedKeyphrase:a,setKeyphraseLimitReached:i}=e;await nt((()=>async function(e){const{code:t,websiteId:s}=e;return await at({path:"yoast/v1/wincher/authenticate",method:"POST",data:{code:t,websiteId:s}})}(t)),(async e=>{s(!0,!0,t.websiteId.toString()),r(e);const l=(Array.isArray(n)?n:[n]).map((e=>e.toLowerCase()));await nt((()=>it(l)),(e=>{r(e),a(e.results)}),(e=>{400===e.status&&e.limit&&i(e.limit),o(e)}),201);const c=Es.getPopup();c&&c.close()}),(async e=>o(e)))})(e,t)},error:{type:"wincher:oauth:error",callback:()=>e.onAuthentication(!1,!1)}},{title:"Wincher_login",width:500,height:700}),Es.createPopup()},vs=t=>t.isLoggedIn?null:(0,e.createElement)("p",null,(0,e.createElement)(ve.NewButton,{onClick:t.onLogin,variant:"primary"},(0,i.sprintf)(/* translators: %s expands to Wincher */
(0,i.__)("Connect with %s","wordpress-seo"),"Wincher")));vs.propTypes={isLoggedIn:g().bool.isRequired,onLogin:g().func.isRequired};const _s=f().div`
	p {
		margin: 1em 0;
	}
`,xs=f().div`
	${e=>e.isDisabled&&"\n\t\topacity: .5;\n\t\tpointer-events: none;\n\t"};
`,Ts=f().div`
	font-weight: var(--yoast-font-weight-bold);
	color: var(--yoast-color-label);
	font-size: var(--yoast-font-size-default);
`,Ss=f().div.attrs({className:"yoast-field-group"})`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 14px;
`,Rs=f().div`
	margin: 8px 0;
`,Cs=Xe().utc().startOf("day"),Is=[{name:(0,i.__)("Last day","wordpress-seo"),value:Xe()(Cs).subtract(1,"days").format(),defaultIndex:1},{name:(0,i.__)("Last week","wordpress-seo"),value:Xe()(Cs).subtract(1,"week").format(),defaultIndex:2},{name:(0,i.__)("Last month","wordpress-seo"),value:Xe()(Cs).subtract(1,"month").format(),defaultIndex:3},{name:(0,i.__)("Last year","wordpress-seo"),value:Xe()(Cs).subtract(1,"year").format(),defaultIndex:0}],Ls=t=>{const{onSelect:s,selected:r,options:o,isLoggedIn:n}=t;return n?o.length<1?null:(0,e.createElement)("select",{className:"components-select-control__input",id:"wincher-period-picker",value:(null==r?void 0:r.value)||o[0].value,onChange:s},o.map((t=>(0,e.createElement)("option",{key:t.name,value:t.value},t.name)))):null};Ls.propTypes={onSelect:g().func.isRequired,selected:g().object,options:g().array.isRequired,isLoggedIn:g().bool.isRequired};const Ps=t=>{const{trackedKeyphrases:s,isLoggedIn:r,keyphrases:o,shouldTrackAll:n,permalink:l,historyDaysLimit:c}=t;if(!l&&r)return(0,e.createElement)(ss,null);if(0===o.length)return(0,e.createElement)(Xt,null);const d=Xe()(Cs).subtract(c,"days"),u=Is.filter((e=>Xe()(e.value).isSameOrAfter(d))),m=(0,p.orderBy)(u,(e=>e.defaultIndex),"desc")[0],[h,g]=(0,a.useState)(m),[y,f]=(0,a.useState)([]),b=y.length>0,w=(0,Pe.usePrevious)(s);(0,a.useEffect)((()=>{if(!(0,p.isEmpty)(s)&&(0,p.difference)(Object.keys(s),Object.keys(w||[])).length){const e=Object.values(s).map((e=>e.keyword));f(e)}}),[s,w]),(0,a.useEffect)((()=>{g(m)}),[null==m?void 0:m.name]);const E=(0,a.useCallback)((e=>{const t=Is.find((t=>t.value===e.target.value));t&&g(t)}),[g]),k=(0,a.useMemo)((()=>(0,p.isEmpty)(y)||(0,p.isEmpty)(s)?[]:Object.values(s).filter((e=>{var t;return!(null==e||null===(t=e.position)||void 0===t||!t.history)})).map((e=>{var t;return{label:e.keyword,data:e.position.history,selected:y.includes(e.keyword)&&!(0,p.isEmpty)(null===(t=e.position)||void 0===t?void 0:t.history)}}))),[y,s]);return(0,e.createElement)(xs,{isDisabled:!r},(0,e.createElement)("p",null,(0,i.__)("You can enable / disable tracking the SEO performance for each keyphrase below.","wordpress-seo")),r&&n&&(0,e.createElement)(Qt,null),(0,e.createElement)(Ss,null,(0,e.createElement)(Ls,{selected:h,onSelect:E,options:u,isLoggedIn:r})),(0,e.createElement)(Rs,null,(0,e.createElement)(fs,{isChartShown:b,datasets:k,keyphrases:o})),(0,e.createElement)(Yt,{startAt:null==h?void 0:h.value,selectedKeyphrases:y,onSelectKeyphrases:f,trackedKeyphrases:s}))};function As(t){const{isNewlyAuthenticated:s,isLoggedIn:r}=t,o=(0,a.useCallback)((()=>{ks(t)}),[ks,t]),n=mt(r);return(0,e.createElement)(_s,null,s&&(0,e.createElement)(wt,null),r&&(0,e.createElement)(bt,{trackingInfo:n}),(0,e.createElement)(Ts,null,(0,i.__)("SEO performance","wordpress-seo"),(0,e.createElement)(ve.HelpIcon,{linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about the SEO performance feature.","wordpress-seo")})),(0,e.createElement)(Zt,null),(0,e.createElement)(vs,{isLoggedIn:r,onLogin:o}),(0,e.createElement)(ws,{...t,onLogin:o}),(0,e.createElement)(Ps,{...t,historyDaysLimit:(null==n?void 0:n.historyDays)||31}))}Ps.propTypes={trackedKeyphrases:g().object,keyphrases:g().array.isRequired,isLoggedIn:g().bool.isRequired,shouldTrackAll:g().bool.isRequired,permalink:g().string.isRequired,historyDaysLimit:g().number},As.propTypes={trackedKeyphrases:g().object,addTrackedKeyphrase:g().func.isRequired,isLoggedIn:g().bool,isNewlyAuthenticated:g().bool,keyphrases:g().array,response:g().object,shouldTrackAll:g().bool,permalink:g().string,historyDaysLimit:g().number},As.defaultProps={trackedKeyphrases:null,isLoggedIn:!1,isNewlyAuthenticated:!1,keyphrases:[],response:{},shouldTrackAll:!1,permalink:"",historyDaysLimit:0};const Fs=(0,Pe.compose)([(0,o.withSelect)((e=>{const{isWincherNewlyAuthenticated:t,getWincherKeyphraseLimitReached:s,getWincherLimit:r,getWincherHistoryDaysLimit:o,getWincherLoginStatus:n,getWincherRequestIsSuccess:a,getWincherRequestResponse:i,getWincherTrackableKeyphrases:l,getWincherTrackedKeyphrases:c,getWincherAllKeyphrasesMissRanking:d,getWincherPermalink:u,shouldWincherAutomaticallyTrackAll:p}=e("yoast-seo/editor");return{keyphrases:l(),trackedKeyphrases:c(),allKeyphrasesMissRanking:d(),isLoggedIn:n(),isNewlyAuthenticated:t(),isSuccess:a(),keyphraseLimitReached:s(),limit:r(),response:i(),shouldTrackAll:p(),permalink:u(),historyDaysLimit:o()}})),(0,o.withDispatch)((e=>{const{setWincherWebsiteId:t,setWincherRequestSucceeded:s,setWincherRequestFailed:r,setWincherTrackingForKeyphrase:o,setWincherSetKeyphraseLimitReached:n,setWincherLoginStatus:a}=e("yoast-seo/editor");return{setRequestSucceeded:e=>{s(e)},setRequestFailed:e=>{r(e)},addTrackedKeyphrase:e=>{o(e)},setKeyphraseLimitReached:e=>{n(e)},onAuthentication:(e,s,r)=>{t(r),a(e,s)}}}))])(As),Os=f()(Ae)`
	width: 18px;
	height: 18px;
	margin: 3px;
`;function Ns(e){const{keyphrases:t,onNoKeyphraseSet:s,onOpen:r,location:o}=e;if(!t.length){let e=document.querySelector("#focus-keyword-input-metabox");return e||(e=document.querySelector("#focus-keyword-input-sidebar")),e.focus(),void s()}r(o)}function Ms(t){const{location:s,whichModalOpen:r,shouldCloseOnClickOutside:o}=t,n=(0,a.useCallback)((()=>{Ns(t)}),[Ns,t]),l=(0,i.__)("Track SEO performance","wordpress-seo"),c=Fe();return(0,e.createElement)(a.Fragment,null,r===s&&(0,e.createElement)(je,{title:l,onRequestClose:t.onClose,icon:(0,e.createElement)(Ye,null),additionalClassName:"yoast-wincher-seo-performance-modal yoast-gutenberg-modal__no-padding",shouldCloseOnClickOutside:o},(0,e.createElement)($e,{className:"yoast-gutenberg-modal__content yoast-wincher-seo-performance-modal__content"},(0,e.createElement)(Fs,null))),"sidebar"===s&&(0,e.createElement)(Ge,{id:`wincher-open-button-${s}`,title:l,SuffixHeroIcon:(0,e.createElement)(Os,{className:"yst-text-slate-500",...c}),onClick:n}),"metabox"===s&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(qe,{id:`wincher-open-button-${s}`,onClick:n},(0,e.createElement)(qe.Text,null,l),(0,e.createElement)(Ae,{className:"yst-h-5 yst-w-5 yst-text-slate-500",...c}))))}Ms.propTypes={location:g().string,whichModalOpen:g().oneOf(["none","metabox","sidebar","postpublish"]),shouldCloseOnClickOutside:g().bool,keyphrases:g().array.isRequired,onNoKeyphraseSet:g().func.isRequired,onOpen:g().func.isRequired,onClose:g().func.isRequired},Ms.defaultProps={location:"",whichModalOpen:"none",shouldCloseOnClickOutside:!0};const qs=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getWincherModalOpen:t,getWincherTrackableKeyphrases:s}=e("yoast-seo/editor");return{keyphrases:s(),whichModalOpen:t()}})),(0,o.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherDismissModal:s,setWincherNoKeyphrase:r}=e("yoast-seo/editor");return{onOpen:e=>{t(e)},onClose:()=>{s()},onNoKeyphraseSet:()=>{r()}}}))])(Ms),Ds=window.yoast.externals.components;function $s(){return(0,Pe.createHigherOrderComponent)((function(e){return(0,Pe.pure)((function(t){const s=(0,a.useContext)(d.LocationContext);return(0,a.createElement)(e,{...t,location:s})}))}),"withLocation")}const Us=(0,Pe.compose)([(0,o.withSelect)((e=>{const{isCornerstoneContent:t}=e("yoast-seo/editor");return{isCornerstone:t(),learnMoreUrl:wpseoAdminL10n["shortlinks.cornerstone_content_info"]}})),(0,o.withDispatch)((e=>{const{toggleCornerstoneContent:t}=e("yoast-seo/editor");return{onChange:t}})),$s()])(Ds.CollapsibleCornerstone),Bs=window.yoast.searchMetadataPreviews,Ws=f()(ve.StyledSection)`
	&${ve.StyledSectionBase} {
		padding: 0;

		& ${ve.StyledHeading} {
			${(0,De.getDirectionalStyle)("padding-left","padding-right")}: 20px;
			margin-left: ${(0,De.getDirectionalStyle)("0","20px")};
		}
	}
`,js=({children:t,title:s,icon:r,hasPaperStyle:o,shoppingData:n})=>(0,e.createElement)(Ws,{headingLevel:3,headingText:s,headingIcon:r,headingIconColor:"#555",hasPaperStyle:o,shoppingData:n},t);js.propTypes={children:g().element,title:g().string,icon:g().string,hasPaperStyle:g().bool,shoppingData:g().object},js.defaultProps={hasPaperStyle:!0,shoppingData:null};const Hs=js,Ks=window.wp.sanitize,zs="SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE";function Ys(e,t,s="",r=!1){const o="string"==typeof t?(0,De.decodeHTML)(t):t;return{type:zs,name:e,value:o,label:s,hidden:r}}function Vs(e){return e.charAt(0).toUpperCase()+e.slice(1)}const{stripHTMLTags:Gs}=De.strings,Zs=["slug","content","contentImage","snippetPreviewImageURL"];function Xs(e,t="_"){return e.replace(/\s/g,t)}function Qs(e,t=156){return(e=(e=(0,Ks.stripTags)(e)).trim()).length<=t||(e=e.substring(0,t),/\s/.test(e)&&(e=e.substring(0,e.lastIndexOf(" ")))),e}const Js=(0,p.memoize)(((e,t)=>0===e?p.noop:(0,p.debounce)((s=>t(s,e)),500))),er=({link:t,text:s})=>(0,e.createElement)(T.Root,null,(0,e.createElement)("p",null,s),(0,e.createElement)(T.Button,{href:t,as:"a",className:"yst-gap-2 yst-mb-5 yst-mt-2",variant:"upsell",target:"_blank",rel:"noopener"},(0,e.createElement)(R,{className:"yst-w-4 yst-h-4 yst--ms-1 yst-shrink-0"}),(0,i.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,i.__)("Unlock with %1$s","wordpress-seo"),"Yoast WooCommerce SEO")));er.propTypes={link:g().string.isRequired,text:g().string.isRequired};const tr=er,sr=function(e,t){let s=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(s=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[s]&&(e.url=e.url.slice(0,s)+e.url.slice(s+1)),function(e){const t=(0,p.get)(window,["YoastSEO","app","pluggable"],!1);if(!t||!(0,p.get)(window,["YoastSEO","app","pluggable","loaded"],!1))return function(e){const t=(0,p.get)(window,["YoastSEO","wp","replaceVarsPlugin","replaceVariables"],p.identity);return{url:e.url,title:Gs(t(e.title)),description:Gs(t(e.description)),filteredSEOTitle:e.filteredSEOTitle?Gs(t(e.filteredSEOTitle)):""}}(e);const s=t._applyModifications.bind(t);return{url:e.url,title:Gs(s("data_page_title",e.title)),description:Gs(s("data_meta_desc",e.description)),filteredSEOTitle:e.filteredSEOTitle?Gs(s("data_page_title",e.filteredSEOTitle)):""}}(e)},rr=(0,Pe.compose)([(0,o.withSelect)((function(e){const{getBaseUrlFromSettings:t,getDateFromSettings:s,getFocusKeyphrase:r,getRecommendedReplaceVars:o,getReplaceVars:n,getShoppingData:a,getSiteIconUrlFromSettings:i,getSnippetEditorData:l,getSnippetEditorMode:c,getSnippetEditorPreviewImageUrl:d,getSnippetEditorWordsToHighlight:u,isCornerstoneContent:p,getIsTerm:m,getContentLocale:h,getSiteName:g}=e("yoast-seo/editor"),y=n();return y.forEach((e=>{""!==e.value||["title","excerpt","excerpt_only"].includes(e.name)||(e.value="%%"+e.name+"%%")})),{baseUrl:t(),data:l(),date:s(),faviconSrc:i(),keyword:r(),mobileImageSrc:d(),mode:c(),recommendedReplacementVariables:o(),replacementVariables:y,shoppingData:a(),wordsToHighlight:u(),isCornerstone:p(),isTaxonomy:m(),locale:h(),siteName:g()}})),(0,o.withDispatch)((function(e,t,{select:s}){const{updateData:r,switchMode:o,updateAnalysisData:n,findCustomFields:a}=e("yoast-seo/editor"),i=e("core/editor"),l=s("yoast-seo/editor").getPostId();return{onChange:(e,t)=>{switch(e){case"mode":o(t);break;case"slug":r({slug:t}),i&&i.editPost({slug:t});break;default:r({[e]:t})}},onChangeAnalysisData:n,onReplacementVariableSearchChange:Js(l,a)}}))])((t=>{const s=(0,o.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/product-google-preview-metabox")),[]),r=(0,o.useSelect)((e=>e("yoast-seo/editor").getIsWooSeoUpsell()),[]),n=(0,i.__)("Want an enhanced Google preview of how your WooCommerce products look in the search results?","wordpress-seo");return(0,e.createElement)(d.LocationConsumer,null,(o=>(0,e.createElement)(Hs,{icon:"eye",hasPaperStyle:t.hasPaperStyle},(0,e.createElement)(e.Fragment,null,r&&(0,e.createElement)(tr,{link:s,text:n}),(0,e.createElement)(Bs.SnippetEditor,{...t,descriptionPlaceholder:(0,i.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:sr,showCloseButton:!1,idSuffix:o})))))})),or=(0,o.withSelect)((e=>{const{getWarningMessage:t}=e("yoast-seo/editor");return{message:t()}}))(ve.Warning),nr=window.yoast.featureFlag,ar=f()(ve.Collapsible)`
	h2 > button {
		padding-left: 24px;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,ir=t=>(0,e.createElement)(ar,{hasPadding:!0,hasSeparator:!0,...t}),lr=()=>{const t=(0,o.useSelect)((e=>e("yoast-seo/editor").getEstimatedReadingTime()),[]),s=(0,a.useMemo)((()=>(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-estimated_reading_time","")),[]);return(0,e.createElement)(ve.InsightsCard,{amount:t,unit:(0,i._n)("minute","minutes",t,"wordpress-seo"),title:(0,i.__)("Reading time","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about reading time","wordpress-seo")})},cr=(0,De.makeOutboundLink)();function dr(t,s,r){const o=function(e){switch(e){case xe.DIFFICULTY.FAIRLY_DIFFICULT:case xe.DIFFICULTY.DIFFICULT:case xe.DIFFICULTY.VERY_DIFFICULT:return(0,i.__)("Try to make shorter sentences, using less difficult words to improve readability","wordpress-seo");case xe.DIFFICULTY.NO_DATA:return(0,i.__)("Continue writing to get insight into the readability of your text!","wordpress-seo");default:return(0,i.__)("Good job!","wordpress-seo")}}(s);return(0,e.createElement)("span",null,function(e,t){return-1===e?(0,i.__)("Your text should be slightly longer to calculate your Flesch reading ease score.","wordpress-seo"):(0,i.sprintf)(
/* Translators: %1$s expands to the numeric Flesch reading ease score,
  %2$s expands to the easiness of reading (e.g. 'easy' or 'very difficult') */
(0,i.__)("The copy scores %1$s in the test, which is considered %2$s to read.","wordpress-seo"),e,function(e){switch(e){case xe.DIFFICULTY.NO_DATA:return(0,i.__)("no data","wordpress-seo");case xe.DIFFICULTY.VERY_EASY:return(0,i.__)("very easy","wordpress-seo");case xe.DIFFICULTY.EASY:return(0,i.__)("easy","wordpress-seo");case xe.DIFFICULTY.FAIRLY_EASY:return(0,i.__)("fairly easy","wordpress-seo");case xe.DIFFICULTY.OKAY:return(0,i.__)("okay","wordpress-seo");case xe.DIFFICULTY.FAIRLY_DIFFICULT:return(0,i.__)("fairly difficult","wordpress-seo");case xe.DIFFICULTY.DIFFICULT:return(0,i.__)("difficult","wordpress-seo");case xe.DIFFICULTY.VERY_DIFFICULT:return(0,i.__)("very difficult","wordpress-seo")}}(t))}(t,s)," ",s>=xe.DIFFICULTY.FAIRLY_DIFFICULT?(0,e.createElement)(cr,{href:r},o+"."):o)}const ur=()=>{let t=(0,o.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseScore()),[]);const s=(0,a.useMemo)((()=>(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease","")),[]),r=(0,o.useSelect)((e=>e("yoast-seo/editor").getFleschReadingEaseDifficulty()),[t]),n=(0,a.useMemo)((()=>{const e=(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-flesch_reading_ease_article","");return dr(t,r,e)}),[t,r]);return-1===t&&(t="?"),(0,e.createElement)(ve.InsightsCard,{amount:t,unit:(0,i.__)("out of 100","wordpress-seo"),title:(0,i.__)("Flesch reading ease","wordpress-seo"),linkTo:s
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about Flesch reading ease","wordpress-seo"),description:n})},pr=({data:t,itemScreenReaderText:s,className:r,...o})=>{const n=(0,a.useMemo)((()=>{var e,s;return null!==(e=null===(s=(0,p.maxBy)(t,"number"))||void 0===s?void 0:s.number)&&void 0!==e?e:0}),[t]);return(0,e.createElement)("ul",{className:Ne()("yoast-data-model",r),...o},t.map((({name:t,number:r})=>(0,e.createElement)("li",{key:`${t}_dataItem`,style:{"--yoast-width":r/n*100+"%"}},t,(0,e.createElement)("span",null,r),s&&(0,e.createElement)("span",{className:"screen-reader-text"},(0,i.sprintf)(s,r))))))};pr.propTypes={data:g().arrayOf(g().shape({name:g().string.isRequired,number:g().number.isRequired})),itemScreenReaderText:g().string,className:g().string},pr.defaultProps={data:[],itemScreenReaderText:"",className:""};const mr=pr,hr=window.wp.url,gr=(0,De.makeOutboundLink)(),yr=({location:t})=>{const s=(0,o.useSelect)((e=>{var t,s;return null===(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getPreference("isProminentWordsAvailable",!1))||void 0===t||t}),[]),r=(0,o.useSelect)((e=>e("yoast-seo/editor").getPreference("shouldUpsell",!1)),[]),n=(0,a.useMemo)((()=>(0,p.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${t}-prominent_words`,"")),[t]),l=(0,a.useMemo)((()=>{const t=(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-keyword_research_link","");return S((0,i.sprintf)(
// translators: %1$s and %2$s are replaced by opening and closing <a> tags.
(0,i.__)("Read our %1$sultimate guide to keyword research%2$s to learn more about keyword research and keyword strategy.","wordpress-seo"),"<a>","</a>"),{a:(0,e.createElement)(gr,{href:t})})}),[]),c=(0,a.useMemo)((()=>S((0,i.sprintf)(
// translators: %1$s expands to a starting `b` tag, %1$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,i.__)("With %1$s%3$s%2$s, this section will show you which words occur most often in your text. By checking these prominent words against your intended keyword(s), you'll know how to edit your text to be more focused.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,e.createElement)("b",null)})),[]),u=(0,o.useSelect)((e=>{var t,s;return null!==(t=null===(s=e("yoast-seo-premium/editor"))||void 0===s?void 0:s.getProminentWords())&&void 0!==t?t:[]}),[]),m=(0,a.useMemo)((()=>{const e=(0,i.sprintf)(
// translators: %1$s expands to Yoast SEO Premium.
(0,i.__)("Get %s to enjoy the benefits of prominent words","wordpress-seo"),"Yoast SEO Premium").split(/\s+/);return e.map(((t,s)=>({name:t,number:e.length-s})))}),[]),h=(0,a.useMemo)((()=>r?m:u.map((({word:e,occurrence:t})=>({name:e,number:t})))),[u,m]);if(!s)return null;const{locationContext:g}=(0,d.useRootContext)();return(0,e.createElement)("div",{className:"yoast-prominent-words"},(0,e.createElement)("div",{className:"yoast-field-group__title"},(0,e.createElement)("b",null,(0,i.__)("Prominent words","wordpress-seo"))),!r&&(0,e.createElement)("p",null,0===h.length?(0,i.__)("Once you add a bit more copy, we'll give you a list of words that occur the most in the content. These give an indication of what your content focuses on.","wordpress-seo"):(0,i.__)("The following words occur the most in the content. These give an indication of what your content focuses on. If the words differ a lot from your topic, you might want to rewrite your content accordingly.","wordpress-seo")),r&&(0,e.createElement)("p",null,c),r&&(0,e.createElement)(gr,{href:(0,hr.addQueryArgs)(n,{context:g}),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2",className:"yoast-button yoast-button-upsell"},(0,i.sprintf)(
// translators: %s expands to `Premium` (part of add-on name).
(0,i.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,e.createElement)("p",null,l),(0,e.createElement)(mr,{data:h,itemScreenReaderText:/* translators: Hidden accessibility text; %d expands to the number of occurrences. */
(0,i.__)("%d occurrences","wordpress-seo"),"aria-label":(0,i.__)("Prominent words","wordpress-seo"),className:r?"yoast-data-model--upsell":null}))};yr.propTypes={location:g().string.isRequired};const fr=yr,br=()=>{const t=(0,o.useSelect)((e=>e("yoast-seo/editor").getTextLength()),[]),s=(0,a.useMemo)((()=>(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-word_count","")),[]);let r=(0,i._n)("word","words",t.count,"wordpress-seo"),n=(0,i.__)("Word count","wordpress-seo"),l=(0,i.__)("Learn more about word count","wordpress-seo");return"character"===t.unit&&(r=(0,i._n)("character","characters",t.count,"wordpress-seo"),n=(0,i.__)("Character count","wordpress-seo"),
/* translators: Hidden accessibility text. */
l=(0,i.__)("Learn more about character count","wordpress-seo")),(0,e.createElement)(ve.InsightsCard,{amount:t.count,unit:r,title:n,linkTo:s,linkText:l})},wr=(0,De.makeOutboundLink)(),Er=({location:t})=>{const s=(0,a.useMemo)((()=>(0,p.get)(window,`wpseoAdminL10n.shortlinks-insights-upsell-${t}-text_formality`,"")),[t]),r=(0,a.useMemo)((()=>S((0,i.sprintf)(
// Translators: %1$s expands to a starting `b` tag, %2$s expands to a closing `b` tag and %3$s expands to `Yoast SEO Premium`.
(0,i.__)("%1$s%3$s%2$s will help you assess the formality level of your text.","wordpress-seo"),"<b>","</b>","Yoast SEO Premium"),{b:(0,e.createElement)("b",null)})),[]);return(0,e.createElement)(a.Fragment,null,(0,e.createElement)("div",null,(0,e.createElement)("p",null,r),(0,e.createElement)(wr,{href:s,className:"yoast-button yoast-button-upsell"},(0,i.sprintf)(
// Translators: %s expands to `Premium` (part of add-on name).
(0,i.__)("Unlock with %s","wordpress-seo"),"Premium"),(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"}))))};Er.propTypes={location:g().string.isRequired};const kr=Er,vr=({location:t,name:s})=>{const r=(0,o.useSelect)((e=>e("yoast-seo/editor").isFormalitySupported()),[]),n=m().isPremium,a=n?(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_premium",""):(0,p.get)(window,"wpseoAdminL10n.shortlinks-insights-text_formality_info_free",""),l=(0,i.__)("Read more about text formality.","wordpress-seo");return r?(0,e.createElement)("div",{className:"yoast-text-formality"},(0,e.createElement)("div",{className:"yoast-field-group__title"},(0,e.createElement)("b",null,(0,i.__)("Text formality","wordpress-seo")),(0,e.createElement)(ve.HelpIcon,{linkTo:a,linkText:l})),n?(0,e.createElement)(k.Slot,{name:s}):(0,e.createElement)(kr,{location:t})):null};vr.propTypes={location:g().string.isRequired,name:g().string.isRequired};const _r=vr,xr=({location:t})=>{const s=(0,o.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]);return(0,e.createElement)(ir,{title:(0,i.__)("Insights","wordpress-seo"),id:`yoast-insights-collapsible-${t}`,className:"yoast-insights"},(0,e.createElement)(fr,{location:t}),(0,e.createElement)("div",null,s&&(0,e.createElement)("div",{className:"yoast-insights-row"},(0,e.createElement)(ur,null)),(0,e.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,e.createElement)(lr,null),(0,e.createElement)(br,null)),(0,nr.isFeatureEnabled)("TEXT_FORMALITY")&&(0,e.createElement)(_r,{location:t,name:"YoastTextFormalityMetabox"})))};xr.propTypes={location:g().string},xr.defaultProps={location:"metabox"};const Tr=xr,Sr=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z",clipRule:"evenodd"}))})),Rr=f().div`
  padding: 25px 32px 32px;
  color: #303030;
`,Cr=f().ul`
  margin: 0;
  padding: 0;

  li {
    list-style-image: var(--yoast-svg-icon-check);
    margin: 0.5rem 0 0 1.5rem;
    line-height: 1.4em;

    &::marker {
      font-size: 1.5rem;
    }
  }
`,Ir=f().span`
  display: block;
  margin-top: 4px;
`,Lr=f().h2`
  margin-top: 0;
  margin-bottom: 0.25rem;
  color: #303030;
  font-size: 0.8125rem;
  font-weight: 600;
`,Pr=f().p`
  display: block;
  margin: 0.25rem 0 1rem 0 !important;
  max-width: 420px;
`,Ar=f().hr`
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  border-top: 0;
  border-bottom: 1px solid #E2E8F0;
`,Fr=f().div`
  text-align: center;
`,Or=f().a`
  width: 100%;
`,Nr=(0,De.makeOutboundLink)(Or);class Mr extends a.Component{constructor(e){super(e)}createBenefitsList(t){return t.length>0&&(0,e.createElement)(Cr,{role:"list"},t.map(((t,s)=>(0,e.createElement)("li",{key:`upsell-benefit-${s}`},S(t,{strong:(0,e.createElement)("strong",null)})))))}render(){const t=(0,o.select)("yoast-seo/editor").isPromotionActive("black-friday-2024-promotion");return(0,e.createElement)(a.Fragment,null,t&&(0,e.createElement)("div",{className:"yst-flex  yst-items-center yst-text-lg yst-content-between yst-bg-black yst-text-amber-300 yst-h-9 yst-border-amber-300 yst-border-y yst-border-x-0 yst-border-solid yst-px-6"},(0,e.createElement)("div",{className:"yst-mx-auto"},(0,i.__)("30% OFF - BLACK FRIDAY","wordpress-seo"))),(0,e.createElement)(Rr,null,(0,e.createElement)(Lr,null,this.props.title),(0,e.createElement)(Pr,null,this.props.description),(0,e.createElement)(Fr,null,(0,e.createElement)(Nr,{...this.props.upsellButton},this.props.upsellButtonText,this.props.upsellButtonHasCaret&&(0,e.createElement)("span",{"aria-hidden":"true",className:"yoast-button-upsell__caret"})),(0,e.createElement)(Ir,{id:this.props.upsellButton["aria-describedby"]},this.props.upsellButtonLabel)),(0,e.createElement)(Ar,null),(0,e.createElement)(Lr,null,this.props.benefitsTitle),this.createBenefitsList(this.props.benefits)))}}Mr.propTypes={title:g().node,benefits:g().array,benefitsTitle:g().node,description:g().node,upsellButton:g().object,upsellButtonText:g().string.isRequired,upsellButtonLabel:g().string,upsellButtonHasCaret:g().bool},Mr.defaultProps={title:null,description:null,benefits:[],benefitsTitle:null,upsellButton:{href:"",className:"button button-primary"},upsellButtonLabel:"",upsellButtonHasCaret:!0};const qr=Mr,Dr=()=>{const[t,,,s,r]=(0,T.useToggleState)(!1),{locationContext:o}=(0,d.useRootContext)(),n=(0,T.useSvgAria)(),a=o.includes("sidebar"),l=o.includes("metabox"),c=wpseoAdminL10n[a?"shortlinks.upsell.sidebar.internal_linking_suggestions":"shortlinks.upsell.metabox.internal_linking_suggestions"];return(0,e.createElement)(e.Fragment,null,t&&(0,e.createElement)(je,{title:(0,i.__)("Get internal linking suggestions","wordpress-seo"),onRequestClose:r,additionalClassName:"",id:"yoast-internal-linking-suggestions-upsell",className:`${Be} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,e.createElement)(Ue,null,(0,e.createElement)(qr,{title:(0,i.__)("Rank higher by connecting your content","wordpress-seo"),description:(0,i.sprintf)(/* translators: %s expands to Yoast SEO Premium. */
(0,i.__)("%s automatically suggests to what content you can link with easy drag-and-drop functionality, which is good for your SEO!","wordpress-seo"),"Yoast SEO Premium"),benefitsTitle:(0,i.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,i.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:he(),upsellButtonText:(0,i.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,i.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:(0,hr.addQueryArgs)(c,{context:o}),className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,i.__)("1 year free support and updates included!","wordpress-seo")}))),a&&(0,e.createElement)(Ge,{id:"yoast-internal-linking-suggestions-sidebar-modal-open-button",title:(0,i.__)("Internal linking suggestions","wordpress-seo"),onClick:s},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Sr,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...n})))),l&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(qe,{id:"yoast-internal-linking-suggestions-metabox-modal-open-button",onClick:s},(0,e.createElement)(qe.Text,null,(0,i.__)("Internal linking suggestions","wordpress-seo")),(0,e.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Sr,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...n}),(0,e.createElement)("span",null,"Premium")))))},$r=({children:t})=>(0,e.createElement)("div",null,t);$r.propTypes={renderPriority:g().number.isRequired,children:g().node.isRequired};const Ur=$r,Br=({noIndex:t,onNoIndexChange:s,editorContext:r,isPrivateBlog:o})=>{const n=(e=>{const t=(0,i.__)("No","wordpress-seo"),s=(0,i.__)("Yes","wordpress-seo"),r=e.noIndex?t:s;return window.wpseoScriptData.isPost?[{name:(0,i.sprintf)(/* translators: %1$s translates to "yes" or "no", %2$s translates to the content type label in plural form */
(0,i.__)("%1$s (current default for %2$s)","wordpress-seo"),r,e.postTypeNamePlural),value:"0"},{name:t,value:"1"},{name:s,value:"2"}]:[{name:(0,i.sprintf)(/* translators: %1$s translates to "yes" or "no", %2$s translates to the content type label in plural form */
(0,i.__)("%1$s (current default for %2$s)","wordpress-seo"),r,e.postTypeNamePlural),value:"default"},{name:s,value:"index"},{name:t,value:"noindex"}]})(r);return(0,e.createElement)(d.LocationConsumer,null,(r=>(0,e.createElement)(a.Fragment,null,o&&(0,e.createElement)(ve.Alert,{type:"warning"},(0,i.__)("Even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect.","wordpress-seo")),(0,e.createElement)(ve.Select,{label:(0,i.__)("Allow search engines to show this content in search results?","wordpress-seo"),onChange:s,id:(0,De.join)(["yoast-meta-robots-noindex",r]),options:n,selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.allow_search_engines"]
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about the no-index setting on our help page.","wordpress-seo")}))))};Br.propTypes={noIndex:g().string.isRequired,onNoIndexChange:g().func.isRequired,editorContext:g().object.isRequired,isPrivateBlog:g().bool},Br.defaultProps={isPrivateBlog:!1};const Wr=({noFollow:t,onNoFollowChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>{const o=(0,De.join)(["yoast-meta-robots-nofollow",r]);return(0,e.createElement)(ve.RadioButtonGroup,{id:o,options:[{value:"0",label:"Yes"},{value:"1",label:"No"}],label:(0,i.__)("Should search engines follow links on this content?","wordpress-seo"),groupName:o,onChange:s,selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.follow_links"]
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about the no-follow setting on our help page.","wordpress-seo")})}));Wr.propTypes={noFollow:g().string.isRequired,onNoFollowChange:g().func.isRequired};const jr=({advanced:t,onAdvancedChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>{const o=(0,De.join)(["yoast-meta-robots-advanced",r]),n=`${o}-input`;return(0,e.createElement)(ve.MultiSelect,{label:(0,i.__)("Meta robots advanced","wordpress-seo"),onChange:s,id:o,inputId:n,options:[{name:(0,i.__)("No Image Index","wordpress-seo"),value:"noimageindex"},{name:(0,i.__)("No Archive","wordpress-seo"),value:"noarchive"},{name:(0,i.__)("No Snippet","wordpress-seo"),value:"nosnippet"}],selected:t,linkTo:wpseoAdminL10n["shortlinks.advanced.meta_robots"]
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about advanced meta robots settings on our help page.","wordpress-seo")})}));jr.propTypes={advanced:g().array.isRequired,onAdvancedChange:g().func.isRequired};const Hr=({breadcrumbsTitle:t,onBreadcrumbsTitleChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>(0,e.createElement)(ve.TextInput,{label:(0,i.__)("Breadcrumbs Title","wordpress-seo"),id:(0,De.join)(["yoast-breadcrumbs-title",r]),onChange:s,value:t,linkTo:wpseoAdminL10n["shortlinks.advanced.breadcrumbs_title"]
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about the breadcrumbs title setting on our help page.","wordpress-seo")})));Hr.propTypes={breadcrumbsTitle:g().string.isRequired,onBreadcrumbsTitleChange:g().func.isRequired};const Kr=({canonical:t,onCanonicalChange:s})=>(0,e.createElement)(d.LocationConsumer,null,(r=>(0,e.createElement)(ve.TextInput,{label:(0,i.__)("Canonical URL","wordpress-seo"),id:(0,De.join)(["yoast-canonical",r]),onChange:s,value:t,linkTo:"https://yoa.st/canonical-url"
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about canonical URLs on our help page.","wordpress-seo")})));Kr.propTypes={canonical:g().string.isRequired,onCanonicalChange:g().func.isRequired};const zr=t=>{const{noIndex:s,noFollow:r,advanced:o,breadcrumbsTitle:n,canonical:i,onNoIndexChange:l,onNoFollowChange:c,onAdvancedChange:d,onBreadcrumbsTitleChange:u,onCanonicalChange:p,onLoad:m,isLoading:h,editorContext:g,isBreadcrumbsDisabled:y,isPrivateBlog:f}=t;(0,a.useEffect)((()=>{setTimeout((()=>{h&&m()}))}));const b={noIndex:s,onNoIndexChange:l,editorContext:g,isPrivateBlog:f},w={noFollow:r,onNoFollowChange:c},E={advanced:o,onAdvancedChange:d},k={breadcrumbsTitle:n,onBreadcrumbsTitleChange:u},v={canonical:i,onCanonicalChange:p};return h?null:(0,e.createElement)(a.Fragment,null,(0,e.createElement)(Br,{...b}),g.isPost&&(0,e.createElement)(Wr,{...w}),g.isPost&&(0,e.createElement)(jr,{...E}),!y&&(0,e.createElement)(Hr,{...k}),(0,e.createElement)(Kr,{...v}))};zr.propTypes={noIndex:g().string.isRequired,canonical:g().string.isRequired,onNoIndexChange:g().func.isRequired,onCanonicalChange:g().func.isRequired,onLoad:g().func.isRequired,isLoading:g().bool.isRequired,editorContext:g().object.isRequired,isBreadcrumbsDisabled:g().bool.isRequired,isPrivateBlog:g().bool,advanced:g().array,onAdvancedChange:g().func,noFollow:g().string,onNoFollowChange:g().func,breadcrumbsTitle:g().string,onBreadcrumbsTitleChange:g().func},zr.defaultProps={advanced:[],onAdvancedChange:()=>{},noFollow:"",onNoFollowChange:()=>{},breadcrumbsTitle:"",onBreadcrumbsTitleChange:()=>{},isPrivateBlog:!1};const Yr=zr,Vr=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getNoIndex:t,getNoFollow:s,getAdvanced:r,getBreadcrumbsTitle:o,getCanonical:n,getIsLoading:a,getEditorContext:i,getPreferences:l}=e("yoast-seo/editor"),{isBreadcrumbsDisabled:c,isPrivateBlog:d}=l();return{noIndex:t(),noFollow:s(),advanced:r(),breadcrumbsTitle:o(),canonical:n(),isLoading:a(),editorContext:i(),isBreadcrumbsDisabled:c,isPrivateBlog:d}})),(0,o.withDispatch)((e=>{const{setNoIndex:t,setNoFollow:s,setAdvanced:r,setBreadcrumbsTitle:o,setCanonical:n,loadAdvancedSettingsData:a}=e("yoast-seo/editor");return{onNoIndexChange:t,onNoFollowChange:s,onAdvancedChange:r,onBreadcrumbsTitleChange:o,onCanonicalChange:n,onLoad:a}}))])(Yr),Gr=f().p`
	color: #606770;
	flex-shrink: 0;
	font-size: 12px;
	line-height: 16px;
	overflow: hidden;
	padding: 0;
	text-overflow: ellipsis;
	text-transform: uppercase;
	white-space: nowrap;
	margin: 0;
	position: ${e=>"landscape"===e.mode?"relative":"static"};
`,Zr=t=>{const{siteUrl:s}=t;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:"screen-reader-text"},s),(0,e.createElement)(Gr,{"aria-hidden":"true"},(0,e.createElement)("span",null,s)))};Zr.propTypes={siteUrl:g().string.isRequired};const Xr=Zr,Qr=window.yoast.socialMetadataForms,Jr=f().img`
	&& {
		max-width: ${e=>e.width}px;
		height: ${e=>e.height}px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		max-width: none;
	}
`,eo=f().img`
	&&{
		height: 100%;
		position: absolute;
		width: 100%;
		object-fit: cover;
	}
`,to=f().div`
	padding-bottom: ${e=>e.aspectRatio}%;
`,so=t=>{const{imageProps:s,width:r,height:o,imageMode:n}=t;return"landscape"===n?(0,e.createElement)(to,{aspectRatio:s.aspectRatio},(0,e.createElement)(eo,{src:s.src,alt:s.alt})):(0,e.createElement)(Jr,{src:s.src,alt:s.alt,width:r,height:o,imageProperties:s})};function ro(e,t,s){return"landscape"===s?{widthRatio:t.width/e.landscapeWidth,heightRatio:t.height/e.landscapeHeight}:"portrait"===s?{widthRatio:t.width/e.portraitWidth,heightRatio:t.height/e.portraitHeight}:{widthRatio:t.width/e.squareWidth,heightRatio:t.height/e.squareHeight}}function oo(e,t){return t.widthRatio<=t.heightRatio?{width:Math.round(e.width/t.widthRatio),height:Math.round(e.height/t.widthRatio)}:{width:Math.round(e.width/t.heightRatio),height:Math.round(e.height/t.heightRatio)}}async function no(e,t,s=!1){const r=await function(e){return new Promise(((t,s)=>{const r=new Image;r.onload=()=>{t({width:r.width,height:r.height})},r.onerror=s,r.src=e}))}(e);let o=s?"landscape":"square";"Facebook"===t&&(o=(0,Qr.determineFacebookImageMode)(r));const n=function(e){return"Twitter"===e?Qr.TWITTER_IMAGE_SIZES:Qr.FACEBOOK_IMAGE_SIZES}(t),a=function(e,t,s){return"square"===s&&t.width===t.height?{width:e.squareWidth,height:e.squareHeight}:oo(t,ro(e,t,s))}(n,r,o);return{mode:o,height:a.height,width:a.width}}async function ao(e,t,s=!1){try{return{imageProperties:await no(e,t,s),status:"loaded"}}catch(e){return{imageProperties:null,status:"errored"}}}so.propTypes={imageProps:g().shape({src:g().string.isRequired,alt:g().string.isRequired,aspectRatio:g().number.isRequired}).isRequired,width:g().number.isRequired,height:g().number.isRequired,imageMode:g().string},so.defaultProps={imageMode:"landscape"};const io=f().div`
	position: relative;
	${e=>"landscape"===e.mode?`max-width: ${e.dimensions.width}`:`min-width: ${e.dimensions.width}; height: ${e.dimensions.height}`};
	overflow: hidden;
	background-color: ${_e.colors.$color_white};
`,lo=f().div`
	box-sizing: border-box;
	max-width: ${Qr.FACEBOOK_IMAGE_SIZES.landscapeWidth}px;
	height: ${Qr.FACEBOOK_IMAGE_SIZES.landscapeHeight}px;
	background-color: ${_e.colors.$color_grey};
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class co extends e.Component{constructor(e){super(e),this.state={imageProperties:null,status:"loading"},this.socialMedium="Facebook",this.handleFacebookImage=this.handleFacebookImage.bind(this),this.setState=this.setState.bind(this)}async handleFacebookImage(){try{const e=await ao(this.props.src,this.socialMedium);this.setState(e),this.props.onImageLoaded(e.imageProperties.mode||"landscape")}catch(e){this.setState(e),this.props.onImageLoaded("landscape")}}componentDidUpdate(e){e.src!==this.props.src&&this.handleFacebookImage()}componentDidMount(){this.handleFacebookImage()}retrieveContainerDimensions(e){switch(e){case"square":return{height:Qr.FACEBOOK_IMAGE_SIZES.squareHeight+"px",width:Qr.FACEBOOK_IMAGE_SIZES.squareWidth+"px"};case"portrait":return{height:Qr.FACEBOOK_IMAGE_SIZES.portraitHeight+"px",width:Qr.FACEBOOK_IMAGE_SIZES.portraitWidth+"px"};case"landscape":return{height:Qr.FACEBOOK_IMAGE_SIZES.landscapeHeight+"px",width:Qr.FACEBOOK_IMAGE_SIZES.landscapeWidth+"px"}}}render(){const{imageProperties:t,status:s}=this.state;if("loading"===s||""===this.props.src||"errored"===s)return(0,e.createElement)(lo,{onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,i.__)("Select image","wordpress-seo"));const r=this.retrieveContainerDimensions(t.mode);return(0,e.createElement)(io,{mode:t.mode,dimensions:r,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave,onClick:this.props.onImageClick},(0,e.createElement)(so,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:Qr.FACEBOOK_IMAGE_SIZES.aspectRatio},width:t.width,height:t.height,imageMode:t.mode}))}}co.propTypes={src:g().string,alt:g().string,onImageLoaded:g().func,onImageClick:g().func,onMouseEnter:g().func,onMouseLeave:g().func},co.defaultProps={src:"",alt:"",onImageLoaded:p.noop,onImageClick:p.noop,onMouseEnter:p.noop,onMouseLeave:p.noop};const uo=co,po=f().span`
	line-height: ${20}px;
	min-height : ${20}px;
	color: #1d2129;
	font-weight: 600;
	overflow: hidden;
	font-size: 16px;
	margin: 3px 0 0;
	letter-spacing: normal;
	white-space: normal;
	flex-shrink: 0;
	cursor: pointer;
	display: -webkit-box;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;
`,mo=f().p`
	line-height: ${16}px;
	min-height : ${16}px;
	color: #606770;
	font-size: 14px;
	padding: 0;
	text-overflow: ellipsis;
	margin: 3px 0 0 0;
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: ${e=>e.lineCount};
	-webkit-box-orient: vertical;
	overflow: hidden;

	@media all and ( max-width: ${e=>e.maxWidth} ) {
		display: none;
	}
`,ho=e=>{switch(e){case"landscape":return"527px";case"square":case"portrait":return"369px";default:return"476px"}},go=f().div`
	box-sizing: border-box;
	display: flex;
	flex-direction: ${e=>"landscape"===e.mode?"column":"row"};
	background-color: #f2f3f5;
	max-width: 527px;
`,yo=f().div`
	box-sizing: border-box;
	background-color: #f2f3f5;
	margin: 0;
	padding: 10px 12px;
	position: relative;
	border-bottom: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-top: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border-right: ${e=>"landscape"===e.mode?"":"1px solid #dddfe2"};
	border: ${e=>"landscape"===e.mode?"1px solid #dddfe2":""};
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: ${e=>"landscape"===e.mode?"flex-start":"center"};
	font-size: 12px;
	overflow: hidden;
`;class fo extends e.Component{constructor(e){super(e),this.state={imageMode:null,maxLineCount:0,descriptionLineCount:0},this.facebookTitleRef=t().createRef(),this.onImageLoaded=this.onImageLoaded.bind(this),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}onImageLoaded(e){this.setState({imageMode:e})}getTitleLineCount(){return this.facebookTitleRef.current.offsetHeight/20}maybeSetMaxLineCount(){const{imageMode:e,maxLineCount:t}=this.state,s="landscape"===e?2:5;s!==t&&this.setState({maxLineCount:s})}maybeSetDescriptionLineCount(){const{descriptionLineCount:e,maxLineCount:t,imageMode:s}=this.state,r=this.getTitleLineCount();let o=t-r;"portrait"===s&&(o=5===r?0:4),o!==e&&this.setState({descriptionLineCount:o})}componentDidUpdate(){this.maybeSetMaxLineCount(),this.maybeSetDescriptionLineCount()}render(){const{imageMode:t,maxLineCount:s,descriptionLineCount:r}=this.state;return(0,e.createElement)(go,{id:"facebookPreview",mode:t},(0,e.createElement)(uo,{src:this.props.imageUrl||this.props.imageFallbackUrl,alt:this.props.alt,onImageLoaded:this.onImageLoaded,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,e.createElement)(yo,{mode:t},(0,e.createElement)(Xr,{siteUrl:this.props.siteUrl,mode:t}),(0,e.createElement)(po,{ref:this.facebookTitleRef,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle,lineCount:s},this.props.title),r>0&&(0,e.createElement)(mo,{maxWidth:ho(t),onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription,lineCount:r},this.props.description)))}}fo.propTypes={siteUrl:g().string.isRequired,title:g().string.isRequired,description:g().string,imageUrl:g().string,imageFallbackUrl:g().string,alt:g().string,onSelect:g().func,onImageClick:g().func,onMouseHover:g().func},fo.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{}};const bo=fo,wo=f().div`
	text-transform: lowercase;
	color: rgb(83, 100, 113);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	fill: currentcolor;
	display: flex;
	flex-direction: row;
	align-items: flex-end;
`,Eo=t=>(0,e.createElement)(wo,null,(0,e.createElement)("span",null,t.siteUrl));Eo.propTypes={siteUrl:g().string.isRequired};const ko=Eo,vo=(e,t=!0)=>e?`\n\t\t\tmax-width: ${Qr.TWITTER_IMAGE_SIZES.landscapeWidth}px;\n\t\t\t${t?"border-bottom: 1px solid #E1E8ED;":""}\n\t\t\tborder-radius: 14px 14px 0 0;\n\t\t\t`:`\n\t\twidth: ${Qr.TWITTER_IMAGE_SIZES.squareWidth}px;\n\t\t${t?"border-right: 1px solid #E1E8ED;":""}\n\t\tborder-radius: 14px 0 0 14px;\n\t\t`,_o=f().div`
	position: relative;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #e1e8ed;
	flex-shrink: 0;
	${e=>vo(e.isLarge)}
`,xo=f().div`
	display: flex;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	max-width: 100%;
	margin: 0;
	padding: 1em;
	text-align: center;
	font-size: 1rem;
	${e=>vo(e.isLarge,!1)}
`,To=f()(xo)`
	${e=>e.isLarge&&`height: ${Qr.TWITTER_IMAGE_SIZES.landscapeHeight}px;`}
	border-top-left-radius: 14px;
	${e=>e.isLarge?"border-top-right-radius":"border-bottom-left-radius"}: 14px;
	border-style: dashed;
	border-width: 1px;
	// We're not using standard colors to increase contrast for accessibility.
	color: #006DAC;
	// We're not using standard colors to increase contrast for accessibility.
	background-color: #f1f1f1;
	text-decoration: underline;
	font-size: 14px;
	cursor: pointer;
`;class So extends t().Component{constructor(e){super(e),this.state={status:"loading"},this.socialMedium="Twitter",this.handleTwitterImage=this.handleTwitterImage.bind(this),this.setState=this.setState.bind(this)}async handleTwitterImage(){if(null===this.props.src)return;const e=await ao(this.props.src,this.socialMedium,this.props.isLarge);this.setState(e)}componentDidUpdate(e){e.src!==this.props.src&&this.handleTwitterImage()}componentDidMount(){this.handleTwitterImage()}render(){const{status:t,imageProperties:s}=this.state;return"loading"===t||""===this.props.src||"errored"===t?(0,e.createElement)(To,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,i.__)("Select image","wordpress-seo")):(0,e.createElement)(_o,{isLarge:this.props.isLarge,onClick:this.props.onImageClick,onMouseEnter:this.props.onMouseEnter,onMouseLeave:this.props.onMouseLeave},(0,e.createElement)(so,{imageProps:{src:this.props.src,alt:this.props.alt,aspectRatio:Qr.TWITTER_IMAGE_SIZES.aspectRatio},width:s.width,height:s.height,imageMode:s.mode}))}}So.propTypes={isLarge:g().bool.isRequired,src:g().string,alt:g().string,onImageClick:g().func,onMouseEnter:g().func,onMouseLeave:g().func},So.defaultProps={src:"",alt:"",onMouseEnter:p.noop,onImageClick:p.noop,onMouseLeave:p.noop};const Ro=f().div`
	display: flex;
	flex-direction: column;
	padding: 12px;
	justify-content: center;
	margin: 0;
	box-sizing: border-box;
	flex: auto;
	min-width: 0px;
	gap:2px;
	> * {
		line-height:20px;
		min-height:20px;
		font-size:15px;
    }
`,Co=t=>(0,e.createElement)(Ro,null,t.children);Co.propTypes={children:g().array.isRequired};const Io=Co,Lo=f().p`
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(15, 20, 25);
	cursor: pointer;
`,Po=f().p`
	max-height: 55px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	color: rgb(83, 100, 113);
	display: -webkit-box;
	cursor: pointer;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;

	@media all and ( max-width: ${Qr.TWITTER_IMAGE_SIZES.landscapeWidth}px ) {
		display: none;
	}
`,Ao=f().div`
	font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 400;
	line-height: 20px;
	max-width: 507px;
	border: 1px solid #E1E8ED;
	box-sizing: border-box;
	border-radius: 14px;
	color: #292F33;
	background: #FFFFFF;
	text-overflow: ellipsis;
	display: flex;

	&:hover {
		background: #f5f8fa;
		border: 1px solid rgba(136,153,166,.5);
	}
`,Fo=f()(Ao)`
	flex-direction: column;
	max-height: 370px;
`,Oo=f()(Ao)`
	flex-direction: row;
	height: 125px;
`;class No extends e.Component{constructor(e){super(e),this.onImageEnter=this.props.onMouseHover.bind(this,"image"),this.onTitleEnter=this.props.onMouseHover.bind(this,"title"),this.onDescriptionEnter=this.props.onMouseHover.bind(this,"description"),this.onLeave=this.props.onMouseHover.bind(this,""),this.onSelectTitle=this.props.onSelect.bind(this,"title"),this.onSelectDescription=this.props.onSelect.bind(this,"description")}render(){const{isLarge:t,imageUrl:s,imageFallbackUrl:r,alt:o,title:n,description:a,siteUrl:i}=this.props,l=t?Fo:Oo;return(0,e.createElement)(l,{id:"twitterPreview"},(0,e.createElement)(So,{src:s||r,alt:o,isLarge:t,onImageClick:this.props.onImageClick,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave}),(0,e.createElement)(Io,null,(0,e.createElement)(ko,{siteUrl:i}),(0,e.createElement)(Lo,{onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onClick:this.onSelectTitle},n),(0,e.createElement)(Po,{onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onClick:this.onSelectDescription},a)))}}No.propTypes={siteUrl:g().string.isRequired,title:g().string.isRequired,description:g().string,isLarge:g().bool,imageUrl:g().string,imageFallbackUrl:g().string,alt:g().string,onSelect:g().func,onImageClick:g().func,onMouseHover:g().func},No.defaultProps={description:"",alt:"",imageUrl:"",imageFallbackUrl:"",onSelect:()=>{},onImageClick:()=>{},onMouseHover:()=>{},isLarge:!0};const Mo=No,qo=window.yoast.replacementVariableEditor;class Do extends e.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.SocialPreview="Social"===e.socialMediumName?bo:Mo,this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:s,onTitleChange:r,onSelectImageClick:o,onRemoveImageClick:n,socialMediumName:a,imageWarnings:i,siteUrl:l,description:c,descriptionInputPlaceholder:d,descriptionPreviewFallback:u,imageUrl:p,imageFallbackUrl:m,alt:h,title:g,titleInputPlaceholder:y,titlePreviewFallback:f,replacementVariables:b,recommendedReplacementVariables:w,applyReplacementVariables:E,onReplacementVariableSearchChange:k,isPremium:v,isLarge:_,socialPreviewLabel:x,idSuffix:T,activeMetaTabId:S}=this.props,R=E({title:g||f,description:c||u});return(0,e.createElement)(t().Fragment,null,x&&(0,e.createElement)(ve.SimulatedLabel,null,x),(0,e.createElement)(this.SocialPreview,{onMouseHover:this.setHoveredField,onSelect:this.setActiveField,onImageClick:o,siteUrl:l,title:R.title,description:R.description,imageUrl:p,imageFallbackUrl:m,alt:h,isLarge:_,activeMetaTabId:S}),(0,e.createElement)(Qr.SocialMetadataPreviewForm,{onDescriptionChange:s,socialMediumName:a,title:g,titleInputPlaceholder:y,onRemoveImageClick:n,imageSelected:!!p,imageUrl:p,imageFallbackUrl:m,onTitleChange:r,onSelectImageClick:o,description:c,descriptionInputPlaceholder:d,imageWarnings:i,replacementVariables:b,recommendedReplacementVariables:w,onReplacementVariableSearchChange:k,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:v,setEditorRef:this.setEditorRef,idSuffix:T}))}}Do.propTypes={title:g().string.isRequired,onTitleChange:g().func.isRequired,description:g().string.isRequired,onDescriptionChange:g().func.isRequired,imageUrl:g().string.isRequired,imageFallbackUrl:g().string.isRequired,onSelectImageClick:g().func.isRequired,onRemoveImageClick:g().func.isRequired,socialMediumName:g().string.isRequired,alt:g().string,isPremium:g().bool,imageWarnings:g().array,isLarge:g().bool,siteUrl:g().string,descriptionInputPlaceholder:g().string,titleInputPlaceholder:g().string,descriptionPreviewFallback:g().string,titlePreviewFallback:g().string,replacementVariables:qo.replacementVariablesShape,recommendedReplacementVariables:qo.recommendedReplacementVariablesShape,applyReplacementVariables:g().func,onReplacementVariableSearchChange:g().func,socialPreviewLabel:g().string,idSuffix:g().string,activeMetaTabId:g().string},Do.defaultProps={imageWarnings:[],recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,isLarge:!0,siteUrl:"",descriptionInputPlaceholder:"",titleInputPlaceholder:"",descriptionPreviewFallback:"",titlePreviewFallback:"",alt:"",applyReplacementVariables:e=>e,onReplacementVariableSearchChange:null,socialPreviewLabel:"",idSuffix:"",activeMetaTabId:""};const $o={},Uo=(e,t,{log:s=console.warn}={})=>{$o[e]||($o[e]=!0,s(t))},Bo=(e,t=p.noop)=>{const s={};for(const r in e)Object.hasOwn(e,r)&&Object.defineProperty(s,r,{set:s=>{e[r]=s,t("set",r,s)},get:()=>(t("get",r),e[r])});return s};Bo({squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},((e,t)=>Uo(`@yoast/social-metadata-previews/TWITTER_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "TWITTER_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`))),Bo({squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}},((e,t)=>Uo(`@yoast/social-metadata-previews/FACEBOOK_IMAGE_SIZES/${e}/${t}`,`[@yoast/social-metadata-previews] "FACEBOOK_IMAGE_SIZES.${t}" is deprecated and will be removed in the future, please use this from @yoast/social-metadata-forms instead.`)));const Wo=f().div`
	max-width: calc(527px + 1.5rem);
`,jo=t=>{const s="X"===t.socialMediumName?(0,i.__)("X share preview","wordpress-seo"):(0,i.__)("Social share preview","wordpress-seo"),{locationContext:r}=(0,T.useRootContext)();return(0,e.createElement)(T.Root,null,(0,e.createElement)(Wo,null,(0,e.createElement)(T.FeatureUpsell,{shouldUpsell:!0,variant:"card",cardLink:(0,hr.addQueryArgs)(wpseoAdminL10n["shortlinks.upsell.social_preview."+t.socialMediumName.toLowerCase()],{context:r}),cardText:(0,i.sprintf)(/* translators: %1$s expands to Yoast SEO Premium. */
(0,i.__)("Unlock with %1$s","wordpress-seo"),"Yoast SEO Premium"),"data-action":"load-nfd-ctb","data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2"},(0,e.createElement)("div",{className:"yst-grayscale yst-opacity-50"},(0,e.createElement)(T.Label,null,s),(0,e.createElement)(bo,{title:"",description:"",siteUrl:"",imageUrl:"",imageFallbackUrl:"",alt:"",onSelect:p.noop,onImageClick:p.noop,onMouseHover:p.noop})))))};jo.propTypes={socialMediumName:g().oneOf(["Social","Twitter","X"]).isRequired};const Ho=jo;class Ko extends a.Component{constructor(e){super(e),this.state={activeField:"",hoveredField:""},this.setHoveredField=this.setHoveredField.bind(this),this.setActiveField=this.setActiveField.bind(this),this.setEditorRef=this.setEditorRef.bind(this),this.setEditorFocus=this.setEditorFocus.bind(this)}setHoveredField(e){e!==this.state.hoveredField&&this.setState({hoveredField:e})}setActiveField(e){e!==this.state.activeField&&this.setState({activeField:e},(()=>this.setEditorFocus(e)))}setEditorFocus(e){switch(e){case"title":this.titleEditorRef.focus();break;case"description":this.descriptionEditorRef.focus()}}setEditorRef(e,t){switch(e){case"title":this.titleEditorRef=t;break;case"description":this.descriptionEditorRef=t}}render(){const{onDescriptionChange:t,onTitleChange:s,onSelectImageClick:r,onRemoveImageClick:o,socialMediumName:n,imageWarnings:i,description:l,descriptionInputPlaceholder:c,imageUrl:d,imageFallbackUrl:u,alt:p,title:m,titleInputPlaceholder:h,replacementVariables:g,recommendedReplacementVariables:y,onReplacementVariableSearchChange:f,isPremium:b,location:w}=this.props;return(0,e.createElement)(a.Fragment,null,(0,e.createElement)(Ho,{socialMediumName:n}),(0,e.createElement)(Qr.SocialMetadataPreviewForm,{onDescriptionChange:t,socialMediumName:n,title:m,titleInputPlaceholder:h,onRemoveImageClick:o,imageSelected:!!d,imageUrl:d,imageFallbackUrl:u,imageAltText:p,onTitleChange:s,onSelectImageClick:r,description:l,descriptionInputPlaceholder:c,imageWarnings:i,replacementVariables:g,recommendedReplacementVariables:y,onReplacementVariableSearchChange:f,onMouseHover:this.setHoveredField,hoveredField:this.state.hoveredField,onSelect:this.setActiveField,activeField:this.state.activeField,isPremium:b,setEditorRef:this.setEditorRef,idSuffix:w}))}}Ko.propTypes={title:g().string.isRequired,onTitleChange:g().func.isRequired,description:g().string.isRequired,onDescriptionChange:g().func.isRequired,imageUrl:g().string.isRequired,imageFallbackUrl:g().string,onSelectImageClick:g().func.isRequired,onRemoveImageClick:g().func.isRequired,socialMediumName:g().string.isRequired,isPremium:g().bool,imageWarnings:g().array,descriptionInputPlaceholder:g().string,titleInputPlaceholder:g().string,replacementVariables:qo.replacementVariablesShape,recommendedReplacementVariables:qo.recommendedReplacementVariablesShape,onReplacementVariableSearchChange:g().func,location:g().string,alt:g().string},Ko.defaultProps={imageWarnings:[],imageFallbackUrl:"",recommendedReplacementVariables:[],replacementVariables:[],isPremium:!1,descriptionInputPlaceholder:"",titleInputPlaceholder:"",onReplacementVariableSearchChange:null,location:"",alt:""};const zo=Ko,Yo=(e,t,s)=>{const[r,o]=(0,a.useState)(!1),n=(0,i.sprintf)(
/* Translators: %1$s expands to the jpg format, %2$s expands to the png format,
  %3$s expands to the webp format, %4$s expands to the gif format. */
(0,i.__)("No image was found that we can automatically set as your social image. Please use %1$s, %2$s, %3$s or %4$s formats to ensure it displays correctly on social media.","wordpress-seo"),"JPG","PNG","WEBP","GIF");return(0,a.useEffect)((()=>{o(""===t&&e.toLowerCase().endsWith(".avif"))}),[e,t]),r?[n]:s},Vo=t=>{const[s,r]=(0,a.useState)(""),o=Yo(t.imageFallbackUrl,t.imageUrl,t.imageWarnings),n=(0,a.useCallback)((e=>{r(e.detail.metaTabId)}),[r]);(0,a.useEffect)((()=>(setTimeout(t.onLoad),window.addEventListener("YoastSEO:metaTabChange",n),()=>{window.removeEventListener("YoastSEO:metaTabChange",n)})),[]);const i={...t,activeMetaTabId:s,imageWarnings:o};return t.isPremium?(0,e.createElement)(k.Slot,{name:`YoastFacebookPremium${t.location.charAt(0).toUpperCase()+t.location.slice(1)}`,fillProps:i}):(0,e.createElement)(zo,{...i})};Vo.propTypes={isPremium:g().bool.isRequired,onLoad:g().func.isRequired,location:g().string.isRequired,imageFallbackUrl:g().string,imageUrl:g().string,imageWarnings:g().array},Vo.defaultProps={imageFallbackUrl:"",imageUrl:"",imageWarnings:[]};const Go=Vo;function Zo(e){(function(e){const t=window.wp.media();return t.on("select",(()=>{const s=t.state().get("selection").first();var r;e({type:(r=s.attributes).subtype,width:r.width,height:r.height,url:r.url,id:r.id,sizes:r.sizes,alt:r.alt||r.title||r.name})})),t})(e).open()}const Xo=()=>{Zo((e=>(0,o.dispatch)("yoast-seo/editor").setFacebookPreviewImage((e=>{const{width:t,height:s}=e,r=(0,Qr.determineFacebookImageMode)({width:t,height:s}),o=Qr.FACEBOOK_IMAGE_SIZES[r+"Width"],n=Qr.FACEBOOK_IMAGE_SIZES[r+"Height"],a=Object.values(e.sizes).find((e=>e.width>=o&&e.height>=n));return{url:a?a.url:e.url,id:e.id,warnings:(0,De.validateFacebookImage)(e),alt:e.alt||""}})(e))))},Qo=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getFacebookDescription:t,getDescription:s,getFacebookTitle:r,getSeoTitle:o,getFacebookImageUrl:n,getImageFallback:a,getFacebookWarnings:i,getRecommendedReplaceVars:l,getReplaceVars:c,getSiteUrl:d,getSeoTitleTemplate:u,getSeoTitleTemplateNoFallback:p,getSocialTitleTemplate:h,getSeoDescriptionTemplate:g,getSocialDescriptionTemplate:y,getReplacedExcerpt:f,getFacebookAltText:b}=e("yoast-seo/editor");return{imageUrl:n(),imageFallbackUrl:a(),recommendedReplacementVariables:l(),replacementVariables:c(),description:t(),descriptionPreviewFallback:y()||s()||g()||f()||"",title:r(),titlePreviewFallback:h()||o()||p()||u()||"",imageWarnings:i(),siteUrl:d(),isPremium:!!m().isPremium,titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"Social",alt:b()}})),(0,o.withDispatch)(((e,t,{select:s})=>{const{setFacebookPreviewTitle:r,setFacebookPreviewDescription:o,clearFacebookPreviewImage:n,loadFacebookPreviewData:a,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:Xo,onRemoveImageClick:n,onDescriptionChange:o,onTitleChange:r,onLoad:a,onReplacementVariableSearchChange:Js(l,i)}})),$s()])(Go),Jo=t=>{const s=Yo(t.imageFallbackUrl,t.imageUrl,t.imageWarnings);(0,a.useEffect)((()=>{setTimeout(t.onLoad)}),[]);const r={...t,imageWarnings:s};return t.isPremium?(0,e.createElement)(k.Slot,{name:`YoastTwitterPremium${t.location.charAt(0).toUpperCase()+t.location.slice(1)}`,fillProps:r}):(0,e.createElement)(zo,{...r})};Jo.propTypes={isPremium:g().bool.isRequired,onLoad:g().func.isRequired,location:g().string.isRequired,imageFallbackUrl:g().string,imageUrl:g().string,imageWarnings:g().array},Jo.defaultProps={imageFallbackUrl:"",imageUrl:"",imageWarnings:[]};const en=Jo,tn=()=>{Zo((e=>(0,o.dispatch)("yoast-seo/editor").setTwitterPreviewImage((e=>{const t="summary"!==(0,p.get)(window,"wpseoScriptData.metabox.twitterCardType")?"landscape":"square",s=Qr.TWITTER_IMAGE_SIZES[t+"Width"],r=Qr.TWITTER_IMAGE_SIZES[t+"Height"],o=Object.values(e.sizes).find((e=>e.width>=s&&e.height>=r));return{url:o?o.url:e.url,id:e.id,warnings:(0,De.validateTwitterImage)(e),alt:e.alt||""}})(e))))},sn=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getTwitterDescription:t,getTwitterTitle:s,getTwitterImageUrl:r,getFacebookImageUrl:o,getFacebookTitle:n,getFacebookDescription:a,getDescription:i,getSeoTitle:l,getTwitterWarnings:c,getTwitterImageType:d,getImageFallback:u,getRecommendedReplaceVars:p,getReplaceVars:h,getSiteUrl:g,getSeoTitleTemplate:y,getSeoTitleTemplateNoFallback:f,getSocialTitleTemplate:b,getSeoDescriptionTemplate:w,getSocialDescriptionTemplate:E,getReplacedExcerpt:k,getTwitterAltText:v}=e("yoast-seo/editor");return{imageUrl:r(),imageFallbackUrl:o()||u(),recommendedReplacementVariables:p(),replacementVariables:h(),description:t(),descriptionPreviewFallback:E()||a()||i()||w()||k()||"",title:s(),titlePreviewFallback:b()||n()||l()||f()||y()||"",imageWarnings:c(),siteUrl:g(),isPremium:!!m().isPremium,isLarge:"summary"!==d(),titleInputPlaceholder:"",descriptionInputPlaceholder:"",socialMediumName:"X",alt:v()}})),(0,o.withDispatch)(((e,t,{select:s})=>{const{setTwitterPreviewTitle:r,setTwitterPreviewDescription:o,clearTwitterPreviewImage:n,loadTwitterPreviewData:a,findCustomFields:i}=e("yoast-seo/editor"),l=s("yoast-seo/editor").getPostId();return{onSelectImageClick:tn,onRemoveImageClick:n,onDescriptionChange:o,onTitleChange:r,onLoad:a,onReplacementVariableSearchChange:Js(l,i)}})),$s()])(en),rn=f().legend`
	margin: 16px 0;
	padding: 0;
	color: ${_e.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,on=f().legend`
	margin: 0 0 16px;
	padding: 0;
	color: ${_e.colors.$color_headings};
	font-size: 12px;
	font-weight: 300;
`,nn=f().div`
	padding: 16px;
`,an=({useOpenGraphData:t,useTwitterData:s})=>(0,e.createElement)(a.Fragment,null,s&&t&&(0,e.createElement)(a.Fragment,null,(0,e.createElement)(ir,{hasSeparator:!1
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,title:(0,i.__)("Social media appearance","wordpress-seo"),initialIsOpen:!0},(0,e.createElement)(on,null,(0,i.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(Qo,null),(0,e.createElement)(rn,null,(0,i.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),(0,e.createElement)(ir,{title:(0,i.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,e.createElement)(sn,null))),t&&!s&&(0,e.createElement)(nn,null,(0,e.createElement)(on,null,(0,i.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(Qo,null)),!t&&s&&(0,e.createElement)(nn,null,(0,e.createElement)(on,null,(0,i.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,e.createElement)(sn,null)));an.propTypes={useOpenGraphData:g().bool.isRequired,useTwitterData:g().bool.isRequired};const ln=an,cn=(0,o.withSelect)((e=>{const{getPreferences:t}=e("yoast-seo/editor"),{useOpenGraphData:s,useTwitterData:r}=t();return{useOpenGraphData:s,useTwitterData:r}}))(ln);function dn({target:t}){return(0,e.createElement)(Se,{target:t},(0,e.createElement)(cn,null))}dn.propTypes={target:g().string.isRequired};const un=(0,De.makeOutboundLink)(),pn=f().div`
	padding: 16px;
`,mn="yoast-seo/editor";function hn({location:t,show:s}){return s?(0,e.createElement)(ve.Alert,{type:"info"},(0,i.sprintf)(/* translators: %s Expands to "Yoast News SEO" */
(0,i.__)("Are you working on a news article? %s helps you optimize your site for Google News.","wordpress-seo"),"Yoast News SEO")+" ",(0,e.createElement)(un,{href:window.wpseoAdminL10n[`shortlinks.upsell.${t}.news`]},(0,i.sprintf)(/* translators: %s: Expands to "Yoast News SEO". */
(0,i.__)("Buy %s now!","wordpress-seo"),"Yoast News SEO"))):null}hn.propTypes={show:g().bool.isRequired,location:g().string.isRequired};const gn=(e,t,s)=>{const r=(0,o.useSelect)((e=>e(mn).getIsProduct()),[]),n=(0,o.useSelect)((e=>e(mn).getIsWooSeoActive()),[]),a=r&&n?{name:(0,i.__)("Item Page","wordpress-seo"),value:"ItemPage"}:e.find((e=>e.value===t));return[{name:(0,i.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s expands to the current site wide default. */
(0,i.__)("Default for %1$s (%2$s)","wordpress-seo"),s,a?a.name:""),value:""},...e]},yn=e=>(0,i.sprintf)(/* translators: %1$s expands to the plural name of the current post type, %2$s and %3$s expand to a link to the Settings page */
(0,i.__)("You can change the default type for %1$s under Content types in the %2$sSettings%3$s.","wordpress-seo"),e,"{{link}}","{{/link}}");g().string.isRequired,g().string.isRequired,g().string.isRequired;const fn=t=>{const s=gn(t.pageTypeOptions,t.defaultPageType,t.postTypeName),r=gn(t.articleTypeOptions,t.defaultArticleType,t.postTypeName),n=(0,o.useSelect)((e=>e(mn).selectLink("https://yoa.st/product-schema-metabox")),[]),l=(0,o.useSelect)((e=>e(mn).getIsWooSeoUpsell()),[]),[c,d]=(0,a.useState)(t.schemaArticleTypeSelected),u=(0,i.__)("Want your products stand out in search results with rich results like price, reviews and more?","wordpress-seo"),p=(0,o.useSelect)((e=>e(mn).getIsProduct()),[]),m=(0,o.useSelect)((e=>e(mn).getIsWooSeoActive()),[]),h=(0,o.useSelect)((e=>e(mn).selectAdminLink("?page=wpseo_page_settings")),[]),g=p&&m,y=(0,a.useCallback)(((e,t)=>{d(t)}),[c]);return(0,a.useEffect)((()=>{y(null,t.schemaArticleTypeSelected)}),[t.schemaArticleTypeSelected]),(0,e.createElement)(a.Fragment,null,(0,e.createElement)(ve.FieldGroup,{label:(0,i.__)("What type of page or content is this?","wordpress-seo"),linkTo:t.additionalHelpTextLink
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about page or content types","wordpress-seo")}),l&&(0,e.createElement)(tr,{link:n,text:u}),(0,e.createElement)(ve.Select,{id:(0,De.join)(["yoast-schema-page-type",t.location]),options:s,label:(0,i.__)("Page type","wordpress-seo"),onChange:t.schemaPageTypeChange,selected:g?"ItemPage":t.schemaPageTypeSelected,disabled:g}),t.showArticleTypeInput&&(0,e.createElement)(ve.Select,{id:(0,De.join)(["yoast-schema-article-type",t.location]),options:r,label:(0,i.__)("Article type","wordpress-seo"),onChange:t.schemaArticleTypeChange,selected:t.schemaArticleTypeSelected,onOptionFocus:y}),(0,e.createElement)(hn,{location:t.location,show:!t.isNewsEnabled&&(w=c,E=t.defaultArticleType,"NewsArticle"===w||""===w&&"NewsArticle"===E)}),t.displayFooter&&!g&&(0,e.createElement)("p",null,(f=t.postTypeName,b=h,(0,Qe.Z)({mixedString:yn(f),components:{link:(0,e.createElement)("a",{href:b,target:"_blank",rel:"noreferrer"})}}))),g&&(0,e.createElement)("p",null,(0,i.sprintf)(/* translators: %1$s expands to Yoast WooCommerce SEO. */
(0,i.__)("You have %1$s activated on your site, automatically setting the Page type for your products to 'Item Page'. As a result, the Page type selection is disabled.","wordpress-seo"),"Yoast WooCommerce SEO")));var f,b,w,E},bn=g().arrayOf(g().shape({name:g().string,value:g().string}));fn.propTypes={schemaPageTypeChange:g().func,schemaPageTypeSelected:g().string,pageTypeOptions:bn.isRequired,schemaArticleTypeChange:g().func,schemaArticleTypeSelected:g().string,articleTypeOptions:bn.isRequired,showArticleTypeInput:g().bool.isRequired,additionalHelpTextLink:g().string.isRequired,helpTextLink:g().string.isRequired,helpTextTitle:g().string.isRequired,helpTextDescription:g().string.isRequired,postTypeName:g().string.isRequired,displayFooter:g().bool,defaultPageType:g().string.isRequired,defaultArticleType:g().string.isRequired,location:g().string.isRequired,isNewsEnabled:g().bool},fn.defaultProps={schemaPageTypeChange:()=>{},schemaPageTypeSelected:null,schemaArticleTypeChange:()=>{},schemaArticleTypeSelected:null,displayFooter:!1,isNewsEnabled:!1};const wn=t=>t.isMetabox?(0,a.createPortal)((0,e.createElement)(pn,null,(0,e.createElement)(fn,{...t})),document.getElementById("wpseo-meta-section-schema")):(0,e.createElement)(fn,{...t});wn.propTypes={showArticleTypeInput:g().bool,articleTypeLabel:g().string,additionalHelpTextLink:g().string,pageTypeLabel:g().string.isRequired,helpTextLink:g().string.isRequired,helpTextTitle:g().string.isRequired,helpTextDescription:g().string.isRequired,isMetabox:g().bool.isRequired,postTypeName:g().string.isRequired,displayFooter:g().bool,loadSchemaArticleData:g().func.isRequired,loadSchemaPageData:g().func.isRequired,location:g().string.isRequired},wn.defaultProps={showArticleTypeInput:!1,articleTypeLabel:"",additionalHelpTextLink:"",displayFooter:!1};const En=wn;class kn{static get articleTypeInput(){return document.getElementById("yoast_wpseo_schema_article_type")}static get defaultArticleType(){return kn.articleTypeInput.getAttribute("data-default")}static get articleType(){return kn.articleTypeInput.value}static set articleType(e){kn.articleTypeInput.value=e}static get pageTypeInput(){return document.getElementById("yoast_wpseo_schema_page_type")}static get defaultPageType(){return kn.pageTypeInput.getAttribute("data-default")}static get pageType(){return kn.pageTypeInput.value}static set pageType(e){kn.pageTypeInput.value=e}}const vn=t=>{const s=null!==kn.articleTypeInput;(0,a.useEffect)((()=>{t.loadSchemaPageData(),s&&t.loadSchemaArticleData()}),[]);const{pageTypeOptions:r,articleTypeOptions:o}=window.wpseoScriptData.metabox.schema,n={articleTypeLabel:(0,i.__)("Article type","wordpress-seo"),pageTypeLabel:(0,i.__)("Page type","wordpress-seo"),postTypeName:window.wpseoAdminL10n.postTypeNamePlural,helpTextTitle:(0,i.__)("Yoast SEO automatically describes your pages using schema.org","wordpress-seo"),helpTextDescription:(0,i.__)("This helps search engines understand your website and your content. You can change some of your settings for this page below.","wordpress-seo"),showArticleTypeInput:s,pageTypeOptions:r,articleTypeOptions:o},l={...t,...n,...(c=t.location,"metabox"===c?{helpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.metabox.schema.page_type"],isMetabox:!0}:{helpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.explanation"],additionalHelpTextLink:wpseoAdminL10n["shortlinks.sidebar.schema.page_type"],isMetabox:!1})};var c;return(0,e.createElement)(En,{...l})};vn.propTypes={displayFooter:g().bool.isRequired,schemaPageTypeSelected:g().string.isRequired,schemaArticleTypeSelected:g().string.isRequired,defaultArticleType:g().string.isRequired,defaultPageType:g().string.isRequired,loadSchemaPageData:g().func.isRequired,loadSchemaArticleData:g().func.isRequired,schemaPageTypeChange:g().func.isRequired,schemaArticleTypeChange:g().func.isRequired,location:g().string.isRequired};const xn=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getPreferences:t,getPageType:s,getDefaultPageType:r,getArticleType:o,getDefaultArticleType:n}=e("yoast-seo/editor"),{displaySchemaSettingsFooter:a,isNewsEnabled:i}=t();return{displayFooter:a,isNewsEnabled:i,schemaPageTypeSelected:s(),schemaArticleTypeSelected:o(),defaultArticleType:n(),defaultPageType:r()}})),(0,o.withDispatch)((e=>{const{setPageType:t,setArticleType:s,getSchemaPageData:r,getSchemaArticleData:o}=e("yoast-seo/editor");return{loadSchemaPageData:r,loadSchemaArticleData:o,schemaPageTypeChange:t,schemaArticleTypeChange:s}})),$s()])(vn),Tn=window.yoast.relatedKeyphraseSuggestions;function Sn(e){const{requestLimitReached:t,isSuccess:s,response:r,requestHasData:o,relatedKeyphrases:n}=e;return t?"requestLimitReached":!s&&function(e){return"invalid_json"===(null==e?void 0:e.code)||"fetch_error"===(null==e?void 0:e.code)||!(0,p.isEmpty)(e)&&"error"in e}(r)?"requestFailed":o?function(e){return e&&e.length>=4}(n)?"maxRelatedKeyphrases":void 0:"requestEmpty"}function Rn(t){var s,r;const{keyphrase:o="",relatedKeyphrases:n=[],renderAction:i=null,requestLimitReached:l=!1,countryCode:c="us",setCountry:d,newRequest:u,response:p={},isRtl:m=!1,userLocale:h="en_US",isPending:g=!1,isPremium:y=!1,semrushUpsellLink:f="",premiumUpsellLink:b=""}=t,[w,E]=(0,a.useState)(c),k=(0,a.useCallback)((async()=>{u(c,o),E(c)}),[c,o,u]);return(0,e.createElement)(T.Root,{context:{isRtl:m}},!l&&!y&&(0,e.createElement)(Tn.PremiumUpsell,{url:b,className:"yst-mb-4"}),!l&&(0,e.createElement)(Tn.CountrySelector,{countryCode:c,activeCountryCode:w,onChange:d,onClick:k,className:"yst-mb-4",userLocale:h.split("_")[0]}),!g&&(0,e.createElement)(Tn.UserMessage,{variant:Sn(t),upsellLink:f}),(0,e.createElement)(Tn.KeyphrasesTable,{relatedKeyphrases:n,columnNames:null==p||null===(s=p.results)||void 0===s?void 0:s.columnNames,data:null==p||null===(r=p.results)||void 0===r?void 0:r.rows,isPending:g,renderButton:i,className:"yst-mt-4"}))}Rn.propTypes={keyphrase:g().string,relatedKeyphrases:g().array,renderAction:g().func,requestLimitReached:g().bool,countryCode:g().string.isRequired,setCountry:g().func.isRequired,newRequest:g().func.isRequired,response:g().object,isRtl:g().bool,userLocale:g().string,isPending:g().bool,isPremium:g().bool,semrushUpsellLink:g().string,premiumUpsellLink:g().string};const Cn=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getFocusKeyphrase:t,getSEMrushSelectedCountry:s,getSEMrushRequestLimitReached:r,getSEMrushRequestResponse:o,getSEMrushRequestIsSuccess:n,getSEMrushIsRequestPending:a,getSEMrushRequestHasData:i,getSEMrushRequestKeyphrase:l,getPreference:c,getIsPremium:d,selectLinkParams:u}=e("yoast-seo/editor");return{keyphrase:t(),countryCode:s(),requestLimitReached:r(),response:o(),isSuccess:n(),isPending:a(),requestHasData:i(),lastRequestKeyphrase:l(),isRtl:c("isRtl",!1),userLocale:c("userLocale","en_US"),isPremium:d(),semrushUpsellLink:(0,hr.addQueryArgs)("https://yoa.st/semrush-prices",u()),premiumUpsellLink:(0,hr.addQueryArgs)("https://yoa.st/413",u())}})),(0,o.withDispatch)((e=>{const{setSEMrushChangeCountry:t,setSEMrushNewRequest:s}=e("yoast-seo/editor");return{setCountry:e=>{t(e)},newRequest:(e,t)=>{s(e,t)}}}))])(Rn),In=(0,i.__)("Check your text on even more SEO criteria and get an enhanced keyphrase analysis, making it easier to optimize your content.","wordpress-seo"),Ln=t=>{const{locationContext:s}=(0,d.useRootContext)(),r=(0,hr.addQueryArgs)(wpseoAdminL10n[t.buyLink],{context:s});return(0,e.createElement)(qr,{title:(0,i.__)("Get more help with writing content that ranks","wordpress-seo"),description:t.description,benefitsTitle:(0,i.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,i.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:he(),upsellButtonText:(0,i.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,i.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:r,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,i.__)("1 year of premium support and updates included!","wordpress-seo")})};Ln.propTypes={buyLink:g().string.isRequired,description:g().string},Ln.defaultProps={description:In};const Pn=Ln,An=({location:t})=>{const[s,r]=(0,a.useState)(!1),o=(0,a.useCallback)((()=>r(!1)),[]),n=(0,a.useCallback)((()=>r(!0)),[]),l=(0,T.useSvgAria)();return(0,e.createElement)(a.Fragment,null,s&&(0,e.createElement)(je,{title:(0,i.__)("Unlock Premium SEO analysis","wordpress-seo"),onRequestClose:o,additionalClassName:"",className:`${Be} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,id:"yoast-premium-seo-analysis-modal",shouldCloseOnClickOutside:!0},(0,e.createElement)(Ue,null,(0,e.createElement)(Pn,{buyLink:`shortlinks.upsell.${t}.premium_seo_analysis_button`}))),"sidebar"===t&&(0,e.createElement)(Ge,{id:"yoast-premium-seo-analysis-modal-open-button",title:(0,i.__)("Premium SEO analysis","wordpress-seo"),prefixIcon:{icon:"seo-score-none",color:_e.colors.$color_grey},onClick:n},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Sr,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...l})))),"metabox"===t&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(qe,{id:"yoast-premium-seo-analysis-metabox-modal-open-button",onClick:n},(0,e.createElement)(ve.SvgIcon,{icon:"seo-score-none",color:_e.colors.$color_grey}),(0,e.createElement)(qe.Text,null,(0,i.__)("Premium SEO analysis","wordpress-seo")),(0,e.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Sr,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...l}),(0,e.createElement)("span",null,"Premium")))))};An.propTypes={location:g().string},An.defaultProps={location:"sidebar"};const Fn=An,On=t=>(0,e.createElement)(qr,{title:(0,i.__)("Reach a wider audience","wordpress-seo"),description:(0,i.__)("Get help optimizing for up to 5 related keyphrases. This helps you reach a wider audience and get more traffic.","wordpress-seo"),benefitsTitle:(0,i.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,i.__)("%s also gives you:","wordpress-seo"),"Yoast SEO Premium"),benefits:he(),upsellButtonText:(0,i.sprintf)(/* translators: %s expands to 'Yoast SEO Premium'. */
(0,i.__)("Unlock with %s","wordpress-seo"),"Yoast SEO Premium"),upsellButton:{href:t.buyLink,className:"yoast-button-upsell",rel:null,"data-ctb-id":"f6a84663-465f-4cb5-8ba5-f7a6d72224b2","data-action":"load-nfd-ctb"},upsellButtonLabel:(0,i.__)("1 year free support and updates included!","wordpress-seo")});On.propTypes={buyLink:g().string.isRequired};const Nn=On,Mn=()=>{const[t,,,s,r]=(0,T.useToggleState)(!1),o=(0,a.useContext)(d.LocationContext),{locationContext:n}=(0,d.useRootContext)(),l=(0,T.useSvgAria)(),c=wpseoAdminL10n["sidebar"===o.toLowerCase()?"shortlinks.upsell.sidebar.additional_button":"shortlinks.upsell.metabox.additional_button"];return(0,e.createElement)(e.Fragment,null,t&&(0,e.createElement)(je,{title:(0,i.__)("Add related keyphrases","wordpress-seo"),onRequestClose:r,additionalClassName:"",id:"yoast-additional-keyphrases-modal",className:`${Be} yoast-gutenberg-modal__box yoast-gutenberg-modal__no-padding`,shouldCloseOnClickOutside:!0},(0,e.createElement)(Ue,null,(0,e.createElement)(Nn,{buyLink:(0,hr.addQueryArgs)(c,{context:n})}))),"sidebar"===o&&(0,e.createElement)(Ge,{id:"yoast-additional-keyphrase-modal-open-button",title:(0,i.__)("Add related keyphrase","wordpress-seo"),prefixIcon:{icon:"plus",color:_e.colors.$color_grey_medium_dark},onClick:s},(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Sr,{className:"yst-w-2.5 yst-h-2.5 yst-shrink-0",...l})))),"metabox"===o&&(0,e.createElement)("div",{className:"yst-root"},(0,e.createElement)(qe,{id:"yoast-additional-keyphrase-metabox-modal-open-button",onClick:s},(0,e.createElement)(ve.SvgIcon,{icon:"plus",color:_e.colors.$color_grey_medium_dark}),(0,e.createElement)(qe.Text,null,(0,i.__)("Add related keyphrase","wordpress-seo")),(0,e.createElement)(T.Badge,{size:"small",variant:"upsell"},(0,e.createElement)(Sr,{className:"yst-w-2.5 yst-h-2.5 yst-me-1 yst-shrink-0",...l}),(0,e.createElement)("span",null,"Premium")))))};var qn,Dn,$n,Un,Bn,Wn,jn,Hn,Kn,zn,Yn,Vn,Gn,Zn,Xn,Qn,Jn,ea,ta,sa,ra,oa,na,aa,ia,la,ca,da,ua,pa,ma,ha,ga,ya,fa,ba,wa,Ea,ka,va,_a,xa,Ta,Sa,Ra,Ca,Ia;function La(){return La=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},La.apply(this,arguments)}const Pa=t=>e.createElement("svg",La({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 448 360"},t),qn||(qn=e.createElement("circle",{cx:226,cy:211,r:149,fill:"#f0ecf0"})),Dn||(Dn=e.createElement("path",{fill:"#fbd2a6",d:"M173.53 189.38s-35.47-5.3-41.78-11c-9.39-24.93-29.61-48-35.47-66.21-.71-2.24 3.72-11.39 3.53-15.41s-5.34-11.64-5.23-14-.09-15.27-.09-15.27l-4.75-.72s-5.13 6.07-3.56 9.87c-1.73-4.19 4.3 7.93.5 9.35 0 0-6-5.94-11.76-8.27s-19.57-3.65-19.57-3.65L43.19 73l-4.42.6L31 69.7l-2.85 5.12 7.53 5.29L40.86 92l17.19 10.2 10.2 10.56 9.86 3.56s26.49 79.67 45 92c17 11.33 37.23 15.92 37.23 15.92z"})),$n||($n=e.createElement("path",{fill:"#a4286a",d:"M270.52 345.13c2.76-14.59 15.94-35.73 30.24-54.58 16.22-21.39 14-79.66-33.19-91.46-17.3-4.32-52.25-1-59.85-3.41C186.54 189 170 187 168 190.17c-5 10.51-7.73 27.81-5.51 36.26 1.18 4.73 3.54 5.91 20.49 13.4-5.12 15-16.35 26.3-22.86 37s7.88 27.2 7.1 33.51c-.48 3.8-4.26 21.13-7.18 34.25a149.47 149.47 0 0 0 110.3 8.66 25.66 25.66 0 0 1 .18-8.12z"})),Un||(Un=e.createElement("path",{fill:"#9a5815",d:"M206.76 66.43c-5 14.4-1.42 25.67-3.93 40.74-10 60.34-24.08 43.92-31.44 93.6 7.24-14.19 14.32-15.82 20.63-23.11-.83 3.09-10.25 13.75-8.05 34.81 9.85-8.51 6.35-8.75 11.86-8.54.36 3.25 3.53 3.22-3.59 10.53 2.52.69 17.42-14.32 20.16-12.66s0 5.72-6 7.76c2.15 2.2 30.47-3.87 43.81-14.71 4.93-4 10-13.16 13.38-18.2 7.17-10.62 12.38-24.77 17.71-36.6 8.94-19.87 15.09-39.34 16.11-61.31.53-10.44-3.41-18.44-4.41-28.86-2.57-27.8-67.63-37.26-86.24 16.55z"})),Bn||(Bn=e.createElement("path",{fill:"#efb17c",d:"M277.74 179.06c.62-.79 1.24-1.59 1.84-2.39-.85 2.59-1.52 3.73-1.84 2.39z"})),Wn||(Wn=e.createElement("path",{fill:"#fbd2a6",d:"M216.1 206.72c3.69-5.42 8.28-3.35 15.57-8.28 3.76-3.06 1.57-9.46 1.77-11.82 18.25 4.56 37.38-1.18 49.07-16 .62 5.16-2.77 22.27-.2 27 4.73 8.67 13.4 18.92 13.4 18.92-35.47-2.76-63.45 39-89.86 44.54 5.52-28.74-2.36-35.84 10.25-54.36z"})),jn||(jn=e.createElement("path",{fill:"#f6b488",d:"m235.21 167.9 53.21-25.23s-3.65 24-6.5 32.72c-64.05 62.66-46.47-7.33-46.71-7.49z"})),Hn||(Hn=e.createElement("path",{fill:"#fbd2a6",d:"M226.86 50.64C215 59.31 206.37 93.21 204 95.57c-19.46 19.47-3.59 41.39-3.94 51.24-.2 5.52-4.14 25.42 5.72 29.36 22.22 8.89 60-3.48 67.19-12.61 13.28-16.75 40.89-94.78 17.74-108.19-7.92-4.58-42.78-20.18-63.85-4.73z"})),Kn||(Kn=e.createElement("path",{fill:"#e5766c",d:"M243.69 143.66c-10.7-6.16-8.56-6.73-19.76-12.71-3.86-2.07-3.94.64-6.32 0-2.91-.79-1.39-2.74-5.37-3.48-6.52-1.21-3.67 3.63-3.15 6 1.32 6.15-8.17 17.3 3.26 21.42 12.65 4.55 21.38-9.41 31.34-11.23z"})),zn||(zn=e.createElement("path",{fill:"#fff",d:"M240.68 143.9c-11.49-5.53-11.65-8.17-24.64-11.69-8.6-2.32-5.53 1-5.69 4.42-.2 4.16-1.26 9.87 4.9 12.66 9 4.09 18.16-6.02 25.43-5.39zm.7-40.9c-.16 1.26-.06 4.9 5.46 8.25 11.43-4.73 16.36-2.56 17-3.33 1.48-1.76-2-8.87-7.88-9.85-5.58-.94-14.14 1.24-14.58 4.93z"})),Yn||(Yn=e.createElement("path",{fill:"#000001",d:"M263.53 108.19c-4.32-4.33-6.85-6.24-12.26-8.21-2.77-1-6.18.18-8.65 1.67a3.65 3.65 0 0 0-1.24 1.23h-.12a3.73 3.73 0 0 1 1-1.52 12.53 12.53 0 0 1 11.93-3c4.73 1 9.43 4.63 9.42 9.82z"})),Vn||(Vn=e.createElement("circle",{cx:254.13,cy:104.05,r:4.19,fill:"#000001"})),Gn||(Gn=e.createElement("path",{fill:"#fff",d:"M225.26 99.22c-.29 1-6.6 3.45-10.92 1.48-1.15-3.24-5-6.43-5.25-6.71-.5-2.86 5.55-8 10.06-6.3a10.21 10.21 0 0 1 6.11 11.53z"})),Zn||(Zn=e.createElement("path",{fill:"#000001",d:"M209.29 94.21c-.19-2.34 1.84-4.1 3.65-5.2 7-3.87 13.18 3 12.43 10h-.12c-.14-4-2.38-8.44-6.47-9.11a3.19 3.19 0 0 0-2.42.31c-1.37.85-2.38 2-3.89 2.56-1 .45-1.92.42-3 1.4h-.22z"})),Xn||(Xn=e.createElement("circle",{cx:219.55,cy:95.28,r:4,fill:"#000001"})),Qn||(Qn=e.createElement("path",{fill:"#efb17c",d:"M218.66 120.27a27.32 27.32 0 0 0 4.54 3.45c-2.29-.72-4.28-.69-6.32-2.27-2.53-2-3.39-5.16-.73-7.72 10.24-9.82 12.56-13.82 14.77-24.42-1 12.37-6 17.77-10.63 23.18-2.53 2.97-4.68 5.06-1.63 7.78z"})),Jn||(Jn=e.createElement("path",{fill:"#a57c52",d:"M231.22 69.91c-.67-3.41-8.78-2.83-11.06-1.93-3.48 1.39-6.08 5.22-7.13 8.53 2.9-4.3 6.74-8.12 12.46-6 1.16.42 3.18 2.35 4.48 1.85s1.03-2.2 1.25-2.45zm32.16 8.56c-2.75-1.66-12.24-5.08-12.18.82 2.56.24 5-.19 7.64.95 11.22 4.76 12.77 17.61 12.85 17.86.2-.53.1 1.26.23.7-.02.2.95-12.12-8.54-20.33z"})),ea||(ea=e.createElement("path",{fill:"#fbd2a6",d:"M53.43 250.73c6.29 0-.6-.17 7.34 0 1.89.05-2.38-.7 0-.69 4.54-4.2 12.48-.74 20.6-2.45 4.55.35 3.93 1.35 5.59 4.19 4.89 8.38 4.78 14.21 14 19.56 16.42 8.38 66 12.92 88.49 18.86 5.52.83 42.64-20.15 61-23.75 6.51 10.74 11.46 28.68 8.39 34.93-6.54 13.3-57.07 25.4-75.91 25.15C156.47 326.18 94 294 92.2 293c-.94-.57.7-.7-7.68 0s-10.15.72-17.47-1.4c-3-.87-4.61-1.33-6.33-3.54-2 .22-3.39.2-4.78-1-3.15-2.74-4.84-6.61-2.73-10.06h-.12c-3.35-2.48-6.54-7.69-3.08-11.72 1-1.18 6.06-1.94 7.77-2.28-1.58-.29-6.37.19-7.49-.72-3.06-2.5-4.96-11.55 3.14-11.55z"})),ta||(ta=e.createElement("path",{fill:"#a4286a",d:"M303.22 237.52c-9.87-11.88-41.59 8.19-47.8 12.34s-14.89 17.95-14.89 17.95c6 9.43 8.36 31 5.65 46.34l30.51-3s18-15.62 22.59-28.7 6.3-42.54 6.3-42.54"})),sa||(sa=e.createElement("path",{fill:"#cb9833",d:"M278.63 31.67c-6.08 0-22.91 4.07-22.93 12.91 0 11 47.9 38.38 16.14 85.85 10.21-.79 10.79-8.12 14.92-14.93-3.66 77-49.38 93.58-40.51 142.25 7.68-25.81 20.3-11.62 38.13-33.84 3.45 4.88 9 18.28-9.46 33.78 50-31.26 57.31-56.6 51.92-95C319.93 113.53 348.7 42 278.63 31.67z"})),ra||(ra=e.createElement("path",{fill:"#fbd2a6",d:"M283.64 126.83c-2.42 9.67-8 15.76-1.48 16.46A21.26 21.26 0 0 0 302 132.6c5.17-8.52 3.93-16.44-2.46-18s-13.48 2.56-15.9 12.23z"})),oa||(oa=e.createElement("path",{fill:"#efb17c",d:"M38 73.45c1.92 2 4.25 9.21 6.32 10.91 2.25 1.85 5.71 2.12 8.1 4.45 3.66-2 6-8.72 10-9.31-2.59 1.31-4.42 3.5-6.93 4.88-1.42.8-3 1.31-4.38 2.25-2.16-1.46-4.27-1.77-6.26-3.38-2.52-2.02-5.31-8-6.85-9.8z"})),na||(na=e.createElement("path",{fill:"#efb17c",d:"M39 74.4c4.83 1.1 12.52 6.44 15.89 10-3.22-1.34-14.73-6.15-15.89-10zm.62-1.5c6.71-.79 18 1.54 23.29 5.9-3.85-.2-5.42-1.48-9-2.94-4.08-1.69-8.83-2.03-14.29-2.96zm46.43 14.58c-3.72-1.32-10.52-1.13-13.22 3.52 2-1.16 1.84-2.11 4.18-1.72-3.81-4.15 8.16-.74 11.6-.24m-2.78 13.15c.56-3.29-8-7.81-10.58-9.17-6.25-3.29-12.16 1.36-19.33-4.53 5.94 6.1 14.23 2.5 19.55 5.76 3.06 1.88 8.65 6.09 9.35 9.38-.23-.4 1.29-1.44 1.01-1.44z"})),aa||(aa=e.createElement("circle",{cx:38.13,cy:30.03,r:3.14,fill:"#b89ac8"})),ia||(ia=e.createElement("circle",{cx:60.26,cy:39.96,r:3.14,fill:"#e31e0c"})),la||(la=e.createElement("circle",{cx:50.29,cy:25.63,r:3.14,fill:"#3baa45"})),ca||(ca=e.createElement("circle",{cx:22.19,cy:19.21,r:3.14,fill:"#2ca9e1"})),da||(da=e.createElement("circle",{cx:22.19,cy:30.03,r:3.14,fill:"#e31e0c"})),ua||(ua=e.createElement("circle",{cx:26.86,cy:8.28,r:3.14,fill:"#3baa45"})),pa||(pa=e.createElement("circle",{cx:49.32,cy:39.99,r:3.14,fill:"#e31e0c"})),ma||(ma=e.createElement("circle",{cx:63.86,cy:59.52,r:3.14,fill:"#f8ad39"})),ha||(ha=e.createElement("circle",{cx:50.88,cy:50.72,r:3.14,fill:"#3baa45"})),ga||(ga=e.createElement("circle",{cx:63.47,cy:76.17,r:3.14,fill:"#e31e0c"})),ya||(ya=e.createElement("circle",{cx:38.34,cy:14.83,r:3.14,fill:"#2ca9e1"})),fa||(fa=e.createElement("circle",{cx:44.44,cy:5.92,r:3.14,fill:"#f8ad39"})),ba||(ba=e.createElement("circle",{cx:57.42,cy:10.24,r:3.14,fill:"#e31e0c"})),wa||(wa=e.createElement("circle",{cx:66.81,cy:12.4,r:3.14,fill:"#2ca9e1"})),Ea||(Ea=e.createElement("circle",{cx:77.95,cy:5.14,r:3.14,fill:"#b89ac8"})),ka||(ka=e.createElement("circle",{cx:77.95,cy:30.34,r:3.14,fill:"#e31e0c"})),va||(va=e.createElement("circle",{cx:80.97,cy:16.55,r:3.14,fill:"#f8ad39"})),_a||(_a=e.createElement("circle",{cx:62.96,cy:27.27,r:3.14,fill:"#3baa45"})),xa||(xa=e.createElement("circle",{cx:75.36,cy:48.67,r:3.14,fill:"#2ca9e1"})),Ta||(Ta=e.createElement("circle",{cx:76.11,cy:65.31,r:3.14,fill:"#3baa45"})),Sa||(Sa=e.createElement("path",{fill:"#71b026",d:"M78.58 178.43C54.36 167.26 32 198.93 5 198.93c19.56 20.49 63.53 1.52 69 15.5 1.48-14.01 4.11-30.9 4.58-36z"})),Ra||(Ra=e.createElement("path",{fill:"#074a67",d:"M67.75 251.08c0-4.65 10.13-72.65 10.13-72.65h2.8l-9.09 72.3z"})),Ca||(Ca=e.createElement("ellipse",{cx:255.38,cy:103.18,fill:"#fff",rx:1.84,ry:1.77})),Ia||(Ia=e.createElement("ellipse",{cx:221.24,cy:94.75,fill:"#fff",rx:1.84,ry:1.77}))),Aa=(0,Pe.compose)([(0,o.withSelect)(((e,t)=>{const{isAlertDismissed:s}=e(t.store||"yoast-seo/editor");return{isAlertDismissed:s(t.alertKey)}})),(0,o.withDispatch)(((e,t)=>{const{dismissAlert:s}=e(t.store||"yoast-seo/editor");return{onDismissed:()=>s(t.alertKey)}}))]),Fa=({children:t,id:s,hasIcon:r=!0,title:o,image:n=null,isAlertDismissed:a,onDismissed:l})=>a?null:(0,e.createElement)("div",{id:s,className:"notice-yoast yoast is-dismissible yoast-webinar-dashboard yoast-general-page-notices"},(0,e.createElement)("div",{className:"notice-yoast__container"},(0,e.createElement)("div",null,(0,e.createElement)("div",{className:"notice-yoast__header"},r&&(0,e.createElement)("span",{className:"yoast-icon"}),(0,e.createElement)("h2",{className:"notice-yoast__header-heading yoast-notice-migrated-header"},o)),(0,e.createElement)("div",{className:"notice-yoast-content"},(0,e.createElement)("p",null,t))),n&&(0,e.createElement)(n,{height:"60"})),(0,e.createElement)("button",{type:"button",className:"notice-dismiss",onClick:l},(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,i.__)("Dismiss this notice.","wordpress-seo"))));Fa.propTypes={children:g().node.isRequired,id:g().string.isRequired,hasIcon:g().bool,title:g().any.isRequired,image:g().elementType,isAlertDismissed:g().bool.isRequired,onDismissed:g().func.isRequired};const Oa=Aa(Fa),Na=({store:t="yoast-seo/editor",image:s=null,title:r,promoId:n,alertKey:a,children:i,...l})=>(0,o.select)(t).isPromotionActive(n)&&(0,e.createElement)(Oa,{alertKey:a,store:t,id:a,title:r,image:s,...l},i);Na.propTypes={store:g().string,image:g().elementType,title:g().any.isRequired,promoId:g().string.isRequired,alertKey:g().string.isRequired,children:g().node};const Ma=({store:t="yoast-seo/editor",location:s="sidebar",...r})=>{const n=(0,o.useSelect)((e=>e(t).getIsPremium()),[t]),a=(0,o.useSelect)((e=>e(t).selectLinkParams()),[t]),l="sidebar"===s?(0,i.sprintf)(/* translators: %1$s expands to Yoast SEO Premium */
(0,i.__)("Now with 30%% OFF: %1$s","wordpress-seo"),"Yoast SEO Premium"):S((0,i.sprintf)(/* translators: %1$s expands to Yoast SEO Premium, %2$s expands to a link on yoast.com, %3$s expands to the anchor end tag. */
(0,i.__)("Now with 30%% OFF: %1$s %2$sBuy now!%3$s","wordpress-seo"),"Yoast SEO Premium","<a>","</a>"),{a:(0,e.createElement)("a",{href:(0,hr.addQueryArgs)("https://yoa.st/black-friday-sale",a),target:"_blank",rel:"noreferrer"})});return n?null:(0,e.createElement)(Na,{id:`black-friday-2024-promotion-${s}`,promoId:"black-friday-2024-promotion",alertKey:"black-friday-2024-promotion",store:t,title:l,...r},(0,e.createElement)("span",{className:"yoast-bf-sale-badge"},(0,i.__)("BLACK FRIDAY SALE","wordpress-seo")," "),"sidebar"===s&&(0,e.createElement)("a",{className:"yst-block yst--mb-[1em]",href:(0,hr.addQueryArgs)("https://yoa.st/black-friday-sale",a),target:"_blank",rel:"noreferrer"},(0,i.__)("Buy now!","wordpress-seo")))};Ma.propTypes={store:g().string,location:g().oneOf(["sidebar","metabox"])};const qa=t=>s=>!(()=>{var e,t;const s=(0,o.select)("yoast-seo/editor").getIsPremium(),r=(0,o.select)("yoast-seo/editor").getWarningMessage();return(s&&null!==(e=null===(t=(0,o.select)("yoast-seo-premium/editor"))||void 0===t?void 0:t.getMetaboxWarning())&&void 0!==e?e:[]).length>0||r.length>0})()&&(0,e.createElement)(t,{...s});function Da(){return window.wpseoScriptData&&"1"===window.wpseoScriptData.isBlockEditor}const $a=()=>{const{editorMode:e,activeAIButtonId:t}=(0,o.useSelect)((e=>({editorMode:e("core/edit-post").getEditorMode(),activeAIButtonId:e("yoast-seo/editor").getActiveAIFixesButton()})),[]),{setMarkerStatus:s}=(0,o.useDispatch)("yoast-seo/editor");(0,a.useEffect)((()=>(s("visual"===e&&t||"text"===e?"disabled":"enabled"),()=>{s("disabled")})),[e,t])},Ua=qa((()=>{const t=(0,o.useSelect)((e=>e("yoast-seo/editor").selectLinkParams()),[]),s=(0,i.sprintf)(/* translators: %1$s expands to 'WooCommerce'. */
(0,i.__)("Is your %1$s store ready for Black Friday?","wordpress-seo"),"WooCommerce");return(0,e.createElement)(Na,{id:"black-friday-2023-product-editor-checklist",alertKey:"black-friday-2023-product-editor-checklist",promoId:"black-friday-2023-checklist",store:"yoast-seo/editor",title:s,image:Pa},S((0,i.sprintf)(/* translators: %1$s expands to a 'strong' start tag, %2$s to a 'strong' end tag. */
(0,i.__)("The Yoast %1$sultimate Black Friday checklist%2$s helps you prepare in time, so you can boost your results during this sale.","wordpress-seo"),"<strong>","</strong>"),{strong:(0,e.createElement)("strong",null)})," ",(0,e.createElement)("a",{href:(0,hr.addQueryArgs)("https://yoa.st/black-friday-checklist",t),target:"_blank",rel:"noreferrer"},(0,i.__)("Get the checklist and start optimizing now!","wordpress-seo")))})),Ba=qa(Ma);function Wa({settings:t}){const{isTerm:s,isProduct:r,isWooCommerceActive:n}=(0,o.useSelect)((e=>({isTerm:e("yoast-seo/editor").getIsTerm(),isProduct:e("yoast-seo/editor").getIsProduct(),isWooCommerceActive:e("yoast-seo/editor").getIsWooCommerceActive()})),[]),l=r&&n;return Da()&&$a(),(0,e.createElement)(a.Fragment,null,(0,e.createElement)(k.Fill,{name:"YoastMetabox"},(0,e.createElement)(Ur,{key:"warning",renderPriority:1},(0,e.createElement)(or,null)),(0,e.createElement)(Ur,{key:"time-constrained-notification",renderPriority:2},l&&(0,e.createElement)(Ua,null),(0,e.createElement)(Ba,{image:null,hasIcon:!1,location:"metabox"})),t.isKeywordAnalysisActive&&(0,e.createElement)(Ur,{key:"keyword-input",renderPriority:8},(0,e.createElement)(Ds.KeywordInput,{isSEMrushIntegrationActive:t.isSEMrushIntegrationActive}),!window.wpseoScriptData.metabox.isPremium&&(0,e.createElement)(k.Fill,{name:"YoastRelatedKeyphrases"},(0,e.createElement)(Cn,null))),(0,e.createElement)(Ur,{key:"search-appearance",renderPriority:9},(0,e.createElement)(ir,{id:"yoast-snippet-editor-metabox",title:(0,i.__)("Search appearance","wordpress-seo"),initialIsOpen:!0},(0,e.createElement)(rr,{hasPaperStyle:!1}))),t.isContentAnalysisActive&&(0,e.createElement)(Ur,{key:"readability-analysis",renderPriority:10},(0,e.createElement)(Ds.ReadabilityAnalysis,{shouldUpsell:t.shouldUpsell})),t.isKeywordAnalysisActive&&(0,e.createElement)(Ur,{key:"seo-analysis",renderPriority:20},(0,e.createElement)(a.Fragment,null,(0,e.createElement)(Ds.SeoAnalysis,{shouldUpsell:t.shouldUpsell,shouldUpsellWordFormRecognition:t.isWordFormRecognitionActive}),t.shouldUpsell&&(0,e.createElement)(Fn,{location:"metabox"}))),t.isInclusiveLanguageAnalysisActive&&(0,e.createElement)(Ur,{key:"inclusive-language-analysis",renderPriority:21},(0,e.createElement)(Ds.InclusiveLanguageAnalysis,null)),t.isKeywordAnalysisActive&&(0,e.createElement)(Ur,{key:"additional-keywords-upsell",renderPriority:22},t.shouldUpsell&&(0,e.createElement)(Mn,null)),t.isKeywordAnalysisActive&&t.isWincherIntegrationActive&&(0,e.createElement)(Ur,{key:"wincher-seo-performance",renderPriority:23},(0,e.createElement)(qs,{location:"metabox"})),t.shouldUpsell&&!s&&(0,e.createElement)(Ur,{key:"internal-linking-suggestions-upsell",renderPriority:25},(0,e.createElement)(Dr,null)),t.isCornerstoneActive&&(0,e.createElement)(Ur,{key:"cornerstone",renderPriority:30},(0,e.createElement)(Us,null)),t.displayAdvancedTab&&(0,e.createElement)(Ur,{key:"advanced",renderPriority:40},(0,e.createElement)(ir,{id:"collapsible-advanced-settings",title:(0,i.__)("Advanced","wordpress-seo")},(0,e.createElement)(Vr,null))),t.displaySchemaSettings&&(0,e.createElement)(Ur,{key:"schema",renderPriority:50},(0,e.createElement)(xn,null)),(0,e.createElement)(Ur,{key:"social",renderPriority:-1},(0,e.createElement)(dn,{target:"wpseo-section-social"})),t.isInsightsEnabled&&(0,e.createElement)(Ur,{key:"insights",renderPriority:52},(0,e.createElement)(Tr,{location:"metabox"}))))}Wa.propTypes={settings:g().object.isRequired};const ja=(0,Pe.compose)([(0,o.withSelect)(((e,t)=>{const{getPreferences:s}=e("yoast-seo/editor");return{settings:s(),store:t.store}}))])(Wa);function Ha({target:t,store:s,theme:r}){return(0,e.createElement)(Se,{target:t},(0,e.createElement)(Le,{store:s,theme:r}),(0,e.createElement)(ja,{store:s,theme:r}))}Ha.propTypes={target:g().string.isRequired,store:g().object,theme:g().object};const Ka=({error:t})=>{const s=(0,a.useCallback)((()=>{var e,t;return null===(e=window)||void 0===e||null===(t=e.location)||void 0===t?void 0:t.reload()}),[]),r=(0,o.useSelect)((e=>e("yoast-seo/editor").selectLink("https://yoa.st/sidebar-error-support")),[]),n=(0,o.useSelect)((e=>e("yoast-seo/editor").getPreference("isRtl",!1)),[]);return(0,e.createElement)(T.Root,{context:{isRtl:n}},(0,e.createElement)(F,{error:t},(0,e.createElement)(F.VerticalButtons,{supportLink:r,handleRefreshClick:s})))};function za({theme:t}){return(0,e.createElement)(x,{theme:t,location:"sidebar"},(0,e.createElement)(T.ErrorBoundary,{FallbackComponent:Ka},(0,e.createElement)(k.Slot,{name:"YoastSidebar"},(e=>v(e)))))}function Ya({score:t,label:s,scoreValue:r}){return(0,e.createElement)("div",{className:"yoast-analysis-check"},(0,e.createElement)(ve.SvgIcon,{...Te(t)}),(0,e.createElement)("span",null," ",s," ",r&&(0,e.createElement)("strong",null,r)))}function Va({checklist:t,onClick:s}){const r=t.every((e=>"good"===e.score));return(0,e.createElement)(a.Fragment,null,t.map((t=>(0,e.createElement)(Ya,{key:t.label,...t}))),(0,e.createElement)("br",null),!r&&(0,e.createElement)(ve.Button,{onClick:s},(0,i.__)("Improve your post with Yoast SEO","wordpress-seo")))}function Ga(e){return(0,p.isNil)(e)||(e/=10),function(e){switch(e){case"feedback":return{className:"na",screenReaderText:(0,i.__)("Not available","wordpress-seo"),screenReaderReadabilityText:(0,i.__)("Not available","wordpress-seo"),screenReaderInclusiveLanguageText:(0,i.__)("Not available","wordpress-seo")};case"bad":return{className:"bad",screenReaderText:(0,i.__)("Needs improvement","wordpress-seo"),screenReaderReadabilityText:(0,i.__)("Needs improvement","wordpress-seo"),screenReaderInclusiveLanguageText:(0,i.__)("Needs improvement","wordpress-seo")};case"ok":return{className:"ok",screenReaderText:(0,i.__)("OK SEO score","wordpress-seo"),screenReaderReadabilityText:(0,i.__)("OK","wordpress-seo"),screenReaderInclusiveLanguageText:(0,i.__)("Potentially non-inclusive","wordpress-seo")};case"good":return{className:"good",screenReaderText:(0,i.__)("Good SEO score","wordpress-seo"),screenReaderReadabilityText:(0,i.__)("Good","wordpress-seo"),screenReaderInclusiveLanguageText:(0,i.__)("Good","wordpress-seo")};default:return{className:"loading",screenReaderText:"",screenReaderReadabilityText:"",screenReaderInclusiveLanguageText:""}}}(xe.interpreters.scoreToRating(e))}function Za(e,t){const{isKeywordAnalysisActive:s}=t.getPreferences();if(s){const s=Ga(t.getReadabilityResults().overallScore);e.push({label:(0,i.__)("Readability analysis:","wordpress-seo"),score:s.className,scoreValue:s.screenReaderReadabilityText})}}function Xa(e,t){const{isContentAnalysisActive:s}=t.getPreferences();if(s){const s=Ga(t.getResultsForFocusKeyword().overallScore),r=m().isPremium;e.push({label:r?(0,i.__)("Premium SEO analysis:","wordpress-seo"):(0,i.__)("SEO analysis:","wordpress-seo"),score:s.className,scoreValue:s.screenReaderReadabilityText})}}function Qa(e,t){const{isInclusiveLanguageAnalysisActive:s}=t.getPreferences();if(s){const s=Ga(t.getInclusiveLanguageResults().overallScore);e.push({label:(0,i.__)("Inclusive language:","wordpress-seo"),score:s.className,scoreValue:s.screenReaderInclusiveLanguageText})}}Ka.propTypes={error:g().object.isRequired},Ya.propTypes={score:h.string.isRequired,label:h.string.isRequired,scoreValue:h.string},Ya.defaultProps={scoreValue:""},Va.propTypes={checklist:g().array.isRequired,onClick:g().func.isRequired};const Ja=(0,Pe.compose)([(0,o.withSelect)((function(e){const t=e("yoast-seo/editor"),s=[];return Xa(s,t),Za(s,t),Qa(s,t),s.push(...Object.values(t.getChecklistItems())),{checklist:s}})),(0,o.withDispatch)((function(e){const{openGeneralSidebar:t}=e("core/edit-post");return{onClick:()=>{t("yoast-seo/seo-sidebar")}}}))])(Va),ei=(0,Pe.compose)([(0,o.withSelect)((e=>{const t=e("yoast-seo/editor"),s=Ga(t.getResultsForFocusKeyword().overallScore),r=Ga(t.getReadabilityResults().overallScore),{isKeywordAnalysisActive:o,isContentAnalysisActive:n}=t.getPreferences();let a,i;switch(r.className){case"good":a=_e.colors.$color_good;break;case"ok":a=_e.colors.$color_ok;break;default:a=_e.colors.$color_bad}switch(s.className){case"good":i=_e.colors.$color_good;break;case"ok":i=_e.colors.$color_ok;break;default:i=_e.colors.$color_bad}return{readabilityScoreColor:a,seoScoreColor:i,isKeywordAnalysisActive:o,isContentAnalysisActive:n}}))])(E);var ti;function si(){return si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},si.apply(this,arguments)}const ri=t=>e.createElement("svg",si({xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",viewBox:"0 0 1600 1600"},t),ti||(ti=e.createElement("g",{fill:"none",fillRule:"evenodd"},e.createElement("path",{fill:"#1877f2",d:"M1600 800a800 800 0 1 0-925 790v-559H472V800h203V624c0-201 119-311 302-311 88 0 179 15 179 15v197h-101c-99 0-130 62-130 125v150h222l-36 231H925v559a800 800 0 0 0 675-790"}),e.createElement("path",{fill:"#fff",d:"M1147 800H925V650c0-63 31-125 130-125h101V328s-91-15-179-15c-183 0-302 110-302 311v176H472v231h203v559a806 806 0 0 0 250 0v-559h186z"}))));var oi;function ni(){return ni=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r])}return e},ni.apply(this,arguments)}const ai=t=>e.createElement("svg",ni({xmlns:"http://www.w3.org/2000/svg",fill:"current",viewBox:"0 0 1200 1227"},t),oi||(oi=e.createElement("path",{d:"M714.163 519.284 1160.89 0h-105.86L667.137 450.887 357.328 0H0l468.492 681.821L0 1226.37h105.866l409.625-476.152 327.181 476.152H1200L714.137 519.284h.026ZM569.165 687.828l-47.468-67.894-377.686-540.24h162.604l304.797 435.991 47.468 67.894 396.2 566.721H892.476L569.165 687.854v-.026Z"})));function ii({permalink:t}){const s=encodeURI(t);return(0,e.createElement)(a.Fragment,null,(0,e.createElement)("div",null,(0,i.__)("Share your post!","wordpress-seo")),(0,e.createElement)("ul",{className:"yoast-seo-social-share-buttons"},(0,e.createElement)("li",null,(0,e.createElement)("a",{href:"https://www.facebook.com/sharer/sharer.php?u="+s,target:"_blank",rel:"noopener noreferrer"},(0,e.createElement)(ri,null),(0,i.__)("Facebook","wordpress-seo"),(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,i.__)("(Opens in a new browser tab)","wordpress-seo")))),(0,e.createElement)("li",null,(0,e.createElement)("a",{href:"https://twitter.com/share?url="+s,target:"_blank",rel:"noopener noreferrer",className:"x-share"},(0,e.createElement)(ai,null),(0,i.__)("X","wordpress-seo"),(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,i.__)("(Opens in a new browser tab)","wordpress-seo"))))))}ii.propTypes={permalink:g().string.isRequired};const li=(0,Pe.compose)([(0,o.withSelect)((e=>({permalink:e("core/editor").getPermalink()})))])(ii);function ci({checklist:t,onClick:s}){let r;return r=t.every((e=>"good"===e.score))?(0,i.__)("We've analyzed your post. Everything looks good. Well done!","wordpress-seo"):(0,i.__)("We've analyzed your post. There is still room for improvement!","wordpress-seo"),(0,e.createElement)(a.Fragment,null,(0,e.createElement)("p",null,r),(0,e.createElement)(Va,{checklist:t,onClick:s}))}ci.propTypes={checklist:g().array.isRequired,onClick:g().func.isRequired};const di=(0,Pe.compose)([(0,o.withSelect)((function(e){const t=e("yoast-seo/editor"),s=[];return function(e,t){t.getFocusKeyphrase()||e.push({label:(0,i.__)("No focus keyword was entered","wordpress-seo"),score:"bad"})}(s,t),Xa(s,t),Za(s,t),Qa(s,t),s.push(...Object.values(t.getChecklistItems())),{checklist:s}})),(0,o.withDispatch)((function(e){const{closePublishSidebar:t,openGeneralSidebar:s}=e("core/edit-post");return{onClick:()=>{t(),s("yoast-seo/seo-sidebar")}}}))])(ci),ui="trustpilot-review-notification",pi="yoast-seo/editor",mi=()=>{const e=(0,o.useSelect)((e=>e(pi).getIsPremium()),[]),t=(0,o.useSelect)((e=>e(pi).isAlertDismissed(ui)),[]),{overallScore:s}=(0,o.useSelect)((e=>e(pi).getResultsForFocusKeyword()),[]),{dismissAlert:r}=(0,o.useDispatch)(pi),n=(0,a.useCallback)((()=>r(ui)),[r]),[i,l]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{var e;"good"===(null===(e=Ga(s))||void 0===e?void 0:e.className)&&l(!0)}),[s]),{shouldShow:!e&&!t&&i,dismiss:n}},hi=(0,De.makeOutboundLink)(),gi=()=>{const{shouldShow:t,dismiss:s}=mi(),{locationContext:r}=(0,d.useRootContext)(),n=(0,o.useSelect)((e=>e(pi).selectLink("https://yoa.st/trustpilot-review",{context:r})),[r]);return(0,e.createElement)(Fa,{alertKey:ui,store:pi,id:ui,title:(0,i.__)("Show Yoast SEO some love!","wordpress-seo"),hasIcon:!1,isAlertDismissed:!t,onDismissed:s},(0,i.__)("Happy with the plugin?","wordpress-seo")," ",(0,e.createElement)(hi,{href:n,rel:"noopener noreferrer"},(0,i.__)("Leave a quick review","wordpress-seo")),".")},yi=({store:t="yoast-seo/editor",image:s=Pa,url:r,...n})=>(0,o.useSelect)((e=>e(t).getIsPremium()))?null:(0,e.createElement)(Oa,{alertKey:"webinar-promo-notification",store:t,id:"webinar-promo-notification",title:(0,i.__)("Join our FREE webinar for SEO success","wordpress-seo"),image:s,url:r,...n},(0,i.__)("Feeling lost when it comes to optimizing your site for the search engines? Join our FREE webinar to gain the confidence that you need in order to start optimizing like a pro! You'll obtain the knowledge and tools to start effectively implementing SEO.","wordpress-seo")," ",(0,e.createElement)("a",{href:r,target:"_blank",rel:"noreferrer"},(0,i.__)("Sign up today!","wordpress-seo")));yi.propTypes={store:g().string,image:g().elementType,url:g().string.isRequired};const fi=yi,bi=(e="yoast-seo/editor")=>{const t=(0,o.select)(e).isPromotionActive("black-friday-2024-promotion"),s=(0,o.select)(e).isAlertDismissed("black-friday-2024-promotion");return t?s:((e="yoast-seo/editor")=>{const t=(0,o.select)(e).isPromotionActive("black-friday-2023-checklist"),s=(0,o.select)(e).isAlertDismissed("black-friday-2023-sidebar-checklist");return!t||s})(e)},wi=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{d:"M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"}))})),Ei=({id:t,postTypeName:s,children:r,title:o,isOpen:n,close:l,open:c,shouldCloseOnClickOutside:u,showChangesWarning:p,SuffixHeroIcon:m})=>(0,e.createElement)(a.Fragment,null,n&&(0,e.createElement)(d.LocationProvider,{value:"modal"},(0,e.createElement)(je,{title:o,onRequestClose:l,additionalClassName:"yoast-collapsible-modal yoast-post-settings-modal",id:"id",shouldCloseOnClickOutside:u},(0,e.createElement)("div",{className:"yoast-content-container"},(0,e.createElement)("div",{className:"yoast-modal-content"},r)),(0,e.createElement)("div",{className:"yoast-notice-container"},(0,e.createElement)("hr",null),(0,e.createElement)("div",{className:"yoast-button-container"},p&&(0,e.createElement)("p",null,/* Translators: %s translates to the Post Label in singular form */
(0,i.sprintf)((0,i.__)("Make sure to save your %s for changes to take effect","wordpress-seo"),s)),(0,e.createElement)("button",{className:"yoast-button yoast-button--primary yoast-button--post-settings-modal",type:"button",onClick:l},/* Translators: %s translates to the Post Label in singular form */
(0,i.sprintf)((0,i.__)("Return to your %s","wordpress-seo"),s)))))),(0,e.createElement)(Ge,{id:t+"-open-button",title:o,SuffixHeroIcon:m,suffixIcon:m?null:{size:"20px",icon:"pencil-square"},onClick:c}));Ei.propTypes={id:g().string.isRequired,postTypeName:g().string.isRequired,children:g().oneOfType([g().node,g().arrayOf(g().node)]).isRequired,title:g().string.isRequired,isOpen:g().bool.isRequired,open:g().func.isRequired,close:g().func.isRequired,shouldCloseOnClickOutside:g().bool,showChangesWarning:g().bool,SuffixHeroIcon:g().object},Ei.defaultProps={shouldCloseOnClickOutside:!0,showChangesWarning:!0};const ki=Ei,vi=(0,Pe.compose)([(0,o.withSelect)(((e,t)=>{const{getPostOrPageString:s,getIsModalOpen:r}=e("yoast-seo/editor");return{postTypeName:s(),isOpen:r(t.id)}})),(0,o.withDispatch)(((e,t)=>{const{openEditorModal:s,closeEditorModal:r}=e("yoast-seo/editor");return{open:()=>s(t.id),close:r}}))])(ki),_i=f()(wi)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,xi=({location:t})=>{const s=(0,o.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]),r=(0,o.useSelect)((e=>e("yoast-seo/editor").isFleschReadingEaseAvailable()),[]),n=Fe();return(0,e.createElement)(vi,{title:(0,i.__)("Insights","wordpress-seo"),id:`yoast-insights-modal-${t}`,shouldCloseOnClickOutside:!s,showChangesWarning:!1,SuffixHeroIcon:(0,e.createElement)(_i,{className:"yst-text-slate-500",...n})},(0,e.createElement)("div",{className:"yoast-insights yoast-modal-content--columns"},(0,e.createElement)(fr,{location:t}),(0,e.createElement)("div",null,r&&(0,e.createElement)("div",{className:"yoast-insights-row"},(0,e.createElement)(ur,null)),(0,e.createElement)("div",{className:"yoast-insights-row yoast-insights-row--columns"},(0,e.createElement)(lr,null),(0,e.createElement)(br,null)),(0,nr.isFeatureEnabled)("TEXT_FORMALITY")&&(0,e.createElement)(_r,{location:t,name:"YoastTextFormalityMetabox"}))))};xi.propTypes={location:g().string},xi.defaultProps={location:"sidebar"};const Ti=xi,Si=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"}))}));class Ri{constructor(e){this.refresh=e,this.loaded=!1,this.preloadThreshold=3e3,this.plugins={},this.modifications={},this._registerPlugin=this._registerPlugin.bind(this),this._ready=this._ready.bind(this),this._reloaded=this._reloaded.bind(this),this._registerModification=this._registerModification.bind(this),this._registerAssessment=this._registerAssessment.bind(this),this._applyModifications=this._applyModifications.bind(this),setTimeout(this._pollLoadingPlugins.bind(this),1500)}_registerPlugin(e,t){return(0,p.isString)(e)?(0,p.isUndefined)(t)||(0,p.isObject)(t)?!1===this._validateUniqueness(e)?(console.error("Failed to register plugin. Plugin with name "+e+" already exists"),!1):(this.plugins[e]=t,!0):(console.error("Failed to register plugin "+e+". Expected parameters `options` to be a object."),!1):(console.error("Failed to register plugin. Expected parameter `pluginName` to be a string."),!1)}_ready(e){return(0,p.isString)(e)?(0,p.isUndefined)(this.plugins[e])?(console.error("Failed to modify status for plugin "+e+". The plugin was not properly registered."),!1):(this.plugins[e].status="ready",!0):(console.error("Failed to modify status for plugin "+e+". Expected parameter `pluginName` to be a string."),!1)}_reloaded(e){return(0,p.isString)(e)?(0,p.isUndefined)(this.plugins[e])?(console.error("Failed to reload Content Analysis for plugin "+e+". The plugin was not properly registered."),!1):(this.refresh(),!0):(console.error("Failed to reload Content Analysis for "+e+". Expected parameter `pluginName` to be a string."),!1)}_registerModification(e,t,s,r){if(!(0,p.isString)(e))return console.error("Failed to register modification for plugin "+s+". Expected parameter `modification` to be a string."),!1;if(!(0,p.isFunction)(t))return console.error("Failed to register modification for plugin "+s+". Expected parameter `callable` to be a function."),!1;if(!(0,p.isString)(s))return console.error("Failed to register modification for plugin "+s+". Expected parameter `pluginName` to be a string."),!1;if(!1===this._validateOrigin(s))return console.error("Failed to register modification for plugin "+s+". The integration has not finished loading yet."),!1;const o={callable:t,origin:s,priority:(0,p.isNumber)(r)?r:10};return(0,p.isUndefined)(this.modifications[e])&&(this.modifications[e]=[]),this.modifications[e].push(o),!0}_registerAssessment(e,t,s,r){return(0,p.isString)(t)?(0,p.isObject)(s)?(0,p.isString)(r)?(t=r+"-"+t,e.addAssessment(t,s),!0):(console.error("Failed to register assessment for plugin "+r+". Expected parameter `pluginName` to be a string."),!1):(console.error("Failed to register assessment for plugin "+r+". Expected parameter `assessment` to be a function."),!1):(console.error("Failed to register test for plugin "+r+". Expected parameter `name` to be a string."),!1)}_applyModifications(e,t,s){let r=this.modifications[e];return!(0,p.isArray)(r)||r.length<1||(r=this._stripIllegalModifications(r),r.sort(((e,t)=>e.priority-t.priority)),(0,p.forEach)(r,(function(r){const o=r.callable(t,s);typeof o==typeof t?t=o:console.error("Modification with name "+e+" performed by plugin with name "+r.origin+" was ignored because the data that was returned by it was of a different type than the data we had passed it.")}))),t}_pollLoadingPlugins(e){e=(0,p.isUndefined)(e)?0:e,!0===this._allReady()?(this.loaded=!0,this.refresh()):e>=this.preloadThreshold?(this._pollTimeExceeded(),this.loaded=!0,this.refresh()):(e+=50,setTimeout(this._pollLoadingPlugins.bind(this,e),50))}_allReady(){return(0,p.reduce)(this.plugins,(function(e,t){return e&&"ready"===t.status}),!0)}_pollTimeExceeded(){(0,p.forEach)(this.plugins,(function(e,t){(0,p.isUndefined)(e.options)||"ready"===e.options.status||(console.error("Error: Plugin "+t+". did not finish loading in time."),delete this.plugins[t])}))}_stripIllegalModifications(e){return(0,p.forEach)(e,((t,s)=>{!1===this._validateOrigin(t.origin)&&delete e[s]})),e}_validateOrigin(e){return"ready"===this.plugins[e].status}_validateUniqueness(e){return(0,p.isUndefined)(this.plugins[e])}}let Ci=null;const Ii=()=>{if(null===Ci){const e=(0,o.dispatch)("yoast-seo/editor").runAnalysis;Ci=window.YoastSEO.app&&window.YoastSEO.app.pluggable?window.YoastSEO.app.pluggable:new Ri(e)}return Ci},Li=(e,t,s)=>Ii().loaded?Ii()._applyModifications(e,t,s):t,{stripHTMLTags:Pi}=De.strings,Ai=(e,t)=>{const s=(0,o.select)("yoast-seo/editor").getSnippetEditorTemplates();""===e.title&&(e.title=s.title),""===e.description&&(e.description=s.description);let r=0;return t.shortenedBaseUrl&&"string"==typeof t.shortenedBaseUrl&&(r=t.shortenedBaseUrl.length),e.url=e.url.replace(/\s+/g,"-"),"-"===e.url[e.url.length-1]&&(e.url=e.url.slice(0,-1)),"-"===e.url[r]&&(e.url=e.url.slice(0,r)+e.url.slice(r+1)),{url:e.url,title:Pi(Li("data_page_title",e.title)),description:Pi(Li("data_meta_desc",e.description)),filteredSEOTitle:Pi(Li("data_page_title",e.filteredSEOTitle))}},Fi=({isLoading:t,onLoad:s,location:r,...o})=>((0,a.useEffect)((()=>{setTimeout((()=>{t&&s()}))})),t?null:(0,e.createElement)(Hs,{icon:"eye",hasPaperStyle:o.hasPaperStyle},(0,e.createElement)(Bs.SnippetEditor,{...o,descriptionPlaceholder:(0,i.__)("Please provide a meta description by editing the snippet below.","wordpress-seo"),mapEditorDataToPreview:Ai,showCloseButton:!1,idSuffix:r})));Fi.propTypes={isLoading:g().bool.isRequired,onLoad:g().func.isRequired,hasPaperStyle:g().bool.isRequired,location:g().string.isRequired};const Oi=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getBaseUrlFromSettings:t,getDateFromSettings:s,getEditorDataImageUrl:r,getFocusKeyphrase:o,getRecommendedReplaceVars:n,getSiteIconUrlFromSettings:a,getSnippetEditorData:i,getSnippetEditorIsLoading:l,getSnippetEditorMode:c,getSnippetEditorWordsToHighlight:d,isCornerstoneContent:u,getContentLocale:p,getSiteName:m,getReplaceVars:h}=e("yoast-seo/editor");return{baseUrl:t(),data:i(),date:s(),faviconSrc:a(),isLoading:l(),keyword:o(),mobileImageSrc:r(),mode:c(),recommendedReplacementVariables:n(),replacementVariables:h(),wordsToHighlight:d(),isCornerstone:u(),locale:p(),siteName:m()}})),(0,o.withDispatch)((e=>{const{updateData:t,switchMode:s,updateAnalysisData:r,loadSnippetEditorData:o}=e("yoast-seo/editor");return{onChange:(e,r)=>{switch(e){case"mode":s(r);break;case"slug":t({slug:r});break;default:t({[e]:r})}},onChangeAnalysisData:r,onLoad:o}})),$s()])(Fi),Ni=f()(Si)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,Mi=()=>{const t=Fe(),s=(0,o.useSelect)((e=>e("yoast-seo/editor").getIsElementorEditor()),[]);return(0,e.createElement)(vi,{title:(0,i.__)("Search appearance","wordpress-seo"),id:"yoast-search-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,e.createElement)(Ni,{className:"yst-text-slate-500",...t})},!0===s&&(0,e.createElement)(Oi,{showCloseButton:!1,hasPaperStyle:!1}),!1===s&&(0,e.createElement)(rr,{showCloseButton:!1,hasPaperStyle:!1}))},qi=e.forwardRef((function(t,s){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",ref:s},t),e.createElement("path",{d:"M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"}))})),Di=f()(ve.Collapsible)`
	h2 > button {
		padding-left: 0;
		padding-top: 16px;

		&:hover {
			background-color: #f0f0f0;
		}
	}

	div[class^="collapsible_content"] {
		padding: 24px 0;
		margin: 0 24px;
		border-top: 1px solid rgba(0,0,0,0.2);
	}

`,$i=t=>(0,e.createElement)(Di,{hasPadding:!1,hasSeparator:!0,...t}),Ui=f()(qi)`
	width: 18px;
	height: 18px;
	margin: 3px;
`,Bi=t=>{const{useOpenGraphData:s,useTwitterData:r}=t;if(!s&&!r)return;const o=Fe();return(0,e.createElement)(vi
/* translators: Social media appearance refers to a preview of how a page will be represented on social media. */,{title:(0,i.__)("Social media appearance","wordpress-seo"),id:"yoast-social-appearance-modal",shouldCloseOnClickOutside:!1,SuffixHeroIcon:(0,e.createElement)(Ui,{className:"yst-text-slate-500",...o})},s&&(0,e.createElement)(a.Fragment,null,(0,e.createElement)(on,null,(0,i.__)("Determine how your post should look on social media like Facebook, X, Instagram, WhatsApp, Threads, LinkedIn, Slack, and more.","wordpress-seo")),(0,e.createElement)(Qo,null),r&&(0,e.createElement)(rn,null,(0,i.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below. If you leave these settings untouched, the 'Social media appearance' settings mentioned above will also be applied for sharing on X.","wordpress-seo"))),s&&r&&(0,e.createElement)($i,{title:(0,i.__)("X appearance","wordpress-seo"),hasSeparator:!0,initialIsOpen:!1},(0,e.createElement)(sn,null)),!s&&r&&(0,e.createElement)(a.Fragment,null,(0,e.createElement)(on,null,(0,i.__)("To customize the appearance of your post specifically for X, please fill out the 'X appearance' settings below.","wordpress-seo")),(0,e.createElement)(sn,null)))};Bi.propTypes={useOpenGraphData:g().bool.isRequired,useTwitterData:g().bool.isRequired};const Wi=Bi,ji=t=>{const[s,r]=(0,a.useState)(!1),{prefixIcon:o}=t;return(0,e.createElement)("div",{className:"yoast components-panel__body "+(s?"is-opened":"")},(0,e.createElement)("h2",{className:"components-panel__body-title"},(0,e.createElement)("button",{onClick:function(){r(!s)},className:"components-button components-panel__body-toggle",type:"button",id:t.buttonId},(0,e.createElement)("span",{className:"yoast-icon-span",style:{fill:`${o&&o.color||""}`}},o&&(0,e.createElement)(ve.SvgIcon,{icon:o.icon,color:o.color,size:o.size})),(0,e.createElement)("span",{className:"yoast-title-container"},(0,e.createElement)("div",{className:"yoast-title"},t.title),(0,e.createElement)("div",{className:"yoast-subtitle"},t.subTitle)),t.hasBetaBadgeLabel&&(0,e.createElement)(ve.BetaBadge,null),(0,e.createElement)("span",{className:"yoast-chevron","aria-hidden":"true"}))),s&&t.children)},Hi=ji;function Ki({settings:t}){const s=(({webinarIntroUrl:t})=>{const{shouldShow:s}=mi(),r=(e=>{for(const t of e)if(null!=t&&t.getIsEligible())return t;return null})([{getIsEligible:()=>s,component:gi},{getIsEligible:bi,component:()=>(0,e.createElement)(fi,{hasIcon:!1,image:null,url:t})},{getIsEligible:()=>!0,component:()=>(0,e.createElement)(Ma,{hasIcon:!1})}]);return(null==r?void 0:r.component)||null})({webinarIntroUrl:(0,p.get)(window,"wpseoScriptData.webinarIntroBlockEditorUrl","https://yoa.st/webinar-intro-block-editor")});return Da()&&$a(),(0,e.createElement)(a.Fragment,null,(0,e.createElement)(k.Fill,{name:"YoastSidebar"},(0,e.createElement)(Ur,{key:"warning",renderPriority:1},(0,e.createElement)(or,null),(0,e.createElement)("div",{style:{margin:"0 16px"}},s&&(0,e.createElement)(s,null))),t.isKeywordAnalysisActive&&(0,e.createElement)(Ur,{key:"keyword-input",renderPriority:8},(0,e.createElement)(Ds.KeywordInput,{isSEMrushIntegrationActive:t.isSEMrushIntegrationActive})),t.isKeywordAnalysisActive&&(0,e.createElement)(Ur,{key:"seo",renderPriority:10},(0,e.createElement)(a.Fragment,null,(0,e.createElement)(Ds.SeoAnalysis,{shouldUpsell:t.shouldUpsell,shouldUpsellWordFormRecognition:t.isWordFormRecognitionActive}),t.shouldUpsell&&(0,e.createElement)(Fn,{location:"sidebar"}))),t.isContentAnalysisActive&&(0,e.createElement)(Ur,{key:"readability",renderPriority:20},(0,e.createElement)(Ds.ReadabilityAnalysis,{shouldUpsell:t.shouldUpsell})),t.isInclusiveLanguageAnalysisActive&&(0,e.createElement)(Ur,{key:"inclusive-language-analysis",renderPriority:21},(0,e.createElement)(Ds.InclusiveLanguageAnalysis,null)),t.isKeywordAnalysisActive&&(0,e.createElement)(Ur,{key:"additional-keywords-upsell",renderPriority:22},t.shouldUpsell&&(0,e.createElement)(Mn,null)),t.isKeywordAnalysisActive&&t.isWincherIntegrationActive&&(0,e.createElement)(Ur,{renderPriority:23},(0,e.createElement)(qs,{location:"sidebar"})),t.shouldUpsell&&(0,e.createElement)(Ur,{key:"internal-linking-suggestions-upsell",renderPriority:25},(0,e.createElement)(Dr,null)),(0,e.createElement)(Ur,{key:"search-appearance",renderPriority:26},(0,e.createElement)(Mi,null)),(t.useOpenGraphData||t.useTwitterData)&&(0,e.createElement)(Ur,{key:"social-appearance",renderPriority:27},(0,e.createElement)(Wi,{useOpenGraphData:t.useOpenGraphData,useTwitterData:t.useTwitterData})),t.displaySchemaSettings&&(0,e.createElement)(Ur,{key:"schema",renderPriority:28},(0,e.createElement)(Hi,{title:(0,i.__)("Schema","wordpress-seo")},(0,e.createElement)(xn,null))),t.displayAdvancedTab&&(0,e.createElement)(Ur,{key:"advanced",renderPriority:29},(0,e.createElement)(Hi,{title:(0,i.__)("Advanced","wordpress-seo")},(0,e.createElement)(Vr,null))),t.isCornerstoneActive&&(0,e.createElement)(Ur,{key:"cornerstone",renderPriority:30},(0,e.createElement)(Us,null)),t.isInsightsEnabled&&(0,e.createElement)(Ur,{renderPriority:32},(0,e.createElement)(Ti,{location:"sidebar"}))))}ji.propTypes={title:g().string.isRequired,children:g().oneOfType([g().node,g().arrayOf(g().node)]).isRequired,prefixIcon:g().object,subTitle:g().string,hasBetaBadgeLabel:g().bool,buttonId:g().string},ji.defaultProps={prefixIcon:null,subTitle:"",hasBetaBadgeLabel:!1,buttonId:null},Ki.propTypes={settings:g().object.isRequired};const zi=(0,o.withSelect)(((e,t)=>{const{getPreferences:s}=e("yoast-seo/editor");return{settings:s(),store:t.store}}))(Ki);function Yi(t){const{hasTrackedKeyphrases:s,trackAll:r}=t;return(0,e.createElement)(a.Fragment,null,(0,e.createElement)(ve.FieldGroup,{label:(0,i.__)("SEO performance","wordpress-seo"),linkTo:wpseoAdminL10n["shortlinks.wincher.seo_performance"]
/* translators: Hidden accessibility text. */,linkText:(0,i.__)("Learn more about the SEO performance feature.","wordpress-seo"),wrapperClassName:"yoast-field-group yoast-wincher-post-publish"}),(0,e.createElement)(Zt,null),s&&(0,e.createElement)("p",null,(0,i.__)("Tracking has already been enabled for one or more keyphrases of this page. Clicking the button below will enable tracking for all of its keyphrases.","wordpress-seo")),(0,e.createElement)("div",{className:"yoast"},(0,e.createElement)(ve.NewButton,{variant:"secondary",small:!0,onClick:r},(0,i.__)("Track all keyphrases on this page","wordpress-seo"))),(0,e.createElement)(qs,{location:"postpublish"}))}Yi.propTypes={trackAll:g().func,hasTrackedKeyphrases:g().bool},Yi.defaultProps={trackAll:()=>{},hasTrackedKeyphrases:!1};const Vi=(0,Pe.compose)([(0,o.withSelect)((e=>{const{getWincherTrackedKeyphrases:t,hasWincherTrackedKeyphrases:s}=e("yoast-seo/editor");return{trackedKeyphrases:t(),hasTrackedKeyphrases:s()}})),(0,o.withDispatch)((e=>{const{setWincherOpenModal:t,setWincherTrackAllKeyphrases:s}=e("yoast-seo/editor");return{trackAll:()=>{s(!0),t("postpublish")}}}))])(Yi);window.wp.annotations;const Gi=/(<([a-z]|\/)[^<>]+>)/gi,{htmlEntitiesRegex:Zi}=xe.helpers.htmlEntities,Xi=e=>{let t=0;return(0,p.forEachRight)(e,(e=>{const[s]=e;let r=s.length;/^<\/?br/.test(s)&&(r-=1),t+=r})),t},Qi="<yoastmark class='yoast-text-mark'>",Ji="</yoastmark>",el='<yoastmark class="yoast-text-mark">';function tl(e,t,s,r,o){const n=r.clientId,a=(0,c.create)({html:e,multilineTag:s.multilineTag,multilineWrapperTag:s.multilineWrapperTag}).text;return(0,p.flatMap)(o,(s=>{let o;return o=s.hasBlockPosition&&s.hasBlockPosition()?function(e,t,s,r,o){if(t===e.getBlockClientId()){let t=e.getBlockPositionStart(),n=e.getBlockPositionEnd();if(e.isMarkForFirstBlockSection()){const e=((e,t,s)=>{const r="yoast/faq-block"===s?'<strong class="schema-faq-question">':'<strong class="schema-how-to-step-name">';return{blockStartOffset:e-=r.length,blockEndOffset:t-=r.length}})(t,n,s);t=e.blockStartOffset,n=e.blockEndOffset}if(r.slice(t,n)===o.slice(t,n))return[{startOffset:t,endOffset:n}];const a=((e,t,s)=>{const r=s.slice(0,e),o=s.slice(0,t),n=((e,t,s,r)=>{const o=[...e.matchAll(Gi)];s-=Xi(o);const n=[...t.matchAll(Gi)];return{blockStartOffset:s,blockEndOffset:r-=Xi(n)}})(r,o,e,t),a=((e,t,s,r)=>{let o=[...e.matchAll(Zi)];return(0,p.forEachRight)(o,(e=>{const[,t]=e;s-=t.length})),o=[...t.matchAll(Zi)],(0,p.forEachRight)(o,(e=>{const[,t]=e;r-=t.length})),{blockStartOffset:s,blockEndOffset:r}})(r,o,e=n.blockStartOffset,t=n.blockEndOffset);return{blockStartOffset:e=a.blockStartOffset,blockEndOffset:t=a.blockEndOffset}})(t,n,r);return[{startOffset:a.blockStartOffset,endOffset:a.blockEndOffset}]}return[]}(s,n,r.name,e,a):function(e,t){const s=t.getOriginal().replace(/(<([^>]+)>)/gi,""),r=t.getMarked().replace(/(<(?!\/?yoastmark)[^>]+>)/gi,""),o=function(e,t,s=!0){const r=[];if(0===e.length)return r;let o,n=0;for(s||(t=t.toLowerCase(),e=e.toLowerCase());(o=e.indexOf(t,n))>-1;)r.push(o),n=o+t.length;return r}(e,s);if(0===o.length)return[];const n=function(e){let t=e.indexOf(Qi);const s=t>=0;s||(t=e.indexOf(el));let r=null;const o=[];for(;t>=0;){if(r=(e=s?e.replace(Qi,""):e.replace(el,"")).indexOf(Ji),r<t)return[];e=e.replace(Ji,""),o.push({startOffset:t,endOffset:r}),t=s?e.indexOf(Qi):e.indexOf(el),r=null}return o}(r),a=[];return n.forEach((e=>{o.forEach((r=>{const o=r+e.startOffset;let n=r+e.endOffset;0===e.startOffset&&e.endOffset===t.getOriginal().length&&(n=r+s.length),a.push({startOffset:o,endOffset:n})}))})),a}(a,s),o?o.map((e=>({...e,block:n,richTextIdentifier:t}))):[]}))}const sl=e=>e[0].toUpperCase()+e.slice(1),rl=(e,t,s,r,o)=>(e=e.map((e=>{const n=`${e.id}-${o[0]}`,a=`${e.id}-${o[1]}`,i=sl(o[0]),l=sl(o[1]),c=e[`json${i}`],d=e[`json${l}`],{marksForFirstSection:u,marksForSecondSection:p}=((e,t)=>({marksForFirstSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&e.isMarkForFirstBlockSection():e)),marksForSecondSection:e.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?e.getBlockAttributeId()===t.id&&!e.isMarkForFirstBlockSection():e))}))(t,e),m=tl(c,n,s,r,u),h=tl(d,a,s,r,p);return m.concat(h)})),(0,p.flattenDeep)(e)),ol="yoast";let nl=[];const al={"core/paragraph":[{key:"content"}],"core/list":[{key:"values",multilineTag:"li",multilineWrapperTag:["ul","ol"]}],"core/list-item":[{key:"content"}],"core/heading":[{key:"content"}],"core/audio":[{key:"caption"}],"core/embed":[{key:"caption"}],"core/gallery":[{key:"caption"}],"core/image":[{key:"caption"}],"core/table":[{key:"caption"}],"core/video":[{key:"caption"}],"yoast/faq-block":[{key:"questions"}],"yoast/how-to-block":[{key:"steps"},{key:"jsonDescription"}]};function il(){const e=nl.shift();e&&((0,o.dispatch)("core/annotations").__experimentalAddAnnotation(e),ll())}function ll(){(0,p.isFunction)(window.requestIdleCallback)?window.requestIdleCallback(il,{timeout:1e3}):setTimeout(il,150)}function cl(){const e=(0,o.select)("core/block-editor").getSelectedBlock(),t=(0,o.select)("yoast-seo/editor").getActiveMarker();if(!e||!t)return;var s;s=e.clientId,(0,o.select)("core/annotations").__experimentalGetAnnotations().filter((e=>e.blockClientId===s&&e.source===ol)).forEach((e=>{(0,o.dispatch)("core/annotations").__experimentalRemoveAnnotation(e.id)}));const r=(0,o.select)("yoast-seo/editor").getResultById(t);if(void 0===r)return;const n=r.marks;var a;a=((e,t)=>{return(0,p.flatMap)((s=e.name,al.hasOwnProperty(s)?al[s]:[]),(s=>"yoast/faq-block"===e.name?((e,t,s)=>{const r=t.attributes[e.key];return 0===r.length?[]:rl(r,s,e,t,["question","answer"])})(s,e,t):"yoast/how-to-block"===e.name?((e,t,s)=>{const r=t.attributes[e.key];if(r&&0===r.length)return[];const o=[];return"steps"===e.key&&o.push(rl(r,s,e,t,["name","text"])),"jsonDescription"===e.key&&(s=s.filter((e=>e.hasBlockPosition&&e.hasBlockPosition()?!e.getBlockAttributeId():e)),o.push(tl(r,"description",e,t,s))),(0,p.flattenDeep)(o)})(s,e,t):function(e,t,s){const r=e.key,o=((e,t)=>{const s=e.attributes[t];return"string"==typeof s?s:(s||"").toString()})(t,r);return tl(o,r,e,t,s)}(s,e,t)));var s})(e,n),nl=a.map((e=>({blockClientId:e.block,source:ol,richTextIdentifier:e.richTextIdentifier,range:{start:e.startOffset,end:e.endOffset}}))),ll()}const dl=window.wp.htmlEntities,ul=(0,De.makeOutboundLink)(f().a`
	display: inline-block;
	position: relative;
	outline: none;
	text-decoration: none;
	border-radius: 100%;
	width: 24px;
	height: 24px;
	margin: -4px 0;
	vertical-align: middle;

	color: ${_e.colors.$color_help_text};
	
	&:hover,
	&:focus {
		color: ${_e.colors.$color_snippet_focus};	
	}
	
	// Overwrite the default blue active color for links.
	&:active {
		color: ${_e.colors.$color_help_text};	
	}

	&::before {
		position: absolute;
		top: 0;
		left: 0;
		padding: 2px;
		content: "\f223";
	}
`);function pl({isActive:t,activeAttributes:s,addingLink:r,value:o,onChange:n,speak:l,stopAddingLink:d,contentRef:u}){const m=(0,a.useMemo)(p.uniqueId,[r]),[h,g]=(0,a.useState)(),y=(0,c.useAnchor)({editableContentElement:u.current,settings:{...yl,isActive:t}}),f={url:s.url,type:s.type,id:s.id,opensInNewTab:"_blank"===s.target,noFollow:s.rel&&s.rel.split(" ").includes("nofollow"),sponsored:s.rel&&s.rel.split(" ").includes("sponsored"),...h},b=e=>f.url===e.url&&f.opensInNewTab!==e.opensInNewTab||f.noFollow!==e.noFollow||f.sponsored!==e.sponsored,w=e=>{if("number"==typeof e||"string"==typeof e)return String(e)},E=(0,e.createElement)(ul,{href:window.wpseoAdminL10n["shortlinks.nofollow_sponsored"],className:"dashicons"},(0,e.createElement)("span",{className:"screen-reader-text"},/* translators: Hidden accessibility text. */
(0,i.__)("Learn more about marking a link as nofollow or sponsored.","wordpress-seo"))),v=S((0,i.sprintf)(
// translators: %1$s and %2$s are opening and closing code tags, %3$s is a help link.
(0,i.__)("Search engines should ignore this link (mark as %1$snofollow%2$s)%3$s","wordpress-seo"),"<code>","</code>","<helplink />"),{code:(0,e.createElement)("code",null),helplink:E}),_=S((0,i.sprintf)(
// translators: %1$s and %2$s are opening and closing code tags, %3$s is a help link.
(0,i.__)("This is a sponsored link or advert (mark as %1$ssponsored%2$s)%3$s","wordpress-seo"),"<code>","</code>","<helplink />"),{code:(0,e.createElement)("code",null),helplink:E}),x=[{id:"opensInNewTab",title:(0,i.__)("Open in new tab","wordpress-seo")},{id:"noFollow",title:v},{id:"sponsored",title:_}],{__experimentalLinkControl:T}=window.wp.blockEditor;return(0,e.createElement)(k.Popover,{key:m,anchor:y,focusOnMount:!!r&&"firstElement",onClose:d,position:"bottom center",placement:"bottom",shift:!0},(0,e.createElement)(T,{value:f,onChange:e=>{e={...h,...e};const s=b(f);if((e=>b(e)&&!0===e.sponsored&&!0!==f.Sponsored)(e)&&(e.noFollow=!0),(e=>b(e)&&!1===e.noFollow&&!1!==f.noFollow)(e)&&(e.sponsored=!1),(e=>b(e)&&!e.url)(e))return void g(e);const r=(0,hr.prependHTTP)(e.url),a=function({url:e,opensInNewWindow:t,noFollow:s,sponsored:r}){const o={type:"core/link",attributes:{url:e}};let n=[];return t&&(o.attributes.target="_blank",n.push("noreferrer noopener")),r&&(n.push("sponsored"),n.push("nofollow")),s&&n.push("nofollow"),n.length>0&&(n=(0,p.uniq)(n),o.attributes.rel=n.join(" ")),o}({url:r,type:e.type,id:w(e.id),opensInNewWindow:e.opensInNewTab,noFollow:e.noFollow,sponsored:e.sponsored});if((0,c.isCollapsed)(o)&&!t){const t=((e,t)=>e.title?e.title:t)(e,r),s=(0,c.applyFormat)((0,c.create)({text:t}),a,0,t.length);n((0,c.insert)(o,s))}else{const e=(0,c.applyFormat)(o,a);e.start=e.end,e.activeFormats=[],n(e)}s||d(),(e=>{!function(e){if(!e)return!1;const t=e.trim();if(!t)return!1;if(/^\S+:/.test(t)){const e=(0,hr.getProtocol)(t);if(!(0,hr.isValidProtocol)(e))return!1;if((0,p.startsWith)(e,"http")&&!/^https?:\/\/[^\/\s]/i.test(t))return!1;const s=(0,hr.getAuthority)(t);if(!(0,hr.isValidAuthority)(s))return!1;const r=(0,hr.getPath)(t);if(r&&!(0,hr.isValidPath)(r))return!1;const o=(0,hr.getQueryString)(t);if(o&&!(0,hr.isValidQueryString)(o))return!1;const n=(0,hr.getFragment)(t);if(n&&!(0,hr.isValidFragment)(n))return!1}return!((0,p.startsWith)(t,"#")&&!(0,hr.isValidFragment)(t))}(e)?l((0,i.__)("Warning: the link has been inserted but may have errors. Please test it.","wordpress-seo"),"assertive"):l(t?(0,i.__)("Link edited.","wordpress-seo"):(0,i.__)("Link inserted.","wordpress-seo"),"assertive")})(r)},forceIsEditingLink:r,settings:x}))}pl.propTypes={isActive:g().bool,activeAttributes:g().object,addingLink:g().bool,value:g().object,onChange:g().func,speak:g().func.isRequired,stopAddingLink:g().func.isRequired,contentRef:g().object};const ml=(0,k.withSpokenMessages)(pl),hl="core/link",gl=(0,i.__)("Link","wordpress-seo"),yl={name:hl,title:gl,tagName:"a",className:null,attributes:{url:"href",target:"target",rel:"rel"},replaces:"core/link",__unstablePasteRule(e,{html:t,plainText:s}){if((0,c.isCollapsed)(e))return e;const r=(t||s).replace(/<[^>]+>/g,"").trim();return(0,hr.isURL)(r)?(window.console.log("Created link:\n\n",r),(0,c.applyFormat)(e,{type:hl,attributes:{url:(0,dl.decodeEntities)(r)}})):e},edit:(0,k.withSpokenMessages)(class extends a.Component{constructor(){super(...arguments),this.addLink=this.addLink.bind(this),this.stopAddingLink=this.stopAddingLink.bind(this),this.onRemoveFormat=this.onRemoveFormat.bind(this),this.state={addingLink:!1}}addLink(){const{value:e,onChange:t}=this.props,s=(0,c.getTextContent)((0,c.slice)(e));s&&(0,hr.isURL)(s)?t((0,c.applyFormat)(e,{type:hl,attributes:{url:s}})):s&&(0,hr.isEmail)(s)?t((0,c.applyFormat)(e,{type:hl,attributes:{url:`mailto:${s}`}})):this.setState({addingLink:!0})}stopAddingLink(){this.setState({addingLink:!1}),this.props.onFocus()}onRemoveFormat(){const{value:e,onChange:t,speak:s}=this.props;t((0,c.removeFormat)(e,hl)),s((0,i.__)("Link removed.","wordpress-seo"),"assertive")}render(){const{isActive:t,activeAttributes:s,value:r,onChange:o}=this.props,{RichTextToolbarButton:n,RichTextShortcut:l}=window.wp.blockEditor;return(0,e.createElement)(a.Fragment,null,(0,e.createElement)(l,{type:"primary",character:"k",onUse:this.addLink}),(0,e.createElement)(l,{type:"primaryShift",character:"k",onUse:this.onRemoveFormat}),t&&(0,e.createElement)(n,{name:"link",icon:"editor-unlink",title:(0,i.__)("Unlink","wordpress-seo"),onClick:this.onRemoveFormat,isActive:t,shortcutType:"primaryShift",shortcutCharacter:"k"}),!t&&(0,e.createElement)(n,{name:"link",icon:"admin-links",title:gl,onClick:this.addLink,isActive:t,shortcutType:"primary",shortcutCharacter:"k"}),(this.state.addingLink||t)&&(0,e.createElement)(ml,{addingLink:this.state.addingLink,stopAddingLink:this.stopAddingLink,isActive:t,activeAttributes:s,value:r,onChange:o,contentRef:this.props.contentRef}))}})};function fl(){const e=m();return(0,p.get)(e,"contentLocale","en_US")}const{updateReplacementVariable:bl,updateData:wl,hideReplacementVariables:El,setContentImage:kl,updateSettings:vl,setEditorDataContent:_l,setEditorDataTitle:xl,setEditorDataExcerpt:Tl,setEditorDataImageUrl:Sl,setEditorDataSlug:Rl}=u.actions,Cl=s.g.jQuery;window.yoast=window.yoast||{},window.yoast.initEditorIntegration=function(t){(function(t){const s=m(),c=s.isPremium?"Yoast SEO Premium":"Yoast SEO",u=(0,e.createElement)(E,null);(0,r.updateCategory)("yoast-structured-data-blocks",{icon:u}),(0,r.updateCategory)("yoast-internal-linking-blocks",{icon:u});const p={isRtl:s.isRtl},h=t.getState().preferences,g=h.isKeywordAnalysisActive||h.isContentAnalysisActive,y=h.isKeywordAnalysisActive&&h.isWincherIntegrationActive;!function(){var e,t,s;const r="yoast-seo/document-panel";var n,a,i,l;Boolean(null===(e=(0,o.dispatch)("core/editor"))||void 0===e?void 0:e.toggleEditorPanelOpened)?((null===(t=(0,o.select)("core/preferences"))||void 0===t?void 0:t.get("core","openPanels"))||(null===(s=(0,o.select)("core/preferences"))||void 0===s?void 0:s.get("core/edit-post","openPanels"))).includes(r)||null===(n=(0,o.dispatch)("core/editor"))||void 0===n||n.toggleEditorPanelOpened(r):null!==(a=(0,o.select)("core/preferences"))&&void 0!==a&&null!==(i=a.get("core/edit-post","openPanels"))&&void 0!==i&&i.includes(r)||null===(l=(0,o.dispatch)("core/edit-post"))||void 0===l||l.toggleEditorPanelOpened(r)}();const f={locationContext:"block-sidebar"},b={locationContext:"block-metabox"};(0,l.registerPlugin)("yoast-seo",{render:()=>(0,e.createElement)(a.Fragment,null,(0,e.createElement)(n.PluginSidebarMoreMenuItem,{target:"seo-sidebar",icon:(0,e.createElement)(ei,null)},c),(0,e.createElement)(n.PluginSidebar,{name:"seo-sidebar",title:c},(0,e.createElement)(d.Root,{context:f},(0,e.createElement)(za,{store:t,theme:p}))),(0,e.createElement)(a.Fragment,null,(0,e.createElement)(zi,{store:t,theme:p}),(0,e.createElement)(d.Root,{context:b},(0,e.createElement)(Ha,{target:"wpseo-metabox-root",store:t,theme:p}))),g&&(0,e.createElement)(n.PluginPrePublishPanel,{className:"yoast-seo-sidebar-panel",title:(0,i.__)("Yoast SEO","wordpress-seo"),initialOpen:!0,icon:(0,e.createElement)(a.Fragment,null)},(0,e.createElement)(di,null)),(0,e.createElement)(n.PluginPostPublishPanel,{className:"yoast-seo-sidebar-panel",title:(0,i.__)("Yoast SEO","wordpress-seo"),initialOpen:!0,icon:(0,e.createElement)(a.Fragment,null)},(0,e.createElement)(li,null),y&&(0,e.createElement)(Vi,null)),g&&(0,e.createElement)(n.PluginDocumentSettingPanel,{name:"document-panel",className:"yoast-seo-sidebar-panel",title:(0,i.__)("Yoast SEO","wordpress-seo"),icon:(0,e.createElement)(a.Fragment,null)},(0,e.createElement)(Ja,null))),icon:(0,e.createElement)(ei,null)})})(t),function(){if("function"==typeof(0,p.get)(window,"wp.blockEditor.__experimentalLinkControl")){const e=(0,o.select)("core/rich-text").getFormatType("core/unknown");void 0!==e&&(0,o.dispatch)("core/rich-text").removeFormatTypes("core/unknown"),[yl].forEach((({name:e,replaces:t,...s})=>{t&&(0,o.dispatch)("core/rich-text").removeFormatTypes(t),e&&(0,c.registerFormatType)(e,s)})),void 0!==e&&(0,c.registerFormatType)("core/unknown",e)}else console.warn((0,i.__)("Marking links with nofollow/sponsored has been disabled for WordPress installs < 5.4.","wordpress-seo")+" "+(0,i.sprintf)(
// translators: %1$s expands to Yoast SEO.
(0,i.__)("Please upgrade your WordPress version or install the Gutenberg plugin to get this %1$s feature.","wordpress-seo"),"Yoast SEO"))}(),function(e){(0,o.select)("core/block-editor")&&(0,p.isFunction)((0,o.select)("core/block-editor").getBlocks)&&(0,o.select)("core/annotations")&&(0,p.isFunction)((0,o.dispatch)("core/annotations").__experimentalAddAnnotation)&&e.dispatch(u.actions.setMarkerStatus("enabled"))}(t)},window.yoast.EditorData=class{constructor(e,t){this._refresh=e,this._store=t,this._data={},this.getPostAttribute=this.getPostAttribute.bind(this),this.refreshYoastSEO=this.refreshYoastSEO.bind(this)}initialize(e,t=[]){var s,r;this._data=this.getInitialData(e),s=this._data,r=this._store,(0,p.forEach)(s,((e,t)=>{Zs.includes(t)||r.dispatch(Ys(t,e))})),this._store.dispatch(El(t)),this.subscribeToGutenberg(),this.subscribeToYoastSEO()}getInitialData(e){const t=this.collectGutenbergData();return e=function(e,t){if(!e.custom_taxonomies)return e;const s={};return(0,p.forEach)(e.custom_taxonomies,((e,t)=>{const{name:r,label:o,descriptionName:n,descriptionLabel:a}=function(e){const t=Xs(e);return{name:"ct_"+t,label:Vs(e+" (custom taxonomy)"),descriptionName:"ct_desc_"+t,descriptionLabel:Vs(e+" description (custom taxonomy)")}}(t),i="string"==typeof e.name?(0,De.decodeHTML)(e.name):e.name,l="string"==typeof e.description?(0,De.decodeHTML)(e.description):e.description;s[r]={value:i,label:o},s[n]={value:l,label:a}})),t.dispatch(function(e){return{type:"SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLES_BATCH",updatedVariables:e}}(s)),(0,p.omit)({...e},"custom_taxonomies")}(e=function(e,t){return e.custom_fields?((0,p.forEach)(e.custom_fields,((e,s)=>{const{name:r,label:o}=function(e){return{name:"cf_"+Xs(e),label:Vs(e+" (custom field)")}}(s);t.dispatch(Ys(r,e,o))})),(0,p.omit)({...e},"custom_fields")):e}(e,this._store),this._store),{...e,...t}}setRefresh(e){this._refresh=e}isShallowEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e.hasOwnProperty(s)&&(!(s in t)||e[s]!==t[s]))return!1;return!0}getMediaById(e){return this._coreDataSelect||(this._coreDataSelect=(0,o.select)("core")),this._coreDataSelect.getMedia(e)}getPostAttribute(e){return this._coreEditorSelect||(this._coreEditorSelect=(0,o.select)("core/editor")),this._coreEditorSelect.getEditedPostAttribute(e)}getSlug(){if("auto-draft"===this.getPostAttribute("status"))return"";let e=this.getPostAttribute("generated_slug")||"";"auto-draft"===e&&(e="");const t=this.getPostAttribute("slug")||e;try{return decodeURI(t)}catch(e){return t}}getPostBaseUrl(){const e=(0,o.select)("core/editor").getPermalinkParts();if(null===e||null==e||!e.prefix)return window.wpseoScriptData.metabox.base_url;let t=e.prefix;if((0,o.select)("core/editor").isEditedPostNew())try{const e=new URL(t);t=e.origin+e.pathname}catch(e){}return t.endsWith("/")||(t+="/"),t}collectGutenbergData(){let e=(0,o.select)("core/editor").getEditedPostContent();const t=(0,o.select)("core/block-editor").getBlocks();1===t.length&&"core/freeform"===t[0].name&&(e=(0,r.getBlockContent)(t[0]));const s=this.calculateContentImage(e),n=this.getPostAttribute("excerpt")||"";return{content:e,title:this.getPostAttribute("title")||"",slug:this.getSlug(),excerpt:n||Qs(e,"ja"===fl()?80:156),excerpt_only:n,snippetPreviewImageURL:this.getFeaturedImage()||s,contentImage:s,baseUrl:this.getPostBaseUrl()}}getFeaturedImage(){const e=this.getPostAttribute("featured_media");if(e){const t=this.getMediaById(e);if(t)return t.source_url}return null}calculateContentImage(e){const t=xe.languageProcessing.imageInText(e);if(0===t.length)return"";const s=Cl.parseHTML(t.join(""));for(const e of s)if(e.src)return e.src;return""}handleEditorChange(e){this._data.content!==e.content&&this._store.dispatch(_l(e.content)),this._data.title!==e.title&&(this._store.dispatch(xl(e.title)),this._store.dispatch(bl("title",e.title))),this._data.excerpt!==e.excerpt&&(this._store.dispatch(Tl(e.excerpt)),this._store.dispatch(bl("excerpt",e.excerpt)),this._store.dispatch(bl("excerpt_only",e.excerpt_only))),this._data.slug!==e.slug&&(this._store.dispatch(Rl(e.slug)),this._store.dispatch(wl({slug:e.slug}))),this._data.snippetPreviewImageURL!==e.snippetPreviewImageURL&&(this._store.dispatch(Sl(e.snippetPreviewImageURL)),this._store.dispatch(wl({snippetPreviewImageURL:e.snippetPreviewImageURL}))),this._data.contentImage!==e.contentImage&&this._store.dispatch(kl(e.contentImage)),this._data.baseUrl!==e.baseUrl&&this._store.dispatch(vl({baseUrl:e.baseUrl}))}reapplyMarkers(){const{getActiveMarker:e,getMarkerPauseStatus:t}=(0,o.select)("yoast-seo/editor"),s=e(),r=t();s&&!r&&cl()}refreshYoastSEO(){const e=this.collectGutenbergData();!this.isShallowEqual(this._data,e)&&(this.handleEditorChange(e),this._data=e,this._refresh())}areNewAnalysisResultsAvailable(){const e=(0,o.select)("yoast-seo/editor"),t=e.getReadabilityResults(),s=e.getResultsForFocusKeyword();return(this._previousReadabilityResults!==t||this._previousSeoResults!==s)&&(this._previousReadabilityResults=t,this._previousSeoResults=s,!0)}onNewAnalysisResultsAvailable(){this.reapplyMarkers()}subscribeToGutenberg(){this.subscriber=(0,p.debounce)(this.refreshYoastSEO,500),(0,o.subscribe)(this.subscriber)}subscribeToYoastSEO(){this.yoastSubscriber=()=>{this.areNewAnalysisResultsAvailable()&&this.onNewAnalysisResultsAvailable()},(0,o.subscribe)(this.yoastSubscriber)}getData(){return this._data}}})()})();