(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var r in s)e.o(s,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:s[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{AnalysisList:()=>v,AnalysisResult:()=>x,ContentAnalysis:()=>A,SiteSEOReport:()=>T,renderRatingToColor:()=>E});const s=window.React;var r=e.n(s);const o=window.wp.i18n,n=window.yoast.styleGuide,i=window.lodash.noop;var l=e.n(i);const a=window.yoast.propTypes;var u=e.n(a);const d=window.yoast.styledComponents;var p=e.n(d);const m=window.yoast.componentsNew,g=window.yoast.helpers,c=window.lodash,{stripTagsFromHtmlString:h}=g.strings,k=["a","b","strong","em","i"],B=p().div`
	display: grid;
	grid-template-rows: 1fr;
	max-width: 32px;
	// This gap value is half the gap value between assessment result list items, which is 12px.
	gap: 6px;
`,b=p().li`
	// This is the height of the IconButtonToggle.
	min-height: 24px;
	margin-bottom: 12px;
	padding: 0;
	display: flex;
	align-items: flex-start;
	position: relative;
	gap: 12px;
`,C=p()(m.SvgIcon)`
	margin: 3px 0 0 0;
`,y=p().p`
	margin: 0;
	flex: 1 1 auto;
	color: ${e=>e.suppressedText?"rgba(30,30,30,0.5)":"inherit"};
`,f=({ariaLabel:e,id:t,className:r,status:o,onClick:n,isPressed:i})=>(0,s.createElement)(m.IconButtonToggle,{marksButtonStatus:o,className:r,onClick:n,id:t,icon:"eye",pressed:i,ariaLabel:e}),R=({ariaLabelMarks:e,ariaLabelEdit:t="",bulletColor:r,buttonIdMarks:o,buttonIdEdit:n="",editButtonClassName:i="",hasAIFixes:l=!1,hasBetaBadgeLabel:a=!1,hasEditButton:u=!1,hasMarksButton:d,id:p="",isPremium:g=!1,marker:R=c.noop,markButtonFactory:x=null,marksButtonStatus:w="enabled",marksButtonClassName:E="",onButtonClickMarks:v,onButtonClickEdit:_=c.noop,onResultChange:I=c.noop,pressed:N,renderHighlightingUpsell:A=c.noop,renderAIOptimizeButton:M=c.noop,shouldUpsellHighlighting:S=!1,suppressedText:H=!1,text:T})=>{const[L,O]=(0,s.useState)(!1),P=(0,s.useCallback)((()=>O(!1)),[]),U=(0,s.useCallback)((()=>O(!0)),[]);x=x||f;let z=null;return function(e,t){return!e||"hidden"===t}(d,w)||(z=x({onClick:S?U:v,status:w,className:E,id:o,isPressed:N,ariaLabel:e})),(0,s.useEffect)((()=>{I(p,R,d)}),[p,R,d]),(0,s.createElement)(b,null,(0,s.createElement)(C,{icon:"circle",color:r,size:"13px"}),(0,s.createElement)(y,{suppressedText:H},a&&(0,s.createElement)(m.BetaBadge,null),(0,s.createElement)("span",{dangerouslySetInnerHTML:{__html:h(T,k)}})),(0,s.createElement)(B,null,z,A(L,P),u&&g&&(0,s.createElement)(m.IconCTAEditButton,{className:i,onClick:_,id:n,icon:"edit",ariaLabel:t}),M(l,p)))};R.propTypes={text:u().string.isRequired,suppressedText:u().bool,bulletColor:u().string.isRequired,hasMarksButton:u().bool.isRequired,hasEditButton:u().bool,hasAIFixes:u().bool,buttonIdMarks:u().string.isRequired,buttonIdEdit:u().string,pressed:u().bool.isRequired,ariaLabelMarks:u().string.isRequired,ariaLabelEdit:u().string,onButtonClickMarks:u().func.isRequired,onButtonClickEdit:u().func,marksButtonStatus:u().string,marksButtonClassName:u().string,markButtonFactory:u().func,editButtonClassName:u().string,hasBetaBadgeLabel:u().bool,isPremium:u().bool,onResultChange:u().func,id:u().string,marker:u().oneOfType([u().func,u().array]),shouldUpsellHighlighting:u().bool,renderHighlightingUpsell:u().func,renderAIOptimizeButton:u().func};const x=R,w=p().ul`
	margin: 8px 0;
	padding: 0;
	list-style: none;
`;function E(e){switch(e){case"good":return n.colors.$color_good;case"OK":return n.colors.$color_ok;case"bad":return n.colors.$color_bad;default:return n.colors.$color_score_icon}}function v({results:e,marksButtonActivatedResult:t="",marksButtonStatus:r="enabled",marksButtonClassName:n="",editButtonClassName:i="",markButtonFactory:a=null,onMarksButtonClick:u=l(),onEditButtonClick:d=l(),isPremium:p=!1,onResultChange:m=l(),shouldUpsellHighlighting:g=!1,renderHighlightingUpsell:c=l(),renderAIOptimizeButton:h=l()}){return(0,s.createElement)(w,{role:"list"},e.map((e=>{const l=E(e.rating),k=e.markerId===t,B=e.id+"Mark",b=e.id+"Edit";let C="";C="disabled"===r?(0,o.__)("Highlighting is currently disabled","wordpress-seo"):k?(0,o.__)("Remove highlight from the text","wordpress-seo"):(0,o.__)("Highlight this result in the text","wordpress-seo");const y=e.editFieldName,f=""===y?"":(0,o.sprintf)(/* Translators: %1$s refers to the name of the field that should be edited (keyphrase, meta description, slug or SEO title). */
(0,o.__)("Edit your %1$s","wordpress-seo"),y);return(0,s.createElement)(x,{key:e.id,id:e.id,text:e.text,marker:e.marker,bulletColor:l,hasMarksButton:e.hasMarks,hasEditButton:e.hasJumps,hasAIFixes:e.hasAIFixes,ariaLabelMarks:C,ariaLabelEdit:f,pressed:k,suppressedText:"upsell"===e.rating,buttonIdMarks:B,buttonIdEdit:b,onButtonClickMarks:()=>u(e.id,e.marker),onButtonClickEdit:()=>d(e.id),marksButtonClassName:n,editButtonClassName:i,marksButtonStatus:r,hasBetaBadgeLabel:e.hasBetaBadge,isPremium:p,onResultChange:m,markButtonFactory:a,shouldUpsellHighlighting:g,renderAIOptimizeButton:h,renderHighlightingUpsell:c})})))}v.propTypes={results:u().array.isRequired,marksButtonActivatedResult:u().string,marksButtonStatus:u().string,marksButtonClassName:u().string,editButtonClassName:u().string,markButtonFactory:u().func,onMarksButtonClick:u().func,onEditButtonClick:u().func,isPremium:u().bool,onResultChange:u().func,shouldUpsellHighlighting:u().bool,renderHighlightingUpsell:u().func,renderAIOptimizeButton:u().func};const _=p().div`
	width: 100%;
	background-color: white;
	border-bottom: 1px solid transparent; // Avoid parent and child margin collapsing.
`,I=p()(m.Collapsible)`
	margin-bottom: 8px;

	${m.StyledIconsButton} {
		padding: 8px 0;
		color: ${n.colors.$color_blue};
		margin: -2px 8px 0 -2px; // Compensate icon size set to 18px.
	}
`;class N extends r().Component{renderCollapsible(e,t,r){return(0,s.createElement)(I,{initialIsOpen:!0,title:`${e} (${r.length})`,prefixIcon:{icon:"angle-up",color:n.colors.$color_grey_dark,size:"18px"},prefixIconCollapsed:{icon:"angle-down",color:n.colors.$color_grey_dark,size:"18px"},suffixIcon:null,suffixIconCollapsed:null,headingProps:{level:t,fontSize:"13px",fontWeight:"500",color:"#1e1e1e"}},(0,s.createElement)(v,{results:r,marksButtonActivatedResult:this.props.activeMarker,marksButtonStatus:this.props.marksButtonStatus,marksButtonClassName:this.props.marksButtonClassName,editButtonClassName:this.props.editButtonClassName,markButtonFactory:this.props.markButtonFactory,onMarksButtonClick:this.props.onMarkButtonClick,onEditButtonClick:this.props.onEditButtonClick,renderAIOptimizeButton:this.props.renderAIOptimizeButton,isPremium:this.props.isPremium,onResultChange:this.props.onResultChange,shouldUpsellHighlighting:this.props.shouldUpsellHighlighting,renderHighlightingUpsell:this.props.renderHighlightingUpsell}))}render(){const{problemsResults:e,improvementsResults:t,goodResults:r,considerationsResults:n,errorsResults:i,upsellResults:l,headingLevel:a,resultCategoryLabels:u}=this.props,d=i.length,p=e.length,m=t.length,g=n.length,c=r.length,h=l.length,k={errors:(0,o.__)("Errors","wordpress-seo"),problems:(0,o.__)("Problems","wordpress-seo"),improvements:(0,o.__)("Improvements","wordpress-seo"),considerations:(0,o.__)("Considerations","wordpress-seo"),goodResults:(0,o.__)("Good results","wordpress-seo")},B=Object.assign(k,u);return(0,s.createElement)(_,null,d>0&&this.renderCollapsible(B.errors,a,i),p+h>0&&this.renderCollapsible(B.problems,a,[...l,...e]),m>0&&this.renderCollapsible(B.improvements,a,t),g>0&&this.renderCollapsible(B.considerations,a,n),c>0&&this.renderCollapsible(B.goodResults,a,r))}}N.propTypes={onMarkButtonClick:u().func,onEditButtonClick:u().func,problemsResults:u().array,improvementsResults:u().array,goodResults:u().array,considerationsResults:u().array,errorsResults:u().array,upsellResults:u().array,headingLevel:u().number,marksButtonStatus:u().string,marksButtonClassName:u().string,markButtonFactory:u().func,editButtonClassName:u().string,activeMarker:u().string,isPremium:u().bool,resultCategoryLabels:u().shape({errors:u().string,problems:u().string,improvements:u().string,considerations:u().string,goodResults:u().string}),onResultChange:u().func,shouldUpsellHighlighting:u().bool,renderHighlightingUpsell:u().func,renderAIOptimizeButton:u().func},N.defaultProps={onMarkButtonClick:()=>{},onEditButtonClick:()=>{},problemsResults:[],improvementsResults:[],goodResults:[],considerationsResults:[],errorsResults:[],upsellResults:[],headingLevel:4,marksButtonStatus:"enabled",marksButtonClassName:"",markButtonFactory:null,editButtonClassName:"",activeMarker:"",isPremium:!1,resultCategoryLabels:{},onResultChange:()=>{},shouldUpsellHighlighting:!1,renderHighlightingUpsell:()=>{},renderAIOptimizeButton:()=>{}};const A=N,M=p().div`
`,S=p().p`
	font-size: 14px;
`,H=({className:e="seo-assessment",seoAssessmentText:t="SEO Assessment",seoAssessmentItems:r=null,barHeight:o="24px"})=>(0,s.createElement)(M,{className:e},(0,s.createElement)(S,{className:`${e}__text`},t),(0,s.createElement)(m.StackedProgressBar,{className:"progress",items:r,barHeight:o}),(0,s.createElement)(m.ScoreAssessments,{className:"assessments",items:r}));H.propTypes={className:u().string,seoAssessmentText:u().string,seoAssessmentItems:u().arrayOf(u().shape({html:u().string.isRequired,value:u().number.isRequired,color:u().string.isRequired})),barHeight:u().string};const T=H;(window.yoast=window.yoast||{}).analysisReport=t})();