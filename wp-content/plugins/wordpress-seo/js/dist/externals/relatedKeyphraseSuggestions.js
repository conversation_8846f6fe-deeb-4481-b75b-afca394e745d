(()=>{var e={54776:(e,a)=>{var t;!function(){"use strict";var l={}.hasOwnProperty;function s(){for(var e="",a=0;a<arguments.length;a++){var t=arguments[a];t&&(e=n(e,r(t)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return s.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var a="";for(var t in e)l.call(e,t)&&e[t]&&(a=n(a,t));return a}function n(e,a){return a?e?e+" "+a:e+a:e}e.exports?(s.default=s,e.exports=s):void 0===(t=function(){return s}.apply(a,[]))||(e.exports=t)}()}},a={};function t(l){var s=a[l];if(void 0!==s)return s.exports;var r=a[l]={exports:{}};return e[l](r,r.exports,t),r.exports}t.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return t.d(a,{a}),a},t.d=(e,a)=>{for(var l in a)t.o(a,l)&&!t.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:a[l]})},t.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};(()=>{"use strict";t.r(l),t.d(l,{CountrySelector:()=>R,DifficultyBullet:()=>w,IntentBadge:()=>v,KeyphrasesTable:()=>_,Modal:()=>D,PremiumUpsell:()=>O,TableButton:()=>S,TrendGraph:()=>c,UserMessage:()=>z});const e=window.React;var a=t.n(e);const s=window.yoast.propTypes;var r=t.n(s);const n=window.wp.i18n,o=[(0,n.__)("Twelve months ago","wordpress-seo"),(0,n.__)("Eleven months ago","wordpress-seo"),(0,n.__)("Ten months ago","wordpress-seo"),(0,n.__)("Nine months ago","wordpress-seo"),(0,n.__)("Eight months ago","wordpress-seo"),(0,n.__)("Seven months ago","wordpress-seo"),(0,n.__)("Six months ago","wordpress-seo"),(0,n.__)("Five months ago","wordpress-seo"),(0,n.__)("Four months ago","wordpress-seo"),(0,n.__)("Three months ago","wordpress-seo"),(0,n.__)("Two months ago","wordpress-seo"),(0,n.__)("Last month","wordpress-seo")],i=({data:e})=>{if(e.length!==o.length)throw new Error("The number of headers and header labels don't match.");return a().createElement("div",{className:"yst-sr-only"},a().createElement("table",null,a().createElement("caption",null,(0,n.__)("Keyphrase volume in the last 12 months on a scale from 0 to 100.","wordpress-seo")),a().createElement("thead",null,a().createElement("tr",null,o.map(((e,t)=>a().createElement("th",{key:t},e))))),a().createElement("tbody",null,a().createElement("tr",null,e.map(((e,t)=>{return a().createElement("td",{key:t},(l=e,Math.round(100*l)));var l}))))))};i.propTypes={data:r().arrayOf(r().number).isRequired};const c=({data:e})=>{if(12!==e.length){const a=12-e.length;for(let t=0;t<a;t++)e.unshift(0)}const t=Math.max(1,...e.map((e=>e))),l=e.map(((e,a)=>`${a/11*66},${22.2-e/t*22.2+1.8}`)).join(" "),s="0,24 "+l+" 66,24";return a().createElement(a().Fragment,null,a().createElement("svg",{width:66,height:24,viewBox:"0 0 66 24",className:"yst-block",role:"img","aria-hidden":"true",focusable:"false"},a().createElement("polygon",{className:"yst-fill-sky-200",points:s}),a().createElement("polyline",{fill:"none",className:"yst-stroke-blue-500",strokeWidth:1.8,strokeLinejoin:"round",strokeLinecap:"round",points:l})),a().createElement(i,{data:e}))};function u(){return u=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var l in t)({}).hasOwnProperty.call(t,l)&&(e[l]=t[l])}return e},u.apply(null,arguments)}c.propTypes={data:r().arrayOf(r().number).isRequired};const m=window.yoast.uiLibrary,d=window.lodash;var p=t(54776),y=t.n(p);const b={i:{title:(0,n.__)("Informational","wordpress-seo"),description:(0,n.__)("The user wants to find an answer to a specific question.","wordpress-seo")},n:{title:(0,n.__)("Navigational","wordpress-seo"),description:(0,n.__)("The user wants to find a specific page or site.","wordpress-seo")},c:{title:(0,n.__)("Commercial","wordpress-seo"),description:(0,n.__)("The user wants to investigate brands or services.","wordpress-seo")},t:{title:(0,n.__)("Transactional","wordpress-seo"),description:(0,n.__)("The user wants to complete an action (conversion).","wordpress-seo")}},v=({id:e,value:t,className:l=""})=>b[t]?a().createElement(m.TooltipContainer,null,a().createElement(m.TooltipTrigger,{ariaDescribedby:e,className:y()("yst-intent-badge",`yst-intent-badge--${t}`,l)},t),a().createElement(m.TooltipWithContext,{id:e,className:"yst-w-48 yst-text-xs yst-leading-4 yst-font-normal"},a().createElement("div",{className:"yst-font-medium"},b[t].title," "),b[t].description)):null;v.propTypes={id:r().string.isRequired,value:r().oneOf(["i","n","c","t"]).isRequired,className:r().string};const h=[{min:0,max:14,name:"very-easy",tooltip:{title:(0,n.__)("Very easy","wordpress-seo"),description:(0,n.__)("Your chance to start ranking new pages.","wordpress-seo")}},{min:15,max:29,name:"easy",tooltip:{title:(0,n.__)("Easy","wordpress-seo"),description:(0,n.__)("You will need quality content focused on the keyword’s intent.","wordpress-seo")}},{min:30,max:49,name:"possible",tooltip:{title:(0,n.__)("Possible","wordpress-seo"),description:(0,n.__)("You will need well-structured and unique content.","wordpress-seo")}},{min:50,max:69,name:"difficult",tooltip:{title:(0,n.__)("Difficult","wordpress-seo"),description:(0,n.__)("You will need lots of ref. domains and optimized content.","wordpress-seo")}},{min:70,max:84,name:"hard",tooltip:{title:(0,n.__)("Hard","wordpress-seo"),description:(0,n.__)("You will need lots of high-quality ref. domains and optimized content.","wordpress-seo")}},{min:85,max:100,name:"very-hard",tooltip:{title:(0,n.__)("Very hard","wordpress-seo"),description:(0,n.__)("It will take a lot of on-page SEO, link building, and content promotion efforts.","wordpress-seo")}}],w=({value:e,id:t})=>{const l=(e=>{for(const a of h)if(a.min<=e&&e<=a.max)return a})(e);return l?a().createElement(m.TooltipContainer,null,a().createElement(m.TooltipTrigger,{ariaDescribedby:t,className:"yst-flex yst-gap-2 yst-items-center yst-relative yst-w-10"},a().createElement("div",{className:"yst-text-right yst-w-5"},e),a().createElement("div",{className:y()("yst-w-3 yst-h-3 yst-rounded-full",`yst-difficulty--${l.name}`)})),a().createElement(m.TooltipWithContext,{id:t,className:"yst-w-48 yst-text-xs yst-leading-4 yst-font-normal",position:"left"},a().createElement("div",{className:"yst-font-medium"},l.tooltip.title," "),l.tooltip.description)):null};w.propTypes={value:r().number.isRequired,id:r().string.isRequired};const g=({keyword:e="",searchVolume:t="",trends:l=[],keywordDifficultyIndex:s=-1,intent:r=[],renderButton:n=null,relatedKeyphrases:o=[],id:i})=>a().createElement(m.Table.Row,{id:i},a().createElement(m.Table.Cell,null,e),a().createElement(m.Table.Cell,null,a().createElement("div",{className:"yst-flex yst-gap-2"},r.length>0&&r.map(((e,t)=>a().createElement(v,{id:`${i}__intent-${t}-${e}`,key:`${i}-${t}-${e}`,value:e}))))),a().createElement(m.Table.Cell,null,a().createElement("div",{className:"yst-flex yst-justify-end"},t)),a().createElement(m.Table.Cell,null,a().createElement(c,{data:l})),a().createElement(m.Table.Cell,null,a().createElement("div",{className:"yst-flex yst-justify-end"},a().createElement(w,{value:s,id:`${i}__difficulty-index`}))),(0,d.isFunction)(n)&&a().createElement(m.Table.Cell,null,n(e,o)));g.propTypes={keyword:r().string,searchVolume:r().string,trends:r().arrayOf(r().number),keywordDifficultyIndex:r().number,intent:r().arrayOf(r().string),renderButton:r().func,relatedKeyphrases:r().arrayOf(r().shape({key:r().string,keyword:r().string,results:r().array,score:r().number})),id:r().string.isRequired};const f=({withButton:e=!1})=>a().createElement(m.Table.Row,null,a().createElement(m.Table.Cell,{className:"yst-w-44"},a().createElement(m.SkeletonLoader,{className:"yst-w-36 yst-h-5"})),a().createElement(m.Table.Cell,null,a().createElement(m.SkeletonLoader,{className:"yst-w-5 yst-h-5"})),a().createElement(m.Table.Cell,null,a().createElement("div",{className:"yst-flex yst-justify-end"},a().createElement(m.SkeletonLoader,{className:"yst-w-14 yst-h-5"}))),a().createElement(m.Table.Cell,null,a().createElement(m.SkeletonLoader,{className:"yst-w-16 yst-h-5"})),a().createElement(m.Table.Cell,null,a().createElement("div",{className:"yst-flex yst-gap-2 yst-justify-end"},a().createElement(m.SkeletonLoader,{className:"yst-w-4 yst-h-5"}),a().createElement(m.SkeletonLoader,{className:"yst-w-3 yst-h-5"}))),e&&a().createElement(m.Table.Cell,{className:"yst-w-32"},a().createElement("div",{className:"yst-flex yst-justify-end"},a().createElement(m.SkeletonLoader,{className:"yst-w-16 yst-h-7"}))));f.propTypes={withButton:r().bool};const E=["i","n","t","c"],_=({columnNames:e=[],data:t=[],renderButton:l=null,relatedKeyphrases:s=[],className:r="",userLocale:o="en",isPending:i=!1,idPrefix:c="yoast-seo"})=>{let p;try{p=new Intl.NumberFormat(o,{notation:"compact",compactDisplay:"short"})}catch(e){p=new Intl.NumberFormat(navigator.language.split("-")[0],{notation:"compact",compactDisplay:"short"})}const y=null==t?void 0:t.map((a=>((e,a,t)=>{const l={};return e.forEach(((e,s)=>{switch(e){case"Trends":l.trends=a[s].split(",").map((e=>parseFloat(e)));break;case"Intent":l.intent=a[s].split(",").map((e=>E[Number(e)]));break;case"Keyword Difficulty Index":l.keywordDifficultyIndex=Number(a[s]);break;case"Search Volume":l.searchVolume=t.format(a[s]);break;default:l[e.toLowerCase()]=a[s]}})),l})(e,a,p)));return y&&0!==y.length||i?a().createElement(m.Table,{className:r},a().createElement(m.Table.Head,null,a().createElement(m.Table.Row,null,a().createElement(m.Table.Header,{className:"yst-text-start"},(0,n.__)("Related keyphrase","wordpress-seo")),a().createElement(m.Table.Header,{className:"yst-text-start"},(0,n.__)("Intent","wordpress-seo")),a().createElement(m.Table.Header,null,a().createElement("div",{className:"yst-flex yst-justify-end"},(0,n.__)("Volume","wordpress-seo"))),a().createElement(m.Table.Header,{className:"yst-text-start"},(0,n.__)("Trend","wordpress-seo")),a().createElement(m.Table.Header,{className:"yst-whitespace-nowrap"},a().createElement("div",{className:"yst-flex yst-justify-end"},(0,n.__)("Difficulty %","wordpress-seo"))),l&&a().createElement(m.Table.Header,null,a().createElement("div",{className:"yst-flex yst-justify-end"},a().createElement("div",{className:"yst-text-end yst-w-[88px]"},(0,n.__)("Add keyphrase","wordpress-seo")))))),a().createElement(m.Table.Body,null,!i&&y&&y.map(((e,t)=>a().createElement(g,u({key:`${c}-related-keyphrase-${t}`,id:`${c}-related-keyphrase-${t}`,renderButton:l,relatedKeyphrases:s},e)))),i&&Array.from({length:10},((e,t)=>a().createElement(f,{key:`loading-row-${t}`,withButton:(0,d.isFunction)(l)}))))):null};_.propTypes={columnNames:r().arrayOf(r().string),data:r().arrayOf(r().arrayOf(r().string)),relatedKeyphrases:r().arrayOf(r().shape({key:r().string,keyword:r().string,results:r().array,score:r().number})),renderButton:r().func,className:r().string,isPending:r().bool,userLocale:r().string,idPrefix:r().string},_.displayName="KeyphrasesTable";const N=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4v16m8-8H4"}))})),k=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"}))})),T=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"}))})),x=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"}))})),C={add:{button:{Icon:N,label:(0,n.__)("Add","wordpress-seo"),variant:"secondary"},success:{Icon:k,label:(0,n.__)("Added!","wordpress-seo")}},remove:{button:{Icon:T,label:(0,n.__)("Remove","wordpress-seo"),variant:"tertiary"},success:{Icon:x,label:(0,n.__)("Removed!","wordpress-seo")}}},L=({variant:e="add",className:t=""})=>{const l=C[e].success.Icon;return a().createElement("div",{role:"alert",className:y()("yst-success-message yst-animate-appear-disappear",`yst-success-message-${e}`,t)},a().createElement(l,{className:"yst-success-icon"}),C[e].success.label)};L.propTypes={variant:r().oneOf(["add","remove"]),className:r().string};const S=(0,e.forwardRef)((({variant:e="add",className:t="",...l},s)=>{const r=C[e].button.Icon;return a().createElement(m.Button,u({},l,{ref:s,variant:C[e].button.variant,size:"small",className:y()("yst-table-button",t)}),a().createElement(r,{className:"yst-button-icon yst--mx-1"}),C[e].button.label)}));S.propTypes={variant:r().oneOf(["add","remove"]).isRequired,className:r().string},S.displayName="TableButton",S.SuccessMessage=L;const M=[{value:"us",label:"United States - US"},{value:"uk",label:"United Kingdom - UK"},{value:"ca",label:"Canada - CA"},{value:"ru",label:"Russia - RU"},{value:"de",label:"Germany - DE"},{value:"fr",label:"France - FR"},{value:"es",label:"Spain - ES"},{value:"it",label:"Italy - IT"},{value:"br",label:"Brazil - BR"},{value:"au",label:"Australia - AU"},{value:"ar",label:"Argentina - AR"},{value:"be",label:"Belgium - BE"},{value:"ch",label:"Switzerland - CH"},{value:"dk",label:"Denmark - DK"},{value:"fi",label:"Finland - FI"},{value:"hk",label:"Hong Kong - HK"},{value:"ie",label:"Ireland - IE"},{value:"il",label:"Israel - IL"},{value:"mx",label:"Mexico - MX"},{value:"nl",label:"Netherlands - NL"},{value:"no",label:"Norway - NO"},{value:"pl",label:"Poland - PL"},{value:"se",label:"Sweden - SE"},{value:"sg",label:"Singapore - SG"},{value:"tr",label:"Turkey - TR"},{value:"jp",label:"Japan - JP"},{value:"in",label:"India - IN"},{value:"hu",label:"Hungary - HU"},{value:"af",label:"Afghanistan - AF"},{value:"al",label:"Albania - AL"},{value:"dz",label:"Algeria - DZ"},{value:"ao",label:"Angola - AO"},{value:"am",label:"Armenia - AM"},{value:"at",label:"Austria - AT"},{value:"az",label:"Azerbaijan - AZ"},{value:"bh",label:"Bahrain - BH"},{value:"bd",label:"Bangladesh - BD"},{value:"by",label:"Belarus - BY"},{value:"bz",label:"Belize - BZ"},{value:"bo",label:"Bolivia - BO"},{value:"ba",label:"Bosnia and Herzegovina - BA"},{value:"bw",label:"Botswana - BW"},{value:"bn",label:"Brunei - BN"},{value:"bg",label:"Bulgaria - BG"},{value:"cv",label:"Cabo Verde - CV"},{value:"kh",label:"Cambodia - KH"},{value:"cm",label:"Cameroon - CM"},{value:"cl",label:"Chile - CL"},{value:"co",label:"Colombia - CO"},{value:"cr",label:"Costa Rica - CR"},{value:"hr",label:"Croatia - HR"},{value:"cy",label:"Cyprus - CY"},{value:"cz",label:"Czech Republic - CZ"},{value:"cd",label:"Congo - CD"},{value:"do",label:"Dominican Republic - DO"},{value:"ec",label:"Ecuador - EC"},{value:"eg",label:"Egypt - EG"},{value:"sv",label:"El Salvador - SV"},{value:"ee",label:"Estonia - EE"},{value:"et",label:"Ethiopia - ET"},{value:"ge",label:"Georgia - GE"},{value:"gh",label:"Ghana - GH"},{value:"gr",label:"Greece - GR"},{value:"gt",label:"Guatemala - GT"},{value:"gy",label:"Guyana - GY"},{value:"ht",label:"Haiti - HT"},{value:"hn",label:"Honduras - HN"},{value:"is",label:"Iceland - IS"},{value:"id",label:"Indonesia - ID"},{value:"jm",label:"Jamaica - JM"},{value:"jo",label:"Jordan - JO"},{value:"kz",label:"Kazakhstan - KZ"},{value:"kw",label:"Kuwait - KW"},{value:"lv",label:"Latvia - LV"},{value:"lb",label:"Lebanon - LB"},{value:"lt",label:"Lithuania - LT"},{value:"lu",label:"Luxembourg - LU"},{value:"mg",label:"Madagascar - MG"},{value:"my",label:"Malaysia - MY"},{value:"mt",label:"Malta - MT"},{value:"mu",label:"Mauritius - MU"},{value:"md",label:"Moldova - MD"},{value:"mn",label:"Mongolia - MN"},{value:"me",label:"Montenegro - ME"},{value:"ma",label:"Morocco - MA"},{value:"mz",label:"Mozambique - MZ"},{value:"na",label:"Namibia - NA"},{value:"np",label:"Nepal - NP"},{value:"nz",label:"New Zealand - NZ"},{value:"ni",label:"Nicaragua - NI"},{value:"ng",label:"Nigeria - NG"},{value:"om",label:"Oman - OM"},{value:"py",label:"Paraguay - PY"},{value:"pe",label:"Peru - PE"},{value:"ph",label:"Philippines - PH"},{value:"pt",label:"Portugal - PT"},{value:"ro",label:"Romania - RO"},{value:"sa",label:"Saudi Arabia - SA"},{value:"sn",label:"Senegal - SN"},{value:"rs",label:"Serbia - RS"},{value:"sk",label:"Slovakia - SK"},{value:"si",label:"Slovenia - SI"},{value:"za",label:"South Africa - ZA"},{value:"kr",label:"South Korea - KR"},{value:"lk",label:"Sri Lanka - LK"},{value:"th",label:"Thailand - TH"},{value:"bs",label:"Bahamas - BS"},{value:"tt",label:"Trinidad and Tobago - TT"},{value:"tn",label:"Tunisia - TN"},{value:"ua",label:"Ukraine - UA"},{value:"ae",label:"United Arab Emirates - AE"},{value:"uy",label:"Uruguay - UY"},{value:"ve",label:"Venezuela - VE"},{value:"vn",label:"Vietnam - VN"},{value:"zm",label:"Zambia - ZM"},{value:"zw",label:"Zimbabwe - ZW"},{value:"ly",label:"Libya - LY"}],R=({countryCode:t="us",activeCountryCode:l="us",onChange:s,onClick:r,className:o="",userLocale:i="en"})=>{let c;try{c=new Intl.DisplayNames([i],{type:"region"})}catch(e){c=new Intl.DisplayNames([navigator.language.split("-")[0]],{type:"region"})}const[u,p]=(0,e.useState)(""),b=(0,e.useMemo)((()=>(0,d.filter)(M,(e=>!u||c.of(e.value.toUpperCase()).toLowerCase().startsWith(u)))),[u]),v=(0,e.useCallback)((e=>{p(e.target.value.toLowerCase())}),[p]);return a().createElement("div",{className:y()("yst-flex yst-items-end yst-gap-2",o)},a().createElement(m.AutocompleteField,{id:"yst-country-selector__select",label:(0,n.__)("Show results for:","wordpress-seo"),value:t,selectedLabel:t?c.of(t.toUpperCase()):"",onChange:s,onQueryChange:v,className:"sm:yst-w-96"},b.map((e=>a().createElement(m.AutocompleteField.Option,{key:e.value,value:e.value},c.of(e.value.toUpperCase()))))),a().createElement(m.Button,{id:"yst-country-selector__button",size:"large",variant:l===t?"secondary":"primary",onClick:r},(0,n.__)("Change country","wordpress-seo")))};R.propTypes={countryCode:r().string,activeCountryCode:r().string,onChange:r().func.isRequired,onClick:r().func.isRequired,className:r().string,userLocale:r().string};const B=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"}))})),O=({url:e,className:t=""})=>a().createElement("div",{className:t},a().createElement("p",null,(0,n.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,n.__)("You’ll reach more people with multiple keyphrases! Want to quickly add these related keyphrases to the %s analyses for even better content optimization?","wordpress-seo"),"Yoast SEO")),a().createElement(m.Button,{variant:"upsell",as:"a",href:e,className:"yst-mt-4 yst-gap-2",target:"_blank"},a().createElement(B,{className:"yst-w-4 yst-h-4 yst-text-amber-900"}),(0,n.sprintf)(/* translators: %s: Expands to "Yoast SEO Premium". */
(0,n.__)("Explore %s!","wordpress-seo"),"Yoast SEO Premium"),a().createElement("span",{className:"yst-sr-only"},(0,n.__)("(Opens in a new browser tab)","wordpress-seo"))));O.propTypes={url:r().string.isRequired,className:r().string};const A=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8l4 4m0 0l-4 4m4-4H3"}))})),j=({upsellLink:e="",className:t=""})=>a().createElement(m.Alert,{variant:"warning",className:t},a().createElement("div",{className:"yst-flex yst-flex-col yst-items-start"},(0,n.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,n.__)("You've reached your request limit for today. Check back tomorrow or upgrade your plan over at %s.","wordpress-seo"),"Semrush"),e&&a().createElement(m.Button,{variant:"upsell",className:"yst-mt-3 yst-gap-1.5",as:"a",href:e,target:"_blank"},(0,n.sprintf)(/* translators: %s : Expands to "Semrush". */
(0,n.__)("Upgrade your %s plan","wordpress-seo"),"Semrush"),a().createElement(A,{className:"yst-w-4 yst-h-4 yst-text-amber-900 rtl:yst-rotate-180"}))));j.propTypes={upsellLink:r().string,className:r().string};const H=({className:e=""})=>a().createElement(m.Alert,{variant:"error",className:e},(0,n.__)("We've encountered a problem trying to get related keyphrases. Please try again later.","wordpress-seo"));H.propTypes={className:r().string};const I=({className:e=""})=>a().createElement(m.Alert,{variant:"info",className:e},(0,n.__)("Sorry, there's no data available for that keyphrase/country combination.","wordpress-seo"));I.propTypes={className:r().string};const q=({className:e=""})=>a().createElement(m.Alert,{variant:"warning",className:e},(0,n.sprintf)(/* translators: %s: Expands to "Yoast SEO". */
(0,n.__)("You've reached the maximum amount of 4 related keyphrases. You can change or remove related keyphrases in the %s metabox or sidebar.","wordpress-seo"),"Yoast SEO"));q.propTypes={className:r().string};const z=({variant:e="requestLimitReached",upsellLink:t="",className:l=""})=>{switch(e){case"requestLimitReached":return a().createElement(j,{upsellLink:t,className:l});case"requestFailed":return a().createElement(H,{className:l});case"requestEmpty":return a().createElement(I,{className:l});case"maxRelatedKeyphrases":return a().createElement(q,{className:l});default:return null}};z.propTypes={variant:r().oneOf(["requestLimitReached","requestFailed","requestEmpty","maxRelatedKeyphrases"]),upsellLink:r().string,className:r().string};const P=e.forwardRef((function(a,t){return e.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor","aria-hidden":"true",ref:t},a),e.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"}))})),K=()=>a().createElement("svg",{className:"yst-w-[17px] yst-h-[17px] yst-fill-primary-500 yst-mt-[3px]",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 425 456.27",role:"img","aria-hidden":"true",focusable:"false"},a().createElement("path",{d:"M73 405.26a66.79 66.79 0 0 1-6.54-1.7 64.75 64.75 0 0 1-6.28-2.31c-1-.42-2-.89-3-1.37-1.49-.72-3-1.56-4.77-2.56-1.5-.88-2.71-1.64-3.83-2.39-.9-.61-1.8-1.26-2.68-1.92a70.154 70.154 0 0 1-5.08-4.19 69.21 69.21 0 0 1-8.4-9.17c-.92-1.2-1.68-2.25-2.35-3.24a70.747 70.747 0 0 1-3.44-5.64 68.29 68.29 0 0 1-8.29-32.55V142.13a68.26 68.26 0 0 1 8.29-32.55c1-1.92 2.21-3.82 3.44-5.64s2.55-3.58 4-5.27a69.26 69.26 0 0 1 14.49-13.25C50.37 84.19 52.27 83 54.2 82A67.59 67.59 0 0 1 73 75.09a68.75 68.75 0 0 1 13.75-1.39h169.66L263 55.39H86.75A86.84 86.84 0 0 0 0 142.13v196.09A86.84 86.84 0 0 0 86.75 425h11.32v-18.35H86.75A68.75 68.75 0 0 1 73 405.26zM368.55 60.85l-1.41-.53-6.41 17.18 1.41.53a68.06 68.06 0 0 1 8.66 4c1.93 1 3.82 2.2 5.65 3.43A69.19 69.19 0 0 1 391 98.67c1.4 1.68 2.72 3.46 3.95 5.27s2.39 3.72 3.44 5.64a68.29 68.29 0 0 1 8.29 32.55v264.52H233.55l-.44.76c-3.07 5.37-6.26 10.48-9.49 15.19L222 425h203V142.13a87.2 87.2 0 0 0-56.45-81.28z"}),a().createElement("path",{d:"M119.8 408.28v46c28.49-1.12 50.73-10.6 69.61-29.58 19.45-19.55 36.17-50 52.61-96L363.94 1.9H305l-98.25 272.89-48.86-153h-54l71.7 184.18a75.67 75.67 0 0 1 0 55.12c-7.3 18.68-20.25 40.66-55.79 47.19z",stroke:"#000",strokeMiterlimit:"10",strokeWidth:"3.81"})),D=({isOpen:e,onClose:t,insightsLink:l,learnMoreLink:s,children:r=null})=>a().createElement(m.Modal,{onClose:t,isOpen:e},a().createElement(m.Modal.Panel,{className:"yst-p-0 yst-max-w-2xl"},a().createElement(m.Modal.Container.Header,{className:"yst-flex yst-gap-3 yst-p-6 yst-border-b-slate-200 yst-border-b yst-flex-row"},a().createElement(K,null),a().createElement(m.Modal.Title,{as:"h3",className:"yst-text-lg yst-font-medium"},(0,n.__)("Related keyphrases","wordpress-seo"))),a().createElement(m.Modal.Container.Content,{className:"yst-related-keyphrase-modal-content yst-m-0"},r),a().createElement(m.Modal.Container.Footer,{className:"yst-p-6 yst-border-t yst-border-t-slate-200 yst-flex yst-justify-between"},a().createElement(m.Link,{href:l,className:"yst-modal-footer-link",target:"_blank"},(0,n.sprintf)(/* translators: %s expands to Semrush */
(0,n.__)("Get more insights at %s","wordpress-seo"),"Semrush"),a().createElement("span",{className:"yst-sr-only"},(0,n.__)("(Opens in a new browser tab)","wordpress-seo")),a().createElement(P,{className:"yst-link-icon rtl:yst-rotate-[270deg]"})),a().createElement(m.Link,{href:s,className:"yst-modal-footer-link yst-text-primary-500",target:"_blank"},(0,n.__)("Learn more about the metrics","wordpress-seo"),a().createElement("span",{className:"yst-sr-only"},(0,n.__)("(Opens in a new browser tab)","wordpress-seo")),a().createElement(A,{className:"yst-link-icon rtl:yst-rotate-180"})))));D.propTypes={isOpen:r().bool.isRequired,onClose:r().func.isRequired,insightsLink:r().string.isRequired,learnMoreLink:r().string.isRequired,children:r().node}})(),(window.yoast=window.yoast||{}).relatedKeyphraseSuggestions=l})();