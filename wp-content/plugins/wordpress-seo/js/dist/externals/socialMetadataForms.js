(()=>{"use strict";var e={17966:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.withCaretStyle=t.default=void 0;var r=i(65736),o=i(81413),a=i(23695),n=i(10224),l=i(37188),s=i(92819),d=f(i(85890)),c=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var i=p(t);if(i&&i.has(e))return i.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var n=o?Object.getOwnPropertyDescriptor(e,a):null;n&&(n.get||n.set)?Object.defineProperty(r,a,n):r[a]=e[a]}return r.default=e,i&&i.set(e,r),r}(i(99196)),u=f(i(98487));function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,i=new WeakMap;return(p=function(e){return e?i:t})(e)}function f(e){return e&&e.__esModule?e:{default:e}}const g=e=>e?l.colors.$color_snippet_focus:l.colors.$color_snippet_hover,m=u.default.div`
	position: relative;

	${e=>!e.isPremium&&"\n\t\t.yoast-image-select__preview {\n\t\t\twidth: 130px;\n\t\t\tmin-height: 72px;\n\t\t\tmax-height: 130px;\n\t\t}\n\t"};
`;m.propTypes={isPremium:d.default.bool},m.defaultProps={isPremium:!1};const _=u.default.div`
	display: ${e=>e.isActive||e.isHovered?"block":"none"};

	::before {
		position: absolute;
		top: -2px;
		${(0,a.getDirectionalStyle)("left","right")}: -25px;
		width: 24px;
		height: 24px;
		background-image: url(
		${e=>(0,a.getDirectionalStyle)((0,l.angleRight)(g(e.isActive)),(0,l.angleLeft)(g(e.isActive)))}
		);
		color: ${e=>g(e.isActive)};
		background-size: 24px;
		background-repeat: no-repeat;
		background-position: center;
		content: "";
	}
`;_.propTypes={isActive:d.default.bool,isHovered:d.default.bool},_.defaultProps={isActive:!1,isHovered:!1};const E=e=>{function t({isActive:t,isHovered:i,isPremium:r=!1,...o}){return c.default.createElement(m,{isPremium:r},c.default.createElement(_,{isActive:t,isHovered:i}),c.default.createElement(e,o))}return t.propTypes={isActive:d.default.bool.isRequired,isHovered:d.default.bool.isRequired,isPremium:d.default.bool},t};t.withCaretStyle=E;const I=E(o.ImageSelect);class S extends c.Component{constructor(e){super(e),this.onImageEnter=e.onMouseHover.bind(this,"image"),this.onTitleEnter=e.onMouseHover.bind(this,"title"),this.onDescriptionEnter=e.onMouseHover.bind(this,"description"),this.onLeave=e.onMouseHover.bind(this,""),this.onImageSelectBlur=e.onSelect.bind(this,""),this.onSelectTitleEditor=this.onSelectEditor.bind(this,"title"),this.onSelectDescriptionEditor=this.onSelectEditor.bind(this,"description"),this.onDeselectEditor=this.onSelectEditor.bind(this,""),this.onTitleEditorRef=this.onSetEditorRef.bind(this,"title"),this.onDescriptionEditorRef=this.onSetEditorRef.bind(this,"description")}onSelectEditor(e){this.props.onSelect(e)}onSetEditorRef(e,t){this.props.setEditorRef(e,t)}getFieldsTitles(e){return"Twitter"===e?{imageSelectTitle:(0,r.__)("Twitter image","wordpress-seo"),titleEditorTitle:(0,r.__)("Twitter title","wordpress-seo"),descEditorTitle:(0,r.__)("Twitter description","wordpress-seo")}:"X"===e?{imageSelectTitle:(0,r.__)("X image","wordpress-seo"),titleEditorTitle:(0,r.__)("X title","wordpress-seo"),descEditorTitle:(0,r.__)("X description","wordpress-seo")}:{imageSelectTitle:(0,r.__)("Social image","wordpress-seo"),titleEditorTitle:(0,r.__)("Social title","wordpress-seo"),descEditorTitle:(0,r.__)("Social description","wordpress-seo")}}render(){const{socialMediumName:e,onSelectImageClick:t,onRemoveImageClick:i,title:r,titleInputPlaceholder:o,description:l,descriptionInputPlaceholder:s,onTitleChange:d,onDescriptionChange:u,onReplacementVariableSearchChange:p,hoveredField:f,activeField:g,isPremium:m,replacementVariables:_,recommendedReplacementVariables:E,imageWarnings:S,imageUrl:h,imageFallbackUrl:T,imageAltText:v,idSuffix:b}=this.props,w=this.getFieldsTitles(e),A=!!h,y=w.imageSelectTitle,O=w.titleEditorTitle,P=w.descEditorTitle,C=e.toLowerCase();return c.default.createElement(c.Fragment,null,c.default.createElement(I,{label:y,onClick:t,onRemoveImageClick:i,warnings:S,imageSelected:A,onMouseEnter:this.onImageEnter,onMouseLeave:this.onLeave,isActive:"image"===g,isHovered:"image"===f,imageUrl:h,usingFallback:!h&&""!==T,imageAltText:v,hasPreview:!m,imageUrlInputId:(0,a.join)([C,"url-input",b]),selectImageButtonId:(0,a.join)([C,"select-button",b]),replaceImageButtonId:(0,a.join)([C,"replace-button",b]),removeImageButtonId:(0,a.join)([C,"remove-button",b]),isPremium:m}),c.default.createElement(n.ReplacementVariableEditor,{onChange:d,content:r,placeholder:o,replacementVariables:_,recommendedReplacementVariables:E,type:"title",fieldId:(0,a.join)([C,"title-input",b]),label:O,onMouseEnter:this.onTitleEnter,onMouseLeave:this.onLeave,onSearchChange:p,isActive:"title"===g,isHovered:"title"===f,withCaret:!0,onFocus:this.onSelectTitleEditor,onBlur:this.onDeselectEditor,editorRef:this.onTitleEditorRef}),c.default.createElement(n.ReplacementVariableEditor,{onChange:u,content:l,placeholder:s,replacementVariables:_,recommendedReplacementVariables:E,type:"description",fieldId:(0,a.join)([C,"description-input",b]),label:P,onMouseEnter:this.onDescriptionEnter,onMouseLeave:this.onLeave,onSearchChange:p,isActive:"description"===g,isHovered:"description"===f,withCaret:!0,onFocus:this.onSelectDescriptionEditor,onBlur:this.onDeselectEditor,editorRef:this.onDescriptionEditorRef}))}}S.propTypes={socialMediumName:d.default.oneOf(["Twitter","X","Social"]).isRequired,onSelectImageClick:d.default.func.isRequired,onRemoveImageClick:d.default.func.isRequired,title:d.default.string.isRequired,description:d.default.string.isRequired,onTitleChange:d.default.func.isRequired,onDescriptionChange:d.default.func.isRequired,onReplacementVariableSearchChange:d.default.func,isPremium:d.default.bool,hoveredField:d.default.string,activeField:d.default.string,onSelect:d.default.func,replacementVariables:n.replacementVariablesShape,recommendedReplacementVariables:d.default.arrayOf(d.default.string),imageWarnings:d.default.array,imageUrl:d.default.string,imageFallbackUrl:d.default.string,imageAltText:d.default.string,titleInputPlaceholder:d.default.string,descriptionInputPlaceholder:d.default.string,setEditorRef:d.default.func,onMouseHover:d.default.func,idSuffix:d.default.string},S.defaultProps={replacementVariables:[],recommendedReplacementVariables:[],imageWarnings:[],hoveredField:"",activeField:"",onSelect:s.noop,onReplacementVariableSearchChange:null,imageUrl:"",imageFallbackUrl:"",imageAltText:"",titleInputPlaceholder:"",descriptionInputPlaceholder:"",isPremium:!1,setEditorRef:s.noop,onMouseHover:s.noop,idSuffix:""},t.default=S},62815:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TWITTER_IMAGE_SIZES=t.FACEBOOK_IMAGE_SIZES=void 0,t.TWITTER_IMAGE_SIZES={squareWidth:125,squareHeight:125,landscapeWidth:506,landscapeHeight:265,aspectRatio:50.2},t.FACEBOOK_IMAGE_SIZES={squareWidth:158,squareHeight:158,landscapeWidth:527,landscapeHeight:273,portraitWidth:158,portraitHeight:237,aspectRatio:52.2,largeThreshold:{width:446,height:233}}},60015:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(62815);t.default=function(e){const{largeThreshold:t}=r.FACEBOOK_IMAGE_SIZES;return e.height>e.width?"portrait":e.width<t.width||e.height<t.height||e.height===e.width?"square":"landscape"}},31357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.setSocialPreviewTitle=t.setSocialPreviewImageUrl=t.setSocialPreviewImageType=t.setSocialPreviewImageId=t.setSocialPreviewImage=t.setSocialPreviewDescription=t.clearSocialPreviewImage=t.SET_SOCIAL_TITLE=t.SET_SOCIAL_IMAGE_URL=t.SET_SOCIAL_IMAGE_TYPE=t.SET_SOCIAL_IMAGE_ID=t.SET_SOCIAL_IMAGE=t.SET_SOCIAL_DESCRIPTION=t.CLEAR_SOCIAL_IMAGE=void 0;const i=t.SET_SOCIAL_TITLE="SET_SOCIAL_TITLE",r=t.SET_SOCIAL_DESCRIPTION="SET_SOCIAL_DESCRIPTION",o=t.SET_SOCIAL_IMAGE_URL="SET_SOCIAL_IMAGE_URL",a=t.SET_SOCIAL_IMAGE_TYPE="SET_SOCIAL_IMAGE_TYPE",n=t.SET_SOCIAL_IMAGE_ID="SET_SOCIAL_IMAGE_ID",l=t.SET_SOCIAL_IMAGE="SET_SOCIAL_IMAGE",s=t.CLEAR_SOCIAL_IMAGE="CLEAR_SOCIAL_IMAGE";t.setSocialPreviewTitle=(e,t)=>({type:i,platform:t,title:e}),t.setSocialPreviewDescription=(e,t)=>({type:r,platform:t,description:e}),t.setSocialPreviewImageUrl=(e,t)=>({type:o,platform:t,imageUrl:e}),t.setSocialPreviewImageType=(e,t)=>({type:a,platform:t,imageType:e}),t.setSocialPreviewImageId=(e,t)=>({type:n,platform:t,imageId:e}),t.setSocialPreviewImage=(e,t)=>({type:l,platform:t,image:e}),t.clearSocialPreviewImage=e=>({type:s,platform:e})},1499:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(7185),o=i(31357);const a={title:"",description:"",warnings:[],image:{bytes:null,type:null,height:null,width:null,url:"",id:null,alt:""}};function n(e=a,t){switch(t.type){case o.SET_SOCIAL_TITLE:return{...e,title:t.title};case o.SET_SOCIAL_DESCRIPTION:return{...e,description:t.description};case o.SET_SOCIAL_IMAGE:return{...e,image:{...t.image}};case o.SET_SOCIAL_IMAGE_URL:return{...e,image:{...e.image,url:t.imageUrl}};case o.SET_SOCIAL_IMAGE_TYPE:return{...e,image:{...e.image,type:t.imageType}};case o.SET_SOCIAL_IMAGE_ID:return{...e,image:{...e.image,id:t.imageId}};case o.CLEAR_SOCIAL_IMAGE:return{...e,image:{bytes:null,type:null,height:null,width:null,url:"",id:null,alt:""}};default:return e}}function l(e,t){return(i,r)=>{const{platform:o}=r;return void 0===i?a:o!==t?i:e(i,r)}}const s=(0,r.combineReducers)({facebook:l(n,"facebook"),twitter:l(n,"twitter")});t.default=s},38166:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"socialReducer",{enumerable:!0,get:function(){return o.default}});var r,o=(r=i(1499))&&r.__esModule?r:{default:r}},31715:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(92819);t.default=e=>{const t={getFacebookData:t=>(0,r.get)(t,`${e}.facebook`,{}),getFacebookTitle:e=>t.getFacebookData(e).title,getFacebookDescription:e=>t.getFacebookData(e).description,getFacebookImageUrl:e=>t.getFacebookData(e).image.url,getFacebookImageType:e=>t.getFacebookData(e).image.type,getTwitterData:t=>(0,r.get)(t,`${e}.twitter`,{}),getTwitterTitle:e=>t.getTwitterData(e).title,getTwitterDescription:e=>t.getTwitterData(e).description,getTwitterImageUrl:e=>t.getTwitterData(e).image.url,getTwitterImageType:e=>t.getTwitterData(e).image.type};return t}},99196:e=>{e.exports=window.React},92819:e=>{e.exports=window.lodash},65736:e=>{e.exports=window.wp.i18n},81413:e=>{e.exports=window.yoast.componentsNew},23695:e=>{e.exports=window.yoast.helpers},85890:e=>{e.exports=window.yoast.propTypes},7185:e=>{e.exports=window.yoast.redux},10224:e=>{e.exports=window.yoast.replacementVariableEditor},37188:e=>{e.exports=window.yoast.styleGuide},98487:e=>{e.exports=window.yoast.styledComponents}},t={};function i(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,i),a.exports}var r={};(()=>{var e=r;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"FACEBOOK_IMAGE_SIZES",{enumerable:!0,get:function(){return a.FACEBOOK_IMAGE_SIZES}}),Object.defineProperty(e,"SocialMetadataPreviewForm",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"TWITTER_IMAGE_SIZES",{enumerable:!0,get:function(){return a.TWITTER_IMAGE_SIZES}}),e.actions=void 0,Object.defineProperty(e,"determineFacebookImageMode",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"selectorsFactory",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"socialReducer",{enumerable:!0,get:function(){return l.socialReducer}});var t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var i=c(t);if(i&&i.has(e))return i.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var n=o?Object.getOwnPropertyDescriptor(e,a):null;n&&(n.get||n.set)?Object.defineProperty(r,a,n):r[a]=e[a]}return r.default=e,i&&i.set(e,r),r}(i(31357));e.actions=t;var o=d(i(31715)),a=i(62815),n=d(i(60015)),l=i(38166),s=d(i(17966));function d(e){return e&&e.__esModule?e:{default:e}}function c(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,i=new WeakMap;return(c=function(e){return e?i:t})(e)}})(),(window.yoast=window.yoast||{}).socialMetadataForms=r})();