(()=>{var e={61117:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n(57147);const r=(e,t)=>{const n=((e,t)=>{for(const n in t)Object.hasOwn(t,n)&&(void 0!==e[n]&&""!==e[n]||(e[n]=t[n]));return e})(e,{dataType:"json",method:"POST",contentType:"application/json"});return void 0===n.headers&&""===n.headers||((e,t)=>{"jquery"===e&&Object.assign(t,{beforeSend:e=>{jQuery.each(t.headers,((t,n)=>{e.setRequestHeader(t,n)}))}}),"fetch"===e&&"json"===t.dataType&&Object.assign(t.headers,{Accepts:"application/json","Content-Type":"application/json"})})(t,n),"json"===n.dataType&&(n.data=JSON.stringify(n.data)),"fetch"===t&&Object.assign(n,{body:n.data}),n};t.default=(e,t)=>"undefined"!=typeof jQuery&&jQuery&&jQuery.ajax?((e,t)=>(Object.assign(t,{url:e}),new Promise(((e,n)=>{jQuery.ajax(t).done((t=>{e(t)})).fail((()=>{n("Wrong request")}))}))))(e,r(t,"jquery")):((e,t)=>{const n=fetch(e,t);return new Promise(((e,t)=>{n.then((n=>200===n.status?e(n.json()):t("Response status is not 200"))).catch((()=>t("Wrong request")))}))})(e,r(t,"fetch"))},70636:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.StyledSvg=void 0;var r=s(n(85890)),o=s(n(99196)),i=s(n(98487)),a=n(92819);function s(e){return e&&e.__esModule?e:{default:e}}const u=t.StyledSvg=i.default.svg`
	width: ${e=>e.size};
	height: ${e=>e.size};
	flex: none;
`;class l extends o.default.Component{render(){const{iconSet:e,icon:t,className:n,color:r,size:i}=this.props,s=e[t];if(!s)return console.warn(`Invalid icon name ("${t}") passed to the SvgIcon component.`),null;const l=s.path,c=s.viewbox,f=["yoast-svg-icon","yoast-svg-icon-"+t,n].filter(Boolean).join(" "),p=s.CustomComponent?s.CustomComponent:u;return o.default.createElement(p,{"aria-hidden":!0,role:"img",focusable:"false",size:i,className:f,xmlns:"http://www.w3.org/2000/svg",viewBox:c,fill:r},(0,a.isArray)(l)?l:o.default.createElement("path",{d:l}))}}l.propTypes={icon:r.default.string.isRequired,iconSet:r.default.object.isRequired,color:r.default.string,size:r.default.string,className:r.default.string},l.defaultProps={size:"16px",color:"currentColor",className:""},t.default=e=>{const t=({icon:t,className:n="",color:r="currentColor",size:i="16px"})=>o.default.createElement(l,{iconSet:e,icon:t,className:n,color:r,size:i});return t.propTypes={icon:r.default.string.isRequired,color:r.default.string,size:r.default.string,className:r.default.string},t}},47305:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return(0,r.default)(e,t,i)};var r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=o(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(r,a,s):r[a]=e[a]}return r.default=e,n&&n.set(e,r),r}(n(56362));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}function i(e,t,n){const o={};return o.id=(0,r.getXPathText)("child::content:slug",e,t,n),o.title=(0,r.getXPathText)("child::title",e,t),o.link=(0,r.getXPathText)("child::link",e,t),o.content=(0,r.getXPathText)("child::content:encoded",e,t,n),o.image=(0,r.getXPathText)("child::content:image",e,t,n),o.ctaButtonCopy=(0,r.getXPathText)("child::content:cta_button_copy",e,t,n),o.ctaButtonType=(0,r.getXPathText)("child::content:cta_button_type",e,t,n),o.ctaButtonUrl=(0,r.getXPathText)("child::content:cta_button_url",e,t,n),o.readMoreLinkText=(0,r.getXPathText)("child::content:read_more_link_text",e,t,n),o.isFree=(0,r.getXPathText)("child::content:is_free",e,t,n),o.isBundle=(0,r.getXPathText)("child::content:is_bundle",e,t,n),o}},56362:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t=0,n=i.parseFeedItem){return fetch(e).then((function(e){return e.text()})).then((function(e){return s(e,t,n)}))},t.getXPathText=a,t.parseFeed=s;var r,o=(r=n(22932))&&r.__esModule?r:{default:r},i=n(93435);function a(e,t,n=null,r=null){if(0===t.evaluate("count("+e+")",n||t,r,XPathResult.ANY_TYPE,null).numberValue)return;const o=t.evaluate(e,n||t,r,XPathResult.STRING_TYPE,null);return o.stringValue?o.stringValue:null}function s(e,t=0,n=i.parseFeedItem){return new Promise((function(r,i){try{"evaluate"in document==0&&o.default.install();const i=(new DOMParser).parseFromString(e,"application/xml"),s=i.createNSResolver(i.documentElement),u=function(e){const t={};return t.title=a("/rss/channel/title",e),t.description=a("/rss/channel/description",e),t.link=a("/rss/channel/link",e),t}(i);u.items=function(e,t,n,r){const o=function(e,t,n=null,r=null){return t.evaluate(e,n||t,r,XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,null)}("/rss/channel/item",e);let i=o.snapshotLength;const a=[];0!==n&&(i=Math.min(i,n));for(let n=0;n<i;n++){const i=o.snapshotItem(n);a.push(r(e,i,t))}return a}(i,s,t,n),r(u)}catch(e){i(e)}}))}},93435:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return(0,r.default)(e,t,i)},t.parseFeedItem=i;var r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=o(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(r,a,s):r[a]=e[a]}return r.default=e,n&&n.set(e,r),r}(n(56362));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}function i(e,t,n){const o={};return o.title=(0,r.getXPathText)("child::title",e,t),o.link=(0,r.getXPathText)("child::link",e,t),o.content=(0,r.getXPathText)("child::content:encoded",e,t,n),o.description=(0,r.getXPathText)("child::description",e,t),o.creator=(0,r.getXPathText)("child::dc:creator",e,t,n),o.date=(0,r.getXPathText)("child::pubDate",e,t),o}},5878:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getValueFromHiddenInput=t.curryUpdateToHiddenInput=void 0;const n=e=>document.querySelector(e);t.curryUpdateToHiddenInput=(e,t=null)=>r=>{const o=n(e);if(o){const e=Array.isArray(r)?r.join(","):r;o.value=e}t&&t(r)},t.getValueFromHiddenInput=e=>{const t=n(e);return t?t.value:null}},59956:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.inlineElements=t.getBlocks=t.default=t.blockElements=void 0,t.isBlockElement=E,t.isInlineElement=_;var r,o,i=n(92819),a=(r=n(14429))&&r.__esModule?r:{default:r},s=t.blockElements=["address","article","aside","blockquote","canvas","dd","div","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","section","table","tfoot","ul","video"],u=t.inlineElements=["b","big","i","small","tt","abbr","acronym","cite","code","dfn","em","kbd","strong","samp","time","var","a","bdo","br","img","map","object","q","script","span","sub","sup","button","input","label","select","textarea"],l=new RegExp("^("+s.join("|")+")$","i"),c=new RegExp("^("+u.join("|")+")$","i"),f=new RegExp("^<("+s.join("|")+")[^>]*?>$","i"),p=new RegExp("^</("+s.join("|")+")[^>]*?>$","i"),d=new RegExp("^<("+u.join("|")+")[^>]*>$","i"),h=new RegExp("^</("+u.join("|")+")[^>]*>$","i"),y=/^<([^>\s/]+)[^>]*>$/,g=/^<\/([^>\s]+)[^>]*>$/,b=/^[^<]+$/,m=/^<[^><]*$/,v=/<!--(.|[\r\n])*?-->/g,w=[];function E(e){return l.test(e)}function _(e){return c.test(e)}const x=t.getBlocks=(0,i.memoize)((function(e){var t=[],n=0,r="",s="",u="";return e=e.replace(v,""),w=[],(o=(0,a.default)((function(e){w.push(e)}))).addRule(b,"content"),o.addRule(m,"greater-than-sign-content"),o.addRule(f,"block-start"),o.addRule(p,"block-end"),o.addRule(d,"inline-start"),o.addRule(h,"inline-end"),o.addRule(y,"other-element-start"),o.addRule(g,"other-element-end"),o.onText(e),o.end(),(0,i.forEach)(w,(function(e,o){var i=w[o+1];switch(e.type){case"content":case"greater-than-sign-content":case"inline-start":case"inline-end":case"other-tag":case"other-element-start":case"other-element-end":case"greater than sign":i&&(0!==n||"block-start"!==i.type&&"block-end"!==i.type)?s+=e.src:(s+=e.src,t.push(s),r="",s="",u="");break;case"block-start":0!==n&&(""!==s.trim()&&t.push(s),s="",u=""),n++,r=e.src;break;case"block-end":n--,u=e.src,""!==r&&""!==u?t.push(r+s+u):""!==s.trim()&&t.push(s),r="",s="",u=""}n<0&&(n=0)})),t}));t.default={blockElements:s,inlineElements:u,isBlockElement:E,isInlineElement:_,getBlocks:x}},29938:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=e=>{var t=document.createElement("textarea");return t.innerHTML=e,t.value}},56101:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={getDirectionalStyle:!0,sendRequest:!0,decodeHTML:!0,getFeed:!0,getCourseFeed:!0,getPostFeed:!0,createSvgIconComponent:!0,getWordBoundaries:!0,strings:!0,join:!0,makeOutboundLink:!0,validateFacebookImage:!0,validateTwitterImage:!0};Object.defineProperty(t,"createSvgIconComponent",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(t,"decodeHTML",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"getCourseFeed",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"getDirectionalStyle",{enumerable:!0,get:function(){return o.getDirectionalStyle}}),Object.defineProperty(t,"getFeed",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"getPostFeed",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"getWordBoundaries",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(t,"join",{enumerable:!0,get:function(){return d.default}}),Object.defineProperty(t,"makeOutboundLink",{enumerable:!0,get:function(){return h.makeOutboundLink}}),Object.defineProperty(t,"sendRequest",{enumerable:!0,get:function(){return i.default}}),t.strings=void 0,Object.defineProperty(t,"validateFacebookImage",{enumerable:!0,get:function(){return y.default}}),Object.defineProperty(t,"validateTwitterImage",{enumerable:!0,get:function(){return g.default}});var o=n(79124),i=v(n(61117)),a=v(n(29938)),s=v(n(56362)),u=v(n(47305)),l=v(n(93435)),c=v(n(70636)),f=v(n(93988)),p=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=m(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}(n(80292));t.strings=p;var d=v(n(15421)),h=n(5850),y=v(n(11192)),g=v(n(69871)),b=n(5878);function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(m=function(e){return e?n:t})(e)}function v(e){return e&&e.__esModule?e:{default:e}}Object.keys(b).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(r,e)||e in t&&t[e]===b[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return b[e]}}))}))},15421:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t="-"){return e.filter(Boolean).join(t)}},5850:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.makeOutboundLink=void 0;var r=s(n(99196)),o=s(n(85890)),i=n(65736),a=n(1571);function s(e){return e&&e.__esModule?e:{default:e}}t.makeOutboundLink=(e="a")=>{class t extends r.default.Component{constructor(e){super(e),this.isYoastLink=this.isYoastLink.bind(this)}isYoastLink(e){return/yoast\.com|yoast\.test|yoa\.st/.test(e)}render(){if(!this.props.href)return null;const t=this.isYoastLink(this.props.href),n=Object.assign({},this.props,{target:"_blank",rel:t?this.props.rel:"noopener"});return r.default.createElement(e,n,this.props.children,r.default.createElement(a.A11yNotice,null,/* translators: Hidden accessibility text. */
(0,i.__)("(Opens in a new browser tab)","wordpress-seo")))}}return t.propTypes={children:o.default.oneOfType([o.default.node]),href:o.default.string,rel:o.default.string},t.defaultProps={children:null,href:null,rel:null},t}},11192:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validateType=t.validateSize=t.default=void 0;var r,o=n(65736),i=(r=n(59994))&&r.__esModule?r:{default:r};const a=e=>{const{width:t,height:n}=e,r=(0,o.sprintf)(/* Translators: %1$d expands to the minimum width, %2$d expands to the minimum height */
(0,o.__)("Your image dimensions are not suitable. The minimum dimensions are %1$dx%2$d pixels.","wordpress-seo"),200,200);return!(t<200||n<200)||r};t.validateSize=a;const s=e=>{const{type:t}=e,n=(0,o.sprintf)(
/* Translators: %1$s expands to the jpg format, %2$s expands to the png format,
  %3$s expands to the webp format, %4$s expands to the gif format. */
(0,o.__)("The format of the uploaded image is not supported. The supported formats are: %1$s, %2$s, %3$s and %4$s.","wordpress-seo"),"JPG","PNG","WEBP","GIF");return!!["jpg","png","gif","jpeg","webp"].includes(t)||n};t.validateType=s;const u=(0,i.default)([a,s]);t.default=u},69871:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.validatesBytes=t.validateType=t.validateSize=t.default=void 0;var r,o=n(65736),i=(r=n(59994))&&r.__esModule?r:{default:r};const a=4096,s=4096,u=(e,t)=>{const{width:n,height:r}=e,i=(0,o.__)("Your image dimensions are not suitable. The minimum dimensions are %1$dx%2$d pixels. The maximum dimensions are %3$dx%4$d pixels.","wordpress-seo"),u=n>a||r>s;
/* Translators: %1$d expands to the minimum width, %2$d expands to the minimum height,
  %3$d expands to the maximum width, %4$d expands to the maximum height. */return t&&(n<300||r<157||u)?(0,o.sprintf)(i,300,157,a,s):!(n<200||r<200||u)||(0,o.sprintf)(i,200,200,a,s)};t.validateSize=u;const l=e=>{const{type:t}=e,n=(0,o.sprintf)(/* Translators: %1$s expands to the gif format, %2$s expands to the gif format. */
(0,o.__)("You have uploaded a %1$s. Please note that, if it’s an animated %2$s, only the first frame will be used.","wordpress-seo"),"GIF","GIF"),r=(0,o.sprintf)(
/* Translators: %1$s expands to the jpg format, %2$s expands to the png format,
  %3$s expands to the webp format, %4$s expands to the gif format. */
(0,o.__)("The format of the uploaded image is not supported. The supported formats are: %1$s, %2$s, %3$s and %4$s.","wordpress-seo"),"JPG","PNG","WEBP","GIF");return!!["jpg","jpeg","png","webp"].includes(t)||("gif"===t?n:r)};t.validateType=l;const c=e=>{const{bytes:t}=e,n=(0,o.sprintf)(/* translators: %1$s expands to X, %2$s expands to the 5MB size. */
(0,o.__)("The file size of the uploaded image is too large for %1$s. File size must be less than %2$s.","wordpress-seo"),"X","5MB");return!(t>=5)||n};t.validatesBytes=c;const f=(0,i.default)([u,l,c]);t.default=f},59994:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=e=>t=>e.map((e=>e(t))).filter((e=>!0!==e))},80292:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"stripHTMLTags",{enumerable:!0,get:function(){return o.stripFullTags}}),Object.defineProperty(t,"stripSpaces",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"stripTagsFromHtmlString",{enumerable:!0,get:function(){return a.stripTagsFromHtmlString}});var r,o=n(49583),i=(r=n(15008))&&r.__esModule?r:{default:r},a=n(57427)},49583:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.stripIncompleteTags=t.stripFullTags=t.stripBlockTagsAtStartEnd=t.default=void 0;var r,o=(r=n(15008))&&r.__esModule?r:{default:r},i=n(59956),a=new RegExp("^<("+i.blockElements.join("|")+")[^>]*?>","i"),s=new RegExp("</("+i.blockElements.join("|")+")[^>]*?>$","i"),u=function(e){return(e=e.replace(/^(<\/([^>]+)>)+/i,"")).replace(/(<([^/>]+)>)+$/i,"")};t.stripIncompleteTags=u;var l=function(e){return(e=e.replace(a,"")).replace(s,"")};t.stripBlockTagsAtStartEnd=l;var c=function(e){return e=e.replace(/(<([^>]+)>)/gi," "),(0,o.default)(e)};t.stripFullTags=c,t.default={stripFullTags:c,stripIncompleteTags:u,stripBlockTagsAtStartEnd:l}},15008:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(e=(e=e.replace(/\s{2,}/g," ")).replace(/\s\.$/,".")).replace(/^\s+|\s+$/g,"")}},57427:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.stripTagsFromHtmlString=void 0;const n=["script","style"],r=[":","https:","http:"],o={a:["href","target","rel"]},i=(e,t,a)=>{const s=[];e.forEach((e=>{if(e.nodeType!==Node.ELEMENT_NODE)return;const u=e.nodeName.toLowerCase();n.includes(u)?e.remove():(i(e.childNodes,t,a),t.includes(u)?((e,t)=>{e.getAttributeNames().forEach((n=>{t.includes(n)?"href"===n&&"a"===e.nodeName.toLowerCase()&&(r.includes(e.protocol)||e.removeAttribute(n)):e.removeAttribute(n)}))})(e,a[u]||o[u]||[]):s.push(e))})),s.forEach((e=>e.replaceWith(...e.childNodes)))};t.stripTagsFromHtmlString=(e,t=[],n={})=>{const r=(new DOMParser).parseFromString(e,"text/html");return i(r.body.childNodes,t,n),r.body.innerHTML}},79124:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDirectionalStyle=function(e,t){return n=>n.theme.isRtl?t:e}},1571:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.A11yNotice=void 0;var r,o=(r=n(98487))&&r.__esModule?r:{default:r};t.A11yNotice=o.default.span`
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
	// Safari+VoiceOver bug see PR 308 and My Yoast issues 445, 712 and PR 715.
	transform: translateY(1em);
`},93988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return[" ","\\n","\\r","\\t"," "," ",".",",","'","(",")",'"',"+","-",";","!","?",":","/","»","«","‹","›","<",">"]}},14429:e=>{var t=function(e,t){var n;for(n=0;n<e.length;n++)if(e[n].regex.test(t))return e[n]},n=function(e,n){var r,o,i;for(r=0;r<n.length;r++)if(o=t(e,n.substring(0,r+1)))i=o;else if(i)return{max_index:r,rule:i};return i?{max_index:n.length,rule:i}:void 0};e.exports=function(e){var r="",o=[],i=1,a=1,s=function(t,n){e({type:n,src:t,line:i,col:a});var r=t.split("\n");i+=r.length-1,a=(r.length>1?1:a)+r[r.length-1].length};return{addRule:function(e,t){o.push({regex:e,type:t})},onText:function(e){for(var t=r+e,i=n(o,t);i&&i.max_index!==t.length;)s(t.substring(0,i.max_index),i.rule.type),t=t.substring(i.max_index),i=n(o,t);r=t},end:function(){if(0!==r.length){var e=t(o,r);if(!e){var n=new Error("unable to tokenize");throw n.tokenizer2={buffer:r,line:i,col:a},n}s(r,e.type)}}}}},57147:function(){!function(e){"use strict";if(!e.fetch){var t="URLSearchParams"in e,n="Symbol"in e&&"iterator"in Symbol,r="FileReader"in e&&"Blob"in e&&function(){try{return new Blob,!0}catch(e){return!1}}(),o="FormData"in e,i="ArrayBuffer"in e;if(i)var a=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],s=function(e){return e&&DataView.prototype.isPrototypeOf(e)},u=ArrayBuffer.isView||function(e){return e&&a.indexOf(Object.prototype.toString.call(e))>-1};h.prototype.append=function(e,t){e=f(e),t=p(t);var n=this.map[e];n||(n=[],this.map[e]=n),n.push(t)},h.prototype.delete=function(e){delete this.map[f(e)]},h.prototype.get=function(e){var t=this.map[f(e)];return t?t[0]:null},h.prototype.getAll=function(e){return this.map[f(e)]||[]},h.prototype.has=function(e){return this.map.hasOwnProperty(f(e))},h.prototype.set=function(e,t){this.map[f(e)]=[p(t)]},h.prototype.forEach=function(e,t){Object.getOwnPropertyNames(this.map).forEach((function(n){this.map[n].forEach((function(r){e.call(t,r,n,this)}),this)}),this)},h.prototype.keys=function(){var e=[];return this.forEach((function(t,n){e.push(n)})),d(e)},h.prototype.values=function(){var e=[];return this.forEach((function(t){e.push(t)})),d(e)},h.prototype.entries=function(){var e=[];return this.forEach((function(t,n){e.push([n,t])})),d(e)},n&&(h.prototype[Symbol.iterator]=h.prototype.entries);var l=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];w.prototype.clone=function(){return new w(this,{body:this._bodyInit})},v.call(w.prototype),v.call(_.prototype),_.prototype.clone=function(){return new _(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new h(this.headers),url:this.url})},_.error=function(){var e=new _(null,{status:0,statusText:""});return e.type="error",e};var c=[301,302,303,307,308];_.redirect=function(e,t){if(-1===c.indexOf(t))throw new RangeError("Invalid status code");return new _(null,{status:t,headers:{location:e}})},e.Headers=h,e.Request=w,e.Response=_,e.fetch=function(e,t){return new Promise((function(n,o){var i=new w(e,t),a=new XMLHttpRequest;a.onload=function(){var e,t,r={status:a.status,statusText:a.statusText,headers:(e=a.getAllResponseHeaders()||"",t=new h,e.split("\r\n").forEach((function(e){var n=e.split(":"),r=n.shift().trim();if(r){var o=n.join(":").trim();t.append(r,o)}})),t)};r.url="responseURL"in a?a.responseURL:r.headers.get("X-Request-URL");var o="response"in a?a.response:a.responseText;n(new _(o,r))},a.onerror=function(){o(new TypeError("Network request failed"))},a.ontimeout=function(){o(new TypeError("Network request failed"))},a.open(i.method,i.url,!0),"include"===i.credentials&&(a.withCredentials=!0),"responseType"in a&&r&&(a.responseType="blob"),i.headers.forEach((function(e,t){a.setRequestHeader(t,e)})),a.send(void 0===i._bodyInit?null:i._bodyInit)}))},e.fetch.polyfill=!0}function f(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(e))throw new TypeError("Invalid character in header field name");return e.toLowerCase()}function p(e){return"string"!=typeof e&&(e=String(e)),e}function d(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return n&&(t[Symbol.iterator]=function(){return t}),t}function h(e){this.map={},e instanceof h?e.forEach((function(e,t){this.append(t,e)}),this):e&&Object.getOwnPropertyNames(e).forEach((function(t){this.append(t,e[t])}),this)}function y(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function g(e){return new Promise((function(t,n){e.onload=function(){t(e.result)},e.onerror=function(){n(e.error)}}))}function b(e){var t=new FileReader,n=g(t);return t.readAsArrayBuffer(e),n}function m(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function v(){return this.bodyUsed=!1,this._initBody=function(e){if(this._bodyInit=e,e)if("string"==typeof e)this._bodyText=e;else if(r&&Blob.prototype.isPrototypeOf(e))this._bodyBlob=e;else if(o&&FormData.prototype.isPrototypeOf(e))this._bodyFormData=e;else if(t&&URLSearchParams.prototype.isPrototypeOf(e))this._bodyText=e.toString();else if(i&&r&&s(e))this._bodyArrayBuffer=m(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer]);else{if(!i||!ArrayBuffer.prototype.isPrototypeOf(e)&&!u(e))throw new Error("unsupported BodyInit type");this._bodyArrayBuffer=m(e)}else this._bodyText="";this.headers.get("content-type")||("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):t&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},r&&(this.blob=function(){var e=y(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?y(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(b)}),this.text=function(){var e,t,n,r=y(this);if(r)return r;if(this._bodyBlob)return e=this._bodyBlob,n=g(t=new FileReader),t.readAsText(e),n;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),n=new Array(t.length),r=0;r<t.length;r++)n[r]=String.fromCharCode(t[r]);return n.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},o&&(this.formData=function(){return this.text().then(E)}),this.json=function(){return this.text().then(JSON.parse)},this}function w(e,t){var n,r,o=(t=t||{}).body;if("string"==typeof e)this.url=e;else{if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new h(e.headers)),this.method=e.method,this.mode=e.mode,o||null==e._bodyInit||(o=e._bodyInit,e.bodyUsed=!0)}if(this.credentials=t.credentials||this.credentials||"omit",!t.headers&&this.headers||(this.headers=new h(t.headers)),this.method=(r=(n=t.method||this.method||"GET").toUpperCase(),l.indexOf(r)>-1?r:n),this.mode=t.mode||this.mode||null,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&o)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(o)}function E(e){var t=new FormData;return e.trim().split("&").forEach((function(e){if(e){var n=e.split("="),r=n.shift().replace(/\+/g," "),o=n.join("=").replace(/\+/g," ");t.append(decodeURIComponent(r),decodeURIComponent(o))}})),t}function _(e,t){t||(t={}),this.type="default",this.status="status"in t?t.status:200,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in t?t.statusText:"OK",this.headers=new h(t.headers),this.url=t.url||"",this._initBody(e)}}("undefined"!=typeof self?self:this)},22932:(e,t,n)=>{(function(){"use strict";var t=this;function n(e){return"string"==typeof e}function r(e,t,n){return e.call.apply(e.bind,arguments)}function o(e,t,n){if(!e)throw Error();if(2<arguments.length){var r=Array.prototype.slice.call(arguments,2);return function(){var n=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(n,r),e.apply(t,n)}}return function(){return e.apply(t,arguments)}}function i(e,t,n){return(i=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?r:o).apply(null,arguments)}function a(e){var t=ae;function n(){}n.prototype=t.prototype,e.G=t.prototype,e.prototype=new n,e.prototype.constructor=e,e.F=function(e,n,r){for(var o=Array(arguments.length-2),i=2;i<arguments.length;i++)o[i-2]=arguments[i];return t.prototype[n].apply(e,o)}}var s=String.prototype.trim?function(e){return e.trim()}:function(e){return e.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")};function u(e,t){return-1!=e.indexOf(t)}function l(e,t){return e<t?-1:e>t?1:0}var c,f=Array.prototype.indexOf?function(e,t,n){return Array.prototype.indexOf.call(e,t,n)}:function(e,t,r){if(r=null==r?0:0>r?Math.max(0,e.length+r):r,n(e))return n(t)&&1==t.length?e.indexOf(t,r):-1;for(;r<e.length;r++)if(r in e&&e[r]===t)return r;return-1},p=Array.prototype.forEach?function(e,t,n){Array.prototype.forEach.call(e,t,n)}:function(e,t,r){for(var o=e.length,i=n(e)?e.split(""):e,a=0;a<o;a++)a in i&&t.call(r,i[a],a,e)},d=Array.prototype.filter?function(e,t,n){return Array.prototype.filter.call(e,t,n)}:function(e,t,r){for(var o=e.length,i=[],a=0,s=n(e)?e.split(""):e,u=0;u<o;u++)if(u in s){var l=s[u];t.call(r,l,u,e)&&(i[a++]=l)}return i},h=Array.prototype.reduce?function(e,t,n,r){return r&&(t=i(t,r)),Array.prototype.reduce.call(e,t,n)}:function(e,t,n,r){var o=n;return p(e,(function(n,i){o=t.call(r,o,n,i,e)})),o},y=Array.prototype.some?function(e,t,n){return Array.prototype.some.call(e,t,n)}:function(e,t,r){for(var o=e.length,i=n(e)?e.split(""):e,a=0;a<o;a++)if(a in i&&t.call(r,i[a],a,e))return!0;return!1};e:{var g=t.navigator;if(g){var b=g.userAgent;if(b){c=b;break e}}c=""}var m,v,w=u(c,"Opera")||u(c,"OPR"),E=u(c,"Trident")||u(c,"MSIE"),_=u(c,"Edge"),x=u(c,"Gecko")&&!(u(c.toLowerCase(),"webkit")&&!u(c,"Edge"))&&!(u(c,"Trident")||u(c,"MSIE"))&&!u(c,"Edge"),T=u(c.toLowerCase(),"webkit")&&!u(c,"Edge");function P(){var e=t.document;return e?e.documentMode:void 0}e:{var O="",j=(v=c,x?/rv\:([^\);]+)(\)|;)/.exec(v):_?/Edge\/([\d\.]+)/.exec(v):E?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(v):T?/WebKit\/(\S+)/.exec(v):w?/(?:Version)[ \/]?(\S+)/.exec(v):void 0);if(j&&(O=j?j[1]:""),E){var N=P();if(null!=N&&N>parseFloat(O)){m=String(N);break e}}m=O}var S={};function A(e){if(!S[e]){for(var t=0,n=s(String(m)).split("."),r=s(String(e)).split("."),o=Math.max(n.length,r.length),i=0;0==t&&i<o;i++){var a=n[i]||"",u=r[i]||"",c=/(\d*)(\D*)/g,f=/(\d*)(\D*)/g;do{var p=c.exec(a)||["","",""],d=f.exec(u)||["","",""];if(0==p[0].length&&0==d[0].length)break;t=l(0==p[1].length?0:parseInt(p[1],10),0==d[1].length?0:parseInt(d[1],10))||l(0==p[2].length,0==d[2].length)||l(p[2],d[2])}while(0==t)}S[e]=0<=t}}var R=t.document,k=R&&E?P()||("CSS1Compat"==R.compatMode?parseInt(m,10):5):void 0,I=E&&!(9<=Number(k)),D=E&&!(8<=Number(k));function M(e,t,n,r){this.a=e,this.nodeName=n,this.nodeValue=r,this.nodeType=2,this.parentNode=this.ownerElement=t}function B(e,t){var n=D&&"href"==t.nodeName?e.getAttribute(t.nodeName,2):t.nodeValue;return new M(t,e,t.nodeName,n)}function F(e){var t=null;if(1==(n=e.nodeType)&&(t=null==(t=null==(t=e.textContent)||null==t?e.innerText:t)||null==t?"":t),"string"!=typeof t)if(I&&"title"==e.nodeName.toLowerCase()&&1==n)t=e.text;else if(9==n||1==n){e=9==n?e.documentElement:e.firstChild;var n=0,r=[];for(t="";e;){do{1!=e.nodeType&&(t+=e.nodeValue),I&&"title"==e.nodeName.toLowerCase()&&(t+=e.text),r[n++]=e}while(e=e.firstChild);for(;n&&!(e=r[--n].nextSibling););}}else t=e.nodeValue;return""+t}function C(e,t,n){if(null===t)return!0;try{if(!e.getAttribute)return!1}catch(e){return!1}return D&&"class"==t&&(t="className"),null==n?!!e.getAttribute(t):e.getAttribute(t,2)==n}function U(e,t,r,o,i){return(I?L:$).call(null,e,t,n(r)?r:null,n(o)?o:null,i||new Q)}function L(e,t,n,r,o){if(e instanceof De||8==e.b||n&&null===e.b){var i=t.all;if(!i)return o;if("*"!=(e=q(e))&&!(i=t.getElementsByTagName(e)))return o;if(n){for(var a=[],s=0;t=i[s++];)C(t,n,r)&&a.push(t);i=a}for(s=0;t=i[s++];)"*"==e&&"!"==t.tagName||ee(o,t);return o}return H(e,t,n,r,o),o}function $(e,t,n,r,o){return t.getElementsByName&&r&&"name"==n&&!E?(t=t.getElementsByName(r),p(t,(function(t){e.a(t)&&ee(o,t)}))):t.getElementsByClassName&&r&&"class"==n?(t=t.getElementsByClassName(r),p(t,(function(t){t.className==r&&e.a(t)&&ee(o,t)}))):e instanceof Pe?H(e,t,n,r,o):t.getElementsByTagName&&(t=t.getElementsByTagName(e.f()),p(t,(function(e){C(e,n,r)&&ee(o,e)}))),o}function Y(e,t,n,r,o){var i;if((e instanceof De||8==e.b||n&&null===e.b)&&(i=t.childNodes)){var a=q(e);return"*"==a||(i=d(i,(function(e){return e.tagName&&e.tagName.toLowerCase()==a})),i)?(n&&(i=d(i,(function(e){return C(e,n,r)}))),p(i,(function(e){"*"==a&&("!"==e.tagName||"*"==a&&1!=e.nodeType)||ee(o,e)})),o):o}return X(e,t,n,r,o)}function X(e,t,n,r,o){for(t=t.firstChild;t;t=t.nextSibling)C(t,n,r)&&e.a(t)&&ee(o,t);return o}function H(e,t,n,r,o){for(t=t.firstChild;t;t=t.nextSibling)C(t,n,r)&&e.a(t)&&ee(o,t),H(e,t,n,r,o)}function q(e){if(e instanceof Pe){if(8==e.b)return"!";if(null===e.b)return"*"}return e.f()}function z(e,t){if(!e||!t)return!1;if(e.contains&&1==t.nodeType)return e==t||e.contains(t);if(void 0!==e.compareDocumentPosition)return e==t||!!(16&e.compareDocumentPosition(t));for(;t&&e!=t;)t=t.parentNode;return t==e}function V(e,n){if(e==n)return 0;if(e.compareDocumentPosition)return 2&e.compareDocumentPosition(n)?1:-1;if(E&&!(9<=Number(k))){if(9==e.nodeType)return-1;if(9==n.nodeType)return 1}if("sourceIndex"in e||e.parentNode&&"sourceIndex"in e.parentNode){var r=1==e.nodeType,o=1==n.nodeType;if(r&&o)return e.sourceIndex-n.sourceIndex;var i=e.parentNode,a=n.parentNode;return i==a?G(e,n):!r&&z(i,n)?-1*W(e,n):!o&&z(a,e)?W(n,e):(r?e.sourceIndex:i.sourceIndex)-(o?n.sourceIndex:a.sourceIndex)}return(r=(o=9==e.nodeType?e:e.ownerDocument||e.document).createRange()).selectNode(e),r.collapse(!0),(o=o.createRange()).selectNode(n),o.collapse(!0),r.compareBoundaryPoints(t.Range.START_TO_END,o)}function W(e,t){var n=e.parentNode;if(n==t)return-1;for(var r=t;r.parentNode!=n;)r=r.parentNode;return G(r,e)}function G(e,t){for(var n=t;n=n.previousSibling;)if(n==e)return-1;return 1}function Q(){this.b=this.a=null,this.l=0}function J(e){this.node=e,this.a=this.b=null}function K(e,t){if(!e.a)return t;if(!t.a)return e;for(var n=e.a,r=t.a,o=null,i=null,a=0;n&&r;){i=n.node;var s=r.node;i==s||i instanceof M&&s instanceof M&&i.a==s.a?(i=n,n=n.a,r=r.a):0<V(n.node,r.node)?(i=r,r=r.a):(i=n,n=n.a),(i.b=o)?o.a=i:e.a=i,o=i,a++}for(i=n||r;i;)i.b=o,o=o.a=i,a++,i=i.a;return e.b=o,e.l=a,e}function Z(e,t){var n=new J(t);n.a=e.a,e.b?e.a.b=n:e.a=e.b=n,e.a=n,e.l++}function ee(e,t){var n=new J(t);n.b=e.b,e.a?e.b.a=n:e.a=e.b=n,e.b=n,e.l++}function te(e){return(e=e.a)?e.node:null}function ne(e){return(e=te(e))?F(e):""}function re(e,t){return new oe(e,!!t)}function oe(e,t){this.f=e,this.b=(this.c=t)?e.b:e.a,this.a=null}function ie(e){var t=e.b;if(null==t)return null;var n=e.a=t;return e.b=e.c?t.b:t.a,n.node}function ae(e){this.i=e,this.b=this.g=!1,this.f=null}function se(e){return"\n  "+e.toString().split("\n").join("\n  ")}function ue(e,t){e.g=t}function le(e,t){e.b=t}function ce(e,t){var n=e.a(t);return n instanceof Q?+ne(n):+n}function fe(e,t){var n=e.a(t);return n instanceof Q?ne(n):""+n}function pe(e,t){var n=e.a(t);return n instanceof Q?!!n.l:!!n}function de(e,t,n){ae.call(this,e.i),this.c=e,this.h=t,this.o=n,this.g=t.g||n.g,this.b=t.b||n.b,this.c==me&&(n.b||n.g||4==n.i||0==n.i||!t.f?t.b||t.g||4==t.i||0==t.i||!n.f||(this.f={name:n.f.name,s:t}):this.f={name:t.f.name,s:n})}function he(e,t,n,r,o){var i;if(t=t.a(r),n=n.a(r),t instanceof Q&&n instanceof Q){for(r=ie(t=re(t));r;r=ie(t))for(i=ie(o=re(n));i;i=ie(o))if(e(F(r),F(i)))return!0;return!1}if(t instanceof Q||n instanceof Q){t instanceof Q?(o=t,r=n):(o=n,r=t);for(var a=typeof r,s=ie(i=re(o));s;s=ie(i)){switch(a){case"number":s=+F(s);break;case"boolean":s=!!F(s);break;case"string":s=F(s);break;default:throw Error("Illegal primitive type for comparison.")}if(o==t&&e(s,r)||o==n&&e(r,s))return!0}return!1}return o?"boolean"==typeof t||"boolean"==typeof n?e(!!t,!!n):"number"==typeof t||"number"==typeof n?e(+t,+n):e(t,n):e(+t,+n)}function ye(e,t,n,r){this.a=e,this.w=t,this.i=n,this.m=r}!x&&!E||E&&9<=Number(k)||x&&A("1.9.1"),E&&A("9"),a(de),de.prototype.a=function(e){return this.c.m(this.h,this.o,e)},de.prototype.toString=function(){return"Binary Expression: "+this.c+se(this.h)+se(this.o)},ye.prototype.toString=function(){return this.a};var ge={};function be(e,t,n,r){if(ge.hasOwnProperty(e))throw Error("Binary operator already created: "+e);return e=new ye(e,t,n,r),ge[e.toString()]=e}be("div",6,1,(function(e,t,n){return ce(e,n)/ce(t,n)})),be("mod",6,1,(function(e,t,n){return ce(e,n)%ce(t,n)})),be("*",6,1,(function(e,t,n){return ce(e,n)*ce(t,n)})),be("+",5,1,(function(e,t,n){return ce(e,n)+ce(t,n)})),be("-",5,1,(function(e,t,n){return ce(e,n)-ce(t,n)})),be("<",4,2,(function(e,t,n){return he((function(e,t){return e<t}),e,t,n)})),be(">",4,2,(function(e,t,n){return he((function(e,t){return e>t}),e,t,n)})),be("<=",4,2,(function(e,t,n){return he((function(e,t){return e<=t}),e,t,n)})),be(">=",4,2,(function(e,t,n){return he((function(e,t){return e>=t}),e,t,n)}));var me=be("=",3,2,(function(e,t,n){return he((function(e,t){return e==t}),e,t,n,!0)}));function ve(e,t,n){this.a=e,this.b=t||1,this.f=n||1}function we(e,t){if(t.a.length&&4!=e.i)throw Error("Primary expression must evaluate to nodeset if filter has predicate(s).");ae.call(this,e.i),this.c=e,this.h=t,this.g=e.g,this.b=e.b}function Ee(e,t){if(t.length<e.A)throw Error("Function "+e.j+" expects at least"+e.A+" arguments, "+t.length+" given");if(null!==e.v&&t.length>e.v)throw Error("Function "+e.j+" expects at most "+e.v+" arguments, "+t.length+" given");e.B&&p(t,(function(t,n){if(4!=t.i)throw Error("Argument "+n+" to function "+e.j+" is not of type Nodeset: "+t)})),ae.call(this,e.i),this.h=e,this.c=t,ue(this,e.g||y(t,(function(e){return e.g}))),le(this,e.D&&!t.length||e.C&&!!t.length||y(t,(function(e){return e.b})))}function _e(e,t,n,r,o,i,a,s,u){this.j=e,this.i=t,this.g=n,this.D=r,this.C=o,this.m=i,this.A=a,this.v=void 0!==s?s:a,this.B=!!u}be("!=",3,2,(function(e,t,n){return he((function(e,t){return e!=t}),e,t,n,!0)})),be("and",2,2,(function(e,t,n){return pe(e,n)&&pe(t,n)})),be("or",1,2,(function(e,t,n){return pe(e,n)||pe(t,n)})),a(we),we.prototype.a=function(e){return e=this.c.a(e),$e(this.h,e)},we.prototype.toString=function(){return"Filter:"+se(this.c)+se(this.h)},a(Ee),Ee.prototype.a=function(e){return this.h.m.apply(null,function(e){return Array.prototype.concat.apply(Array.prototype,arguments)}(e,this.c))},Ee.prototype.toString=function(){var e="Function: "+this.h;if(this.c.length){var t=h(this.c,(function(e,t){return e+se(t)}),"Arguments:");e+=se(t)}return e},_e.prototype.toString=function(){return this.j};var xe={};function Te(e,t,n,r,o,i,a,s){if(xe.hasOwnProperty(e))throw Error("Function already created: "+e+".");xe[e]=new _e(e,t,n,r,!1,o,i,a,s)}function Pe(e,t){switch(this.h=e,this.c=void 0!==t?t:null,this.b=null,e){case"comment":this.b=8;break;case"text":this.b=3;break;case"processing-instruction":this.b=7;break;case"node":break;default:throw Error("Unexpected argument")}}function Oe(e){return"comment"==e||"text"==e||"processing-instruction"==e||"node"==e}function je(e){this.b=e,this.a=0}Te("boolean",2,!1,!1,(function(e,t){return pe(t,e)}),1),Te("ceiling",1,!1,!1,(function(e,t){return Math.ceil(ce(t,e))}),1),Te("concat",3,!1,!1,(function(e,t){return h(function(e,t,n){return 2>=arguments.length?Array.prototype.slice.call(e,t):Array.prototype.slice.call(e,t,n)}(arguments,1),(function(t,n){return t+fe(n,e)}),"")}),2,null),Te("contains",2,!1,!1,(function(e,t,n){return u(fe(t,e),fe(n,e))}),2),Te("count",1,!1,!1,(function(e,t){return t.a(e).l}),1,1,!0),Te("false",2,!1,!1,(function(){return!1}),0),Te("floor",1,!1,!1,(function(e,t){return Math.floor(ce(t,e))}),1),Te("id",4,!1,!1,(function(e,t){var r=9==(o=e.a).nodeType?o:o.ownerDocument,o=fe(t,e).split(/\s+/),i=[];p(o,(function(e){!(e=function(e){if(I){var t=r.all[e];if(t){if(t.nodeType&&e==t.id)return t;if(t.length)return function(e,t){var r;e:{r=e.length;for(var o=n(e)?e.split(""):e,i=0;i<r;i++)if(i in o&&t.call(void 0,o[i],i,e)){r=i;break e}r=-1}return 0>r?null:n(e)?e.charAt(r):e[r]}(t,(function(t){return e==t.id}))}return null}return r.getElementById(e)}(e))||0<=f(i,e)||i.push(e)})),i.sort(V);var a=new Q;return p(i,(function(e){ee(a,e)})),a}),1),Te("lang",2,!1,!1,(function(){return!1}),1),Te("last",1,!0,!1,(function(e){if(1!=arguments.length)throw Error("Function last expects ()");return e.f}),0),Te("local-name",3,!1,!0,(function(e,t){var n=t?te(t.a(e)):e.a;return n?n.localName||n.nodeName.toLowerCase():""}),0,1,!0),Te("name",3,!1,!0,(function(e,t){var n=t?te(t.a(e)):e.a;return n?n.nodeName.toLowerCase():""}),0,1,!0),Te("namespace-uri",3,!0,!1,(function(){return""}),0,1,!0),Te("normalize-space",3,!1,!0,(function(e,t){return(t?fe(t,e):F(e.a)).replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")}),0,1),Te("not",2,!1,!1,(function(e,t){return!pe(t,e)}),1),Te("number",1,!1,!0,(function(e,t){return t?ce(t,e):+F(e.a)}),0,1),Te("position",1,!0,!1,(function(e){return e.b}),0),Te("round",1,!1,!1,(function(e,t){return Math.round(ce(t,e))}),1),Te("starts-with",2,!1,!1,(function(e,t,n){return t=fe(t,e),e=fe(n,e),0==t.lastIndexOf(e,0)}),2),Te("string",3,!1,!0,(function(e,t){return t?fe(t,e):F(e.a)}),0,1),Te("string-length",1,!1,!0,(function(e,t){return(t?fe(t,e):F(e.a)).length}),0,1),Te("substring",3,!1,!1,(function(e,t,n,r){if(n=ce(n,e),isNaN(n)||1/0==n||-1/0==n)return"";if(r=r?ce(r,e):1/0,isNaN(r)||-1/0===r)return"";n=Math.round(n)-1;var o=Math.max(n,0);return e=fe(t,e),1/0==r?e.substring(o):e.substring(o,n+Math.round(r))}),2,3),Te("substring-after",3,!1,!1,(function(e,t,n){return t=fe(t,e),e=fe(n,e),-1==(n=t.indexOf(e))?"":t.substring(n+e.length)}),2),Te("substring-before",3,!1,!1,(function(e,t,n){return t=fe(t,e),e=fe(n,e),-1==(e=t.indexOf(e))?"":t.substring(0,e)}),2),Te("sum",1,!1,!1,(function(e,t){for(var n=re(t.a(e)),r=0,o=ie(n);o;o=ie(n))r+=+F(o);return r}),1,1,!0),Te("translate",3,!1,!1,(function(e,t,n,r){t=fe(t,e),n=fe(n,e);var o=fe(r,e);for(e={},r=0;r<n.length;r++){var i=n.charAt(r);i in e||(e[i]=o.charAt(r))}for(n="",r=0;r<t.length;r++)n+=(i=t.charAt(r))in e?e[i]:i;return n}),3),Te("true",2,!1,!1,(function(){return!0}),0),Pe.prototype.a=function(e){return null===this.b||this.b==e.nodeType},Pe.prototype.f=function(){return this.h},Pe.prototype.toString=function(){var e="Kind Test: "+this.h;return null===this.c||(e+=se(this.c)),e};var Ne=/\$?(?:(?![0-9-\.])(?:\*|[\w-\.]+):)?(?![0-9-\.])(?:\*|[\w-\.]+)|\/\/|\.\.|::|\d+(?:\.\d*)?|\.\d+|"[^"]*"|'[^']*'|[!<>]=|\s+|./g,Se=/^\s/;function Ae(e,t){return e.b[e.a+(t||0)]}function Re(e){return e.b[e.a++]}function ke(e){return e.b.length<=e.a}function Ie(e){ae.call(this,3),this.c=e.substring(1,e.length-1)}function De(e,t){var n;this.j=e.toLowerCase(),n="*"==this.j?"*":"http://www.w3.org/1999/xhtml",this.c=t?t.toLowerCase():n}function Me(e,t){if(ae.call(this,e.i),this.h=e,this.c=t,this.g=e.g,this.b=e.b,1==this.c.length){var n=this.c[0];n.u||n.c!=ze||"*"!=(n=n.o).f()&&(this.f={name:n.f(),s:null})}}function Be(){ae.call(this,4)}function Fe(){ae.call(this,4)}function Ce(e){return"/"==e||"//"==e}function Ue(e){ae.call(this,4),this.c=e,ue(this,y(this.c,(function(e){return e.g}))),le(this,y(this.c,(function(e){return e.b})))}function Le(e,t){this.a=e,this.b=!!t}function $e(e,t,n){for(n=n||0;n<e.a.length;n++)for(var r,o=e.a[n],i=re(t),a=t.l,s=0;r=ie(i);s++){var u=e.b?a-s:s+1;if("number"==typeof(r=o.a(new ve(r,u,a))))u=u==r;else if("string"==typeof r||"boolean"==typeof r)u=!!r;else{if(!(r instanceof Q))throw Error("Predicate.evaluate returned an unexpected type.");u=0<r.l}if(!u){if(r=(u=i).f,!(c=u.a))throw Error("Next must be called at least once before remove.");var l=c.b,c=c.a;l?l.a=c:r.a=c,c?c.b=l:r.b=l,r.l--,u.a=null}}return t}function Ye(e,t,n,r){ae.call(this,4),this.c=e,this.o=t,this.h=n||new Le([]),this.u=!!r,t=0<(t=this.h).a.length?t.a[0].f:null,e.b&&t&&(e=t.name,e=I?e.toLowerCase():e,this.f={name:e,s:t.s});e:{for(e=this.h,t=0;t<e.a.length;t++)if((n=e.a[t]).g||1==n.i||0==n.i){e=!0;break e}e=!1}this.g=e}function Xe(e,t,n,r){this.j=e,this.f=t,this.a=n,this.b=r}a(Ie),Ie.prototype.a=function(){return this.c},Ie.prototype.toString=function(){return"Literal: "+this.c},De.prototype.a=function(e){var t=e.nodeType;return!(1!=t&&2!=t||(t=void 0!==e.localName?e.localName:e.nodeName,"*"!=this.j&&this.j!=t.toLowerCase()||"*"!=this.c&&this.c!=(e.namespaceURI?e.namespaceURI.toLowerCase():"http://www.w3.org/1999/xhtml")))},De.prototype.f=function(){return this.j},De.prototype.toString=function(){return"Name Test: "+("http://www.w3.org/1999/xhtml"==this.c?"":this.c+":")+this.j},a(Me),a(Be),Be.prototype.a=function(e){var t=new Q;return 9==(e=e.a).nodeType?ee(t,e):ee(t,e.ownerDocument),t},Be.prototype.toString=function(){return"Root Helper Expression"},a(Fe),Fe.prototype.a=function(e){var t=new Q;return ee(t,e.a),t},Fe.prototype.toString=function(){return"Context Helper Expression"},Me.prototype.a=function(e){var t=this.h.a(e);if(!(t instanceof Q))throw Error("Filter expression must evaluate to nodeset.");for(var n=0,r=(e=this.c).length;n<r&&t.l;n++){var o,i=e[n],a=re(t,i.c.a);if(i.g||i.c!=Ge)if(i.g||i.c!=Je)for(o=ie(a),t=i.a(new ve(o));null!=(o=ie(a));)t=K(t,o=i.a(new ve(o)));else o=ie(a),t=i.a(new ve(o));else{for(o=ie(a);(t=ie(a))&&(!o.contains||o.contains(t))&&8&t.compareDocumentPosition(o);o=t);t=i.a(new ve(o))}}return t},Me.prototype.toString=function(){var e;if(e="Path Expression:"+se(this.h),this.c.length){var t=h(this.c,(function(e,t){return e+se(t)}),"Steps:");e+=se(t)}return e},a(Ue),Ue.prototype.a=function(e){var t=new Q;return p(this.c,(function(n){if(!((n=n.a(e))instanceof Q))throw Error("Path expression must evaluate to NodeSet.");t=K(t,n)})),t},Ue.prototype.toString=function(){return h(this.c,(function(e,t){return e+se(t)}),"Union Expression:")},Le.prototype.toString=function(){return h(this.a,(function(e,t){return e+se(t)}),"Predicates:")},a(Ye),Ye.prototype.a=function(e){var t=e.a,n=null,r=null,o=null,i=0;if((n=this.f)&&(r=n.name,o=n.s?fe(n.s,e):null,i=1),this.u)if(this.g||this.c!=Ve)if(t=ie(e=re(new Ye(We,new Pe("node")).a(e))))for(n=this.m(t,r,o,i);null!=(t=ie(e));)n=K(n,this.m(t,r,o,i));else n=new Q;else n=U(this.o,t,r,o),n=$e(this.h,n,i);else n=this.m(e.a,r,o,i);return n},Ye.prototype.m=function(e,t,n,r){return e=this.c.f(this.o,e,t,n),$e(this.h,e,r)},Ye.prototype.toString=function(){var e;if(e="Step:"+se("Operator: "+(this.u?"//":"/")),this.c.j&&(e+=se("Axis: "+this.c)),e+=se(this.o),this.h.a.length){var t=h(this.h.a,(function(e,t){return e+se(t)}),"Predicates:");e+=se(t)}return e},Xe.prototype.toString=function(){return this.j};var He={};function qe(e,t,n,r){if(He.hasOwnProperty(e))throw Error("Axis already created: "+e);return t=new Xe(e,t,n,!!r),He[e]=t}qe("ancestor",(function(e,t){for(var n=new Q,r=t;r=r.parentNode;)e.a(r)&&Z(n,r);return n}),!0),qe("ancestor-or-self",(function(e,t){var n=new Q,r=t;do{e.a(r)&&Z(n,r)}while(r=r.parentNode);return n}),!0);var ze=qe("attribute",(function(e,t){var n=new Q;if("style"==(i=e.f())&&I&&t.style)return ee(n,new M(t.style,t,"style",t.style.cssText)),n;var r=t.attributes;if(r)if(e instanceof Pe&&null===e.b||"*"==i)for(var o,i=0;o=r[i];i++)I?o.nodeValue&&ee(n,B(t,o)):ee(n,o);else(o=r.getNamedItem(i))&&(I?o.nodeValue&&ee(n,B(t,o)):ee(n,o));return n}),!1),Ve=qe("child",(function(e,t,r,o,i){return(I?Y:X).call(null,e,t,n(r)?r:null,n(o)?o:null,i||new Q)}),!1,!0);qe("descendant",U,!1,!0);var We=qe("descendant-or-self",(function(e,t,n,r){var o=new Q;return C(t,n,r)&&e.a(t)&&ee(o,t),U(e,t,n,r,o)}),!1,!0),Ge=qe("following",(function(e,t,n,r){var o=new Q;do{for(var i=t;i=i.nextSibling;)C(i,n,r)&&e.a(i)&&ee(o,i),o=U(e,i,n,r,o)}while(t=t.parentNode);return o}),!1,!0);qe("following-sibling",(function(e,t){for(var n=new Q,r=t;r=r.nextSibling;)e.a(r)&&ee(n,r);return n}),!1),qe("namespace",(function(){return new Q}),!1);var Qe=qe("parent",(function(e,t){var n=new Q;if(9==t.nodeType)return n;if(2==t.nodeType)return ee(n,t.ownerElement),n;var r=t.parentNode;return e.a(r)&&ee(n,r),n}),!1),Je=qe("preceding",(function(e,t,n,r){var o=new Q,i=[];do{i.unshift(t)}while(t=t.parentNode);for(var a=1,s=i.length;a<s;a++){var u=[];for(t=i[a];t=t.previousSibling;)u.unshift(t);for(var l=0,c=u.length;l<c;l++)C(t=u[l],n,r)&&e.a(t)&&ee(o,t),o=U(e,t,n,r,o)}return o}),!0,!0);qe("preceding-sibling",(function(e,t){for(var n=new Q,r=t;r=r.previousSibling;)e.a(r)&&Z(n,r);return n}),!0);var Ke=qe("self",(function(e,t){var n=new Q;return e.a(t)&&ee(n,t),n}),!1);function Ze(e){ae.call(this,1),this.c=e,this.g=e.g,this.b=e.b}function et(e){ae.call(this,1),this.c=e}function tt(e,t){this.a=e,this.b=t}function nt(e){for(var t,n=[];;){rt(e,"Missing right hand side of binary expression."),t=ct(e);var r=Re(e.a);if(!r)break;var o=(r=ge[r]||null)&&r.w;if(!o){e.a.a--;break}for(;n.length&&o<=n[n.length-1].w;)t=new de(n.pop(),n.pop(),t);n.push(t,r)}for(;n.length;)t=new de(n.pop(),n.pop(),t);return t}function rt(e,t){if(ke(e.a))throw Error(t)}function ot(e,t){var n=Re(e.a);if(n!=t)throw Error("Bad token, expected: "+t+" got: "+n)}function it(e){if(")"!=(e=Re(e.a)))throw Error("Bad token: "+e)}function at(e){if(2>(e=Re(e.a)).length)throw Error("Unclosed literal string");return new Ie(e)}function st(e){var t,n,r=[];if(Ce(Ae(e.a))){if(t=Re(e.a),n=Ae(e.a),"/"==t&&(ke(e.a)||"."!=n&&".."!=n&&"@"!=n&&"*"!=n&&!/(?![0-9])[\w]/.test(n)))return new Be;n=new Be,rt(e,"Missing next location step."),t=ut(e,t),r.push(t)}else{e:{switch(n=(t=Ae(e.a)).charAt(0)){case"$":throw Error("Variable reference not allowed in HTML XPath");case"(":Re(e.a),t=nt(e),rt(e,'unclosed "("'),ot(e,")");break;case'"':case"'":t=at(e);break;default:if(isNaN(+t)){if(Oe(t)||!/(?![0-9])[\w]/.test(n)||"("!=Ae(e.a,1)){t=null;break e}for(t=Re(e.a),t=xe[t]||null,Re(e.a),n=[];")"!=Ae(e.a)&&(rt(e,"Missing function argument list."),n.push(nt(e)),","==Ae(e.a));)Re(e.a);rt(e,"Unclosed function argument list."),it(e),t=new Ee(t,n)}else t=new et(+Re(e.a))}"["==Ae(e.a)&&(t=new we(t,n=new Le(lt(e))))}if(t){if(!Ce(Ae(e.a)))return t;n=t}else t=ut(e,"/"),n=new Fe,r.push(t)}for(;Ce(Ae(e.a));)t=Re(e.a),rt(e,"Missing next location step."),t=ut(e,t),r.push(t);return new Me(n,r)}function ut(e,t){var n,r,o,i;if("/"!=t&&"//"!=t)throw Error('Step op should be "/" or "//"');if("."==Ae(e.a))return r=new Ye(Ke,new Pe("node")),Re(e.a),r;if(".."==Ae(e.a))return r=new Ye(Qe,new Pe("node")),Re(e.a),r;if("@"==Ae(e.a))i=ze,Re(e.a),rt(e,"Missing attribute name");else if("::"==Ae(e.a,1)){if(!/(?![0-9])[\w]/.test(Ae(e.a).charAt(0)))throw Error("Bad token: "+Re(e.a));if(n=Re(e.a),!(i=He[n]||null))throw Error("No axis with name: "+n);Re(e.a),rt(e,"Missing node name")}else i=Ve;if(n=Ae(e.a),!/(?![0-9])[\w\*]/.test(n.charAt(0)))throw Error("Bad token: "+Re(e.a));if("("==Ae(e.a,1)){if(!Oe(n))throw Error("Invalid node type: "+n);if(!Oe(n=Re(e.a)))throw Error("Invalid type name: "+n);ot(e,"("),rt(e,"Bad nodetype");var a=null;'"'!=(o=Ae(e.a).charAt(0))&&"'"!=o||(a=at(e)),rt(e,"Bad nodetype"),it(e),n=new Pe(n,a)}else if(-1==(o=(n=Re(e.a)).indexOf(":")))n=new De(n);else{var s;if("*"==(a=n.substring(0,o)))s="*";else if(!(s=e.b(a)))throw Error("Namespace prefix not declared: "+a);n=new De(n=n.substr(o+1),s)}return o=new Le(lt(e),i.a),r||new Ye(i,n,o,"//"==t)}function lt(e){for(var t=[];"["==Ae(e.a);){Re(e.a),rt(e,"Missing predicate expression.");var n=nt(e);t.push(n),rt(e,"Unclosed predicate expression."),ot(e,"]")}return t}function ct(e){if("-"==Ae(e.a))return Re(e.a),new Ze(ct(e));var t=st(e);if("|"!=Ae(e.a))e=t;else{for(t=[t];"|"==Re(e.a);)rt(e,"Missing next union location path."),t.push(st(e));e.a.a--,e=new Ue(t)}return e}function ft(e){switch(e.nodeType){case 1:return function(e,t){var n=Array.prototype.slice.call(arguments,1);return function(){var t=n.slice();return t.push.apply(t,arguments),e.apply(this,t)}}(dt,e);case 9:return ft(e.documentElement);case 11:case 10:case 6:case 12:return pt;default:return e.parentNode?ft(e.parentNode):pt}}function pt(){return null}function dt(e,t){if(e.prefix==t)return e.namespaceURI||"http://www.w3.org/1999/xhtml";var n=e.getAttributeNode("xmlns:"+t);return n&&n.specified?n.value||null:e.parentNode&&9!=e.parentNode.nodeType?dt(e.parentNode,t):null}function ht(e,t){if(!e.length)throw Error("Empty XPath expression.");var n=function(e){e=e.match(Ne);for(var t=0;t<e.length;t++)Se.test(e[t])&&e.splice(t,1);return new je(e)}(e);if(ke(n))throw Error("Invalid XPath expression.");t?"function"==function(e){var t=typeof e;if("object"==t){if(!e)return"null";if(e instanceof Array)return"array";if(e instanceof Object)return t;var n=Object.prototype.toString.call(e);if("[object Window]"==n)return"object";if("[object Array]"==n||"number"==typeof e.length&&void 0!==e.splice&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("splice"))return"array";if("[object Function]"==n||void 0!==e.call&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("call"))return"function"}else if("function"==t&&void 0===e.call)return"object";return t}(t)||(t=i(t.lookupNamespaceURI,t)):t=function(){return null};var r=nt(new tt(n,t));if(!ke(n))throw Error("Bad token: "+Re(n));this.evaluate=function(e,t){return new yt(r.a(new ve(e)),t)}}function yt(e,t){if(0==t)if(e instanceof Q)t=4;else if("string"==typeof e)t=2;else if("number"==typeof e)t=1;else{if("boolean"!=typeof e)throw Error("Unexpected evaluation result.");t=3}if(2!=t&&1!=t&&3!=t&&!(e instanceof Q))throw Error("value could not be converted to the specified type");var n;switch(this.resultType=t,t){case 2:this.stringValue=e instanceof Q?ne(e):""+e;break;case 1:this.numberValue=e instanceof Q?+ne(e):+e;break;case 3:this.booleanValue=e instanceof Q?0<e.l:!!e;break;case 4:case 5:case 6:case 7:var r=re(e);n=[];for(var o=ie(r);o;o=ie(r))n.push(o instanceof M?o.a:o);this.snapshotLength=e.l,this.invalidIteratorState=!1;break;case 8:case 9:r=te(e),this.singleNodeValue=r instanceof M?r.a:r;break;default:throw Error("Unknown XPathResult type.")}var i=0;this.iterateNext=function(){if(4!=t&&5!=t)throw Error("iterateNext called with wrong result type");return i>=n.length?null:n[i++]},this.snapshotItem=function(e){if(6!=t&&7!=t)throw Error("snapshotItem called with wrong result type");return e>=n.length||0>e?null:n[e]}}function gt(e){this.lookupNamespaceURI=ft(e)}function bt(e,n){var r=e||t,o=r.Document&&r.Document.prototype||r.document;o.evaluate&&!n||(r.XPathResult=yt,o.evaluate=function(e,t,n,r){return new ht(e,n).evaluate(t,r)},o.createExpression=function(e,t){return new ht(e,t)},o.createNSResolver=function(e){return new gt(e)})}a(Ze),Ze.prototype.a=function(e){return-ce(this.c,e)},Ze.prototype.toString=function(){return"Unary Expression: -"+se(this.c)},a(et),et.prototype.a=function(){return this.c},et.prototype.toString=function(){return"Number: "+this.c},yt.ANY_TYPE=0,yt.NUMBER_TYPE=1,yt.STRING_TYPE=2,yt.BOOLEAN_TYPE=3,yt.UNORDERED_NODE_ITERATOR_TYPE=4,yt.ORDERED_NODE_ITERATOR_TYPE=5,yt.UNORDERED_NODE_SNAPSHOT_TYPE=6,yt.ORDERED_NODE_SNAPSHOT_TYPE=7,yt.ANY_UNORDERED_NODE_TYPE=8,yt.FIRST_ORDERED_NODE_TYPE=9;var mt,vt=["wgxpath","install"],wt=t;vt[0]in wt||!wt.execScript||wt.execScript("var "+vt[0]);for(;vt.length&&(mt=vt.shift());)vt.length||void 0===bt?wt=wt[mt]?wt[mt]:wt[mt]={}:wt[mt]=bt;e.exports.install=bt,e.exports.XPathResultType={ANY_TYPE:0,NUMBER_TYPE:1,STRING_TYPE:2,BOOLEAN_TYPE:3,UNORDERED_NODE_ITERATOR_TYPE:4,ORDERED_NODE_ITERATOR_TYPE:5,UNORDERED_NODE_SNAPSHOT_TYPE:6,ORDERED_NODE_SNAPSHOT_TYPE:7,ANY_UNORDERED_NODE_TYPE:8,FIRST_ORDERED_NODE_TYPE:9}}).call(n.g)},99196:e=>{"use strict";e.exports=window.React},92819:e=>{"use strict";e.exports=window.lodash},65736:e=>{"use strict";e.exports=window.wp.i18n},85890:e=>{"use strict";e.exports=window.yoast.propTypes},98487:e=>{"use strict";e.exports=window.yoast.styledComponents}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var r=n(56101);(window.yoast=window.yoast||{}).helpers=r})();