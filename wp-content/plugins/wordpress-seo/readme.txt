=== Yoast SEO - Advanced SEO with real-time guidance and built-in AI ===
Contributors: yoast, joost<PERSON><PERSON>k, tdevalk
Donate link: https://yoa.st/1up
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl.html
Tags: SEO, XML sitemap, Content analysis, Readability, Schema
Tested up to: 6.8
Stable tag: 25.4
Requires PHP: 7.4

Improve your SEO with real-time feedback, schema, and clear guidance. Upgrade for AI tools, Google Docs integration, and 24/7 support, no hidden fees.

== Description ==

## YOAST SEO: THE #1 WORDPRESS SEO PLUGIN

Yoast SEO helps your WordPress site grow by making SEO clear, actionable, and fast. With real-time feedback, built-in guidance, and AI tools in our Premium plans, you don’t have to be an expert to get more traffic and better content performance.

Whether you’re publishing your first post or managing a high-traffic site, Yoast gives you the tools to improve visibility and reach the right audience.

Yoast SEO is **free to install and includes all the essential tools to improve your content**. Upgrade only if and when you need more. **As of July 2025, we’ve updated our pricing and packaging**, so you’re seeing the most current plans and features. You’ll find all the extensive benefits of our [Yoast SEO Premium](https://yoa.st/1v8) plugin and our [Yoast WooCommerce SEO](https://yoa.st/3rh) plan for merchants below.

### Built-in SEO tools to grow your traffic

#### Real-time SEO feedback while you write
Yoast checks your content as you type and shows exactly what to improve for better rankings and readability. Right inside your editor, you get clear, immediate guidance on everything from keyword use to structure.

#### Live snippet editor for better search visibility
With editable SEO titles and meta descriptions, you can control your page’s appearance in search results. The live preview helps you create snippets that drive clicks and attract the right traffic.

#### Automatic structured data for rich results
Yoast adds schema markup behind the scenes, so search engines understand your pages and can display rich results, without you touching a line of code.

#### SEO-friendly sitemaps and breadcrumbs
With automatically generated XML sitemaps and breadcrumbs, your site is easier to crawl and navigate, improving the experience for both users and search engines.

#### Readability checks that keep users engaged
Clear content keeps readers on the page and reduces bounce. Yoast helps you simplify your writing, improve flow, and make your message easier to understand while supporting SEO.

#### Works with the tools you already use
Yoast SEO integrates with the WordPress Block Editor, Classic Editor, Elementor, and WooCommerce. That means you can optimize pages, posts, and products without switching tools or slowing down your workflow.

### Works with your favorite WordPress tools
* **Elementor** – Optimize content visually without leaving the builder

* **WooCommerce** – Improve product and category SEO with schema and snippet tools

* **Semrush** – Research keywords inside your post editor

* **Wincher** – Track keyword rankings to monitor performance


### Yoast SEO Premium - updated July 2025
#### All the value of Yoast SEO *plus*:

Real-time SEO guidance and built-in [AI features](https://yoa.st/51c) for teams that want to improve visibility without needing deep SEO expertise.

Yoast SEO Premium is built for in-house marketing teams, entrepreneurs, and content creators who rely on content-driven channels to grow. It gives you smart, practical tools that help you edit and publish with confidence. 

Optimize content for up to five keyphrases per page, generate SEO-friendly titles and descriptions using AI, and get real-time suggestions as you write. Yoast helps you meet SEO standards effortlessly.

Premium features like the Redirect Manager, Internal Linking suggestions, and Social Previews streamline content maintenance and strengthen your site’s structure. Guided SEO Workouts help your team tackle tasks like improving internal links or updating outdated content, without feeling overwhelmed.

#### Also included with Premium - from July 2025:

* **[News SEO](https://yoa.st/1uv)** - Ensure your news content is discovered quickly with automatic structured data and XML News sitemaps

* **[Local SEO](https://yoa.st/1uu)** - Make your business more visible in local search with custom location pages, local schema, and map integrations

* **[Video SEO](https://yoa.st/1uw)** - Get your videos indexed and enhanced with rich results like thumbnails and timestamps, no coding needed

* **[One seat for the _new_ Yoast SEO Google Docs Add-on](https://yoa.st/52u)** - Optimize content directly in Docs with real-time SEO analysis, then export in WordPress-ready format.

**AI tools are built in, with no extra cost or logins**. Whether generating metadata or optimizing content, your team can move faster and stay consistent.

With **24/7 premium support and intuitive tools, Yoast SEO Premium helps you turn content into results**. Boost your organic visibility, drive relevant traffic, and grow your brand online, all without needing to be an SEO expert.

### Yoast WooCommerce SEO
#### All the value of Yoast SEO Premium *plus*:

Better product visibility. Smarter SEO for online stores. Built-in tools to help you stand out in search, without the technical complexity.

[Yoast WooCommerce SEO](https://yoa.st/3rh) combines all the power of Yoast SEO Premium with advanced tools made specifically for WooCommerce. It’s designed for e-commerce managers, DTC brands, and independent sellers who want their products to show up clearly in Google, drive more clicks, and convert more customers, without doing everything manually.

The plugin automatically adds rich, structured data to your product pages so Google can display key details like price, availability, and reviews. You also get a custom WooCommerce XML sitemap that includes what matters and skips what doesn’t, helping search engines crawl your store more efficiently.

Built-in content analysis checks for key e-commerce elements like image alt tags, product identifiers, and short descriptions. AI tools generate SEO-friendly titles and descriptions for your product and category pages, saving time while boosting performance.

Everything is designed to work with your existing WooCommerce setup, no technical skills required. Yoast WooCommerce SEO helps your products get found, look better in search results, and drive more traffic, all while saving you hours of manual optimization.


### Bug reports

Find a bug in Yoast SEO? We welcome your bug reports! Please report bugs in the WordPress SEO [repository on GitHub](https://github.com/Yoast/wordpress-seo). Note that GitHub is not a support forum but an efficient platform for addressing and resolving issues efficiently.

### There's more to learn about SEO

For a comprehensive resource on search engine optimization and Yoast SEO, please explore our [website](https://yoast.com), which is rich with insights and inspiration. Seek guidance in our extensively curated help center, designed to help you provide the knowledge to optimize your website effectively.

== Installation ==
Starting with Yoast SEO consists of just two steps: installing and setting up the plugin. Yoast SEO is designed to work with your site’s specific needs, so don’t forget to go through the Yoast SEO first-time configuration as explained in the ‘after activation’ step! For the most up-to-date guidance on how to install Yoast SEO products, [please visit our help center](https://yoast.com/help/yoast-installation-manuals/#h-yoast-seo-and-yoast-seo-premium-for-wordpress). 

== Frequently Asked Questions ==

= How do the XML Sitemaps in the Yoast SEO plugin work? =

Having an XML sitemap can be beneficial for SEO, as Google can retrieve essential pages of a website very fast, even if the internal linking of a site isn’t flawless.
The sitemap index and individual sitemaps are updated automatically as you add or remove content and will include the post types you want search engines to index. Post Types marked as noindex will not appear in the sitemap. [Learn more about XML Sitemaps](https://yoa.st/3qt).

= How can I add my website to Google Search Console? =

It is straightforward to add your website to Google Search Console.
1. Create a Google Search Console account and login into your account.
2. Click ‘Add a property’ under the search drop-down.
3. Enter your website URL in the box and click ‘Continue’.
4. Click the arrow next to ‘HTML tag’ to expand the option.
5. Copy the meta tag.
6. Log in to your WordPress website.
7. Click on ‘SEO’ in the dashboard.
8. Click on ‘General’.
9. Click on the ‘Webmaster Tools’ tab.
10. Paste the code in the Google field and click ‘Save Changes’.
11. Go back to Google Search Console and click ‘Verify’.

If you want more details steps, please visit [our article on our help center](https://yoa.st/3qu).

= How do I implement Yoast SEO breadcrumbs? =

The steps below are a temporary solution as manual edits made to theme files may be overwritten with future theme updates. Please contact the theme developer for a permanent solution. We’ve written an article about the [importance of breadcrumbs for SEO](https://yoa.st/3qv).

To implement the [breadcrumbs](https://yoa.st/3qw) function in Yoast SEO, you will have to edit your theme. We recommend that prior to any editing of the theme files, a backup is taken. Your host provider can help you take a backup.
Copy the following code into your theme where you want the breadcrumbs to be. If you are not sure, you will need to experiment with placement:

<code>
<?php
if ( function_exists( 'yoast_breadcrumb' ) ) {
    yoast_breadcrumb( '<p id="breadcrumbs">','</p>' );
}
?>
</code>

Common places where you could place your breadcrumbs are inside your `single.php` and/or `page.php` file just above the page’s title. Another option that makes it really easy in some themes is by just pasting the code in `header.php` at the very end.

In most non-WooTheme themes, this code snippet should not be added to your `functions.php` file.
Alternatively, you can manually add the breadcrumb shortcode to individual posts or pages: `[wpseo_breadcrumb]`

If you need more details or a step by step guide, read our [Implementation guide for Yoast SEO breadcrumbs](https://yoa.st/3qx).

= How do I noindex URLS? =

Yoast SEO provides multiple options for setting a URL or group of URLs to noindex. [Read more about how to do this in this guide](https://yoa.st/3qy/).

= Google shows the wrong description, how do I fix this? =

If you’ve crafted nice meta descriptions for your blog posts, nothing is more annoying than Google showing another description for your site completely in the search result snippet.

Possible causes could be:
1. wrong description in code
2. Google cache is outdated
3. Search term manipulation
4. Google ignored the meta description

You can [read more here on how to solve the issue with the wrong description](https://yoa.st/3qz).

= How often is Yoast SEO updated? =

Yoast SEO is updated every two weeks. If you want to know why, please read [this post on why we release every two weeks](https://yoa.st/3q-)!

= How do I get support? =

As our free plugin is used by millions of people worldwide, we cannot offer you all one on one support. If you have trouble with the Yoast SEO for WordPress plugin, you can get help on the support forums here at [wordpress.org](https://wordpress.org/support/plugin/wordpress-seo/) or by checking out our help center at [yoast.com/help/](https://yoa.st/3r1).

The plugins you buy at Yoast are called ‘premium plugins’ (even if Premium isn’t in its name) and include a complete year of free updates and premium support. This means you can contact our support team if you have any questions about that plugin.

[Read more on how to get support](https://yoa.st/3r2)

= What happens to my data if I enable usage tracking? =

[This page on yoast.com explains what data we collect to improve Yoast SEO](https://yoa.st/4w7). We only collect data when you explicitly opt in. Read more about how we handle your data in [our Privacy Policy](https://yoa.st/4w8).

= I have a different question than listed here =

Your question has most likely been answered on our help center: [yoast.com/help/](https://yoa.st/1va).

== Screenshots ==

1. The modern interface makes Yoast SEO easy to work with.
2. Easily manage how your posts and pages appear in SERPs.
3. Yoast SEO Premium has extra crawl optimization options.
4. Yoast SEO integrates with tools like Semrush and Wincher.
5. The famous SEO and readability analyses in Yoast SEO.
6. See what your post looks like in Google.
7. The First-time configuration helps you get started quickly.
8. The inclusive language analysis in Yoast SEO.

== Changelog ==

= 25.4 =

Release date: 2025-07-01

Yoast SEO 25.4 brings more enhancements and bugfixes. [Find more information about our software releases and updates here](https://yoa.st/releases).

#### Enhancements

* Adds descriptions for the posts listed in the llms.txt file, using their excerpts when those are explicitly set.
* Adds support for preventing specifically `noindex`ed posts from getting into the llms.txt file.
* Changes one of the feedback texts for the _text length_ assessment to be consistent with the others.
* Improves support for non-english characters in llms.txt, for servers that don't serve .txt files in UTF-8.
* Improves the feedback of the _competing links_ assessment by making it clearer.
* Makes the assessments _single title_ and _competing links_ available from the get-go in the SEO analysis.
* Prioritizes cornerstone content for the posts lists in the llms.txt file.
* Renames the _link keyphrase_ assessment to _competing links_.

#### Bugfixes

* Fixes a bug where the llmst.txt file wouldn't be able to be generated in wp.com.

#### Other

* Improves the internal engine that creates the post lists in the llms.txt file for a more efficient and lighter generation.
* Improves the translatability of the feedback strings for the _text length_ assessment.
* Introduces the `wpseo_llmstxt_filesystem_path` filter that allows editing the file path of the llmst.txt file, to help users in servers with filesystem restrictions.

= 25.3.1 =

Release date: 2025-06-18

This is a maintenance release which is required to align with changes to Yoast SEO Premium 25.3.1.

= 25.3 =

Release date: 2025-06-10

*New:* Yoast SEO 25.3 introduces llms.txt support to help AI tools understand your site better. [Read the full release post here!](https://yoa.st/release-10-6-25)

#### Enhancements

* Introduces the llms.txt feature, which gives site owners the opportunity to automatically generate an llms.txt file. This file helps LLMs to better understand the site's content.

= Earlier versions =
For the changelog of earlier versions, please refer to [the changelog on yoast.com](https://yoa.st/yoast-seo-changelog).
