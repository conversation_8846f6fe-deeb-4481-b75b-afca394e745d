<?php
// phpcs:disable Yoast.NamingConventions.NamespaceName.TooLong
namespace Yoast\WP\SEO\Llms_Txt\User_Interface\Health_Check;

use Yoast\WP\SEO\Services\Health_Check\Report_Builder_Factory;
use Yoast\WP\SEO\Services\Health_Check\Reports_Trait;

/**
 * Presents a set of different messages for the File_Generation health check.
 */
class File_Reports {

	use Reports_Trait;

	/**
	 * Constructor
	 *
	 * @param  Report_Builder_Factory $report_builder_factory The factory for result builder objects.
	 *                                                        This class uses the report builder to generate WordPress-friendly
	 *                                                        health check results.
	 */
	public function __construct( Report_Builder_Factory $report_builder_factory ) {
		$this->report_builder_factory = $report_builder_factory;
	}

	/**
	 * Returns the message for a successful health check.
	 *
	 * @return string[] The message as a WordPress site status report.
	 */
	public function get_success_result() {
		$label = \sprintf(
			/* translators: %s: Yoast SEO. */
			\__( 'Your llms.txt file is auto-generated by %s', 'wordpress-seo' ),
			'Yoast SEO',
		);

		$description = \sprintf(
			/* translators: %s: Yoast SEO. */
			\__( '%s keeps your llms.txt file up-to-date. This helps LLMs access and provide your site\'s information more easily.', 'wordpress-seo' ),
			'Yoast SEO',
		);

		return $this->get_report_builder()
			->set_label( $label )
			->set_status_good()
			->set_description( $description )
			->build();
	}

	/**
	 * Returns the message for a failed health check. In this case, when the llms.txt file couldn't be auto-generated.
	 *
	 * @param string $reason The reason why the llms.txt file couldn't be auto-generated.
	 *
	 * @return string[] The message as a WordPress site status report.
	 */
	public function get_generation_failure_result( $reason ) {
		switch ( $reason ) {
			case 'not_managed_by_yoast_seo':
				$title   = \__( 'Your llms.txt file couldn\'t be auto-generated', 'wordpress-seo' );
				$message = \sprintf(
					/* translators: 1,3,5: expand to opening paragraph tag, 2,4,6: expand to opening paragraph tag. */
					\__( '%1$sYou have activated the Yoast llms.txt feature, but we couldn\'t generate an llms.txt file.%2$s%3$sIt looks like there is an llms.txt file already that wasn\'t created by Yoast, or the llms.txt file created by Yoast has been edited manually.%4$s%5$sWe don\'t want to overwrite this file\'s content, so if you want to let Yoast keep auto-generating the llms.txt file, you can manually delete the existing one. Otherwise, consider disabling the Yoast feature.%6$s', 'wordpress-seo' ),
					'<p>',
					'</p>',
					'<p>',
					'</p>',
					'<p>',
					'</p>'
				);
				break;
			case 'filesystem_permissions':
				$title   = \__( 'Your llms.txt file couldn\'t be auto-generated', 'wordpress-seo' );
				$message = \sprintf(
					/* translators: 1,3: expand to opening paragraph tag, 2,4: expand to opening paragraph tag. */
					\__( '%1$sYou have activated the Yoast llms.txt feature, but we couldn\'t generate an llms.txt file.%2$s%3$sIt looks like there aren\'t sufficient permissions on the web server\'s filesystem.%4$s', 'wordpress-seo' ),
					'<p>',
					'</p>',
					'<p>',
					'</p>'
				);
				break;
			default:
				$title   = \__( 'Your llms.txt file couldn\'t be auto-generated', 'wordpress-seo' );
				$message = \__( 'You have activated the Yoast llms.txt feature, but we couldn\'t generate an llms.txt file, for unknown reasons.', 'wordpress-seo' );
				break;
		}

		return $this->get_report_builder()
			->set_label( $title )
			->set_status_recommended()
			->set_description( $message )
			->build();
	}
}
