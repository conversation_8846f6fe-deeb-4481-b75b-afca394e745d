<?php

// phpcs:disable Yoast.NamingConventions.NamespaceName.TooLong
namespace Yoast\WP\SEO\Llms_Txt\Application\Markdown_Builders;

use Yoast\WP\SEO\Llms_Txt\Domain\Markdown\Sections\Intro;
use Yoast\WP\SEO\Llms_Txt\Infrastructure\Markdown_Services\Sitemap_Link_Collector;

/**
 * The builder of the intro section.
 */
class Intro_Builder {

	/**
	 * The sitemap link collector.
	 *
	 * @var Sitemap_Link_Collector
	 */
	protected $sitemap_link_collector;

	/**
	 * The constructor.
	 *
	 * @param Sitemap_Link_Collector $sitemap_link_collector The sitemap link collector.
	 */
	public function __construct(
		Sitemap_Link_Collector $sitemap_link_collector
	) {
		$this->sitemap_link_collector = $sitemap_link_collector;
	}

	/**
	 * Gets the plugin version that generated the llms.txt file.
	 *
	 * @return string The plugin version that generated the llms.txt file.
	 */
	protected function get_generator_version(): string {
		return 'Yoast SEO v' . \WPSEO_VERSION;
	}

	/**
	 * Builds the intro section.
	 *
	 * @return Intro The intro section.
	 */
	public function build_intro(): Intro {
		$intro_content = \sprintf(
			'Generated by %s, this is an llms.txt file, meant for consumption by LLMs.',
			$this->get_generator_version()
		);
		$intro_links   = [];

		$sitemap_link = $this->sitemap_link_collector->get_link();
		if ( $sitemap_link !== null ) {
			$intro_links[] = $sitemap_link;

			$intro_content .= \PHP_EOL . \PHP_EOL . 'This is the %s of this website.';
		}

		return new Intro( $intro_content, $intro_links );
	}
}
