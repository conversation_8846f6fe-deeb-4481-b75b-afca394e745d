<?php return array('addon-installation.js' => array('dependencies' => array('react', 'wp-components', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => 'b9f59c3fa79d3c219f77'), 'admin-global.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'ee3745f3fdc45e43fb51'), 'admin-modules.js' => array('dependencies' => array('react', 'wp-data', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package'), 'version' => 'fdbe5d62f7cf6543e337'), 'analysis-worker.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'aa04978fbd423b404462'), 'api-client.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'f56d7de163fa219c67e2'), 'block-editor.js' => array('dependencies' => array('lodash', 'moment', 'react', 'react-dom', 'wp-annotations', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-edit-post', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-plugins', 'wp-polyfill', 'wp-rich-text', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-components', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-related-keyphrase-suggestions-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-search-metadata-previews-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '5596c880ef91ec7ea42e'), 'bulk-editor.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => '308d4f19cc8fcb346d3d'), 'classic-editor.js' => array('dependencies' => array('jquery', 'lodash', 'moment', 'react', 'react-dom', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-components', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-related-keyphrase-suggestions-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-search-metadata-previews-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '86a48229f4ec7bd7d0f5'), 'crawl-settings.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'd511931b46d0b74648b4'), 'dashboard-widget.js' => array('dependencies' => array('react', 'wp-element', 'wp-polyfill', 'yoast-seo-analysis-report-package', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-style-guide-package'), 'version' => 'ad6147e367cc50376cc5'), 'wincher-dashboard-widget.js' => array('dependencies' => array('lodash', 'moment', 'react', 'wp-api-fetch', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package'), 'version' => '2dd220d7b48b6315a5f3'), 'dynamic-blocks.js' => array('dependencies' => array('react', 'wp-block-editor', 'wp-blocks', 'wp-polyfill', 'wp-server-side-render'), 'version' => '739ebde8b3e418b9886a'), 'edit-page.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'afab9d8fdff1d98c8ca9'), 'editor-modules.js' => array('dependencies' => array('lodash', 'moment', 'react', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-analysis-report-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-related-keyphrase-suggestions-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '4e4fb978a72689256d24'), 'elementor.js' => array('dependencies' => array('lodash', 'moment', 'react', 'react-dom', 'wp-annotations', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-rich-text', 'wp-sanitize', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-chart.js-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-components', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-related-keyphrase-suggestions-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-search-metadata-previews-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '601ea8e273504a056336'), 'externals-components.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-analysis-report-package', 'yoast-seo-components-new-package', 'yoast-seo-externals-contexts', 'yoast-seo-externals-redux', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-related-keyphrase-suggestions-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '8cfd628b8f5eddfada0e'), 'externals-contexts.js' => array('dependencies' => array('react', 'wp-element', 'wp-polyfill', 'yoast-seo-prop-types-package'), 'version' => '10ecaeb7fee15b420938'), 'externals-redux.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-data', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-sanitize', 'wp-url', 'yoast-seo-helpers-package', 'yoast-seo-redux-js-toolkit-package'), 'version' => 'f41e10aa9d403f53d476'), 'filter-explanation.js' => array('dependencies' => array('wp-polyfill'), 'version' => '8b3042cee26c58eb9be7'), 'help-scout-beacon.js' => array('dependencies' => array('react', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-styled-components-package'), 'version' => '4cff4f48af6493046757'), 'import.js' => array('dependencies' => array('jquery', 'lodash', 'wp-i18n', 'wp-polyfill'), 'version' => 'cbe848d7253c616f3a75'), 'indexation.js' => array('dependencies' => array('jquery', 'react', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package', 'yoast-seo-styled-components-package'), 'version' => '874600c8f036af9d81b5'), 'installation-success.js' => array('dependencies' => array('lodash', 'react', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill'), 'version' => '413369901a13055f6d7f'), 'integrations-page.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-dashboard-frontend-package', 'yoast-seo-externals-contexts', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '281f025f05868f3d2510'), 'introductions.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-api-fetch', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => 'c4909819edcbce623a7d'), 'network-admin.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'c28de4314d03147fca4a'), 'post-edit.js' => array('dependencies' => array('jquery', 'lodash', 'react', 'react-dom', 'wp-annotations', 'wp-api', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-rich-text', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '69b2180b6780530723e2'), 'quick-edit-handler.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'e7d3f8a9873afbfd1425'), 'reindex-links.js' => array('dependencies' => array('jquery', 'wp-polyfill'), 'version' => 'e4694eb7292052d53fc4'), 'redirect-old-features-tab.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'a792fdd4c0d1c2ef737c'), 'settings.js' => array('dependencies' => array('jquery', 'lodash', 'react', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-externals-redux', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '0b6302a0ba04dc4a33a0'), 'new-settings.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-externals-redux', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-replacement-variable-editor-package', 'yoast-seo-styled-components-package', 'yoast-seo-ui-library-package'), 'version' => '0051e772b40a2f6ead03'), 'academy.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => '609712bcb8180edc0519'), 'general-page.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-dashboard-frontend-package', 'yoast-seo-externals-redux', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-social-metadata-forms-package', 'yoast-seo-ui-library-package'), 'version' => '611baf60b0d43c3166d2'), 'support.js' => array('dependencies' => array('lodash', 'react', 'react-dom', 'wp-api-fetch', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'wp-url', 'yoast-seo-externals-redux', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => '51d77f45209582a7d46f'), 'how-to-block.js' => array('dependencies' => array('lodash', 'react', 'wp-a11y', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-element', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '70091636e473d2d87583'), 'faq-block.js' => array('dependencies' => array('react', 'wp-a11y', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-element', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'yoast-seo-prop-types-package'), 'version' => '34ebc93abf046084fe1c'), 'term-edit.js' => array('dependencies' => array('jquery', 'lodash', 'react', 'react-dom', 'wp-annotations', 'wp-api', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-rich-text', 'wp-url', 'yoast-seo-analysis-package', 'yoast-seo-externals-redux', 'yoast-seo-feature-flag-package', 'yoast-seo-prop-types-package', 'yoast-seo-react-helmet-package', 'yoast-seo-redux-js-toolkit-package', 'yoast-seo-ui-library-package'), 'version' => 'fd37eb7a8998673afc57'), 'used-keywords-assessment.js' => array('dependencies' => array('wp-polyfill', 'yoast-seo-analysis-package'), 'version' => 'f2d934f4e70fdace40fc'), 'react-select.js' => array('dependencies' => array('react', 'react-dom', 'wp-polyfill', 'yoast-seo-prop-types-package'), 'version' => '016b19f563b56deb8ad5'), 'workouts.js' => array('dependencies' => array('lodash', 'react', 'wp-api-fetch', 'wp-components', 'wp-compose', 'wp-data', 'wp-dom-ready', 'wp-element', 'wp-i18n', 'wp-polyfill', 'yoast-seo-components-new-package', 'yoast-seo-externals-contexts', 'yoast-seo-helpers-package', 'yoast-seo-prop-types-package', 'yoast-seo-styled-components-package'), 'version' => '5285008d6544c5f1c0d4'), 'frontend-inspector-resources.js' => array('dependencies' => array('lodash', 'react', 'wp-i18n', 'wp-polyfill', 'yoast-seo-analysis-package', 'yoast-seo-components-new-package', 'yoast-seo-prop-types-package', 'yoast-seo-style-guide-package'), 'version' => '9f0c47d9fdc1587c505a'));
