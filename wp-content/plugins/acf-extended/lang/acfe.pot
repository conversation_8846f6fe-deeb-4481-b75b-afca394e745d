#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields: Extended\n"
"POT-Creation-Date: 2024-12-19 08:49+0100\n"
"PO-Revision-Date: 2021-07-27 23:12+0200\n"
"Last-Translator: \n"
"Language-Team: https://www.acf-extended.com\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: acf-extended.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: .build\n"

#: acf-extended.php:451
msgid ""
"An another version of ACF Extended has been detected. Please activate only "
"one version."
msgstr ""

#: acf-extended.php:453
msgid "ACF Extended Pro has been detected. Please activate only one version."
msgstr ""

#: assets/js/acfe-admin.js:325 includes/modules/option/module-option.php:500
#: includes/modules/options-page/module-options-page.php:313
#: pro/includes/updates.php:398
msgid "Yes"
msgstr ""

#: assets/js/acfe-admin.js:326 includes/modules/option/module-option.php:499
#: includes/modules/options-page/module-options-page.php:313
#: pro/includes/updates.php:412
msgid "No"
msgstr ""

#: assets/js/acfe-input.js:2270
msgid "Select Image"
msgstr ""

#: assets/js/acfe-input.js:2276 includes/admin/tools/module-import.php:58
#: pro/assets/js/acfe-pro-input.js:1858 pro/assets/js/acfe-pro-input.js:1873
#: pro/includes/admin/tools/settings-import.php:40
msgid "Select File"
msgstr ""

#: assets/js/acfe-input.js:2328 pro/assets/js/acfe-pro-input.js:1905
msgid "Edit File"
msgstr ""

#: assets/js/acfe-input.js:2329 pro/assets/js/acfe-pro-input.js:1906
msgid "Update File"
msgstr ""

#: assets/js/acfe-input.js:2335 assets/js/acfe-input.js:2409
msgid "Edit Image"
msgstr ""

#: assets/js/acfe-input.js:2336 assets/js/acfe-input.js:2410
msgid "Update Image"
msgstr ""

#: assets/js/acfe-input.js:2368
msgid "Maximum selection reached"
msgstr ""

#: assets/js/acfe-input.js:2377
msgid "Add Image to Gallery"
msgstr ""

#: assets/js/acfe-input.js:3051 assets/js/acfe-input.js:5087
#: includes/assets.php:124 includes/field-groups/field-group-meta.php:160
#: includes/fields-settings/data.php:94
#: includes/fields/field-advanced-link.php:100
#: includes/fields/field-flexible-content-edit.php:278
#: includes/fields/field-flexible-content-settings.php:275
#: includes/fields/field-flexible-content.php:812
#: includes/fields/field-flexible-content.php:818
#: pro/assets/js/acfe-pro-admin.js:616 pro/assets/js/acfe-pro-input.js:1425
#: pro/includes/field-groups/field-group-ui.php:619
#: pro/includes/fields/field-payment.php:512
#: pro/includes/modules/dev/module-dev-clean-meta.php:116
#: pro/includes/modules/dev/module-dev-metabox.php:121
#: pro/includes/modules/dev/module-dev-metabox.php:196
#: pro/includes/modules/dev/module-dev-metabox.php:265
#: pro/includes/modules/dev/module-dev-metabox.php:369
#: pro/includes/modules/dev/module-dev-metabox.php:427
#: pro/includes/modules/dev/module-dev-metabox.php:485
#: pro/includes/modules/dev/module-dev-metabox.php:541
msgid "Close"
msgstr ""

#: assets/js/acfe-input.js:3614 includes/fields/field-flexible-content.php:38
msgid "Layout data has been copied to your clipboard."
msgstr ""

#: assets/js/acfe-input.js:3614 assets/js/acfe-input.js:3644
#: includes/fields/field-flexible-content.php:42
msgid ""
"You can now paste it on another page, using the \"Paste\" button action."
msgstr ""

#: assets/js/acfe-input.js:3615 assets/js/acfe-input.js:3645
#: assets/js/acfe.js:2330 includes/assets.php:130
#: includes/fields/field-flexible-content.php:40
msgid "Please copy the following data to your clipboard."
msgstr ""

#: assets/js/acfe-input.js:3615 assets/js/acfe-input.js:3645
#: includes/fields/field-flexible-content.php:43
msgid ""
"You can then paste it on another page, using the \"Paste\" button action."
msgstr ""

#: assets/js/acfe-input.js:3644 includes/fields/field-flexible-content.php:39
msgid "Layouts data have been copied to your clipboard."
msgstr ""

#: assets/js/acfe-input.js:3656 includes/fields/field-flexible-content.php:41
msgid "Please paste previously copied layout data in the following field:"
msgstr ""

#: assets/js/acfe-input.js:4373 includes/fields/field-flexible-content.php:320
msgid "Add Row"
msgstr ""

#: assets/js/acfe.js:2329 includes/assets.php:129
msgid "Data has been copied to your clipboard."
msgstr ""

#: includes/acfe-deprecated-functions.php:112
#, php-format
msgid ""
"%1$s is <strong>deprecated</strong> since version %2$s! Use %3$s instead."
msgstr ""

#: includes/acfe-deprecated-functions.php:114
#, php-format
msgid ""
"%1$s is <strong>deprecated</strong> since version %2$s with no alternative "
"available."
msgstr ""

#: includes/acfe-field-functions.php:597 includes/module-legacy.php:534
#: includes/modules/form/module-form-fields.php:977
msgid "no label"
msgstr ""

#: includes/acfe-field-group-functions.php:468
#: includes/modules/block-type/module-block-type.php:207
#: includes/modules/form/module-form-fields.php:1217
#: includes/modules/form/module-form.php:61
#: includes/modules/form/module-form.php:256
#: includes/modules/options-page/module-options-page.php:238
#: pro/includes/fields/field-field-groups.php:17
#: pro/includes/modules/template/module-template.php:59
msgid "Field Groups"
msgstr ""

#: includes/acfe-field-group-functions.php:536
msgid "edit"
msgstr ""

#: includes/acfe-field-group-functions.php:553
#: includes/modules/form/module-form-fields.php:1584
#: pro/includes/fields/field-color-picker.php:69
#: pro/includes/fields/field-color-picker.php:166
#: pro/includes/fields/field-menu-locations.php:122
msgid "Label"
msgstr ""

#: includes/acfe-field-group-functions.php:554
#: includes/modules/block-type/module-block-type.php:59
#: includes/modules/dev/module-dev.php:436
#: includes/modules/form/module-form-action-term.php:754
#: includes/modules/form/module-form-action-term.php:1138
#: includes/modules/form/module-form-fields.php:1198
#: includes/modules/form/module-form.php:60
#: includes/modules/option/module-option-table.php:226
#: includes/modules/option/module-option.php:361
#: includes/modules/post-type/module-post-type.php:60
#: includes/modules/taxonomy/module-taxonomy.php:60
#: pro/includes/fields/field-address.php:40
#: pro/includes/fields/field-menu-locations.php:121
#: pro/includes/fields/field-post-field.php:53
#: pro/includes/modules/dev/module-dev-edit-meta.php:95
#: pro/includes/modules/template/module-template.php:57
msgid "Name"
msgstr ""

#: includes/acfe-field-group-functions.php:555
#: includes/field-groups/field-group-meta.php:64
#: pro/includes/field-groups/field-group-ui.php:190
#: pro/includes/field-groups/field-group-ui.php:353
#: pro/includes/field-groups/field-group-ui.php:522
msgid "Key"
msgstr ""

#: includes/acfe-field-group-functions.php:556
#: includes/fields/field-advanced-link.php:153
#: pro/includes/modules/dev/module-dev-metabox.php:82
msgid "Type"
msgstr ""

#: includes/acfe-file-functions.php:204
#: includes/field-groups/field-groups.php:863
#: includes/field-groups/field-groups.php:873
#: includes/field-groups/field-groups.php:883
msgid "Located"
msgstr ""

#: includes/acfe-file-functions.php:205
msgid "Not found"
msgstr ""

#: includes/acfe-file-functions.php:220 includes/acfe-file-functions.php:231
#: includes/field-groups/field-groups.php:866
msgid "in theme:"
msgstr ""

#: includes/acfe-file-functions.php:242
#: includes/field-groups/field-groups.php:876
msgid "in plugin:"
msgstr ""

#: includes/acfe-file-functions.php:253
#: includes/field-groups/field-groups.php:886
msgid "in:"
msgstr ""

#: includes/acfe-form-functions.php:200
msgid "Input is invalid: Must be a json string or an array."
msgstr ""

#: includes/acfe-screen-functions.php:49
msgid "Custom Fields"
msgstr ""

#: includes/acfe-user-functions.php:23
msgid "Super Admin"
msgstr ""

#: includes/admin/plugins.php:32
msgid "Advanced Custom Fields"
msgstr ""

#: includes/admin/settings.php:534 includes/admin/settings.php:695
#: includes/admin/settings.php:705 includes/fields-settings/settings.php:136
#: includes/fields/field-flexible-content.php:92
#: includes/modules/form/module-form-fields.php:1264
#: pro/includes/admin/settings.php:316 pro/includes/admin/settings.php:376
#: pro/includes/module-item.php:52
msgid "Settings"
msgstr ""

#: includes/admin/tools/field-groups-export.php:37
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/field-groups-local.php:19
msgid "Export Local Field Groups"
msgstr ""

#: includes/admin/tools/field-groups-local.php:40
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/field-groups-local.php:62
msgid "No field group selected"
msgstr ""

#: includes/admin/tools/field-groups-local.php:186
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""

#: includes/admin/tools/field-groups-local.php:226
#: includes/admin/tools/module-export.php:210
#: pro/includes/admin/tools/rewrite-rules-export.php:116
#: pro/includes/admin/tools/settings-export.php:147
msgid "Copy to clipboard"
msgstr ""

#: includes/admin/tools/field-groups-local.php:254
#: includes/admin/tools/module-export.php:265
#: pro/includes/admin/tools/rewrite-rules-export.php:144
#: pro/includes/admin/tools/settings-export.php:203
msgid "Copied"
msgstr ""

#: includes/admin/tools/module-export.php:126
#: includes/admin/tools/module-export.php:315
#: pro/includes/admin/tools/settings-export.php:59
msgid "Export File"
msgstr ""

#: includes/admin/tools/module-export.php:130
#: includes/admin/tools/module-export.php:319
#: pro/includes/admin/tools/rewrite-rules-export.php:58
#: pro/includes/admin/tools/settings-export.php:60
msgid "Generate PHP"
msgstr ""

#: includes/admin/tools/module-export.php:166
msgid ""
"You can copy and paste the following code to your theme's <code>functions."
"php</code> file or include it within an external file."
msgstr ""

#: includes/admin/tools/module-import.php:69
#: pro/includes/admin/tools/settings-import.php:51
msgid "Import File"
msgstr ""

#: includes/admin/tools/module-import.php:162
#: pro/includes/admin/tools/settings-import.php:88
msgid "No file selected"
msgstr ""

#: includes/admin/tools/module-import.php:173
#: pro/includes/admin/tools/settings-import.php:99
msgid "Error uploading file. Please try again"
msgstr ""

#: includes/admin/tools/module-import.php:181
#: pro/includes/admin/tools/settings-import.php:107
msgid "Incorrect file type"
msgstr ""

#: includes/admin/tools/module-import.php:193
#: pro/includes/admin/tools/settings-import.php:119
msgid "Import file empty"
msgstr ""

#: includes/admin/views/html-options-edit.php:4
#: includes/modules/option/module-option.php:517
msgid "Edit Option"
msgstr ""

#: includes/admin/views/html-options-edit.php:6
#: includes/modules/option/module-option.php:512
msgid "Add Option"
msgstr ""

#: includes/admin/views/html-options-list.php:3
#: includes/modules/option/module-option-table.php:22
#: includes/modules/option/module-option.php:60
msgid "Options"
msgstr ""

#: includes/admin/views/html-options-list.php:4
#: pro/includes/locations/post-screen.php:22
msgid "Add New"
msgstr ""

#: includes/assets.php:125 includes/locations/post-type-list.php:199
#: includes/locations/taxonomy-list.php:199
#: includes/modules/option/module-option.php:541
#: includes/modules/options-page/module-options-page.php:79
#: pro/assets/js/acfe-pro-admin.js:54 pro/includes/admin/dashboard.php:185
#: pro/includes/admin/settings.php:358
#: pro/includes/locations/attachment-list.php:191
#: pro/includes/locations/user-list.php:189
#: pro/includes/modules/dev/module-dev-metabox.php:688
msgid "Update"
msgstr ""

#: includes/assets.php:126 pro/assets/js/acfe-pro-input.js:6059
msgid "Read more"
msgstr ""

#: includes/assets.php:127
msgid "Details"
msgstr ""

#: includes/assets.php:128 pro/assets/js/acfe-pro-admin.js:615
#: pro/assets/js/acfe-pro-admin.js:616
msgid "Debug"
msgstr ""

#: includes/field-groups/field-group-advanced.php:51
#: pro/includes/field-groups/field-group-ui.php:102
#: pro/includes/field-groups/field-group-ui.php:267
#: pro/includes/field-groups/field-group-ui.php:435
msgid "Advanced settings"
msgstr ""

#: includes/field-groups/field-group-advanced.php:56
#: pro/includes/field-groups/field-group-ui.php:107
#: pro/includes/field-groups/field-group-ui.php:272
#: pro/includes/field-groups/field-group-ui.php:440
msgid "Enable advanced fields settings & validation"
msgstr ""

#: includes/field-groups/field-group-category.php:55
#: includes/field-groups/field-group-category.php:56
msgctxt "Category"
msgid "Categories"
msgstr ""

#: includes/field-groups/field-group-category.php:57
msgid "Search categories"
msgstr ""

#: includes/field-groups/field-group-category.php:58
msgid "All categories"
msgstr ""

#: includes/field-groups/field-group-category.php:59
msgid "Parent category"
msgstr ""

#: includes/field-groups/field-group-category.php:60
msgid "Parent category:"
msgstr ""

#: includes/field-groups/field-group-category.php:61
msgid "Edit category"
msgstr ""

#: includes/field-groups/field-group-category.php:62
msgid "Update category"
msgstr ""

#: includes/field-groups/field-group-category.php:63
msgid "Add New category"
msgstr ""

#: includes/field-groups/field-group-category.php:64
msgid "New category name"
msgstr ""

#: includes/field-groups/field-group-category.php:65
msgid "category"
msgstr ""

#: includes/field-groups/field-group-category.php:80
#: includes/field-groups/field-group-category.php:147
#: includes/fields/field-flexible-content-select.php:194
msgid "Categories"
msgstr ""

#: includes/field-groups/field-group-hide-on-screen.php:53
#: pro/includes/fields/field-block-editor.php:19
msgid "Block Editor"
msgstr ""

#: includes/field-groups/field-group-instruction-placement.php:39
#: includes/modules/form/module-form-fields.php:1949
msgid "Above fields"
msgstr ""

#: includes/field-groups/field-group-instruction-placement.php:40
#: includes/modules/form/module-form-fields.php:1587
msgid "Tooltip"
msgstr ""

#: includes/field-groups/field-group-meta.php:34
msgid "Field group"
msgstr ""

#: includes/field-groups/field-group-meta.php:48
#: pro/includes/field-groups/field-group-ui.php:174
#: pro/includes/field-groups/field-group-ui.php:337
#: pro/includes/field-groups/field-group-ui.php:506
msgid "Custom meta data"
msgstr ""

#: includes/field-groups/field-group-meta.php:51
#: pro/includes/field-groups/field-group-ui.php:177
#: pro/includes/field-groups/field-group-ui.php:340
#: pro/includes/field-groups/field-group-ui.php:509
msgid "Add custom meta data to the field group."
msgstr ""

#: includes/field-groups/field-group-meta.php:54
#: pro/includes/field-groups/field-group-ui.php:180
#: pro/includes/field-groups/field-group-ui.php:343
#: pro/includes/field-groups/field-group-ui.php:512
msgid "+ Meta"
msgstr ""

#: includes/field-groups/field-group-meta.php:81
#: includes/fields/field-hidden.php:66 includes/modules/dev/module-dev.php:437
#: includes/modules/option/module-option-table.php:227
#: includes/modules/option/module-option.php:396
#: includes/modules/option/module-option.php:427
#: includes/modules/option/module-option.php:460
#: pro/includes/field-groups/field-group-ui.php:207
#: pro/includes/field-groups/field-group-ui.php:370
#: pro/includes/field-groups/field-group-ui.php:539
#: pro/includes/fields/field-color-picker.php:163
#: pro/includes/fields/field-image-selector.php:119
#: pro/includes/modules/dev/module-dev-edit-meta.php:117
msgid "Value"
msgstr ""

#: includes/field-groups/field-group-meta.php:101
#: pro/includes/field-groups/field-group-ui.php:244
#: pro/includes/field-groups/field-group-ui.php:403
#: pro/includes/field-groups/field-group-ui.php:412
#: pro/includes/field-groups/field-group-ui.php:572
#: pro/includes/field-groups/field-group-ui.php:581
msgid "Note"
msgstr ""

#: includes/field-groups/field-group-meta.php:105
#: pro/includes/field-groups/field-group-ui.php:248
#: pro/includes/field-groups/field-group-ui.php:416
#: pro/includes/field-groups/field-group-ui.php:585
msgid "Add personal note. Only visible to administrators"
msgstr ""

#: includes/field-groups/field-group-meta.php:115
#: pro/includes/field-groups/field-group-ui.php:227
#: pro/includes/field-groups/field-group-ui.php:390
#: pro/includes/field-groups/field-group-ui.php:559
msgid "Field group data"
msgstr ""

#: includes/field-groups/field-group-meta.php:116
#: pro/includes/field-groups/field-group-ui.php:228
#: pro/includes/field-groups/field-group-ui.php:391
#: pro/includes/field-groups/field-group-ui.php:560
msgid "View raw field group data, for development use"
msgstr ""

#: includes/field-groups/field-group-meta.php:147
#: includes/field-groups/field-group-meta.php:159
#: includes/fields-settings/data.php:93
#: pro/includes/field-groups/field-group-ui.php:328
#: pro/includes/field-groups/field-group-ui.php:497
#: pro/includes/field-groups/field-group-ui.php:606
#: pro/includes/field-groups/field-group-ui.php:618
msgid "Data"
msgstr ""

#: includes/field-groups/field-group.php:49
#: includes/fields-settings/settings.php:99
msgid "Advanced Settings"
msgstr ""

#: includes/field-groups/field-group.php:97
#: pro/includes/field-groups/field-group-ui.php:117
#: pro/includes/field-groups/field-group-ui.php:282
#: pro/includes/field-groups/field-group-ui.php:450
msgid "Display title"
msgstr ""

#: includes/field-groups/field-group.php:98
#: pro/includes/field-groups/field-group-ui.php:118
#: pro/includes/field-groups/field-group-ui.php:283
#: pro/includes/field-groups/field-group-ui.php:451
msgid "Render this title on edit post screen"
msgstr ""

#: includes/field-groups/field-group.php:122
#: includes/field-groups/field-groups.php:575
#: includes/field-groups/field-groups.php:583 pro/includes/module-item.php:95
#: pro/includes/module-local.php:716 pro/includes/module-posts.php:311
msgid "Sync available"
msgstr ""

#: includes/field-groups/field-group.php:123
msgid "Local json file is different from the version in database."
msgstr ""

#: includes/field-groups/field-group.php:134
msgid "Synchronize"
msgstr ""

#: includes/field-groups/field-group.php:143 pro/includes/module-item.php:161
msgid "Review changes"
msgstr ""

#: includes/field-groups/field-group.php:164 pro/includes/module-item.php:170
msgid "Auto Sync"
msgstr ""

#: includes/field-groups/field-group.php:181
#: includes/fields-settings/permissions.php:23
#: pro/includes/field-groups/field-group-ui.php:151
#: pro/includes/field-groups/field-group-ui.php:300
#: pro/includes/field-groups/field-group-ui.php:309
#: pro/includes/field-groups/field-group-ui.php:469
#: pro/includes/field-groups/field-group-ui.php:478
msgid "Permissions"
msgstr ""

#: includes/field-groups/field-group.php:185
#: pro/includes/field-groups/field-group-ui.php:155
#: pro/includes/field-groups/field-group-ui.php:313
#: pro/includes/field-groups/field-group-ui.php:482
msgid ""
"Select user roles that are allowed to view and edit this field group in post "
"edition"
msgstr ""

#: includes/field-groups/field-groups-local.php:83
msgid "Local"
msgstr ""

#: includes/field-groups/field-groups-local.php:160
#: pro/includes/module-posts.php:441
#, php-format
msgid "Select %s"
msgstr ""

#: includes/field-groups/field-groups-local.php:243
#: includes/field-groups/field-groups.php:780
msgid "Export PHP"
msgstr ""

#: includes/field-groups/field-groups-local.php:244
#: includes/field-groups/field-groups.php:781
msgid "Export Json"
msgstr ""

#: includes/field-groups/field-groups-local.php:245
msgid "Sync to database"
msgstr ""

#: includes/field-groups/field-groups.php:102
#: includes/field-groups/field-groups.php:112
#: includes/fields/field-flexible-content-select.php:105
#: pro/includes/field-groups/field-group-hide-on-screen.php:39
#: pro/includes/fields/field-post-field.php:60
#: pro/includes/modules/script/module-script-table.php:257
msgid "Title"
msgstr ""

#: includes/field-groups/field-groups.php:103
#: includes/field-groups/field-groups.php:113
#: includes/modules/form/module-form-action-post.php:1708
#: includes/modules/form/module-form-action-term.php:1054
#: includes/modules/form/module-form-action-user.php:1919
#: pro/includes/modules/form/module-form-action-option.php:403
msgid "Source"
msgstr ""

#: includes/field-groups/field-groups.php:104
#: includes/field-groups/field-groups.php:114
#: includes/field-groups/field-groups.php:167
#: pro/includes/fields/field-fields.php:17
msgid "Fields"
msgstr ""

#: includes/field-groups/field-groups.php:105
#: includes/field-groups/field-groups.php:115
#: includes/field-groups/field-groups.php:144
#: includes/field-groups/field-groups.php:168
msgid "Location"
msgstr ""

#: includes/field-groups/field-groups.php:121
#: includes/field-groups/field-groups.php:147
#: includes/field-groups/field-groups.php:171
#: includes/modules/form/module-form-action-post.php:1672
#: includes/modules/form/module-form-action-term.php:1017
#: includes/modules/form/module-form-action-user.php:1872
#: pro/includes/module-posts.php:498
#: pro/includes/modules/form/module-form-action-option.php:368
msgid "Load"
msgstr ""

#: includes/field-groups/field-groups.php:125
msgid "PHP Sync"
msgstr ""

#: includes/field-groups/field-groups.php:129
msgid "Json Sync"
msgstr ""

#: includes/field-groups/field-groups.php:151
#: includes/field-groups/field-groups.php:175 pro/includes/module-posts.php:502
msgid "PHP"
msgstr ""

#: includes/field-groups/field-groups.php:155
#: includes/field-groups/field-groups.php:179
#: includes/fields/field-code-editor.php:77 pro/includes/module-posts.php:506
msgid "Json"
msgstr ""

#: includes/field-groups/field-groups.php:287
msgid "Theme/Plugin"
msgstr ""

#: includes/field-groups/field-groups.php:421
#: includes/field-groups/field-groups.php:428
#: includes/field-groups/field-groups.php:593 pro/includes/module-local.php:743
msgid "Synchronized"
msgstr ""

#: includes/field-groups/field-groups.php:421
#: includes/field-groups/field-groups.php:422
msgid "Warning: Duplicated PHP code found in theme/plugin."
msgstr ""

#: includes/field-groups/field-groups.php:442
#: includes/field-groups/field-groups.php:607 pro/includes/module-local.php:770
#: pro/includes/module-local.php:771
msgid "Awaiting save"
msgstr ""

#: includes/field-groups/field-groups.php:442
#: includes/field-groups/field-groups.php:443
#: includes/field-groups/field-groups.php:450
#: includes/field-groups/field-groups.php:607
#: includes/field-groups/field-groups.php:608
#: includes/field-groups/field-groups.php:615 pro/includes/module-local.php:770
#: pro/includes/module-local.php:771 pro/includes/module-local.php:784
msgid "Save path"
msgstr ""

#: includes/field-groups/field-groups.php:558
msgid "Sync"
msgstr ""

#: includes/field-groups/field-groups.php:558
#: includes/field-groups/field-groups.php:566
msgid "Import"
msgstr ""

#: includes/field-groups/field-groups.php:566 pro/includes/module-local.php:720
#: pro/includes/module-local.php:734
msgid "Review"
msgstr ""

#: includes/field-groups/field-groups.php:711
#: includes/field-groups/field-groups.php:719 includes/module-legacy.php:371
#: includes/module-posts.php:171 pro/includes/module-posts.php:449
msgctxt "post status"
msgid "Disabled"
msgstr ""

#: includes/field-groups/field-groups.php:728
msgid "Alternative title"
msgstr ""

#: includes/fields-settings/bidirectional.php:45
msgid "Bidirectional"
msgstr ""

#: includes/fields-settings/bidirectional.php:48
msgid "Set the field as bidirectional"
msgstr ""

#: includes/fields-settings/data.php:71
msgid "Field data unavailable"
msgstr ""

#: includes/fields-settings/data.php:77
msgid "Post object unavailable"
msgstr ""

#: includes/fields-settings/data.php:80
msgid "Field Data"
msgstr ""

#: includes/fields-settings/permissions.php:26
msgid "Restrict user roles that are allowed to view and edit this field"
msgstr ""

#: includes/fields-settings/settings.php:102
msgid "Change field settings based on location"
msgstr ""

#: includes/fields-settings/settings.php:104
msgid "Add settings"
msgstr ""

#: includes/fields-settings/settings.php:141
msgid "+"
msgstr ""

#: includes/fields-settings/validation.php:156
msgid "Advanced Validation"
msgstr ""

#: includes/fields-settings/validation.php:159
msgid "Validate value against rules"
msgstr ""

#: includes/fields-settings/validation.php:161
msgid "Add validation"
msgstr ""

#: includes/fields-settings/validation.php:193
msgid "Rules"
msgstr ""

#: includes/fields-settings/validation.php:198
msgid "+ AND"
msgstr ""

#: includes/fields/field-advanced-link.php:17
msgid "Advanced Link"
msgstr ""

#: includes/fields/field-advanced-link.php:39
msgid "Filter by Post Type"
msgstr ""

#: includes/fields/field-advanced-link.php:47
#: includes/fields/field-post-types.php:61
msgid "All post types"
msgstr ""

#: includes/fields/field-advanced-link.php:52
msgid "Filter by Taxonomy"
msgstr ""

#: includes/fields/field-advanced-link.php:60
#: includes/fields/field-taxonomies.php:61
#: includes/fields/field-taxonomy-terms.php:376
msgid "All taxonomies"
msgstr ""

#: includes/fields/field-advanced-link.php:118
msgid "Select Link"
msgstr ""

#: includes/fields/field-advanced-link.php:123
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/field-advanced-link.php:124
#: includes/fields/field-clone.php:164 includes/fields/field-clone.php:228
#: includes/fields/field-group.php:97 includes/fields/field-group.php:156
#: includes/locations/post-type-archive.php:236
#: includes/modules/option/module-option-table.php:208
#: includes/modules/option/module-option.php:516
#: includes/modules/ui/module-ui-settings.php:70
#: includes/modules/ui/module-ui-term.php:102
#: includes/modules/ui/module-ui-user.php:166
#: pro/includes/fields/field-file.php:540
#: pro/includes/locations/post-screen.php:23
#: pro/includes/modules/dev/module-dev-edit-meta.php:56
msgid "Edit"
msgstr ""

#: includes/fields/field-advanced-link.php:125
#: pro/includes/fields/field-file.php:542
msgid "Remove"
msgstr ""

#: includes/fields/field-advanced-link.php:158
#: includes/fields/field-advanced-link.php:168
msgid "URL"
msgstr ""

#: includes/fields/field-advanced-link.php:159
#: includes/fields/field-advanced-link.php:188
#: includes/locations/post-type-archive.php:291
#: includes/locations/post-type-list.php:214
#: includes/modules/form/module-form-action-post.php:944
#: pro/includes/locations/menu-item-type.php:23
msgid "Post"
msgstr ""

#: includes/fields/field-advanced-link.php:160
#: includes/fields/field-advanced-link.php:213
#: includes/modules/form/module-form-action-term.php:641
msgid "Term"
msgstr ""

#: includes/fields/field-advanced-link.php:237
msgid "Link text"
msgstr ""

#: includes/fields/field-advanced-link.php:247
#: includes/modules/form/module-form-action-post.php:972
#: includes/modules/form/module-form-action-term.php:670
#: includes/modules/form/module-form-action-user.php:1433
#: pro/includes/modules/form/module-form-action-option.php:315
msgid "Target"
msgstr ""

#: includes/fields/field-advanced-link.php:249
msgid "Open in a new window"
msgstr ""

#: includes/fields/field-advanced-link.php:732
msgid "Archives"
msgstr ""

#: includes/fields/field-button.php:17 includes/fields/field-button.php:92
msgid "Button"
msgstr ""

#: includes/fields/field-button.php:20 includes/modules/form/module-form.php:91
#: includes/modules/option/module-option.php:511
#: pro/includes/modules/dev/module-dev-metabox.php:634
msgid "Submit"
msgstr ""

#: includes/fields/field-button.php:78
#: pro/includes/fields/field-payment.php:161
msgid "Button value"
msgstr ""

#: includes/fields/field-button.php:79
#: pro/includes/fields/field-payment.php:162
msgid "Set a default button value"
msgstr ""

#: includes/fields/field-button.php:86
msgid "Button type"
msgstr ""

#: includes/fields/field-button.php:87
msgid "Choose the button type"
msgstr ""

#: includes/fields/field-button.php:93
msgid "Input"
msgstr ""

#: includes/fields/field-button.php:99
#: pro/includes/fields/field-payment.php:176
msgid "Button attributes"
msgstr ""

#: includes/fields/field-button.php:103
#: pro/includes/fields/field-payment.php:180
msgid "class"
msgstr ""

#: includes/fields/field-button.php:112
#: pro/includes/fields/field-payment.php:196
msgid "id"
msgstr ""

#: includes/fields/field-button.php:118
#: pro/includes/fields/field-payment.php:209
msgid "Before HTML"
msgstr ""

#: includes/fields/field-button.php:119
#: pro/includes/fields/field-payment.php:210
msgid "Custom HTML before the button"
msgstr ""

#: includes/fields/field-button.php:127
#: pro/includes/fields/field-payment.php:225
msgid "After HTML"
msgstr ""

#: includes/fields/field-button.php:128
#: pro/includes/fields/field-payment.php:226
msgid "Custom HTML after the button"
msgstr ""

#: includes/fields/field-button.php:136
msgid "Ajax Request"
msgstr ""

#: includes/fields/field-button.php:137
msgid "Trigger ajax event on click"
msgstr ""

#: includes/fields/field-button.php:137
#: includes/fields/field-flexible-content-actions.php:87
#: includes/fields/field-flexible-content-async.php:65
#: includes/fields/field-flexible-content-controls.php:75
#: includes/fields/field-flexible-content-controls.php:103
#: includes/fields/field-flexible-content-edit.php:81
#: includes/fields/field-flexible-content-hide.php:66
#: includes/fields/field-flexible-content-preview.php:83
#: includes/fields/field-flexible-content-preview.php:106
#: includes/fields/field-flexible-content-preview.php:134
#: includes/fields/field-flexible-content-select.php:78
#: includes/fields/field-flexible-content-settings.php:74
#: includes/fields/field-flexible-content-thumbnail.php:74
#: pro/includes/fields/field-flexible-content-grid.php:136
#: pro/includes/fields/field-flexible-content-iframe.php:45
#: pro/includes/fields/field-flexible-content-iframe.php:78
#: pro/includes/fields/field-flexible-content-locations.php:247
msgid "See documentation"
msgstr ""

#: includes/fields/field-checkbox.php:38
msgid "You may use \"## Title\" to create a group of options."
msgstr ""

#: includes/fields/field-clone.php:86 includes/fields/field-group.php:36
msgid "Seamless Style"
msgstr ""

#: includes/fields/field-clone.php:89 includes/fields/field-group.php:38
msgid "Enable better CSS integration: remove borders and padding"
msgstr ""

#: includes/fields/field-clone.php:112 includes/fields/field-group.php:55
msgid "Edition modal"
msgstr ""

#: includes/fields/field-clone.php:115 includes/fields/field-group.php:57
msgid "Edit fields in a modal"
msgstr ""

#: includes/fields/field-clone.php:138 includes/fields/field-group.php:74
msgid "Edition modal: Close button"
msgstr ""

#: includes/fields/field-clone.php:141 includes/fields/field-group.php:76
msgid "Display close button"
msgstr ""

#: includes/fields/field-clone.php:159 includes/fields/field-group.php:93
msgid "Edition modal: Text button"
msgstr ""

#: includes/fields/field-clone.php:162 includes/fields/field-group.php:95
msgid "Text displayed in the edition modal button"
msgstr ""

#: includes/fields/field-clone.php:178 includes/fields/field-group.php:110
msgid "Edition modal: Size"
msgstr ""

#: includes/fields/field-clone.php:181 includes/fields/field-group.php:112
msgid "Choose the modal size"
msgstr ""

#: includes/fields/field-code-editor.php:24
msgid "Code Editor"
msgstr ""

#: includes/fields/field-code-editor.php:52 includes/fields/field-forms.php:80
#: includes/fields/field-post-statuses.php:80
#: includes/fields/field-post-types.php:80 includes/fields/field-slug.php:39
#: includes/fields/field-taxonomies.php:80
#: includes/fields/field-taxonomy-terms.php:509
#: includes/fields/field-user-roles.php:78
#: pro/includes/fields/field-block-types.php:107
#: pro/includes/fields/field-countries.php:152
#: pro/includes/fields/field-currencies.php:152
#: pro/includes/fields/field-field-groups.php:114
#: pro/includes/fields/field-field-types.php:112
#: pro/includes/fields/field-fields.php:122
#: pro/includes/fields/field-image-selector.php:58
#: pro/includes/fields/field-image-sizes.php:119
#: pro/includes/fields/field-languages.php:231
#: pro/includes/fields/field-menu-locations.php:108
#: pro/includes/fields/field-menus.php:108
#: pro/includes/fields/field-options-pages.php:110
#: pro/includes/fields/field-payment-cart.php:90
#: pro/includes/fields/field-phone-number.php:165
#: pro/includes/fields/field-post-formats.php:119
#: pro/includes/fields/field-templates.php:115
msgid "Default Value"
msgstr ""

#: includes/fields/field-code-editor.php:53 includes/fields/field-slug.php:40
msgid "Appears when creating a new post"
msgstr ""

#: includes/fields/field-code-editor.php:61 includes/fields/field-forms.php:126
#: includes/fields/field-post-statuses.php:186
#: includes/fields/field-post-types.php:186 includes/fields/field-select.php:54
#: includes/fields/field-taxonomies.php:186
#: includes/fields/field-taxonomy-terms.php:574
#: includes/fields/field-user-roles.php:171
#: pro/includes/fields/field-address.php:86
#: pro/includes/fields/field-block-types.php:212
#: pro/includes/fields/field-countries.php:331
#: pro/includes/fields/field-currencies.php:320
#: pro/includes/fields/field-date-picker.php:35
#: pro/includes/fields/field-date-range-picker.php:110
#: pro/includes/fields/field-date-time-picker.php:43
#: pro/includes/fields/field-field-groups.php:221
#: pro/includes/fields/field-field-types.php:218
#: pro/includes/fields/field-fields.php:230
#: pro/includes/fields/field-image-sizes.php:239
#: pro/includes/fields/field-languages.php:398
#: pro/includes/fields/field-menu-locations.php:215
#: pro/includes/fields/field-menus.php:215
#: pro/includes/fields/field-options-pages.php:216
#: pro/includes/fields/field-payment-cart.php:186
#: pro/includes/fields/field-phone-number.php:173
#: pro/includes/fields/field-post-formats.php:211
#: pro/includes/fields/field-templates.php:221
#: pro/includes/fields/field-time-picker.php:40
msgid "Placeholder"
msgstr ""

#: includes/fields/field-code-editor.php:62 includes/fields/field-forms.php:127
#: includes/fields/field-post-statuses.php:187
#: includes/fields/field-post-types.php:187 includes/fields/field-select.php:55
#: includes/fields/field-slug.php:48 includes/fields/field-taxonomies.php:187
#: includes/fields/field-taxonomy-terms.php:575
#: includes/fields/field-user-roles.php:172
#: pro/includes/fields/field-address.php:87
#: pro/includes/fields/field-block-types.php:213
#: pro/includes/fields/field-countries.php:332
#: pro/includes/fields/field-currencies.php:321
#: pro/includes/fields/field-field-groups.php:222
#: pro/includes/fields/field-field-types.php:219
#: pro/includes/fields/field-fields.php:231
#: pro/includes/fields/field-image-sizes.php:240
#: pro/includes/fields/field-languages.php:399
#: pro/includes/fields/field-menu-locations.php:216
#: pro/includes/fields/field-menus.php:216
#: pro/includes/fields/field-options-pages.php:217
#: pro/includes/fields/field-payment-cart.php:187
#: pro/includes/fields/field-post-formats.php:212
#: pro/includes/fields/field-templates.php:222
msgid "Appears within the input"
msgstr ""

#: includes/fields/field-code-editor.php:70
msgid "Editor mode"
msgstr ""

#: includes/fields/field-code-editor.php:71
msgid "Choose the syntax highlight"
msgstr ""

#: includes/fields/field-code-editor.php:75
msgid "Text/HTML"
msgstr ""

#: includes/fields/field-code-editor.php:76
msgid "JavaScript"
msgstr ""

#: includes/fields/field-code-editor.php:78
msgid "CSS"
msgstr ""

#: includes/fields/field-code-editor.php:79
msgid "PHP (mixed)"
msgstr ""

#: includes/fields/field-code-editor.php:80
msgid "PHP (plain)"
msgstr ""

#: includes/fields/field-code-editor.php:86
msgid "Show Lines"
msgstr ""

#: includes/fields/field-code-editor.php:95
msgid "Indent Unit"
msgstr ""

#: includes/fields/field-code-editor.php:104 includes/fields/field-slug.php:71
msgid "Character Limit"
msgstr ""

#: includes/fields/field-code-editor.php:105 includes/fields/field-slug.php:72
msgid "Leave blank for no limit"
msgstr ""

#: includes/fields/field-code-editor.php:112
msgid "Rows"
msgstr ""

#: includes/fields/field-code-editor.php:113
msgid "Sets the textarea height"
msgstr ""

#: includes/fields/field-code-editor.php:121
msgid "Max rows"
msgstr ""

#: includes/fields/field-code-editor.php:122
msgid "Sets the textarea max height"
msgstr ""

#: includes/fields/field-code-editor.php:130 includes/fields/field-forms.php:88
#: includes/fields/field-post-statuses.php:88
#: includes/fields/field-post-types.php:88
#: includes/fields/field-taxonomies.php:88
#: includes/fields/field-taxonomy-terms.php:517
#: pro/includes/fields/field-block-types.php:115
#: pro/includes/fields/field-color-picker.php:157
#: pro/includes/fields/field-countries.php:160
#: pro/includes/fields/field-currencies.php:175
#: pro/includes/fields/field-field-groups.php:122
#: pro/includes/fields/field-field-types.php:120
#: pro/includes/fields/field-fields.php:130
#: pro/includes/fields/field-image-selector.php:113
#: pro/includes/fields/field-image-sizes.php:141
#: pro/includes/fields/field-languages.php:239
#: pro/includes/fields/field-menu-locations.php:116
#: pro/includes/fields/field-menus.php:116
#: pro/includes/fields/field-options-pages.php:118
#: pro/includes/fields/field-phone-number.php:193
#: pro/includes/fields/field-templates.php:123
msgid "Return Value"
msgstr ""

#: includes/fields/field-code-editor.php:136
msgid "HTML Entities"
msgstr ""

#: includes/fields/field-code-editor.php:137
msgid "New Lines to &lt;br&gt;"
msgstr ""

#: includes/fields/field-column.php:17
msgid "Column"
msgstr ""

#: includes/fields/field-column.php:36
msgid "Columns"
msgstr ""

#: includes/fields/field-column.php:68
msgid "Endpoint"
msgstr ""

#: includes/fields/field-column.php:69
msgid "Define an endpoint for the previous columns to stop."
msgstr ""

#: includes/fields/field-dynamic-render.php:17
#: includes/fields/field-flexible-content-preview.php:80
msgid "Dynamic Render"
msgstr ""

#: includes/fields/field-file.php:66 pro/includes/fields/field-file.php:533
msgid "File size"
msgstr ""

#: includes/fields/field-file.php:67
msgid "Min size"
msgstr ""

#: includes/fields/field-file.php:74
msgid "Max size"
msgstr ""

#: includes/fields/field-file.php:130 includes/fields/field-image.php:100
msgid "Uploader Type"
msgstr ""

#: includes/fields/field-file.php:133 includes/fields/field-image.php:103
msgid "Choose the uploader type"
msgstr ""

#: includes/fields/field-file.php:136 includes/fields/field-image.php:106
#: includes/modules/form/module-form-action-email.php:751
#: includes/modules/form/module-form-action-post.php:1018
#: includes/modules/form/module-form-action-post.php:1070
#: includes/modules/form/module-form-action-post.php:1099
#: includes/modules/form/module-form-action-post.php:1132
#: includes/modules/form/module-form-action-post.php:1159
#: includes/modules/form/module-form-action-post.php:1204
#: includes/modules/form/module-form-action-post.php:1277
#: includes/modules/form/module-form-action-post.php:1349
#: includes/modules/form/module-form-action-post.php:1420
#: includes/modules/form/module-form-action-post.php:1492
#: includes/modules/form/module-form-action-post.php:1566
#: includes/modules/form/module-form-action-post.php:1617
#: includes/modules/form/module-form-action-post.php:1754
#: includes/modules/form/module-form-action-term.php:716
#: includes/modules/form/module-form-action-term.php:774
#: includes/modules/form/module-form-action-term.php:802
#: includes/modules/form/module-form-action-term.php:827
#: includes/modules/form/module-form-action-term.php:880
#: includes/modules/form/module-form-action-term.php:954
#: includes/modules/form/module-form-action-term.php:1100
#: includes/modules/form/module-form-action-user.php:1344
#: includes/modules/form/module-form-action-user.php:1369
#: includes/modules/form/module-form-action-user.php:1394
#: includes/modules/form/module-form-action-user.php:1479
#: includes/modules/form/module-form-action-user.php:1530
#: includes/modules/form/module-form-action-user.php:1555
#: includes/modules/form/module-form-action-user.php:1583
#: includes/modules/form/module-form-action-user.php:1608
#: includes/modules/form/module-form-action-user.php:1633
#: includes/modules/form/module-form-action-user.php:1658
#: includes/modules/form/module-form-action-user.php:1683
#: includes/modules/form/module-form-action-user.php:1708
#: includes/modules/form/module-form-action-user.php:1754
#: includes/modules/form/module-form-action-user.php:1805
#: includes/modules/form/module-form-action-user.php:1965
#: includes/modules/form/module-form-fields.php:1351
#: pro/includes/fields/field-true-false.php:38
msgid "Default"
msgstr ""

#: includes/fields/field-file.php:138 includes/fields/field-image.php:108
#: includes/modules/form/module-form-fields.php:1353
msgid "Browser"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:72
msgid "Inline Title Edit"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:73
msgid "Toggle Layout"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:74
msgid "Copy/paste Layout"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:75
msgid "Lock Layouts"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:76
msgid "Close Button"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:80
msgid "Clone"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:84
msgid "Additional Actions"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:431
msgid "Toggle layout"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:440
msgid "Copy layout"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:449
msgid "Clone layout"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:474
msgid "Copy layouts"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:475
msgid "Paste layouts"
msgstr ""

#: includes/fields/field-flexible-content-actions.php:501
#: includes/fields/field-forms.php:325
#: includes/fields/field-post-statuses.php:300
#: includes/fields/field-post-types.php:300
#: includes/fields/field-taxonomies.php:300
#: includes/fields/field-taxonomy-terms.php:710
#: includes/fields/field-user-roles.php:285
#: pro/includes/fields/field-block-types.php:326
#: pro/includes/fields/field-countries.php:445
#: pro/includes/fields/field-currencies.php:434
#: pro/includes/fields/field-field-groups.php:335
#: pro/includes/fields/field-field-types.php:332
#: pro/includes/fields/field-fields.php:344
#: pro/includes/fields/field-image-selector.php:145
#: pro/includes/fields/field-image-sizes.php:353
#: pro/includes/fields/field-languages.php:512
#: pro/includes/fields/field-menu-locations.php:329
#: pro/includes/fields/field-menus.php:329
#: pro/includes/fields/field-options-pages.php:330
#: pro/includes/fields/field-payment-cart.php:281
#: pro/includes/fields/field-payment-selector.php:102
#: pro/includes/fields/field-post-formats.php:325
#: pro/includes/fields/field-templates.php:335
msgid "Layout"
msgstr ""

#: includes/fields/field-flexible-content-async.php:62
msgid "Asynchronous Settings"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:58
msgid "Advanced Flexible Content"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:61
msgid "Show advanced Flexible Content settings"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:72
#: includes/fields/field-repeater.php:37
msgid "Stylised Button"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:75
msgid "Better actions buttons integration"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:100
msgid "Hide Empty Message"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:128
msgid "Empty Message"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:131
msgid "Text displayed when the flexible field is empty"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:133
msgid "Click the \"Add Row\" button below to start creating your layout"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:203
msgid "Add layout"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:204
msgid "Duplicate layout"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:205
msgid "Remove layout"
msgstr ""

#: includes/fields/field-flexible-content-controls.php:206
msgid "Click to toggle"
msgstr ""

#: includes/fields/field-flexible-content-edit.php:78
msgid "Edit Modal"
msgstr ""

#: includes/fields/field-flexible-content-edit.php:81
msgid "Edit layout content in a modal"
msgstr ""

#: includes/fields/field-flexible-content-edit.php:169
msgid "Modal settings"
msgstr ""

#: includes/fields/field-flexible-content-hide.php:63
msgid "Hide Buttons"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:83
msgid "Render the layout using custom template, style & javascript files"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:103
msgid "Dynamic Preview"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:106
msgid ""
"Use layouts render settings to display a dynamic preview in the "
"administration"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:131
msgid "Layouts Placeholder"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:134
msgid "Display a placeholder with an icon"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:182
#: includes/modules/form/module-form-fields.php:1705
msgid "Render"
msgstr ""

#: includes/fields/field-flexible-content-preview.php:328
msgid "Edit layout"
msgstr ""

#: includes/fields/field-flexible-content-select.php:75
msgid "Selection Modal"
msgstr ""

#: includes/fields/field-flexible-content-select.php:78
msgid "Select layouts in a modal"
msgstr ""

#: includes/fields/field-flexible-content-select.php:247
#: includes/modules/block-type/module-block-type.php:60
msgid "Category"
msgstr ""

#: includes/fields/field-flexible-content-select.php:256
msgid "Enter value"
msgstr ""

#: includes/fields/field-flexible-content-settings.php:71
msgid "Layouts Settings Modal"
msgstr ""

#: includes/fields/field-flexible-content-settings.php:74
msgid "Choose a field group to clone and to be used as a configuration modal"
msgstr ""

#: includes/fields/field-flexible-content-settings.php:126
msgid "Clone settings"
msgstr ""

#: includes/fields/field-flexible-content-state.php:55
msgid "Default Layouts State"
msgstr ""

#: includes/fields/field-flexible-content-state.php:58
msgid "Force layouts to be collapsed or opened"
msgstr ""

#: includes/fields/field-flexible-content-state.php:62
msgid "Default (User preference)"
msgstr ""

#: includes/fields/field-flexible-content-thumbnail.php:71
msgid "Layouts Thumbnails"
msgstr ""

#: includes/fields/field-flexible-content-thumbnail.php:74
msgid "Set a thumbnail for each layouts"
msgstr ""

#: includes/fields/field-flexible-content-thumbnail.php:111
#: pro/includes/fields/field-block-editor.php:654
msgid "Thumbnail"
msgstr ""

#: includes/fields/field-flexible-content.php:343
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: includes/fields/field-flexible-content.php:539
msgid "Drag to reorder"
msgstr ""

#: includes/fields/field-forms.php:17 includes/locations/taxonomy-list.php:214
#: includes/modules/form/module-form.php:22
#: includes/modules/form/module-form.php:27
#: includes/modules/form/module-form.php:29
#: includes/modules/form/module-form.php:299
#: pro/includes/locations/settings.php:77
#: pro/includes/modules/template/module-template-features.php:44
msgid "Forms"
msgstr ""

#: includes/fields/field-forms.php:53
msgid "Allow Forms"
msgstr ""

#: includes/fields/field-forms.php:61
msgid "All forms"
msgstr ""

#: includes/fields/field-forms.php:66
#: includes/fields/field-post-statuses.php:66
#: includes/fields/field-post-types.php:66
#: includes/fields/field-taxonomies.php:66
#: includes/fields/field-taxonomy-terms.php:495
#: includes/fields/field-user-roles.php:64
#: pro/includes/fields/field-block-types.php:93
#: pro/includes/fields/field-countries.php:139
#: pro/includes/fields/field-currencies.php:139
#: pro/includes/fields/field-field-groups.php:100
#: pro/includes/fields/field-field-types.php:98
#: pro/includes/fields/field-fields.php:108
#: pro/includes/fields/field-image-sizes.php:105
#: pro/includes/fields/field-languages.php:218
#: pro/includes/fields/field-menu-locations.php:94
#: pro/includes/fields/field-menus.php:94
#: pro/includes/fields/field-options-pages.php:96
#: pro/includes/fields/field-post-formats.php:106
#: pro/includes/fields/field-templates.php:101
msgid "Appearance"
msgstr ""

#: includes/fields/field-forms.php:67
#: includes/fields/field-post-statuses.php:67
#: includes/fields/field-post-types.php:67
#: includes/fields/field-taxonomies.php:67
#: includes/fields/field-taxonomy-terms.php:496
#: includes/fields/field-user-roles.php:65
#: pro/includes/fields/field-block-types.php:94
#: pro/includes/fields/field-countries.php:140
#: pro/includes/fields/field-currencies.php:140
#: pro/includes/fields/field-field-groups.php:101
#: pro/includes/fields/field-field-types.php:99
#: pro/includes/fields/field-fields.php:109
#: pro/includes/fields/field-image-sizes.php:106
#: pro/includes/fields/field-languages.php:219
#: pro/includes/fields/field-menu-locations.php:95
#: pro/includes/fields/field-menus.php:95
#: pro/includes/fields/field-options-pages.php:97
#: pro/includes/fields/field-post-formats.php:107
#: pro/includes/fields/field-templates.php:102
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/field-forms.php:72
#: includes/fields/field-post-statuses.php:72
#: includes/fields/field-post-types.php:72
#: includes/fields/field-taxonomies.php:72
#: includes/fields/field-taxonomy-terms.php:501
#: includes/fields/field-user-roles.php:70
#: pro/includes/fields/field-block-types.php:99
#: pro/includes/fields/field-countries.php:144
#: pro/includes/fields/field-currencies.php:144
#: pro/includes/fields/field-field-groups.php:106
#: pro/includes/fields/field-field-types.php:104
#: pro/includes/fields/field-fields.php:114
#: pro/includes/fields/field-image-sizes.php:111
#: pro/includes/fields/field-languages.php:223
#: pro/includes/fields/field-menu-locations.php:100
#: pro/includes/fields/field-menus.php:100
#: pro/includes/fields/field-options-pages.php:102
#: pro/includes/fields/field-payment-cart.php:117
#: pro/includes/fields/field-post-formats.php:111
#: pro/includes/fields/field-templates.php:107
msgid "Checkbox"
msgstr ""

#: includes/fields/field-forms.php:73
#: includes/fields/field-post-statuses.php:73
#: includes/fields/field-post-types.php:73
#: includes/fields/field-taxonomies.php:73
#: includes/fields/field-taxonomy-terms.php:502
#: includes/fields/field-user-roles.php:71
#: pro/includes/fields/field-block-types.php:100
#: pro/includes/fields/field-countries.php:145
#: pro/includes/fields/field-currencies.php:145
#: pro/includes/fields/field-field-groups.php:107
#: pro/includes/fields/field-field-types.php:105
#: pro/includes/fields/field-fields.php:115
#: pro/includes/fields/field-image-sizes.php:112
#: pro/includes/fields/field-languages.php:224
#: pro/includes/fields/field-menu-locations.php:101
#: pro/includes/fields/field-menus.php:101
#: pro/includes/fields/field-options-pages.php:103
#: pro/includes/fields/field-post-formats.php:112
#: pro/includes/fields/field-templates.php:108
msgid "Radio Buttons"
msgstr ""

#: includes/fields/field-forms.php:74
#: includes/fields/field-post-statuses.php:74
#: includes/fields/field-post-types.php:74
#: includes/fields/field-taxonomies.php:74
#: includes/fields/field-taxonomy-terms.php:503
#: includes/fields/field-user-roles.php:72
#: pro/includes/fields/field-block-types.php:101
#: pro/includes/fields/field-countries.php:146
#: pro/includes/fields/field-currencies.php:146
#: pro/includes/fields/field-field-groups.php:108
#: pro/includes/fields/field-field-types.php:106
#: pro/includes/fields/field-fields.php:116
#: pro/includes/fields/field-image-sizes.php:113
#: pro/includes/fields/field-languages.php:225
#: pro/includes/fields/field-menu-locations.php:102
#: pro/includes/fields/field-menus.php:102
#: pro/includes/fields/field-options-pages.php:104
#: pro/includes/fields/field-post-formats.php:113
#: pro/includes/fields/field-templates.php:109
msgctxt "noun"
msgid "Select"
msgstr ""

#: includes/fields/field-forms.php:81
#: includes/fields/field-post-statuses.php:81
#: includes/fields/field-post-types.php:81
#: includes/fields/field-taxonomies.php:81
#: includes/fields/field-taxonomy-terms.php:510
#: includes/fields/field-user-roles.php:79
#: pro/includes/fields/field-block-types.php:108
#: pro/includes/fields/field-countries.php:153
#: pro/includes/fields/field-currencies.php:153
#: pro/includes/fields/field-field-groups.php:115
#: pro/includes/fields/field-field-types.php:113
#: pro/includes/fields/field-fields.php:123
#: pro/includes/fields/field-image-selector.php:59
#: pro/includes/fields/field-image-sizes.php:120
#: pro/includes/fields/field-languages.php:232
#: pro/includes/fields/field-menu-locations.php:109
#: pro/includes/fields/field-menus.php:109
#: pro/includes/fields/field-options-pages.php:111
#: pro/includes/fields/field-payment-cart.php:91
#: pro/includes/fields/field-post-formats.php:120
#: pro/includes/fields/field-templates.php:116
msgid "Enter each default value on a new line"
msgstr ""

#: includes/fields/field-forms.php:93
msgid "Form ID"
msgstr ""

#: includes/fields/field-forms.php:94
msgid "Form name"
msgstr ""

#: includes/fields/field-forms.php:101
#: includes/fields/field-post-statuses.php:101
#: includes/fields/field-post-types.php:101
#: includes/fields/field-taxonomies.php:101
#: includes/fields/field-taxonomy-terms.php:549
#: includes/fields/field-user-roles.php:86
#: pro/includes/fields/field-block-types.php:128
#: pro/includes/fields/field-countries.php:247
#: pro/includes/fields/field-currencies.php:236
#: pro/includes/fields/field-field-groups.php:136
#: pro/includes/fields/field-field-types.php:133
#: pro/includes/fields/field-fields.php:145
#: pro/includes/fields/field-image-selector.php:127
#: pro/includes/fields/field-image-sizes.php:154
#: pro/includes/fields/field-languages.php:314
#: pro/includes/fields/field-menu-locations.php:130
#: pro/includes/fields/field-menus.php:130
#: pro/includes/fields/field-options-pages.php:131
#: pro/includes/fields/field-payment-cart.php:125
#: pro/includes/fields/field-post-formats.php:127
#: pro/includes/fields/field-templates.php:136
msgid "Allow Null?"
msgstr ""

#: includes/fields/field-forms.php:130
#: includes/fields/field-post-statuses.php:190
#: includes/fields/field-post-types.php:190 includes/fields/field-select.php:58
#: includes/fields/field-taxonomies.php:190
#: includes/fields/field-taxonomy-terms.php:578
#: includes/fields/field-user-roles.php:175
#: pro/includes/fields/field-block-types.php:216
#: pro/includes/fields/field-countries.php:335
#: pro/includes/fields/field-currencies.php:324
#: pro/includes/fields/field-field-groups.php:225
#: pro/includes/fields/field-field-types.php:222
#: pro/includes/fields/field-fields.php:234
#: pro/includes/fields/field-image-sizes.php:243
#: pro/includes/fields/field-languages.php:402
#: pro/includes/fields/field-menu-locations.php:219
#: pro/includes/fields/field-menus.php:219
#: pro/includes/fields/field-options-pages.php:220
#: pro/includes/fields/field-payment-cart.php:190
#: pro/includes/fields/field-post-formats.php:215
#: pro/includes/fields/field-templates.php:225
msgctxt "verb"
msgid "Select"
msgstr ""

#: includes/fields/field-forms.php:193
#: includes/fields/field-post-statuses.php:253
#: includes/fields/field-post-types.php:253
#: includes/fields/field-select.php:106
#: includes/fields/field-taxonomies.php:253
#: includes/fields/field-taxonomy-terms.php:641
#: includes/fields/field-user-roles.php:238
#: pro/includes/fields/field-block-types.php:279
#: pro/includes/fields/field-countries.php:398
#: pro/includes/fields/field-currencies.php:387
#: pro/includes/fields/field-field-groups.php:288
#: pro/includes/fields/field-field-types.php:285
#: pro/includes/fields/field-fields.php:297
#: pro/includes/fields/field-image-sizes.php:306
#: pro/includes/fields/field-languages.php:465
#: pro/includes/fields/field-menu-locations.php:282
#: pro/includes/fields/field-menus.php:282
#: pro/includes/fields/field-options-pages.php:283
#: pro/includes/fields/field-payment-cart.php:253
#: pro/includes/fields/field-post-formats.php:278
#: pro/includes/fields/field-templates.php:288
msgid "Search Input Placeholder"
msgstr ""

#: includes/fields/field-forms.php:194
#: includes/fields/field-post-statuses.php:254
#: includes/fields/field-post-types.php:254
#: includes/fields/field-select.php:107
#: includes/fields/field-taxonomies.php:254
#: includes/fields/field-taxonomy-terms.php:642
#: includes/fields/field-user-roles.php:239
#: pro/includes/fields/field-block-types.php:280
#: pro/includes/fields/field-countries.php:399
#: pro/includes/fields/field-currencies.php:388
#: pro/includes/fields/field-field-groups.php:289
#: pro/includes/fields/field-field-types.php:286
#: pro/includes/fields/field-fields.php:298
#: pro/includes/fields/field-image-sizes.php:307
#: pro/includes/fields/field-languages.php:466
#: pro/includes/fields/field-menu-locations.php:283
#: pro/includes/fields/field-menus.php:283
#: pro/includes/fields/field-options-pages.php:284
#: pro/includes/fields/field-payment-cart.php:254
#: pro/includes/fields/field-post-formats.php:279
#: pro/includes/fields/field-templates.php:289
msgid "Appears within the search input"
msgstr ""

#: includes/fields/field-forms.php:221
#: includes/fields/field-post-statuses.php:126
#: includes/fields/field-post-types.php:126
#: includes/fields/field-taxonomies.php:126
#: includes/fields/field-taxonomy-terms.php:669
#: includes/fields/field-user-roles.php:111
#: pro/includes/fields/field-block-types.php:153
#: pro/includes/fields/field-countries.php:272
#: pro/includes/fields/field-currencies.php:261
#: pro/includes/fields/field-field-groups.php:161
#: pro/includes/fields/field-field-types.php:158
#: pro/includes/fields/field-fields.php:170
#: pro/includes/fields/field-image-selector.php:136
#: pro/includes/fields/field-image-sizes.php:179
#: pro/includes/fields/field-languages.php:339
#: pro/includes/fields/field-menu-locations.php:155
#: pro/includes/fields/field-menus.php:155
#: pro/includes/fields/field-options-pages.php:156
#: pro/includes/fields/field-payment-cart.php:150
#: pro/includes/fields/field-post-formats.php:152
#: pro/includes/fields/field-templates.php:161
msgid "Select multiple values?"
msgstr ""

#: includes/fields/field-forms.php:239
#: includes/fields/field-post-statuses.php:144
#: includes/fields/field-post-types.php:144
#: includes/fields/field-taxonomies.php:144
#: includes/fields/field-taxonomy-terms.php:531
#: includes/fields/field-user-roles.php:129
#: pro/includes/fields/field-block-types.php:171
#: pro/includes/fields/field-countries.php:290
#: pro/includes/fields/field-currencies.php:279
#: pro/includes/fields/field-field-groups.php:179
#: pro/includes/fields/field-field-types.php:176
#: pro/includes/fields/field-fields.php:188
#: pro/includes/fields/field-image-sizes.php:197
#: pro/includes/fields/field-languages.php:357
#: pro/includes/fields/field-menu-locations.php:173
#: pro/includes/fields/field-menus.php:173
#: pro/includes/fields/field-options-pages.php:174
#: pro/includes/fields/field-payment-cart.php:168
#: pro/includes/fields/field-payment-selector.php:122
#: pro/includes/fields/field-post-formats.php:170
#: pro/includes/fields/field-templates.php:179
msgid "Stylised UI"
msgstr ""

#: includes/fields/field-forms.php:258
#: includes/fields/field-post-statuses.php:163
#: includes/fields/field-post-types.php:163
#: includes/fields/field-taxonomies.php:163
#: includes/fields/field-taxonomy-terms.php:687
#: includes/fields/field-user-roles.php:148
#: pro/includes/fields/field-block-types.php:189
#: pro/includes/fields/field-countries.php:308
#: pro/includes/fields/field-currencies.php:297
#: pro/includes/fields/field-field-groups.php:198
#: pro/includes/fields/field-field-types.php:195
#: pro/includes/fields/field-fields.php:207
#: pro/includes/fields/field-image-sizes.php:216
#: pro/includes/fields/field-languages.php:375
#: pro/includes/fields/field-menu-locations.php:192
#: pro/includes/fields/field-menus.php:192
#: pro/includes/fields/field-options-pages.php:193
#: pro/includes/fields/field-post-formats.php:188
#: pro/includes/fields/field-templates.php:198
msgid "Use AJAX to lazy load choices?"
msgstr ""

#: includes/fields/field-forms.php:281
#: includes/fields/field-post-statuses.php:281
#: includes/fields/field-post-types.php:281
#: includes/fields/field-taxonomies.php:281
#: includes/fields/field-user-roles.php:266
#: pro/includes/fields/field-block-types.php:307
#: pro/includes/fields/field-countries.php:426
#: pro/includes/fields/field-currencies.php:415
#: pro/includes/fields/field-field-groups.php:316
#: pro/includes/fields/field-field-types.php:313
#: pro/includes/fields/field-fields.php:325
#: pro/includes/fields/field-image-sizes.php:334
#: pro/includes/fields/field-languages.php:493
#: pro/includes/fields/field-menu-locations.php:310
#: pro/includes/fields/field-menus.php:310
#: pro/includes/fields/field-options-pages.php:311
#: pro/includes/fields/field-post-formats.php:306
#: pro/includes/fields/field-templates.php:316
msgid "Other"
msgstr ""

#: includes/fields/field-forms.php:286
#: includes/fields/field-post-statuses.php:286
#: includes/fields/field-post-types.php:286
#: includes/fields/field-taxonomies.php:286
#: includes/fields/field-user-roles.php:271
#: pro/includes/fields/field-block-types.php:312
#: pro/includes/fields/field-countries.php:431
#: pro/includes/fields/field-currencies.php:420
#: pro/includes/fields/field-field-groups.php:321
#: pro/includes/fields/field-field-types.php:318
#: pro/includes/fields/field-fields.php:330
#: pro/includes/fields/field-image-sizes.php:339
#: pro/includes/fields/field-languages.php:498
#: pro/includes/fields/field-menu-locations.php:315
#: pro/includes/fields/field-menus.php:315
#: pro/includes/fields/field-options-pages.php:316
#: pro/includes/fields/field-post-formats.php:311
#: pro/includes/fields/field-templates.php:321
msgid "Add 'other' choice to allow for custom values"
msgstr ""

#: includes/fields/field-forms.php:301
msgid "Save Other"
msgstr ""

#: includes/fields/field-forms.php:306
msgid "Save 'other' values to the field's choices"
msgstr ""

#: includes/fields/field-forms.php:331
#: includes/fields/field-post-statuses.php:306
#: includes/fields/field-post-types.php:306
#: includes/fields/field-taxonomies.php:306
#: includes/fields/field-taxonomy-terms.php:716
#: includes/fields/field-user-roles.php:291
#: pro/includes/fields/field-block-types.php:332
#: pro/includes/fields/field-countries.php:451
#: pro/includes/fields/field-currencies.php:440
#: pro/includes/fields/field-field-groups.php:341
#: pro/includes/fields/field-field-types.php:338
#: pro/includes/fields/field-fields.php:350
#: pro/includes/fields/field-image-selector.php:152
#: pro/includes/fields/field-image-sizes.php:359
#: pro/includes/fields/field-languages.php:518
#: pro/includes/fields/field-menu-locations.php:335
#: pro/includes/fields/field-menus.php:335
#: pro/includes/fields/field-options-pages.php:336
#: pro/includes/fields/field-payment-cart.php:287
#: pro/includes/fields/field-payment-selector.php:108
#: pro/includes/fields/field-post-formats.php:331
#: pro/includes/fields/field-templates.php:341
msgid "Vertical"
msgstr ""

#: includes/fields/field-forms.php:332
#: includes/fields/field-post-statuses.php:307
#: includes/fields/field-post-types.php:307
#: includes/fields/field-taxonomies.php:307
#: includes/fields/field-taxonomy-terms.php:717
#: includes/fields/field-user-roles.php:292
#: pro/includes/fields/field-block-types.php:333
#: pro/includes/fields/field-countries.php:452
#: pro/includes/fields/field-currencies.php:441
#: pro/includes/fields/field-field-groups.php:342
#: pro/includes/fields/field-field-types.php:339
#: pro/includes/fields/field-fields.php:351
#: pro/includes/fields/field-image-selector.php:151
#: pro/includes/fields/field-image-sizes.php:360
#: pro/includes/fields/field-languages.php:519
#: pro/includes/fields/field-menu-locations.php:336
#: pro/includes/fields/field-menus.php:336
#: pro/includes/fields/field-options-pages.php:337
#: pro/includes/fields/field-payment-cart.php:288
#: pro/includes/fields/field-payment-selector.php:109
#: pro/includes/fields/field-post-formats.php:332
#: pro/includes/fields/field-templates.php:342
msgid "Horizontal"
msgstr ""

#: includes/fields/field-forms.php:354
#: includes/fields/field-post-statuses.php:329
#: includes/fields/field-post-types.php:329
#: includes/fields/field-taxonomies.php:329
#: includes/fields/field-taxonomy-terms.php:739
#: includes/fields/field-user-roles.php:314
#: pro/includes/fields/field-block-types.php:355
#: pro/includes/fields/field-countries.php:474
#: pro/includes/fields/field-currencies.php:463
#: pro/includes/fields/field-field-groups.php:364
#: pro/includes/fields/field-field-types.php:361
#: pro/includes/fields/field-fields.php:373
#: pro/includes/fields/field-image-sizes.php:382
#: pro/includes/fields/field-languages.php:541
#: pro/includes/fields/field-menu-locations.php:358
#: pro/includes/fields/field-menus.php:358
#: pro/includes/fields/field-options-pages.php:359
#: pro/includes/fields/field-payment-cart.php:310
#: pro/includes/fields/field-post-formats.php:354
#: pro/includes/fields/field-templates.php:364
msgid "Toggle"
msgstr ""

#: includes/fields/field-forms.php:355
#: includes/fields/field-post-statuses.php:330
#: includes/fields/field-post-types.php:330
#: includes/fields/field-taxonomies.php:330
#: includes/fields/field-taxonomy-terms.php:740
#: includes/fields/field-user-roles.php:315
#: pro/includes/fields/field-block-types.php:356
#: pro/includes/fields/field-countries.php:475
#: pro/includes/fields/field-currencies.php:464
#: pro/includes/fields/field-field-groups.php:365
#: pro/includes/fields/field-field-types.php:362
#: pro/includes/fields/field-fields.php:374
#: pro/includes/fields/field-image-sizes.php:383
#: pro/includes/fields/field-languages.php:542
#: pro/includes/fields/field-menu-locations.php:359
#: pro/includes/fields/field-menus.php:359
#: pro/includes/fields/field-options-pages.php:360
#: pro/includes/fields/field-payment-cart.php:311
#: pro/includes/fields/field-post-formats.php:355
#: pro/includes/fields/field-templates.php:365
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/field-forms.php:372
#: includes/fields/field-post-statuses.php:347
#: includes/fields/field-post-types.php:347 includes/fields/field-select.php:35
#: includes/fields/field-taxonomies.php:347
#: includes/fields/field-user-roles.php:332
#: pro/includes/fields/field-block-types.php:373
#: pro/includes/fields/field-countries.php:492
#: pro/includes/fields/field-currencies.php:481
#: pro/includes/fields/field-field-groups.php:382
#: pro/includes/fields/field-field-types.php:379
#: pro/includes/fields/field-fields.php:391
#: pro/includes/fields/field-image-sizes.php:400
#: pro/includes/fields/field-languages.php:559
#: pro/includes/fields/field-menu-locations.php:376
#: pro/includes/fields/field-menus.php:376
#: pro/includes/fields/field-options-pages.php:377
#: pro/includes/fields/field-post-formats.php:372
#: pro/includes/fields/field-templates.php:382
msgid "Allow Custom"
msgstr ""

#: includes/fields/field-forms.php:377
#: includes/fields/field-post-statuses.php:352
#: includes/fields/field-post-types.php:352 includes/fields/field-select.php:40
#: includes/fields/field-taxonomies.php:352
#: includes/fields/field-user-roles.php:337
#: pro/includes/fields/field-block-types.php:378
#: pro/includes/fields/field-countries.php:497
#: pro/includes/fields/field-currencies.php:486
#: pro/includes/fields/field-field-groups.php:387
#: pro/includes/fields/field-field-types.php:384
#: pro/includes/fields/field-fields.php:396
#: pro/includes/fields/field-image-sizes.php:405
#: pro/includes/fields/field-languages.php:564
#: pro/includes/fields/field-menu-locations.php:381
#: pro/includes/fields/field-menus.php:381
#: pro/includes/fields/field-options-pages.php:382
#: pro/includes/fields/field-post-formats.php:377
#: pro/includes/fields/field-templates.php:387
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/field-forms.php:392
msgid "Save Custom"
msgstr ""

#: includes/fields/field-forms.php:397
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/field-hidden.php:17
#: includes/modules/form/module-form-fields.php:1562
msgid "Hidden"
msgstr ""

#: includes/fields/field-hidden.php:67
msgid "Default value in the hidden input"
msgstr ""

#: includes/fields/field-image.php:129
msgid "Featured Thumbnail"
msgstr ""

#: includes/fields/field-image.php:132
msgid "Make this image the featured thumbnail"
msgstr ""

#: includes/fields/field-post-object.php:38
msgid "Allow & Save Custom value"
msgstr ""

#: includes/fields/field-post-object.php:43
msgid "Save 'custom' values as new post"
msgstr ""

#: includes/fields/field-post-object.php:48
msgid "New Post Arguments"
msgstr ""

#: includes/fields/field-post-object.php:49
msgid ""
"See available hooks in the <a href=\"https://www.acf-extended.com/features/"
"fields/post-object#custom-value-hooks\" target=\"_blank\">documentation</a>."
msgstr ""

#: includes/fields/field-post-statuses.php:17
msgid "Post Statuses"
msgstr ""

#: includes/fields/field-post-statuses.php:53
msgid "Allow Post Status"
msgstr ""

#: includes/fields/field-post-statuses.php:61
msgid "All post statuses"
msgstr ""

#: includes/fields/field-post-statuses.php:93
msgid "Post status object"
msgstr ""

#: includes/fields/field-post-statuses.php:94
msgid "Post status name"
msgstr ""

#: includes/fields/field-post-types.php:17
#: includes/modules/post-type/module-post-type.php:25
#: includes/modules/post-type/module-post-type.php:28
#: includes/modules/post-type/module-post-type.php:30
#: includes/modules/taxonomy/module-taxonomy.php:61
msgid "Post Types"
msgstr ""

#: includes/fields/field-post-types.php:53
msgid "Allow Post Type"
msgstr ""

#: includes/fields/field-post-types.php:93
msgid "Post type object"
msgstr ""

#: includes/fields/field-post-types.php:94
msgid "Post type name"
msgstr ""

#: includes/fields/field-recaptcha.php:21
msgid "Google reCaptcha"
msgstr ""

#: includes/fields/field-recaptcha.php:49
msgid "Version"
msgstr ""

#: includes/fields/field-recaptcha.php:50
msgid "Select the reCaptcha version"
msgstr ""

#: includes/fields/field-recaptcha.php:54
msgid "reCaptcha V2"
msgstr ""

#: includes/fields/field-recaptcha.php:55
msgid "reCaptcha V3"
msgstr ""

#: includes/fields/field-recaptcha.php:61
msgid "Theme"
msgstr ""

#: includes/fields/field-recaptcha.php:62
msgid "Select the reCaptcha theme"
msgstr ""

#: includes/fields/field-recaptcha.php:66
msgid "Light"
msgstr ""

#: includes/fields/field-recaptcha.php:67
msgid "Dark"
msgstr ""

#: includes/fields/field-recaptcha.php:82
#: pro/includes/fields/field-image-sizes.php:134
msgid "Size"
msgstr ""

#: includes/fields/field-recaptcha.php:83
msgid "Select the reCaptcha size"
msgstr ""

#: includes/fields/field-recaptcha.php:87
msgid "Normal"
msgstr ""

#: includes/fields/field-recaptcha.php:88
msgid "Compact"
msgstr ""

#: includes/fields/field-recaptcha.php:103
msgid "Hide logo"
msgstr ""

#: includes/fields/field-recaptcha.php:104
msgid "Hide the reCaptcha logo"
msgstr ""

#: includes/fields/field-recaptcha.php:121
msgid "Site key"
msgstr ""

#: includes/fields/field-recaptcha.php:122
msgid ""
"Enter the site key. <a href=\"https://www.google.com/recaptcha/admin\" "
"target=\"_blank\">reCaptcha API Admin</a>"
msgstr ""

#: includes/fields/field-recaptcha.php:129
#: pro/includes/fields/field-payment.php:261
#: pro/includes/fields/field-payment.php:281
msgid "Secret key"
msgstr ""

#: includes/fields/field-recaptcha.php:130
msgid ""
"Enter the secret key. <a href=\"https://www.google.com/recaptcha/admin\" "
"target=\"_blank\">reCaptcha API Admin</a>"
msgstr ""

#: includes/fields/field-recaptcha.php:256
#: includes/fields/field-recaptcha.php:265
#: pro/includes/fields/field-payment.php:55
#: pro/includes/fields/field-payment.php:56
#: pro/includes/fields/field-payment.php:57
#: pro/includes/fields/field-payment.php:59
msgid "An error has occured"
msgstr ""

#: includes/fields/field-recaptcha.php:270
msgid "Invalid reCaptcha, please try again"
msgstr ""

#: includes/fields/field-repeater.php:40
msgid "Better row button integration"
msgstr ""

#: includes/fields/field-slug.php:17
#: includes/modules/form/module-form-action-term.php:783
#: includes/modules/form/module-form-action-term.php:1172
#: pro/includes/fields/field-color-picker.php:68
#: pro/includes/fields/field-color-picker.php:165
msgid "Slug"
msgstr ""

#: includes/fields/field-slug.php:47 pro/includes/fields/field-file.php:190
msgid "Placeholder Text"
msgstr ""

#: includes/fields/field-slug.php:55 pro/includes/fields/field-address.php:146
#: pro/includes/fields/field-date-range-picker.php:227
#: pro/includes/fields/field-select.php:34
msgid "Prepend"
msgstr ""

#: includes/fields/field-slug.php:56 pro/includes/fields/field-address.php:147
#: pro/includes/fields/field-date-range-picker.php:228
#: pro/includes/fields/field-select.php:35
msgid "Appears before the input"
msgstr ""

#: includes/fields/field-slug.php:63
#: includes/modules/form/module-form-action-post.php:1643
#: pro/includes/fields/field-address.php:155
#: pro/includes/fields/field-date-range-picker.php:236
#: pro/includes/fields/field-select.php:43
msgid "Append"
msgstr ""

#: includes/fields/field-slug.php:64 pro/includes/fields/field-address.php:156
#: pro/includes/fields/field-date-range-picker.php:237
#: pro/includes/fields/field-select.php:44
msgid "Appears after the input"
msgstr ""

#: includes/fields/field-slug.php:113
#, php-format
msgid "Value must not exceed %d characters"
msgstr ""

#: includes/fields/field-taxonomies.php:17
#: includes/modules/post-type/module-post-type.php:61
#: includes/modules/taxonomy/module-taxonomy.php:25
#: includes/modules/taxonomy/module-taxonomy.php:28
#: includes/modules/taxonomy/module-taxonomy.php:30
msgid "Taxonomies"
msgstr ""

#: includes/fields/field-taxonomies.php:53
#: includes/fields/field-taxonomy-terms.php:368
msgid "Allow Taxonomy"
msgstr ""

#: includes/fields/field-taxonomies.php:93
msgid "Taxonomy object"
msgstr ""

#: includes/fields/field-taxonomies.php:94
msgid "Taxonomy name"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:20
msgid "Taxonomy Terms"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:467
msgid "Allow Terms"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:476
msgid "All terms"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:482
msgid "Terms_level"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:488
#: includes/locations/post-type-all.php:31
#: includes/locations/post-type-archive.php:320
#: includes/locations/post-type-list.php:237
#: includes/locations/taxonomy-list.php:223
#: pro/includes/locations/post-author-role.php:24
#: pro/includes/locations/settings.php:87
#: pro/includes/modules/script/module-script-table.php:148
msgid "All"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:522
msgid "Term object"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:523
msgid "Term name"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:524
msgid "Term ID"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:757
msgid "Save Terms"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:758
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:766
msgid "Load Terms"
msgstr ""

#: includes/fields/field-taxonomy-terms.php:767
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/field-textarea.php:62
msgid "Code mode"
msgstr ""

#: includes/fields/field-textarea.php:65
msgid ""
"Switch font family to monospace and allow tab indent. For a more advanced "
"code editor, please use the <code>Code Editor</code> field type"
msgstr ""

#: includes/fields/field-user-roles.php:17
msgid "User Roles"
msgstr ""

#: includes/fields/field-user-roles.php:51
msgid "Allow User Role"
msgstr ""

#: includes/fields/field-user-roles.php:59
msgid "All user roles"
msgstr ""

#: includes/init.php:179
msgid ""
"ACF Extended requires <a href=\"https://www.advancedcustomfields.com/pro/\" "
"target=\"_blank\">Advanced Custom Fields PRO</a> (minimum: 5.8)."
msgstr ""

#: includes/locations/post-type-archive.php:44
#, php-format
msgid "Archive <span class=\"count\">(%s)</span>"
msgstr ""

#: includes/locations/post-type-archive.php:139
msgid "Permalink:"
msgstr ""

#: includes/locations/post-type-archive.php:236
msgid "Archive"
msgstr ""

#: includes/locations/post-type-archive.php:292
msgid "Post Type Archive"
msgstr ""

#: includes/locations/post-type-list.php:215
msgid "Post Type List"
msgstr ""

#: includes/locations/taxonomy-list.php:215
msgid "Taxonomy List"
msgstr ""

#: includes/module-acf.php:91 includes/module-post.php:130
#: includes/modules/form/module-form.php:721
#: pro/includes/fields-settings/required.php:26
#: pro/includes/fields/field-block-editor.php:572
#, php-format
msgid "%s value is required"
msgstr ""

#: includes/module-legacy.php:100 includes/module-posts.php:41
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/module-legacy.php:192
msgid "Export "
msgstr ""

#: includes/module-legacy.php:256 includes/module-post.php:129
#: includes/modules/performance/module-performance-functions.php:660
#: pro/includes/module-item.php:75
msgid "Active"
msgstr ""

#: includes/module-legacy.php:256 includes/module-post.php:129
msgid "Inactive"
msgstr ""

#: includes/module-post.php:151 includes/module-posts.php:233
msgid "Export"
msgstr ""

#: includes/module-post.php:254 includes/module-post.php:255
#: includes/module-post.php:257
#, php-format
msgid "%s updated."
msgstr ""

#: includes/module-post.php:256
#, php-format
msgid "%s deleted."
msgstr ""

#: includes/module-post.php:259
#, php-format
msgid "%s published."
msgstr ""

#: includes/module-post.php:260
#, php-format
msgid "%s saved."
msgstr ""

#: includes/module-post.php:261
#, php-format
msgid "%s submitted."
msgstr ""

#: includes/module-post.php:262
#, php-format
msgid "%s scheduled."
msgstr ""

#: includes/module-post.php:263
#, php-format
msgid "%s draft updated."
msgstr ""

#: includes/module-posts.php:204 pro/includes/fields/field-payment.php:511
#: pro/includes/modules/dev/module-dev-clean-meta.php:112
#: pro/includes/modules/dev/module-dev-metabox.php:87
#: pro/includes/modules/dev/module-dev-metabox.php:179
#: pro/includes/modules/dev/module-dev-metabox.php:248
#: pro/includes/modules/dev/module-dev-metabox.php:352
#: pro/includes/modules/dev/module-dev-metabox.php:410
#: pro/includes/modules/dev/module-dev-metabox.php:468
#: pro/includes/modules/dev/module-dev-metabox.php:524
#: pro/includes/modules/script/module-script-table.php:195
msgid "View"
msgstr ""

#: includes/module.php:424
msgid "copy"
msgstr ""

#: includes/module.php:1068
msgid "Reset"
msgstr ""

#: includes/modules/author.php:75 pro/includes/fields/field-post-field.php:46
msgid "Author"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:33
#: includes/modules/block-type/module-block-type.php:28
#: pro/includes/fields/field-block-editor.php:297
msgid "Block Type"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:77
msgid ""
"A unique name that identifies the block (without namespace).<br />Note: A "
"block name can only contain lowercase alphanumeric characters and dashes, "
"and must begin with a letter."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:96
msgid "This is a short description for your block."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:115
msgid ""
"Blocks are grouped into categories to help users browse and discover them. "
"The core provided categories are [ common | formatting | layout | widgets | "
"embed ]. Plugins and Themes can also register custom block categories.<br /"
"><br />Plugins and Themes can also register <a href=\"https://developer."
"wordpress.org/block-editor/reference-guides/filters/block-filters/#managing-"
"block-categories\" target=\"_blank\">custom block categories</a>."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:134
msgid ""
"An array of search terms to help user discover the block while searching."
"<br />One line for each keyword. ie:<br /><br />quote<br />mention<br />cite"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:154
msgid "An array of post types to restrict this block type to."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:170
msgid ""
"The display mode for your block. Available settings are \"auto\", "
"\"preview\" and \"edit\". Defaults to \"preview\".<br /><br /"
"><strong>Preview</strong>: Preview is always shown. Edit form appears in "
"sidebar when block is selected.<br /><strong>Auto</strong>: Preview is shown "
"by default but changes to edit form when block is selected.<br /"
"><strong>Edit</strong>: Edit form is always shown.<br /><br />Note. When in "
"\"preview\" or \"edit\" modes, an icon will appear in the block toolbar to "
"toggle between modes."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:196
msgid ""
"The default block alignment. Available settings are \"left\", \"center\", "
"\"right\", \"wide\" and \"full\". Defaults to an empty string."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:225
msgid ""
"The default block text alignment (see supports setting for more info). "
"Available settings are \"left\", \"center\" and \"right\". Defaults to the "
"current language's text alignment."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:252
msgid ""
"The default block content alignment (see supports setting for more info). "
"Available settings are \"top\", \"center\" and \"bottom\".<br /><br />When "
"utilising the \"Matrix\" control type, additional settings are available to "
"specify all 9 positions from “top left” to “bottom right”. Defaults to "
"\"top\"."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:289
msgid "Enable Anchor attribute. Defaults to false"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:310
msgid ""
"Parse the block HTML as JSX for the Inner Block Component to function within "
"the React based block editor."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:331
msgid ""
"This property adds block controls which allow the user to change the block’s "
"alignment. Defaults to true. Set to false to hide the alignment toolbar. Set "
"to an array of specific alignment names to customize the toolbar."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:351
msgid ""
"Set to an array of specific alignment names to customize the toolbar.<br /"
">One line for each name. ie:<br /><br />left<br />right<br />full"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:381
msgid ""
"This property enables a toolbar button to control the block’s text "
"alignment. Defaults to false. Set to true to show the alignment toolbar "
"button."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:402
msgid ""
"This property enables a toolbar button to control the block’s inner content "
"alignment. Defaults to false. Set to true to show the alignment toolbar "
"button, or set to \"matrix\" to enable the full alignment matrix in the "
"toolbar"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:426
msgid "This property enables the full height button on the toolbar of a block"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:447
msgid ""
"This property allows the user to toggle between edit and preview modes via a "
"button. Defaults to true."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:467
msgid ""
"This property allows the block to be added multiple times. Defaults to true."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:504
msgid ""
"Simple: Specify a Dashicons class or SVG path<br />Colors: Specify colors & "
"Dashicons class"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:530
msgid ""
"An icon property can be specified to make it easier to identify a block. "
"These can be any of WordPress’ Dashicons, or a custom svg element."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:558
msgid ""
"Specifying a background color to appear with the icon e.g.: in the inserter."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:582
msgid ""
"Specifying a color for the icon (optional: if not set, a readable color will "
"be automatically defined)"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:606
msgid "Specifying a dashicon for the block"
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:651
msgid ""
"The path to a template file used to render the block HTML. This can either "
"be a relative path to a file within the active theme, parent theme, wp-"
"content directory or a full path to any file."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:670
msgid ""
"Instead of providing a render_template, a callback function name may be "
"specified to output the block's HTML."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:706
msgid ""
"The url to a .css file to be enqueued whenever your block is displayed "
"(front-end and back-end). This can either be a relative path to a file "
"within the active theme, parent theme, wp-content directory or a full path "
"to any file."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:725
msgid ""
"The url to a .js file to be enqueued whenever your block is displayed (front-"
"end and back-end). This can either be a relative path to a file within the "
"active theme, parent theme, wp-content directory or a full path to any file."
msgstr ""

#: includes/modules/block-type/module-block-type-fields.php:744
msgid ""
"A callback function that runs whenever your block is displayed (front-end "
"and back-end) and enqueues scripts and/or styles."
msgstr ""

#: includes/modules/block-type/module-block-type.php:24
#: includes/modules/block-type/module-block-type.php:27
#: includes/modules/block-type/module-block-type.php:29
#: pro/includes/fields/field-block-types.php:17
msgid "Block Types"
msgstr ""

#: includes/modules/block-type/module-block-type.php:30
msgid "Edit Block Type"
msgstr ""

#: includes/modules/block-type/module-block-type.php:31
msgid "New Block Type"
msgstr ""

#: includes/modules/block-type/module-block-type.php:32
msgid "Block Type Title"
msgstr ""

#: includes/modules/block-type/module-block-type.php:37
#: includes/modules/block-type/module-block-type.php:38
msgid "Export Block Types"
msgstr ""

#: includes/modules/block-type/module-block-type.php:39
msgid "Select Block Types"
msgstr ""

#: includes/modules/block-type/module-block-type.php:40
msgid "No block type available."
msgstr ""

#: includes/modules/block-type/module-block-type.php:41
msgid "No block types selected"
msgstr ""

#: includes/modules/block-type/module-block-type.php:42
msgid "1 block type exported"
msgstr ""

#: includes/modules/block-type/module-block-type.php:43
#, php-format
msgid "%s block types exported"
msgstr ""

#: includes/modules/block-type/module-block-type.php:44
#: includes/modules/options-page/module-options-page.php:46
#, php-format
msgid ""
"It is recommended to include this code within the <code>acf/init</code> hook "
"(<a href=\"%s\" target=\"blank\">see documentation</a>)."
msgstr ""

#: includes/modules/block-type/module-block-type.php:45
#: includes/modules/block-type/module-block-type.php:46
msgid "Import Block Types"
msgstr ""

#: includes/modules/block-type/module-block-type.php:47
msgid "1 block type imported"
msgstr ""

#: includes/modules/block-type/module-block-type.php:48
#, php-format
msgid "%s block types imported"
msgstr ""

#: includes/modules/block-type/module-block-type.php:61
#: includes/modules/post-type/module-post-type.php:62
msgid "Posts"
msgstr ""

#: includes/modules/block-type/module-block-type.php:236
#: includes/modules/block-type/module-block-type.php:241
msgid "This block type already exists"
msgstr ""

#: includes/modules/dev/module-dev-delete-meta.php:60
#: includes/modules/dev/module-dev-delete-meta.php:93
#: includes/modules/option/module-option-table.php:209
#: includes/modules/option/module-option-table.php:261
#: includes/modules/option/module-option.php:533
msgid "Delete"
msgstr ""

#: includes/modules/dev/module-dev-delete-meta.php:87
msgid "Select bulk action"
msgstr ""

#: includes/modules/dev/module-dev-delete-meta.php:92
msgid "Bulk Actions"
msgstr ""

#: includes/modules/dev/module-dev-delete-meta.php:96
msgid "Apply"
msgstr ""

#: includes/modules/dev/module-dev.php:320
msgid "WP Options Meta"
msgstr ""

#: includes/modules/dev/module-dev.php:320
msgid "WP Custom Fields"
msgstr ""

#: includes/modules/dev/module-dev.php:343
msgid "ACF Options Meta"
msgstr ""

#: includes/modules/dev/module-dev.php:343
msgid "ACF Custom Fields"
msgstr ""

#: includes/modules/dev/module-dev.php:445
#: pro/includes/fields/field-payment-cart.php:112
#: pro/includes/fields/field-payment-cart.php:113
#: pro/includes/fields/field-payment-selector.php:90
#: pro/includes/fields/field-payment-selector.php:91
#: pro/includes/fields/field-post-field.php:40
msgid "Field Type"
msgstr ""

#: includes/modules/dev/module-dev.php:446
msgid "Field Group"
msgstr ""

#: includes/modules/dev/module-dev.php:450
#: includes/modules/option/module-option-table.php:228
#: includes/modules/option/module-option.php:484
#: includes/modules/options-page/module-options-page.php:63
#: pro/includes/modules/dev/module-dev-edit-meta.php:144
msgid "Autoload"
msgstr ""

#: includes/modules/dev/module-dev.php:555
msgid "empty"
msgstr ""

#: includes/modules/dev/module-dev.php:654
#: includes/modules/dev/module-dev.php:655
msgid "Undefined"
msgstr ""

#: includes/modules/form/module-form-action-custom.php:17
msgid "Custom action"
msgstr ""

#: includes/modules/form/module-form-action-custom.php:103
msgid "This action name is reserved"
msgstr ""

#: includes/modules/form/module-form-action-custom.php:139
#: includes/modules/form/module-form-action-email.php:532
#: includes/modules/form/module-form-action-post.php:886
#: includes/modules/form/module-form-action-redirect.php:100
#: includes/modules/form/module-form-action-term.php:582
#: includes/modules/form/module-form-action-user.php:1186
#: includes/modules/form/module-form-upgrades.php:1237
#: includes/modules/form/module-form-upgrades.php:1312
#: includes/modules/form/module-form-upgrades.php:1678
#: includes/modules/form/module-form-upgrades.php:2891
#: includes/modules/form/module-form-upgrades.php:2988
#: includes/modules/form/module-form-upgrades.php:3777
#: includes/modules/form/module-form-upgrades.php:5356
#: includes/modules/form/module-form.php:293
#: pro/includes/modules/form/module-form-action-option.php:249
msgid "Documentation"
msgstr ""

#: includes/modules/form/module-form-action-custom.php:148
#: includes/modules/form/module-form-action-email.php:541
#: includes/modules/form/module-form-action-post.php:895
#: includes/modules/form/module-form-action-post.php:912
#: includes/modules/form/module-form-action-redirect.php:109
#: includes/modules/form/module-form-action-term.php:591
#: includes/modules/form/module-form-action-term.php:608
#: includes/modules/form/module-form-action-user.php:1195
#: includes/modules/form/module-form-action-user.php:1212
#: pro/includes/modules/form/module-form-action-option.php:258
msgid "Action"
msgstr ""

#: includes/modules/form/module-form-action-custom.php:165
#: includes/modules/form/module-form-action-email.php:558
#: includes/modules/form/module-form-action-post.php:931
#: includes/modules/form/module-form-action-redirect.php:126
#: includes/modules/form/module-form-action-term.php:627
#: includes/modules/form/module-form-action-user.php:1254
#: pro/includes/modules/form/module-form-action-option.php:275
msgid "Action name"
msgstr ""

#: includes/modules/form/module-form-action-custom.php:168
msgid "Target this action using hooks."
msgstr ""

#: includes/modules/form/module-form-action-custom.php:178
#: includes/modules/form/module-form-fields.php:460
#: includes/modules/form/module-form-fields.php:551
#: includes/modules/form/module-form-fields.php:769
#: includes/modules/form/module-form-fields.php:942
#: pro/includes/locations/menu-item-type.php:22
msgid "Custom"
msgstr ""

#: includes/modules/form/module-form-action-email.php:17
msgid "Email action"
msgstr ""

#: includes/modules/form/module-form-action-email.php:561
#: includes/modules/form/module-form-action-post.php:934
#: includes/modules/form/module-form-action-redirect.php:129
#: includes/modules/form/module-form-action-term.php:630
#: includes/modules/form/module-form-action-user.php:1257
#: includes/modules/form/module-form-upgrades.php:1342
#: includes/modules/form/module-form-upgrades.php:2921
#: pro/includes/modules/form/module-form-action-option.php:278
msgid "(Optional) Target this action using hooks."
msgstr ""

#: includes/modules/form/module-form-action-email.php:571
#: includes/modules/form/module-form-action-email.php:582
#: includes/modules/form/module-form-action-user.php:1514
#: includes/modules/form/module-form-action-user.php:2000
msgid "Email"
msgstr ""

#: includes/modules/form/module-form-action-email.php:598
msgid "From"
msgstr ""

#: includes/modules/form/module-form-action-email.php:610
#: includes/modules/form/module-form-action-email.php:647
msgid "Name <<EMAIL>>"
msgstr ""

#: includes/modules/form/module-form-action-email.php:617
msgid "To"
msgstr ""

#: includes/modules/form/module-form-action-email.php:629
#: includes/modules/form/module-form-action-email.php:666
#: includes/modules/form/module-form-action-email.php:685
msgid "<EMAIL>"
msgstr ""

#: includes/modules/form/module-form-action-email.php:635
msgid "Reply to"
msgstr ""

#: includes/modules/form/module-form-action-email.php:654
msgid "Cc"
msgstr ""

#: includes/modules/form/module-form-action-email.php:673
msgid "Bcc"
msgstr ""

#: includes/modules/form/module-form-action-email.php:692
msgid "Subject"
msgstr ""

#: includes/modules/form/module-form-action-email.php:712
#: pro/includes/fields/field-post-field.php:48
msgid "Content"
msgstr ""

#: includes/modules/form/module-form-action-email.php:715
msgid "Render customized email content."
msgstr ""

#: includes/modules/form/module-form-action-email.php:716
msgid "Render all fields values:"
msgstr ""

#: includes/modules/form/module-form-action-email.php:717
msgid "Render field value:"
msgstr ""

#: includes/modules/form/module-form-action-email.php:743
#: includes/modules/form/module-form-action-post.php:1197
#: includes/modules/form/module-form-action-post.php:1270
#: includes/modules/form/module-form-action-term.php:947
#: includes/modules/form/module-form-action-user.php:1746
msgid "Content Editor"
msgstr ""

#: includes/modules/form/module-form-action-email.php:744
msgid "Raw HTML"
msgstr ""

#: includes/modules/form/module-form-action-email.php:814
msgid "Attachments"
msgstr ""

#: includes/modules/form/module-form-action-email.php:830
msgid "Dynamic files"
msgstr ""

#: includes/modules/form/module-form-action-email.php:845
#: includes/modules/form/module-form-action-email.php:909
msgid "Add file"
msgstr ""

#: includes/modules/form/module-form-action-email.php:849
#: includes/modules/form/module-form-action-email.php:913
msgid "File"
msgstr ""

#: includes/modules/form/module-form-action-email.php:868
#: includes/modules/form/module-form-action-post.php:1020
#: includes/modules/form/module-form-action-post.php:1077
#: includes/modules/form/module-form-action-post.php:1106
#: includes/modules/form/module-form-action-post.php:1134
#: includes/modules/form/module-form-action-post.php:1161
#: includes/modules/form/module-form-action-post.php:1206
#: includes/modules/form/module-form-action-post.php:1279
#: includes/modules/form/module-form-action-post.php:1351
#: includes/modules/form/module-form-action-post.php:1422
#: includes/modules/form/module-form-action-post.php:1494
#: includes/modules/form/module-form-action-post.php:1568
#: includes/modules/form/module-form-action-post.php:1624
#: includes/modules/form/module-form-action-post.php:1756
#: includes/modules/form/module-form-action-term.php:718
#: includes/modules/form/module-form-action-term.php:776
#: includes/modules/form/module-form-action-term.php:804
#: includes/modules/form/module-form-action-term.php:834
#: includes/modules/form/module-form-action-term.php:882
#: includes/modules/form/module-form-action-term.php:956
#: includes/modules/form/module-form-action-term.php:1102
#: includes/modules/form/module-form-action-user.php:1346
#: includes/modules/form/module-form-action-user.php:1371
#: includes/modules/form/module-form-action-user.php:1396
#: includes/modules/form/module-form-action-user.php:1481
#: includes/modules/form/module-form-action-user.php:1532
#: includes/modules/form/module-form-action-user.php:1557
#: includes/modules/form/module-form-action-user.php:1585
#: includes/modules/form/module-form-action-user.php:1610
#: includes/modules/form/module-form-action-user.php:1635
#: includes/modules/form/module-form-action-user.php:1660
#: includes/modules/form/module-form-action-user.php:1685
#: includes/modules/form/module-form-action-user.php:1710
#: includes/modules/form/module-form-action-user.php:1756
#: includes/modules/form/module-form-action-user.php:1812
#: includes/modules/form/module-form-action-user.php:1967
#: pro/includes/modules/form/module-form-action-option.php:335
#: pro/includes/modules/form/module-form-action-option.php:431
msgid "Select a field or enter a custom value/template tag."
msgstr ""

#: includes/modules/form/module-form-action-email.php:874
msgid "Delete file"
msgstr ""

#: includes/modules/form/module-form-action-email.php:884
msgid "Delete file once sent"
msgstr ""

#: includes/modules/form/module-form-action-email.php:894
msgid "Static files"
msgstr ""

#: includes/modules/form/module-form-action-post.php:17
msgid "Post action"
msgstr ""

#: includes/modules/form/module-form-action-post.php:924
msgid "Create post"
msgstr ""

#: includes/modules/form/module-form-action-post.php:925
msgid "Update post"
msgstr ""

#: includes/modules/form/module-form-action-post.php:955
#: includes/modules/form/module-form-action-term.php:652
#: includes/modules/form/module-form-action-user.php:1407
#: pro/includes/modules/form/module-form-action-option.php:299
msgid "Save"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1009
#: includes/modules/form/module-form-action-post.php:1557
#: includes/modules/form/module-form-action-post.php:1745
msgid "Current Post"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1010
#: includes/modules/form/module-form-action-post.php:1558
#: includes/modules/form/module-form-action-post.php:1746
msgid "Current Post Parent"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1011
#: includes/modules/form/module-form-action-post.php:1559
#: includes/modules/form/module-form-action-post.php:1747
msgid "Post Selector"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1054
#: includes/modules/form/module-form-action-post.php:1788
msgid "Post type"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1083
#: includes/modules/form/module-form-action-post.php:1822
msgid "Post status"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1113
#: includes/modules/form/module-form-action-post.php:1856
msgid "Post title"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1124
#: includes/modules/form/module-form-action-post.php:1152
#: includes/modules/form/module-form-action-term.php:765
#: includes/modules/form/module-form-action-term.php:794
msgid "Generated ID"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1125
#: includes/modules/form/module-form-action-term.php:766
msgid "#Generated ID"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1141
#: includes/modules/form/module-form-action-post.php:1890
msgid "Post name"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1168
#: includes/modules/form/module-form-action-post.php:1924
msgid "Post content"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1241
#: includes/modules/form/module-form-action-post.php:1958
msgid "Post excerpt"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1311
#: includes/modules/form/module-form-action-post.php:1992
msgid "Post author"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1340
#: includes/modules/form/module-form-action-user.php:1470
#: includes/modules/form/module-form-action-user.php:1956
msgid "Current User"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1341
#: includes/modules/form/module-form-action-user.php:1471
#: includes/modules/form/module-form-action-user.php:1957
msgid "Current Post Author"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1342
#: includes/modules/form/module-form-action-user.php:1472
#: includes/modules/form/module-form-action-user.php:1958
msgid "User Selector"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1384
#: includes/modules/form/module-form-action-post.php:2026
msgid "Post date"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1413
msgid "Date picker"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1456
#: includes/modules/form/module-form-action-post.php:2060
msgid "Post thumbnail"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1485
#: pro/includes/fields/field-image-selector.php:17
msgid "Image Selector"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1528
#: includes/modules/form/module-form-action-post.php:2094
msgid "Post parent"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1601
#: includes/modules/form/module-form-action-post.php:2128
msgid "Post terms"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1630
msgid "Append terms"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1647
#: includes/modules/form/module-form-action-term.php:992
#: includes/modules/form/module-form-action-user.php:1847
#: pro/includes/modules/form/module-form-action-option.php:341
msgid "Save ACF fields"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1650
#: includes/modules/form/module-form-action-term.php:995
#: includes/modules/form/module-form-action-user.php:1850
#: includes/modules/form/module-form-fields.php:263
#: pro/includes/modules/form/module-form-action-option.php:344
msgid "Which ACF fields should be saved as metadata"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1688
#: includes/modules/form/module-form-action-term.php:1033
#: includes/modules/form/module-form-action-user.php:1897
#: pro/includes/modules/form/module-form-action-option.php:384
msgid "Load Values"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1691
#: includes/modules/form/module-form-action-term.php:1036
#: includes/modules/form/module-form-action-user.php:1900
#: pro/includes/modules/form/module-form-action-option.php:387
msgid "Fill inputs with values"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1805
#: includes/modules/form/module-form-action-post.php:1839
#: includes/modules/form/module-form-action-post.php:1873
#: includes/modules/form/module-form-action-post.php:1907
#: includes/modules/form/module-form-action-post.php:1941
#: includes/modules/form/module-form-action-post.php:1975
#: includes/modules/form/module-form-action-post.php:2009
#: includes/modules/form/module-form-action-post.php:2043
#: includes/modules/form/module-form-action-post.php:2077
#: includes/modules/form/module-form-action-post.php:2111
#: includes/modules/form/module-form-action-post.php:2145
#: includes/modules/form/module-form-action-term.php:1155
#: includes/modules/form/module-form-action-term.php:1189
#: includes/modules/form/module-form-action-term.php:1223
#: includes/modules/form/module-form-action-term.php:1257
#: includes/modules/form/module-form-action-term.php:1291
#: includes/modules/form/module-form-action-user.php:2017
#: includes/modules/form/module-form-action-user.php:2051
#: includes/modules/form/module-form-action-user.php:2085
#: includes/modules/form/module-form-action-user.php:2119
#: includes/modules/form/module-form-action-user.php:2153
#: includes/modules/form/module-form-action-user.php:2187
#: includes/modules/form/module-form-action-user.php:2221
#: includes/modules/form/module-form-action-user.php:2255
#: includes/modules/form/module-form-action-user.php:2289
#: includes/modules/form/module-form-action-user.php:2323
#: pro/includes/fields/field-address.php:117
msgid "None"
msgstr ""

#: includes/modules/form/module-form-action-post.php:1807
#: includes/modules/form/module-form-action-post.php:1841
#: includes/modules/form/module-form-action-post.php:1875
#: includes/modules/form/module-form-action-post.php:1909
#: includes/modules/form/module-form-action-post.php:1943
#: includes/modules/form/module-form-action-post.php:1977
#: includes/modules/form/module-form-action-post.php:2011
#: includes/modules/form/module-form-action-post.php:2045
#: includes/modules/form/module-form-action-post.php:2079
#: includes/modules/form/module-form-action-post.php:2113
#: includes/modules/form/module-form-action-post.php:2147
#: includes/modules/form/module-form-action-term.php:1157
#: includes/modules/form/module-form-action-term.php:1191
#: includes/modules/form/module-form-action-term.php:1225
#: includes/modules/form/module-form-action-term.php:1259
#: includes/modules/form/module-form-action-term.php:1293
#: includes/modules/form/module-form-action-user.php:2019
#: includes/modules/form/module-form-action-user.php:2053
#: includes/modules/form/module-form-action-user.php:2087
#: includes/modules/form/module-form-action-user.php:2121
#: includes/modules/form/module-form-action-user.php:2155
#: includes/modules/form/module-form-action-user.php:2189
#: includes/modules/form/module-form-action-user.php:2223
#: includes/modules/form/module-form-action-user.php:2257
#: includes/modules/form/module-form-action-user.php:2291
#: includes/modules/form/module-form-action-user.php:2325
msgid "Select a field or enter a field key"
msgstr ""

#: includes/modules/form/module-form-action-post.php:2162
#: includes/modules/form/module-form-action-term.php:1308
#: includes/modules/form/module-form-action-user.php:2341
#: pro/includes/modules/form/module-form-action-option.php:437
msgid "Load ACF fields"
msgstr ""

#: includes/modules/form/module-form-action-post.php:2165
#: includes/modules/form/module-form-action-term.php:1311
#: includes/modules/form/module-form-action-user.php:2344
msgid "Select which ACF fields should have their values loaded"
msgstr ""

#: includes/modules/form/module-form-action-redirect.php:17
msgid "Redirect action"
msgstr ""

#: includes/modules/form/module-form-action-redirect.php:139
msgid "Redirect"
msgstr ""

#: includes/modules/form/module-form-action-redirect.php:146
msgid "Action URL"
msgstr ""

#: includes/modules/form/module-form-action-redirect.php:149
msgid "The redirection URL."
msgstr ""

#: includes/modules/form/module-form-action-term.php:17
msgid "Term action"
msgstr ""

#: includes/modules/form/module-form-action-term.php:620
msgid "Create term"
msgstr ""

#: includes/modules/form/module-form-action-term.php:621
msgid "Update term"
msgstr ""

#: includes/modules/form/module-form-action-term.php:707
#: includes/modules/form/module-form-action-term.php:871
#: includes/modules/form/module-form-action-term.php:1091
msgid "Current Term"
msgstr ""

#: includes/modules/form/module-form-action-term.php:708
#: includes/modules/form/module-form-action-term.php:872
#: includes/modules/form/module-form-action-term.php:1092
msgid "Current Term Parent"
msgstr ""

#: includes/modules/form/module-form-action-term.php:709
#: includes/modules/form/module-form-action-term.php:873
#: includes/modules/form/module-form-action-term.php:1093
msgid "Term Selector"
msgstr ""

#: includes/modules/form/module-form-action-term.php:811
#: includes/modules/form/module-form-action-term.php:1206
#: includes/modules/taxonomy/module-taxonomy-fields.php:33
#: includes/modules/taxonomy/module-taxonomy.php:29
#: pro/includes/fields/field-post-field.php:59
#: pro/includes/fields/field-post-field.php:68
#: pro/includes/locations/menu-item-type.php:24
#: pro/includes/modules/dev/module-dev-metabox.php:174
msgid "Taxonomy"
msgstr ""

#: includes/modules/form/module-form-action-term.php:842
#: includes/modules/form/module-form-action-term.php:1240
msgid "Parent"
msgstr ""

#: includes/modules/form/module-form-action-term.php:918
#: includes/modules/form/module-form-action-term.php:1274
#: includes/modules/form/module-form-action-user.php:1717
#: includes/modules/form/module-form-action-user.php:2272
#: pro/includes/fields/field-payment.php:129
#: pro/includes/modules/script/module-script-table.php:258
msgid "Description"
msgstr ""

#: includes/modules/form/module-form-action-user.php:19
msgid "User action"
msgstr ""

#: includes/modules/form/module-form-action-user.php:339
msgid "An error has occured. Please try again"
msgstr ""

#: includes/modules/form/module-form-action-user.php:340
msgid "Invalid e-mail"
msgstr ""

#: includes/modules/form/module-form-action-user.php:341
msgid "Invalid e-mail or password"
msgstr ""

#: includes/modules/form/module-form-action-user.php:342
msgid "Invalid username"
msgstr ""

#: includes/modules/form/module-form-action-user.php:343
msgid "Invalid username or password"
msgstr ""

#: includes/modules/form/module-form-action-user.php:344
msgid "E-mail address is already used"
msgstr ""

#: includes/modules/form/module-form-action-user.php:345
msgid "Username is already used"
msgstr ""

#: includes/modules/form/module-form-action-user.php:346
msgid "Username may not be longer than 60 characters."
msgstr ""

#: includes/modules/form/module-form-action-user.php:1224
msgid "Create user"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1225
msgid "Update user"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1226
#: includes/modules/form/module-form-action-user.php:1819
msgid "Log user"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1233
#: includes/modules/form/module-form-fields.php:1747
msgid "Validation"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1236
msgid "(Optional) Automatically validate fields"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1244
msgid "Built-in validation"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1267
msgid "User"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1278
#: includes/modules/form/module-form-action-user.php:1328
msgid "Login"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1303
msgid "Login type"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1314
msgid "E-mail"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1315
#: includes/modules/form/module-form-action-user.php:1539
#: includes/modules/form/module-form-action-user.php:2034
#: pro/includes/fields/field-payment.php:301
#: pro/includes/fields/field-payment.php:341
msgid "Username"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1316
msgid "E-mail or username"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1353
#: includes/modules/form/module-form-action-user.php:1564
#: includes/modules/form/module-form-action-user.php:2068
#: pro/includes/fields/field-payment.php:311
#: pro/includes/fields/field-payment.php:351
msgid "Password"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1378
msgid "Remember me"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1575
msgid "Generate Password"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1592
#: includes/modules/form/module-form-action-user.php:2102
msgid "First name"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1617
#: includes/modules/form/module-form-action-user.php:2136
msgid "Last name"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1642
#: includes/modules/form/module-form-action-user.php:2170
msgid "Nickname"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1667
#: includes/modules/form/module-form-action-user.php:2204
msgid "Display name"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1692
#: includes/modules/form/module-form-action-user.php:2238
msgid "Website"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1791
#: includes/modules/form/module-form-action-user.php:2306
msgid "Role"
msgstr ""

#: includes/modules/form/module-form-action-user.php:1822
msgid "Log user once created"
msgstr ""

#: includes/modules/form/module-form-fields.php:1153
#: includes/modules/form/module-form-shortcode.php:159
#: includes/modules/form/module-form.php:28
msgid "Form"
msgstr ""

#: includes/modules/form/module-form-fields.php:1181
msgid "General"
msgstr ""

#: includes/modules/form/module-form-fields.php:1241
#: includes/modules/form/module-form.php:62
msgid "Actions"
msgstr ""

#: includes/modules/form/module-form-fields.php:1244
msgid "Add actions on form submission"
msgstr ""

#: includes/modules/form/module-form-fields.php:1254
msgid "Add action"
msgstr ""

#: includes/modules/form/module-form-fields.php:1280
msgid "Field groups locations rules"
msgstr ""

#: includes/modules/form/module-form-fields.php:1283
msgid "Apply field groups locations rules on front-end display"
msgstr ""

#: includes/modules/form/module-form-fields.php:1299
msgid "Honeypot"
msgstr ""

#: includes/modules/form/module-form-fields.php:1302
msgid ""
"Whether to include a hidden input field to capture non human form "
"submission. Defaults to true."
msgstr ""

#: includes/modules/form/module-form-fields.php:1319
msgid "Kses"
msgstr ""

#: includes/modules/form/module-form-fields.php:1322
msgid ""
"Whether or not to sanitize all $_POST data with the wp_kses_post() function. "
"Defaults to true."
msgstr ""

#: includes/modules/form/module-form-fields.php:1339
msgid "Uploader"
msgstr ""

#: includes/modules/form/module-form-fields.php:1342
msgid ""
"Whether to use the WP uploader or a basic input for image and file fields. "
"Defaults to 'wp'."
msgstr ""

#: includes/modules/form/module-form-fields.php:1352
msgid "WordPress"
msgstr ""

#: includes/modules/form/module-form-fields.php:1369
#: pro/includes/fields/field-post-field.php:45
msgid "Attributes"
msgstr ""

#: includes/modules/form/module-form-fields.php:1385
msgid "Form attributes"
msgstr ""

#: includes/modules/form/module-form-fields.php:1388
msgid "Attributes settings related to the form."
msgstr ""

#: includes/modules/form/module-form-fields.php:1465
msgid "Fields attributes"
msgstr ""

#: includes/modules/form/module-form-fields.php:1468
msgid "Attributes settings related to the fields."
msgstr ""

#: includes/modules/form/module-form-fields.php:1560
#: pro/includes/fields/field-flexible-content-grid.php:191
msgid "Top"
msgstr ""

#: includes/modules/form/module-form-fields.php:1561
#: pro/includes/fields/field-flexible-content-grid.php:163
msgid "Left"
msgstr ""

#: includes/modules/form/module-form-fields.php:1585
msgid "Field"
msgstr ""

#: includes/modules/form/module-form-fields.php:1586
msgid "Above field"
msgstr ""

#: includes/modules/form/module-form-fields.php:1599
#: includes/modules/form/module-form-fields.php:1647
msgid "Submit button"
msgstr ""

#: includes/modules/form/module-form-fields.php:1602
msgid "Whether or not to create a form submit button. Defaults to true"
msgstr ""

#: includes/modules/form/module-form-fields.php:1619
msgid "Submit value"
msgstr ""

#: includes/modules/form/module-form-fields.php:1622
msgid "The text displayed on the submit button"
msgstr ""

#: includes/modules/form/module-form-fields.php:1650
msgid "HTML used to render the submit button."
msgstr ""

#: includes/modules/form/module-form-fields.php:1674
msgid "Submit spinner"
msgstr ""

#: includes/modules/form/module-form-fields.php:1677
msgid "HTML used to render the submit button loading spinner."
msgstr ""

#: includes/modules/form/module-form-fields.php:1721
msgid "Form render"
msgstr ""

#: includes/modules/form/module-form-fields.php:1724
msgid "Render customized form HTML. Leave empty to render form normally."
msgstr ""

#: includes/modules/form/module-form-fields.php:1725
msgid "Render field group:"
msgstr ""

#: includes/modules/form/module-form-fields.php:1726
msgid "Render field:"
msgstr ""

#: includes/modules/form/module-form-fields.php:1727
msgid "Render all fields:"
msgstr ""

#: includes/modules/form/module-form-fields.php:1728
msgid "Render submit button:"
msgstr ""

#: includes/modules/form/module-form-fields.php:1764
msgid "Hide general error"
msgstr ""

#: includes/modules/form/module-form-fields.php:1767
msgid ""
"Hide the general error message: \"Validation failed. 1 field requires "
"attention\""
msgstr ""

#: includes/modules/form/module-form-fields.php:1784
msgid "Hide successful re-validation"
msgstr ""

#: includes/modules/form/module-form-fields.php:1787
msgid ""
"Hide \"Validation successful\" notice when an error has been previously "
"thrown"
msgstr ""

#: includes/modules/form/module-form-fields.php:1812
msgid "Hide confirmation on exit"
msgstr ""

#: includes/modules/form/module-form-fields.php:1815
msgid "Do not prompt user on page refresh"
msgstr ""

#: includes/modules/form/module-form-fields.php:1832
msgid "General error messages"
msgstr ""

#: includes/modules/form/module-form-fields.php:1835
msgid "Customize general error messages."
msgstr ""

#: includes/modules/form/module-form-fields.php:1937
msgid "Fields errors position"
msgstr ""

#: includes/modules/form/module-form-fields.php:1940
msgid "Choose where to display field errors"
msgstr ""

#: includes/modules/form/module-form-fields.php:1950
msgid "Below fields"
msgstr ""

#: includes/modules/form/module-form-fields.php:1951
msgid "Group errors"
msgstr ""

#: includes/modules/form/module-form-fields.php:1952
msgid "Hide errors"
msgstr ""

#: includes/modules/form/module-form-fields.php:1964
msgid "Fields errors class"
msgstr ""

#: includes/modules/form/module-form-fields.php:1967
msgid "Add class to the error message"
msgstr ""

#: includes/modules/form/module-form-fields.php:1996
msgid "Success"
msgstr ""

#: includes/modules/form/module-form-fields.php:2012
msgid "Redirection"
msgstr ""

#: includes/modules/form/module-form-fields.php:2015
msgid "The URL to be redirected to after the form is submitted."
msgstr ""

#: includes/modules/form/module-form-fields.php:2015
msgid "This setting is deprecated, use the new \"Redirect Action\" instead."
msgstr ""

#: includes/modules/form/module-form-fields.php:2033
msgid "Hide form"
msgstr ""

#: includes/modules/form/module-form-fields.php:2036
msgid "Hide form on successful submission"
msgstr ""

#: includes/modules/form/module-form-fields.php:2060
msgid "Scroll to message"
msgstr ""

#: includes/modules/form/module-form-fields.php:2063
msgid "Scroll to message on success"
msgstr ""

#: includes/modules/form/module-form-fields.php:2087
msgid "Success message"
msgstr ""

#: includes/modules/form/module-form-fields.php:2090
msgid "The message displayed above the form after the submission."
msgstr ""

#: includes/modules/form/module-form-fields.php:2107
#: includes/modules/form/module-form.php:112
msgid "Form updated"
msgstr ""

#: includes/modules/form/module-form-fields.php:2116
msgid "Success wrapper HTML"
msgstr ""

#: includes/modules/form/module-form-fields.php:2119
msgid "HTML used to render the updated message."
msgstr ""

#: includes/modules/form/module-form-fields.php:2120
#, php-format
msgid ""
"If used, you have to include the following code <code>%s</code> to print the "
"actual \"Success message\" above."
msgstr ""

#: includes/modules/form/module-form-front-render.php:302
msgid "Validate Email"
msgstr ""

#: includes/modules/form/module-form-upgrades.php:1267
msgid "Set a unique action slug."
msgstr ""

#: includes/modules/form/module-form-upgrades.php:6480
msgid "Post updated"
msgstr ""

#: includes/modules/form/module-form.php:30
msgid "Edit Form"
msgstr ""

#: includes/modules/form/module-form.php:31
#: includes/modules/form/module-form.php:32
msgid "New Form"
msgstr ""

#: includes/modules/form/module-form.php:33
msgid "Form Title"
msgstr ""

#: includes/modules/form/module-form.php:38
#: includes/modules/form/module-form.php:39
msgid "Export Forms"
msgstr ""

#: includes/modules/form/module-form.php:40
msgid "Select Forms"
msgstr ""

#: includes/modules/form/module-form.php:41
msgid "No form available."
msgstr ""

#: includes/modules/form/module-form.php:42
msgid "No forms selected"
msgstr ""

#: includes/modules/form/module-form.php:43
msgid "1 form exported"
msgstr ""

#: includes/modules/form/module-form.php:44
#, php-format
msgid "%s forms exported"
msgstr ""

#: includes/modules/form/module-form.php:45
#: pro/includes/modules/template/module-template.php:42
msgid ""
"It is recommended to include this code within the <code>acfe/init</code> "
"hook."
msgstr ""

#: includes/modules/form/module-form.php:46
#: includes/modules/form/module-form.php:47
msgid "Import Forms"
msgstr ""

#: includes/modules/form/module-form.php:48
msgid "1 form imported"
msgstr ""

#: includes/modules/form/module-form.php:49
#, php-format
msgid "%s forms imported"
msgstr ""

#: includes/modules/form/module-form.php:63
msgid "Shortcode"
msgstr ""

#: includes/modules/form/module-form.php:103
msgid "Validation failed"
msgstr ""

#: includes/modules/form/module-form.php:104
msgid "Validation successful"
msgstr ""

#: includes/modules/form/module-form.php:105
msgid "1 field requires attention"
msgstr ""

#: includes/modules/form/module-form.php:106
#, php-format
msgid "%d fields require attention"
msgstr ""

#: includes/modules/form/module-form.php:261
#: includes/modules/form/module-form.php:300
msgid "Integration"
msgstr ""

#: includes/modules/form/module-form.php:301
msgid "Template Tags"
msgstr ""

#: includes/modules/form/module-form.php:302
msgid "Hooks"
msgstr ""

#: includes/modules/form/module-form.php:303
msgid "Helpers"
msgstr ""

#: includes/modules/form/module-form.php:309
msgid "Guides"
msgstr ""

#: includes/modules/form/module-form.php:315
msgid "Dummy Title & Content Fields"
msgstr ""

#: includes/modules/form/module-form.php:316
msgid "Hide a Field on Front-End"
msgstr ""

#: includes/modules/form/module-form.php:317
msgid "Passing Data to a Form"
msgstr ""

#: includes/modules/form/module-form.php:318
msgid "Using Actions Output Data"
msgstr ""

#: includes/modules/form/module-form.php:330
msgid "Shortcodes"
msgstr ""

#: includes/modules/form/module-form.php:345
msgid "PHP code"
msgstr ""

#: includes/modules/form/module-form.php:761
msgid "This form name already exists"
msgstr ""

#: includes/modules/option/module-option-table.php:21
#: pro/includes/modules/form/module-form-action-option.php:288
msgid "Option"
msgstr ""

#: includes/modules/option/module-option-table.php:106
msgid "No options avaliable."
msgstr ""

#: includes/modules/option/module-option-table.php:225
#: pro/includes/modules/dev/module-dev-metabox.php:78
#: pro/includes/modules/dev/module-dev-metabox.php:170
#: pro/includes/modules/dev/module-dev-metabox.php:239
#: pro/includes/modules/dev/module-dev-metabox.php:300
#: pro/includes/modules/dev/module-dev-metabox.php:347
#: pro/includes/modules/dev/module-dev-metabox.php:405
#: pro/includes/modules/dev/module-dev-metabox.php:463
#: pro/includes/modules/dev/module-dev-metabox.php:519
#: pro/includes/modules/dev/module-dev-metabox.php:574
msgid "ID"
msgstr ""

#: includes/modules/option/module-option.php:80
msgid "Option has been deleted"
msgstr ""

#: includes/modules/option/module-option.php:85
msgid "Options have been deleted"
msgstr ""

#: includes/modules/option/module-option.php:90
msgid "Option has been updated"
msgstr ""

#: includes/modules/option/module-option.php:95
msgid "Option has been added"
msgstr ""

#: includes/modules/option/module-option.php:564
#: pro/includes/admin/dashboard.php:144
msgid "Edit field group"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:33
#: includes/modules/options-page/module-options-page.php:30
msgid "Options Page"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:60
msgid ""
"The URL slug used to uniquely identify this options page. Defaults to a url "
"friendly version of Menu Title"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:84
msgid "The title displayed in the wp-admin sidebar. Defaults to Page Title"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:103
msgid ""
"The capability required for this menu to be displayed to the user. Defaults "
"to edit_posts.<br /><br />Read more about capability here: <a href=\"https://"
"wordpress.org/support/article/roles-and-capabilities/\" "
"target=\"_blank\">https://wordpress.org/support/article/roles-and-"
"capabilities/</a>"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:122
msgid ""
"The position in the menu order this menu should appear. Defaults to bottom "
"of utility menu items.<br /><br />WARNING: if two menu items use the same "
"position attribute, one of the items may be overwritten so that only one "
"item displays!<br />Risk of conflict can be reduced by using decimal instead "
"of integer values, e.g. 63.3 instead of 63."
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:141
msgid ""
"The slug of another WP admin page. if set, this will become a child page."
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:160
msgid ""
"The icon class for this menu. Defaults to default WordPress gear.<br /><br /"
">Read more about dashicons here: <a href=\"https://developer.wordpress.org/"
"resource/dashicons/\" target=\"_blank\">https://developer.wordpress.org/"
"resource/dashicons/</a>"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:179
msgid ""
"If set to true, this options page will redirect to the first child page (if "
"a child page exists). If set to false, this parent page will appear "
"alongside any child pages. Defaults to true"
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:198
msgid ""
"The <code>$post_id</code> to save/load data to/from. Can be set to a numeric "
"post ID (123), or a string ('user_2'). Defaults to 'options'."
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:217
msgid ""
"Whether to load the option (values saved from this options page) when "
"WordPress starts up. Defaults to false."
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:236
msgid "The update button text."
msgstr ""

#: includes/modules/options-page/module-options-page-fields.php:255
msgid "The message shown above the form on submit."
msgstr ""

#: includes/modules/options-page/module-options-page.php:25
#: includes/modules/options-page/module-options-page.php:29
#: includes/modules/options-page/module-options-page.php:31
#: pro/includes/fields/field-options-pages.php:14
msgid "Options Pages"
msgstr ""

#: includes/modules/options-page/module-options-page.php:32
msgid "Edit Options Page"
msgstr ""

#: includes/modules/options-page/module-options-page.php:33
msgid "New Options Page"
msgstr ""

#: includes/modules/options-page/module-options-page.php:34
msgid "Options Page Title"
msgstr ""

#: includes/modules/options-page/module-options-page.php:39
#: includes/modules/options-page/module-options-page.php:40
msgid "Export Options Pages"
msgstr ""

#: includes/modules/options-page/module-options-page.php:41
msgid "Select Options Pages"
msgstr ""

#: includes/modules/options-page/module-options-page.php:42
msgid "No options page available."
msgstr ""

#: includes/modules/options-page/module-options-page.php:43
msgid "No options pages selected"
msgstr ""

#: includes/modules/options-page/module-options-page.php:44
msgid "1 options page exported"
msgstr ""

#: includes/modules/options-page/module-options-page.php:45
#, php-format
msgid "%s options pages exported"
msgstr ""

#: includes/modules/options-page/module-options-page.php:47
#: includes/modules/options-page/module-options-page.php:48
msgid "Import Options Pages"
msgstr ""

#: includes/modules/options-page/module-options-page.php:49
msgid "1 options page imported"
msgstr ""

#: includes/modules/options-page/module-options-page.php:50
#, php-format
msgid "%s options pages imported"
msgstr ""

#: includes/modules/options-page/module-options-page.php:61
msgid "Menu slug"
msgstr ""

#: includes/modules/options-page/module-options-page.php:62
msgid "Post ID"
msgstr ""

#: includes/modules/options-page/module-options-page.php:64
msgid "Position"
msgstr ""

#: includes/modules/options-page/module-options-page.php:80
msgid "Options Updated"
msgstr ""

#: includes/modules/options-page/module-options-page.php:267
#: includes/modules/options-page/module-options-page.php:277
msgid "This options page slug already exists"
msgstr ""

#: includes/modules/performance/module-performance-functions.php:660
msgid "Ready"
msgstr ""

#: includes/modules/performance/module-performance-functions.php:674
msgid "Performance Mode is active."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:674
#, php-format
msgid "The '%s' meta was found on this object and is effective."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:679
msgid "Performance Mode is ready."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:679
#, php-format
msgid "The '%s' meta will be created when object will be saved."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:729
msgid "Hybrid meta found"
msgstr ""

#: includes/modules/performance/module-performance-functions.php:730
#, php-format
msgid "Hybrid engine '%s' meta found."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:730
msgid "This meta will be converted to Ultra engine upon save."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:755
msgid "Ultra meta found"
msgstr ""

#: includes/modules/performance/module-performance-functions.php:756
#, php-format
msgid "Ultra engine '%s' meta found."
msgstr ""

#: includes/modules/performance/module-performance-functions.php:756
msgid "This meta will be converted to Hybrid engine upon save."
msgstr ""

#: includes/modules/performance/module-performance-ui.php:47
msgid "Performance Mode"
msgstr ""

#: includes/modules/performance/module-performance-ui.php:84
#: pro/includes/modules/script/module-script-performance-converter.php:164
msgid "Test Drive"
msgstr ""

#: includes/modules/performance/module-performance-ui.php:85
#: pro/includes/fields/field-payment.php:384
#: pro/includes/fields/field-payment.php:1423
#: pro/includes/modules/script/module-script-performance-converter.php:165
msgid "Production"
msgstr ""

#: includes/modules/performance/module-performance-ui.php:86
#: pro/includes/modules/script/module-script-performance-converter.php:166
msgid "Rollback"
msgstr ""

#: includes/modules/performance/module-performance-ui.php:103
msgid "Engine"
msgstr ""

#: includes/modules/performance/module-performance-ui.php:108
#: pro/includes/fields/field-post-field.php:58
msgid "Status"
msgstr ""

#: includes/modules/performance/module-performance-ultra-fields.php:44
msgid "Save as individual meta"
msgstr ""

#: includes/modules/performance/module-performance-ultra-fields.php:47
msgid "Save the field as an individual meta."
msgstr ""

#: includes/modules/post-type/module-post-type-features.php:291
msgid "View post"
msgstr ""

#: includes/modules/post-type/module-post-type-features.php:292
msgid "Preview post"
msgstr ""

#: includes/modules/post-type/module-post-type-features.php:306
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#: includes/modules/post-type/module-post-type-features.php:307
msgctxt "publish box date format"
msgid "M j, Y"
msgstr ""

#: includes/modules/post-type/module-post-type-features.php:308
msgctxt "publish box time format"
msgid "H:i"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:33
#: includes/modules/post-type/module-post-type.php:29
msgid "Post Type"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:77
msgid ""
"Post type key. Must not exceed 20 characters and may only contain lowercase "
"alphanumeric characters, dashes, and underscores. See <a href=\"https://"
"developer.wordpress.org/reference/functions/sanitize_key/\" "
"target=\"_blank\">sanitize_key()</a>."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:96
msgid "A short descriptive summary of what the post type is."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:115
msgid ""
"Whether the post type is hierarchical (e.g. page). Allows Parent to be "
"specified. The <code>supports</code> parameter should contain 'page-"
"attributes' to show the parent select box on the editor page. Default false."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:134
msgid ""
"Core feature(s) the post type supports. Serves as an alias for calling <a "
"href=\"https://developer.wordpress.org/reference/functions/"
"add_post_type_support/\" target=\"_blank\">add_post_type_support()</a> "
"directly. Default is an array containing 'title' and 'editor'."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:167
msgid ""
"An array of taxonomy identifiers that will be registered for the post type. "
"Taxonomies can be registered later with <a href=\"https://developer."
"wordpress.org/reference/functions/register_taxonomy/\" "
"target=\"_blank\">register_taxonomy()</a> or <a href=\"https://developer."
"wordpress.org/reference/functions/register_taxonomy_for_object_type/\" "
"target=\"_blank\">register_taxonomy_for_object_type()</a>."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:185
msgid ""
"Controls how the type is visible to authors (<code>show_in_nav_menus</code>, "
"<code>show_ui</code>) and readers (<code>exclude_from_search</code>, "
"<code>publicly_queryable</code>). Default: false."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:204
msgid ""
"Whether to exclude posts with this post type from front end search results. "
"Default: value of the opposite of <code>public</code> argument"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:223
msgid ""
"Whether queries can be performed on the front end as part of "
"<code>parse_request()</code>. Default: value of <code>public</code> argument."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:242
msgid "Can this post_type be exported. Default: true."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:261
msgid ""
"Whether to delete posts of this type when deleting a user.<br/><br/>If true, "
"posts of this type belonging to the user will be moved to trash when then "
"user is deleted.<br/><br/>If false, posts of this type belonging to the user "
"will not be trashed or deleted.<br/><br/>If not set (the default), posts are "
"trashed if <code>post_type_supports('author')</code>. Otherwise posts are "
"not trashed or deleted. Default: null."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:305
msgid ""
"The position in the menu order the post type should appear. "
"<code>show_in_menu</code> must be true. Default: null – defaults to below "
"Comments."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:326
msgid ""
"The url to the icon to be used for this menu or the name of the icon from "
"the iconfont (<a href=\"https://developer.wordpress.org/resource/dashicons/"
"\" target=\"_blank\">Dashicons</a>). Default: null – defaults to the posts "
"icon."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:345
msgid ""
"Whether to generate a default UI for managing this post type in the admin. "
"Default: value of <code>public</code> argument"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:364
msgid ""
"Where to show the post type in the admin menu. <code>show_ui</code> must be "
"true. Default: value of <code>show_ui</code> argument"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:383
msgid ""
"If an existing top level page such as <code>tools.php</code> or <code>edit."
"php?post_type=page</code>, the post type will be placed as a sub menu of "
"that."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:411
msgid ""
"Whether post_type is available for selection in navigation menus. Default: "
"value of <code>public</code> argument"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:430
msgid ""
"Whether to make this post type available in the WordPress admin bar. "
"Default: value of <code>show_in_menu</code> argument"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:465
msgid ""
"Which template file to load for the archive query. Default: <a "
"href=\"https://developer.wordpress.org/themes/basics/template-hierarchy/\" "
"target=\"_blank\">Template hierarchy</a>"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:484
msgid "Enables post type archives. Default: false"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:503
msgid "Will use <code>name</code> as archive slug by default."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:531
#: includes/modules/taxonomy/module-taxonomy-fields.php:446
msgid "Number of posts to display in the archive page. Default: 10"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:560
msgid ""
"Sort retrieved posts by parameter in the archive page. Default: date "
"(<code>post_date</code>)."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:587
msgid ""
"Designates the ascending or descending order of the <code>orderby</code> "
"parameter in the archive page. Default: DESC."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:622
#: includes/modules/taxonomy/module-taxonomy-fields.php:513
msgid ""
"Custom field used for the <code>orderby</code> parameter in the archive page"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:649
#: includes/modules/taxonomy/module-taxonomy-fields.php:532
msgid ""
"Custom field type (NUMERIC, BINARY, CHAR, DATE, DATETIME, DECIMAL, SIGNED, "
"TIME, UNSIGNED) used for the <code>orderby</code> parameter in the archive "
"page"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:692
msgid ""
"Which template file to load for the single query. Default: <a href=\"https://"
"developer.wordpress.org/themes/basics/template-hierarchy/\" "
"target=\"_blank\">Template hierarchy</a>"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:711
msgid ""
"Triggers the handling of rewrites for this post type. To prevent rewrites, "
"set to false. Default: true and use <code>name</code> as slug"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:787
msgid ""
"Customize the permalink structure slug. Defaults to the <code>name</code> "
"value. Should be translatable."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:815
msgid ""
"Should the permalink structure be prepended with the front base. (example: "
"if your permalink structure is <code>/blog/</code>, then your links will be: "
"false-><code>/news/</code>, true-><code>/blog/news/</code>). Defaults to "
"true."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:843
msgid ""
"Should a feed permalink structure be built for this post type. Defaults to "
"<code>has_archive</code> value."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:871
msgid ""
"Should the permalink structure provide for pagination. Defaults to true."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:917
msgid "Add an \"Archive\" Options Page as submenu of the post type."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:935
msgid "Number of posts to display on the admin list screen. Default: 10"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:956
msgid ""
"Sort retrieved posts by parameter in the admin list screen. Default: date "
"(<code>post_date</code>)."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:975
msgid ""
"Designates the ascending or descending order of the <code>orderby</code> "
"parameter in the admin list screen. Default: DESC."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1002
#: includes/modules/taxonomy/module-taxonomy-fields.php:796
msgid ""
"Custom field used for the <code>orderby</code> parameter in the admin list "
"screen"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1021
#: includes/modules/taxonomy/module-taxonomy-fields.php:815
msgid ""
"Custom field type (NUMERIC, BINARY, CHAR, DATE, DATETIME, DECIMAL, SIGNED, "
"TIME, UNSIGNED) used for the <code>orderby</code> parameter in the admin "
"list screen"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1679
msgid ""
"The string to use to build the read, edit, and delete capabilities.<br />May "
"be passed as an array to allow for alternative plurals when using this "
"argument as a base to construct the capabilities, like this:<br /><br /"
">story<br />stories"
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1699
msgid ""
"An array of the capabilities for this post type. Specify capabilities like "
"this:<br /><br />edit_post : edit_book<br />read_post : read_book<br /"
">delete_post : delete_book<br />edit_posts : edit_books<br />publish_posts : "
"publish_books<br />etc..."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1719
msgid ""
"Whether to use the internal default meta capability handling. Default: null."
"<br/><br/>If set it to false then standard admin role can't edit the posts "
"types. Then the <code>edit_post</code> capability must be added to all roles "
"to add or edit the posts types."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1764
msgid ""
"Whether to expose this post type in the REST API. Must be true to enable the "
"Gutenberg editor. Default: false."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1783
msgid ""
"The base slug that this post type will use when accessed using the REST API: "
"Default: <code>name</code>."
msgstr ""

#: includes/modules/post-type/module-post-type-fields.php:1802
msgid ""
"An optional custom controller to use instead of <a href=\"https://developer."
"wordpress.org/reference/classes/wp_rest_posts_controller/\" "
"target=\"_blank\">WP_REST_Posts_Controller</a>. Must be a subclass of <a "
"href=\"https://developer.wordpress.org/reference/classes/wp_rest_controller/"
"\" target=\"_blank\">WP_REST_Controller</a>. Default: <a href=\"https://"
"developer.wordpress.org/reference/classes/wp_rest_posts_controller/\" "
"target=\"_blank\">WP_REST_Posts_Controller</a>."
msgstr ""

#: includes/modules/post-type/module-post-type.php:31
msgid "Edit Post Type"
msgstr ""

#: includes/modules/post-type/module-post-type.php:32
msgid "New Post Type"
msgstr ""

#: includes/modules/post-type/module-post-type.php:33
msgid "Post Type Label"
msgstr ""

#: includes/modules/post-type/module-post-type.php:38
#: includes/modules/post-type/module-post-type.php:39
msgid "Export Post Types"
msgstr ""

#: includes/modules/post-type/module-post-type.php:40
msgid "Select Post Types"
msgstr ""

#: includes/modules/post-type/module-post-type.php:41
msgid "No post type available."
msgstr ""

#: includes/modules/post-type/module-post-type.php:42
msgid "No post types selected"
msgstr ""

#: includes/modules/post-type/module-post-type.php:43
msgid "1 post type exported"
msgstr ""

#: includes/modules/post-type/module-post-type.php:44
#, php-format
msgid "%s post types exported"
msgstr ""

#: includes/modules/post-type/module-post-type.php:45
#: includes/modules/taxonomy/module-taxonomy.php:45
#, php-format
msgid ""
"It is recommended to include this code within the <code>init</code> hook (<a "
"href=\"%s\" target=\"blank\">see documentation</a>)."
msgstr ""

#: includes/modules/post-type/module-post-type.php:46
#: includes/modules/post-type/module-post-type.php:47
msgid "Import Post Types"
msgstr ""

#: includes/modules/post-type/module-post-type.php:48
msgid "1 post type imported"
msgstr ""

#: includes/modules/post-type/module-post-type.php:49
#, php-format
msgid "%s post types imported"
msgstr ""

#: includes/modules/post-type/module-post-type.php:195
#: includes/modules/post-type/module-post-type.php:215
msgid "This post type already exists"
msgstr ""

#: includes/modules/post-type/module-post-type.php:205
msgid "This post type is reserved"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:77
msgid ""
"The name of the taxonomy. Name should only contain lowercase letters and the "
"underscore character, and not be more than 32 characters long (database "
"structure restriction)"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:96
msgid "Include a description of the taxonomy"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:115
msgid ""
"Is this taxonomy hierarchical (have descendants) like categories or not "
"hierarchical like tags. Default: false"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:134
msgid ""
"The name of the object type for the taxonomy object. Object-types can be "
"built-in Post Type or any Custom Post Type that may be registered. Default "
"is None."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:150
msgid ""
"Whether a taxonomy is intended for use publicly either via the admin "
"interface or by front-end users. The default settings of "
"<code>publicly_queryable</code>, <code>show_ui</code>, and "
"<code>show_in_nav_menus</code> are inherited from <code>public</code>. "
"Default: true."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:169
msgid ""
"Whether the taxonomy is publicly queryable. Default: value of <code>public</"
"code>"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:188
msgid ""
"A function name that will be called when the count of an associated "
"<code>object_type</code>, such as post, is updated. Works much like a hook. "
"Default: None."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:207
msgid ""
"Provide a callback function for the meta box display. If not set, <a "
"href=\"https://developer.wordpress.org/reference/functions/"
"post_categories_meta_box/\" target=\"_blank\">post_categories_meta_box()</a> "
"is used for hierarchical taxonomies, and <a href=\"https://developer."
"wordpress.org/reference/functions/post_tags_meta_box/\" "
"target=\"_blank\">post_tags_meta_box()</a> is used for non-hierarchical. If "
"false, no meta box is shown. Default: null."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:262
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to <code>wp_set_object_terms()</code>. Default null which equates "
"to false."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:297
msgid ""
"Whether to generate and allow a UI for managing terms in this taxonomy in "
"the admin. Default: value of <code>public</code>."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:316
msgid ""
"Whether to show the taxonomy in the admin menu. If true, the taxonomy is "
"shown as a submenu of the object type menu. If false, no menu is shown. "
"<code>show_ui</code> must be true. Default: value of <code>show_ui</code>."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:335
msgid ""
"Makes this taxonomy available for selection in navigation menus. Default: "
"value of <code>public</code>."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:354
msgid ""
"Whether to list the taxonomy in the Tag Cloud Widget controls. Default: "
"value of <code>show_ui</code>."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:373
msgid ""
"Whether to show the taxonomy in the quick/bulk edit panel. Default: value of "
"<code>show_ui</code>."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:392
msgid ""
"Whether to display a column for the taxonomy on its post type listing "
"screens. Default: false."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:427
msgid ""
"Which template file to load for the archive query. Default: <a "
"href=\"https://developer.wordpress.org/themes/basics/template-hierarchy/"
"\">Template hierarchy</a>"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:467
msgid ""
"Sort retrieved posts by parameter in the archive page. Defaults: date "
"(<code>post_date</code>)."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:486
msgid ""
"Designates the ascending or descending order of the <code>orderby</code> "
"parameter in the archive page. Defaults: DESC."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:551
msgid ""
"Set to false to prevent automatic URL rewriting a.k.a. \"pretty "
"permalinks\". Pass an argument array to override default URL settings for "
"permalinks"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:627
msgid ""
"Used as pretty permalink text (i.e. <code>/tag/</code>). Default: value of "
"<code>name</code>"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:655
msgid "Allowing permalinks to be prepended with front base. Default: true."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:683
msgid "Either hierarchical rewrite tag or not. Default: false."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:729
msgid "Number of terms to display on the admin list screen"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:750
msgid ""
"Sort retrieved terms by parameter in the admin list screen. Accepts term "
"fields <code>name</code>, <code>slug</code>, <code>term_group</code>, "
"<code>term_id</code>, <code>id</code>, <code>description</code>, "
"<code>parent</code>, <code>count</code> (for term taxonomy count), or "
"<code>none</code> to omit the ORDER BY clause"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:769
msgid ""
"Designates the ascending or descending order of the <code>orderby</code> "
"parameter in the admin list screen. Default: ASC."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:850
msgid ""
"An array of labels for this taxonomy. By default tag labels are used for non-"
"hierarchical types and category labels for hierarchical ones.<br /><br /"
">Default: if empty, <code>name</code> is set to <code>label</code> value, "
"and <code>singular_name</code> is set to <code>name</code> value."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:1206
msgid ""
"An array of the capabilities for this taxonomy:<br /><br />manage_terms : "
"edit_posts<br />edit_terms : edit_posts<br />delete_terms : edit_posts<br /"
">assign_terms : edit_posts"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:1242
msgid ""
"Whether to include the taxonomy in the REST API. You will need to set this "
"to true in order to use the taxonomy in your gutenberg metablock. Default: "
"false."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy-fields.php:1261
msgid "To change the base url of REST API route. Default: <code>name</code>."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:31
msgid "Edit Taxonomy"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:32
msgid "New Taxonomy"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:33
msgid "Taxonomy Label"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:38
#: includes/modules/taxonomy/module-taxonomy.php:39
msgid "Export Taxonomies"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:40
msgid "Select Taxonomies"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:41
msgid "No taxonomy available."
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:42
msgid "No taxonomies selected"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:43
msgid "1 taxonomy exported"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:44
#, php-format
msgid "%s taxonomies exported"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:46
#: includes/modules/taxonomy/module-taxonomy.php:47
msgid "Import Taxonomies"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:48
msgid "1 taxonomy imported"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:49
#, php-format
msgid "%s taxonomies imported"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:62
msgid "Terms"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:188
#: includes/modules/taxonomy/module-taxonomy.php:209
msgid "This taxonomy already exists"
msgstr ""

#: includes/modules/taxonomy/module-taxonomy.php:199
msgid "This taxonomy is reserved"
msgstr ""

#: includes/modules/ui/module-ui-attachment.php:108
msgid "Edit Media"
msgstr ""

#: pro/assets/js/acfe-pro-admin.js:54 pro/assets/js/acfe-pro-input.js:4813
msgid "Cancel"
msgstr ""

#: pro/assets/js/acfe-pro-admin.js:183
msgid "Review local changes"
msgstr ""

#: pro/assets/js/acfe-pro-admin.js:187 pro/includes/module-posts.php:87
msgid "Loading diff"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:31
#: pro/includes/field-groups/field-group-ui.php:51
msgid "Shift+click: Alternative mode"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:72
#: pro/includes/field-groups/field-group-ui.php:48
msgid "Enter a Field Type"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:91
#: pro/includes/field-groups/field-group-ui.php:49
msgid "Enter the Field Label"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:91
msgid "Hint: Shift+click to bypass this prompt"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:234
#: pro/includes/fields/field-flexible-content-locations.php:312
msgid "or"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:305
msgid "(this field)"
msgstr ""

#: pro/assets/js/acfe-pro-field-group.js:336
msgid "No toggle fields available"
msgstr ""

#: pro/assets/js/acfe-pro-input.js:656 pro/assets/js/acfe-pro-input.js:718
#, javascript-format
msgid "Location not found: %s"
msgstr ""

#: pro/assets/js/acfe-pro-input.js:1426
#: pro/includes/modules/script/module-script.php:291
msgid "Clear"
msgstr ""

#: pro/assets/js/acfe-pro-input.js:1787
msgid "Maximum items reached ({max} items)"
msgstr ""

#: pro/assets/js/acfe-pro-input.js:2399
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/assets/js/acfe-pro-input.js:2400
#: pro/includes/fields/field-flexible-content-locations.php:181
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/assets/js/acfe-pro-input.js:4813
msgid "OK"
msgstr ""

#: pro/includes/admin/dashboard.php:184 pro/includes/admin/dashboard.php:232
msgid "Widget updated."
msgstr ""

#: pro/includes/admin/tools/rewrite-rules-export.php:15
#: pro/includes/admin/tools/rewrite-rules-export.php:42
#: pro/includes/admin/tools/rewrite-rules-export.php:48
msgid "Export Rewrite Rules"
msgstr ""

#: pro/includes/admin/tools/rewrite-rules-export.php:177
msgid "Rewrite rules exported."
msgstr ""

#: pro/includes/admin/tools/settings-export.php:15
msgid "Export Settings"
msgstr ""

#: pro/includes/admin/tools/settings-export.php:42
#: pro/includes/admin/tools/settings-export.php:48
#: pro/includes/admin/tools/settings-export.php:82
msgid "Export ACF Settings"
msgstr ""

#: pro/includes/admin/tools/settings-export.php:89
msgid ""
"You can copy and paste the following code to your theme's functions.php file "
"or include it within an external file."
msgstr ""

#: pro/includes/admin/tools/settings-export.php:243
msgid "Settings exported."
msgstr ""

#: pro/includes/admin/tools/settings-import.php:15
msgid "Import Settings"
msgstr ""

#: pro/includes/admin/tools/settings-import.php:26
#: pro/includes/admin/tools/settings-import.php:32
msgid "Import ACF Settings"
msgstr ""

#: pro/includes/admin/tools/settings-import.php:79
msgid "Settings imported."
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:40
msgid "Save Draft"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:41
#: pro/includes/fields/field-post-field.php:55
msgid "Preview"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:42
msgid "Post Status"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:43
msgid "Post Visibility"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:44
msgid "Publish Date"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:45
msgid "Move to trash"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:46
msgid "Publish/Update"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:47
msgid "Minor Publishing Actions"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:48
msgid "Misc Publishing Actions"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:49
msgid "Major Publishing Actions"
msgstr ""

#: pro/includes/field-groups/field-group-hide-on-screen.php:50
msgid "Publish Metabox"
msgstr ""

#: pro/includes/field-groups/field-group-ui.php:50
msgid "Hint: Shift to bypass this prompt"
msgstr ""

#: pro/includes/fields-settings/instructions.php:45
msgid "Instructions Placement"
msgstr ""

#: pro/includes/fields-settings/min-max.php:319
msgid "Selection restrictions"
msgstr ""

#: pro/includes/fields-settings/min-max.php:324
msgid "Min"
msgstr ""

#: pro/includes/fields-settings/min-max.php:333
msgid "Max"
msgstr ""

#: pro/includes/fields-settings/min-max.php:372
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] ""
msgstr[1] ""

#: pro/includes/fields-settings/min-max.php:385
#, php-format
msgid "%s allows a maximum of %s selection"
msgid_plural "%s allows a maximum of %s selections"
msgstr[0] ""
msgstr[1] ""

#: pro/includes/fields-settings/required.php:22
msgid "Required Message"
msgstr ""

#: pro/includes/fields-settings/visibility.php:44
msgid "Field Visibility"
msgstr ""

#: pro/includes/fields/field-address.php:19
#: pro/includes/fields/field-address.php:119
msgid "Address"
msgstr ""

#: pro/includes/fields/field-address.php:23
#: pro/includes/fields/field-address.php:90
msgid "Search for address..."
msgstr ""

#: pro/includes/fields/field-address.php:39
msgid "Full Address"
msgstr ""

#: pro/includes/fields/field-address.php:41
msgid "Street Number"
msgstr ""

#: pro/includes/fields/field-address.php:42
msgid "Street Name"
msgstr ""

#: pro/includes/fields/field-address.php:43
msgid "City"
msgstr ""

#: pro/includes/fields/field-address.php:44
msgid "State"
msgstr ""

#: pro/includes/fields/field-address.php:45
msgid "State (code)"
msgstr ""

#: pro/includes/fields/field-address.php:46
msgid "Post code"
msgstr ""

#: pro/includes/fields/field-address.php:47
msgid "Country"
msgstr ""

#: pro/includes/fields/field-address.php:48
msgid "Country (code)"
msgstr ""

#: pro/includes/fields/field-address.php:49
msgid "Place ID"
msgstr ""

#: pro/includes/fields/field-address.php:50
msgid "Latitutde"
msgstr ""

#: pro/includes/fields/field-address.php:51
msgid "Longitude"
msgstr ""

#: pro/includes/fields/field-address.php:95
#: pro/includes/fields/field-countries.php:123
#: pro/includes/fields/field-phone-number.php:48
msgid "Allow Countries"
msgstr ""

#: pro/includes/fields/field-address.php:96
msgid "Limit the search to specific countries"
msgstr ""

#: pro/includes/fields/field-address.php:106
#: pro/includes/fields/field-countries.php:134
msgid "All Countries"
msgstr ""

#: pro/includes/fields/field-address.php:111
msgid "Search Type"
msgstr ""

#: pro/includes/fields/field-address.php:112
msgid "Choose the allowed search type"
msgstr ""

#: pro/includes/fields/field-address.php:118
msgid "Geocode (non-business)"
msgstr ""

#: pro/includes/fields/field-address.php:120
msgid "Regions"
msgstr ""

#: pro/includes/fields/field-address.php:121
msgid "Cities"
msgstr ""

#: pro/includes/fields/field-address.php:122
msgid "Business"
msgstr ""

#: pro/includes/fields/field-address.php:128
msgid "Allow Geolocation"
msgstr ""

#: pro/includes/fields/field-address.php:129
msgid "Allow the user to find their current location"
msgstr ""

#: pro/includes/fields/field-address.php:137
msgid "Allow Custom Address"
msgstr ""

#: pro/includes/fields/field-address.php:138
msgid "Allow the user to enter an address not found in the Google Places API"
msgstr ""

#: pro/includes/fields/field-address.php:164
#: pro/includes/fields/field-google-map.php:317
msgid "API Key"
msgstr ""

#: pro/includes/fields/field-address.php:172
msgid "Individual Sub Fields"
msgstr ""

#: pro/includes/fields/field-address.php:175
msgid ""
"Choose which address component should be also saved as an individual sub "
"field"
msgstr ""

#: pro/includes/fields/field-address.php:177
msgid "+ Add Subfield"
msgstr ""

#: pro/includes/fields/field-address.php:325
msgid "Search"
msgstr ""

#: pro/includes/fields/field-address.php:327
msgid "Clear location"
msgstr ""

#: pro/includes/fields/field-address.php:329
msgid "Find current location"
msgstr ""

#: pro/includes/fields/field-block-editor.php:85
#: pro/includes/fields/field-block-editor.php:106
#: pro/includes/fields/field-wysiwyg.php:97
#: pro/includes/fields/field-wysiwyg.php:117
#: pro/includes/fields/field-wysiwyg.php:139
msgid "Height"
msgstr ""

#: pro/includes/fields/field-block-editor.php:151
#: pro/includes/fields/field-wysiwyg.php:209
msgid "Autoresize"
msgstr ""

#: pro/includes/fields/field-block-editor.php:154
#: pro/includes/fields/field-wysiwyg.php:212
msgid "Height will be based on the editor content"
msgstr ""

#: pro/includes/fields/field-block-editor.php:163
msgid "Top Bar"
msgstr ""

#: pro/includes/fields/field-block-editor.php:166
#: pro/includes/fields/field-wysiwyg.php:248
msgid "Show the menu bar on top of the editor"
msgstr ""

#: pro/includes/fields/field-block-editor.php:174
msgid "Top Bar Tools"
msgstr ""

#: pro/includes/fields/field-block-editor.php:182
msgid "Inserter"
msgstr ""

#: pro/includes/fields/field-block-editor.php:183
msgid "Selector"
msgstr ""

#: pro/includes/fields/field-block-editor.php:184
msgid "Undo"
msgstr ""

#: pro/includes/fields/field-block-editor.php:185
msgid "Contents"
msgstr ""

#: pro/includes/fields/field-block-editor.php:186
msgid "Navigation"
msgstr ""

#: pro/includes/fields/field-block-editor.php:187
msgid "Inspector"
msgstr ""

#: pro/includes/fields/field-block-editor.php:188
msgid "Editor"
msgstr ""

#: pro/includes/fields/field-block-editor.php:203
msgid "Fixed Toolbar"
msgstr ""

#: pro/includes/fields/field-block-editor.php:215
msgid "Allow Code Editing"
msgstr ""

#: pro/includes/fields/field-block-editor.php:227
msgid "Allow Lock"
msgstr ""

#: pro/includes/fields/field-block-editor.php:239
msgid "Allow File Upload"
msgstr ""

#: pro/includes/fields/field-block-editor.php:251
msgid "Allow File Library"
msgstr ""

#: pro/includes/fields/field-block-editor.php:272
msgid "Allowed Blocks"
msgstr ""

#: pro/includes/fields/field-block-editor.php:275
msgid "List of all available core blocks"
msgstr ""

#: pro/includes/fields/field-block-editor.php:277
msgid "+ Add block"
msgstr ""

#: pro/includes/fields/field-block-editor.php:622
msgctxt "CSS Font Family for Editor Font"
msgid "Noto Serif"
msgstr ""

#: pro/includes/fields/field-block-editor.php:654
msgid "Medium"
msgstr ""

#: pro/includes/fields/field-block-editor.php:654
msgid "Large"
msgstr ""

#: pro/includes/fields/field-block-editor.php:654
#: pro/includes/fields/field-image-sizes.php:62
#: pro/includes/fields/field-image-sizes.php:490
msgid "Full Size"
msgstr ""

#: pro/includes/fields/field-block-editor.php:674
msgid "Add title"
msgstr ""

#: pro/includes/fields/field-block-editor.php:675
msgid "Start writing or type / to choose a block"
msgstr ""

#: pro/includes/fields/field-block-types.php:80
msgid "Allow Block Types"
msgstr ""

#: pro/includes/fields/field-block-types.php:88
msgid "All block types"
msgstr ""

#: pro/includes/fields/field-block-types.php:121
msgid "Block type object"
msgstr ""

#: pro/includes/fields/field-block-types.php:122
msgid "Block type name"
msgstr ""

#: pro/includes/fields/field-checkbox.php:107
msgid "Toggle All"
msgstr ""

#: pro/includes/fields/field-color-picker.php:23
#: pro/includes/fields/field-color-picker.php:177
msgid "Select Color"
msgstr ""

#: pro/includes/fields/field-color-picker.php:67
#: pro/includes/fields/field-color-picker.php:164
msgid "Color Array"
msgstr ""

#: pro/includes/fields/field-color-picker.php:143
msgid "Display Style"
msgstr ""

#: pro/includes/fields/field-color-picker.php:173
#: pro/includes/fields/field-file.php:240
msgid "Button Label"
msgstr ""

#: pro/includes/fields/field-color-picker.php:190
msgid "Color Picker"
msgstr ""

#: pro/includes/fields/field-color-picker.php:198
msgid "Position Absolute"
msgstr ""

#: pro/includes/fields/field-color-picker.php:215
msgid "Text Input"
msgstr ""

#: pro/includes/fields/field-color-picker.php:239
#: pro/includes/fields/field-date-range-picker.php:275
msgid "Allow null"
msgstr ""

#: pro/includes/fields/field-color-picker.php:250
msgid "RGBA"
msgstr ""

#: pro/includes/fields/field-color-picker.php:260
msgid "Use Theme Colors"
msgstr ""

#: pro/includes/fields/field-color-picker.php:268
msgid "Custom Colors"
msgstr ""

#: pro/includes/fields/field-color-picker.php:269
msgid "Enter each choice on a new line."
msgstr ""

#: pro/includes/fields/field-column.php:66
#: pro/includes/fields/field-column.php:88
msgid "Border"
msgstr ""

#: pro/includes/fields/field-column.php:72
msgid "Column Border"
msgstr ""

#: pro/includes/fields/field-column.php:73
msgid "Fields Border"
msgstr ""

#: pro/includes/fields/field-column.php:94
msgid "Endpoint Border"
msgstr ""

#: pro/includes/fields/field-countries.php:17
msgid "Countries"
msgstr ""

#: pro/includes/fields/field-countries.php:166
msgid "Country array"
msgstr ""

#: pro/includes/fields/field-countries.php:167
msgid "Country name"
msgstr ""

#: pro/includes/fields/field-countries.php:168
msgid "Country code"
msgstr ""

#: pro/includes/fields/field-countries.php:186
#: pro/includes/fields/field-currencies.php:160
#: pro/includes/fields/field-date-range-picker.php:70
#: pro/includes/fields/field-image-sizes.php:127
#: pro/includes/fields/field-languages.php:253
#: pro/includes/fields/field-payment-cart.php:98
msgid "Display Format"
msgstr ""

#: pro/includes/fields/field-countries.php:187
#: pro/includes/fields/field-currencies.php:161
#: pro/includes/fields/field-date-range-picker.php:71
#: pro/includes/fields/field-languages.php:254
#: pro/includes/fields/field-payment-cart.php:99
msgid "The format displayed when editing a post"
msgstr ""

#: pro/includes/fields/field-countries.php:195
#: pro/includes/fields/field-currencies.php:169
#: pro/includes/fields/field-date-range-picker.php:79
#: pro/includes/fields/field-date-range-picker.php:95
#: pro/includes/fields/field-languages.php:262
#: pro/includes/fields/field-payment-cart.php:106
msgid "Custom:"
msgstr ""

#: pro/includes/fields/field-countries.php:201
#: pro/includes/fields/field-currencies.php:190
#: pro/includes/fields/field-languages.php:268
msgid "Display Flags"
msgstr ""

#: pro/includes/fields/field-countries.php:202
#: pro/includes/fields/field-currencies.php:191
msgid "Display countries flags"
msgstr ""

#: pro/includes/fields/field-countries.php:238
#: pro/includes/fields/field-currencies.php:227
#: pro/includes/fields/field-languages.php:305
msgid "Group by Continents"
msgstr ""

#: pro/includes/fields/field-countries.php:239
msgid "Group countries by their continent"
msgstr ""

#: pro/includes/fields/field-currencies.php:17
msgid "Currencies"
msgstr ""

#: pro/includes/fields/field-currencies.php:123
msgid "Allow Currencies"
msgstr ""

#: pro/includes/fields/field-currencies.php:134
msgid "All Currencies"
msgstr ""

#: pro/includes/fields/field-currencies.php:181
msgid "Currency array"
msgstr ""

#: pro/includes/fields/field-currencies.php:182
msgid "Currency name"
msgstr ""

#: pro/includes/fields/field-currencies.php:183
msgid "Currency code"
msgstr ""

#: pro/includes/fields/field-currencies.php:184
msgid "Currency symbol"
msgstr ""

#: pro/includes/fields/field-currencies.php:228
msgid "Group currencies by their continent"
msgstr ""

#: pro/includes/fields/field-date-picker.php:44
#: pro/includes/fields/field-date-range-picker.php:180
#: pro/includes/fields/field-date-time-picker.php:53
msgid "Date Restriction"
msgstr ""

#: pro/includes/fields/field-date-picker.php:70
#: pro/includes/fields/field-date-range-picker.php:255
#: pro/includes/fields/field-date-time-picker.php:105
msgid "No Weekends"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:22
msgid "Date Range Picker"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:85
msgid "Return Format"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:86
msgid "The format returned via template functions"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:101
msgid "Week Starts On"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:118
msgid "Separator"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:126
msgid "Default Date"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:153
msgid "Range Restriction"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:210
msgid "Custom Ranges"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:245
msgid "Show Dropdowns"
msgstr ""

#: pro/includes/fields/field-date-range-picker.php:265
msgid "Auto Close on Selection"
msgstr ""

#: pro/includes/fields/field-date-time-picker.php:80
#: pro/includes/fields/field-time-picker.php:50
msgid "Time Restriction"
msgstr ""

#: pro/includes/fields/field-date-time-picker.php:115
#: pro/includes/fields/field-time-picker.php:76
msgid "Hour Restriction"
msgstr ""

#: pro/includes/fields/field-date-time-picker.php:141
#: pro/includes/fields/field-time-picker.php:102
msgid "Minutes Restriction"
msgstr ""

#: pro/includes/fields/field-date-time-picker.php:168
#: pro/includes/fields/field-time-picker.php:129
msgid "Seconds Restriction"
msgstr ""

#: pro/includes/fields/field-field-groups.php:87
msgid "Allow Field Groups"
msgstr ""

#: pro/includes/fields/field-field-groups.php:95
msgid "All field groups"
msgstr ""

#: pro/includes/fields/field-field-groups.php:127
msgid "Field group object"
msgstr ""

#: pro/includes/fields/field-field-groups.php:128
msgid "Field group key"
msgstr ""

#: pro/includes/fields/field-field-groups.php:129
msgid "Field group ID"
msgstr ""

#: pro/includes/fields/field-field-types.php:17
msgid "Field Types"
msgstr ""

#: pro/includes/fields/field-field-types.php:85
msgid "Allow Field Types"
msgstr ""

#: pro/includes/fields/field-field-types.php:93
msgid "All field types"
msgstr ""

#: pro/includes/fields/field-field-types.php:125
msgid "Field type object"
msgstr ""

#: pro/includes/fields/field-field-types.php:126
msgid "Field type name"
msgstr ""

#: pro/includes/fields/field-fields.php:95
msgid "Allow Fields"
msgstr ""

#: pro/includes/fields/field-fields.php:103
msgid "All fields"
msgstr ""

#: pro/includes/fields/field-fields.php:135
msgid "Field object"
msgstr ""

#: pro/includes/fields/field-fields.php:136
msgid "Field key"
msgstr ""

#: pro/includes/fields/field-fields.php:137
msgid "Field name"
msgstr ""

#: pro/includes/fields/field-fields.php:138
msgid "Field ID"
msgstr ""

#: pro/includes/fields/field-file.php:20 pro/includes/fields/field-file.php:194
#: pro/includes/fields/field-payment-cart.php:119
#: pro/includes/fields/field-payment-selector.php:96
#: pro/includes/fields/field-phone-number.php:67
msgid "Select"
msgstr ""

#: pro/includes/fields/field-file.php:22 pro/includes/fields/field-file.php:243
#: pro/includes/fields/field-file.php:562
msgid "Add File"
msgstr ""

#: pro/includes/fields/field-file.php:134
msgid "Preview Style"
msgstr ""

#: pro/includes/fields/field-file.php:223
#: pro/includes/fields/field-image.php:87
msgid "Leave blank to use the default upload folder."
msgstr ""

#: pro/includes/fields/field-file.php:223
#: pro/includes/fields/field-image.php:87
msgid "Available template tags:"
msgstr ""

#: pro/includes/fields/field-file.php:226
#: pro/includes/fields/field-image.php:90
msgid "Current default upload folder:"
msgstr ""

#: pro/includes/fields/field-file.php:231
#: pro/includes/fields/field-image.php:95
msgid "Upload Folder"
msgstr ""

#: pro/includes/fields/field-file.php:279
msgid "Stylized Button"
msgstr ""

#: pro/includes/fields/field-file.php:302
msgid "File Count"
msgstr ""

#: pro/includes/fields/field-file.php:351
msgid "Allow Multiple Files"
msgstr ""

#: pro/includes/fields/field-file.php:529
msgid "File name"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:16
msgid "Auto"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:133
msgid "Grid System"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:136
msgid "Enable columns mode"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:164
#: pro/includes/fields/field-flexible-content-grid.php:192
msgid "Center"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:165
msgid "Right"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:166
msgid "Space evenly"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:167
msgid "Space between"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:168
msgid "Space around"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:181
msgid "Align"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:190
msgid "Stretch"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:193
msgid "Bottom"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:206
msgid "Valign"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:220
msgid "No Wrap"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:248
msgid "Grid System: Container"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:251
msgid "Apply maximum grid width"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:292
msgid "Grid settings"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:319
msgid "Default Col"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:330
msgid "All sizes"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:338
msgid "Allowed Col"
msgstr ""

#: pro/includes/fields/field-flexible-content-grid.php:530
msgid "Resize column"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:42
msgid "Dynamic Preview: Iframe"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:45
msgid "Render dynamic previews in isolated iframes"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:75
msgid "Dynamic Preview: Responsive"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:78
msgid "Render responsive icons to switch the flexible content size"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:132
msgid "Fullscreen"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:140
msgid "Desktop"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:145
msgid "Tablet"
msgstr ""

#: pro/includes/fields/field-flexible-content-iframe.php:150
msgid "Mobile"
msgstr ""

#: pro/includes/fields/field-flexible-content-locations.php:180
#: pro/includes/fields/field-flexible-content-locations.php:195
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/includes/fields/field-flexible-content-locations.php:244
msgid "Layouts Locations Rules"
msgstr ""

#: pro/includes/fields/field-flexible-content-locations.php:247
msgid "Define custom locations rules for each layouts"
msgstr ""

#: pro/includes/fields/field-flexible-content-locations.php:312
msgid "Location Rules"
msgstr ""

#: pro/includes/fields/field-flexible-content-locations.php:344
msgid "Add rule group"
msgstr ""

#: pro/includes/fields/field-flexible-content-locations.php:442
msgid "and"
msgstr ""

#: pro/includes/fields/field-google-map.php:56
msgid "Map Preview"
msgstr ""

#: pro/includes/fields/field-google-map.php:91
msgid "Zoom"
msgstr ""

#: pro/includes/fields/field-google-map.php:153
msgid "Marker: Image"
msgstr ""

#: pro/includes/fields/field-google-map.php:161
msgid "Marker: Size"
msgstr ""

#: pro/includes/fields/field-google-map.php:200
msgid "View: Map Type"
msgstr ""

#: pro/includes/fields/field-google-map.php:215
msgid "View: Hide UI"
msgstr ""

#: pro/includes/fields/field-google-map.php:226
msgid "View: Hide Zoom Control"
msgstr ""

#: pro/includes/fields/field-google-map.php:246
msgid "View: Hide Map Selection"
msgstr ""

#: pro/includes/fields/field-google-map.php:266
msgid "View: Hide Fullscreen"
msgstr ""

#: pro/includes/fields/field-google-map.php:286
msgid "View: Hide Streetview"
msgstr ""

#: pro/includes/fields/field-google-map.php:306
msgid "View: Map Style"
msgstr ""

#: pro/includes/fields/field-image-selector.php:50
msgid "Choices"
msgstr ""

#: pro/includes/fields/field-image-selector.php:51
msgid ""
"Enter each choice on a new line. Image can be an URL or an attachment ID. "
"For example:"
msgstr ""

#: pro/includes/fields/field-image-selector.php:51
msgid "choice1 : 895<br/>choice2 : /image.jpg"
msgstr ""

#: pro/includes/fields/field-image-selector.php:71
msgid "Images Size"
msgstr ""

#: pro/includes/fields/field-image-selector.php:82
msgid "Container"
msgstr ""

#: pro/includes/fields/field-image-selector.php:120
msgid "Array"
msgstr ""

#: pro/includes/fields/field-image-selector.php:121
msgid "Image"
msgstr ""

#: pro/includes/fields/field-image-sizes.php:17
msgid "Image Sizes"
msgstr ""

#: pro/includes/fields/field-image-sizes.php:92
msgid "Allow Image Sizes"
msgstr ""

#: pro/includes/fields/field-image-sizes.php:100
msgid "All image sizes"
msgstr ""

#: pro/includes/fields/field-image-sizes.php:133
#: pro/includes/fields/field-image-sizes.php:148
msgid "Size name"
msgstr ""

#: pro/includes/fields/field-image-sizes.php:135
msgid "Size name & dimensions"
msgstr ""

#: pro/includes/fields/field-image-sizes.php:147
msgid "Size object"
msgstr ""

#: pro/includes/fields/field-languages.php:17
#: pro/includes/fields/field-languages.php:179
msgid "Languages"
msgstr ""

#: pro/includes/fields/field-languages.php:180
msgid "Display languages set in"
msgstr ""

#: pro/includes/fields/field-languages.php:200
msgid "Allow Languages"
msgstr ""

#: pro/includes/fields/field-languages.php:212
msgid "All Languages"
msgstr ""

#: pro/includes/fields/field-languages.php:245
msgid "Language array"
msgstr ""

#: pro/includes/fields/field-languages.php:246
msgid "Language name"
msgstr ""

#: pro/includes/fields/field-languages.php:247
msgid "Language locale code"
msgstr ""

#: pro/includes/fields/field-languages.php:269
msgid "Display languages flags"
msgstr ""

#: pro/includes/fields/field-languages.php:306
msgid "Group languages by their continent"
msgstr ""

#: pro/includes/fields/field-menu-locations.php:17
msgid "Menu Locations"
msgstr ""

#: pro/includes/fields/field-menu-locations.php:81
msgid "Allow Locations"
msgstr ""

#: pro/includes/fields/field-menu-locations.php:89
msgid "All locations"
msgstr ""

#: pro/includes/fields/field-menu-locations.php:123
msgid "Both (Array)"
msgstr ""

#: pro/includes/fields/field-menus.php:17
msgid "Menus"
msgstr ""

#: pro/includes/fields/field-menus.php:81
msgid "Allow Menus"
msgstr ""

#: pro/includes/fields/field-menus.php:89
msgid "All menus"
msgstr ""

#: pro/includes/fields/field-menus.php:121
msgid "Menu object"
msgstr ""

#: pro/includes/fields/field-menus.php:122
msgid "Menu name"
msgstr ""

#: pro/includes/fields/field-menus.php:123
msgid "Menu ID"
msgstr ""

#: pro/includes/fields/field-options-pages.php:83
msgid "Allow Options Pages"
msgstr ""

#: pro/includes/fields/field-options-pages.php:91
msgid "All options pages"
msgstr ""

#: pro/includes/fields/field-options-pages.php:123
msgid "Options page object"
msgstr ""

#: pro/includes/fields/field-options-pages.php:124
msgid "Options page name"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:19
msgid "Payment Cart"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:68
#: pro/includes/fields/field-payment-selector.php:57
msgid "Payment Field"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:76
#: pro/includes/fields/field-payment-selector.php:65
msgid "Select the payment field"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:82
msgid "items"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:83
msgid "Enter each choice on a new line with the item price. For example:"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:83
msgid "Item 1 : 29<br/>Item 2 : 49"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:118
#: pro/includes/fields/field-payment-selector.php:95
msgid "Radio Button"
msgstr ""

#: pro/includes/fields/field-payment-cart.php:490
msgid "This item doesn't exists. Please try again"
msgstr ""

#: pro/includes/fields/field-payment-selector.php:19
msgid "Payment Selector"
msgstr ""

#: pro/includes/fields/field-payment-selector.php:23
#: pro/includes/fields/field-payment-selector.php:74
msgid "Credit Card"
msgstr ""

#: pro/includes/fields/field-payment-selector.php:71
msgid "Payments Labels"
msgstr ""

#: pro/includes/fields/field-payment-selector.php:80
msgid "PayPal Label"
msgstr ""

#: pro/includes/fields/field-payment-selector.php:140
msgid "Icons"
msgstr ""

#: pro/includes/fields/field-payment.php:20
msgid "Payment"
msgstr ""

#: pro/includes/fields/field-payment.php:27
msgid "PayPal Checkout"
msgstr ""

#: pro/includes/fields/field-payment.php:29
msgid "Pay now"
msgstr ""

#: pro/includes/fields/field-payment.php:54
msgid "Amount cannot be null"
msgstr ""

#: pro/includes/fields/field-payment.php:58
msgid "Payment failed"
msgstr ""

#: pro/includes/fields/field-payment.php:60
msgid "Your card number is invalid"
msgstr ""

#: pro/includes/fields/field-payment.php:61
msgid "Payment cancelled, please try again"
msgstr ""

#: pro/includes/fields/field-payment.php:93
msgid "Gateways"
msgstr ""

#: pro/includes/fields/field-payment.php:94
msgid "Choose your payment gateways"
msgstr ""

#: pro/includes/fields/field-payment.php:107
#: pro/includes/fields/field-payment.php:489
#: pro/includes/fields/field-payment.php:1457
msgid "Amount"
msgstr ""

#: pro/includes/fields/field-payment.php:108
msgid "The amount to charge"
msgstr ""

#: pro/includes/fields/field-payment.php:116
msgid "Currency"
msgstr ""

#: pro/includes/fields/field-payment.php:117
msgid "The currency to use"
msgstr ""

#: pro/includes/fields/field-payment.php:130
msgid "A description attached to the payment. Useful for displaying to users"
msgstr ""

#: pro/includes/fields/field-payment.php:137
msgid "Paypal Button"
msgstr ""

#: pro/includes/fields/field-payment.php:138
msgid "The paypal checkout button text"
msgstr ""

#: pro/includes/fields/field-payment.php:152
msgid "Display Button"
msgstr ""

#: pro/includes/fields/field-payment.php:241
msgid "Stripe: Hide Postal Code"
msgstr ""

#: pro/includes/fields/field-payment.php:242
msgid ""
"Hide Stripe's postal code field validation which can be displayed in "
"specific cases"
msgstr ""

#: pro/includes/fields/field-payment.php:257
msgid "Stripe Test API"
msgstr ""

#: pro/includes/fields/field-payment.php:258
msgid "Your Stripe Test API Keys"
msgstr ""

#: pro/includes/fields/field-payment.php:271
#: pro/includes/fields/field-payment.php:291
msgid "Public key"
msgstr ""

#: pro/includes/fields/field-payment.php:277
msgid "Stripe Production API"
msgstr ""

#: pro/includes/fields/field-payment.php:278
msgid "Your Stripe Production API Keys"
msgstr ""

#: pro/includes/fields/field-payment.php:297
msgid "PayPal Test API"
msgstr ""

#: pro/includes/fields/field-payment.php:298
msgid "Your PayPal Sandbox Credentials"
msgstr ""

#: pro/includes/fields/field-payment.php:321
#: pro/includes/fields/field-payment.php:361
msgid "Signature"
msgstr ""

#: pro/includes/fields/field-payment.php:331
#: pro/includes/fields/field-payment.php:371
msgid "Merchant ID"
msgstr ""

#: pro/includes/fields/field-payment.php:337
msgid "PayPal Production API"
msgstr ""

#: pro/includes/fields/field-payment.php:338
msgid "Your PayPal Production Credentials"
msgstr ""

#: pro/includes/fields/field-payment.php:377
msgid "Mode"
msgstr ""

#: pro/includes/fields/field-payment.php:378
msgid "Switch API mode"
msgstr ""

#: pro/includes/fields/field-payment.php:383
#: pro/includes/fields/field-payment.php:485
#: pro/includes/fields/field-payment.php:1422
#: pro/includes/fields/field-payment.php:1456
msgid "Test"
msgstr ""

#: pro/includes/fields/field-payment.php:485
#: pro/includes/fields/field-payment.php:1456
msgid "Gateway"
msgstr ""

#: pro/includes/fields/field-payment.php:494
#: pro/includes/fields/field-payment.php:1459
msgid "Items"
msgstr ""

#: pro/includes/fields/field-payment.php:499
#: pro/includes/fields/field-payment.php:1461
#: pro/includes/fields/field-post-field.php:49
msgid "Date"
msgstr ""

#: pro/includes/fields/field-payment.php:503
#: pro/includes/fields/field-payment.php:1462
msgid "IP Address"
msgstr ""

#: pro/includes/fields/field-payment.php:507
#: pro/includes/fields/field-payment.php:1463
msgid "Payment ID"
msgstr ""

#: pro/includes/fields/field-payment.php:511
#: pro/includes/fields/field-payment.php:512
msgid "Payment Object"
msgstr ""

#: pro/includes/fields/field-phone-number.php:19
#: pro/includes/fields/field-phone-number.php:181
msgid "Phone Number"
msgstr ""

#: pro/includes/fields/field-phone-number.php:54
msgid "All countries"
msgstr ""

#: pro/includes/fields/field-phone-number.php:61
msgid "Preferred Countries"
msgstr ""

#: pro/includes/fields/field-phone-number.php:74
msgid "Default Country"
msgstr ""

#: pro/includes/fields/field-phone-number.php:95
msgid "Geolocation"
msgstr ""

#: pro/includes/fields/field-phone-number.php:96
msgid ""
"Lookup the user's country based on their IP address using <a href='https://"
"ipinfo.io/' target='_blank'>IPinfo.io</a>"
msgstr ""

#: pro/includes/fields/field-phone-number.php:103
msgid "Geolocation API token"
msgstr ""

#: pro/includes/fields/field-phone-number.php:104
msgid ""
"<a href=\"https://ipinfo.io/\" target=\"_blank\">IPinfo.io</a> API token"
msgstr ""

#: pro/includes/fields/field-phone-number.php:120
msgid "Native Names"
msgstr ""

#: pro/includes/fields/field-phone-number.php:129
msgid "National Mode"
msgstr ""

#: pro/includes/fields/field-phone-number.php:147
msgid "Allow Dropdown"
msgstr ""

#: pro/includes/fields/field-phone-number.php:156
msgid "Separate Dial Code"
msgstr ""

#: pro/includes/fields/field-phone-number.php:180
msgid "Phone Array"
msgstr ""

#: pro/includes/fields/field-phone-number.php:186
msgid "National Number"
msgstr ""

#: pro/includes/fields/field-phone-number.php:187
msgid "International Number"
msgstr ""

#: pro/includes/fields/field-phone-number.php:205
msgid "Additional Settings"
msgstr ""

#: pro/includes/fields/field-phone-number.php:209
msgid ""
"Additional settings such as \"National Number\", \"International Number\" "
"return formats and phone number server validation are available when using "
"the <a href=\"https://github.com/giggsey/libphonenumber-for-php\" "
"target=\"_blank\">Libphonenumber for PHP</a> library.<br /><br />You can "
"install this library manually or with the <a href=\"https://www.acf-extended."
"com/addons/acf-extended-pro-libphonenumber.zip\" target=\"_blank\">ACF "
"Extended: Phone Number Library Addon</a> plugin."
msgstr ""

#: pro/includes/fields/field-phone-number.php:232
msgctxt "Phone Number JS invalidPhoneNumber"
msgid "Invalid Phone Number"
msgstr ""

#: pro/includes/fields/field-phone-number.php:233
msgctxt "Phone Number JS invalidCountry"
msgid "Invalid Country"
msgstr ""

#: pro/includes/fields/field-phone-number.php:234
msgctxt "Phone Number JS phoneNumberTooShort"
msgid "Phone Number is too short"
msgstr ""

#: pro/includes/fields/field-phone-number.php:235
msgctxt "Phone Number JS phoneNumberTooLong"
msgid "Phone Number is too long"
msgstr ""

#: pro/includes/fields/field-phone-number.php:339
#: pro/includes/fields/field-phone-number.php:356
msgid "Invalid Phone Number"
msgstr ""

#: pro/includes/fields/field-phone-number.php:350
msgid "Invalid Country"
msgstr ""

#: pro/includes/fields/field-post-field.php:20
msgid "Post Field"
msgstr ""

#: pro/includes/fields/field-post-field.php:47
msgid "Comments"
msgstr ""

#: pro/includes/fields/field-post-field.php:50
#: pro/includes/locations/settings.php:91
msgid "Discussion"
msgstr ""

#: pro/includes/fields/field-post-field.php:51
msgid "Excerpt"
msgstr ""

#: pro/includes/fields/field-post-field.php:52
msgid "Featured Image"
msgstr ""

#: pro/includes/fields/field-post-field.php:54
msgid "Permalink"
msgstr ""

#: pro/includes/fields/field-post-field.php:56
msgid "Revisions"
msgstr ""

#: pro/includes/fields/field-post-field.php:57
msgid "Revisions List"
msgstr ""

#: pro/includes/fields/field-post-field.php:61
msgid "Trackbacks"
msgstr ""

#: pro/includes/fields/field-post-field.php:62
msgid "Visibility"
msgstr ""

#: pro/includes/fields/field-post-formats.php:17
msgid "Post Formats"
msgstr ""

#: pro/includes/fields/field-post-formats.php:93
msgid "Allow Formats"
msgstr ""

#: pro/includes/fields/field-post-formats.php:101
msgid "All Formats"
msgstr ""

#: pro/includes/fields/field-relationship.php:36
msgid "Allow Post Creation"
msgstr ""

#: pro/includes/fields/field-relationship.php:44
msgid "Allow Post Edit"
msgstr ""

#: pro/includes/fields/field-relationship.php:137
msgctxt "post"
msgid "Add New"
msgstr ""

#: pro/includes/fields/field-tab.php:32
msgid "No Preference"
msgstr ""

#: pro/includes/fields/field-templates.php:17
#: pro/includes/modules/template/module-template.php:22
#: pro/includes/modules/template/module-template.php:25
#: pro/includes/modules/template/module-template.php:27
msgid "Templates"
msgstr ""

#: pro/includes/fields/field-templates.php:88
msgid "Allow Templates"
msgstr ""

#: pro/includes/fields/field-templates.php:96
msgid "All templates"
msgstr ""

#: pro/includes/fields/field-templates.php:128
msgid "Template ID"
msgstr ""

#: pro/includes/fields/field-templates.php:129
msgid "Template name"
msgstr ""

#: pro/includes/fields/field-true-false.php:33
msgid "Style"
msgstr ""

#: pro/includes/fields/field-true-false.php:39
msgid "Default Rounded"
msgstr ""

#: pro/includes/fields/field-true-false.php:40
msgid "Small"
msgstr ""

#: pro/includes/fields/field-true-false.php:41
msgid "Small Rounded"
msgstr ""

#: pro/includes/fields/field-true-false.php:42
msgid "Alternative"
msgstr ""

#: pro/includes/fields/field-true-false.php:43
msgid "Alternative Rounded"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:74
msgid "Auto initialization"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:75
msgid "Automatically initialize TinyMCE when shown"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:162
msgid "Valid Elements"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:165
msgid "Set custom valid HTML tags"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:183
msgid "Custom Style"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:186
msgid "Add multiple files separated with comma"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:197
msgid "Disable WP Style"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:200
msgid "Remove TinyMCE builtin stylesheets"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:221
msgid "Disable Resize"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:224
msgid "Remove the editor resize functionality"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:233
msgid "Disable Path"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:236
msgid "Hide the editor path status bar"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:245
msgid "Menu Bar"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:257
msgid "Transparent Editor"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:260
msgid "Set the editor's background as transparent"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:269
msgid "Merge Toolbars"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:272
msgid "Glue editor toolbars together"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:281
msgid "Customize Toolbar"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:349
msgid "+ Add button"
msgstr ""

#: pro/includes/fields/field-wysiwyg.php:445
msgid "Custom Toolbar Buttons"
msgstr ""

#: pro/includes/locations/attachment-list.php:206
#: pro/includes/locations/user-list.php:204
msgid "List"
msgstr ""

#: pro/includes/locations/dashboard.php:14
msgid "WP Dashboard"
msgstr ""

#: pro/includes/locations/dashboard.php:22
msgid "Widget"
msgstr ""

#: pro/includes/locations/menu-item-depth.php:14
msgid "Menu Item Depth"
msgstr ""

#: pro/includes/locations/menu-item-depth.php:45
#: pro/includes/locations/post-date-time.php:59
#: pro/includes/locations/post-date.php:59
#: pro/includes/locations/post-time.php:59
#: pro/includes/modules/global-field-condition.php:267
msgid "is less than"
msgstr ""

#: pro/includes/locations/menu-item-depth.php:46
#: pro/includes/locations/post-date-time.php:60
#: pro/includes/locations/post-date.php:60
#: pro/includes/locations/post-time.php:60
#: pro/includes/modules/global-field-condition.php:268
msgid "is less or equal to"
msgstr ""

#: pro/includes/locations/menu-item-depth.php:47
#: pro/includes/locations/post-date-time.php:61
#: pro/includes/locations/post-date.php:61
#: pro/includes/locations/post-time.php:61
#: pro/includes/modules/global-field-condition.php:269
msgid "is greater than"
msgstr ""

#: pro/includes/locations/menu-item-depth.php:48
#: pro/includes/locations/post-date-time.php:62
#: pro/includes/locations/post-date.php:62
#: pro/includes/locations/post-time.php:62
#: pro/includes/modules/global-field-condition.php:270
msgid "is greater or equal to"
msgstr ""

#: pro/includes/locations/menu-item-type.php:14
msgid "Menu Item Type"
msgstr ""

#: pro/includes/locations/post-author-role.php:14
msgid "Post Author Role"
msgstr ""

#: pro/includes/locations/post-author.php:14
msgid "Post Author"
msgstr ""

#: pro/includes/locations/post-date-time.php:14
msgid "Post Date Time"
msgstr ""

#: pro/includes/locations/post-date.php:14
msgid "Post Date"
msgstr ""

#: pro/includes/locations/post-path.php:14
msgid "Post Path"
msgstr ""

#: pro/includes/locations/post-path.php:44
#: pro/includes/locations/post-slug.php:44
#: pro/includes/locations/post-title.php:44
#: pro/includes/locations/taxonomy-term-name.php:44
#: pro/includes/locations/taxonomy-term-slug.php:44
msgid "contains"
msgstr ""

#: pro/includes/locations/post-path.php:45
#: pro/includes/locations/post-slug.php:45
#: pro/includes/locations/post-title.php:45
#: pro/includes/locations/taxonomy-term-name.php:45
#: pro/includes/locations/taxonomy-term-slug.php:45
msgid "doesn't contains"
msgstr ""

#: pro/includes/locations/post-path.php:46
#: pro/includes/locations/post-slug.php:46
#: pro/includes/locations/post-title.php:46
#: pro/includes/locations/taxonomy-term-name.php:46
#: pro/includes/locations/taxonomy-term-slug.php:46
msgid "starts with"
msgstr ""

#: pro/includes/locations/post-path.php:47
#: pro/includes/locations/post-slug.php:47
#: pro/includes/locations/post-title.php:47
#: pro/includes/locations/taxonomy-term-name.php:47
#: pro/includes/locations/taxonomy-term-slug.php:47
msgid "doesn't starts with"
msgstr ""

#: pro/includes/locations/post-path.php:48
#: pro/includes/locations/post-slug.php:48
#: pro/includes/locations/post-title.php:48
#: pro/includes/locations/taxonomy-term-name.php:48
#: pro/includes/locations/taxonomy-term-slug.php:48
msgid "ends with"
msgstr ""

#: pro/includes/locations/post-path.php:49
#: pro/includes/locations/post-slug.php:49
#: pro/includes/locations/post-title.php:49
#: pro/includes/locations/taxonomy-term-name.php:49
#: pro/includes/locations/taxonomy-term-slug.php:49
msgid "doesn't ends with"
msgstr ""

#: pro/includes/locations/post-path.php:50
#: pro/includes/locations/post-slug.php:50
#: pro/includes/locations/post-title.php:50
#: pro/includes/locations/taxonomy-term-name.php:50
#: pro/includes/locations/taxonomy-term-slug.php:50
msgid "matches regex"
msgstr ""

#: pro/includes/locations/post-path.php:51
#: pro/includes/locations/post-slug.php:51
#: pro/includes/locations/post-title.php:51
#: pro/includes/locations/taxonomy-term-name.php:51
#: pro/includes/locations/taxonomy-term-slug.php:51
msgid "doesn't matches regex"
msgstr ""

#: pro/includes/locations/post-screen.php:14
msgid "Post Screen"
msgstr ""

#: pro/includes/locations/post-slug.php:14
msgid "Post Slug"
msgstr ""

#: pro/includes/locations/post-time.php:14
msgid "Post Time"
msgstr ""

#: pro/includes/locations/post-title.php:14
msgid "Post Title"
msgstr ""

#: pro/includes/locations/settings.php:78
msgid "WP Settings"
msgstr ""

#: pro/includes/locations/settings.php:88
msgctxt "settings screen"
msgid "General"
msgstr ""

#: pro/includes/locations/settings.php:89
msgid "Writing"
msgstr ""

#: pro/includes/locations/settings.php:90
msgid "Reading"
msgstr ""

#: pro/includes/locations/settings.php:92
msgid "Media"
msgstr ""

#: pro/includes/locations/settings.php:93
msgid "Permalinks"
msgstr ""

#: pro/includes/locations/taxonomy-term-name.php:14
msgid "Taxonomy Term Name"
msgstr ""

#: pro/includes/locations/taxonomy-term-parent.php:14
msgid "Taxonomy Term Parent"
msgstr ""

#: pro/includes/locations/taxonomy-term-slug.php:14
msgid "Taxonomy Term Slug"
msgstr ""

#: pro/includes/locations/taxonomy-term-type.php:14
msgid "Taxonomy Term Type"
msgstr ""

#: pro/includes/locations/taxonomy-term-type.php:22
msgid "Top Level Term (no parent)"
msgstr ""

#: pro/includes/locations/taxonomy-term-type.php:23
msgid "Parent Term (has children)"
msgstr ""

#: pro/includes/locations/taxonomy-term-type.php:24
msgid "Child Term (has parent)"
msgstr ""

#: pro/includes/locations/taxonomy-term.php:14
msgid "Taxonomy Term"
msgstr ""

#: pro/includes/locations/woocommerce.php:14
msgid "Woocommerce"
msgstr ""

#: pro/includes/locations/woocommerce.php:22
msgid "Cart page"
msgstr ""

#: pro/includes/locations/woocommerce.php:23
msgid "Checkout page"
msgstr ""

#: pro/includes/locations/woocommerce.php:24
msgid "My account page"
msgstr ""

#: pro/includes/locations/woocommerce.php:25
msgid "Shop page"
msgstr ""

#: pro/includes/locations/woocommerce.php:26
msgid "Terms and conditions"
msgstr ""

#: pro/includes/module-local.php:730
msgid "Import available"
msgstr ""

#: pro/includes/module-posts.php:85
msgid "Review local JSON changes"
msgstr ""

#: pro/includes/module-posts.php:86
msgid "Review local PHP changes"
msgstr ""

#: pro/includes/module-posts.php:88
msgid "Sync changes"
msgstr ""

#: pro/includes/module-posts.php:128
#, php-format
msgid "Item synchronised."
msgid_plural "%s items synchronised."
msgstr[0] ""
msgstr[1] ""

#: pro/includes/module-posts.php:240
#, php-format
msgid "Item duplicated."
msgid_plural "%s items duplicated."
msgstr[0] ""
msgstr[1] ""

#: pro/includes/module-posts.php:357
msgid "Duplicate this item"
msgstr ""

#: pro/includes/module-posts.php:357 pro/includes/module-posts.php:377
msgid "Duplicate"
msgstr ""

#: pro/includes/module-posts.php:387 pro/includes/module-sync.php:128
msgid "Sync from Database"
msgstr ""

#: pro/includes/module-posts.php:388 pro/includes/module-sync.php:165
msgid "Sync from JSON file"
msgstr ""

#: pro/includes/module-posts.php:389 pro/includes/module-sync.php:165
msgid "Sync from PHP file"
msgstr ""

#: pro/includes/module-sync.php:31
msgid "Invalid parameter(s)."
msgstr ""

#: pro/includes/module-sync.php:58 pro/includes/module-sync.php:80
msgid "Sorry, this item is unavailable for diff comparison."
msgstr ""

#: pro/includes/module-sync.php:91
msgid "Database item"
msgstr ""

#: pro/includes/module-sync.php:183
msgid "JSON file"
msgstr ""

#: pro/includes/module-sync.php:183
msgid "PHP file"
msgstr ""

#: pro/includes/module-sync.php:202
#, php-format
msgid "Last updated: %s"
msgstr ""

#: pro/includes/modules/dev/module-dev-clean-meta.php:108
#: pro/includes/modules/script/module-script-orphan-meta-cleaner.php:298
msgid "No orphan meta found"
msgstr ""

#: pro/includes/modules/dev/module-dev-clean-meta.php:116
msgid "Deleted meta"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:86
#: pro/includes/modules/dev/module-dev-metabox.php:178
#: pro/includes/modules/dev/module-dev-metabox.php:247
#: pro/includes/modules/dev/module-dev-metabox.php:351
#: pro/includes/modules/dev/module-dev-metabox.php:409
#: pro/includes/modules/dev/module-dev-metabox.php:467
#: pro/includes/modules/dev/module-dev-metabox.php:523
msgid "Object data"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:90
#: pro/includes/modules/dev/module-dev-metabox.php:182
#: pro/includes/modules/dev/module-dev-metabox.php:251
#: pro/includes/modules/dev/module-dev-metabox.php:304
#: pro/includes/modules/dev/module-dev-metabox.php:355
#: pro/includes/modules/dev/module-dev-metabox.php:413
#: pro/includes/modules/dev/module-dev-metabox.php:471
#: pro/includes/modules/dev/module-dev-metabox.php:527
#: pro/includes/modules/dev/module-dev-metabox.php:578
msgid "Meta count"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:93
#: pro/includes/modules/dev/module-dev-metabox.php:185
#: pro/includes/modules/dev/module-dev-metabox.php:254
#: pro/includes/modules/dev/module-dev-metabox.php:307
#: pro/includes/modules/dev/module-dev-metabox.php:358
#: pro/includes/modules/dev/module-dev-metabox.php:416
#: pro/includes/modules/dev/module-dev-metabox.php:474
#: pro/includes/modules/dev/module-dev-metabox.php:530
#: pro/includes/modules/dev/module-dev-metabox.php:581
msgid "Clean"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:99
#: pro/includes/modules/dev/module-dev-metabox.php:191
#: pro/includes/modules/dev/module-dev-metabox.php:260
#: pro/includes/modules/dev/module-dev-metabox.php:313
#: pro/includes/modules/dev/module-dev-metabox.php:364
#: pro/includes/modules/dev/module-dev-metabox.php:422
#: pro/includes/modules/dev/module-dev-metabox.php:480
#: pro/includes/modules/dev/module-dev-metabox.php:536
#: pro/includes/modules/dev/module-dev-metabox.php:587
msgid "Performance"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:121
msgid "Post Object"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:196
msgid "Term Object"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:265
msgid "User Object"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:369
msgid "Options Page Object"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:427
#: pro/includes/modules/dev/module-dev-metabox.php:541
msgid "Post Type Object"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:485
msgid "Taxonomy Object"
msgstr ""

#: pro/includes/modules/dev/module-dev-metabox.php:648
msgid "Users"
msgstr ""

#: pro/includes/modules/form/module-form-action-option.php:17
msgid "Option action"
msgstr ""

#: pro/includes/modules/form/module-form-action-option.php:440
msgid "Which ACF fields should have their values loaded"
msgstr ""

#: pro/includes/modules/form/module-form-ajax.php:149
msgid "Ajax submission"
msgstr ""

#: pro/includes/modules/form/module-form-ajax.php:152
msgid "Enable Ajax form submission"
msgstr ""

#: pro/includes/modules/global-field-condition.php:77
msgid "Set as Global Conditional Logic"
msgstr ""

#: pro/includes/modules/rewrite-rules.php:37
#: pro/includes/modules/rewrite-rules.php:186
msgid "Rewrite Rules"
msgstr ""

#: pro/includes/modules/script/module-script-orphan-meta-cleaner.php:127
#: pro/includes/modules/script/module-script-performance-converter.php:129
msgid "No options pages"
msgstr ""

#: pro/includes/modules/script/module-script-orphan-meta-cleaner.php:297
msgid "Script finished."
msgstr ""

#: pro/includes/modules/script/module-script-table.php:27
msgid "Script"
msgstr ""

#: pro/includes/modules/script/module-script-table.php:28
#: pro/includes/modules/script/module-script.php:57
#: pro/includes/modules/script/module-script.php:393
msgid "Scripts"
msgstr ""

#: pro/includes/modules/script/module-script-table.php:134
msgid "No scripts avaliable."
msgstr ""

#: pro/includes/modules/script/module-script-table.php:196
msgid "Run now"
msgstr ""

#: pro/includes/modules/script/module-script.php:92
msgid "Sorry, you are not allowed to access this page."
msgstr ""

#: pro/includes/modules/script/module-script.php:295
msgid "Total"
msgstr ""

#: pro/includes/modules/script/module-script.php:296
msgid "Items left"
msgstr ""

#: pro/includes/modules/script/module-script.php:297
msgid "Time left"
msgstr ""

#: pro/includes/modules/script/module-script.php:298
msgid "Timer"
msgstr ""

#: pro/includes/modules/template/module-template-features.php:45
#: pro/includes/modules/template/module-template-fields.php:33
#: pro/includes/modules/template/module-template.php:26
msgid "Template"
msgstr ""

#: pro/includes/modules/template/module-template-features.php:61
msgid "is equal to"
msgstr ""

#: pro/includes/modules/template/module-template-features.php:78
msgid "No templates found"
msgstr ""

#: pro/includes/modules/template/module-template-fields.php:61
msgid "The template name."
msgstr ""

#: pro/includes/modules/template/module-template-fields.php:107
msgid ""
"The Dynamic Templates module let you manage default ACF values in an "
"advanced way. In order to start, you need to connect a field group to a "
"specific template. Head over the Field Groups administration, select the "
"field group of your choice and scroll down to the location settings. To "
"connect a field group to a template, choose a classic location (like Post "
"Type = Post) and add a new rule using the “AND” operator. Select the rule "
"\"Dynamic Template\" under \"Forms\", then choose your template and save the "
"field group."
msgstr ""

#: pro/includes/modules/template/module-template-fields.php:109
msgid ""
"You can now fill up the template page, values will be automatically loaded "
"for the location it is tied to if the user never saved anything. In this "
"screenshot, there is a different template for the \"Post Type: Page\" & the "
"\"Post Type: Post\" while using the same field group."
msgstr ""

#: pro/includes/modules/template/module-template-fields.php:111
msgid ""
"The Dynamic Template design is smart enough to fulfill complex scenarios. "
"For example, one single template can be used in conjunction with as many "
"field group location as needed. It is also possible to add multiple field "
"groups into a single template to keep things organized."
msgstr ""

#: pro/includes/modules/template/module-template-fields.php:113
msgid ""
"<u>Note:</u> Template values will be loaded when the user haven't saved any "
"data related to the said values. Typically in a \"New Post\" situation. If "
"the user save a value, even an empty one, the template won't be loaded."
msgstr ""

#: pro/includes/modules/template/module-template.php:28
msgid "Edit Template"
msgstr ""

#: pro/includes/modules/template/module-template.php:29
msgid "New Template"
msgstr ""

#: pro/includes/modules/template/module-template.php:30
msgid "Template Title"
msgstr ""

#: pro/includes/modules/template/module-template.php:35
#: pro/includes/modules/template/module-template.php:36
msgid "Export Templates"
msgstr ""

#: pro/includes/modules/template/module-template.php:37
msgid "Select Templates"
msgstr ""

#: pro/includes/modules/template/module-template.php:38
msgid "No template available."
msgstr ""

#: pro/includes/modules/template/module-template.php:39
msgid "No templates selected"
msgstr ""

#: pro/includes/modules/template/module-template.php:40
msgid "1 template exported"
msgstr ""

#: pro/includes/modules/template/module-template.php:41
#, php-format
msgid "%s templates exported"
msgstr ""

#: pro/includes/modules/template/module-template.php:43
#: pro/includes/modules/template/module-template.php:44
msgid "Import Templates"
msgstr ""

#: pro/includes/modules/template/module-template.php:45
msgid "1 template imported"
msgstr ""

#: pro/includes/modules/template/module-template.php:46
#, php-format
msgid "%s templates imported"
msgstr ""

#: pro/includes/modules/template/module-template.php:58
msgid "Locations"
msgstr ""

#: pro/includes/modules/template/module-template.php:102
msgid "You are currently editing a Dynamic Template."
msgstr ""

#: pro/includes/modules/template/module-template.php:325
msgid "This template already exists"
msgstr ""

#: pro/includes/updater.php:253
#, php-format
msgid ""
"There is a new version of %1$s available. %2$sView version %3$s details%4$s."
msgstr ""

#: pro/includes/updater.php:261
#, php-format
msgid ""
"There is a new version of %1$s available. %2$sView version %3$s details%4$s "
"or %5$supdate now%6$s."
msgstr ""

#: pro/includes/updater.php:510
msgid "You do not have permission to install plugin updates"
msgstr ""

#: pro/includes/updater.php:510
msgid "Error"
msgstr ""

#: pro/includes/updates.php:259
msgid "Deactivate License"
msgstr ""

#: pro/includes/updates.php:259
msgid "Activate License"
msgstr ""

#: pro/includes/updates.php:299
msgid "ACF Extended: License Information"
msgstr ""

#: pro/includes/updates.php:303
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/includes/updates.php:311 pro/includes/updates.php:331
msgid "License Key"
msgstr ""

#: pro/includes/updates.php:367
msgid "ACF Extended: Update Information"
msgstr ""

#: pro/includes/updates.php:374
msgid "Current Version"
msgstr ""

#: pro/includes/updates.php:383
msgid "Latest Version"
msgstr ""

#: pro/includes/updates.php:392
msgid "Update Available"
msgstr ""

#: pro/includes/updates.php:403 pro/includes/updates.php:430
msgid "Update Plugin"
msgstr ""

#: pro/includes/updates.php:406 pro/includes/updates.php:433
msgid "Please enter your license key above to unlock updates"
msgstr ""

#: pro/includes/updates.php:415 pro/includes/updates.php:437
msgid "Check Again"
msgstr ""

#: pro/includes/updates.php:448
msgid "Changelog"
msgstr ""

#: pro/includes/updates.php:494
msgid "An error occurred, please try again."
msgstr ""

#: pro/includes/updates.php:498
msgid "Your license key has expired."
msgstr ""

#: pro/includes/updates.php:502
msgid "Your license key has been disabled."
msgstr ""

#: pro/includes/updates.php:506
msgid "Licence key invalid."
msgstr ""

#: pro/includes/updates.php:510
msgid "Your license is not active for this URL."
msgstr ""

#: pro/includes/updates.php:514
msgid "This appears to be an invalid license key for ACF Extended Pro."
msgstr ""

#: pro/includes/updates.php:518
msgid "Your license key has reached its activation limit."
msgstr ""

#: pro/includes/updates.php:522
msgid "<b>Licence key activated</b>. Updates are now enabled."
msgstr ""

#. Plugin Name of the plugin/theme
msgid "Advanced Custom Fields: Extended PRO"
msgstr ""

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.acf-extended.com"
msgstr ""

#. Description of the plugin/theme
msgid ""
"All-in-one enhancement suite that improves WordPress & Advanced Custom "
"Fields."
msgstr ""

#. Author of the plugin/theme
msgid "ACF Extended"
msgstr ""
