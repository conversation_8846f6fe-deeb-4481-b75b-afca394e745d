!function(t){"undefined"!=typeof acf&&"undefined"!=typeof acfe&&void 0!==window.wp.mce&&tinymce.PluginManager.add("acfe_form",function(n,e){window.wp.mce.views.register("acfe_form",{initialize:function(){t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax({action:"acfe/form/shortcode",args:this.shortcode.attrs.named}),type:"post",dataType:"html",context:this,beforeSend:function(){this.render('<div style="border:1px solid #ddd; padding:120px 25px; background:#f8f8f8; text-align:center;"></div>')},success:function(e){this.render(e,!0)}})},edit:function(e,t){n.windowManager.open({width:800,height:62,title:"Shortcode",body:[{label:"",name:"content",type:"textbox",value:e}],onsubmit:function(e){t(e.data.content)}})}})})}(jQuery);