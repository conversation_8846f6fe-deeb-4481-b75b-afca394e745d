<?php

if(!defined('ABSPATH')){
    exit;
}

if(!class_exists('acfe_module_post_type_field_groups')):

class acfe_module_post_type_field_groups{
    
    /**
     * construct
     */
    function __construct(){
        
        add_filter('acfe/module/register_field_groups/module=post_type', array($this, 'register_field_groups'), 10, 2);
        
    }
    
    
    /**
     * register_field_groups
     *
     * @param $field_groups
     * @param $module
     *
     * @return mixed
     */
    function register_field_groups($field_groups, $module){
        
        $field_groups[] = array(
            'key' => 'group_acfe_post_type',
            'title' => __('Post Type', 'acfe'),
    
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => $module->post_type,
                    ),
                ),
            ),
    
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'left',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => 1,
            'description' => '',
    
            'fields' => array(
                array(
                    'key' => 'field_tab_general',
                    'label' => 'General',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                        'data-no-preference' => true,
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_name',
                    'label' => 'Name',
                    'name' => 'name',
                    'type' => 'acfe_slug',
                    'instructions' => __('Post type key. Must not exceed 20 characters and may only contain lowercase alphanumeric characters, dashes, and underscores. See <a href="https://developer.wordpress.org/reference/functions/sanitize_key/" target="_blank">sanitize_key()</a>.', 'acfe'),
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 20,
                ),
                array(
                    'key' => 'field_description',
                    'label' => 'Description',
                    'name' => 'description',
                    'type' => 'text',
                    'instructions' => __('A short descriptive summary of what the post type is.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                ),
                array(
                    'key' => 'field_hierarchical',
                    'label' => 'Hierarchical',
                    'name' => 'hierarchical',
                    'type' => 'true_false',
                    'instructions' => __('Whether the post type is hierarchical (e.g. page). Allows Parent to be specified. The <code>supports</code> parameter should contain \'page-attributes\' to show the parent select box on the editor page. Default false.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_supports',
                    'label' => 'Supports',
                    'name' => 'supports',
                    'type' => 'checkbox',
                    'instructions' => __('Core feature(s) the post type supports. Serves as an alias for calling <a href="https://developer.wordpress.org/reference/functions/add_post_type_support/" target="_blank">add_post_type_support()</a> directly. Default is an array containing \'title\' and \'editor\'.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'title' => 'Title',
                        'editor' => 'Editor',
                        'author' => 'Author',
                        'thumbnail' => 'Thumbnail',
                        'excerpt' => 'Excerpt',
                        'trackbacks' => 'Trackbacks',
                        'custom-fields' => 'Custom fields',
                        'comments' => 'Comments',
                        'revisions' => 'Revisions',
                        'page-attributes' => 'Page attributes',
                        'post-formats' => 'Post formats',
                    ),
                    'allow_custom' => 1,
                    'save_custom' => 1,
                    'default_value' => array(),
                    'layout' => 'vertical',
                    'toggle' => 0,
                    'return_format' => 'value',
                ),
                array(
                    'key' => 'field_taxonomies',
                    'label' => 'Taxonomies',
                    'name' => 'taxonomies',
                    'type' => 'acfe_taxonomies',
                    'instructions' => __('An array of taxonomy identifiers that will be registered for the post type. Taxonomies can be registered later with <a href="https://developer.wordpress.org/reference/functions/register_taxonomy/" target="_blank">register_taxonomy()</a> or <a href="https://developer.wordpress.org/reference/functions/register_taxonomy_for_object_type/" target="_blank">register_taxonomy_for_object_type()</a>.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'field_type' => 'checkbox',
                    'return_format' => 'name',
                    'multiple' => 0,
                    'allow_null' => 0,
                ),
                array(
                    'key' => 'field_public',
                    'label' => 'Public',
                    'name' => 'public',
                    'type' => 'true_false',
                    'instructions' => __('Controls how the type is visible to authors (<code>show_in_nav_menus</code>, <code>show_ui</code>) and readers (<code>exclude_from_search</code>, <code>publicly_queryable</code>). Default: false.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_exclude_from_search',
                    'label' => 'Exclude from search',
                    'name' => 'exclude_from_search',
                    'type' => 'true_false',
                    'instructions' => __('Whether to exclude posts with this post type from front end search results. Default: value of the opposite of <code>public</code> argument', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_publicly_queryable',
                    'label' => 'Publicly queryable',
                    'name' => 'publicly_queryable',
                    'type' => 'true_false',
                    'instructions' => __('Whether queries can be performed on the front end as part of <code>parse_request()</code>. Default: value of <code>public</code> argument.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_can_export',
                    'label' => 'Can export',
                    'name' => 'can_export',
                    'type' => 'true_false',
                    'instructions' => __('Can this post_type be exported. Default: true.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_delete_with_user',
                    'label' => 'Delete with user',
                    'name' => 'delete_with_user',
                    'type' => 'radio',
                    'instructions' => __('Whether to delete posts of this type when deleting a user.<br/><br/>If true, posts of this type belonging to the user will be moved to trash when then user is deleted.<br/><br/>If false, posts of this type belonging to the user will not be trashed or deleted.<br/><br/>If not set (the default), posts are trashed if <code>post_type_supports(\'author\')</code>. Otherwise posts are not trashed or deleted. Default: null.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'null' => 'Null (default)',
                        'false' => 'False',
                        'true' => 'True',
                    ),
                    'default_value' => array(
                    ),
                    'allow_null' => 0,
                    'multiple' => 0,
                    'ui' => 0,
                    'return_format' => 'value',
                    'ajax' => 0,
                    'placeholder' => '',
                    'unparse_type' => true,
                ),
                array(
                    'key' => 'field_tab_menu',
                    'label' => 'Menu',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_menu_position',
                    'label' => 'Menu position',
                    'name' => 'menu_position',
                    'type' => 'number',
                    'instructions' => __('The position in the menu order the post type should appear. <code>show_in_menu</code> must be true. Default: null – defaults to below Comments.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'min' => 0,
                    'max' => '',
                    'step' => '',
                ),
                array(
                    'key' => 'field_menu_icon',
                    'label' => 'Menu icon',
                    'name' => 'menu_icon',
                    'type' => 'text',
                    'instructions' => __('The url to the icon to be used for this menu or the name of the icon from the iconfont (<a href="https://developer.wordpress.org/resource/dashicons/" target="_blank">Dashicons</a>). Default: null – defaults to the posts icon.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'dashicons-admin-post',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                ),
                array(
                    'key' => 'field_show_ui',
                    'label' => 'Show UI',
                    'name' => 'show_ui',
                    'type' => 'true_false',
                    'instructions' => __('Whether to generate a default UI for managing this post type in the admin. Default: value of <code>public</code> argument', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_show_in_menu',
                    'label' => 'Show in menu',
                    'name' => 'show_in_menu',
                    'type' => 'true_false',
                    'instructions' => __('Where to show the post type in the admin menu. <code>show_ui</code> must be true. Default: value of <code>show_ui</code> argument', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_show_in_menu_text',
                    'label' => 'Show in menu (text)',
                    'name' => 'show_in_menu_text',
                    'type' => 'text',
                    'instructions' => __('If an existing top level page such as <code>tools.php</code> or <code>edit.php?post_type=page</code>, the post type will be placed as a sub menu of that.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field'     => 'field_show_in_menu',
                                'operator'  => '==',
                                'value'     => '1',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'cleanup_key' => true,
                ),
                array(
                    'key' => 'field_show_in_nav_menus',
                    'label' => 'Show in nav menus',
                    'name' => 'show_in_nav_menus',
                    'type' => 'true_false',
                    'instructions' => __('Whether post_type is available for selection in navigation menus. Default: value of <code>public</code> argument', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_show_in_admin_bar',
                    'label' => 'Show in admin bar',
                    'name' => 'show_in_admin_bar',
                    'type' => 'true_false',
                    'instructions' => __('Whether to make this post type available in the WordPress admin bar. Default: value of <code>show_in_menu</code> argument', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_tab_archive',
                    'label' => 'Archive',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_acfe_archive_template',
                    'label' => 'Template',
                    'name' => 'acfe_archive_template',
                    'type' => 'text',
                    'instructions' => __('Which template file to load for the archive query. Default: <a href="https://developer.wordpress.org/themes/basics/template-hierarchy/" target="_blank">Template hierarchy</a>', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'my-template.php',
                    'prepend' => trailingslashit(acfe_get_setting('theme_folder')),
                    'append' => '',
                    'maxlength' => '',
                ),
                array(
                    'key' => 'field_has_archive',
                    'label' => 'Has archive',
                    'name' => 'has_archive',
                    'type' => 'true_false',
                    'instructions' => __('Enables post type archives. Default: false', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_has_archive_slug',
                    'label' => 'Slug',
                    'name' => 'has_archive_slug',
                    'type' => 'text',
                    'instructions' => __('Will use <code>name</code> as archive slug by default.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_has_archive',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Default',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'cleanup_key' => true,
                ),
                array(
                    'key' => 'field_acfe_archive_ppp',
                    'label' => 'Posts per page',
                    'name' => 'acfe_archive_ppp',
                    'type' => 'number',
                    'instructions' => __('Number of posts to display in the archive page. Default: 10', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 10,
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'min' => -1,
                    'max' => '',
                    'step' => '',
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_has_archive',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                ),
                array(
                    'key' => 'field_acfe_archive_orderby',
                    'label' => 'Order by',
                    'name' => 'acfe_archive_orderby',
                    'type' => 'text',
                    'instructions' => __('Sort retrieved posts by parameter in the archive page. Default: date (<code>post_date</code>).', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'date',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_has_archive',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                ),
                array(
                    'key' => 'field_acfe_archive_order',
                    'label' => 'Order',
                    'name' => 'acfe_archive_order',
                    'type' => 'select',
                    'instructions' => __('Designates the ascending or descending order of the <code>orderby</code> parameter in the archive page. Default: DESC.', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'ASC' => 'ASC',
                        'DESC' => 'DESC',
                    ),
                    'default_value' => array(
                        0 => 'DESC',
                    ),
                    'allow_null' => 0,
                    'multiple' => 0,
                    'ui' => 0,
                    'return_format' => 'value',
                    'ajax' => 0,
                    'placeholder' => '',
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_has_archive',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                ),
                array(
                    'key' => 'field_acfe_archive_meta_key',
                    'label' => 'Meta key',
                    'name' => 'acfe_archive_meta_key',
                    'type' => 'text',
                    'instructions' => __('Custom field used for the <code>orderby</code> parameter in the archive page', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_has_archive',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                ),
                array(
                    'key' => 'field_acfe_archive_meta_type',
                    'label' => 'Meta type',
                    'name' => 'acfe_archive_meta_type',
                    'type' => 'text',
                    'instructions' => __('Custom field type (NUMERIC, BINARY, CHAR, DATE, DATETIME, DECIMAL, SIGNED, TIME, UNSIGNED) used for the <code>orderby</code> parameter in the archive page', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_has_archive',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                ),
                array(
                    'key' => 'field_tab_single',
                    'label' => 'Single',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_acfe_single_template',
                    'label' => 'Template',
                    'name' => 'acfe_single_template',
                    'type' => 'text',
                    'instructions' => __('Which template file to load for the single query. Default: <a href="https://developer.wordpress.org/themes/basics/template-hierarchy/" target="_blank">Template hierarchy</a>' ,'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'my-template.php',
                    'prepend' => trailingslashit(acfe_get_setting('theme_folder')),
                    'append' => '',
                    'maxlength' => '',
                ),
                array(
                    'key' => 'field_rewrite',
                    'label' => 'Rewrite',
                    'name' => 'rewrite',
                    'type' => 'true_false',
                    'instructions' => __('Triggers the handling of rewrites for this post type. To prevent rewrites, set to false. Default: true and use <code>name</code> as slug'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_rewrite_args_select',
                    'label' => 'Rewrite Arguments',
                    'name' => 'rewrite_args_select',
                    'type' => 'true_false',
                    'instructions' => 'Use additional rewrite arguments',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_rewrite',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                    'cleanup_key' => true,
                ),
                array(
                    'key' => 'field_rewrite_args',
                    'label' => 'Rewrite Arguments',
                    'name' => 'rewrite_args',
                    'type' => 'group',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_rewrite',
                                'operator' => '==',
                                'value' => '1',
                            ),
                            array(
                                'field' => 'field_rewrite_args_select',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'layout' => 'row',
                    'cleanup_key' => true,
                    'sub_fields' => array(
                        array(
                            'key' => 'field_slug',
                            'label' => 'Slug',
                            'name' => 'slug',
                            'type' => 'text',
                            'instructions' => __('Customize the permalink structure slug. Defaults to the <code>name</code> value. Should be translatable.', 'acfe'),
                            'required' => 0,
                            'conditional_logic' => array(
                                array(
                                    array(
                                        'field' => 'field_rewrite_args_select',
                                        'operator' => '==',
                                        'value' => '1',
                                    ),
                                ),
                            ),
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                                'data-instruction-placement' => 'field',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Default',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_with_front',
                            'label' => 'With front',
                            'name' => 'with_front',
                            'type' => 'true_false',
                            'instructions' => __('Should the permalink structure be prepended with the front base. (example: if your permalink structure is <code>/blog/</code>, then your links will be: false-><code>/news/</code>, true-><code>/blog/news/</code>). Defaults to true.' ,'acfe'),
                            'required' => 0,
                            'conditional_logic' => array(
                                array(
                                    array(
                                        'field' => 'field_rewrite_args_select',
                                        'operator' => '==',
                                        'value' => '1',
                                    ),
                                ),
                            ),
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                                'data-instruction-placement' => 'field',
                            ),
                            'message' => '',
                            'default_value' => 1,
                            'ui' => 1,
                            'ui_on_text' => '',
                            'ui_off_text' => '',
                        ),
                        array(
                            'key' => 'field_feeds',
                            'label' => 'Feeds',
                            'name' => 'feeds',
                            'type' => 'true_false',
                            'instructions' => __('Should a feed permalink structure be built for this post type. Defaults to <code>has_archive</code> value.', 'acfe'),
                            'required' => 0,
                            'conditional_logic' => array(
                                array(
                                    array(
                                        'field' => 'field_rewrite_args_select',
                                        'operator' => '==',
                                        'value' => '1',
                                    ),
                                ),
                            ),
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                                'data-instruction-placement' => 'field',
                            ),
                            'message' => '',
                            'default_value' => 1,
                            'ui' => 1,
                            'ui_on_text' => '',
                            'ui_off_text' => '',
                        ),
                        array(
                            'key' => 'field_pages',
                            'label' => 'Pages',
                            'name' => 'pages',
                            'type' => 'true_false',
                            'instructions' => __('Should the permalink structure provide for pagination. Defaults to true.', 'acfe'),
                            'required' => 0,
                            'conditional_logic' => array(
                                array(
                                    array(
                                        'field' => 'field_rewrite_args_select',
                                        'operator' => '==',
                                        'value' => '1',
                                    ),
                                ),
                            ),
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                                'data-instruction-placement' => 'field',
                            ),
                            'message' => '',
                            'default_value' => 1,
                            'ui' => 1,
                            'ui_on_text' => '',
                            'ui_off_text' => '',
                        ),
                    ),
                ),
                array(
                    'key' => 'field_tab_admin',
                    'label' => 'Admin',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_acfe_admin_archive',
                    'label' => 'Archive Page',
                    'name' => 'acfe_admin_archive',
                    'type' => 'true_false',
                    'instructions' => __('Add an "Archive" Options Page as submenu of the post type.', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_acfe_admin_ppp',
                    'label' => 'Posts per page',
                    'name' => 'acfe_admin_ppp',
                    'type' => 'number',
                    'instructions' => __('Number of posts to display on the admin list screen. Default: 10', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 10,
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'min' => -1,
                    'max' => '',
                    'step' => '',
                ),
                array(
                    'key' => 'field_acfe_admin_orderby',
                    'label' => 'Order by',
                    'name' => 'acfe_admin_orderby',
                    'type' => 'text',
                    'instructions' => __('Sort retrieved posts by parameter in the admin list screen. Default: date (<code>post_date</code>).' ,'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'date',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                ),
                array(
                    'key' => 'field_acfe_admin_order',
                    'label' => 'Order',
                    'name' => 'acfe_admin_order',
                    'type' => 'select',
                    'instructions' => __('Designates the ascending or descending order of the <code>orderby</code> parameter in the admin list screen. Default: DESC.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'ASC' => 'ASC',
                        'DESC' => 'DESC',
                    ),
                    'default_value' => array(
                        0 => 'DESC',
                    ),
                    'allow_null' => 0,
                    'multiple' => 0,
                    'ui' => 0,
                    'return_format' => 'value',
                    'ajax' => 0,
                    'placeholder' => '',
                ),
                array(
                    'key' => 'field_acfe_admin_meta_key',
                    'label' => 'Meta key',
                    'name' => 'acfe_admin_meta_key',
                    'type' => 'text',
                    'instructions' => __('Custom field used for the <code>orderby</code> parameter in the admin list screen', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'conditional_logic' => array(),
                ),
                array(
                    'key' => 'field_acfe_admin_meta_type',
                    'label' => 'Meta type',
                    'name' => 'acfe_admin_meta_type',
                    'type' => 'text',
                    'instructions' => __('Custom field type (NUMERIC, BINARY, CHAR, DATE, DATETIME, DECIMAL, SIGNED, TIME, UNSIGNED) used for the <code>orderby</code> parameter in the admin list screen', 'acfe'),
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                    'conditional_logic' => array(),
                ),
                array(
                    'key' => 'field_tab_labels',
                    'label' => 'Labels',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_labels',
                    'label' => 'Labels',
                    'name' => 'labels',
                    'type' => 'group',
                    'instructions' => 'An array of labels for this post type. By default, post labels are used for non-hierarchical post types and page labels for hierarchical ones.<br /><br />
Default: if empty, <code>name</code> is set to value of <code>label</code>, and <code>singular_name</code> is set to value of <code>name</code>.',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'layout' => 'row',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_singular_name',
                            'label' => 'Singular name',
                            'name' => 'singular_name',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_add_new',
                            'label' => 'Add new',
                            'name' => 'add_new',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_add_new_item',
                            'label' => 'Add new item',
                            'name' => 'add_new_item',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_edit_item',
                            'label' => 'Edit item',
                            'name' => 'edit_item',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_new_item',
                            'label' => 'New item',
                            'name' => 'new_item',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_view_item',
                            'label' => 'View item',
                            'name' => 'view_item',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_view_items',
                            'label' => 'View items',
                            'name' => 'view_items',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_search_items',
                            'label' => 'Search items',
                            'name' => 'search_items',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_not_found',
                            'label' => 'Not found',
                            'name' => 'not_found',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_not_found_in_trash',
                            'label' => 'Not found in trash',
                            'name' => 'not_found_in_trash',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_parent_item_colon',
                            'label' => 'Parent item colon',
                            'name' => 'parent_item_colon',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_all_items',
                            'label' => 'All items',
                            'name' => 'all_items',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_archives',
                            'label' => 'Archives',
                            'name' => 'archives',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_attributes',
                            'label' => 'Attributes',
                            'name' => 'attributes',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_insert_into_item',
                            'label' => 'Insert into item',
                            'name' => 'insert_into_item',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_uploaded_to_this_item',
                            'label' => 'Uploaded to this item',
                            'name' => 'uploaded_to_this_item',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_featured_image',
                            'label' => 'Featured image',
                            'name' => 'featured_image',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_set_featured_image',
                            'label' => 'Set featured image',
                            'name' => 'set_featured_image',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_remove_featured_image',
                            'label' => 'Remove featured image',
                            'name' => 'remove_featured_image',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_use_featured_image',
                            'label' => 'Use featured image',
                            'name' => 'use_featured_image',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_menu_name',
                            'label' => 'Menu name',
                            'name' => 'menu_name',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_filter_items_list',
                            'label' => 'Filter items list',
                            'name' => 'filter_items_list',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_items_list_navigation',
                            'label' => 'Items list navigation',
                            'name' => 'items_list_navigation',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_items_list',
                            'label' => 'Items list',
                            'name' => 'items_list',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_name_admin_bar',
                            'label' => 'Name admin bar',
                            'name' => 'name_admin_bar',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_item_published',
                            'label' => 'Item published',
                            'name' => 'item_published',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_item_published_privately',
                            'label' => 'Item published privately',
                            'name' => 'item_published_privately',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_item_reverted_to_draft',
                            'label' => 'Item reverted to draft',
                            'name' => 'item_reverted_to_draft',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_item_scheduled',
                            'label' => 'Item scheduled',
                            'name' => 'item_scheduled',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_item_updated',
                            'label' => 'Item updated',
                            'name' => 'item_updated',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                        array(
                            'key' => 'field_enter_title',
                            'label' => 'Enter Title',
                            'name' => 'enter_title',
                            'type' => 'text',
                            'instructions' => '',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => '',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => '',
                        ),
                    ),
                ),
                array(
                    'key' => 'field_tab_capability',
                    'label' => 'Capability',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_capability_type',
                    'label' => 'Capability type',
                    'name' => 'capability_type',
                    'type' => 'textarea',
                    'instructions' => __('The string to use to build the read, edit, and delete capabilities.<br />May be passed as an array to allow for alternative plurals when using this argument as a base to construct the capabilities, like this:<br /><br />story<br />stories', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'post',
                    'placeholder' => '',
                    'maxlength' => '',
                    'rows' => '',
                    'new_lines' => '',
                    'encode_value' => true,
                ),
                array(
                    'key' => 'field_capabilities',
                    'label' => 'Capabilities',
                    'name' => 'capabilities',
                    'type' => 'textarea',
                    'instructions' => __('An array of the capabilities for this post type. Specify capabilities like this:<br /><br />edit_post : edit_book<br />read_post : read_book<br />delete_post : delete_book<br />edit_posts : edit_books<br />publish_posts : publish_books<br />etc...', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'maxlength' => '',
                    'rows' => '',
                    'new_lines' => '',
                    'encode_value' => true,
                ),
                array(
                    'key' => 'field_map_meta_cap',
                    'label' => 'Map meta cap',
                    'name' => 'map_meta_cap',
                    'type' => 'select',
                    'instructions' => __('Whether to use the internal default meta capability handling. Default: null.<br/><br/>If set it to false then standard admin role can\'t edit the posts types. Then the <code>edit_post</code> capability must be added to all roles to add or edit the posts types.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'null' => 'Null (default)',
                        'false' => 'False',
                        'true' => 'True',
                    ),
                    'default_value' => array(
                        0 => 'null',
                    ),
                    'allow_null' => 0,
                    'multiple' => 0,
                    'ui' => 0,
                    'return_format' => 'value',
                    'ajax' => 0,
                    'placeholder' => '',
                    'unparse_type' => true,
                ),
                array(
                    'key' => 'field_tab_rest',
                    'label' => 'REST',
                    'name' => '',
                    'type' => 'tab',
                    'instructions' => '',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'placement' => 'top',
                    'endpoint' => 0,
                ),
                array(
                    'key' => 'field_show_in_rest',
                    'label' => 'Show in rest',
                    'name' => 'show_in_rest',
                    'type' => 'true_false',
                    'instructions' => __('Whether to expose this post type in the REST API. Must be true to enable the Gutenberg editor. Default: false.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => '',
                    'ui_off_text' => '',
                ),
                array(
                    'key' => 'field_rest_base',
                    'label' => 'Rest base',
                    'name' => 'rest_base',
                    'type' => 'text',
                    'instructions' => __('The base slug that this post type will use when accessed using the REST API: Default: <code>name</code>.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                ),
                array(
                    'key' => 'field_rest_controller_class',
                    'label' => 'Rest controller class',
                    'name' => 'rest_controller_class',
                    'type' => 'text',
                    'instructions' => __('An optional custom controller to use instead of <a href="https://developer.wordpress.org/reference/classes/wp_rest_posts_controller/" target="_blank">WP_REST_Posts_Controller</a>. Must be a subclass of <a href="https://developer.wordpress.org/reference/classes/wp_rest_controller/" target="_blank">WP_REST_Controller</a>. Default: <a href="https://developer.wordpress.org/reference/classes/wp_rest_posts_controller/" target="_blank">WP_REST_Posts_Controller</a>.', 'acfe'),
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'WP_REST_Posts_Controller',
                    'placeholder' => '',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => '',
                ),
            ),
        );
        
        return $field_groups;
        
    }
    
}

acf_new_instance('acfe_module_post_type_field_groups');

endif;