{"version": 3, "sources": ["webpack:///./node_modules/cropperjs/dist/cropper.css", "webpack:///input.scss", "webpack:///./assets/src/input.scss", "webpack:///./assets/src/input-legacy.scss"], "names": [], "mappings": "AAAA;;;;;;;;ECQE,CDAC,mBAGD,aAAc,CACd,WAAY,CACZ,aAAc,CACd,iBAAkB,CAElB,iBAAkB,CAClB,wBAAyB,CACzB,qBAAsB,CACtB,oBAAqB,CACrB,gBAAiB,CAClB,uBAGC,aAAc,CACd,WAAY,CACZ,sBAAuB,CACvB,0BAA2B,CAC3B,yBAA0B,CAC1B,uBAAwB,CACxB,sBAAuB,CACvB,UAAW,CACZ,qFAOC,QAAS,CACT,MAAO,CACP,iBAAkB,CAClB,OAAQ,CACR,KAAM,CACP,kCAIC,eAAgB,CACjB,kBAGC,qBAAsB,CACtB,SAAU,CACX,eAGC,qBAAsB,CACtB,WAAY,CACb,kBAGC,aAAc,CACd,WAAY,CACZ,sBAAuB,CACvB,mCAAuC,CACvC,eAAgB,CAChB,UAAW,CACZ,gBAGC,oBAAqB,CACrB,aAAc,CACd,WAAY,CACZ,iBAAkB,CACnB,yBAGC,uBAAwB,CACxB,oBAAqB,CACrB,qBAAsB,CACtB,MAAO,CACP,kBAAmB,CACnB,UAAW,CACZ,yBAGC,qBAAsB,CACtB,sBAAuB,CACvB,WAAY,CACZ,mBAAoB,CACpB,KAAM,CACN,oBAAqB,CACtB,gBAGC,aAAc,CACd,QAAS,CACT,QAAS,CACT,YAAa,CACb,iBAAkB,CAClB,OAAQ,CACR,OAAQ,CACT,+CAIC,qBAAsB,CACtB,WAAY,CACZ,aAAc,CACd,iBAAkB,CACnB,wBAGC,UAAW,CACX,SAAU,CACV,KAAM,CACN,SAAU,CACX,uBAGC,UAAW,CACX,MAAO,CACP,QAAS,CACT,SAAU,CACX,2CAKC,aAAc,CACd,WAAY,CACZ,WAAY,CACZ,iBAAkB,CAClB,UAAW,CACZ,cAGC,qBAAsB,CACtB,MAAO,CACP,KAAM,CACP,cAGC,qBAAsB,CACvB,qBAGC,gBAAiB,CACjB,UAAW,CACX,KAAM,CACN,SAAU,CACX,qBAGC,gBAAiB,CACjB,UAAW,CACX,MAAO,CACP,QAAS,CACV,qBAGC,gBAAiB,CACjB,SAAU,CACV,KAAM,CACN,SAAU,CACX,qBAGC,WAAY,CACZ,gBAAiB,CACjB,UAAW,CACX,MAAO,CACR,eAGC,qBAAsB,CACtB,UAAW,CACX,YAAa,CACb,SAAU,CACX,uBAGC,gBAAiB,CACjB,eAAgB,CAChB,UAAW,CACX,OAAQ,CACT,uBAGC,gBAAiB,CACjB,QAAS,CACT,gBAAiB,CACjB,QAAS,CACV,uBAGC,gBAAiB,CACjB,SAAU,CACV,eAAgB,CAChB,OAAQ,CACT,uBAGC,WAAY,CACZ,eAAgB,CAChB,QAAS,CACT,gBAAiB,CAClB,wBAGC,kBAAmB,CACnB,UAAW,CACX,QAAS,CACV,wBAGC,kBAAmB,CACnB,SAAU,CACV,QAAS,CACV,wBAGC,WAAY,CACZ,kBAAmB,CACnB,SAAU,CACX,wBAGC,WAAY,CACZ,kBAAmB,CACnB,WAAY,CACZ,SAAU,CACV,UAAW,CACX,UAAW,CACZ,0BAGC,wBACE,WAAY,CACZ,UAAW,CACZ,CAGH,0BACE,wBACE,WAAY,CACZ,UAAW,CACZ,CAGH,2BACE,wBACE,UAAW,CACX,YAAa,CACb,SAAU,CACX,CAGH,gCACE,qBAAsB,CACtB,WAAY,CACZ,WAAY,CACZ,aAAc,CACd,WAAY,CACZ,SAAU,CACV,iBAAkB,CAClB,UAAW,CACX,UAAW,CACZ,mBAGC,SAAU,CACX,YAGC,8QAA+Q,CAChR,cAGC,aAAc,CACd,QAAS,CACT,iBAAkB,CAClB,OAAQ,CACT,gBAGC,uBAAwB,CACzB,cAGC,WAAY,CACb,cAGC,gBAAiB,CAClB,qIAMC,kBAAmB,CE5SrB,mFAEQ,iBAAkB,CAF1B,8FAMQ,YAAa,CACb,UAAW,CACX,iBAAkB,CAClB,cAAe,CATvB,6FAYQ,aAAc,CACd,UAAW,CACX,iBAAkB,CAClB,cAAe,CAfvB,qGAmBQ,aAAc,CAnBtB,oGAsBQ,YAAa,CAtBrB,uFA0BQ,UAAW,CACX,WAAY,CACZ,aAAc,CACd,eAAgB,CAChB,eAAgB,CAChB,kBAAmB,CACnB,gBAAiB,CAhCzB,+FAoCQ,aAAc,CACd,mBAAoB,CArC5B,gGAyCQ,UAAW,CACd,sDA1CL,uFA8CY,UAAW,CACX,cAAe,CAClB,CAhDT,0FAwDQ,iBAAkB,CAClB,SAAU,CACV,WAAY,CACZ,mEAAoE,CAEpE,iBAAkB,CAClB,SAAU,CA9DlB,2GAkEQ,mBAAoB,CAEpB,kBAAmB,CACnB,SAAU,CArElB,6FAyEQ,aAAc,CACd,QAAS,CACT,SAAU,CA3ElB,gGA+EQ,gBAAiB,CA/EzB,mFAwFQ,iBAAkB,CAxF1B,yFA8FQ,UAAW,CACX,UAAW,CACX,aAAc,CAhGtB,qFAmGQ,QAAS,CAnGjB,+FAsGQ,iBAAkB,CAClB,UAAW,CAvGnB,mGA2GQ,cAAe,CACf,UAAW,CACX,WAAY,CACZ,aAAc,CACd,eAAgB,CAChB,eAAgB,CAChB,kBAAmB,CACnB,QAAS,CACT,SAAU,CAnHlB,gHAuHQ,gBAAiB,CACjB,eAAgB,CAxHxB,4GA2HQ,YAAa,CA3HrB,kHA8HQ,YAAa,CA9HrB,gGAiIQ,UAAW,CAjInB,+GAoIQ,WAAY,CApIpB,iEAwIQ,eAAgB,CAxIxB,oEA4IQ,UAAW,CACX,WAAY,CACZ,UAAW,CACX,2BAA4B,CAC5B,iCAAkC,CAClC,giBAAiiB,CAjJziB,qJAsJQ,giBAAiiB,CAtJziB,6EA0JQ,UAAW,CACX,WAAY,CACZ,UAAW,CACX,2BAA4B,CAC5B,iCAAkC,CAClC,4RAA6R,CA/JrS,uKAoKQ,4RAA6R,CAChS,sCAID,cAAe,CACf,cAAe,CACf,KAAM,CACN,MAAO,CACP,OAAQ,CACR,QAAS,CACT,gCAAoC,CACpC,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CACtB,mCAGG,eAAgB,CAChB,cAAe,CACf,sBAAuB,CACvB,cAAe,CAClB,2CAGG,cAAe,CAEf,qCAHJ,2CAIQ,eAAgB,CAEvB,CAED,sFAEQ,UAAW,CACX,YAAa,CACb,sBAAuB,CAJ/B,4EAQQ,aAAc,CACd,2CAA4C,CAC5C,cAAe,CAVvB,8EAcQ,iBAAkB,CAClB,WAAY,CACZ,wBAAyB,CACzB,YAAa,CACb,kBAAmB,CAlB3B,mFAsBQ,+HACgE,CAChE,gBAAiB,CACjB,cAAe,CACf,aAAc,CACd,gBAAiB,CA3BzB,6EA+BQ,eAAgB,CAChB,WAAY,CACZ,wBAAyB,CACzB,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAC9B,iBAAkB,CAClB,kBAAmB,CAtC3B,8EA6CQ,YAAa,CACb,kBAAmB,CACtB,8BAGG,KACI,yBAA0B,EAJjC,sBAGG,KACI,yBAA0B,EAnDtC,mFAwDQ,WAAY,CACZ,UAAW,CACX,wBAAyB,CACzB,iBAAkB,CAClB,iBAAkB,CAClB,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CACnB,eAAgB,CAChB,aAAc,CAjEtB,uFAqEQ,+CAAW,CAAX,uCAAwC,CArEhD,mFAyEQ,+HACgE,CAChE,cAAe,CACf,aAAc,CACd,kBAAmB,CA7E3B,4EAiFQ,YAAa,CACb,kBAAmB,CACnB,aAAc,CAnFtB,iFAuFQ,iBAAkB,CAvF1B,iFA2FQ,+HACgE,CAChE,cAAe,CACf,eAAgB,CAChB,aAAc,CACd,kBAAmB,CAhG3B,qFAoGQ,kBAAmB,CACnB,YAAa,CArGrB,oFAyGQ,gBAAiB,CAzGzB,oFA6GQ,WAAY,CACZ,UAAW,CACX,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CACnB,QAAS,CACT,QAAS,CACT,SAAU,CACV,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAChB,4BAA6B,CAC7B,iBAAkB,CAClB,OAAQ,CACR,cAAe,CAzHvB,uMA8HQ,YAAa,CA9HrB,qDAkIQ,uBAAwB,CAlIhC,iDAsIQ,mBAAoB,CAtI5B,kDA0IQ,sBAAuB,CACvB,iBAAkB,CAClB,SAAU,CACV,kBAAmB,CA7I3B,mDAiJQ,kBAAmB,CAjJ3B,iDAqJQ,mBAAoB,CACpB,4BAA6B,CAChC,2BAvJL,2DA2JY,SAAU,CACb,CA5JT,sEAgKQ,mBAAoB,CACpB,kBAAmB,CAjK3B,mFAmKY,YAAa,CAnKzB,gFAsKY,YAAa,CACb,mDAAoD,CAvKhE,0EA4KQ,UAAW,CACX,gBAAiB,CACjB,YAAa,CACb,yCAA0C,CA/KlD,2DAmLQ,SAAU,CACV,UAAW,CApLnB,yDAwLQ,aAAc,CACd,oBAAqB,CACrB,cAAe,CACf,sBAAuB,CACvB,eAAgB,CAChB,QAAS,CACT,cAAe,CACf,cAAe,CACf,gBAAiB,CACjB,kBAAmB,CACnB,uBAAwB,CACxB,iBAAkB,CAClB,kBAAmB,CACnB,qBAAsB,CACtB,aAAc,CACd,0CAA2C,CAC3C,oBAAqB,CACrB,iDAAkD,CAClD,kBAAmB,CACnB,kBAAmB,CA3M3B,kEA6MY,wBAAyB,CACzB,6BAA8B,CAC9B,4BAA6B,CAC7B,0BAA2B,CAC3B,2BAA4B,CAC5B,cAAe,CAlN3B,+DAqNY,kBAAmB,CACnB,aAAc,CACd,oDAAqD,CACrD,oBAAqB,CACrB,2DAA4D,CAzNxE,yDA6NQ,aAAc,CACd,oBAAqB,CACrB,cAAe,CACf,sBAAuB,CACvB,eAAgB,CAChB,QAAS,CACT,cAAe,CACf,cAAe,CACf,gBAAiB,CACjB,kBAAmB,CACnB,uBAAwB,CACxB,iBAAkB,CAClB,kBAAmB,CACnB,qBAAsB,CACtB,kBAAmB,CACnB,kBAAmB,CACnB,+CAAgD,CAChD,oBAAqB,CACrB,iDAAkD,CAClD,UAAW,CACX,oBAAqB,CACrB,gBAAiB,CAlPzB,+DAoPY,kBAAmB,CACnB,yDAA0D,CAC1D,oBAAqB,CACrB,2DAA4D,CAC5D,UAAW,CAxPvB,kEA2PY,wBAAyB,CACzB,6BAA8B,CAC9B,4BAA6B,CAC7B,0BAA2B,CAC3B,2BAA4B,CAC5B,cAAe,CAhQ3B,sDAqQQ,kBAAmB,CACnB,qBAAsB,CACtB,cAAe,CACf,sBAAuB,CACvB,eAAgB,CAChB,kBAAmB,CACnB,QAAS,CACT,SAAU,CACV,eAAgB,CAChB,QAAS,CACT,eAAgB,CAChB,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,aAAc,CACd,0CAA2C,CAC3C,oBAAqB,CACrB,iBAAkB,CAClB,kBAAmB,CACnB,iBAAkB,CAClB,kBAAmB,CAzR3B,4DA2RY,aAAc,CACd,oDAAqD,CA5RjE,+DA+RY,aAAc,CA/R1B,yLAsSY,YAAa,CAChB,wSCteO,oBAAqB,CACrB,oBAAqB,CACrB,cAAe,CACf,gBAAiB,CACjB,WAAY,CACZ,kBAAmB,CACnB,cAAe,CACf,gBAAiB,CACjB,kBAAmB,CACnB,uBAAwB,CACxB,iBAAkB,CAClB,kBAAmB,CACnB,qBAAsB,CAGtB,kBAAmB,CACnB,oCAAqC,CACrC,0BAA2B,CAC3B,UAAW,CACX,oBAAqB,CACrB,wFACyC,CA5BzD,goBAiCoB,kBAAmB,CACnB,oBAAqB,CACrB,UAAW,CAnC/B,gUAuCoB,+CAAgD,CAvCpE,wSA4CgB,oBAAqB,CACrB,oBAAqB,CACrB,cAAe,CACf,gBAAiB,CACjB,WAAY,CACZ,kBAAmB,CACnB,cAAe,CACf,gBAAiB,CACjB,kBAAmB,CACnB,uBAAwB,CACxB,iBAAkB,CAClB,kBAAmB,CACnB,qBAAsB,CAEtB,UAAW,CACX,iBAAkB,CAClB,kBAAmB,CACnB,uBAAwB,CACxB,kBAAmB,CA9DnC,g8BAoEoB,kBAAmB,CACnB,iBAAkB,CAClB,aAAc,CAtElC,goBA4EoB,oBAAqB,CACrB,sCAA0C,CA7E9D,4RAkFgB,QAAS,CACT,SAAU,CACV,eAAgB,CAChB,QAAS,CACT,eAAgB,CAChB,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,aAAc,CACd,6CAA8C,CAC9C,yBAA0B,CAC1B,sCAAuC,CA7FvD,4SA+FoB,YAAa,CA/FjC,4mBAmGoB,aAAc,CAnGlC,4oBAqGwB,YAAa", "file": "input-style.css", "sourcesContent": ["/*!\n * Cropper.js v1.5.12\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2021-06-12T08:00:11.623Z\n */\n\n.cropper-container {\n  direction: ltr;\n  font-size: 0;\n  line-height: 0;\n  position: relative;\n  -ms-touch-action: none;\n  touch-action: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.cropper-container img {\n  display: block;\n  height: 100%;\n  image-orientation: 0deg;\n  max-height: none !important;\n  max-width: none !important;\n  min-height: 0 !important;\n  min-width: 0 !important;\n  width: 100%;\n}\n\n.cropper-wrap-box,\n.cropper-canvas,\n.cropper-drag-box,\n.cropper-crop-box,\n.cropper-modal {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.cropper-wrap-box,\n.cropper-canvas {\n  overflow: hidden;\n}\n\n.cropper-drag-box {\n  background-color: #fff;\n  opacity: 0;\n}\n\n.cropper-modal {\n  background-color: #000;\n  opacity: 0.5;\n}\n\n.cropper-view-box {\n  display: block;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  overflow: hidden;\n  width: 100%;\n}\n\n.cropper-dashed {\n  border: 0 dashed #eee;\n  display: block;\n  opacity: 0.5;\n  position: absolute;\n}\n\n.cropper-dashed.dashed-h {\n  border-bottom-width: 1px;\n  border-top-width: 1px;\n  height: calc(100% / 3);\n  left: 0;\n  top: calc(100% / 3);\n  width: 100%;\n}\n\n.cropper-dashed.dashed-v {\n  border-left-width: 1px;\n  border-right-width: 1px;\n  height: 100%;\n  left: calc(100% / 3);\n  top: 0;\n  width: calc(100% / 3);\n}\n\n.cropper-center {\n  display: block;\n  height: 0;\n  left: 50%;\n  opacity: 0.75;\n  position: absolute;\n  top: 50%;\n  width: 0;\n}\n\n.cropper-center::before,\n.cropper-center::after {\n  background-color: #eee;\n  content: ' ';\n  display: block;\n  position: absolute;\n}\n\n.cropper-center::before {\n  height: 1px;\n  left: -3px;\n  top: 0;\n  width: 7px;\n}\n\n.cropper-center::after {\n  height: 7px;\n  left: 0;\n  top: -3px;\n  width: 1px;\n}\n\n.cropper-face,\n.cropper-line,\n.cropper-point {\n  display: block;\n  height: 100%;\n  opacity: 0.1;\n  position: absolute;\n  width: 100%;\n}\n\n.cropper-face {\n  background-color: #fff;\n  left: 0;\n  top: 0;\n}\n\n.cropper-line {\n  background-color: #39f;\n}\n\n.cropper-line.line-e {\n  cursor: ew-resize;\n  right: -3px;\n  top: 0;\n  width: 5px;\n}\n\n.cropper-line.line-n {\n  cursor: ns-resize;\n  height: 5px;\n  left: 0;\n  top: -3px;\n}\n\n.cropper-line.line-w {\n  cursor: ew-resize;\n  left: -3px;\n  top: 0;\n  width: 5px;\n}\n\n.cropper-line.line-s {\n  bottom: -3px;\n  cursor: ns-resize;\n  height: 5px;\n  left: 0;\n}\n\n.cropper-point {\n  background-color: #39f;\n  height: 5px;\n  opacity: 0.75;\n  width: 5px;\n}\n\n.cropper-point.point-e {\n  cursor: ew-resize;\n  margin-top: -3px;\n  right: -3px;\n  top: 50%;\n}\n\n.cropper-point.point-n {\n  cursor: ns-resize;\n  left: 50%;\n  margin-left: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-w {\n  cursor: ew-resize;\n  left: -3px;\n  margin-top: -3px;\n  top: 50%;\n}\n\n.cropper-point.point-s {\n  bottom: -3px;\n  cursor: s-resize;\n  left: 50%;\n  margin-left: -3px;\n}\n\n.cropper-point.point-ne {\n  cursor: nesw-resize;\n  right: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-nw {\n  cursor: nwse-resize;\n  left: -3px;\n  top: -3px;\n}\n\n.cropper-point.point-sw {\n  bottom: -3px;\n  cursor: nesw-resize;\n  left: -3px;\n}\n\n.cropper-point.point-se {\n  bottom: -3px;\n  cursor: nwse-resize;\n  height: 20px;\n  opacity: 1;\n  right: -3px;\n  width: 20px;\n}\n\n@media (min-width: 768px) {\n  .cropper-point.point-se {\n    height: 15px;\n    width: 15px;\n  }\n}\n\n@media (min-width: 992px) {\n  .cropper-point.point-se {\n    height: 10px;\n    width: 10px;\n  }\n}\n\n@media (min-width: 1200px) {\n  .cropper-point.point-se {\n    height: 5px;\n    opacity: 0.75;\n    width: 5px;\n  }\n}\n\n.cropper-point.point-se::before {\n  background-color: #39f;\n  bottom: -50%;\n  content: ' ';\n  display: block;\n  height: 200%;\n  opacity: 0;\n  position: absolute;\n  right: -50%;\n  width: 200%;\n}\n\n.cropper-invisible {\n  opacity: 0;\n}\n\n.cropper-bg {\n  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');\n}\n\n.cropper-hide {\n  display: block;\n  height: 0;\n  position: absolute;\n  width: 0;\n}\n\n.cropper-hidden {\n  display: none !important;\n}\n\n.cropper-move {\n  cursor: move;\n}\n\n.cropper-crop {\n  cursor: crosshair;\n}\n\n.cropper-disabled .cropper-drag-box,\n.cropper-disabled .cropper-face,\n.cropper-disabled .cropper-line,\n.cropper-disabled .cropper-point {\n  cursor: not-allowed;\n}\n", "/*!\n * Cropper.js v1.5.12\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2021-06-12T08:00:11.623Z\n */.cropper-container{direction:ltr;font-size:0;line-height:0;position:relative;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.cropper-container img{display:block;height:100%;image-orientation:0deg;max-height:none !important;max-width:none !important;min-height:0 !important;min-width:0 !important;width:100%}.cropper-wrap-box,.cropper-canvas,.cropper-drag-box,.cropper-crop-box,.cropper-modal{bottom:0;left:0;position:absolute;right:0;top:0}.cropper-wrap-box,.cropper-canvas{overflow:hidden}.cropper-drag-box{background-color:#fff;opacity:0}.cropper-modal{background-color:#000;opacity:0.5}.cropper-view-box{display:block;height:100%;outline:1px solid #39f;outline-color:rgba(51,153,255,0.75);overflow:hidden;width:100%}.cropper-dashed{border:0 dashed #eee;display:block;opacity:0.5;position:absolute}.cropper-dashed.dashed-h{border-bottom-width:1px;border-top-width:1px;height:calc(100% / 3);left:0;top:calc(100% / 3);width:100%}.cropper-dashed.dashed-v{border-left-width:1px;border-right-width:1px;height:100%;left:calc(100% / 3);top:0;width:calc(100% / 3)}.cropper-center{display:block;height:0;left:50%;opacity:0.75;position:absolute;top:50%;width:0}.cropper-center::before,.cropper-center::after{background-color:#eee;content:' ';display:block;position:absolute}.cropper-center::before{height:1px;left:-3px;top:0;width:7px}.cropper-center::after{height:7px;left:0;top:-3px;width:1px}.cropper-face,.cropper-line,.cropper-point{display:block;height:100%;opacity:0.1;position:absolute;width:100%}.cropper-face{background-color:#fff;left:0;top:0}.cropper-line{background-color:#39f}.cropper-line.line-e{cursor:ew-resize;right:-3px;top:0;width:5px}.cropper-line.line-n{cursor:ns-resize;height:5px;left:0;top:-3px}.cropper-line.line-w{cursor:ew-resize;left:-3px;top:0;width:5px}.cropper-line.line-s{bottom:-3px;cursor:ns-resize;height:5px;left:0}.cropper-point{background-color:#39f;height:5px;opacity:0.75;width:5px}.cropper-point.point-e{cursor:ew-resize;margin-top:-3px;right:-3px;top:50%}.cropper-point.point-n{cursor:ns-resize;left:50%;margin-left:-3px;top:-3px}.cropper-point.point-w{cursor:ew-resize;left:-3px;margin-top:-3px;top:50%}.cropper-point.point-s{bottom:-3px;cursor:s-resize;left:50%;margin-left:-3px}.cropper-point.point-ne{cursor:nesw-resize;right:-3px;top:-3px}.cropper-point.point-nw{cursor:nwse-resize;left:-3px;top:-3px}.cropper-point.point-sw{bottom:-3px;cursor:nesw-resize;left:-3px}.cropper-point.point-se{bottom:-3px;cursor:nwse-resize;height:20px;opacity:1;right:-3px;width:20px}@media (min-width: 768px){.cropper-point.point-se{height:15px;width:15px}}@media (min-width: 992px){.cropper-point.point-se{height:10px;width:10px}}@media (min-width: 1200px){.cropper-point.point-se{height:5px;opacity:0.75;width:5px}}.cropper-point.point-se::before{background-color:#39f;bottom:-50%;content:' ';display:block;height:200%;opacity:0;position:absolute;right:-50%;width:200%}.cropper-invisible{opacity:0}.cropper-bg{background-image:url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC\")}.cropper-hide{display:block;height:0;position:absolute;width:0}.cropper-hidden{display:none !important}.cropper-move{cursor:move}.cropper-crop{cursor:crosshair}.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point{cursor:not-allowed}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop{position:relative}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .has-image{display:none;float:left;position:relative;max-width:100%}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .no-image{display:block;float:left;position:relative;max-width:100%}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop.active .has-image{display:block}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop.active .no-image{display:none}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop img{width:100%;height:auto;display:block;min-width:150px;min-height:30px;background:#f1f1f1;margin:0 0 0 2px}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .no-image p{display:block;margin:0 !important}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop input.button{width:auto}@media screen and (-webkit-min-device-pixel-ratio: 0){.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop img{width:auto;max-width:100%}}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .hover{position:absolute;top:-11px;right:-11px;transition:opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;visibility:hidden;opacity:0}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .has-image:hover .hover{transition-delay:0s;visibility:visible;opacity:1}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .hover ul{display:block;margin:0;padding:0}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .hover ul li{margin:0 0 5px 0}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop{position:relative}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop:after{clear:both;content:'';display:table}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop p{margin:0}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .image-wrap{position:relative;float:left}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .image-wrap img{max-width:100%;width:auto;height:auto;display:block;min-width:150px;min-height:30px;background:#f1f1f1;margin:0;padding:0}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .image-wrap img[src$='.svg']{min-height:100px;min-width:100px}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .image-wrap .acf-actions{display:none}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop .image-wrap:hover .acf-actions{display:flex}.acf-field.acf-field-image-aspect-ratio-crop .acf-image-uploader-aspect-ratio-crop input.button{width:auto}.acf-field.acf-field-image-aspect-ratio-crop html[dir='rtl'] .acf-image-uploader-aspect-ratio-crop .image-wrap{float:right}.acf-field.acf-field-image-aspect-ratio-crop .acf-icon+.acf-icon{margin-left:4px}.acf-field.acf-field-image-aspect-ratio-crop .acf-icon.-crop:before{content:'';height:26px;width:26px;background-repeat:no-repeat;background-position:center center;background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cg fill='%23EEEEEE' fill-rule='evenodd' transform='translate%28.5%29'%3E%3Crect width='1.5' height='4' x='3' rx='.5'/%3E%3Crect width='1.5' height='3.5' x='10' y='12' rx='.5'/%3E%3Crect width='1.5' height='6.5' x='10' y='3' rx='.5'/%3E%3Crect width='12' height='1.5' x='3' y='11' rx='.5'/%3E%3Crect width='11' height='1.5' y='3' rx='.5'/%3E%3Crect width='1.5' height='6' x='3' y='6' rx='.5'/%3E%3C/g%3E%3C/svg%3E\")}.acf-field.acf-field-image-aspect-ratio-crop .acf-icon.-crop:hover:before,.acf-field.acf-field-image-aspect-ratio-crop .acf-icon.-crop:active:before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cg fill='%23238cc6' fill-rule='evenodd' transform='translate%28.5%29'%3E%3Crect width='1.5' height='4' x='3' rx='.5'/%3E%3Crect width='1.5' height='3.5' x='10' y='12' rx='.5'/%3E%3Crect width='1.5' height='6.5' x='10' y='3' rx='.5'/%3E%3Crect width='12' height='1.5' x='3' y='11' rx='.5'/%3E%3Crect width='11' height='1.5' y='3' rx='.5'/%3E%3Crect width='1.5' height='6' x='3' y='6' rx='.5'/%3E%3C/g%3E%3C/svg%3E\")}.acf-field.acf-field-image-aspect-ratio-crop .acf-icon.-cancel-custom:before{content:'';height:26px;width:26px;background-repeat:no-repeat;background-position:center center;background-image:url(\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd' stroke='%23f7efef' stroke-width='2'%3E%3Cpath d='M10.084 10.251 2 2M1.917 10.167l8.25-8.084'/%3E%3C/g%3E%3C/svg%3E%0A\")}.acf-field.acf-field-image-aspect-ratio-crop .acf-icon.-cancel-custom:hover:before,.acf-field.acf-field-image-aspect-ratio-crop .acf-icon.-cancel-custom:active:before{background-image:url(\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd' stroke='%23dc3232' stroke-width='2'%3E%3Cpath d='M10.084 10.251 2 2M1.917 10.167l8.25-8.084'/%3E%3C/g%3E%3C/svg%3E%0A\")}.acf-image-aspect-ratio-crop-backdrop{position:fixed;z-index:159900;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.7);display:flex;justify-content:center;align-items:center}.acf-image-aspect-ratio-crop-modal{max-height:100%;max-width:100%;background-color:white;z-index:159901}.acf-image-aspect-ratio-crop-modal-wrapper{max-width:100%}@media screen and (min-width: 600px){.acf-image-aspect-ratio-crop-modal-wrapper{min-width:600px}}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-image-container{width:100%;display:flex;justify-content:center}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-image{display:block;max-height:calc(100vh - 60px - 50px - 80px);max-width:100%}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-heading{position:relative;height:50px;background-color:#fcfcfc;display:flex;align-items:center}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-heading-text{font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;font-weight:bold;font-size:20px;color:#24282d;margin-left:16px}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-footer{overflow-x:auto;height:60px;background-color:#fcfcfc;display:flex;align-items:center;justify-content:space-between;padding-left:16px;padding-right:16px}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-loading{display:flex;align-items:center}@keyframes aiarc-spin{100%{transform:rotate(-360deg)}}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-loading-icon{height:16px;width:16px;background-color:#0085ba;border-radius:50%;margin-right:11px;display:flex;justify-content:center;align-items:center;margin-left:4px;flex-shrink:0}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-loading-icon svg{animation:aiarc-spin 2s linear infinite}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-loading-text{font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;font-size:14px;color:#555555;white-space:nowrap}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-error{display:flex;align-items:center;flex-shrink:1}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-error-icon{margin-right:10px}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-error-text{font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;font-size:14px;font-weight:700;color:#f44336;white-space:nowrap}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-footer-buttons{white-space:nowrap;display:flex}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-footer button{margin-left:16px}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-heading-close{height:50px;width:50px;display:flex;justify-content:center;align-items:center;border:0;margin:0;padding:0;appearance:none;background-color:transparent;position:absolute;right:0;cursor:pointer}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-heading-close:hover svg path,.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-heading-close:active svg path{fill:#00a0d2}.acf-image-aspect-ratio-crop-modal .cropper-view-box{outline:2px solid white}.acf-image-aspect-ratio-crop-modal .cropper-line{outline-color:white}.acf-image-aspect-ratio-crop-modal .cropper-point{background-color:white;border-radius:50%;opacity:1;transform:scale(2)}.acf-image-aspect-ratio-crop-modal .cropper-dashed{border-style:solid}.acf-image-aspect-ratio-crop-modal .cropper-line{outline:transparent;background-color:transparent}@media (min-width: 1200px){.acf-image-aspect-ratio-crop-modal .cropper-point.point-se{opacity:1}}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-reset{display:inline-flex;align-items:center}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-reset:disabled svg{fill:#828282}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-reset:hover svg{fill:#0071a1;fill:var(--wp-admin-theme-color-darker-10, #0071a1)}.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-reset svg{width:16px;margin-right:8px;fill:#0073aa;fill:var(--wp-admin-theme-color, #0073aa)}.acf-image-aspect-ratio-crop-modal .cropper-point.point-se{width:5px;height:5px}.acf-image-aspect-ratio-crop-modal .aiarc-button-default{display:block;text-decoration:none;font-size:13px;line-height:2.15384615;min-height:30px;margin:0;padding:0 10px;cursor:pointer;border-width:1px;border-style:solid;-webkit-appearance:none;border-radius:3px;white-space:nowrap;box-sizing:border-box;color:#0071a1;color:var(--wp-admin-theme-color, #0071a1);border-color:#0071a1;border-color:var(--wp-admin-theme-color, #0071a1);background:#f3f5f6;vertical-align:top}.acf-image-aspect-ratio-crop-modal .aiarc-button-default:disabled{color:#a0a5aa !important;background:#f7f7f7 !important;border-color:#ddd !important;box-shadow:none !important;text-shadow:none !important;cursor:default}.acf-image-aspect-ratio-crop-modal .aiarc-button-default:hover{background:#f1f1f1;color:#006ba1;color:var(--wp-admin-theme-color-darker-10, #006ba1);border-color:#006ba1;border-color:var(--wp-admin-theme-color-darker-10, #006ba1)}.acf-image-aspect-ratio-crop-modal .aiarc-button-primary{display:block;text-decoration:none;font-size:13px;line-height:2.15384615;min-height:30px;margin:0;padding:0 10px;cursor:pointer;border-width:1px;border-style:solid;-webkit-appearance:none;border-radius:3px;white-space:nowrap;box-sizing:border-box;vertical-align:top;background:#007cba;background:var(--wp-admin-theme-color, #007cba);border-color:#007cba;border-color:var(--wp-admin-theme-color, #007cba);color:#fff;text-decoration:none;text-shadow:none}.acf-image-aspect-ratio-crop-modal .aiarc-button-primary:hover{background:#006ba1;background:var(--wp-admin-theme-color-darker-10, #006ba1);border-color:#006ba1;border-color:var(--wp-admin-theme-color-darker-10, #006ba1);color:#fff}.acf-image-aspect-ratio-crop-modal .aiarc-button-primary:disabled{color:#a0a5aa !important;background:#f7f7f7 !important;border-color:#ddd !important;box-shadow:none !important;text-shadow:none !important;cursor:default}.acf-image-aspect-ratio-crop-modal .aiarc-button-link{white-space:nowrap;box-sizing:border-box;font-size:13px;line-height:2.15384615;min-height:30px;vertical-align:top;margin:0;padding:0;box-shadow:none;border:0;border-radius:0;background:0 0;cursor:pointer;text-align:left;color:#0073aa;color:var(--wp-admin-theme-color, #0073aa);text-decoration:none;padding-left:10px;padding-right:10px;margin-left:-10px;margin-right:-10px}.acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover{color:#006ba1;color:var(--wp-admin-theme-color-darker-10, #006ba1)}.acf-image-aspect-ratio-crop-modal .aiarc-button-link:disabled{color:#828282}.acf-image-aspect-ratio-crop-modal .aiarc-button:focus:not(:focus-visible),.acf-image-aspect-ratio-crop-modal .acf-image-aspect-ratio-crop-modal-heading-close:focus:not(:focus-visible){outline:none}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary{display:inline-block;text-decoration:none;font-size:13px;line-height:26px;height:28px;padding:0 10px 1px;cursor:pointer;border-width:1px;border-style:solid;-webkit-appearance:none;border-radius:3px;white-space:nowrap;box-sizing:border-box;background:#0085ba;border-color:#0073aa #006799 #006799;box-shadow:0 1px 0 #006799;color:#fff;text-decoration:none;text-shadow:0 -1px 1px #006799, 1px 0 1px #006799, 0 1px 1px #006799, -1px 0 1px #006799}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:hover,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:hover,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:hover,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:hover{background:#008ec2;border-color:#006799;color:#fff}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-primary:focus{box-shadow:0 1px 0 #0073aa, 0 0 2px 1px #33b3db}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-default,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-default,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-default,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-default{display:inline-block;text-decoration:none;font-size:13px;line-height:26px;height:28px;padding:0 10px 1px;cursor:pointer;border-width:1px;border-style:solid;-webkit-appearance:none;border-radius:3px;white-space:nowrap;box-sizing:border-box;color:#555;border-color:#ccc;background:#f7f7f7;box-shadow:0 1px 0 #ccc;vertical-align:top}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:hover,body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:hover,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:hover,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:hover,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus{background:#fafafa;border-color:#999;color:#23282d}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-default:focus{border-color:#5b9dd9;box-shadow:0 0 3px rgba(0,115,170,0.8)}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-link,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-link,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-link,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-link{margin:0;padding:0;box-shadow:none;border:0;border-radius:0;background:0 0;cursor:pointer;text-align:left;color:#0073aa;transition-property:border, background, color;transition-duration:0.05s;transition-timing-function:ease-in-out}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-link svg,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-link svg,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-link svg,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-link svg{fill:#0073aa}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover,body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active{color:#00a0d2}body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover svg,body.version-4-9 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active svg,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover svg,body.version-5-0 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active svg,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover svg,body.version-5-1 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active svg,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:hover svg,body.version-5-2 .acf-image-aspect-ratio-crop-modal .aiarc-button-link:active svg{fill:#00a0d2}\n", "@import '~cropperjs/dist/cropper';\n\n.acf-field.acf-field-image-aspect-ratio-crop {\n    .acf-image-uploader-aspect-ratio-crop {\n        position: relative;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop .has-image {\n        display: none;\n        float: left;\n        position: relative;\n        max-width: 100%;\n    }\n    .acf-image-uploader-aspect-ratio-crop .no-image {\n        display: block;\n        float: left;\n        position: relative;\n        max-width: 100%;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop.active .has-image {\n        display: block;\n    }\n    .acf-image-uploader-aspect-ratio-crop.active .no-image {\n        display: none;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop img {\n        width: 100%;\n        height: auto;\n        display: block;\n        min-width: 150px;\n        min-height: 30px;\n        background: #f1f1f1;\n        margin: 0 0 0 2px;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop .no-image p {\n        display: block;\n        margin: 0 !important;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop input.button {\n        width: auto;\n    }\n\n    @media screen and (-webkit-min-device-pixel-ratio: 0) {\n        .acf-image-uploader-aspect-ratio-crop img {\n            width: auto;\n            max-width: 100%;\n        }\n    }\n\n    /*\n    *  Hover\n    */\n\n    .acf-image-uploader-aspect-ratio-crop .hover {\n        position: absolute;\n        top: -11px;\n        right: -11px;\n        transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\n        visibility: hidden;\n        opacity: 0;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop .has-image:hover .hover {\n        transition-delay: 0s;\n\n        visibility: visible;\n        opacity: 1;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop .hover ul {\n        display: block;\n        margin: 0;\n        padding: 0;\n    }\n\n    .acf-image-uploader-aspect-ratio-crop .hover ul li {\n        margin: 0 0 5px 0;\n    }\n\n    /*--------------------------------------------------------------------------------------------\n    *\n    *\tImage\n    *\n    *--------------------------------------------------------------------------------------------*/\n    .acf-image-uploader-aspect-ratio-crop {\n        position: relative;\n        /* image wrap*/\n        /* input */\n        /* rtl */\n    }\n    .acf-image-uploader-aspect-ratio-crop:after {\n        clear: both;\n        content: '';\n        display: table;\n    }\n    .acf-image-uploader-aspect-ratio-crop p {\n        margin: 0;\n    }\n    .acf-image-uploader-aspect-ratio-crop .image-wrap {\n        position: relative;\n        float: left;\n        /* hover */\n    }\n    .acf-image-uploader-aspect-ratio-crop .image-wrap img {\n        max-width: 100%;\n        width: auto;\n        height: auto;\n        display: block;\n        min-width: 150px;\n        min-height: 30px;\n        background: #f1f1f1;\n        margin: 0;\n        padding: 0;\n        /* svg */\n    }\n    .acf-image-uploader-aspect-ratio-crop .image-wrap img[src$='.svg'] {\n        min-height: 100px;\n        min-width: 100px;\n    }\n    .acf-image-uploader-aspect-ratio-crop .image-wrap .acf-actions {\n        display: none;\n    }\n    .acf-image-uploader-aspect-ratio-crop .image-wrap:hover .acf-actions {\n        display: flex;\n    }\n    .acf-image-uploader-aspect-ratio-crop input.button {\n        width: auto;\n    }\n    html[dir='rtl'] .acf-image-uploader-aspect-ratio-crop .image-wrap {\n        float: right;\n    }\n\n    .acf-icon + .acf-icon {\n        margin-left: 4px;\n    }\n\n    .acf-icon.-crop:before {\n        content: '';\n        height: 26px;\n        width: 26px;\n        background-repeat: no-repeat;\n        background-position: center center;\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cg fill='%23EEEEEE' fill-rule='evenodd' transform='translate%28.5%29'%3E%3Crect width='1.5' height='4' x='3' rx='.5'/%3E%3Crect width='1.5' height='3.5' x='10' y='12' rx='.5'/%3E%3Crect width='1.5' height='6.5' x='10' y='3' rx='.5'/%3E%3Crect width='12' height='1.5' x='3' y='11' rx='.5'/%3E%3Crect width='11' height='1.5' y='3' rx='.5'/%3E%3Crect width='1.5' height='6' x='3' y='6' rx='.5'/%3E%3C/g%3E%3C/svg%3E\");\n    }\n\n    .acf-icon.-crop:hover:before,\n    .acf-icon.-crop:active:before {\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cg fill='%23238cc6' fill-rule='evenodd' transform='translate%28.5%29'%3E%3Crect width='1.5' height='4' x='3' rx='.5'/%3E%3Crect width='1.5' height='3.5' x='10' y='12' rx='.5'/%3E%3Crect width='1.5' height='6.5' x='10' y='3' rx='.5'/%3E%3Crect width='12' height='1.5' x='3' y='11' rx='.5'/%3E%3Crect width='11' height='1.5' y='3' rx='.5'/%3E%3Crect width='1.5' height='6' x='3' y='6' rx='.5'/%3E%3C/g%3E%3C/svg%3E\");\n    }\n\n    .acf-icon.-cancel-custom:before {\n        content: '';\n        height: 26px;\n        width: 26px;\n        background-repeat: no-repeat;\n        background-position: center center;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd' stroke='%23f7efef' stroke-width='2'%3E%3Cpath d='M10.084 10.251 2 2M1.917 10.167l8.25-8.084'/%3E%3C/g%3E%3C/svg%3E%0A\");\n    }\n\n    .acf-icon.-cancel-custom:hover:before,\n    .acf-icon.-cancel-custom:active:before {\n        background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd' stroke='%23dc3232' stroke-width='2'%3E%3Cpath d='M10.084 10.251 2 2M1.917 10.167l8.25-8.084'/%3E%3C/g%3E%3C/svg%3E%0A\");\n    }\n}\n\n.acf-image-aspect-ratio-crop-backdrop {\n    position: fixed;\n    z-index: 159900;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.7);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.acf-image-aspect-ratio-crop-modal {\n    max-height: 100%;\n    max-width: 100%;\n    background-color: white;\n    z-index: 159901;\n}\n\n.acf-image-aspect-ratio-crop-modal-wrapper {\n    max-width: 100%;\n\n    @media screen and (min-width: 600px) {\n        min-width: 600px;\n    }\n}\n\n.acf-image-aspect-ratio-crop-modal {\n    .acf-image-aspect-ratio-crop-modal-image-container {\n        width: 100%;\n        display: flex;\n        justify-content: center;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-image {\n        display: block;\n        max-height: calc(100vh - 60px - 50px - 80px);\n        max-width: 100%;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-heading {\n        position: relative;\n        height: 50px;\n        background-color: #fcfcfc;\n        display: flex;\n        align-items: center;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-heading-text {\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n            Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;\n        font-weight: bold;\n        font-size: 20px;\n        color: #24282d;\n        margin-left: 16px;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-footer {\n        overflow-x: auto;\n        height: 60px;\n        background-color: #fcfcfc;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding-left: 16px;\n        padding-right: 16px;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-footer-status {\n    }\n\n    .acf-image-aspect-ratio-crop-modal-loading {\n        display: flex;\n        align-items: center;\n    }\n\n    @keyframes aiarc-spin {\n        100% {\n            transform: rotate(-360deg);\n        }\n    }\n\n    .acf-image-aspect-ratio-crop-modal-loading-icon {\n        height: 16px;\n        width: 16px;\n        background-color: #0085ba;\n        border-radius: 50%;\n        margin-right: 11px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-left: 4px;\n        flex-shrink: 0;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-loading-icon svg {\n        animation: aiarc-spin 2s linear infinite;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-loading-text {\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n            Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;\n        font-size: 14px;\n        color: #555555;\n        white-space: nowrap;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-error {\n        display: flex;\n        align-items: center;\n        flex-shrink: 1;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-error-icon {\n        margin-right: 10px;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-error-text {\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n            Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;\n        font-size: 14px;\n        font-weight: 700;\n        color: #f44336;\n        white-space: nowrap;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-footer-buttons {\n        white-space: nowrap;\n        display: flex;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-footer button {\n        margin-left: 16px;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-heading-close {\n        height: 50px;\n        width: 50px;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        border: 0;\n        margin: 0;\n        padding: 0;\n        appearance: none;\n        background-color: transparent;\n        position: absolute;\n        right: 0;\n        cursor: pointer;\n    }\n\n    .acf-image-aspect-ratio-crop-modal-heading-close:hover svg path,\n    .acf-image-aspect-ratio-crop-modal-heading-close:active svg path {\n        fill: #00a0d2;\n    }\n\n    .cropper-view-box {\n        outline: 2px solid white;\n    }\n\n    .cropper-line {\n        outline-color: white;\n    }\n\n    .cropper-point {\n        background-color: white;\n        border-radius: 50%;\n        opacity: 1;\n        transform: scale(2);\n    }\n\n    .cropper-dashed {\n        border-style: solid;\n    }\n\n    .cropper-line {\n        outline: transparent;\n        background-color: transparent;\n    }\n\n    @media (min-width: 1200px) {\n        .cropper-point.point-se {\n            opacity: 1;\n        }\n    }\n\n    .acf-image-aspect-ratio-crop-reset {\n        display: inline-flex;\n        align-items: center;\n        &:disabled svg {\n            fill: #828282;\n        }\n        &:hover svg {\n            fill: #0071a1;\n            fill: var(--wp-admin-theme-color-darker-10, #0071a1);\n        }\n    }\n\n    .acf-image-aspect-ratio-crop-reset svg {\n        width: 16px;\n        margin-right: 8px;\n        fill: #0073aa;\n        fill: var(--wp-admin-theme-color, #0073aa);\n    }\n\n    .cropper-point.point-se {\n        width: 5px;\n        height: 5px;\n    }\n\n    .aiarc-button-default {\n        display: block;\n        text-decoration: none;\n        font-size: 13px;\n        line-height: 2.15384615;\n        min-height: 30px;\n        margin: 0;\n        padding: 0 10px;\n        cursor: pointer;\n        border-width: 1px;\n        border-style: solid;\n        -webkit-appearance: none;\n        border-radius: 3px;\n        white-space: nowrap;\n        box-sizing: border-box;\n        color: #0071a1;\n        color: var(--wp-admin-theme-color, #0071a1);\n        border-color: #0071a1;\n        border-color: var(--wp-admin-theme-color, #0071a1);\n        background: #f3f5f6;\n        vertical-align: top;\n        &:disabled {\n            color: #a0a5aa !important;\n            background: #f7f7f7 !important;\n            border-color: #ddd !important;\n            box-shadow: none !important;\n            text-shadow: none !important;\n            cursor: default;\n        }\n        &:hover {\n            background: #f1f1f1;\n            color: #006ba1;\n            color: var(--wp-admin-theme-color-darker-10, #006ba1);\n            border-color: #006ba1;\n            border-color: var(--wp-admin-theme-color-darker-10, #006ba1);\n        }\n    }\n    .aiarc-button-primary {\n        display: block;\n        text-decoration: none;\n        font-size: 13px;\n        line-height: 2.15384615;\n        min-height: 30px;\n        margin: 0;\n        padding: 0 10px;\n        cursor: pointer;\n        border-width: 1px;\n        border-style: solid;\n        -webkit-appearance: none;\n        border-radius: 3px;\n        white-space: nowrap;\n        box-sizing: border-box;\n        vertical-align: top;\n        background: #007cba;\n        background: var(--wp-admin-theme-color, #007cba);\n        border-color: #007cba;\n        border-color: var(--wp-admin-theme-color, #007cba);\n        color: #fff;\n        text-decoration: none;\n        text-shadow: none;\n        &:hover {\n            background: #006ba1;\n            background: var(--wp-admin-theme-color-darker-10, #006ba1);\n            border-color: #006ba1;\n            border-color: var(--wp-admin-theme-color-darker-10, #006ba1);\n            color: #fff;\n        }\n        &:disabled {\n            color: #a0a5aa !important;\n            background: #f7f7f7 !important;\n            border-color: #ddd !important;\n            box-shadow: none !important;\n            text-shadow: none !important;\n            cursor: default;\n        }\n    }\n\n    .aiarc-button-link {\n        white-space: nowrap;\n        box-sizing: border-box;\n        font-size: 13px;\n        line-height: 2.15384615;\n        min-height: 30px;\n        vertical-align: top;\n        margin: 0;\n        padding: 0;\n        box-shadow: none;\n        border: 0;\n        border-radius: 0;\n        background: 0 0;\n        cursor: pointer;\n        text-align: left;\n        color: #0073aa;\n        color: var(--wp-admin-theme-color, #0073aa);\n        text-decoration: none;\n        padding-left: 10px;\n        padding-right: 10px;\n        margin-left: -10px;\n        margin-right: -10px;\n        &:hover {\n            color: #006ba1;\n            color: var(--wp-admin-theme-color-darker-10, #006ba1);\n        }\n        &:disabled {\n            color: #828282;\n        }\n    }\n\n    .aiarc-button,\n    .acf-image-aspect-ratio-crop-modal-heading-close {\n        &:focus:not(:focus-visible) {\n            outline: none;\n        }\n    }\n}\n\n@import 'input-legacy';\n", "body {\n    &.version-4-9,\n    &.version-5-0,\n    &.version-5-1,\n    &.version-5-2 {\n        .acf-image-aspect-ratio-crop-modal {\n            .aiarc-button-primary {\n                display: inline-block;\n                text-decoration: none;\n                font-size: 13px;\n                line-height: 26px;\n                height: 28px;\n                padding: 0 10px 1px;\n                cursor: pointer;\n                border-width: 1px;\n                border-style: solid;\n                -webkit-appearance: none;\n                border-radius: 3px;\n                white-space: nowrap;\n                box-sizing: border-box;\n\n                /* Primary buttons styles */\n                background: #0085ba;\n                border-color: #0073aa #006799 #006799;\n                box-shadow: 0 1px 0 #006799;\n                color: #fff;\n                text-decoration: none;\n                text-shadow: 0 -1px 1px #006799, 1px 0 1px #006799,\n                    0 1px 1px #006799, -1px 0 1px #006799;\n\n                /* Primary buttons :hover styles */\n                &:focus,\n                &:hover {\n                    background: #008ec2;\n                    border-color: #006799;\n                    color: #fff;\n                }\n                /* Primary buttons :focus styles */\n                &:focus {\n                    box-shadow: 0 1px 0 #0073aa, 0 0 2px 1px #33b3db;\n                }\n            }\n\n            .aiarc-button-default {\n                display: inline-block;\n                text-decoration: none;\n                font-size: 13px;\n                line-height: 26px;\n                height: 28px;\n                padding: 0 10px 1px;\n                cursor: pointer;\n                border-width: 1px;\n                border-style: solid;\n                -webkit-appearance: none;\n                border-radius: 3px;\n                white-space: nowrap;\n                box-sizing: border-box;\n\n                color: #555;\n                border-color: #ccc;\n                background: #f7f7f7;\n                box-shadow: 0 1px 0 #ccc;\n                vertical-align: top;\n\n                /* Secondary buttons :hover styles */\n                &:focus,\n                &:hover,\n                &:focus {\n                    background: #fafafa;\n                    border-color: #999;\n                    color: #23282d;\n                }\n\n                /* Secondary buttons :focus styles */\n                &:focus,\n                &:focus {\n                    border-color: #5b9dd9;\n                    box-shadow: 0 0 3px rgba(0, 115, 170, 0.8);\n                }\n            }\n\n            .aiarc-button-link {\n                margin: 0;\n                padding: 0;\n                box-shadow: none;\n                border: 0;\n                border-radius: 0;\n                background: 0 0;\n                cursor: pointer;\n                text-align: left;\n                color: #0073aa;\n                transition-property: border, background, color;\n                transition-duration: 0.05s;\n                transition-timing-function: ease-in-out;\n                svg {\n                    fill: #0073aa;\n                }\n                &:hover,\n                &:active {\n                    color: #00a0d2;\n                    svg {\n                        fill: #00a0d2;\n                    }\n                }\n            }\n        }\n    }\n}\n"], "sourceRoot": ""}