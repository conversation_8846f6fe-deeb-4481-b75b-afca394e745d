{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./node_modules/axios/lib/utils.js", "webpack:///./node_modules/axios/index.js", "webpack:///./node_modules/qs/lib/index.js", "webpack:///./node_modules/get-intrinsic/index.js", "webpack:///./node_modules/function-bind/index.js", "webpack:///./node_modules/qs/lib/formats.js", "webpack:///./node_modules/axios/lib/helpers/bind.js", "webpack:///./node_modules/axios/lib/helpers/buildURL.js", "webpack:///./node_modules/axios/lib/cancel/isCancel.js", "webpack:///./node_modules/axios/lib/defaults.js", "webpack:///./node_modules/axios/lib/adapters/xhr.js", "webpack:///./node_modules/axios/lib/core/createError.js", "webpack:///./node_modules/axios/lib/core/mergeConfig.js", "webpack:///./node_modules/axios/lib/cancel/Cancel.js", "webpack:///./node_modules/qs/lib/utils.js", "webpack:///./node_modules/cropperjs/dist/cropper.js", "webpack:///./node_modules/sprintf-js/src/sprintf.js", "webpack:///./node_modules/axios/lib/axios.js", "webpack:///./node_modules/axios/lib/core/Axios.js", "webpack:///./node_modules/axios/lib/core/InterceptorManager.js", "webpack:///./node_modules/axios/lib/core/dispatchRequest.js", "webpack:///./node_modules/axios/lib/core/transformData.js", "webpack:///./node_modules/process/browser.js", "webpack:///./node_modules/axios/lib/helpers/normalizeHeaderName.js", "webpack:///./node_modules/axios/lib/core/settle.js", "webpack:///./node_modules/axios/lib/core/enhanceError.js", "webpack:///./node_modules/axios/lib/helpers/cookies.js", "webpack:///./node_modules/axios/lib/core/buildFullPath.js", "webpack:///./node_modules/axios/lib/helpers/isAbsoluteURL.js", "webpack:///./node_modules/axios/lib/helpers/combineURLs.js", "webpack:///./node_modules/axios/lib/helpers/parseHeaders.js", "webpack:///./node_modules/axios/lib/helpers/isURLSameOrigin.js", "webpack:///./node_modules/axios/lib/cancel/CancelToken.js", "webpack:///./node_modules/axios/lib/helpers/spread.js", "webpack:///./node_modules/qs/lib/stringify.js", "webpack:///./node_modules/side-channel/index.js", "webpack:///./node_modules/has-symbols/index.js", "webpack:///./node_modules/has-symbols/shams.js", "webpack:///./node_modules/function-bind/implementation.js", "webpack:///./node_modules/has/src/index.js", "webpack:///./node_modules/call-bind/callBound.js", "webpack:///./node_modules/call-bind/index.js", "webpack:///./node_modules/object-inspect/index.js", "webpack:///./node_modules/qs/lib/parse.js", "webpack:///./assets/src/close.svg", "webpack:///./assets/src/reset.svg", "webpack:///./assets/src/field-group-admin.js", "webpack:///./assets/src/input.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "require", "toString", "isArray", "val", "isUndefined", "isObject", "isPlainObject", "getPrototypeOf", "isFunction", "for<PERSON>ach", "obj", "fn", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "isFormData", "FormData", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "isString", "isNumber", "isDate", "isFile", "isBlob", "isStream", "pipe", "isURLSearchParams", "URLSearchParams", "isStandardBrowserEnv", "navigator", "product", "window", "document", "merge", "result", "assignValue", "slice", "arguments", "extend", "a", "b", "thisArg", "trim", "str", "replace", "stripBOM", "content", "charCodeAt", "stringify", "parse", "formats", "$SyntaxError", "SyntaxError", "$Function", "Function", "$TypeError", "TypeError", "getEvalledConstructor", "expressionSyntax", "e", "$gOPD", "getOwnPropertyDescriptor", "throwTypeError", "ThrowTypeError", "calleeThrows", "gOPDthrows", "hasSymbols", "getProto", "x", "__proto__", "needsEval", "TypedArray", "Uint8Array", "undefined", "INTRINSICS", "AggregateError", "Array", "iterator", "Atomics", "BigInt", "Boolean", "DataView", "Date", "decodeURI", "decodeURIComponent", "encodeURI", "encodeURIComponent", "Error", "eval", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Float32Array", "Float64Array", "FinalizationRegistry", "Int8Array", "Int16Array", "Int32Array", "isFinite", "isNaN", "JSON", "Map", "Math", "Number", "parseFloat", "parseInt", "Promise", "Proxy", "RangeError", "ReferenceError", "Reflect", "RegExp", "Set", "SharedArrayBuffer", "String", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "URIError", "WeakMap", "WeakRef", "WeakSet", "LEGACY_ALIASES", "hasOwn", "$concat", "concat", "$spliceApply", "apply", "splice", "$replace", "$strSlice", "rePropName", "reEscapeChar", "stringToPath", "string", "first", "last", "match", "number", "quote", "subString", "getBaseIntrinsic", "allowMissing", "alias", "intrinsicName", "<PERSON><PERSON><PERSON>", "gen", "parts", "intrinsicBaseName", "intrinsic", "intrinsicRealName", "skipF<PERSON>herCaching", "isOwn", "part", "desc", "implementation", "percentTwenties", "Format", "formatters", "RFC1738", "RFC3986", "args", "utils", "encode", "url", "params", "paramsSerializer", "serializedParams", "v", "toISOString", "push", "join", "hashmarkIndex", "indexOf", "__CANCEL__", "normalizeHeaderName", "DEFAULT_CONTENT_TYPE", "setContentTypeIfUnset", "headers", "adapter", "defaults", "XMLHttpRequest", "process", "transformRequest", "data", "transformResponse", "timeout", "xsrfCookieName", "xsrfHeaderName", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateStatus", "status", "common", "method", "settle", "cookies", "buildURL", "buildFullPath", "parseHeaders", "isURLSameOrigin", "createError", "config", "resolve", "reject", "requestData", "requestHeaders", "type", "request", "auth", "username", "password", "unescape", "Authorization", "btoa", "fullPath", "baseURL", "open", "toUpperCase", "onreadystatechange", "readyState", "responseURL", "responseHeaders", "getAllResponseHeaders", "response", "responseType", "responseText", "statusText", "<PERSON>ab<PERSON>", "onerror", "ontimeout", "timeoutErrorMessage", "xsrfValue", "withCredentials", "read", "toLowerCase", "setRequestHeader", "onDownloadProgress", "addEventListener", "onUploadProgress", "upload", "cancelToken", "promise", "then", "cancel", "abort", "send", "enhanceError", "message", "code", "error", "config1", "config2", "valueFromConfig2Keys", "mergeDeepPropertiesKeys", "defaultToConfig2Keys", "directMergeKeys", "getMergedValue", "target", "source", "mergeDeepProperties", "prop", "axios<PERSON><PERSON><PERSON>", "otherKeys", "keys", "filter", "Cancel", "this", "has", "hexTable", "array", "arrayToObject", "options", "plainObjects", "assign", "reduce", "acc", "combine", "compact", "queue", "refs", "item", "j", "pop", "compacted", "compactQueue", "decode", "decoder", "charset", "strWithoutPlus", "defaultEncoder", "kind", "format", "escape", "$0", "out", "char<PERSON>t", "isRegExp", "maybeMap", "mapped", "allowPrototypes", "mergeTarget", "targetItem", "factory", "ownKeys", "enumerableOnly", "getOwnPropertySymbols", "symbols", "sym", "_objectSpread2", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_typeof", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "configurable", "writable", "_toConsumableArray", "arr", "_arrayLikeToArray", "_arrayWithoutHoles", "iter", "from", "_iterableToArray", "minLen", "test", "_unsupportedIterableToArray", "_nonIterableSpread", "len", "arr2", "IS_BROWSER", "WINDOW", "IS_TOUCH_DEVICE", "documentElement", "HAS_POINTER_EVENT", "CLASS_CROP", "CLASS_DISABLED", "CLASS_HIDDEN", "CLASS_HIDE", "CLASS_INVISIBLE", "CLASS_MODAL", "CLASS_MOVE", "DATA_ACTION", "DATA_PREVIEW", "EVENT_POINTER_DOWN", "EVENT_POINTER_MOVE", "EVENT_POINTER_UP", "REGEXP_ACTIONS", "REGEXP_DATA_URL", "REGEXP_DATA_URL_JPEG", "REGEXP_TAG_NAME", "DEFAULTS", "viewMode", "dragMode", "initialAspectRatio", "NaN", "aspectRatio", "preview", "responsive", "restore", "checkCrossOrigin", "checkOrientation", "modal", "guides", "center", "highlight", "background", "autoCrop", "autoCropArea", "movable", "rotatable", "scalable", "zoomable", "zoomOnTouch", "zoomOnWheel", "wheelZoomRatio", "cropBoxMovable", "cropBoxResizable", "toggleDragModeOnDblclick", "minCanvasWidth", "minCanvasHeight", "minCropBoxWidth", "minCropBoxHeight", "minContainer<PERSON><PERSON><PERSON>", "minContainerHeight", "ready", "cropstart", "cropmove", "cropend", "crop", "zoom", "isPositiveNumber", "Infinity", "_constructor", "toArray", "callback", "_len", "_key", "arg", "REGEXP_DECIMALS", "normalizeDecimalNumber", "times", "round", "REGEXP_SUFFIX", "setStyle", "element", "styles", "style", "addClass", "elem", "classList", "add", "className", "removeClass", "remove", "toggleClass", "added", "REGEXP_CAMEL_CASE", "toParamCase", "getData", "dataset", "getAttribute", "setData", "setAttribute", "REGEXP_SPACES", "onceSupported", "supported", "once", "listener", "set", "removeEventListener", "removeListener", "handler", "split", "event", "listeners", "addListener", "_handler", "_element$listeners", "_len2", "_key2", "dispatchEvent", "Event", "CustomEvent", "detail", "bubbles", "cancelable", "createEvent", "initCustomEvent", "getOffset", "box", "getBoundingClientRect", "left", "pageXOffset", "clientLeft", "top", "pageYOffset", "clientTop", "location", "REGEXP_ORIGINS", "isCrossOriginURL", "protocol", "hostname", "port", "addTimestamp", "timestamp", "getTime", "getTransforms", "_ref", "rotate", "scaleX", "scaleY", "translateX", "translateY", "values", "transform", "WebkitTransform", "msTransform", "getPointer", "_ref2", "endOnly", "pageX", "pageY", "end", "endX", "endY", "startX", "startY", "getAdjustedSizes", "_ref4", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isValidHeight", "adjustedWidth", "getSourceCanvas", "image", "_ref6", "_ref7", "_ref8", "imageAspectRatio", "imageNaturalWidth", "naturalWidth", "imageNaturalHeight", "naturalHeight", "_ref6$rotate", "_ref6$scaleX", "_ref6$scaleY", "_ref8$fillColor", "fillColor", "_ref8$imageSmoothingE", "imageSmoothingEnabled", "_ref8$imageSmoothingQ", "imageSmoothingQuality", "_ref8$maxWidth", "max<PERSON><PERSON><PERSON>", "_ref8$maxHeight", "maxHeight", "_ref8$minWidth", "min<PERSON><PERSON><PERSON>", "_ref8$minHeight", "minHeight", "canvas", "createElement", "context", "getContext", "maxSizes", "minSizes", "min", "max", "destMaxSizes", "destMinSizes", "destWidth", "destHeight", "fillStyle", "fillRect", "save", "translate", "PI", "scale", "drawImage", "map", "param", "floor", "fromCharCode", "REGEXP_DATA_URL_HEAD", "resetAndGetOrientation", "arrayBuffer", "orientation", "dataView", "littleEndian", "app1Start", "ifdStart", "getUint8", "byteLength", "offset", "tiffOffset", "start", "getStringFromCharCode", "endianness", "getUint16", "firstIFDOffset", "getUint32", "_offset", "_length", "setUint16", "render", "initContainer", "initCanvas", "initCropBox", "renderCanvas", "cropped", "renderCropBox", "container", "cropper", "containerData", "offsetWidth", "offsetHeight", "imageData", "rotated", "abs", "canvasWidth", "canvasHeight", "canvasData", "limited", "limitCanvas", "oldLeft", "oldTop", "initialCanvasData", "sizeLimited", "positionLimited", "cropBoxData", "_getAdjustedSizes", "newCanvasLeft", "newCanvasTop", "minLeft", "minTop", "maxLeft", "maxTop", "changed", "transformed", "_getRotatedSizes", "_ref5", "degree", "arc", "sinArc", "sin", "cosArc", "cos", "newWidth", "newHeight", "getRotatedSizes", "renderImage", "limitCropBox", "output", "initialCropBoxData", "maxCropBox<PERSON>idth", "maxCropBoxHeight", "face", "cropBox", "disabled", "initPreview", "crossOrigin", "crossOriginUrl", "alt", "src", "viewBox", "append<PERSON><PERSON><PERSON>", "viewBoxImage", "previews", "ownerDocument", "querySelectorAll", "querySelector", "el", "img", "html", "innerHTML", "cssText", "resetPreview", "removeAttribute", "removeData", "cropBoxWidth", "cropBoxHeight", "originalWidth", "originalHeight", "ratio", "getElementsByTagName", "events", "onCropStart", "cropStart", "onWheel", "wheel", "passive", "capture", "onDblclick", "dblclick", "onCropMove", "cropMove", "onCropEnd", "cropEnd", "onResize", "resize", "unbind", "handlers", "ratioX", "ratioY", "getCanvasData", "getCropBoxData", "setCanvasData", "setCropBoxData", "setDragMode", "dragBox", "contains", "_this", "delta", "preventDefault", "wheeling", "setTimeout", "deltaY", "wheelDelta", "buttons", "button", "pointerType", "ctrl<PERSON>ey", "action", "pointers", "changedTouches", "touch", "identifier", "pointerId", "originalEvent", "cropping", "change", "right", "bottom", "renderable", "shift<PERSON>ey", "pointer", "range", "y", "check", "side", "move", "pointers2", "maxRatio", "pointer2", "x1", "y1", "x2", "y2", "z1", "sqrt", "getMaxZoomRatio", "methods", "reset", "initialImageData", "clear", "hasSameSize", "isImg", "replaced", "uncreate", "load", "enable", "disable", "destroy", "originalUrl", "offsetX", "offsetY", "_this$canvasData", "moveTo", "_originalEvent", "zoomTo", "pivot", "oldRatio", "count", "_ref3", "getPointersCenter", "rotateTo", "_scaleX", "_scaleY", "rounded", "getContainerData", "getImageData", "sized", "widthChanged", "heightChanged", "getCroppedCanvas", "HTMLCanvasElement", "_this$getData", "initialX", "initialY", "initialWidth", "initialHeight", "_options$imageSmoothi", "srcWidth", "srcHeight", "dstX", "dstY", "dstWidth", "dstHeight", "sourceWidth", "sourceHeight", "srcX", "srcY", "setAspectRatio", "croppable", "AnotherCropper", "C<PERSON>per", "tagName", "reloading", "sizing", "init", "protoProps", "staticProps", "toDataURL", "base64", "binary", "atob", "uint8", "clone", "xhr", "onprogress", "getResponseHeader", "onload", "onloadend", "mimeType", "chunks", "subarray", "arrayBufferToDataURL", "_parseOrientation", "parseOrientation", "stop", "parentNode", "insertBefore", "nextS<PERSON>ling", "_this2", "isIOSWebKit", "userAgent", "done", "build", "sizingImage", "body", "<PERSON><PERSON><PERSON><PERSON>", "template", "getElementsByClassName", "unbuild", "define", "re", "not_string", "not_bool", "not_type", "not_primitive", "numeric_arg", "json", "not_json", "text", "modulo", "placeholder", "key_access", "index_access", "sign", "sprintf", "sprintf_format", "sprintf_parse", "vsprintf", "fmt", "argv", "parse_tree", "k", "ph", "pad", "pad_character", "pad_length", "is_positive", "cursor", "tree_length", "param_no", "precision", "toExponential", "toFixed", "toPrecision", "substring", "valueOf", "pad_char", "repeat", "align", "sprintf_cache", "_fmt", "arg_names", "exec", "field_list", "replacement_field", "field_match", "A<PERSON>os", "mergeConfig", "createInstance", "defaultConfig", "axios", "instanceConfig", "CancelToken", "isCancel", "all", "promises", "spread", "InterceptorManager", "dispatchRequest", "interceptors", "chain", "interceptor", "unshift", "fulfilled", "rejected", "shift", "get<PERSON><PERSON>", "use", "eject", "id", "h", "transformData", "throwIfCancellationRequested", "throwIfRequested", "reason", "fns", "cachedSetTimeout", "cachedClearTimeout", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "clearTimeout", "currentQueue", "draining", "queueIndex", "cleanUpNextTick", "drainQueue", "run", "marker", "runClearTimeout", "<PERSON><PERSON>", "noop", "nextTick", "title", "browser", "env", "version", "versions", "on", "off", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "binding", "cwd", "chdir", "dir", "umask", "normalizedName", "isAxiosError", "toJSON", "description", "fileName", "lineNumber", "columnNumber", "stack", "write", "expires", "path", "domain", "secure", "cookie", "toGMTString", "now", "isAbsoluteURL", "combineURLs", "requestedURL", "relativeURL", "ignoreDuplicateOf", "parsed", "line", "substr", "originURL", "msie", "urlParsingNode", "resolveURL", "href", "host", "search", "hash", "pathname", "requestURL", "executor", "resolvePromise", "token", "getSideChannel", "arrayPrefixGenerators", "brackets", "prefix", "comma", "indices", "pushToArray", "valueOrArray", "toISO", "defaultFormat", "addQueryPrefix", "allowDots", "charset<PERSON><PERSON><PERSON>l", "delimiter", "encoder", "encodeValuesOnly", "formatter", "serializeDate", "date", "skipNulls", "strict<PERSON>ull<PERSON>andling", "generateArrayPrefix", "sort", "sideChannel", "ob<PERSON><PERSON><PERSON><PERSON>", "keyPrefix", "valueSideChannel", "opts", "normalizeStringifyOptions", "arrayFormat", "joined", "GetIntrinsic", "callBound", "inspect", "$WeakMap", "$Map", "$weakMapGet", "$weakMapSet", "$weakMapHas", "$mapGet", "$mapSet", "$mapHas", "listGetNode", "list", "curr", "prev", "next", "$wm", "$m", "$o", "channel", "assert", "objects", "node", "listGet", "listHas", "listSet", "origSymbol", "hasSymbolSham", "symObj", "getOwnPropertyNames", "syms", "propertyIsEnumerable", "ERROR_MESSAGE", "toStr", "that", "bound", "binder", "<PERSON><PERSON><PERSON><PERSON>", "boundArgs", "Empty", "callBind", "$indexOf", "$apply", "$call", "$reflectApply", "$defineProperty", "$max", "originalFunction", "func", "applyBind", "hasMap", "mapSizeDescriptor", "mapSize", "mapForEach", "hasSet", "setSizeDescriptor", "setSize", "setForEach", "weakMapHas", "weakSetHas", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deref", "booleanValueOf", "objectToString", "functionToString", "bigIntValueOf", "gOPS", "symToString", "hasShammedSymbols", "isEnumerable", "gPO", "O", "inspectCustom", "custom", "inspectSymbol", "isSymbol", "wrapQuotes", "defaultStyle", "quoteChar", "quoteStyle", "inspect_", "depth", "seen", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "customInspect", "indent", "inspectString", "remaining", "trailer", "lowbyte", "max<PERSON><PERSON><PERSON>", "baseIndent", "base", "getIndent", "noIndent", "newOpts", "f", "nameOf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "symString", "markBoxed", "HTMLElement", "nodeName", "isElement", "attrs", "attributes", "childNodes", "xs", "singleLineValues", "indented<PERSON><PERSON><PERSON>", "isError", "isMap", "mapParts", "collectionOf", "isSet", "setParts", "isWeakMap", "weakCollectionOf", "isWeakSet", "isWeakRef", "isBigInt", "isBoolean", "ys", "protoTag", "stringTag", "tag", "8", "9", "10", "12", "13", "size", "entries", "lineJoiner", "isArr", "symMap", "allowSparse", "arrayLimit", "ignoreQueryPrefix", "interpretNumericEntities", "parameterLimit", "parseA<PERSON>ys", "numberStr", "parseArrayValue", "parse<PERSON>eys", "<PERSON><PERSON><PERSON>", "valuesParsed", "child", "segment", "parent", "index", "leaf", "root", "cleanRoot", "0", "parseObject", "normalizeParseOptions", "tempObj", "cleanStr", "limit", "skipIndex", "bracketEqualsPos", "pos", "encodedVal", "parseV<PERSON>ues", "newObj", "$", "checkElementHasValue", "rootElement", "parents", "find", "makeElementReadOnly", "makeElementEditable", "clearElement", "makeElementRequired", "makeElementNotRequired", "copyElementValue", "element1", "element2", "targetElement1", "targetElement2", "attr", "getElementValue", "setElementValue", "targetElement", "toggleCropType", "actionType", "$element", "each", "acf", "add_action", "setHeight", "sourceElement", "aspectRatioWidth", "aspectRatioHeight", "<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "field", "initialize_field", "$el", "fields", "image_aspect_ratio_crop", "$input", "$img", "actions", "append", "front_end_upload", "uploadElement", "currentTarget", "acf<PERSON>ey", "$field", "files", "formData", "isFirstCrop", "settings", "progressEvent", "percentCompleted", "loaded", "total", "aiarc_translations", "upload_progress", "aiarc", "nonce", "wp_rest_nonce", "hide", "show", "post", "api_root", "attachment_id", "attachment", "Backbone", "Model", "openModal", "errorMessage", "upload_failed", "alert", "focus", "get_data", "initialize", "self", "uploader", "closest", "escapeHandlerBound", "<PERSON><PERSON><PERSON><PERSON>", "closeModal", "cropData", "css", "cropType", "temp_post_id", "loading", "cropping_in_progress", "cropping_failed", "empty", "aiarc_settings", "rest_api_compat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qs", "cropComplete", "console", "prepare", "_valid", "caption", "$repeater", "get_closest_field", "media", "popup", "_e", "multiple", "library", "mime_types", "select", "$tr", "nextAll", "get_field", "exists", "repeater", "doFocus", "changeCrop", "originalImageId", "edit", "modal_type", "coordinates", "modal_title", "_acf_image_aspect_ratio_cropper", "postData"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,I,oQChFrD,IAAIP,EAAOQ,EAAQ,GAMfC,EAAWvB,OAAOkB,UAAUK,SAQhC,SAASC,EAAQC,GACf,MAA8B,mBAAvBF,EAAS9B,KAAKgC,GASvB,SAASC,EAAYD,GACnB,YAAsB,IAARA,EA4EhB,SAASE,EAASF,GAChB,OAAe,OAARA,GAA+B,WAAf,EAAOA,GAShC,SAASG,EAAcH,GACrB,GAA2B,oBAAvBF,EAAS9B,KAAKgC,GAChB,OAAO,EAGT,IAAIP,EAAYlB,OAAO6B,eAAeJ,GACtC,OAAqB,OAAdP,GAAsBA,IAAclB,OAAOkB,UAuCpD,SAASY,EAAWL,GAClB,MAA8B,sBAAvBF,EAAS9B,KAAKgC,GAwEvB,SAASM,EAAQC,EAAKC,GAEpB,GAAID,QAUJ,GALmB,WAAf,EAAOA,KAETA,EAAM,CAACA,IAGLR,EAAQQ,GAEV,IAAK,IAAI1C,EAAI,EAAGC,EAAIyC,EAAIE,OAAQ5C,EAAIC,EAAGD,IACrC2C,EAAGxC,KAAK,KAAMuC,EAAI1C,GAAIA,EAAG0C,QAI3B,IAAK,IAAInB,KAAOmB,EACVhC,OAAOkB,UAAUC,eAAe1B,KAAKuC,EAAKnB,IAC5CoB,EAAGxC,KAAK,KAAMuC,EAAInB,GAAMA,EAAKmB,GA2ErC3C,EAAOD,QAAU,CACfoC,QAASA,EACTW,cA1RF,SAAuBV,GACrB,MAA8B,yBAAvBF,EAAS9B,KAAKgC,IA0RrBW,SAtSF,SAAkBX,GAChB,OAAe,OAARA,IAAiBC,EAAYD,IAA4B,OAApBA,EAAIY,cAAyBX,EAAYD,EAAIY,cAChD,mBAA7BZ,EAAIY,YAAYD,UAA2BX,EAAIY,YAAYD,SAASX,IAqShFa,WAlRF,SAAoBb,GAClB,MAA4B,oBAAbc,UAA8Bd,aAAec,UAkR5DC,kBAzQF,SAA2Bf,GAOzB,MAL4B,oBAAhBgB,aAAiCA,YAAYC,OAC9CD,YAAYC,OAAOjB,GAElBA,GAASA,EAAIkB,QAAYlB,EAAIkB,kBAAkBF,aAqQ3DG,SA1PF,SAAkBnB,GAChB,MAAsB,iBAARA,GA0PdoB,SAjPF,SAAkBpB,GAChB,MAAsB,iBAARA,GAiPdE,SAAUA,EACVC,cAAeA,EACfF,YAAaA,EACboB,OAlNF,SAAgBrB,GACd,MAA8B,kBAAvBF,EAAS9B,KAAKgC,IAkNrBsB,OAzMF,SAAgBtB,GACd,MAA8B,kBAAvBF,EAAS9B,KAAKgC,IAyMrBuB,OAhMF,SAAgBvB,GACd,MAA8B,kBAAvBF,EAAS9B,KAAKgC,IAgMrBK,WAAYA,EACZmB,SA9KF,SAAkBxB,GAChB,OAAOE,EAASF,IAAQK,EAAWL,EAAIyB,OA8KvCC,kBArKF,SAA2B1B,GACzB,MAAkC,oBAApB2B,iBAAmC3B,aAAe2B,iBAqKhEC,qBAzIF,WACE,OAAyB,oBAAdC,WAAoD,gBAAtBA,UAAUC,SACY,iBAAtBD,UAAUC,SACY,OAAtBD,UAAUC,WAI/B,oBAAXC,QACa,oBAAbC,WAkIT1B,QAASA,EACT2B,MAvEF,SAASA,IACP,IAAIC,EAAS,GACb,SAASC,EAAYnC,EAAKZ,GACpBe,EAAc+B,EAAO9C,KAASe,EAAcH,GAC9CkC,EAAO9C,GAAO6C,EAAMC,EAAO9C,GAAMY,GACxBG,EAAcH,GACvBkC,EAAO9C,GAAO6C,EAAM,GAAIjC,GACfD,EAAQC,GACjBkC,EAAO9C,GAAOY,EAAIoC,QAElBF,EAAO9C,GAAOY,EAIlB,IAAK,IAAInC,EAAI,EAAGC,EAAIuE,UAAU5B,OAAQ5C,EAAIC,EAAGD,IAC3CyC,EAAQ+B,UAAUxE,GAAIsE,GAExB,OAAOD,GAuDPI,OA5CF,SAAgBC,EAAGC,EAAGC,GAQpB,OAPAnC,EAAQkC,GAAG,SAAqBxC,EAAKZ,GAEjCmD,EAAEnD,GADAqD,GAA0B,mBAARzC,EACXX,EAAKW,EAAKyC,GAEVzC,KAGNuC,GAqCPG,KAhKF,SAAcC,GACZ,OAAOA,EAAIC,QAAQ,OAAQ,IAAIA,QAAQ,OAAQ,KAgK/CC,SA7BF,SAAkBC,GAIhB,OAH8B,QAA1BA,EAAQC,WAAW,KACrBD,EAAUA,EAAQV,MAAM,IAEnBU,K,gBCpUTlF,EAAOD,QAAUkC,EAAQ,K,6BCEzB,IAAImD,EAAYnD,EAAQ,IACpBoD,EAAQpD,EAAQ,IAChBqD,EAAUrD,EAAQ,GAEtBjC,EAAOD,QAAU,CACbuF,QAASA,EACTD,MAAOA,EACPD,UAAWA,I,kQCPf,IAEIG,EAAeC,YACfC,EAAYC,SACZC,EAAaC,UAGbC,EAAwB,SAAUC,GACrC,IACC,OAAOL,EAAU,yBAA2BK,EAAmB,iBAAxDL,GACN,MAAOM,MAGNC,EAAQrF,OAAOsF,yBACnB,GAAID,EACH,IACCA,EAAM,GAAI,IACT,MAAOD,GACRC,EAAQ,KAIV,IAAIE,EAAiB,WACpB,MAAM,IAAIP,GAEPQ,EAAiBH,EACjB,WACF,IAGC,OAAOE,EACN,MAAOE,GACR,IAEC,OAAOJ,EAAMvB,UAAW,UAAU3D,IACjC,MAAOuF,GACR,OAAOH,IAVP,GAcDA,EAECI,EAAarE,EAAQ,GAARA,GAEbsE,EAAW5F,OAAO6B,gBAAkB,SAAUgE,GAAK,OAAOA,EAAEC,WAE5DC,EAAY,GAEZC,EAAmC,oBAAfC,gBAhDpBC,EAgD6DN,EAASK,YAEtEE,EAAa,CAChB,mBAA8C,oBAAnBC,oBAnDxBF,EAmDqEE,eACxE,UAAWC,MACX,gBAAwC,oBAAhB5D,iBArDrByD,EAqD+DzD,YAClE,2BAA4BkD,EAAaC,EAAS,GAAGvF,OAAOiG,kBAtDzDJ,EAuDH,wCAvDGA,EAwDH,kBAAmBH,EACnB,mBAAoBA,EACpB,2BAA4BA,EAC5B,2BAA4BA,EAC5B,YAAgC,oBAAZQ,aA5DjBL,EA4DuDK,QAC1D,WAA8B,oBAAXC,YA7DhBN,EA6DqDM,OACxD,YAAaC,QACb,aAAkC,oBAAbC,cA/DlBR,EA+DyDQ,SAC5D,SAAUC,KACV,cAAeC,UACf,uBAAwBC,mBACxB,cAAeC,UACf,uBAAwBC,mBACxB,UAAWC,MACX,SAAUC,KACV,cAAeC,UACf,iBAA0C,oBAAjBC,kBAxEtBjB,EAwEiEiB,aACpE,iBAA0C,oBAAjBC,kBAzEtBlB,EAyEiEkB,aACpE,yBAA0D,oBAAzBC,0BA1E9BnB,EA0EiFmB,qBACpF,aAAcvC,EACd,sBAAuBiB,EACvB,cAAoC,oBAAduB,eA7EnBpB,EA6E2DoB,UAC9D,eAAsC,oBAAfC,gBA9EpBrB,EA8E6DqB,WAChE,eAAsC,oBAAfC,gBA/EpBtB,EA+E6DsB,WAChE,aAAcC,SACd,UAAWC,MACX,sBAAuB/B,EAAaC,EAASA,EAAS,GAAGvF,OAAOiG,mBAlF7DJ,EAmFH,SAA0B,YAAhB,oBAAOyB,KAAP,cAAOA,OAAoBA,UAnFlCzB,EAoFH,QAAwB,oBAAR0B,SApFb1B,EAoF+C0B,IAClD,yBAAyC,oBAARA,KAAwBjC,EAAyBC,GAAS,IAAIgC,KAAMvH,OAAOiG,kBArFzGJ,EAsFH,SAAU2B,KACV,WAAYC,OACZ,WAAY9H,OACZ,eAAgB+H,WAChB,aAAcC,SACd,YAAgC,oBAAZC,aA3FjB/B,EA2FuD+B,QAC1D,UAA4B,oBAAVC,WA5FfhC,EA4FmDgC,MACtD,eAAgBC,WAChB,mBAAoBC,eACpB,YAAgC,oBAAZC,aA/FjBnC,EA+FuDmC,QAC1D,WAAYC,OACZ,QAAwB,oBAARC,SAjGbrC,EAiG+CqC,IAClD,yBAAyC,oBAARA,KAAwB5C,EAAyBC,GAAS,IAAI2C,KAAMlI,OAAOiG,kBAlGzGJ,EAmGH,sBAAoD,oBAAtBsC,uBAnG3BtC,EAmG2EsC,kBAC9E,WAAYC,OACZ,4BAA6B9C,EAAaC,EAAS,GAAGvF,OAAOiG,kBArG1DJ,EAsGH,WAAYP,EAAatF,YAtGtB6F,EAuGH,gBAAiBtB,EACjB,mBAAoBY,EACpB,eAAgBQ,EAChB,cAAehB,EACf,eAAsC,oBAAfiB,gBA3GpBC,EA2G6DD,WAChE,sBAAoD,oBAAtByC,uBA5G3BxC,EA4G2EwC,kBAC9E,gBAAwC,oBAAhBC,iBA7GrBzC,EA6G+DyC,YAClE,gBAAwC,oBAAhBC,iBA9GrB1C,EA8G+D0C,YAClE,aAAcC,SACd,YAAgC,oBAAZC,aAhHjB5C,EAgHuD4C,QAC1D,YAAgC,oBAAZC,aAjHjB7C,EAiHuD6C,QAC1D,YAAgC,oBAAZC,aAlHjB9C,EAkHuD8C,SA4BvDC,EAAiB,CACpB,yBAA0B,CAAC,cAAe,aAC1C,mBAAoB,CAAC,QAAS,aAC9B,uBAAwB,CAAC,QAAS,YAAa,WAC/C,uBAAwB,CAAC,QAAS,YAAa,WAC/C,oBAAqB,CAAC,QAAS,YAAa,QAC5C,sBAAuB,CAAC,QAAS,YAAa,UAC9C,2BAA4B,CAAC,gBAAiB,aAC9C,mBAAoB,CAAC,yBAA0B,aAC/C,4BAA6B,CAAC,yBAA0B,YAAa,aACrE,qBAAsB,CAAC,UAAW,aAClC,sBAAuB,CAAC,WAAY,aACpC,kBAAmB,CAAC,OAAQ,aAC5B,mBAAoB,CAAC,QAAS,aAC9B,uBAAwB,CAAC,YAAa,aACtC,0BAA2B,CAAC,eAAgB,aAC5C,0BAA2B,CAAC,eAAgB,aAC5C,sBAAuB,CAAC,WAAY,aACpC,cAAe,CAAC,oBAAqB,aACrC,uBAAwB,CAAC,oBAAqB,YAAa,aAC3D,uBAAwB,CAAC,YAAa,aACtC,wBAAyB,CAAC,aAAc,aACxC,wBAAyB,CAAC,aAAc,aACxC,cAAe,CAAC,OAAQ,SACxB,kBAAmB,CAAC,OAAQ,aAC5B,iBAAkB,CAAC,MAAO,aAC1B,oBAAqB,CAAC,SAAU,aAChC,oBAAqB,CAAC,SAAU,aAChC,sBAAuB,CAAC,SAAU,YAAa,YAC/C,qBAAsB,CAAC,SAAU,YAAa,WAC9C,qBAAsB,CAAC,UAAW,aAClC,sBAAuB,CAAC,UAAW,YAAa,QAChD,gBAAiB,CAAC,UAAW,OAC7B,mBAAoB,CAAC,UAAW,UAChC,oBAAqB,CAAC,UAAW,WACjC,wBAAyB,CAAC,aAAc,aACxC,4BAA6B,CAAC,iBAAkB,aAChD,oBAAqB,CAAC,SAAU,aAChC,iBAAkB,CAAC,MAAO,aAC1B,+BAAgC,CAAC,oBAAqB,aACtD,oBAAqB,CAAC,SAAU,aAChC,oBAAqB,CAAC,SAAU,aAChC,yBAA0B,CAAC,cAAe,aAC1C,wBAAyB,CAAC,aAAc,aACxC,uBAAwB,CAAC,YAAa,aACtC,wBAAyB,CAAC,aAAc,aACxC,+BAAgC,CAAC,oBAAqB,aACtD,yBAA0B,CAAC,cAAe,aAC1C,yBAA0B,CAAC,cAAe,aAC1C,sBAAuB,CAAC,WAAY,aACpC,qBAAsB,CAAC,UAAW,aAClC,qBAAsB,CAAC,UAAW,cAG/BnI,EAAOQ,EAAQ,GACf4H,EAAS5H,EAAQ,IACjB6H,EAAUrI,EAAKrB,KAAKsF,SAAStF,KAAM4G,MAAMnF,UAAUkI,QACnDC,EAAevI,EAAKrB,KAAKsF,SAASuE,MAAOjD,MAAMnF,UAAUqI,QACzDC,EAAW1I,EAAKrB,KAAKsF,SAAStF,KAAMgJ,OAAOvH,UAAUmD,SACrDoF,EAAY3I,EAAKrB,KAAKsF,SAAStF,KAAMgJ,OAAOvH,UAAU2C,OAGtD6F,EAAa,qGACbC,EAAe,WACfC,EAAe,SAAsBC,GACxC,IAAIC,EAAQL,EAAUI,EAAQ,EAAG,GAC7BE,EAAON,EAAUI,GAAS,GAC9B,GAAc,MAAVC,GAA0B,MAATC,EACpB,MAAM,IAAInF,EAAa,kDACjB,GAAa,MAATmF,GAA0B,MAAVD,EAC1B,MAAM,IAAIlF,EAAa,kDAExB,IAAIjB,EAAS,GAIb,OAHA6F,EAASK,EAAQH,GAAY,SAAUM,EAAOC,EAAQC,EAAOC,GAC5DxG,EAAOA,EAAOzB,QAAUgI,EAAQV,EAASW,EAAWR,EAAc,MAAQM,GAAUD,KAE9ErG,GAIJyG,EAAmB,SAA0BvK,EAAMwK,GACtD,IACIC,EADAC,EAAgB1K,EAOpB,GALIqJ,EAAOD,EAAgBsB,KAE1BA,EAAgB,KADhBD,EAAQrB,EAAesB,IACK,GAAK,KAG9BrB,EAAO/C,EAAYoE,GAAgB,CACtC,IAAIhK,EAAQ4F,EAAWoE,GAIvB,GAHIhK,IAAUwF,IACbxF,EApHU,SAASiK,EAAO3K,GAC5B,IAAIU,EACJ,GAAa,oBAATV,EACHU,EAAQ2E,EAAsB,6BACxB,GAAa,wBAATrF,EACVU,EAAQ2E,EAAsB,wBACxB,GAAa,6BAATrF,EACVU,EAAQ2E,EAAsB,8BACxB,GAAa,qBAATrF,EAA6B,CACvC,IAAIoC,EAAKuI,EAAO,4BACZvI,IACH1B,EAAQ0B,EAAGf,gBAEN,GAAa,6BAATrB,EAAqC,CAC/C,IAAI4K,EAAMD,EAAO,oBACbC,IACHlK,EAAQqF,EAAS6E,EAAIvJ,YAMvB,OAFAiF,EAAWtG,GAAQU,EAEZA,EA8FGiK,CAAOD,SAEK,IAAVhK,IAA0B8J,EACpC,MAAM,IAAIrF,EAAW,aAAenF,EAAO,wDAG5C,MAAO,CACNyK,MAAOA,EACPzK,KAAM0K,EACNhK,MAAOA,GAIT,MAAM,IAAIqE,EAAa,aAAe/E,EAAO,qBAG9CR,EAAOD,QAAU,SAAsBS,EAAMwK,GAC5C,GAAoB,iBAATxK,GAAqC,IAAhBA,EAAKqC,OACpC,MAAM,IAAI8C,EAAW,6CAEtB,GAAIlB,UAAU5B,OAAS,GAA6B,kBAAjBmI,EAClC,MAAM,IAAIrF,EAAW,6CAGtB,IAAI0F,EAAQd,EAAa/J,GACrB8K,EAAoBD,EAAMxI,OAAS,EAAIwI,EAAM,GAAK,GAElDE,EAAYR,EAAiB,IAAMO,EAAoB,IAAKN,GAC5DQ,EAAoBD,EAAU/K,KAC9BU,EAAQqK,EAAUrK,MAClBuK,GAAqB,EAErBR,EAAQM,EAAUN,MAClBA,IACHK,EAAoBL,EAAM,GAC1BjB,EAAaqB,EAAOvB,EAAQ,CAAC,EAAG,GAAImB,KAGrC,IAAK,IAAIhL,EAAI,EAAGyL,GAAQ,EAAMzL,EAAIoL,EAAMxI,OAAQ5C,GAAK,EAAG,CACvD,IAAI0L,EAAON,EAAMpL,GACbwK,EAAQL,EAAUuB,EAAM,EAAG,GAC3BjB,EAAON,EAAUuB,GAAO,GAC5B,IAEa,MAAVlB,GAA2B,MAAVA,GAA2B,MAAVA,GACtB,MAATC,GAAyB,MAATA,GAAyB,MAATA,IAElCD,IAAUC,EAEb,MAAM,IAAInF,EAAa,wDASxB,GAPa,gBAAToG,GAA2BD,IAC9BD,GAAqB,GAMlB5B,EAAO/C,EAFX0E,EAAoB,KADpBF,GAAqB,IAAMK,GACmB,KAG7CzK,EAAQ4F,EAAW0E,QACb,GAAa,MAATtK,EAAe,CACzB,KAAMyK,KAAQzK,GAAQ,CACrB,IAAK8J,EACJ,MAAM,IAAIrF,EAAW,sBAAwBnF,EAAO,+CAErD,OAED,GAAIwF,GAAU/F,EAAI,GAAMoL,EAAMxI,OAAQ,CACrC,IAAI+I,EAAO5F,EAAM9E,EAAOyK,GAWvBzK,GAVDwK,IAAUE,IASG,QAASA,KAAU,kBAAmBA,EAAK9K,KAC/C8K,EAAK9K,IAELI,EAAMyK,QAGfD,EAAQ7B,EAAO3I,EAAOyK,GACtBzK,EAAQA,EAAMyK,GAGXD,IAAUD,IACb3E,EAAW0E,GAAqBtK,IAInC,OAAOA,I,6BCtUR,IAAI2K,EAAiB5J,EAAQ,IAE7BjC,EAAOD,QAAU2F,SAAS7D,UAAUJ,MAAQoK,G,6BCF5C,IAAI7G,EAAUoE,OAAOvH,UAAUmD,QAC3B8G,EAAkB,OAElBC,EACS,UADTA,EAES,UAGb/L,EAAOD,QAAU,CACb,QAAWgM,EACXC,WAAY,CACRC,QAAS,SAAU/K,GACf,OAAO8D,EAAQ5E,KAAKc,EAAO4K,EAAiB,MAEhDI,QAAS,SAAUhL,GACf,OAAOkI,OAAOlI,KAGtB+K,QAASF,EACTG,QAASH,I,6BCnBb/L,EAAOD,QAAU,SAAc6C,EAAIiC,GACjC,OAAO,WAEL,IADA,IAAIsH,EAAO,IAAInF,MAAMvC,UAAU5B,QACtB5C,EAAI,EAAGA,EAAIkM,EAAKtJ,OAAQ5C,IAC/BkM,EAAKlM,GAAKwE,UAAUxE,GAEtB,OAAO2C,EAAGqH,MAAMpF,EAASsH,M,6BCN7B,IAAIC,EAAQnK,EAAQ,GAEpB,SAASoK,EAAOjK,GACd,OAAOsF,mBAAmBtF,GACxB4C,QAAQ,QAAS,KACjBA,QAAQ,OAAQ,KAChBA,QAAQ,QAAS,KACjBA,QAAQ,OAAQ,KAChBA,QAAQ,QAAS,KACjBA,QAAQ,QAAS,KAUrBhF,EAAOD,QAAU,SAAkBuM,EAAKC,EAAQC,GAE9C,IAAKD,EACH,OAAOD,EAGT,IAAIG,EACJ,GAAID,EACFC,EAAmBD,EAAiBD,QAC/B,GAAIH,EAAMtI,kBAAkByI,GACjCE,EAAmBF,EAAOrK,eACrB,CACL,IAAImJ,EAAQ,GAEZe,EAAM1J,QAAQ6J,GAAQ,SAAmBnK,EAAKZ,GACxCY,UAIAgK,EAAMjK,QAAQC,GAChBZ,GAAY,KAEZY,EAAM,CAACA,GAGTgK,EAAM1J,QAAQN,GAAK,SAAoBsK,GACjCN,EAAM3I,OAAOiJ,GACfA,EAAIA,EAAEC,cACGP,EAAM9J,SAASoK,KACxBA,EAAIpE,KAAKlD,UAAUsH,IAErBrB,EAAMuB,KAAKP,EAAO7K,GAAO,IAAM6K,EAAOK,WAI1CD,EAAmBpB,EAAMwB,KAAK,KAGhC,GAAIJ,EAAkB,CACpB,IAAIK,EAAgBR,EAAIS,QAAQ,MACT,IAAnBD,IACFR,EAAMA,EAAI9H,MAAM,EAAGsI,IAGrBR,KAA8B,IAAtBA,EAAIS,QAAQ,KAAc,IAAM,KAAON,EAGjD,OAAOH,I,6BClETtM,EAAOD,QAAU,SAAkBmB,GACjC,SAAUA,IAASA,EAAM8L,c,8BCH3B,YAEA,IAAIZ,EAAQnK,EAAQ,GAChBgL,EAAsBhL,EAAQ,IAE9BiL,EAAuB,CACzB,eAAgB,qCAGlB,SAASC,EAAsBC,EAASlM,IACjCkL,EAAM/J,YAAY+K,IAAYhB,EAAM/J,YAAY+K,EAAQ,mBAC3DA,EAAQ,gBAAkBlM,GAgB9B,IAXMmM,EAWFC,EAAW,CACbD,UAX8B,oBAAnBE,qBAGmB,IAAZC,GAAuE,qBAA5C7M,OAAOkB,UAAUK,SAAS9B,KAAKoN,MAD1EH,EAAUpL,EAAQ,KAKboL,GAMPI,iBAAkB,CAAC,SAA0BC,EAAMN,GAGjD,OAFAH,EAAoBG,EAAS,UAC7BH,EAAoBG,EAAS,gBACzBhB,EAAMnJ,WAAWyK,IACnBtB,EAAMtJ,cAAc4K,IACpBtB,EAAMrJ,SAAS2K,IACftB,EAAMxI,SAAS8J,IACftB,EAAM1I,OAAOgK,IACbtB,EAAMzI,OAAO+J,GAENA,EAELtB,EAAMjJ,kBAAkBuK,GACnBA,EAAKpK,OAEV8I,EAAMtI,kBAAkB4J,IAC1BP,EAAsBC,EAAS,mDACxBM,EAAKxL,YAEVkK,EAAM9J,SAASoL,IACjBP,EAAsBC,EAAS,kCACxB9E,KAAKlD,UAAUsI,IAEjBA,IAGTC,kBAAmB,CAAC,SAA2BD,GAE7C,GAAoB,iBAATA,EACT,IACEA,EAAOpF,KAAKjD,MAAMqI,GAClB,MAAO3H,IAEX,OAAO2H,IAOTE,QAAS,EAETC,eAAgB,aAChBC,eAAgB,eAEhBC,kBAAmB,EACnBC,eAAgB,EAEhBC,eAAgB,SAAwBC,GACtC,OAAOA,GAAU,KAAOA,EAAS,MAIrCZ,EAASF,QAAU,CACjBe,OAAQ,CACN,OAAU,sCAId/B,EAAM1J,QAAQ,CAAC,SAAU,MAAO,SAAS,SAA6B0L,GACpEd,EAASF,QAAQgB,GAAU,MAG7BhC,EAAM1J,QAAQ,CAAC,OAAQ,MAAO,UAAU,SAA+B0L,GACrEd,EAASF,QAAQgB,GAAUhC,EAAM/H,MAAM6I,MAGzClN,EAAOD,QAAUuN,I,+CC/FjB,IAAIlB,EAAQnK,EAAQ,GAChBoM,EAASpM,EAAQ,IACjBqM,EAAUrM,EAAQ,IAClBsM,EAAWtM,EAAQ,GACnBuM,EAAgBvM,EAAQ,IACxBwM,EAAexM,EAAQ,IACvByM,EAAkBzM,EAAQ,IAC1B0M,EAAc1M,EAAQ,IAE1BjC,EAAOD,QAAU,SAAoB6O,GACnC,OAAO,IAAIhG,SAAQ,SAA4BiG,EAASC,GACtD,IAAIC,EAAcH,EAAOlB,KACrBsB,EAAiBJ,EAAOxB,QAExBhB,EAAMnJ,WAAW8L,WACZC,EAAe,iBAIrB5C,EAAMzI,OAAOoL,IAAgB3C,EAAM1I,OAAOqL,KAC3CA,EAAYE,aAELD,EAAe,gBAGxB,IAAIE,EAAU,IAAI3B,eAGlB,GAAIqB,EAAOO,KAAM,CACf,IAAIC,EAAWR,EAAOO,KAAKC,UAAY,GACnCC,EAAWC,SAAS5H,mBAAmBkH,EAAOO,KAAKE,YAAc,GACrEL,EAAeO,cAAgB,SAAWC,KAAKJ,EAAW,IAAMC,GAGlE,IAAII,EAAWjB,EAAcI,EAAOc,QAASd,EAAOtC,KA4EpD,GA3EA4C,EAAQS,KAAKf,EAAOR,OAAOwB,cAAerB,EAASkB,EAAUb,EAAOrC,OAAQqC,EAAOpC,mBAAmB,GAGtG0C,EAAQtB,QAAUgB,EAAOhB,QAGzBsB,EAAQW,mBAAqB,WAC3B,GAAKX,GAAkC,IAAvBA,EAAQY,aAQD,IAAnBZ,EAAQhB,QAAkBgB,EAAQa,aAAwD,IAAzCb,EAAQa,YAAYhD,QAAQ,UAAjF,CAKA,IAAIiD,EAAkB,0BAA2Bd,EAAUT,EAAaS,EAAQe,yBAA2B,KAEvGC,EAAW,CACbxC,KAFkBkB,EAAOuB,cAAwC,SAAxBvB,EAAOuB,aAAiDjB,EAAQgB,SAA/BhB,EAAQkB,aAGlFlC,OAAQgB,EAAQhB,OAChBmC,WAAYnB,EAAQmB,WACpBjD,QAAS4C,EACTpB,OAAQA,EACRM,QAASA,GAGXb,EAAOQ,EAASC,EAAQoB,GAGxBhB,EAAU,OAIZA,EAAQoB,QAAU,WACXpB,IAILJ,EAAOH,EAAY,kBAAmBC,EAAQ,eAAgBM,IAG9DA,EAAU,OAIZA,EAAQqB,QAAU,WAGhBzB,EAAOH,EAAY,gBAAiBC,EAAQ,KAAMM,IAGlDA,EAAU,MAIZA,EAAQsB,UAAY,WAClB,IAAIC,EAAsB,cAAgB7B,EAAOhB,QAAU,cACvDgB,EAAO6B,sBACTA,EAAsB7B,EAAO6B,qBAE/B3B,EAAOH,EAAY8B,EAAqB7B,EAAQ,eAC9CM,IAGFA,EAAU,MAMR9C,EAAMpI,uBAAwB,CAEhC,IAAI0M,GAAa9B,EAAO+B,iBAAmBjC,EAAgBe,KAAcb,EAAOf,eAC9ES,EAAQsC,KAAKhC,EAAOf,qBACpBhH,EAEE6J,IACF1B,EAAeJ,EAAOd,gBAAkB4C,GAuB5C,GAlBI,qBAAsBxB,GACxB9C,EAAM1J,QAAQsM,GAAgB,SAA0B5M,EAAKZ,QAChC,IAAhBuN,GAAqD,iBAAtBvN,EAAIqP,qBAErC7B,EAAexN,GAGtB0N,EAAQ4B,iBAAiBtP,EAAKY,MAM/BgK,EAAM/J,YAAYuM,EAAO+B,mBAC5BzB,EAAQyB,kBAAoB/B,EAAO+B,iBAIjC/B,EAAOuB,aACT,IACEjB,EAAQiB,aAAevB,EAAOuB,aAC9B,MAAOpK,GAGP,GAA4B,SAAxB6I,EAAOuB,aACT,MAAMpK,EAM6B,mBAA9B6I,EAAOmC,oBAChB7B,EAAQ8B,iBAAiB,WAAYpC,EAAOmC,oBAIP,mBAA5BnC,EAAOqC,kBAAmC/B,EAAQgC,QAC3DhC,EAAQgC,OAAOF,iBAAiB,WAAYpC,EAAOqC,kBAGjDrC,EAAOuC,aAETvC,EAAOuC,YAAYC,QAAQC,MAAK,SAAoBC,GAC7CpC,IAILA,EAAQqC,QACRzC,EAAOwC,GAEPpC,EAAU,SAITH,IACHA,EAAc,MAIhBG,EAAQsC,KAAKzC,Q,6BCrLjB,IAAI0C,EAAexP,EAAQ,IAY3BjC,EAAOD,QAAU,SAAqB2R,EAAS9C,EAAQ+C,EAAMzC,EAASgB,GACpE,IAAI0B,EAAQ,IAAIjK,MAAM+J,GACtB,OAAOD,EAAaG,EAAOhD,EAAQ+C,EAAMzC,EAASgB,K,6BCdpD,IAAI9D,EAAQnK,EAAQ,GAUpBjC,EAAOD,QAAU,SAAqB8R,EAASC,GAE7CA,EAAUA,GAAW,GACrB,IAAIlD,EAAS,GAETmD,EAAuB,CAAC,MAAO,SAAU,QACzCC,EAA0B,CAAC,UAAW,OAAQ,QAAS,UACvDC,EAAuB,CACzB,UAAW,mBAAoB,oBAAqB,mBACpD,UAAW,iBAAkB,kBAAmB,UAAW,eAAgB,iBAC3E,iBAAkB,mBAAoB,qBAAsB,aAC5D,mBAAoB,gBAAiB,eAAgB,YAAa,YAClE,aAAc,cAAe,aAAc,oBAEzCC,EAAkB,CAAC,kBAEvB,SAASC,EAAeC,EAAQC,GAC9B,OAAIjG,EAAM7J,cAAc6P,IAAWhG,EAAM7J,cAAc8P,GAC9CjG,EAAM/H,MAAM+N,EAAQC,GAClBjG,EAAM7J,cAAc8P,GACtBjG,EAAM/H,MAAM,GAAIgO,GACdjG,EAAMjK,QAAQkQ,GAChBA,EAAO7N,QAET6N,EAGT,SAASC,EAAoBC,GACtBnG,EAAM/J,YAAYyP,EAAQS,IAEnBnG,EAAM/J,YAAYwP,EAAQU,MACpC3D,EAAO2D,GAAQJ,OAAetL,EAAWgL,EAAQU,KAFjD3D,EAAO2D,GAAQJ,EAAeN,EAAQU,GAAOT,EAAQS,IAMzDnG,EAAM1J,QAAQqP,GAAsB,SAA0BQ,GACvDnG,EAAM/J,YAAYyP,EAAQS,MAC7B3D,EAAO2D,GAAQJ,OAAetL,EAAWiL,EAAQS,QAIrDnG,EAAM1J,QAAQsP,EAAyBM,GAEvClG,EAAM1J,QAAQuP,GAAsB,SAA0BM,GACvDnG,EAAM/J,YAAYyP,EAAQS,IAEnBnG,EAAM/J,YAAYwP,EAAQU,MACpC3D,EAAO2D,GAAQJ,OAAetL,EAAWgL,EAAQU,KAFjD3D,EAAO2D,GAAQJ,OAAetL,EAAWiL,EAAQS,OAMrDnG,EAAM1J,QAAQwP,GAAiB,SAAeK,GACxCA,KAAQT,EACVlD,EAAO2D,GAAQJ,EAAeN,EAAQU,GAAOT,EAAQS,IAC5CA,KAAQV,IACjBjD,EAAO2D,GAAQJ,OAAetL,EAAWgL,EAAQU,QAIrD,IAAIC,EAAYT,EACbhI,OAAOiI,GACPjI,OAAOkI,GACPlI,OAAOmI,GAENO,EAAY9R,OACb+R,KAAKb,GACL9H,OAAOpJ,OAAO+R,KAAKZ,IACnBa,QAAO,SAAyBnR,GAC/B,OAAmC,IAA5BgR,EAAUzF,QAAQvL,MAK7B,OAFA4K,EAAM1J,QAAQ+P,EAAWH,GAElB1D,I,6BC7ET,SAASgE,EAAOlB,GACdmB,KAAKnB,QAAUA,EAGjBkB,EAAO/Q,UAAUK,SAAW,WAC1B,MAAO,UAAY2Q,KAAKnB,QAAU,KAAOmB,KAAKnB,QAAU,KAG1DkB,EAAO/Q,UAAUmL,YAAa,EAE9BhN,EAAOD,QAAU6S,G,kQChBjB,IAAItN,EAAUrD,EAAQ,GAElB6Q,EAAMnS,OAAOkB,UAAUC,eACvBK,EAAU6E,MAAM7E,QAEhB4Q,EAAY,WAEZ,IADA,IAAIC,EAAQ,GACH/S,EAAI,EAAGA,EAAI,MAAOA,EACvB+S,EAAMpG,KAAK,MAAQ3M,EAAI,GAAK,IAAM,IAAMA,EAAEiC,SAAS,KAAK0N,eAG5D,OAAOoD,EANK,GA4BZC,EAAgB,SAAuBZ,EAAQa,GAE/C,IADA,IAAIvQ,EAAMuQ,GAAWA,EAAQC,aAAexS,OAAOY,OAAO,MAAQ,GACzDtB,EAAI,EAAGA,EAAIoS,EAAOxP,SAAU5C,OACR,IAAdoS,EAAOpS,KACd0C,EAAI1C,GAAKoS,EAAOpS,IAIxB,OAAO0C,GAoMX3C,EAAOD,QAAU,CACbkT,cAAeA,EACfG,OA1IS,SAA4BhB,EAAQC,GAC7C,OAAO1R,OAAO+R,KAAKL,GAAQgB,QAAO,SAAUC,EAAK9R,GAE7C,OADA8R,EAAI9R,GAAO6Q,EAAO7Q,GACX8R,IACRlB,IAuIHmB,QAlBU,SAAiB5O,EAAGC,GAC9B,MAAO,GAAGmF,OAAOpF,EAAGC,IAkBpB4O,QAvDU,SAAiBtS,GAI3B,IAHA,IAAIuS,EAAQ,CAAC,CAAE9Q,IAAK,CAAEjC,EAAGQ,GAASqR,KAAM,MACpCmB,EAAO,GAEFzT,EAAI,EAAGA,EAAIwT,EAAM5Q,SAAU5C,EAKhC,IAJA,IAAI0T,EAAOF,EAAMxT,GACb0C,EAAMgR,EAAKhR,IAAIgR,EAAKpB,MAEpBG,EAAO/R,OAAO+R,KAAK/P,GACdiR,EAAI,EAAGA,EAAIlB,EAAK7P,SAAU+Q,EAAG,CAClC,IAAIpS,EAAMkR,EAAKkB,GACXxR,EAAMO,EAAInB,GACK,WAAf,EAAOY,IAA4B,OAARA,IAAuC,IAAvBsR,EAAK3G,QAAQ3K,KACxDqR,EAAM7G,KAAK,CAAEjK,IAAKA,EAAK4P,KAAM/Q,IAC7BkS,EAAK9G,KAAKxK,IAOtB,OAjMe,SAAsBqR,GACrC,KAAOA,EAAM5Q,OAAS,GAAG,CACrB,IAAI8Q,EAAOF,EAAMI,MACblR,EAAMgR,EAAKhR,IAAIgR,EAAKpB,MAExB,GAAIpQ,EAAQQ,GAAM,CAGd,IAFA,IAAImR,EAAY,GAEPF,EAAI,EAAGA,EAAIjR,EAAIE,SAAU+Q,OACR,IAAXjR,EAAIiR,IACXE,EAAUlH,KAAKjK,EAAIiR,IAI3BD,EAAKhR,IAAIgR,EAAKpB,MAAQuB,IAiL9BC,CAAaN,GAENvS,GAmCP8S,OAtIS,SAAUjP,EAAKkP,EAASC,GACjC,IAAIC,EAAiBpP,EAAIC,QAAQ,MAAO,KACxC,GAAgB,eAAZkP,EAEA,OAAOC,EAAenP,QAAQ,iBAAkBsK,UAGpD,IACI,OAAO9H,mBAAmB2M,GAC5B,MAAOpO,GACL,OAAOoO,IA6HX9H,OAzHS,SAAgBtH,EAAKqP,EAAgBF,EAASG,EAAMC,GAG7D,GAAmB,IAAfvP,EAAIlC,OACJ,OAAOkC,EAGX,IAAIyF,EAASzF,EAOb,GANmB,WAAf,EAAOA,GACPyF,EAASxJ,OAAOa,UAAUK,SAAS9B,KAAK2E,GAClB,iBAARA,IACdyF,EAASpB,OAAOrE,IAGJ,eAAZmP,EACA,OAAOK,OAAO/J,GAAQxF,QAAQ,mBAAmB,SAAUwP,GACvD,MAAO,SAAW7L,SAAS6L,EAAGhQ,MAAM,GAAI,IAAM,SAKtD,IADA,IAAIiQ,EAAM,GACDxU,EAAI,EAAGA,EAAIuK,EAAO3H,SAAU5C,EAAG,CACpC,IAAIK,EAAIkK,EAAOrF,WAAWlF,GAGhB,KAANK,GACS,KAANA,GACM,KAANA,GACM,MAANA,GACCA,GAAK,IAAQA,GAAK,IAClBA,GAAK,IAAQA,GAAK,IAClBA,GAAK,IAAQA,GAAK,KAClBgU,IAAWhP,EAAQ2G,UAAkB,KAAN3L,GAAoB,KAANA,GAEjDmU,GAAOjK,EAAOkK,OAAOzU,GAIrBK,EAAI,IACJmU,GAAY1B,EAASzS,GAIrBA,EAAI,KACJmU,GAAa1B,EAAS,IAAQzS,GAAK,GAAMyS,EAAS,IAAY,GAAJzS,GAI1DA,EAAI,OAAUA,GAAK,MACnBmU,GAAa1B,EAAS,IAAQzS,GAAK,IAAOyS,EAAS,IAASzS,GAAK,EAAK,IAASyS,EAAS,IAAY,GAAJzS,IAIpGL,GAAK,EACLK,EAAI,QAAiB,KAAJA,IAAc,GAA8B,KAAvBkK,EAAOrF,WAAWlF,IACxDwU,GAAO1B,EAAS,IAAQzS,GAAK,IACvByS,EAAS,IAASzS,GAAK,GAAM,IAC7ByS,EAAS,IAASzS,GAAK,EAAK,IAC5ByS,EAAS,IAAY,GAAJzS,IAG3B,OAAOmU,GA6DP1R,SA9BW,SAAkBJ,GAC7B,SAAKA,GAAsB,WAAf,EAAOA,QAITA,EAAIK,aAAeL,EAAIK,YAAYD,UAAYJ,EAAIK,YAAYD,SAASJ,KA0BlFgS,SAnCW,SAAkBhS,GAC7B,MAA+C,oBAAxChC,OAAOkB,UAAUK,SAAS9B,KAAKuC,IAmCtCiS,SApBW,SAAkBxS,EAAKQ,GAClC,GAAIT,EAAQC,GAAM,CAEd,IADA,IAAIyS,EAAS,GACJ5U,EAAI,EAAGA,EAAImC,EAAIS,OAAQ5C,GAAK,EACjC4U,EAAOjI,KAAKhK,EAAGR,EAAInC,KAEvB,OAAO4U,EAEX,OAAOjS,EAAGR,IAaViC,MA3MQ,SAASA,EAAM+N,EAAQC,EAAQa,GAEvC,IAAKb,EACD,OAAOD,EAGX,GAAsB,WAAlB,EAAOC,GAAqB,CAC5B,GAAIlQ,EAAQiQ,GACRA,EAAOxF,KAAKyF,OACT,KAAID,GAA4B,WAAlB,EAAOA,GAKxB,MAAO,CAACA,EAAQC,IAJXa,IAAYA,EAAQC,cAAgBD,EAAQ4B,mBAAsBhC,EAAI1S,KAAKO,OAAOkB,UAAWwQ,MAC9FD,EAAOC,IAAU,GAMzB,OAAOD,EAGX,IAAKA,GAA4B,WAAlB,EAAOA,GAClB,MAAO,CAACA,GAAQrI,OAAOsI,GAG3B,IAAI0C,EAAc3C,EAKlB,OAJIjQ,EAAQiQ,KAAYjQ,EAAQkQ,KAC5B0C,EAAc9B,EAAcb,EAAQc,IAGpC/Q,EAAQiQ,IAAWjQ,EAAQkQ,IAC3BA,EAAO3P,SAAQ,SAAUiR,EAAM1T,GAC3B,GAAI6S,EAAI1S,KAAKgS,EAAQnS,GAAI,CACrB,IAAI+U,EAAa5C,EAAOnS,GACpB+U,GAAoC,WAAtB,EAAOA,IAA2BrB,GAAwB,WAAhB,EAAOA,GAC/DvB,EAAOnS,GAAKoE,EAAM2Q,EAAYrB,EAAMT,GAEpCd,EAAOxF,KAAK+G,QAGhBvB,EAAOnS,GAAK0T,KAGbvB,GAGJzR,OAAO+R,KAAKL,GAAQgB,QAAO,SAAUC,EAAK9R,GAC7C,IAAIN,EAAQmR,EAAO7Q,GAOnB,OALIsR,EAAI1S,KAAKkT,EAAK9R,GACd8R,EAAI9R,GAAO6C,EAAMiP,EAAI9R,GAAMN,EAAOgS,GAElCI,EAAI9R,GAAON,EAERoS,IACRyB,M,wBC1FYE,E;;;;;;;;;GAAAA,EAIV,WAAc,aAErB,SAASC,EAAQvT,EAAQwT,GACvB,IAAIzC,EAAO/R,OAAO+R,KAAK/Q,GAEvB,GAAIhB,OAAOyU,sBAAuB,CAChC,IAAIC,EAAU1U,OAAOyU,sBAAsBzT,GAEvCwT,IACFE,EAAUA,EAAQ1C,QAAO,SAAU2C,GACjC,OAAO3U,OAAOsF,yBAAyBtE,EAAQ2T,GAAKzU,eAIxD6R,EAAK9F,KAAK3C,MAAMyI,EAAM2C,GAGxB,OAAO3C,EAGT,SAAS6C,EAAenD,GACtB,IAAK,IAAInS,EAAI,EAAGA,EAAIwE,UAAU5B,OAAQ5C,IAAK,CACzC,IAAIoS,EAAyB,MAAhB5N,UAAUxE,GAAawE,UAAUxE,GAAK,GAE/CA,EAAI,EACNiV,EAAQvU,OAAO0R,IAAS,GAAM3P,SAAQ,SAAUlB,GAC9CgU,EAAgBpD,EAAQ5Q,EAAK6Q,EAAO7Q,OAE7Bb,OAAO8U,0BAChB9U,OAAO+U,iBAAiBtD,EAAQzR,OAAO8U,0BAA0BpD,IAEjE6C,EAAQvU,OAAO0R,IAAS3P,SAAQ,SAAUlB,GACxCb,OAAOC,eAAewR,EAAQ5Q,EAAKb,OAAOsF,yBAAyBoM,EAAQ7Q,OAKjF,OAAO4Q,EAGT,SAASuD,EAAQhT,GAaf,OATEgT,EADoB,mBAAX3U,QAAoD,iBAApBA,OAAOiG,SACtC,SAAUtE,GAClB,cAAcA,GAGN,SAAUA,GAClB,OAAOA,GAAyB,mBAAX3B,QAAyB2B,EAAIK,cAAgBhC,QAAU2B,IAAQ3B,OAAOa,UAAY,gBAAkBc,IAI9GA,GAGjB,SAASiT,EAAgBC,EAAUC,GACjC,KAAMD,aAAoBC,GACxB,MAAM,IAAIlQ,UAAU,qCAIxB,SAASmQ,EAAkB3D,EAAQ4D,GACjC,IAAK,IAAI/V,EAAI,EAAGA,EAAI+V,EAAMnT,OAAQ5C,IAAK,CACrC,IAAIgW,EAAaD,EAAM/V,GACvBgW,EAAWpV,WAAaoV,EAAWpV,aAAc,EACjDoV,EAAWC,cAAe,EACtB,UAAWD,IAAYA,EAAWE,UAAW,GACjDxV,OAAOC,eAAewR,EAAQ6D,EAAWzU,IAAKyU,IAUlD,SAAST,EAAgB7S,EAAKnB,EAAKN,GAYjC,OAXIM,KAAOmB,EACThC,OAAOC,eAAe+B,EAAKnB,EAAK,CAC9BN,MAAOA,EACPL,YAAY,EACZqV,cAAc,EACdC,UAAU,IAGZxT,EAAInB,GAAON,EAGNyB,EAGT,SAASyT,EAAmBC,GAC1B,OAGF,SAA4BA,GAC1B,GAAIrP,MAAM7E,QAAQkU,GAAM,OAAOC,EAAkBD,GAJ1CE,CAAmBF,IAO5B,SAA0BG,GACxB,GAAsB,oBAAXxV,QAAmD,MAAzBwV,EAAKxV,OAAOiG,WAA2C,MAAtBuP,EAAK,cAAuB,OAAOxP,MAAMyP,KAAKD,GARlFE,CAAiBL,IAWrD,SAAqC3V,EAAGiW,GACtC,GAAKjW,EAAL,CACA,GAAiB,iBAANA,EAAgB,OAAO4V,EAAkB5V,EAAGiW,GACvD,IAAIjV,EAAIf,OAAOkB,UAAUK,SAAS9B,KAAKM,GAAG8D,MAAM,GAAI,GAEpD,MADU,WAAN9C,GAAkBhB,EAAEsC,cAAatB,EAAIhB,EAAEsC,YAAYxC,MAC7C,QAANkB,GAAqB,QAANA,EAAoBsF,MAAMyP,KAAK/V,GACxC,cAANgB,GAAqB,2CAA2CkV,KAAKlV,GAAW4U,EAAkB5V,EAAGiW,QAAzG,GAjB2DE,CAA4BR,IA4BzF,WACE,MAAM,IAAIzQ,UAAU,wIA7B2EkR,GAoBjG,SAASR,EAAkBD,EAAKU,IACnB,MAAPA,GAAeA,EAAMV,EAAIxT,UAAQkU,EAAMV,EAAIxT,QAE/C,IAAK,IAAI5C,EAAI,EAAG+W,EAAO,IAAIhQ,MAAM+P,GAAM9W,EAAI8W,EAAK9W,IAAK+W,EAAK/W,GAAKoW,EAAIpW,GAEnE,OAAO+W,EAOT,IAAIC,EAA+B,oBAAX9S,aAAqD,IAApBA,OAAOC,SAC5D8S,EAASD,EAAa9S,OAAS,GAC/BgT,KAAkBF,IAAcC,EAAO9S,SAASgT,kBAAkB,iBAAkBF,EAAO9S,SAASgT,gBACpGC,IAAoBJ,GAAa,iBAAkBC,EAgBnDI,EAAa,GAAGvN,OAfJ,UAesB,SAClCwN,EAAiB,GAAGxN,OAhBR,UAgB0B,aACtCyN,EAAe,GAAGzN,OAjBN,UAiBwB,WACpC0N,EAAa,GAAG1N,OAlBJ,UAkBsB,SAClC2N,EAAkB,GAAG3N,OAnBT,UAmB2B,cACvC4N,EAAc,GAAG5N,OApBL,UAoBuB,UACnC6N,EAAa,GAAG7N,OArBJ,UAqBsB,SAElC8N,EAAc,GAAG9N,OAvBL,UAuBuB,UACnC+N,EAAe,GAAG/N,OAxBN,UAwBwB,WAcpCgO,EAAqBV,EAAoB,cAHrBF,EAAkB,aAAe,YAIrDa,EAAqBX,EAAoB,cAHtBF,EAAkB,YAAc,YAInDc,EAAmBZ,EAAoB,0BAHrBF,EAAkB,uBAAyB,UAW7De,EAAiB,2CACjBC,EAAkB,SAClBC,EAAuB,4BACvBC,EAAkB,gBAMlBC,EAAW,CAEbC,SAAU,EAGVC,SApCmB,OAuCnBC,mBAAoBC,IAEpBC,YAAaD,IAEbhL,KAAM,KAENkL,QAAS,GAETC,YAAY,EAEZC,SAAS,EAETC,kBAAkB,EAElBC,kBAAkB,EAElBC,OAAO,EAEPC,QAAQ,EAERC,QAAQ,EAERC,WAAW,EAEXC,YAAY,EAEZC,UAAU,EAEVC,aAAc,GAEdC,SAAS,EAETC,WAAW,EAEXC,UAAU,EAEVC,UAAU,EAEVC,aAAa,EAEbC,aAAa,EAEbC,eAAgB,GAEhBC,gBAAgB,EAEhBC,kBAAkB,EAElBC,0BAA0B,EAE1BC,eAAgB,EAChBC,gBAAiB,EACjBC,gBAAiB,EACjBC,iBAAkB,EAClBC,kBAjEwB,IAkExBC,mBAjEyB,IAmEzBC,MAAO,KACPC,UAAW,KACXC,SAAU,KACVC,QAAS,KACTC,KAAM,KACNC,KAAM,MASJxS,EAAQI,OAAOJ,OAAS6O,EAAO7O,MAOnC,SAAS7E,EAAStC,GAChB,MAAwB,iBAAVA,IAAuBmH,EAAMnH,GAQ7C,IAAI4Z,EAAmB,SAA0B5Z,GAC/C,OAAOA,EAAQ,GAAKA,EAAQ6Z,KAQ9B,SAAS1Y,EAAYnB,GACnB,YAAwB,IAAVA,EAQhB,SAASoB,EAASpB,GAChB,MAA0B,WAAnByU,EAAQzU,IAAiC,OAAVA,EAExC,IAAIY,EAAiBnB,OAAOkB,UAAUC,eAOtC,SAASS,EAAcrB,GACrB,IAAKoB,EAASpB,GACZ,OAAO,EAGT,IACE,IAAI8Z,EAAe9Z,EAAM8B,YACrBnB,EAAYmZ,EAAanZ,UAC7B,OAAOmZ,GAAgBnZ,GAAaC,EAAe1B,KAAKyB,EAAW,iBACnE,MAAO+P,GACP,OAAO,GASX,SAASnP,EAAWvB,GAClB,MAAwB,mBAAVA,EAEhB,IAAIsD,EAAQwC,MAAMnF,UAAU2C,MAO5B,SAASyW,EAAQ/Z,GACf,OAAO8F,MAAMyP,KAAOzP,MAAMyP,KAAKvV,GAASsD,EAAMpE,KAAKc,GASrD,SAASwB,EAAQgL,EAAMwN,GAerB,OAdIxN,GAAQjL,EAAWyY,KACjBlU,MAAM7E,QAAQuL,IAASlK,EAASkK,EAAK7K,QAGrCoY,EAAQvN,GAAMhL,SAAQ,SAAUxB,EAAOM,GACrC0Z,EAAS9a,KAAKsN,EAAMxM,EAAOM,EAAKkM,MAEzBpL,EAASoL,IACpB/M,OAAO+R,KAAKhF,GAAMhL,SAAQ,SAAUlB,GAClC0Z,EAAS9a,KAAKsN,EAAMA,EAAKlM,GAAMA,EAAKkM,OAKnCA,EAST,IAAI0F,EAASzS,OAAOyS,QAAU,SAAgBhB,GAC5C,IAAK,IAAI+I,EAAO1W,UAAU5B,OAAQsJ,EAAO,IAAInF,MAAMmU,EAAO,EAAIA,EAAO,EAAI,GAAIC,EAAO,EAAGA,EAAOD,EAAMC,IAClGjP,EAAKiP,EAAO,GAAK3W,UAAU2W,GAa7B,OAVI9Y,EAAS8P,IAAWjG,EAAKtJ,OAAS,GACpCsJ,EAAKzJ,SAAQ,SAAU2Y,GACjB/Y,EAAS+Y,IACX1a,OAAO+R,KAAK2I,GAAK3Y,SAAQ,SAAUlB,GACjC4Q,EAAO5Q,GAAO6Z,EAAI7Z,SAMnB4Q,GAELkJ,EAAkB,uBAStB,SAASC,EAAuBra,GAC9B,IAAIsa,EAAQ/W,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK,KAChF,OAAO6W,EAAgB1E,KAAK1V,GAASsH,KAAKiT,MAAMva,EAAQsa,GAASA,EAAQta,EAE3E,IAAIwa,EAAgB,+CAOpB,SAASC,EAASC,EAASC,GACzB,IAAIC,EAAQF,EAAQE,MACpBpZ,EAAQmZ,GAAQ,SAAU3a,EAAOU,GAC3B8Z,EAAc9E,KAAKhV,IAAa4B,EAAStC,KAC3CA,EAAQ,GAAG6I,OAAO7I,EAAO,OAG3B4a,EAAMla,GAAYV,KAmBtB,SAAS6a,EAASH,EAAS1a,GACzB,GAAKA,EAIL,GAAIsC,EAASoY,EAAQ/Y,QACnBH,EAAQkZ,GAAS,SAAUI,GACzBD,EAASC,EAAM9a,WAKnB,GAAI0a,EAAQK,UACVL,EAAQK,UAAUC,IAAIhb,OADxB,CAKA,IAAIib,EAAYP,EAAQO,UAAUrX,OAE7BqX,EAEMA,EAAUpP,QAAQ7L,GAAS,IACpC0a,EAAQO,UAAY,GAAGpS,OAAOoS,EAAW,KAAKpS,OAAO7I,IAFrD0a,EAAQO,UAAYjb,GAWxB,SAASkb,EAAYR,EAAS1a,GACvBA,IAIDsC,EAASoY,EAAQ/Y,QACnBH,EAAQkZ,GAAS,SAAUI,GACzBI,EAAYJ,EAAM9a,MAKlB0a,EAAQK,UACVL,EAAQK,UAAUI,OAAOnb,GAIvB0a,EAAQO,UAAUpP,QAAQ7L,IAAU,IACtC0a,EAAQO,UAAYP,EAAQO,UAAUnX,QAAQ9D,EAAO,MAUzD,SAASob,EAAYV,EAAS1a,EAAOqb,GAC9Brb,IAIDsC,EAASoY,EAAQ/Y,QACnBH,EAAQkZ,GAAS,SAAUI,GACzBM,EAAYN,EAAM9a,EAAOqb,MAMzBA,EACFR,EAASH,EAAS1a,GAElBkb,EAAYR,EAAS1a,IAGzB,IAAIsb,EAAoB,oBAOxB,SAASC,EAAYvb,GACnB,OAAOA,EAAM8D,QAAQwX,EAAmB,SAAS3L,cASnD,SAAS6L,EAAQd,EAASpb,GACxB,OAAI8B,EAASsZ,EAAQpb,IACZob,EAAQpb,GAGbob,EAAQe,QACHf,EAAQe,QAAQnc,GAGlBob,EAAQgB,aAAa,QAAQ7S,OAAO0S,EAAYjc,KASzD,SAASqc,EAAQjB,EAASpb,EAAMkN,GAC1BpL,EAASoL,GACXkO,EAAQpb,GAAQkN,EACPkO,EAAQe,QACjBf,EAAQe,QAAQnc,GAAQkN,EAExBkO,EAAQkB,aAAa,QAAQ/S,OAAO0S,EAAYjc,IAAQkN,GA2B5D,IAAIqP,EAAgB,QAEhBC,EAAgB,WAClB,IAAIC,GAAY,EAEhB,GAAIhG,EAAY,CACd,IAAIiG,GAAO,EAEPC,EAAW,aAEXjK,EAAUvS,OAAOC,eAAe,GAAI,OAAQ,CAC9CE,IAAK,WAEH,OADAmc,GAAY,EACLC,GAQTE,IAAK,SAAalc,GAChBgc,EAAOhc,KAGXgW,EAAOlG,iBAAiB,OAAQmM,EAAUjK,GAC1CgE,EAAOmG,oBAAoB,OAAQF,EAAUjK,GAG/C,OAAO+J,EA3BW,GAsCpB,SAASK,GAAe1B,EAAS3M,EAAMkO,GACrC,IAAIjK,EAAUzO,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK,GAC9E8Y,EAAUJ,EACdlO,EAAKnK,OAAO0Y,MAAMT,GAAera,SAAQ,SAAU+a,GACjD,IAAKT,EAAe,CAClB,IAAIU,EAAY9B,EAAQ8B,UAEpBA,GAAaA,EAAUD,IAAUC,EAAUD,GAAON,KACpDI,EAAUG,EAAUD,GAAON,UACpBO,EAAUD,GAAON,GAEqB,IAAzCxc,OAAO+R,KAAKgL,EAAUD,IAAQ5a,eACzB6a,EAAUD,GAGmB,IAAlC9c,OAAO+R,KAAKgL,GAAW7a,eAClB+Y,EAAQ8B,WAKrB9B,EAAQyB,oBAAoBI,EAAOF,EAASrK,MAWhD,SAASyK,GAAY/B,EAAS3M,EAAMkO,GAClC,IAAIjK,EAAUzO,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK,GAC9EmZ,EAAWT,EACflO,EAAKnK,OAAO0Y,MAAMT,GAAera,SAAQ,SAAU+a,GACjD,GAAIvK,EAAQgK,OAASF,EAAe,CAClC,IAAIa,EAAqBjC,EAAQ8B,UAC7BA,OAAmC,IAAvBG,EAAgC,GAAKA,EAErDD,EAAW,kBACFF,EAAUD,GAAON,GACxBvB,EAAQyB,oBAAoBI,EAAOG,EAAU1K,GAE7C,IAAK,IAAI4K,EAAQrZ,UAAU5B,OAAQsJ,EAAO,IAAInF,MAAM8W,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACpF5R,EAAK4R,GAAStZ,UAAUsZ,GAG1BZ,EAASlT,MAAM2R,EAASzP,IAGrBuR,EAAUD,KACbC,EAAUD,GAAS,IAGjBC,EAAUD,GAAON,IACnBvB,EAAQyB,oBAAoBI,EAAOC,EAAUD,GAAON,GAAWjK,GAGjEwK,EAAUD,GAAON,GAAYS,EAC7BhC,EAAQ8B,UAAYA,EAGtB9B,EAAQ5K,iBAAiByM,EAAOG,EAAU1K,MAW9C,SAAS8K,GAAcpC,EAAS3M,EAAMvB,GACpC,IAAI+P,EAaJ,OAXIhb,EAAWwb,QAAUxb,EAAWyb,aAClCT,EAAQ,IAAIS,YAAYjP,EAAM,CAC5BkP,OAAQzQ,EACR0Q,SAAS,EACTC,YAAY,KAGdZ,EAAQrZ,SAASka,YAAY,gBACvBC,gBAAgBtP,GAAM,GAAM,EAAMvB,GAGnCkO,EAAQoC,cAAcP,GAQ/B,SAASe,GAAU5C,GACjB,IAAI6C,EAAM7C,EAAQ8C,wBAClB,MAAO,CACLC,KAAMF,EAAIE,MAAQxa,OAAOya,YAAcxa,SAASgT,gBAAgByH,YAChEC,IAAKL,EAAIK,KAAO3a,OAAO4a,YAAc3a,SAASgT,gBAAgB4H,YAGlE,IAAIC,GAAW/H,EAAO+H,SAClBC,GAAiB,gCAOrB,SAASC,GAAiB7S,GACxB,IAAIjB,EAAQiB,EAAI3B,MAAMuU,IACtB,OAAiB,OAAV7T,IAAmBA,EAAM,KAAO4T,GAASG,UAAY/T,EAAM,KAAO4T,GAASI,UAAYhU,EAAM,KAAO4T,GAASK,MAQtH,SAASC,GAAajT,GACpB,IAAIkT,EAAY,aAAazV,QAAO,IAAIzC,MAAOmY,WAC/C,OAAOnT,IAA6B,IAAtBA,EAAIS,QAAQ,KAAc,IAAM,KAAOyS,EAQvD,SAASE,GAAcC,GACrB,IAAIC,EAASD,EAAKC,OACdC,EAASF,EAAKE,OACdC,EAASH,EAAKG,OACdC,EAAaJ,EAAKI,WAClBC,EAAaL,EAAKK,WAClBC,EAAS,GAETzc,EAASuc,IAA8B,IAAfA,GAC1BE,EAAOrT,KAAK,cAAc7C,OAAOgW,EAAY,QAG3Cvc,EAASwc,IAA8B,IAAfA,GAC1BC,EAAOrT,KAAK,cAAc7C,OAAOiW,EAAY,QAI3Cxc,EAASoc,IAAsB,IAAXA,GACtBK,EAAOrT,KAAK,UAAU7C,OAAO6V,EAAQ,SAGnCpc,EAASqc,IAAsB,IAAXA,GACtBI,EAAOrT,KAAK,UAAU7C,OAAO8V,EAAQ,MAGnCrc,EAASsc,IAAsB,IAAXA,GACtBG,EAAOrT,KAAK,UAAU7C,OAAO+V,EAAQ,MAGvC,IAAII,EAAYD,EAAOpd,OAASod,EAAOpT,KAAK,KAAO,OACnD,MAAO,CACLsT,gBAAiBD,EACjBE,YAAaF,EACbA,UAAWA,GAsCf,SAASG,GAAWC,EAAOC,GACzB,IAAIC,EAAQF,EAAME,MACdC,EAAQH,EAAMG,MACdC,EAAM,CACRC,KAAMH,EACNI,KAAMH,GAER,OAAOF,EAAUG,EAAMnL,EAAe,CACpCsL,OAAQL,EACRM,OAAQL,GACPC,GAiCL,SAASK,GAAiBC,GAExB,IAAIrI,EAAcqI,EAAMrI,YACpBsI,EAASD,EAAMC,OACfC,EAAQF,EAAME,MACdjS,EAAOxK,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK,UAC3E0c,EAAerG,EAAiBoG,GAChCE,EAAgBtG,EAAiBmG,GAErC,GAAIE,GAAgBC,EAAe,CACjC,IAAIC,EAAgBJ,EAAStI,EAEhB,YAAT1J,GAAsBoS,EAAgBH,GAAkB,UAATjS,GAAoBoS,EAAgBH,EACrFD,EAASC,EAAQvI,EAEjBuI,EAAQD,EAAStI,OAEVwI,EACTF,EAASC,EAAQvI,EACRyI,IACTF,EAAQD,EAAStI,GAGnB,MAAO,CACLuI,MAAOA,EACPD,OAAQA,GA4CZ,SAASK,GAAgBC,EAAOC,EAAOC,EAAOC,GAC5C,IAAIC,EAAmBH,EAAM7I,YACzBiJ,EAAoBJ,EAAMK,aAC1BC,EAAqBN,EAAMO,cAC3BC,EAAeR,EAAM5B,OACrBA,OAA0B,IAAjBoC,EAA0B,EAAIA,EACvCC,EAAeT,EAAM3B,OACrBA,OAA0B,IAAjBoC,EAA0B,EAAIA,EACvCC,EAAeV,EAAM1B,OACrBA,OAA0B,IAAjBoC,EAA0B,EAAIA,EACvCvJ,EAAc8I,EAAM9I,YACpBkJ,EAAeJ,EAAMI,aACrBE,EAAgBN,EAAMM,cACtBI,EAAkBT,EAAMU,UACxBA,OAAgC,IAApBD,EAA6B,cAAgBA,EACzDE,EAAwBX,EAAMY,sBAC9BA,OAAkD,IAA1BD,GAA0CA,EAClEE,EAAwBb,EAAMc,sBAC9BA,OAAkD,IAA1BD,EAAmC,MAAQA,EACnEE,EAAiBf,EAAMgB,SACvBA,OAA8B,IAAnBD,EAA4B1H,IAAW0H,EAClDE,EAAkBjB,EAAMkB,UACxBA,OAAgC,IAApBD,EAA6B5H,IAAW4H,EACpDE,EAAiBnB,EAAMoB,SACvBA,OAA8B,IAAnBD,EAA4B,EAAIA,EAC3CE,EAAkBrB,EAAMsB,UACxBA,OAAgC,IAApBD,EAA6B,EAAIA,EAC7CE,EAAS7e,SAAS8e,cAAc,UAChCC,EAAUF,EAAOG,WAAW,MAC5BC,EAAWtC,GAAiB,CAC9BpI,YAAaA,EACbuI,MAAOwB,EACPzB,OAAQ2B,IAENU,EAAWvC,GAAiB,CAC9BpI,YAAaA,EACbuI,MAAO4B,EACP7B,OAAQ+B,GACP,SACC9B,EAAQ1Y,KAAK+a,IAAIF,EAASnC,MAAO1Y,KAAKgb,IAAIF,EAASpC,MAAOW,IAC1DZ,EAASzY,KAAK+a,IAAIF,EAASpC,OAAQzY,KAAKgb,IAAIF,EAASrC,OAAQc,IAG7D0B,EAAe1C,GAAiB,CAClCpI,YAAagJ,EACbT,MAAOwB,EACPzB,OAAQ2B,IAENc,EAAe3C,GAAiB,CAClCpI,YAAagJ,EACbT,MAAO4B,EACP7B,OAAQ+B,GACP,SACCW,EAAYnb,KAAK+a,IAAIE,EAAavC,MAAO1Y,KAAKgb,IAAIE,EAAaxC,MAAOU,IACtEgC,EAAapb,KAAK+a,IAAIE,EAAaxC,OAAQzY,KAAKgb,IAAIE,EAAazC,OAAQa,IACzEvV,EAAS,EAAEoX,EAAY,GAAIC,EAAa,EAAGD,EAAWC,GAe1D,OAdAX,EAAO/B,MAAQ3F,EAAuB2F,GACtC+B,EAAOhC,OAAS1F,EAAuB0F,GACvCkC,EAAQU,UAAYzB,EACpBe,EAAQW,SAAS,EAAG,EAAG5C,EAAOD,GAC9BkC,EAAQY,OACRZ,EAAQa,UAAU9C,EAAQ,EAAGD,EAAS,GACtCkC,EAAQvD,OAAOA,EAASpX,KAAKyb,GAAK,KAClCd,EAAQe,MAAMrE,EAAQC,GACtBqD,EAAQb,sBAAwBA,EAChCa,EAAQX,sBAAwBA,EAChCW,EAAQgB,UAAUla,MAAMkZ,EAAS,CAAC5B,GAAOxX,OAAOqM,EAAmB7J,EAAO6X,KAAI,SAAUC,GACtF,OAAO7b,KAAK8b,MAAM/I,EAAuB8I,UAE3ClB,EAAQrK,UACDmK,EAET,IAAIsB,GAAenb,OAAOmb,aAmBtBC,GAAuB,YA6C3B,SAASC,GAAuBC,GAC9B,IACIC,EADAC,EAAW,IAAIvd,SAASqd,GAG5B,IACE,IAAIG,EACAC,EACAC,EAEJ,GAA6B,MAAzBH,EAASI,SAAS,IAAwC,MAAzBJ,EAASI,SAAS,GAIrD,IAHA,IAAIniB,EAAS+hB,EAASK,WAClBC,EAAS,EAENA,EAAS,EAAIriB,GAAQ,CAC1B,GAAkC,MAA9B+hB,EAASI,SAASE,IAAsD,MAAlCN,EAASI,SAASE,EAAS,GAAa,CAChFJ,EAAYI,EACZ,MAGFA,GAAU,EAId,GAAIJ,EAAW,CACb,IACIK,EAAaL,EAAY,GAE7B,GAAuD,SAlF7D,SAA+BF,EAAUQ,EAAOviB,GAC9C,IAAIkC,EAAM,GACVlC,GAAUuiB,EAEV,IAAK,IAAInlB,EAAImlB,EAAOnlB,EAAI4C,EAAQ5C,GAAK,EACnC8E,GAAOwf,GAAaK,EAASI,SAAS/kB,IAGxC,OAAO8E,EA0ECsgB,CAAsBT,EAHTE,EAAY,EAGmB,GAAe,CAC7D,IAAIQ,EAAaV,EAASW,UAAUJ,GAGpC,KAFAN,EAA8B,QAAfS,IAEoB,QAAfA,IAGyC,KAArDV,EAASW,UAAUJ,EAAa,EAAGN,GAA0B,CAC/D,IAAIW,EAAiBZ,EAASa,UAAUN,EAAa,EAAGN,GAEpDW,GAAkB,IACpBT,EAAWI,EAAaK,KAOpC,GAAIT,EAAU,CACZ,IAEIW,EAEAzlB,EAJA0lB,EAAUf,EAASW,UAAUR,EAAUF,GAM3C,IAAK5kB,EAAI,EAAGA,EAAI0lB,EAAS1lB,GAAK,EAG5B,GAFAylB,EAAUX,EAAe,GAAJ9kB,EAAS,EAEoB,MAA9C2kB,EAASW,UAAUG,EAASb,GAE9B,CAEEa,GAAW,EAEXf,EAAcC,EAASW,UAAUG,EAASb,GAE1CD,EAASgB,UAAUF,EAAS,EAAGb,GAC/B,QAIR,MAAOjT,GACP+S,EAAc,EAGhB,OAAOA,EA2DT,IAAIkB,GAAS,CACXA,OAAQ,WACNhT,KAAKiT,gBACLjT,KAAKkT,aACLlT,KAAKmT,cACLnT,KAAKoT,eAEDpT,KAAKqT,SACPrT,KAAKsT,iBAGTL,cAAe,WACb,IAAIlK,EAAU/I,KAAK+I,QACf1I,EAAUL,KAAKK,QACfkT,EAAYvT,KAAKuT,UACjBC,EAAUxT,KAAKwT,QACfvD,EAAWra,OAAOyK,EAAQoH,mBAC1B0I,EAAYva,OAAOyK,EAAQqH,oBAC/BwB,EAASsK,EAAS7O,GAClB4E,EAAYR,EAASpE,GACrB,IAAI8O,EAAgB,CAClBpF,MAAO1Y,KAAKgb,IAAI4C,EAAUG,YAAazD,GAAY,EAAIA,EAhhCnC,KAihCpB7B,OAAQzY,KAAKgb,IAAI4C,EAAUI,aAAcxD,GAAa,EAAIA,EAhhCrC,MAkhCvBnQ,KAAKyT,cAAgBA,EACrB3K,EAAS0K,EAAS,CAChBnF,MAAOoF,EAAcpF,MACrBD,OAAQqF,EAAcrF,SAExBlF,EAASH,EAASpE,GAClB4E,EAAYiK,EAAS7O,IAGvBuO,WAAY,WACV,IAAIO,EAAgBzT,KAAKyT,cACrBG,EAAY5T,KAAK4T,UACjBlO,EAAW1F,KAAKK,QAAQqF,SACxBmO,EAAUle,KAAKme,IAAIF,EAAU7G,QAAU,KAAQ,GAC/CiC,EAAe6E,EAAUD,EAAU1E,cAAgB0E,EAAU5E,aAC7DE,EAAgB2E,EAAUD,EAAU5E,aAAe4E,EAAU1E,cAC7DpJ,EAAckJ,EAAeE,EAC7B6E,EAAcN,EAAcpF,MAC5B2F,EAAeP,EAAcrF,OAE7BqF,EAAcrF,OAAStI,EAAc2N,EAAcpF,MACpC,IAAb3I,EACFqO,EAAcN,EAAcrF,OAAStI,EAErCkO,EAAeP,EAAcpF,MAAQvI,EAEjB,IAAbJ,EACTsO,EAAeP,EAAcpF,MAAQvI,EAErCiO,EAAcN,EAAcrF,OAAStI,EAGvC,IAAImO,EAAa,CACfnO,YAAaA,EACbkJ,aAAcA,EACdE,cAAeA,EACfb,MAAO0F,EACP3F,OAAQ4F,GAEVhU,KAAKiU,WAAaA,EAClBjU,KAAKkU,QAAuB,IAAbxO,GAA+B,IAAbA,EACjC1F,KAAKmU,aAAY,GAAM,GACvBF,EAAW5F,MAAQ1Y,KAAK+a,IAAI/a,KAAKgb,IAAIsD,EAAW5F,MAAO4F,EAAWhE,UAAWgE,EAAWpE,UACxFoE,EAAW7F,OAASzY,KAAK+a,IAAI/a,KAAKgb,IAAIsD,EAAW7F,OAAQ6F,EAAW9D,WAAY8D,EAAWlE,WAC3FkE,EAAWnI,MAAQ2H,EAAcpF,MAAQ4F,EAAW5F,OAAS,EAC7D4F,EAAWhI,KAAOwH,EAAcrF,OAAS6F,EAAW7F,QAAU,EAC9D6F,EAAWG,QAAUH,EAAWnI,KAChCmI,EAAWI,OAASJ,EAAWhI,IAC/BjM,KAAKsU,kBAAoB/T,EAAO,GAAI0T,IAEtCE,YAAa,SAAqBI,EAAaC,GAC7C,IAAInU,EAAUL,KAAKK,QACfoT,EAAgBzT,KAAKyT,cACrBQ,EAAajU,KAAKiU,WAClBQ,EAAczU,KAAKyU,YACnB/O,EAAWrF,EAAQqF,SACnBI,EAAcmO,EAAWnO,YACzBuN,EAAUrT,KAAKqT,SAAWoB,EAE9B,GAAIF,EAAa,CACf,IAAIlN,EAAiBzR,OAAOyK,EAAQgH,iBAAmB,EACnDC,EAAkB1R,OAAOyK,EAAQiH,kBAAoB,EAErD5B,EAAW,GACb2B,EAAiB1R,KAAKgb,IAAItJ,EAAgBoM,EAAcpF,OACxD/G,EAAkB3R,KAAKgb,IAAIrJ,EAAiBmM,EAAcrF,QAEzC,IAAb1I,IACE4B,EAAkBxB,EAAcuB,EAClCA,EAAiBC,EAAkBxB,EAEnCwB,EAAkBD,EAAiBvB,IAG9BJ,EAAW,IAChB2B,EACFA,EAAiB1R,KAAKgb,IAAItJ,EAAgBgM,EAAUoB,EAAYpG,MAAQ,GAC/D/G,EACTA,EAAkB3R,KAAKgb,IAAIrJ,EAAiB+L,EAAUoB,EAAYrG,OAAS,GAClEiF,IACThM,EAAiBoN,EAAYpG,OAC7B/G,EAAkBmN,EAAYrG,QAERtI,EAAcuB,EAClCA,EAAiBC,EAAkBxB,EAEnCwB,EAAkBD,EAAiBvB,IAKzC,IAAI4O,EAAoBxG,GAAiB,CACvCpI,YAAaA,EACbuI,MAAOhH,EACP+G,OAAQ9G,IAGVD,EAAiBqN,EAAkBrG,MACnC/G,EAAkBoN,EAAkBtG,OACpC6F,EAAWhE,SAAW5I,EACtB4M,EAAW9D,UAAY7I,EACvB2M,EAAWpE,SAAW3H,IACtB+L,EAAWlE,UAAY7H,IAGzB,GAAIsM,EACF,GAAI9O,GAAY2N,EAAU,EAAI,GAAI,CAChC,IAAIsB,EAAgBlB,EAAcpF,MAAQ4F,EAAW5F,MACjDuG,EAAenB,EAAcrF,OAAS6F,EAAW7F,OACrD6F,EAAWY,QAAUlf,KAAK+a,IAAI,EAAGiE,GACjCV,EAAWa,OAASnf,KAAK+a,IAAI,EAAGkE,GAChCX,EAAWc,QAAUpf,KAAKgb,IAAI,EAAGgE,GACjCV,EAAWe,OAASrf,KAAKgb,IAAI,EAAGiE,GAE5BvB,GAAWrT,KAAKkU,UAClBD,EAAWY,QAAUlf,KAAK+a,IAAI+D,EAAY3I,KAAM2I,EAAY3I,MAAQ2I,EAAYpG,MAAQ4F,EAAW5F,QACnG4F,EAAWa,OAASnf,KAAK+a,IAAI+D,EAAYxI,IAAKwI,EAAYxI,KAAOwI,EAAYrG,OAAS6F,EAAW7F,SACjG6F,EAAWc,QAAUN,EAAY3I,KACjCmI,EAAWe,OAASP,EAAYxI,IAEf,IAAbvG,IACEuO,EAAW5F,OAASoF,EAAcpF,QACpC4F,EAAWY,QAAUlf,KAAK+a,IAAI,EAAGiE,GACjCV,EAAWc,QAAUpf,KAAKgb,IAAI,EAAGgE,IAG/BV,EAAW7F,QAAUqF,EAAcrF,SACrC6F,EAAWa,OAASnf,KAAK+a,IAAI,EAAGkE,GAChCX,EAAWe,OAASrf,KAAKgb,IAAI,EAAGiE,WAKtCX,EAAWY,SAAWZ,EAAW5F,MACjC4F,EAAWa,QAAUb,EAAW7F,OAChC6F,EAAWc,QAAUtB,EAAcpF,MACnC4F,EAAWe,OAASvB,EAAcrF,QAIxCgF,aAAc,SAAsB6B,EAASC,GAC3C,IAAIjB,EAAajU,KAAKiU,WAClBL,EAAY5T,KAAK4T,UAErB,GAAIsB,EAAa,CACf,IAAIC,EAvdV,SAAyBC,GACvB,IAAI/G,EAAQ+G,EAAM/G,MACdD,EAASgH,EAAMhH,OACfiH,EAASD,EAAMC,OAGnB,GAAe,KAFfA,EAAS1f,KAAKme,IAAIuB,GAAU,KAG1B,MAAO,CACLhH,MAAOD,EACPA,OAAQC,GAIZ,IAAIiH,EAAMD,EAAS,GAAK1f,KAAKyb,GAAK,IAC9BmE,EAAS5f,KAAK6f,IAAIF,GAClBG,EAAS9f,KAAK+f,IAAIJ,GAClBK,EAAWtH,EAAQoH,EAASrH,EAASmH,EACrCK,EAAYvH,EAAQkH,EAASnH,EAASqH,EAC1C,OAAOJ,EAAS,GAAK,CACnBhH,MAAOuH,EACPxH,OAAQuH,GACN,CACFtH,MAAOsH,EACPvH,OAAQwH,GAgciBC,CAAgB,CACrCxH,MAAOuF,EAAU5E,aAAerZ,KAAKme,IAAIF,EAAU5G,QAAU,GAC7DoB,OAAQwF,EAAU1E,cAAgBvZ,KAAKme,IAAIF,EAAU3G,QAAU,GAC/DoI,OAAQzB,EAAU7G,QAAU,IAE1BiC,EAAemG,EAAiB9G,MAChCa,EAAgBiG,EAAiB/G,OAEjCC,EAAQ4F,EAAW5F,OAASW,EAAeiF,EAAWjF,cACtDZ,EAAS6F,EAAW7F,QAAUc,EAAgB+E,EAAW/E,eAC7D+E,EAAWnI,OAASuC,EAAQ4F,EAAW5F,OAAS,EAChD4F,EAAWhI,MAAQmC,EAAS6F,EAAW7F,QAAU,EACjD6F,EAAW5F,MAAQA,EACnB4F,EAAW7F,OAASA,EACpB6F,EAAWnO,YAAckJ,EAAeE,EACxC+E,EAAWjF,aAAeA,EAC1BiF,EAAW/E,cAAgBA,EAC3BlP,KAAKmU,aAAY,GAAM,IAGrBF,EAAW5F,MAAQ4F,EAAWpE,UAAYoE,EAAW5F,MAAQ4F,EAAWhE,YAC1EgE,EAAWnI,KAAOmI,EAAWG,UAG3BH,EAAW7F,OAAS6F,EAAWlE,WAAakE,EAAW7F,OAAS6F,EAAW9D,aAC7E8D,EAAWhI,IAAMgI,EAAWI,QAG9BJ,EAAW5F,MAAQ1Y,KAAK+a,IAAI/a,KAAKgb,IAAIsD,EAAW5F,MAAO4F,EAAWhE,UAAWgE,EAAWpE,UACxFoE,EAAW7F,OAASzY,KAAK+a,IAAI/a,KAAKgb,IAAIsD,EAAW7F,OAAQ6F,EAAW9D,WAAY8D,EAAWlE,WAC3F/P,KAAKmU,aAAY,GAAO,GACxBF,EAAWnI,KAAOnW,KAAK+a,IAAI/a,KAAKgb,IAAIsD,EAAWnI,KAAMmI,EAAWY,SAAUZ,EAAWc,SACrFd,EAAWhI,IAAMtW,KAAK+a,IAAI/a,KAAKgb,IAAIsD,EAAWhI,IAAKgI,EAAWa,QAASb,EAAWe,QAClFf,EAAWG,QAAUH,EAAWnI,KAChCmI,EAAWI,OAASJ,EAAWhI,IAC/BnD,EAAS9I,KAAKoQ,OAAQ7P,EAAO,CAC3B8N,MAAO4F,EAAW5F,MAClBD,OAAQ6F,EAAW7F,QAClBvB,GAAc,CACfK,WAAY+G,EAAWnI,KACvBqB,WAAY8G,EAAWhI,QAEzBjM,KAAK8V,YAAYb,GAEbjV,KAAKqT,SAAWrT,KAAKkU,SACvBlU,KAAK+V,cAAa,GAAM,IAG5BD,YAAa,SAAqBb,GAChC,IAAIhB,EAAajU,KAAKiU,WAClBL,EAAY5T,KAAK4T,UACjBvF,EAAQuF,EAAU5E,cAAgBiF,EAAW5F,MAAQ4F,EAAWjF,cAChEZ,EAASwF,EAAU1E,eAAiB+E,EAAW7F,OAAS6F,EAAW/E,eACvE3O,EAAOqT,EAAW,CAChBvF,MAAOA,EACPD,OAAQA,EACRtC,MAAOmI,EAAW5F,MAAQA,GAAS,EACnCpC,KAAMgI,EAAW7F,OAASA,GAAU,IAEtCtF,EAAS9I,KAAK0O,MAAOnO,EAAO,CAC1B8N,MAAOuF,EAAUvF,MACjBD,OAAQwF,EAAUxF,QACjBvB,GAActM,EAAO,CACtB2M,WAAY0G,EAAU9H,KACtBqB,WAAYyG,EAAU3H,KACrB2H,MAECqB,GACFjV,KAAKgW,UAGT7C,YAAa,WACX,IAAI9S,EAAUL,KAAKK,QACf4T,EAAajU,KAAKiU,WAClBnO,EAAczF,EAAQyF,aAAezF,EAAQuF,mBAC7Cc,EAAe9Q,OAAOyK,EAAQqG,eAAiB,GAC/C+N,EAAc,CAChBpG,MAAO4F,EAAW5F,MAClBD,OAAQ6F,EAAW7F,QAGjBtI,IACEmO,EAAW7F,OAAStI,EAAcmO,EAAW5F,MAC/CoG,EAAYrG,OAASqG,EAAYpG,MAAQvI,EAEzC2O,EAAYpG,MAAQoG,EAAYrG,OAAStI,GAI7C9F,KAAKyU,YAAcA,EACnBzU,KAAK+V,cAAa,GAAM,GAExBtB,EAAYpG,MAAQ1Y,KAAK+a,IAAI/a,KAAKgb,IAAI8D,EAAYpG,MAAOoG,EAAYxE,UAAWwE,EAAY5E,UAC5F4E,EAAYrG,OAASzY,KAAK+a,IAAI/a,KAAKgb,IAAI8D,EAAYrG,OAAQqG,EAAYtE,WAAYsE,EAAY1E,WAE/F0E,EAAYpG,MAAQ1Y,KAAKgb,IAAI8D,EAAYxE,SAAUwE,EAAYpG,MAAQ3H,GACvE+N,EAAYrG,OAASzY,KAAKgb,IAAI8D,EAAYtE,UAAWsE,EAAYrG,OAAS1H,GAC1E+N,EAAY3I,KAAOmI,EAAWnI,MAAQmI,EAAW5F,MAAQoG,EAAYpG,OAAS,EAC9EoG,EAAYxI,IAAMgI,EAAWhI,KAAOgI,EAAW7F,OAASqG,EAAYrG,QAAU,EAC9EqG,EAAYL,QAAUK,EAAY3I,KAClC2I,EAAYJ,OAASI,EAAYxI,IACjCjM,KAAKiW,mBAAqB1V,EAAO,GAAIkU,IAEvCsB,aAAc,SAAsBxB,EAAaC,GAC/C,IAAInU,EAAUL,KAAKK,QACfoT,EAAgBzT,KAAKyT,cACrBQ,EAAajU,KAAKiU,WAClBQ,EAAczU,KAAKyU,YACnBP,EAAUlU,KAAKkU,QACfpO,EAAczF,EAAQyF,YAE1B,GAAIyO,EAAa,CACf,IAAIhN,EAAkB3R,OAAOyK,EAAQkH,kBAAoB,EACrDC,EAAmB5R,OAAOyK,EAAQmH,mBAAqB,EACvD0O,EAAkBhC,EAAUve,KAAK+a,IAAI+C,EAAcpF,MAAO4F,EAAW5F,MAAO4F,EAAW5F,MAAQ4F,EAAWnI,KAAM2H,EAAcpF,MAAQ4F,EAAWnI,MAAQ2H,EAAcpF,MACvK8H,EAAmBjC,EAAUve,KAAK+a,IAAI+C,EAAcrF,OAAQ6F,EAAW7F,OAAQ6F,EAAW7F,OAAS6F,EAAWhI,IAAKwH,EAAcrF,OAAS6F,EAAWhI,KAAOwH,EAAcrF,OAE9K7G,EAAkB5R,KAAK+a,IAAInJ,EAAiBkM,EAAcpF,OAC1D7G,EAAmB7R,KAAK+a,IAAIlJ,EAAkBiM,EAAcrF,QAExDtI,IACEyB,GAAmBC,EACjBA,EAAmB1B,EAAcyB,EACnCC,EAAmBD,EAAkBzB,EAErCyB,EAAkBC,EAAmB1B,EAE9ByB,EACTC,EAAmBD,EAAkBzB,EAC5B0B,IACTD,EAAkBC,EAAmB1B,GAGnCqQ,EAAmBrQ,EAAcoQ,EACnCC,EAAmBD,EAAkBpQ,EAErCoQ,EAAkBC,EAAmBrQ,GAKzC2O,EAAYxE,SAAWta,KAAK+a,IAAInJ,EAAiB2O,GACjDzB,EAAYtE,UAAYxa,KAAK+a,IAAIlJ,EAAkB2O,GACnD1B,EAAY5E,SAAWqG,EACvBzB,EAAY1E,UAAYoG,EAGtB3B,IACEN,GACFO,EAAYI,QAAUlf,KAAKgb,IAAI,EAAGsD,EAAWnI,MAC7C2I,EAAYK,OAASnf,KAAKgb,IAAI,EAAGsD,EAAWhI,KAC5CwI,EAAYM,QAAUpf,KAAK+a,IAAI+C,EAAcpF,MAAO4F,EAAWnI,KAAOmI,EAAW5F,OAASoG,EAAYpG,MACtGoG,EAAYO,OAASrf,KAAK+a,IAAI+C,EAAcrF,OAAQ6F,EAAWhI,IAAMgI,EAAW7F,QAAUqG,EAAYrG,SAEtGqG,EAAYI,QAAU,EACtBJ,EAAYK,OAAS,EACrBL,EAAYM,QAAUtB,EAAcpF,MAAQoG,EAAYpG,MACxDoG,EAAYO,OAASvB,EAAcrF,OAASqG,EAAYrG,UAI9DkF,cAAe,WACb,IAAIjT,EAAUL,KAAKK,QACfoT,EAAgBzT,KAAKyT,cACrBgB,EAAczU,KAAKyU,aAEnBA,EAAYpG,MAAQoG,EAAY5E,UAAY4E,EAAYpG,MAAQoG,EAAYxE,YAC9EwE,EAAY3I,KAAO2I,EAAYL,UAG7BK,EAAYrG,OAASqG,EAAY1E,WAAa0E,EAAYrG,OAASqG,EAAYtE,aACjFsE,EAAYxI,IAAMwI,EAAYJ,QAGhCI,EAAYpG,MAAQ1Y,KAAK+a,IAAI/a,KAAKgb,IAAI8D,EAAYpG,MAAOoG,EAAYxE,UAAWwE,EAAY5E,UAC5F4E,EAAYrG,OAASzY,KAAK+a,IAAI/a,KAAKgb,IAAI8D,EAAYrG,OAAQqG,EAAYtE,WAAYsE,EAAY1E,WAC/F/P,KAAK+V,cAAa,GAAO,GACzBtB,EAAY3I,KAAOnW,KAAK+a,IAAI/a,KAAKgb,IAAI8D,EAAY3I,KAAM2I,EAAYI,SAAUJ,EAAYM,SACzFN,EAAYxI,IAAMtW,KAAK+a,IAAI/a,KAAKgb,IAAI8D,EAAYxI,IAAKwI,EAAYK,QAASL,EAAYO,QACtFP,EAAYL,QAAUK,EAAY3I,KAClC2I,EAAYJ,OAASI,EAAYxI,IAE7B5L,EAAQsG,SAAWtG,EAAQ6G,gBAE7B8C,EAAQhK,KAAKoW,KAAMpR,EAAayP,EAAYpG,OAASoF,EAAcpF,OAASoG,EAAYrG,QAAUqF,EAAcrF,OA94CpG,OAFD,OAm5CbtF,EAAS9I,KAAKqW,QAAS9V,EAAO,CAC5B8N,MAAOoG,EAAYpG,MACnBD,OAAQqG,EAAYrG,QACnBvB,GAAc,CACfK,WAAYuH,EAAY3I,KACxBqB,WAAYsH,EAAYxI,QAGtBjM,KAAKqT,SAAWrT,KAAKkU,SACvBlU,KAAKmU,aAAY,GAAM,GAGpBnU,KAAKsW,UACRtW,KAAKgW,UAGTA,OAAQ,WACNhW,KAAK+F,UACLoF,GAAcnL,KAAK+I,QAz4CN,OAy4C2B/I,KAAK6J,aAI7C9D,GAAU,CACZwQ,YAAa,WACX,IAAIxN,EAAU/I,KAAK+I,QACfyN,EAAcxW,KAAKwW,YACnBzQ,EAAU/F,KAAKK,QAAQ0F,QACvBtM,EAAM+c,EAAcxW,KAAKyW,eAAiBzW,KAAKvG,IAC/Cid,EAAM3N,EAAQ2N,KAAO,uBACrBhI,EAAQnd,SAAS8e,cAAc,OAWnC,GATImG,IACF9H,EAAM8H,YAAcA,GAGtB9H,EAAMiI,IAAMld,EACZiV,EAAMgI,IAAMA,EACZ1W,KAAK4W,QAAQC,YAAYnI,GACzB1O,KAAK8W,aAAepI,EAEf3I,EAAL,CAIA,IAAIgR,EAAWhR,EAEQ,iBAAZA,EACTgR,EAAWhO,EAAQiO,cAAcC,iBAAiBlR,GACzCA,EAAQmR,gBACjBH,EAAW,CAAChR,IAGd/F,KAAK+W,SAAWA,EAChBlnB,EAAQknB,GAAU,SAAUI,GAC1B,IAAIC,EAAM7lB,SAAS8e,cAAc,OAEjCrG,EAAQmN,EAAIlS,EAAc,CACxBoJ,MAAO8I,EAAGzD,YACVtF,OAAQ+I,EAAGxD,aACX0D,KAAMF,EAAGG,YAGPd,IACFY,EAAIZ,YAAcA,GAGpBY,EAAIT,IAAMld,EACV2d,EAAIV,IAAMA,EAQVU,EAAInO,MAAMsO,QAAU,0KACpBJ,EAAGG,UAAY,GACfH,EAAGN,YAAYO,QAGnBI,aAAc,WACZ3nB,EAAQmQ,KAAK+W,UAAU,SAAUhO,GAC/B,IAAIlO,EAAOgP,EAAQd,EAAS9D,GAC5B6D,EAASC,EAAS,CAChBsF,MAAOxT,EAAKwT,MACZD,OAAQvT,EAAKuT,SAEfrF,EAAQuO,UAAYzc,EAAKwc,KA1jC/B,SAAoBtO,EAASpb,GAC3B,GAAI8B,EAASsZ,EAAQpb,IACnB,WACSob,EAAQpb,GACf,MAAOoR,GACPgK,EAAQpb,QAAQqG,OAEb,GAAI+U,EAAQe,QAEjB,WACSf,EAAQe,QAAQnc,GACvB,MAAOoR,GACPgK,EAAQe,QAAQnc,QAAQqG,OAG1B+U,EAAQ0O,gBAAgB,QAAQvgB,OAAO0S,EAAYjc,KA4iCjD+pB,CAAW3O,EAAS9D,OAGxBc,QAAS,WACP,IAAI6N,EAAY5T,KAAK4T,UACjBK,EAAajU,KAAKiU,WAClBQ,EAAczU,KAAKyU,YACnBkD,EAAelD,EAAYpG,MAC3BuJ,EAAgBnD,EAAYrG,OAC5BC,EAAQuF,EAAUvF,MAClBD,EAASwF,EAAUxF,OACnBtC,EAAO2I,EAAY3I,KAAOmI,EAAWnI,KAAO8H,EAAU9H,KACtDG,EAAMwI,EAAYxI,IAAMgI,EAAWhI,IAAM2H,EAAU3H,IAElDjM,KAAKqT,UAAWrT,KAAKsW,WAI1BxN,EAAS9I,KAAK8W,aAAcvW,EAAO,CACjC8N,MAAOA,EACPD,OAAQA,GACPvB,GAActM,EAAO,CACtB2M,YAAapB,EACbqB,YAAalB,GACZ2H,MACH/jB,EAAQmQ,KAAK+W,UAAU,SAAUhO,GAC/B,IAAIlO,EAAOgP,EAAQd,EAAS9D,GACxB4S,EAAgBhd,EAAKwT,MACrByJ,EAAiBjd,EAAKuT,OACtBuH,EAAWkC,EACXjC,EAAYkC,EACZC,EAAQ,EAERJ,IAEF/B,EAAYgC,GADZG,EAAQF,EAAgBF,IAItBC,GAAiBhC,EAAYkC,IAE/BnC,EAAWgC,GADXI,EAAQD,EAAiBF,GAEzBhC,EAAYkC,GAGdhP,EAASC,EAAS,CAChBsF,MAAOsH,EACPvH,OAAQwH,IAEV9M,EAASC,EAAQiP,qBAAqB,OAAO,GAAIzX,EAAO,CACtD8N,MAAOA,EAAQ0J,EACf3J,OAAQA,EAAS2J,GAChBlL,GAActM,EAAO,CACtB2M,YAAapB,EAAOiM,EACpB5K,YAAalB,EAAM8L,GAClBnE,YAKLqE,GAAS,CACXrpB,KAAM,WACJ,IAAIma,EAAU/I,KAAK+I,QACf1I,EAAUL,KAAKK,QACfmT,EAAUxT,KAAKwT,QAEf5jB,EAAWyQ,EAAQuH,YACrBkD,GAAY/B,EA9gDK,YA8gDsB1I,EAAQuH,WAG7ChY,EAAWyQ,EAAQwH,WACrBiD,GAAY/B,EAnhDI,WAmhDsB1I,EAAQwH,UAG5CjY,EAAWyQ,EAAQyH,UACrBgD,GAAY/B,EAxhDG,UAwhDsB1I,EAAQyH,SAG3ClY,EAAWyQ,EAAQ0H,OACrB+C,GAAY/B,EA7hDD,OA6hDsB1I,EAAQ0H,MAGvCnY,EAAWyQ,EAAQ2H,OACrB8C,GAAY/B,EAnhDD,OAmhDsB1I,EAAQ2H,MAG3C8C,GAAY0I,EAAStO,EAAoBlF,KAAKkY,YAAclY,KAAKmY,UAAUvpB,KAAKoR,OAE5EK,EAAQyG,UAAYzG,EAAQ2G,aAC9B8D,GAAY0I,EA1hDA,QA0hDsBxT,KAAKoY,QAAUpY,KAAKqY,MAAMzpB,KAAKoR,MAAO,CACtEsY,SAAS,EACTC,SAAS,IAITlY,EAAQ+G,0BACV0D,GAAY0I,EA1iDG,WA0iDsBxT,KAAKwY,WAAaxY,KAAKyY,SAAS7pB,KAAKoR,OAG5E8K,GAAY/B,EAAQiO,cAAe7R,EAAoBnF,KAAK0Y,WAAa1Y,KAAK2Y,SAAS/pB,KAAKoR,OAC5F8K,GAAY/B,EAAQiO,cAAe5R,EAAkBpF,KAAK4Y,UAAY5Y,KAAK6Y,QAAQjqB,KAAKoR,OAEpFK,EAAQ2F,YACV8E,GAAYxZ,OAziDC,SAyiDqB0O,KAAK8Y,SAAW9Y,KAAK+Y,OAAOnqB,KAAKoR,QAGvEgZ,OAAQ,WACN,IAAIjQ,EAAU/I,KAAK+I,QACf1I,EAAUL,KAAKK,QACfmT,EAAUxT,KAAKwT,QAEf5jB,EAAWyQ,EAAQuH,YACrB6C,GAAe1B,EA3jDE,YA2jDyB1I,EAAQuH,WAGhDhY,EAAWyQ,EAAQwH,WACrB4C,GAAe1B,EAhkDC,WAgkDyB1I,EAAQwH,UAG/CjY,EAAWyQ,EAAQyH,UACrB2C,GAAe1B,EArkDA,UAqkDyB1I,EAAQyH,SAG9ClY,EAAWyQ,EAAQ0H,OACrB0C,GAAe1B,EA1kDJ,OA0kDyB1I,EAAQ0H,MAG1CnY,EAAWyQ,EAAQ2H,OACrByC,GAAe1B,EAhkDJ,OAgkDyB1I,EAAQ2H,MAG9CyC,GAAe+I,EAAStO,EAAoBlF,KAAKkY,aAE7C7X,EAAQyG,UAAYzG,EAAQ2G,aAC9ByD,GAAe+I,EAvkDH,QAukDyBxT,KAAKoY,QAAS,CACjDE,SAAS,EACTC,SAAS,IAITlY,EAAQ+G,0BACVqD,GAAe+I,EAvlDA,WAulDyBxT,KAAKwY,YAG/C/N,GAAe1B,EAAQiO,cAAe7R,EAAoBnF,KAAK0Y,YAC/DjO,GAAe1B,EAAQiO,cAAe5R,EAAkBpF,KAAK4Y,WAEzDvY,EAAQ2F,YACVyE,GAAenZ,OAtlDF,SAslDwB0O,KAAK8Y,YAK5CG,GAAW,CACbF,OAAQ,WACN,IAAI/Y,KAAKsW,SAAT,CAIA,IAQMrC,EACAQ,EATFpU,EAAUL,KAAKK,QACfkT,EAAYvT,KAAKuT,UACjBE,EAAgBzT,KAAKyT,cACrByF,EAAS3F,EAAUG,YAAcD,EAAcpF,MAC/C8K,EAAS5F,EAAUI,aAAeF,EAAcrF,OAChD2J,EAAQpiB,KAAKme,IAAIoF,EAAS,GAAKvjB,KAAKme,IAAIqF,EAAS,GAAKD,EAASC,EAErD,IAAVpB,IAIE1X,EAAQ4F,UACVgO,EAAajU,KAAKoZ,gBAClB3E,EAAczU,KAAKqZ,kBAGrBrZ,KAAKgT,SAED3S,EAAQ4F,UACVjG,KAAKsZ,cAAczpB,EAAQokB,GAAY,SAAUplB,EAAGzB,GAClD6mB,EAAW7mB,GAAKyB,EAAIkpB,MAEtB/X,KAAKuZ,eAAe1pB,EAAQ4kB,GAAa,SAAU5lB,EAAGzB,GACpDqnB,EAAYrnB,GAAKyB,EAAIkpB,UAK7BU,SAAU,WA93CZ,IAAkB1P,EAAS1a,EA+3CnB2R,KAAKsW,UA5oDQ,SA4oDItW,KAAKK,QAAQsF,UAIlC3F,KAAKwZ,aAn4CSzQ,EAm4CY/I,KAAKyZ,QAn4CRprB,EAm4CiBoW,GAl4CnCsE,EAAQK,UAAYL,EAAQK,UAAUsQ,SAASrrB,GAAS0a,EAAQO,UAAUpP,QAAQ7L,IAAU,GA/QhF,OADA,UAopDnBgqB,MAAO,SAAezN,GACpB,IAAI+O,EAAQ3Z,KAER+X,EAAQniB,OAAOoK,KAAKK,QAAQ4G,iBAAmB,GAC/C2S,EAAQ,EAER5Z,KAAKsW,WAIT1L,EAAMiP,iBAEF7Z,KAAK8Z,WAIT9Z,KAAK8Z,UAAW,EAChBC,YAAW,WACTJ,EAAMG,UAAW,IAChB,IAEClP,EAAMoP,OACRJ,EAAQhP,EAAMoP,OAAS,EAAI,GAAK,EACvBpP,EAAMqP,WACfL,GAAShP,EAAMqP,WAAa,IACnBrP,EAAMU,SACfsO,EAAQhP,EAAMU,OAAS,EAAI,GAAK,GAGlCtL,KAAKgI,MAAM4R,EAAQ7B,EAAOnN,MAE5BuN,UAAW,SAAmBvN,GAC5B,IAAIsP,EAAUtP,EAAMsP,QAChBC,EAASvP,EAAMuP,OAEnB,KAAIna,KAAKsW,WACU,cAAf1L,EAAMxO,MAAuC,gBAAfwO,EAAMxO,MAAgD,UAAtBwO,EAAMwP,eACxEzpB,EAASupB,IAAwB,IAAZA,GAAiBvpB,EAASwpB,IAAsB,IAAXA,GACvDvP,EAAMyP,UAHT,CAOA,IAEIC,EAFAja,EAAUL,KAAKK,QACfka,EAAWva,KAAKua,SAGhB3P,EAAM4P,eAER3qB,EAAQ+a,EAAM4P,gBAAgB,SAAUC,GACtCF,EAASE,EAAMC,YAAclN,GAAWiN,MAI1CF,EAAS3P,EAAM+P,WAAa,GAAKnN,GAAW5C,GAI5C0P,EADExsB,OAAO+R,KAAK0a,GAAUvqB,OAAS,GAAKqQ,EAAQyG,UAAYzG,EAAQ0G,YAjuDtD,OAouDH8C,EAAQe,EAAMrL,OAAQyF,GAG5BK,EAAetB,KAAKuW,KAOlB,IAHHnP,GAAcnL,KAAK+I,QA/sDJ,YA+sD+B,CAChD6R,cAAehQ,EACf0P,OAAQA,MAMV1P,EAAMiP,iBACN7Z,KAAKsa,OAASA,EACdta,KAAK6a,UAAW,EAvvDF,SAyvDVP,IACFta,KAAK6a,UAAW,EAChB3R,EAASlJ,KAAKyZ,QAAS3U,OAG3B6T,SAAU,SAAkB/N,GAC1B,IAAI0P,EAASta,KAAKsa,OAElB,IAAIta,KAAKsW,UAAagE,EAAtB,CAIA,IAAIC,EAAWva,KAAKua,SACpB3P,EAAMiP,kBAKC,IAHH1O,GAAcnL,KAAK+I,QA3uDL,WA2uD+B,CAC/C6R,cAAehQ,EACf0P,OAAQA,MAKN1P,EAAM4P,eACR3qB,EAAQ+a,EAAM4P,gBAAgB,SAAUC,GAEtCla,EAAOga,EAASE,EAAMC,aAAe,GAAIlN,GAAWiN,GAAO,OAG7Dla,EAAOga,EAAS3P,EAAM+P,WAAa,IAAM,GAAInN,GAAW5C,GAAO,IAGjE5K,KAAK8a,OAAOlQ,MAEdiO,QAAS,SAAiBjO,GACxB,IAAI5K,KAAKsW,SAAT,CAIA,IAAIgE,EAASta,KAAKsa,OACdC,EAAWva,KAAKua,SAEhB3P,EAAM4P,eACR3qB,EAAQ+a,EAAM4P,gBAAgB,SAAUC,UAC/BF,EAASE,EAAMC,sBAGjBH,EAAS3P,EAAM+P,WAAa,GAGhCL,IAIL1P,EAAMiP,iBAED/rB,OAAO+R,KAAK0a,GAAUvqB,SACzBgQ,KAAKsa,OAAS,IAGZta,KAAK6a,WACP7a,KAAK6a,UAAW,EAChBpR,EAAYzJ,KAAKyZ,QAAS3U,EAAa9E,KAAKqT,SAAWrT,KAAKK,QAAQ+F,QAGtE+E,GAAcnL,KAAK+I,QA7xDF,UA6xD2B,CAC1C6R,cAAehQ,EACf0P,OAAQA,QAKVQ,GAAS,CACXA,OAAQ,SAAgBlQ,GACtB,IAkBIyH,EAlBAhS,EAAUL,KAAKK,QACf4T,EAAajU,KAAKiU,WAClBR,EAAgBzT,KAAKyT,cACrBgB,EAAczU,KAAKyU,YACnB8F,EAAWva,KAAKua,SAChBD,EAASta,KAAKsa,OACdxU,EAAczF,EAAQyF,YACtBgG,EAAO2I,EAAY3I,KACnBG,EAAMwI,EAAYxI,IAClBoC,EAAQoG,EAAYpG,MACpBD,EAASqG,EAAYrG,OACrB2M,EAAQjP,EAAOuC,EACf2M,EAAS/O,EAAMmC,EACfyG,EAAU,EACVC,EAAS,EACTjF,EAAW4D,EAAcpF,MACzB0B,EAAY0D,EAAcrF,OAC1B6M,GAAa,GAGZnV,GAAe8E,EAAMsQ,WACxBpV,EAAcuI,GAASD,EAASC,EAAQD,EAAS,GAG/CpO,KAAKkU,UACPW,EAAUJ,EAAYI,QACtBC,EAASL,EAAYK,OACrBjF,EAAWgF,EAAUlf,KAAK+a,IAAI+C,EAAcpF,MAAO4F,EAAW5F,MAAO4F,EAAWnI,KAAOmI,EAAW5F,OAClG0B,EAAY+E,EAASnf,KAAK+a,IAAI+C,EAAcrF,OAAQ6F,EAAW7F,OAAQ6F,EAAWhI,IAAMgI,EAAW7F,SAGrG,IAAI+M,EAAUZ,EAASzsB,OAAO+R,KAAK0a,GAAU,IACzCa,EAAQ,CACVznB,EAAGwnB,EAAQrN,KAAOqN,EAAQnN,OAC1BqN,EAAGF,EAAQpN,KAAOoN,EAAQlN,QAGxBqN,EAAQ,SAAeC,GACzB,OAAQA,GACN,IAt2DU,IAu2DJR,EAAQK,EAAMznB,EAAIkc,IACpBuL,EAAMznB,EAAIkc,EAAWkL,GAGvB,MAEF,IA52DU,IA62DJjP,EAAOsP,EAAMznB,EAAIkhB,IACnBuG,EAAMznB,EAAIkhB,EAAU/I,GAGtB,MAEF,IAj3DW,IAk3DLG,EAAMmP,EAAMC,EAAIvG,IAClBsG,EAAMC,EAAIvG,EAAS7I,GAGrB,MAEF,IAz3DW,IA03DL+O,EAASI,EAAMC,EAAItL,IACrBqL,EAAMC,EAAItL,EAAYiL,KAO9B,OAAQV,GAEN,IA14DW,MA24DTxO,GAAQsP,EAAMznB,EACdsY,GAAOmP,EAAMC,EACb,MAGF,IA54DY,IA64DV,GAAID,EAAMznB,GAAK,IAAMonB,GAASlL,GAAY/J,IAAgBmG,GAAO6I,GAAUkG,GAAUjL,IAAa,CAChGkL,GAAa,EACb,MAGFK,EAl5DU,MAm5DVjN,GAAS+M,EAAMznB,GAEH,IACV2mB,EAr5DQ,IAu5DRxO,GADAuC,GAASA,GAIPvI,IACFsI,EAASC,EAAQvI,EACjBmG,IAAQwI,EAAYrG,OAASA,GAAU,GAGzC,MAEF,IA/5Da,IAg6DX,GAAIgN,EAAMC,GAAK,IAAMpP,GAAO6I,GAAUhP,IAAgBgG,GAAQ+I,GAAWkG,GAASlL,IAAY,CAC5FoL,GAAa,EACb,MAGFK,EAr6DW,KAs6DXlN,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,EAETjN,EAAS,IACXkM,EA36DS,IA66DTrO,GADAmC,GAAUA,GAIRtI,IACFuI,EAAQD,EAAStI,EACjBgG,IAAS2I,EAAYpG,MAAQA,GAAS,GAGxC,MAEF,IAx7DY,IAy7DV,GAAI+M,EAAMznB,GAAK,IAAMmY,GAAQ+I,GAAW/O,IAAgBmG,GAAO6I,GAAUkG,GAAUjL,IAAa,CAC9FkL,GAAa,EACb,MAGFK,EA97DU,KA+7DVjN,GAAS+M,EAAMznB,EACfmY,GAAQsP,EAAMznB,EAEV0a,EAAQ,IACViM,EAp8DQ,IAs8DRxO,GADAuC,GAASA,GAIPvI,IACFsI,EAASC,EAAQvI,EACjBmG,IAAQwI,EAAYrG,OAASA,GAAU,GAGzC,MAEF,IA98Da,IA+8DX,GAAIgN,EAAMC,GAAK,IAAML,GAAUjL,GAAajK,IAAgBgG,GAAQ+I,GAAWkG,GAASlL,IAAY,CAClGoL,GAAa,EACb,MAGFK,EAp9DW,MAq9DXlN,GAAUgN,EAAMC,GAEH,IACXf,EAv9DS,IAy9DTrO,GADAmC,GAAUA,GAIRtI,IACFuI,EAAQD,EAAStI,EACjBgG,IAAS2I,EAAYpG,MAAQA,GAAS,GAGxC,MAEF,IAl+DkB,KAm+DhB,GAAIvI,EAAa,CACf,GAAIsV,EAAMC,GAAK,IAAMpP,GAAO6I,GAAUiG,GAASlL,GAAW,CACxDoL,GAAa,EACb,MAGFK,EA1+DS,KA2+DTlN,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,EACbhN,EAAQD,EAAStI,OAEjBwV,EA/+DS,KAg/DTA,EAn/DQ,KAq/DJF,EAAMznB,GAAK,EACTonB,EAAQlL,EACVxB,GAAS+M,EAAMznB,EACNynB,EAAMC,GAAK,GAAKpP,GAAO6I,IAChCmG,GAAa,GAGf5M,GAAS+M,EAAMznB,EAGbynB,EAAMC,GAAK,EACTpP,EAAM6I,IACR1G,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,IAGfjN,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,GAIbhN,EAAQ,GAAKD,EAAS,GACxBkM,EApgEc,KAugEdrO,GAFAmC,GAAUA,EAGVtC,GAFAuC,GAASA,GAGAA,EAAQ,GACjBiM,EA5gEc,KA8gEdxO,GADAuC,GAASA,GAEAD,EAAS,IAClBkM,EA/gEc,KAihEdrO,GADAmC,GAAUA,GAIZ,MAEF,IAvhEkB,KAwhEhB,GAAItI,EAAa,CACf,GAAIsV,EAAMC,GAAK,IAAMpP,GAAO6I,GAAUhJ,GAAQ+I,GAAU,CACtDoG,GAAa,EACb,MAGFK,EAhiES,KAiiETlN,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,EACbhN,EAAQD,EAAStI,EACjBgG,GAAQ2I,EAAYpG,MAAQA,OAE5BiN,EAtiES,KAuiETA,EAziEQ,KA2iEJF,EAAMznB,GAAK,EACTmY,EAAO+I,GACTxG,GAAS+M,EAAMznB,EACfmY,GAAQsP,EAAMznB,GACLynB,EAAMC,GAAK,GAAKpP,GAAO6I,IAChCmG,GAAa,IAGf5M,GAAS+M,EAAMznB,EACfmY,GAAQsP,EAAMznB,GAGZynB,EAAMC,GAAK,EACTpP,EAAM6I,IACR1G,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,IAGfjN,GAAUgN,EAAMC,EAChBpP,GAAOmP,EAAMC,GAIbhN,EAAQ,GAAKD,EAAS,GACxBkM,EA9jEc,KAikEdrO,GAFAmC,GAAUA,EAGVtC,GAFAuC,GAASA,GAGAA,EAAQ,GACjBiM,EAtkEc,KAwkEdxO,GADAuC,GAASA,GAEAD,EAAS,IAClBkM,EAvkEc,KAykEdrO,GADAmC,GAAUA,GAIZ,MAEF,IA9kEkB,KA+kEhB,GAAItI,EAAa,CACf,GAAIsV,EAAMznB,GAAK,IAAMmY,GAAQ+I,GAAWmG,GAAUjL,GAAY,CAC5DkL,GAAa,EACb,MAGFK,EA3lEQ,KA4lERjN,GAAS+M,EAAMznB,EACfmY,GAAQsP,EAAMznB,EACdya,EAASC,EAAQvI,OAEjBwV,EA/lES,KAgmETA,EAjmEQ,KAmmEJF,EAAMznB,GAAK,EACTmY,EAAO+I,GACTxG,GAAS+M,EAAMznB,EACfmY,GAAQsP,EAAMznB,GACLynB,EAAMC,GAAK,GAAKL,GAAUjL,IACnCkL,GAAa,IAGf5M,GAAS+M,EAAMznB,EACfmY,GAAQsP,EAAMznB,GAGZynB,EAAMC,GAAK,EACTL,EAASjL,IACX3B,GAAUgN,EAAMC,GAGlBjN,GAAUgN,EAAMC,EAIhBhN,EAAQ,GAAKD,EAAS,GACxBkM,EAtnEc,KAynEdrO,GAFAmC,GAAUA,EAGVtC,GAFAuC,GAASA,GAGAA,EAAQ,GACjBiM,EA1nEc,KA4nEdxO,GADAuC,GAASA,GAEAD,EAAS,IAClBkM,EA/nEc,KAioEdrO,GADAmC,GAAUA,GAIZ,MAEF,IAroEkB,KAsoEhB,GAAItI,EAAa,CACf,GAAIsV,EAAMznB,GAAK,IAAMonB,GAASlL,GAAYmL,GAAUjL,GAAY,CAC9DkL,GAAa,EACb,MAGFK,EAlpEQ,KAopERlN,GADAC,GAAS+M,EAAMznB,GACEmS,OAEjBwV,EAppES,KAqpETA,EAvpEQ,KAypEJF,EAAMznB,GAAK,EACTonB,EAAQlL,EACVxB,GAAS+M,EAAMznB,EACNynB,EAAMC,GAAK,GAAKL,GAAUjL,IACnCkL,GAAa,GAGf5M,GAAS+M,EAAMznB,EAGbynB,EAAMC,GAAK,EACTL,EAASjL,IACX3B,GAAUgN,EAAMC,GAGlBjN,GAAUgN,EAAMC,EAIhBhN,EAAQ,GAAKD,EAAS,GACxBkM,EAxqEc,KA2qEdrO,GAFAmC,GAAUA,EAGVtC,GAFAuC,GAASA,GAGAA,EAAQ,GACjBiM,EA5qEc,KA8qEdxO,GADAuC,GAASA,GAEAD,EAAS,IAClBkM,EAnrEc,KAqrEdrO,GADAmC,GAAUA,GAIZ,MAGF,IAjsEY,OAksEVpO,KAAKwb,KAAKJ,EAAMznB,EAAGynB,EAAMC,GACzBJ,GAAa,EACb,MAGF,IAtsEY,OAusEVjb,KAAKgI,KAnjDb,SAAyBuS,GACvB,IAAIkB,EAAY/Y,EAAe,GAAI6X,GAE/BmB,EAAW,EAiBf,OAhBA7rB,EAAQ0qB,GAAU,SAAUY,EAASR,UAC5Bc,EAAUd,GACjB9qB,EAAQ4rB,GAAW,SAAUE,GAC3B,IAAIC,EAAKjmB,KAAKme,IAAIqH,EAAQnN,OAAS2N,EAAS3N,QACxC6N,EAAKlmB,KAAKme,IAAIqH,EAAQlN,OAAS0N,EAAS1N,QACxC6N,EAAKnmB,KAAKme,IAAIqH,EAAQrN,KAAO6N,EAAS7N,MACtCiO,EAAKpmB,KAAKme,IAAIqH,EAAQpN,KAAO4N,EAAS5N,MACtCiO,EAAKrmB,KAAKsmB,KAAKL,EAAKA,EAAKC,EAAKA,GAE9B9D,GADKpiB,KAAKsmB,KAAKH,EAAKA,EAAKC,EAAKA,GAChBC,GAAMA,EAEpBrmB,KAAKme,IAAIiE,GAASpiB,KAAKme,IAAI4H,KAC7BA,EAAW3D,SAIV2D,EA+hDSQ,CAAgB3B,GAAW3P,GACrCqQ,GAAa,EACb,MAGF,IA9sEY,OA+sEV,IAAKG,EAAMznB,IAAMynB,EAAMC,EAAG,CACxBJ,GAAa,EACb,MAGF5I,EAAS1G,GAAU3L,KAAKwT,SACxB1H,EAAOqP,EAAQnN,OAASqE,EAAOvG,KAC/BG,EAAMkP,EAAQlN,OAASoE,EAAOpG,IAC9BoC,EAAQoG,EAAYxE,SACpB7B,EAASqG,EAAYtE,UAEjBiL,EAAMznB,EAAI,EACZ2mB,EAASc,EAAMC,EAAI,EAltEL,KAFA,KAqtELD,EAAMznB,EAAI,IACnBmY,GAAQuC,EACRiM,EAASc,EAAMC,EAAI,EAptEL,KAFA,MAytEZD,EAAMC,EAAI,IACZpP,GAAOmC,GAIJpO,KAAKqT,UACR9J,EAAYvJ,KAAKqW,QAAS1R,GAC1B3E,KAAKqT,SAAU,EAEXrT,KAAKkU,SACPlU,KAAK+V,cAAa,GAAM,IAO5BkF,IACFxG,EAAYpG,MAAQA,EACpBoG,EAAYrG,OAASA,EACrBqG,EAAY3I,KAAOA,EACnB2I,EAAYxI,IAAMA,EAClBjM,KAAKsa,OAASA,EACdta,KAAKsT,iBAIPzjB,EAAQ0qB,GAAU,SAAUrrB,GAC1BA,EAAE8e,OAAS9e,EAAE4e,KACb5e,EAAE+e,OAAS/e,EAAE6e,UAKfoO,GAAU,CAEZpU,KAAM,WAaJ,OAZI/H,KAAK2H,OAAU3H,KAAKqT,SAAYrT,KAAKsW,WACvCtW,KAAKqT,SAAU,EACfrT,KAAK+V,cAAa,GAAM,GAEpB/V,KAAKK,QAAQ+F,OACf8C,EAASlJ,KAAKyZ,QAAS3U,GAGzByE,EAAYvJ,KAAKqW,QAAS1R,GAC1B3E,KAAKuZ,eAAevZ,KAAKiW,qBAGpBjW,MAGToc,MAAO,WAYL,OAXIpc,KAAK2H,QAAU3H,KAAKsW,WACtBtW,KAAK4T,UAAYrT,EAAO,GAAIP,KAAKqc,kBACjCrc,KAAKiU,WAAa1T,EAAO,GAAIP,KAAKsU,mBAClCtU,KAAKyU,YAAclU,EAAO,GAAIP,KAAKiW,oBACnCjW,KAAKoT,eAEDpT,KAAKqT,SACPrT,KAAKsT,iBAIFtT,MAGTsc,MAAO,WAiBL,OAhBItc,KAAKqT,UAAYrT,KAAKsW,WACxB/V,EAAOP,KAAKyU,YAAa,CACvB3I,KAAM,EACNG,IAAK,EACLoC,MAAO,EACPD,OAAQ,IAEVpO,KAAKqT,SAAU,EACfrT,KAAKsT,gBACLtT,KAAKmU,aAAY,GAAM,GAEvBnU,KAAKoT,eACL7J,EAAYvJ,KAAKyZ,QAAS3U,GAC1BoE,EAASlJ,KAAKqW,QAAS1R,IAGlB3E,MAST7N,QAAS,SAAiBsH,GACxB,IAAI8iB,EAAc3qB,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,IAAmBA,UAAU,GA4BjF,OA1BKoO,KAAKsW,UAAY7c,IAChBuG,KAAKwc,QACPxc,KAAK+I,QAAQ4N,IAAMld,GAGjB8iB,GACFvc,KAAKvG,IAAMA,EACXuG,KAAK0O,MAAMiI,IAAMld,EAEbuG,KAAK2H,QACP3H,KAAK8W,aAAaH,IAAMld,EACxB5J,EAAQmQ,KAAK+W,UAAU,SAAUhO,GAC/BA,EAAQiP,qBAAqB,OAAO,GAAGrB,IAAMld,QAI7CuG,KAAKwc,QACPxc,KAAKyc,UAAW,GAGlBzc,KAAKK,QAAQxF,KAAO,KACpBmF,KAAK0c,WACL1c,KAAK2c,KAAKljB,KAIPuG,MAGT4c,OAAQ,WAMN,OALI5c,KAAK2H,OAAS3H,KAAKsW,WACrBtW,KAAKsW,UAAW,EAChB/M,EAAYvJ,KAAKwT,QAAS9O,IAGrB1E,MAGT6c,QAAS,WAMP,OALI7c,KAAK2H,QAAU3H,KAAKsW,WACtBtW,KAAKsW,UAAW,EAChBpN,EAASlJ,KAAKwT,QAAS9O,IAGlB1E,MAOT8c,QAAS,WACP,IAAI/T,EAAU/I,KAAK+I,QAEnB,OAAKA,EAAO,SAIZA,EAAO,aAAc/U,EAEjBgM,KAAKwc,OAASxc,KAAKyc,WACrB1T,EAAQ4N,IAAM3W,KAAK+c,aAGrB/c,KAAK0c,WACE1c,MAVEA,MAmBXwb,KAAM,SAAcwB,GAClB,IAAIC,EAAUrrB,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAKorB,EAC9EE,EAAmBld,KAAKiU,WACxBnI,EAAOoR,EAAiBpR,KACxBG,EAAMiR,EAAiBjR,IAC3B,OAAOjM,KAAKmd,OAAO3tB,EAAYwtB,GAAWA,EAAUlR,EAAOlW,OAAOonB,GAAUxtB,EAAYytB,GAAWA,EAAUhR,EAAMrW,OAAOqnB,KAS5HE,OAAQ,SAAgBxpB,GACtB,IAAI0nB,EAAIzpB,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK+B,EACxEsgB,EAAajU,KAAKiU,WAClBgB,GAAU,EAoBd,OAnBAthB,EAAIiC,OAAOjC,GACX0nB,EAAIzlB,OAAOylB,GAEPrb,KAAK2H,QAAU3H,KAAKsW,UAAYtW,KAAKK,QAAQsG,UAC3ChW,EAASgD,KACXsgB,EAAWnI,KAAOnY,EAClBshB,GAAU,GAGRtkB,EAAS0qB,KACXpH,EAAWhI,IAAMoP,EACjBpG,GAAU,GAGRA,GACFjV,KAAKoT,cAAa,IAIfpT,MASTgI,KAAM,SAAc+P,EAAOqF,GACzB,IAAInJ,EAAajU,KAAKiU,WAStB,OALE8D,GAHFA,EAAQniB,OAAOmiB,IAEH,EACF,GAAK,EAAIA,GAET,EAAIA,EAGP/X,KAAKqd,OAAOpJ,EAAW5F,MAAQ0J,EAAQ9D,EAAWjF,aAAc,KAAMoO,IAU/EC,OAAQ,SAAgBtF,EAAOuF,EAAOF,GACpC,IAAI/c,EAAUL,KAAKK,QACf4T,EAAajU,KAAKiU,WAClB5F,EAAQ4F,EAAW5F,MACnBD,EAAS6F,EAAW7F,OACpBY,EAAeiF,EAAWjF,aAC1BE,EAAgB+E,EAAW/E,cAG/B,IAFA6I,EAAQniB,OAAOmiB,KAEF,GAAK/X,KAAK2H,QAAU3H,KAAKsW,UAAYjW,EAAQyG,SAAU,CAClE,IAAI6O,EAAW3G,EAAe+I,EAC1BnC,EAAY1G,EAAgB6I,EAEhC,IAIO,IAJH5M,GAAcnL,KAAK+I,QAj7EZ,OAi7EiC,CAC1CgP,MAAOA,EACPwF,SAAUlP,EAAQW,EAClB4L,cAAewC,IAEf,OAAOpd,KAGT,GAAIod,EAAgB,CAClB,IAAI7C,EAAWva,KAAKua,SAChBlI,EAAS1G,GAAU3L,KAAKwT,SACxBlN,EAASiU,GAAYzsB,OAAO+R,KAAK0a,GAAUvqB,OAhyDvD,SAA2BuqB,GACzB,IAAI5M,EAAQ,EACRC,EAAQ,EACR4P,EAAQ,EAUZ,OATA3tB,EAAQ0qB,GAAU,SAAUkD,GAC1B,IAAIzP,EAASyP,EAAMzP,OACfC,EAASwP,EAAMxP,OACnBN,GAASK,EACTJ,GAASK,EACTuP,GAAS,KAIJ,CACL7P,MAHFA,GAAS6P,EAIP5P,MAHFA,GAAS4P,GAoxDqDE,CAAkBnD,GAAY,CACpF5M,MAAOyP,EAAezP,MACtBC,MAAOwP,EAAexP,OAGxBqG,EAAWnI,OAAS6J,EAAWtH,KAAW/H,EAAOqH,MAAQ0E,EAAOvG,KAAOmI,EAAWnI,MAAQuC,GAC1F4F,EAAWhI,MAAQ2J,EAAYxH,KAAY9H,EAAOsH,MAAQyE,EAAOpG,IAAMgI,EAAWhI,KAAOmC,QAChF1e,EAAc4tB,IAAU3sB,EAAS2sB,EAAM3pB,IAAMhD,EAAS2sB,EAAMjC,IACrEpH,EAAWnI,OAAS6J,EAAWtH,KAAWiP,EAAM3pB,EAAIsgB,EAAWnI,MAAQuC,GACvE4F,EAAWhI,MAAQ2J,EAAYxH,KAAYkP,EAAMjC,EAAIpH,EAAWhI,KAAOmC,KAGvE6F,EAAWnI,OAAS6J,EAAWtH,GAAS,EACxC4F,EAAWhI,MAAQ2J,EAAYxH,GAAU,GAG3C6F,EAAW5F,MAAQsH,EACnB1B,EAAW7F,OAASwH,EACpB5V,KAAKoT,cAAa,GAGpB,OAAOpT,MAQT+M,OAAQ,SAAgBsI,GACtB,OAAOrV,KAAK2d,UAAU3d,KAAK4T,UAAU7G,QAAU,GAAKnX,OAAOyf,KAQ7DsI,SAAU,SAAkBtI,GAQ1B,OALI1kB,EAFJ0kB,EAASzf,OAAOyf,KAEQrV,KAAK2H,QAAU3H,KAAKsW,UAAYtW,KAAKK,QAAQuG,YACnE5G,KAAK4T,UAAU7G,OAASsI,EAAS,IACjCrV,KAAKoT,cAAa,GAAM,IAGnBpT,MAQTgN,OAAQ,SAAgB4Q,GACtB,IAAI3Q,EAASjN,KAAK4T,UAAU3G,OAC5B,OAAOjN,KAAKqR,MAAMuM,EAASjtB,EAASsc,GAAUA,EAAS,IAQzDA,OAAQ,SAAgB4Q,GACtB,IAAI7Q,EAAShN,KAAK4T,UAAU5G,OAC5B,OAAOhN,KAAKqR,MAAM1gB,EAASqc,GAAUA,EAAS,EAAG6Q,IASnDxM,MAAO,SAAerE,GACpB,IAAIC,EAASrb,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAKob,EAC7E4G,EAAY5T,KAAK4T,UACjBsB,GAAc,EAoBlB,OAnBAlI,EAASpX,OAAOoX,GAChBC,EAASrX,OAAOqX,GAEZjN,KAAK2H,QAAU3H,KAAKsW,UAAYtW,KAAKK,QAAQwG,WAC3ClW,EAASqc,KACX4G,EAAU5G,OAASA,EACnBkI,GAAc,GAGZvkB,EAASsc,KACX2G,EAAU3G,OAASA,EACnBiI,GAAc,GAGZA,GACFlV,KAAKoT,cAAa,GAAM,IAIrBpT,MAQT6J,QAAS,WACP,IAKIhP,EALAijB,EAAUlsB,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,IAAmBA,UAAU,GACzEyO,EAAUL,KAAKK,QACfuT,EAAY5T,KAAK4T,UACjBK,EAAajU,KAAKiU,WAClBQ,EAAczU,KAAKyU,YAGvB,GAAIzU,KAAK2H,OAAS3H,KAAKqT,QAAS,CAC9BxY,EAAO,CACLlH,EAAG8gB,EAAY3I,KAAOmI,EAAWnI,KACjCuP,EAAG5G,EAAYxI,IAAMgI,EAAWhI,IAChCoC,MAAOoG,EAAYpG,MACnBD,OAAQqG,EAAYrG,QAEtB,IAAI2J,EAAQnE,EAAUvF,MAAQuF,EAAU5E,aAKxC,GAJAnf,EAAQgL,GAAM,SAAUhM,EAAGzB,GACzByN,EAAKzN,GAAKyB,EAAIkpB,KAGZ+F,EAAS,CAGX,IAAI9C,EAASrlB,KAAKiT,MAAM/N,EAAKwgB,EAAIxgB,EAAKuT,QAClC2M,EAAQplB,KAAKiT,MAAM/N,EAAKlH,EAAIkH,EAAKwT,OACrCxT,EAAKlH,EAAIgC,KAAKiT,MAAM/N,EAAKlH,GACzBkH,EAAKwgB,EAAI1lB,KAAKiT,MAAM/N,EAAKwgB,GACzBxgB,EAAKwT,MAAQ0M,EAAQlgB,EAAKlH,EAC1BkH,EAAKuT,OAAS4M,EAASngB,EAAKwgB,QAG9BxgB,EAAO,CACLlH,EAAG,EACH0nB,EAAG,EACHhN,MAAO,EACPD,OAAQ,GAaZ,OATI/N,EAAQuG,YACV/L,EAAKkS,OAAS6G,EAAU7G,QAAU,GAGhC1M,EAAQwG,WACVhM,EAAKmS,OAAS4G,EAAU5G,QAAU,EAClCnS,EAAKoS,OAAS2G,EAAU3G,QAAU,GAG7BpS,GAQTmP,QAAS,SAAiBnP,GACxB,IAAIwF,EAAUL,KAAKK,QACfuT,EAAY5T,KAAK4T,UACjBK,EAAajU,KAAKiU,WAClBQ,EAAc,GAElB,GAAIzU,KAAK2H,QAAU3H,KAAKsW,UAAY5mB,EAAcmL,GAAO,CACvD,IAAIqa,GAAc,EAEd7U,EAAQuG,WACNjW,EAASkK,EAAKkS,SAAWlS,EAAKkS,SAAW6G,EAAU7G,SACrD6G,EAAU7G,OAASlS,EAAKkS,OACxBmI,GAAc,GAId7U,EAAQwG,WACNlW,EAASkK,EAAKmS,SAAWnS,EAAKmS,SAAW4G,EAAU5G,SACrD4G,EAAU5G,OAASnS,EAAKmS,OACxBkI,GAAc,GAGZvkB,EAASkK,EAAKoS,SAAWpS,EAAKoS,SAAW2G,EAAU3G,SACrD2G,EAAU3G,OAASpS,EAAKoS,OACxBiI,GAAc,IAIdA,GACFlV,KAAKoT,cAAa,GAAM,GAG1B,IAAI2E,EAAQnE,EAAUvF,MAAQuF,EAAU5E,aAEpCre,EAASkK,EAAKlH,KAChB8gB,EAAY3I,KAAOjR,EAAKlH,EAAIokB,EAAQ9D,EAAWnI,MAG7Cnb,EAASkK,EAAKwgB,KAChB5G,EAAYxI,IAAMpR,EAAKwgB,EAAItD,EAAQ9D,EAAWhI,KAG5Ctb,EAASkK,EAAKwT,SAChBoG,EAAYpG,MAAQxT,EAAKwT,MAAQ0J,GAG/BpnB,EAASkK,EAAKuT,UAChBqG,EAAYrG,OAASvT,EAAKuT,OAAS2J,GAGrC/X,KAAKuZ,eAAe9E,GAGtB,OAAOzU,MAOT+d,iBAAkB,WAChB,OAAO/d,KAAK2H,MAAQpH,EAAO,GAAIP,KAAKyT,eAAiB,IAOvDuK,aAAc,WACZ,OAAOhe,KAAKie,MAAQ1d,EAAO,GAAIP,KAAK4T,WAAa,IAOnDwF,cAAe,WACb,IAAInF,EAAajU,KAAKiU,WAClBpZ,EAAO,GAQX,OANImF,KAAK2H,OACP9X,EAAQ,CAAC,OAAQ,MAAO,QAAS,SAAU,eAAgB,kBAAkB,SAAUhB,GACrFgM,EAAKhM,GAAKolB,EAAWplB,MAIlBgM,GAQTye,cAAe,SAAuBze,GACpC,IAAIoZ,EAAajU,KAAKiU,WAClBnO,EAAcmO,EAAWnO,YAsB7B,OApBI9F,KAAK2H,QAAU3H,KAAKsW,UAAY5mB,EAAcmL,KAC5ClK,EAASkK,EAAKiR,QAChBmI,EAAWnI,KAAOjR,EAAKiR,MAGrBnb,EAASkK,EAAKoR,OAChBgI,EAAWhI,IAAMpR,EAAKoR,KAGpBtb,EAASkK,EAAKwT,QAChB4F,EAAW5F,MAAQxT,EAAKwT,MACxB4F,EAAW7F,OAASvT,EAAKwT,MAAQvI,GACxBnV,EAASkK,EAAKuT,UACvB6F,EAAW7F,OAASvT,EAAKuT,OACzB6F,EAAW5F,MAAQxT,EAAKuT,OAAStI,GAGnC9F,KAAKoT,cAAa,IAGbpT,MAOTqZ,eAAgB,WACd,IACIxe,EADA4Z,EAAczU,KAAKyU,YAYvB,OATIzU,KAAK2H,OAAS3H,KAAKqT,UACrBxY,EAAO,CACLiR,KAAM2I,EAAY3I,KAClBG,IAAKwI,EAAYxI,IACjBoC,MAAOoG,EAAYpG,MACnBD,OAAQqG,EAAYrG,SAIjBvT,GAAQ,IAQjB0e,eAAgB,SAAwB1e,GACtC,IAEIqjB,EACAC,EAHA1J,EAAczU,KAAKyU,YACnB3O,EAAc9F,KAAKK,QAAQyF,YAkC/B,OA9BI9F,KAAK2H,OAAS3H,KAAKqT,UAAYrT,KAAKsW,UAAY5mB,EAAcmL,KAC5DlK,EAASkK,EAAKiR,QAChB2I,EAAY3I,KAAOjR,EAAKiR,MAGtBnb,EAASkK,EAAKoR,OAChBwI,EAAYxI,IAAMpR,EAAKoR,KAGrBtb,EAASkK,EAAKwT,QAAUxT,EAAKwT,QAAUoG,EAAYpG,QACrD6P,GAAe,EACfzJ,EAAYpG,MAAQxT,EAAKwT,OAGvB1d,EAASkK,EAAKuT,SAAWvT,EAAKuT,SAAWqG,EAAYrG,SACvD+P,GAAgB,EAChB1J,EAAYrG,OAASvT,EAAKuT,QAGxBtI,IACEoY,EACFzJ,EAAYrG,OAASqG,EAAYpG,MAAQvI,EAChCqY,IACT1J,EAAYpG,MAAQoG,EAAYrG,OAAStI,IAI7C9F,KAAKsT,iBAGAtT,MAQToe,iBAAkB,WAChB,IAAI/d,EAAUzO,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK,GAElF,IAAKoO,KAAK2H,QAAUrW,OAAO+sB,kBACzB,OAAO,KAGT,IAAIpK,EAAajU,KAAKiU,WAClBzU,EAASiP,GAAgBzO,KAAK0O,MAAO1O,KAAK4T,UAAWK,EAAY5T,GAErE,IAAKL,KAAKqT,QACR,OAAO7T,EAGT,IAAI8e,EAAgBte,KAAK6J,UACrB0U,EAAWD,EAAc3qB,EACzB6qB,EAAWF,EAAcjD,EACzBoD,EAAeH,EAAcjQ,MAC7BqQ,EAAgBJ,EAAclQ,OAE9B2J,EAAQvY,EAAO6O,MAAQ1Y,KAAK8b,MAAMwC,EAAWjF,cAEnC,IAAV+I,IACFwG,GAAYxG,EACZyG,GAAYzG,EACZ0G,GAAgB1G,EAChB2G,GAAiB3G,GAGnB,IAAIjS,EAAc2Y,EAAeC,EAC7BlO,EAAWtC,GAAiB,CAC9BpI,YAAaA,EACbuI,MAAOhO,EAAQwP,UAAY3H,IAC3BkG,OAAQ/N,EAAQ0P,WAAa7H,MAE3BuI,EAAWvC,GAAiB,CAC9BpI,YAAaA,EACbuI,MAAOhO,EAAQ4P,UAAY,EAC3B7B,OAAQ/N,EAAQ8P,WAAa,GAC5B,SAECuE,EAAoBxG,GAAiB,CACvCpI,YAAaA,EACbuI,MAAOhO,EAAQgO,QAAoB,IAAV0J,EAAcvY,EAAO6O,MAAQoQ,GACtDrQ,OAAQ/N,EAAQ+N,SAAqB,IAAV2J,EAAcvY,EAAO4O,OAASsQ,KAEvDrQ,EAAQqG,EAAkBrG,MAC1BD,EAASsG,EAAkBtG,OAE/BC,EAAQ1Y,KAAK+a,IAAIF,EAASnC,MAAO1Y,KAAKgb,IAAIF,EAASpC,MAAOA,IAC1DD,EAASzY,KAAK+a,IAAIF,EAASpC,OAAQzY,KAAKgb,IAAIF,EAASrC,OAAQA,IAC7D,IAAIgC,EAAS7e,SAAS8e,cAAc,UAChCC,EAAUF,EAAOG,WAAW,MAChCH,EAAO/B,MAAQ3F,EAAuB2F,GACtC+B,EAAOhC,OAAS1F,EAAuB0F,GACvCkC,EAAQU,UAAY3Q,EAAQkP,WAAa,cACzCe,EAAQW,SAAS,EAAG,EAAG5C,EAAOD,GAC9B,IAAIuQ,EAAwBte,EAAQoP,sBAChCA,OAAkD,IAA1BkP,GAA0CA,EAClEhP,EAAwBtP,EAAQsP,sBACpCW,EAAQb,sBAAwBA,EAE5BE,IACFW,EAAQX,sBAAwBA,GAIlC,IAKIiP,EACAC,EAEAC,EACAC,EACAC,EACAC,EAXAC,EAAc1f,EAAO6O,MACrB8Q,EAAe3f,EAAO4O,OAEtBgR,EAAOb,EACPc,EAAOb,EASPY,IAASX,GAAgBW,EAAOF,GAClCE,EAAO,EACPR,EAAW,EACXE,EAAO,EACPE,EAAW,GACFI,GAAQ,GACjBN,GAAQM,EACRA,EAAO,EAEPJ,EADAJ,EAAWjpB,KAAK+a,IAAIwO,EAAaT,EAAeW,IAEvCA,GAAQF,IACjBJ,EAAO,EAEPE,EADAJ,EAAWjpB,KAAK+a,IAAI+N,EAAcS,EAAcE,IAI9CR,GAAY,GAAKS,IAASX,GAAiBW,EAAOF,GACpDE,EAAO,EACPR,EAAY,EACZE,EAAO,EACPE,EAAY,GACHI,GAAQ,GACjBN,GAAQM,EACRA,EAAO,EAEPJ,EADAJ,EAAYlpB,KAAK+a,IAAIyO,EAAcT,EAAgBW,IAE1CA,GAAQF,IACjBJ,EAAO,EAEPE,EADAJ,EAAYlpB,KAAK+a,IAAIgO,EAAeS,EAAeE,IAIrD,IAAI3lB,EAAS,CAAC0lB,EAAMC,EAAMT,EAAUC,GAEpC,GAAIG,EAAW,GAAKC,EAAY,EAAG,CACjC,IAAI5N,EAAQhD,EAAQoQ,EACpB/kB,EAAOK,KAAK+kB,EAAOzN,EAAO0N,EAAO1N,EAAO2N,EAAW3N,EAAO4N,EAAY5N,GAQxE,OAHAf,EAAQgB,UAAUla,MAAMkZ,EAAS,CAAC9Q,GAAQtI,OAAOqM,EAAmB7J,EAAO6X,KAAI,SAAUC,GACvF,OAAO7b,KAAK8b,MAAM/I,EAAuB8I,UAEpCpB,GAQTkP,eAAgB,SAAwBxZ,GACtC,IAAIzF,EAAUL,KAAKK,QAenB,OAbKL,KAAKsW,UAAa9mB,EAAYsW,KAEjCzF,EAAQyF,YAAcnQ,KAAKgb,IAAI,EAAG7K,IAAgBD,IAE9C7F,KAAK2H,QACP3H,KAAKmT,cAEDnT,KAAKqT,SACPrT,KAAKsT,kBAKJtT,MAQTwZ,YAAa,SAAqBjrB,GAChC,IAAI8R,EAAUL,KAAKK,QACfoZ,EAAUzZ,KAAKyZ,QACfrD,EAAOpW,KAAKoW,KAEhB,GAAIpW,KAAK2H,QAAU3H,KAAKsW,SAAU,CAChC,IAAIiJ,EAh9FW,SAg9FChxB,EACZoY,EAAUtG,EAAQsG,SAh9FP,SAg9FkBpY,EACjCA,EAAOgxB,GAAa5Y,EAAUpY,EAh9Ff,OAi9Ff8R,EAAQsF,SAAWpX,EACnByb,EAAQyP,EAASzU,EAAazW,GAC9Bkb,EAAYgQ,EAAShV,EAAY8a,GACjC9V,EAAYgQ,EAAS1U,EAAY4B,GAE5BtG,EAAQ6G,iBAEX8C,EAAQoM,EAAMpR,EAAazW,GAC3Bkb,EAAY2M,EAAM3R,EAAY8a,GAC9B9V,EAAY2M,EAAMrR,EAAY4B,IAIlC,OAAO3G,OAIPwf,GAAiBnb,EAAOob,QAExBA,GAAuB,WAMzB,SAASA,EAAQ1W,GACf,IAAI1I,EAAUzO,UAAU5B,OAAS,QAAsBgE,IAAjBpC,UAAU,GAAmBA,UAAU,GAAK,GAIlF,GAFAmR,EAAgB/C,KAAMyf,IAEjB1W,IAAYvD,EAAgBzB,KAAKgF,EAAQ2W,SAC5C,MAAM,IAAI5qB,MAAM,4EAGlBkL,KAAK+I,QAAUA,EACf/I,KAAKK,QAAUE,EAAO,GAAIkF,EAAU/V,EAAc2Q,IAAYA,GAC9DL,KAAKqT,SAAU,EACfrT,KAAKsW,UAAW,EAChBtW,KAAKua,SAAW,GAChBva,KAAK2H,OAAQ,EACb3H,KAAK2f,WAAY,EACjB3f,KAAKyc,UAAW,EAChBzc,KAAKie,OAAQ,EACbje,KAAK4f,QAAS,EACd5f,KAAK6f,OAnlGT,IAAsB5c,EAAa6c,EAAYC,EAi9G7C,OAj9GoB9c,EAslGPwc,EAtlGgCM,EA+7GzC,CAAC,CACHpxB,IAAK,aACLN,MAAO,WAEL,OADAiD,OAAOmuB,QAAUD,GACVC,IAOR,CACD9wB,IAAK,cACLN,MAAO,SAAqBgS,GAC1BE,EAAOkF,EAAU/V,EAAc2Q,IAAYA,OA78Gdyf,EAslGX,CAAC,CACrBnxB,IAAK,OACLN,MAAO,WACL,IAEIoL,EAFAsP,EAAU/I,KAAK+I,QACf2W,EAAU3W,EAAQ2W,QAAQ1hB,cAG9B,IAAI+K,EAAO,QAAX,CAMA,GAFAA,EAAO,QAAc/I,KAEL,QAAZ0f,EAAmB,CAMrB,GALA1f,KAAKwc,OAAQ,EAEb/iB,EAAMsP,EAAQgB,aAAa,QAAU,GACrC/J,KAAK+c,YAActjB,GAEdA,EACH,OAIFA,EAAMsP,EAAQ4N,QACO,WAAZ+I,GAAwBpuB,OAAO+sB,oBACxC5kB,EAAMsP,EAAQiX,aAGhBhgB,KAAK2c,KAAKljB,MAEX,CACD9K,IAAK,OACLN,MAAO,SAAcoL,GACnB,IAAIkgB,EAAQ3Z,KAEZ,GAAKvG,EAAL,CAIAuG,KAAKvG,IAAMA,EACXuG,KAAK4T,UAAY,GACjB,IAAI7K,EAAU/I,KAAK+I,QACf1I,EAAUL,KAAKK,QAOnB,GALKA,EAAQuG,WAAcvG,EAAQwG,WACjCxG,EAAQ8F,kBAAmB,GAIxB9F,EAAQ8F,kBAAqB7U,OAAOf,YAMzC,GAAI+U,EAAgBvB,KAAKtK,GAEnB8L,EAAqBxB,KAAKtK,GAC5BuG,KAAKjC,MA9sETkiB,EA8sEmCxmB,EA9sElBtH,QAAQwf,GAAsB,IAC/CuO,EAASC,KAAKF,GACdpO,EAAc,IAAIthB,YAAY2vB,EAAOlwB,QAEzCH,EADIuwB,EAAQ,IAAIrsB,WAAW8d,IACZ,SAAUxjB,EAAOjB,GAC9BgzB,EAAMhzB,GAAK8yB,EAAO5tB,WAAWlF,MAExBykB,IA2sEC7R,KAAKqgB,YAPT,CA5sEN,IACMJ,EACAC,EACArO,EACAuO,EAutEIE,EAAM,IAAI5lB,eACV2lB,EAAQrgB,KAAKqgB,MAAMzxB,KAAKoR,MAC5BA,KAAK2f,WAAY,EACjB3f,KAAKsgB,IAAMA,EAKXA,EAAI7iB,QAAU4iB,EACdC,EAAI5iB,QAAU2iB,EACdC,EAAI3iB,UAAY0iB,EAEhBC,EAAIC,WAAa,WAjkGF,eAmkGTD,EAAIE,kBAAkB,iBACxBF,EAAI5hB,SAIR4hB,EAAIG,OAAS,WACX9G,EAAM5b,KAAKuiB,EAAIjjB,WAGjBijB,EAAII,UAAY,WACd/G,EAAMgG,WAAY,EAClBhG,EAAM2G,IAAM,MAIVjgB,EAAQ6F,kBAAoBoG,GAAiB7S,IAAQsP,EAAQyN,cAC/D/c,EAAMiT,GAAajT,IAIrB6mB,EAAIxjB,KAAK,MAAOrD,GAAK,GACrB6mB,EAAIhjB,aAAe,cACnBgjB,EAAIxiB,gBAA0C,oBAAxBiL,EAAQyN,YAC9B8J,EAAI3hB,YAzDFqB,KAAKqgB,WA2DR,CACD1xB,IAAK,OACLN,MAAO,SAAcwjB,GACnB,IAAIxR,EAAUL,KAAKK,QACfuT,EAAY5T,KAAK4T,UAGjB9B,EAAcF,GAAuBC,GACrC9E,EAAS,EACTC,EAAS,EACTC,EAAS,EAEb,GAAI6E,EAAc,EAAG,CAEnB9R,KAAKvG,IA/vEb,SAA8BoY,EAAa8O,GAMzC,IALA,IAAIC,EAAS,GAGTR,EAAQ,IAAIrsB,WAAW8d,GAEpBuO,EAAMpwB,OAAS,GAGpB4wB,EAAO7mB,KAAK2X,GAAata,MAAM,KAAMgR,EAAQgY,EAAMS,SAAS,EAN9C,SAOdT,EAAQA,EAAMS,SAPA,MAUhB,MAAO,QAAQ3pB,OAAOypB,EAAU,YAAYzpB,OAAOyF,KAAKikB,EAAO5mB,KAAK,MAkvEnD8mB,CAAqBjP,EA1mGnB,cA4mGb,IAAIkP,EA5pEZ,SAA0BjP,GACxB,IAAI/E,EAAS,EACTC,EAAS,EACTC,EAAS,EAEb,OAAQ6E,GAEN,KAAK,EACH9E,GAAU,EACV,MAGF,KAAK,EACHD,GAAU,IACV,MAGF,KAAK,EACHE,GAAU,EACV,MAGF,KAAK,EACHF,EAAS,GACTE,GAAU,EACV,MAGF,KAAK,EACHF,EAAS,GACT,MAGF,KAAK,EACHA,EAAS,GACTC,GAAU,EACV,MAGF,KAAK,EACHD,GAAU,GAId,MAAO,CACLA,OAAQA,EACRC,OAAQA,EACRC,OAAQA,GA6mEoB+T,CAAiBlP,GAEzC/E,EAASgU,EAAkBhU,OAC3BC,EAAS+T,EAAkB/T,OAC3BC,EAAS8T,EAAkB9T,OAGzB5M,EAAQuG,YACVgN,EAAU7G,OAASA,GAGjB1M,EAAQwG,WACV+M,EAAU5G,OAASA,EACnB4G,EAAU3G,OAASA,GAGrBjN,KAAKqgB,UAEN,CACD1xB,IAAK,QACLN,MAAO,WACL,IAAI0a,EAAU/I,KAAK+I,QACftP,EAAMuG,KAAKvG,IACX+c,EAAczN,EAAQyN,YACtBC,EAAiBhd,EAEjBuG,KAAKK,QAAQ6F,kBAAoBoG,GAAiB7S,KAC/C+c,IACHA,EAAc,aAIhBC,EAAiB/J,GAAajT,IAGhCuG,KAAKwW,YAAcA,EACnBxW,KAAKyW,eAAiBA,EACtB,IAAI/H,EAAQnd,SAAS8e,cAAc,OAE/BmG,IACF9H,EAAM8H,YAAcA,GAGtB9H,EAAMiI,IAAMF,GAAkBhd,EAC9BiV,EAAMgI,IAAM3N,EAAQ2N,KAAO,oBAC3B1W,KAAK0O,MAAQA,EACbA,EAAM+R,OAASzgB,KAAKuS,MAAM3jB,KAAKoR,MAC/B0O,EAAMhR,QAAUsC,KAAKihB,KAAKryB,KAAKoR,MAC/BkJ,EAASwF,EAAO9J,GAChBmE,EAAQmY,WAAWC,aAAazS,EAAO3F,EAAQqY,eAEhD,CACDzyB,IAAK,QACLN,MAAO,WACL,IAAIgzB,EAASrhB,KAET0O,EAAQ1O,KAAK0O,MACjBA,EAAM+R,OAAS,KACf/R,EAAMhR,QAAU,KAChBsC,KAAK4f,QAAS,EAGd,IAAI0B,EAAcjd,EAAOjT,WAAa,sCAAsC2S,KAAKM,EAAOjT,UAAUmwB,WAE9FC,EAAO,SAAcxS,EAAcE,GACrC3O,EAAO8gB,EAAOzN,UAAW,CACvB5E,aAAcA,EACdE,cAAeA,EACfpJ,YAAakJ,EAAeE,IAE9BmS,EAAOhF,iBAAmB9b,EAAO,GAAI8gB,EAAOzN,WAC5CyN,EAAOzB,QAAS,EAChByB,EAAOpD,OAAQ,EAEfoD,EAAOI,SAIT,IAAI/S,EAAMM,cAAiBsS,EAA3B,CAKA,IAAII,EAAcnwB,SAAS8e,cAAc,OACrCsR,EAAOpwB,SAASowB,MAAQpwB,SAASgT,gBACrCvE,KAAK0hB,YAAcA,EAEnBA,EAAYjB,OAAS,WACnBe,EAAKE,EAAYrT,MAAOqT,EAAYtT,QAE/BkT,GACHK,EAAKC,YAAYF,IAIrBA,EAAY/K,IAAMjI,EAAMiI,IAGnB2K,IACHI,EAAYzY,MAAMsO,QAAU,uJAC5BoK,EAAK9K,YAAY6K,SArBjBF,EAAK9S,EAAMM,aAAcN,EAAMQ,iBAwBlC,CACDvgB,IAAK,OACLN,MAAO,WACL,IAAIqgB,EAAQ1O,KAAK0O,MACjBA,EAAM+R,OAAS,KACf/R,EAAMhR,QAAU,KAChBgR,EAAMwS,WAAWU,YAAYlT,GAC7B1O,KAAK0O,MAAQ,OAEd,CACD/f,IAAK,QACLN,MAAO,WACL,GAAK2R,KAAKie,QAASje,KAAK2H,MAAxB,CAIA,IAAIoB,EAAU/I,KAAK+I,QACf1I,EAAUL,KAAKK,QACfqO,EAAQ1O,KAAK0O,MAEb6E,EAAYxK,EAAQmY,WACpBW,EAAWtwB,SAAS8e,cAAc,OACtCwR,EAASvK,UArpGA,orCAspGT,IAAI9D,EAAUqO,EAAS3K,cAAc,IAAIhgB,OAxxG/B,UAwxGiD,eACvDkZ,EAASoD,EAAQ0D,cAAc,IAAIhgB,OAzxG7B,UAyxG+C,YACrDuiB,EAAUjG,EAAQ0D,cAAc,IAAIhgB,OA1xG9B,UA0xGgD,cACtDmf,EAAU7C,EAAQ0D,cAAc,IAAIhgB,OA3xG9B,UA2xGgD,cACtDkf,EAAOC,EAAQa,cAAc,IAAIhgB,OA5xG3B,UA4xG6C,UACvD8I,KAAKuT,UAAYA,EACjBvT,KAAKwT,QAAUA,EACfxT,KAAKoQ,OAASA,EACdpQ,KAAKyZ,QAAUA,EACfzZ,KAAKqW,QAAUA,EACfrW,KAAK4W,QAAUpD,EAAQ0D,cAAc,IAAIhgB,OAlyG/B,UAkyGiD,cAC3D8I,KAAKoW,KAAOA,EACZhG,EAAOyG,YAAYnI,GAEnBxF,EAASH,EAASpE,GAElB4O,EAAU4N,aAAa3N,EAASzK,EAAQqY,aAEnCphB,KAAKwc,OACRjT,EAAYmF,EAAO9J,GAGrB5E,KAAKuW,cACLvW,KAAKpR,OACLyR,EAAQuF,mBAAqBjQ,KAAKgb,IAAI,EAAGtQ,EAAQuF,qBAAuBC,IACxExF,EAAQyF,YAAcnQ,KAAKgb,IAAI,EAAGtQ,EAAQyF,cAAgBD,IAC1DxF,EAAQqF,SAAW/P,KAAKgb,IAAI,EAAGhb,KAAK+a,IAAI,EAAG/a,KAAKiT,MAAMvI,EAAQqF,aAAe,EAC7EwD,EAASmN,EAAS1R,GAEbtE,EAAQgG,QACX6C,EAASmN,EAAQyL,uBAAuB,GAAG5qB,OAtzGnC,UAszGqD,YAAayN,GAGvEtE,EAAQiG,QACX4C,EAASmN,EAAQyL,uBAAuB,GAAG5qB,OA1zGnC,UA0zGqD,YAAayN,GAGxEtE,EAAQmG,YACV0C,EAASsK,EAAS,GAAGtc,OA9zGb,UA8zG+B,QAGpCmJ,EAAQkG,WACX2C,EAASkN,EAAMvR,GAGbxE,EAAQ6G,iBACVgC,EAASkN,EAAMrR,GACfiF,EAAQoM,EAAMpR,EAr0GL,QAw0GN3E,EAAQ8G,mBACX+B,EAASmN,EAAQyL,uBAAuB,GAAG5qB,OA30GnC,UA20GqD,UAAWyN,GACxEuE,EAASmN,EAAQyL,uBAAuB,GAAG5qB,OA50GnC,UA40GqD,WAAYyN,IAG3E3E,KAAKgT,SACLhT,KAAK2H,OAAQ,EACb3H,KAAKwZ,YAAYnZ,EAAQsF,UAErBtF,EAAQoG,UACVzG,KAAK+H,OAGP/H,KAAKgK,QAAQ3J,EAAQxF,MAEjBjL,EAAWyQ,EAAQsH,QACrBmD,GAAY/B,EAjzGF,QAizGwB1I,EAAQsH,MAAO,CAC/C0C,MAAM,IAIVc,GAAcpC,EAtzGF,YAwzGb,CACDpa,IAAK,UACLN,MAAO,WACA2R,KAAK2H,QAIV3H,KAAK2H,OAAQ,EACb3H,KAAKgZ,SACLhZ,KAAKwX,eACLxX,KAAKwT,QAAQ0N,WAAWU,YAAY5hB,KAAKwT,SACzCjK,EAAYvJ,KAAK+I,QAASpE,MAE3B,CACDhW,IAAK,WACLN,MAAO,WACD2R,KAAK2H,OACP3H,KAAK+hB,UACL/hB,KAAK2H,OAAQ,EACb3H,KAAKqT,SAAU,GACNrT,KAAK4f,QACd5f,KAAK0hB,YAAYjB,OAAS,KAC1BzgB,KAAK4f,QAAS,EACd5f,KAAKie,OAAQ,GACJje,KAAK2f,WACd3f,KAAKsgB,IAAI7iB,QAAU,KACnBuC,KAAKsgB,IAAI5hB,SACAsB,KAAK0O,OACd1O,KAAKihB,YAt7GK/d,EAAkBD,EAAYjU,UAAW8wB,GACrDC,GAAa7c,EAAkBD,EAAa8c,GA+8GzCN,EAvZkB,GA4Z3B,OAFAlf,EAAOkf,GAAQzwB,UAAWgkB,GAAQjN,GAASkS,GAAQgB,GAAU6B,GAAQqB,IAE9DsD,IAjiHY,WAAnB,EAAOvyB,SAA0C,IAAXC,EAAyBA,EAAOD,QAAUkV,SAC7B,0BAAN4f,KAAM,mC,4PCVpD,WACG,aAEA,IAAIC,EAAK,CACLC,WAAY,OACZC,SAAU,OACVC,SAAU,OACVC,cAAe,OACftqB,OAAQ,UACRuqB,YAAa,eACbC,KAAM,MACNC,SAAU,OACVC,KAAM,YACNC,OAAQ,WACRC,YAAa,2FACbh0B,IAAK,sBACLi0B,WAAY,wBACZC,aAAc,aACdC,KAAM,SAGV,SAASC,EAAQp0B,GAEb,OAAOq0B,EAAeC,EAAct0B,GAAMiD,WAG9C,SAASsxB,EAASC,EAAKC,GACnB,OAAOL,EAAQ3rB,MAAM,KAAM,CAAC+rB,GAAKjsB,OAAOksB,GAAQ,KAGpD,SAASJ,EAAeK,EAAYD,GAChC,IAAiD5a,EAAkBpb,EAAGk2B,EAAGC,EAAIC,EAAKC,EAAeC,EAAYC,EAAab,EAAtHc,EAAS,EAAGC,EAAcR,EAAWrzB,OAAagmB,EAAS,GAC/D,IAAK5oB,EAAI,EAAGA,EAAIy2B,EAAaz2B,IACzB,GAA6B,iBAAlBi2B,EAAWj2B,GAClB4oB,GAAUqN,EAAWj2B,QAEpB,GAA6B,WAAzB,EAAOi2B,EAAWj2B,IAAiB,CAExC,IADAm2B,EAAKF,EAAWj2B,IACTyS,KAEH,IADA2I,EAAM4a,EAAKQ,GACNN,EAAI,EAAGA,EAAIC,EAAG1jB,KAAK7P,OAAQszB,IAAK,CACjC,GAAWtvB,MAAPwU,EACA,MAAM,IAAI1T,MAAMiuB,EAAQ,gEAAiEQ,EAAG1jB,KAAKyjB,GAAIC,EAAG1jB,KAAKyjB,EAAE,KAEnH9a,EAAMA,EAAI+a,EAAG1jB,KAAKyjB,SAItB9a,EADK+a,EAAGO,SACFV,EAAKG,EAAGO,UAGRV,EAAKQ,KAOf,GAJI3B,EAAGG,SAASre,KAAKwf,EAAGnnB,OAAS6lB,EAAGI,cAActe,KAAKwf,EAAGnnB,OAASoM,aAAe3V,WAC9E2V,EAAMA,KAGNyZ,EAAGK,YAAYve,KAAKwf,EAAGnnB,OAAyB,iBAARoM,GAAoBhT,MAAMgT,GAClE,MAAM,IAAIzV,UAAUgwB,EAAQ,0CAA2Cva,IAO3E,OAJIyZ,EAAGlqB,OAAOgM,KAAKwf,EAAGnnB,QAClBunB,EAAcnb,GAAO,GAGjB+a,EAAGnnB,MACP,IAAK,IACDoM,EAAM1S,SAAS0S,EAAK,IAAInZ,SAAS,GACjC,MACJ,IAAK,IACDmZ,EAAMjS,OAAOmb,aAAa5b,SAAS0S,EAAK,KACxC,MACJ,IAAK,IACL,IAAK,IACDA,EAAM1S,SAAS0S,EAAK,IACpB,MACJ,IAAK,IACDA,EAAM/S,KAAKlD,UAAUiW,EAAK,KAAM+a,EAAGlV,MAAQvY,SAASytB,EAAGlV,OAAS,GAChE,MACJ,IAAK,IACD7F,EAAM+a,EAAGQ,UAAYluB,WAAW2S,GAAKwb,cAAcT,EAAGQ,WAAaluB,WAAW2S,GAAKwb,gBACnF,MACJ,IAAK,IACDxb,EAAM+a,EAAGQ,UAAYluB,WAAW2S,GAAKyb,QAAQV,EAAGQ,WAAaluB,WAAW2S,GACxE,MACJ,IAAK,IACDA,EAAM+a,EAAGQ,UAAYxtB,OAAOX,OAAO4S,EAAI0b,YAAYX,EAAGQ,aAAeluB,WAAW2S,GAChF,MACJ,IAAK,IACDA,GAAO1S,SAAS0S,EAAK,MAAQ,GAAGnZ,SAAS,GACzC,MACJ,IAAK,IACDmZ,EAAMjS,OAAOiS,GACbA,EAAO+a,EAAGQ,UAAYvb,EAAI2b,UAAU,EAAGZ,EAAGQ,WAAavb,EACvD,MACJ,IAAK,IACDA,EAAMjS,SAASiS,GACfA,EAAO+a,EAAGQ,UAAYvb,EAAI2b,UAAU,EAAGZ,EAAGQ,WAAavb,EACvD,MACJ,IAAK,IACDA,EAAM1a,OAAOkB,UAAUK,SAAS9B,KAAKib,GAAK7W,MAAM,GAAI,GAAGqM,cACvDwK,EAAO+a,EAAGQ,UAAYvb,EAAI2b,UAAU,EAAGZ,EAAGQ,WAAavb,EACvD,MACJ,IAAK,IACDA,EAAM1S,SAAS0S,EAAK,MAAQ,EAC5B,MACJ,IAAK,IACDA,EAAMA,EAAI4b,UACV5b,EAAO+a,EAAGQ,UAAYvb,EAAI2b,UAAU,EAAGZ,EAAGQ,WAAavb,EACvD,MACJ,IAAK,IACDA,GAAO1S,SAAS0S,EAAK,MAAQ,GAAGnZ,SAAS,IACzC,MACJ,IAAK,IACDmZ,GAAO1S,SAAS0S,EAAK,MAAQ,GAAGnZ,SAAS,IAAI0N,cAGjDklB,EAAGM,KAAKxe,KAAKwf,EAAGnnB,MAChB4Z,GAAUxN,IAGNyZ,EAAGlqB,OAAOgM,KAAKwf,EAAGnnB,OAAWunB,IAAeJ,EAAGT,KAK/CA,EAAO,IAJPA,EAAOa,EAAc,IAAM,IAC3Bnb,EAAMA,EAAInZ,WAAW8C,QAAQ8vB,EAAGa,KAAM,KAK1CW,EAAgBF,EAAGc,SAA2B,MAAhBd,EAAGc,SAAmB,IAAMd,EAAGc,SAASxiB,OAAO,GAAK,IAClF6hB,EAAaH,EAAGlV,OAASyU,EAAOta,GAAKxY,OACrCwzB,EAAMD,EAAGlV,OAASqV,EAAa,EAAID,EAAca,OAAOZ,GAAoB,GAC5E1N,GAAUuN,EAAGgB,MAAQzB,EAAOta,EAAMgb,EAAyB,MAAlBC,EAAwBX,EAAOU,EAAMhb,EAAMgb,EAAMV,EAAOta,GAI7G,OAAOwN,EAGX,IAAIwO,EAAgB12B,OAAOY,OAAO,MAElC,SAASu0B,EAAcE,GACnB,GAAIqB,EAAcrB,GACd,OAAOqB,EAAcrB,GAIzB,IADA,IAAgBrrB,EAAZ2sB,EAAOtB,EAAYE,EAAa,GAAIqB,EAAY,EAC7CD,GAAM,CACT,GAAqC,QAAhC3sB,EAAQmqB,EAAGQ,KAAKkC,KAAKF,IACtBpB,EAAWtpB,KAAKjC,EAAM,SAErB,GAAuC,QAAlCA,EAAQmqB,EAAGS,OAAOiC,KAAKF,IAC7BpB,EAAWtpB,KAAK,SAEf,IAA4C,QAAvCjC,EAAQmqB,EAAGU,YAAYgC,KAAKF,IA6ClC,MAAM,IAAI9xB,YAAY,oCA5CtB,GAAImF,EAAM,GAAI,CACV4sB,GAAa,EACb,IAAIE,EAAa,GAAIC,EAAoB/sB,EAAM,GAAIgtB,EAAc,GACjE,GAAuD,QAAlDA,EAAc7C,EAAGtzB,IAAIg2B,KAAKE,IAe3B,MAAM,IAAIlyB,YAAY,gDAbtB,IADAiyB,EAAW7qB,KAAK+qB,EAAY,IACwD,MAA5ED,EAAoBA,EAAkBV,UAAUW,EAAY,GAAG90B,UACnE,GAA8D,QAAzD80B,EAAc7C,EAAGW,WAAW+B,KAAKE,IAClCD,EAAW7qB,KAAK+qB,EAAY,QAE3B,IAAgE,QAA3DA,EAAc7C,EAAGY,aAAa8B,KAAKE,IAIzC,MAAM,IAAIlyB,YAAY,gDAHtBiyB,EAAW7qB,KAAK+qB,EAAY,IAUxChtB,EAAM,GAAK8sB,OAGXF,GAAa,EAEjB,GAAkB,IAAdA,EACA,MAAM,IAAI5vB,MAAM,6EAGpBuuB,EAAWtpB,KACP,CACI4oB,YAAa7qB,EAAM,GACnBgsB,SAAahsB,EAAM,GACnB+H,KAAa/H,EAAM,GACnBgrB,KAAahrB,EAAM,GACnBusB,SAAavsB,EAAM,GACnBysB,MAAazsB,EAAM,GACnBuW,MAAavW,EAAM,GACnBisB,UAAajsB,EAAM,GACnBsE,KAAatE,EAAM,KAO/B2sB,EAAOA,EAAKN,UAAUrsB,EAAM,GAAG9H,QAEnC,OAAOw0B,EAAcrB,GAAOE,EAQ5Bn2B,EAAO,QAAc61B,EACrB71B,EAAO,SAAeg2B,EAEJ,oBAAX5xB,SACPA,OAAM,QAAcyxB,EACpBzxB,OAAM,SAAe4xB,OAGX,KAANlB,aACI,MAAO,CACH,QAAWe,EACX,SAAYG,IAHd,+BA3NjB,I,6BCAD,IAAI3pB,EAAQnK,EAAQ,GAChBR,EAAOQ,EAAQ,GACf21B,EAAQ31B,EAAQ,IAChB41B,EAAc51B,EAAQ,IAS1B,SAAS61B,EAAeC,GACtB,IAAI5U,EAAU,IAAIyU,EAAMG,GACpBliB,EAAWpU,EAAKm2B,EAAM/1B,UAAUqN,QAASiU,GAQ7C,OALA/W,EAAM1H,OAAOmR,EAAU+hB,EAAM/1B,UAAWshB,GAGxC/W,EAAM1H,OAAOmR,EAAUsN,GAEhBtN,EAIT,IAAImiB,EAAQF,EAtBG71B,EAAQ,IAyBvB+1B,EAAMJ,MAAQA,EAGdI,EAAMz2B,OAAS,SAAgB02B,GAC7B,OAAOH,EAAeD,EAAYG,EAAM1qB,SAAU2qB,KAIpDD,EAAMplB,OAAS3Q,EAAQ,IACvB+1B,EAAME,YAAcj2B,EAAQ,IAC5B+1B,EAAMG,SAAWl2B,EAAQ,GAGzB+1B,EAAMI,IAAM,SAAaC,GACvB,OAAOzvB,QAAQwvB,IAAIC,IAErBL,EAAMM,OAASr2B,EAAQ,IAEvBjC,EAAOD,QAAUi4B,EAGjBh4B,EAAOD,QAAP,QAAyBi4B,G,6BClDzB,IAAI5rB,EAAQnK,EAAQ,GAChBsM,EAAWtM,EAAQ,GACnBs2B,EAAqBt2B,EAAQ,IAC7Bu2B,EAAkBv2B,EAAQ,IAC1B41B,EAAc51B,EAAQ,IAO1B,SAAS21B,EAAMK,GACbplB,KAAKvF,SAAW2qB,EAChBplB,KAAK4lB,aAAe,CAClBvpB,QAAS,IAAIqpB,EACbroB,SAAU,IAAIqoB,GASlBX,EAAM/1B,UAAUqN,QAAU,SAAiBN,GAGnB,iBAAXA,GACTA,EAASnK,UAAU,IAAM,IAClB6H,IAAM7H,UAAU,GAEvBmK,EAASA,GAAU,IAGrBA,EAASipB,EAAYhlB,KAAKvF,SAAUsB,IAGzBR,OACTQ,EAAOR,OAASQ,EAAOR,OAAOyC,cACrBgC,KAAKvF,SAASc,OACvBQ,EAAOR,OAASyE,KAAKvF,SAASc,OAAOyC,cAErCjC,EAAOR,OAAS,MAIlB,IAAIsqB,EAAQ,CAACF,OAAiB3xB,GAC1BuK,EAAUxI,QAAQiG,QAAQD,GAU9B,IARAiE,KAAK4lB,aAAavpB,QAAQxM,SAAQ,SAAoCi2B,GACpED,EAAME,QAAQD,EAAYE,UAAWF,EAAYG,aAGnDjmB,KAAK4lB,aAAavoB,SAASxN,SAAQ,SAAkCi2B,GACnED,EAAM9rB,KAAK+rB,EAAYE,UAAWF,EAAYG,aAGzCJ,EAAM71B,QACXuO,EAAUA,EAAQC,KAAKqnB,EAAMK,QAASL,EAAMK,SAG9C,OAAO3nB,GAGTwmB,EAAM/1B,UAAUm3B,OAAS,SAAgBpqB,GAEvC,OADAA,EAASipB,EAAYhlB,KAAKvF,SAAUsB,GAC7BL,EAASK,EAAOtC,IAAKsC,EAAOrC,OAAQqC,EAAOpC,kBAAkBxH,QAAQ,MAAO,KAIrFoH,EAAM1J,QAAQ,CAAC,SAAU,MAAO,OAAQ,YAAY,SAA6B0L,GAE/EwpB,EAAM/1B,UAAUuM,GAAU,SAAS9B,EAAKsC,GACtC,OAAOiE,KAAK3D,QAAQ2oB,EAAYjpB,GAAU,GAAI,CAC5CR,OAAQA,EACR9B,IAAKA,SAKXF,EAAM1J,QAAQ,CAAC,OAAQ,MAAO,UAAU,SAA+B0L,GAErEwpB,EAAM/1B,UAAUuM,GAAU,SAAS9B,EAAKoB,EAAMkB,GAC5C,OAAOiE,KAAK3D,QAAQ2oB,EAAYjpB,GAAU,GAAI,CAC5CR,OAAQA,EACR9B,IAAKA,EACLoB,KAAMA,SAKZ1N,EAAOD,QAAU63B,G,6BC3FjB,IAAIxrB,EAAQnK,EAAQ,GAEpB,SAASs2B,IACP1lB,KAAKiZ,SAAW,GAWlByM,EAAmB12B,UAAUo3B,IAAM,SAAaJ,EAAWC,GAKzD,OAJAjmB,KAAKiZ,SAASlf,KAAK,CACjBisB,UAAWA,EACXC,SAAUA,IAELjmB,KAAKiZ,SAASjpB,OAAS,GAQhC01B,EAAmB12B,UAAUq3B,MAAQ,SAAeC,GAC9CtmB,KAAKiZ,SAASqN,KAChBtmB,KAAKiZ,SAASqN,GAAM,OAYxBZ,EAAmB12B,UAAUa,QAAU,SAAiBE,GACtDwJ,EAAM1J,QAAQmQ,KAAKiZ,UAAU,SAAwBsN,GACzC,OAANA,GACFx2B,EAAGw2B,OAKTp5B,EAAOD,QAAUw4B,G,6BCjDjB,IAAInsB,EAAQnK,EAAQ,GAChBo3B,EAAgBp3B,EAAQ,IACxBk2B,EAAWl2B,EAAQ,GACnBqL,EAAWrL,EAAQ,GAKvB,SAASq3B,EAA6B1qB,GAChCA,EAAOuC,aACTvC,EAAOuC,YAAYooB,mBAUvBv5B,EAAOD,QAAU,SAAyB6O,GA6BxC,OA5BA0qB,EAA6B1qB,GAG7BA,EAAOxB,QAAUwB,EAAOxB,SAAW,GAGnCwB,EAAOlB,KAAO2rB,EACZzqB,EAAOlB,KACPkB,EAAOxB,QACPwB,EAAOnB,kBAITmB,EAAOxB,QAAUhB,EAAM/H,MACrBuK,EAAOxB,QAAQe,QAAU,GACzBS,EAAOxB,QAAQwB,EAAOR,SAAW,GACjCQ,EAAOxB,SAGThB,EAAM1J,QACJ,CAAC,SAAU,MAAO,OAAQ,OAAQ,MAAO,QAAS,WAClD,SAA2B0L,UAClBQ,EAAOxB,QAAQgB,OAIZQ,EAAOvB,SAAWC,EAASD,SAE1BuB,GAAQyC,MAAK,SAA6BnB,GAUvD,OATAopB,EAA6B1qB,GAG7BsB,EAASxC,KAAO2rB,EACdnpB,EAASxC,KACTwC,EAAS9C,QACTwB,EAAOjB,mBAGFuC,KACN,SAA4BspB,GAc7B,OAbKrB,EAASqB,KACZF,EAA6B1qB,GAGzB4qB,GAAUA,EAAOtpB,WACnBspB,EAAOtpB,SAASxC,KAAO2rB,EACrBG,EAAOtpB,SAASxC,KAChB8rB,EAAOtpB,SAAS9C,QAChBwB,EAAOjB,qBAKN/E,QAAQkG,OAAO0qB,Q,6BC1E1B,IAAIptB,EAAQnK,EAAQ,GAUpBjC,EAAOD,QAAU,SAAuB2N,EAAMN,EAASqsB,GAMrD,OAJArtB,EAAM1J,QAAQ+2B,GAAK,SAAmB72B,GACpC8K,EAAO9K,EAAG8K,EAAMN,MAGXM,I,cCjBT,IAOIgsB,EACAC,EARAnsB,EAAUxN,EAAOD,QAAU,GAU/B,SAAS65B,IACL,MAAM,IAAIjyB,MAAM,mCAEpB,SAASkyB,IACL,MAAM,IAAIlyB,MAAM,qCAsBpB,SAASmyB,EAAWC,GAChB,GAAIL,IAAqB9M,WAErB,OAAOA,WAAWmN,EAAK,GAG3B,IAAKL,IAAqBE,IAAqBF,IAAqB9M,WAEhE,OADA8M,EAAmB9M,WACZA,WAAWmN,EAAK,GAE3B,IAEI,OAAOL,EAAiBK,EAAK,GAC/B,MAAMh0B,GACJ,IAEI,OAAO2zB,EAAiBt5B,KAAK,KAAM25B,EAAK,GAC1C,MAAMh0B,GAEJ,OAAO2zB,EAAiBt5B,KAAKyS,KAAMknB,EAAK,MAvCnD,WACG,IAEQL,EADsB,mBAAf9M,WACYA,WAEAgN,EAEzB,MAAO7zB,GACL2zB,EAAmBE,EAEvB,IAEQD,EADwB,mBAAjBK,aACcA,aAEAH,EAE3B,MAAO9zB,GACL4zB,EAAqBE,GAjB5B,GAwED,IAEII,EAFAxmB,EAAQ,GACRymB,GAAW,EAEXC,GAAc,EAElB,SAASC,IACAF,GAAaD,IAGlBC,GAAW,EACPD,EAAap3B,OACb4Q,EAAQwmB,EAAalwB,OAAO0J,GAE5B0mB,GAAc,EAEd1mB,EAAM5Q,QACNw3B,KAIR,SAASA,IACL,IAAIH,EAAJ,CAGA,IAAItsB,EAAUksB,EAAWM,GACzBF,GAAW,EAGX,IADA,IAAInjB,EAAMtD,EAAM5Q,OACVkU,GAAK,CAGP,IAFAkjB,EAAexmB,EACfA,EAAQ,KACC0mB,EAAapjB,GACdkjB,GACAA,EAAaE,GAAYG,MAGjCH,GAAc,EACdpjB,EAAMtD,EAAM5Q,OAEhBo3B,EAAe,KACfC,GAAW,EAnEf,SAAyBK,GACrB,GAAIZ,IAAuBK,aAEvB,OAAOA,aAAaO,GAGxB,IAAKZ,IAAuBE,IAAwBF,IAAuBK,aAEvE,OADAL,EAAqBK,aACdA,aAAaO,GAExB,IAEWZ,EAAmBY,GAC5B,MAAOx0B,GACL,IAEI,OAAO4zB,EAAmBv5B,KAAK,KAAMm6B,GACvC,MAAOx0B,GAGL,OAAO4zB,EAAmBv5B,KAAKyS,KAAM0nB,KAgD7CC,CAAgB5sB,IAiBpB,SAAS6sB,EAAKV,EAAK/mB,GACfH,KAAKknB,IAAMA,EACXlnB,KAAKG,MAAQA,EAYjB,SAAS0nB,KA5BTltB,EAAQmtB,SAAW,SAAUZ,GACzB,IAAI5tB,EAAO,IAAInF,MAAMvC,UAAU5B,OAAS,GACxC,GAAI4B,UAAU5B,OAAS,EACnB,IAAK,IAAI5C,EAAI,EAAGA,EAAIwE,UAAU5B,OAAQ5C,IAClCkM,EAAKlM,EAAI,GAAKwE,UAAUxE,GAGhCwT,EAAM7G,KAAK,IAAI6tB,EAAKV,EAAK5tB,IACJ,IAAjBsH,EAAM5Q,QAAiBq3B,GACvBJ,EAAWO,IASnBI,EAAK54B,UAAUy4B,IAAM,WACjBznB,KAAKknB,IAAI9vB,MAAM,KAAM4I,KAAKG,QAE9BxF,EAAQotB,MAAQ,UAChBptB,EAAQqtB,SAAU,EAClBrtB,EAAQstB,IAAM,GACdttB,EAAQyoB,KAAO,GACfzoB,EAAQutB,QAAU,GAClBvtB,EAAQwtB,SAAW,GAInBxtB,EAAQytB,GAAKP,EACbltB,EAAQmQ,YAAc+c,EACtBltB,EAAQ0P,KAAOwd,EACfltB,EAAQ0tB,IAAMR,EACdltB,EAAQ8P,eAAiBod,EACzBltB,EAAQ2tB,mBAAqBT,EAC7BltB,EAAQ4tB,KAAOV,EACfltB,EAAQ6tB,gBAAkBX,EAC1BltB,EAAQ8tB,oBAAsBZ,EAE9BltB,EAAQkQ,UAAY,SAAUld,GAAQ,MAAO,IAE7CgN,EAAQ+tB,QAAU,SAAU/6B,GACxB,MAAM,IAAImH,MAAM,qCAGpB6F,EAAQguB,IAAM,WAAc,MAAO,KACnChuB,EAAQiuB,MAAQ,SAAUC,GACtB,MAAM,IAAI/zB,MAAM,mCAEpB6F,EAAQmuB,MAAQ,WAAa,OAAO,I,6BCrLpC,IAAIvvB,EAAQnK,EAAQ,GAEpBjC,EAAOD,QAAU,SAA6BqN,EAASwuB,GACrDxvB,EAAM1J,QAAQ0K,GAAS,SAAuBlM,EAAOV,GAC/CA,IAASo7B,GAAkBp7B,EAAKoP,gBAAkBgsB,EAAehsB,gBACnExC,EAAQwuB,GAAkB16B,SACnBkM,EAAQ5M,S,6BCNrB,IAAImO,EAAc1M,EAAQ,IAS1BjC,EAAOD,QAAU,SAAgB8O,EAASC,EAAQoB,GAChD,IAAIjC,EAAiBiC,EAAStB,OAAOX,eAChCiC,EAAShC,QAAWD,IAAkBA,EAAeiC,EAAShC,QAGjEY,EAAOH,EACL,mCAAqCuB,EAAShC,OAC9CgC,EAAStB,OACT,KACAsB,EAAShB,QACTgB,IAPFrB,EAAQqB,K,6BCFZlQ,EAAOD,QAAU,SAAsB6R,EAAOhD,EAAQ+C,EAAMzC,EAASgB,GA4BnE,OA3BA0B,EAAMhD,OAASA,EACX+C,IACFC,EAAMD,KAAOA,GAGfC,EAAM1C,QAAUA,EAChB0C,EAAM1B,SAAWA,EACjB0B,EAAMiqB,cAAe,EAErBjqB,EAAMkqB,OAAS,WACb,MAAO,CAELpqB,QAASmB,KAAKnB,QACdlR,KAAMqS,KAAKrS,KAEXu7B,YAAalpB,KAAKkpB,YAClBnxB,OAAQiI,KAAKjI,OAEboxB,SAAUnpB,KAAKmpB,SACfC,WAAYppB,KAAKopB,WACjBC,aAAcrpB,KAAKqpB,aACnBC,MAAOtpB,KAAKspB,MAEZvtB,OAAQiE,KAAKjE,OACb+C,KAAMkB,KAAKlB,OAGRC,I,6BCtCT,IAAIxF,EAAQnK,EAAQ,GAEpBjC,EAAOD,QACLqM,EAAMpI,uBAIK,CACLo4B,MAAO,SAAe57B,EAAMU,EAAOm7B,EAASC,EAAMC,EAAQC,GACxD,IAAIC,EAAS,GACbA,EAAO7vB,KAAKpM,EAAO,IAAMkH,mBAAmBxG,IAExCkL,EAAM5I,SAAS64B,IACjBI,EAAO7vB,KAAK,WAAa,IAAItF,KAAK+0B,GAASK,eAGzCtwB,EAAM7I,SAAS+4B,IACjBG,EAAO7vB,KAAK,QAAU0vB,GAGpBlwB,EAAM7I,SAASg5B,IACjBE,EAAO7vB,KAAK,UAAY2vB,IAGX,IAAXC,GACFC,EAAO7vB,KAAK,UAGdxI,SAASq4B,OAASA,EAAO5vB,KAAK,OAGhC+D,KAAM,SAAcpQ,GAClB,IAAImK,EAAQvG,SAASq4B,OAAO9xB,MAAM,IAAI1B,OAAO,aAAezI,EAAO,cACnE,OAAQmK,EAAQnD,mBAAmBmD,EAAM,IAAM,MAGjD0R,OAAQ,SAAgB7b,GACtBqS,KAAKupB,MAAM57B,EAAM,GAAI8G,KAAKq1B,MAAQ,SAO/B,CACLP,MAAO,aACPxrB,KAAM,WAAkB,OAAO,MAC/ByL,OAAQ,e,6BC/ChB,IAAIugB,EAAgB36B,EAAQ,IACxB46B,EAAc56B,EAAQ,IAW1BjC,EAAOD,QAAU,SAAuB2P,EAASotB,GAC/C,OAAIptB,IAAYktB,EAAcE,GACrBD,EAAYntB,EAASotB,GAEvBA,I,6BCVT98B,EAAOD,QAAU,SAAuBuM,GAItC,MAAO,gCAAgCsK,KAAKtK,K,6BCH9CtM,EAAOD,QAAU,SAAqB2P,EAASqtB,GAC7C,OAAOA,EACHrtB,EAAQ1K,QAAQ,OAAQ,IAAM,IAAM+3B,EAAY/3B,QAAQ,OAAQ,IAChE0K,I,6BCVN,IAAItD,EAAQnK,EAAQ,GAIhB+6B,EAAoB,CACtB,MAAO,gBAAiB,iBAAkB,eAAgB,OAC1D,UAAW,OAAQ,OAAQ,oBAAqB,sBAChD,gBAAiB,WAAY,eAAgB,sBAC7C,UAAW,cAAe,cAgB5Bh9B,EAAOD,QAAU,SAAsBqN,GACrC,IACI5L,EACAY,EACAnC,EAHAg9B,EAAS,GAKb,OAAK7vB,GAELhB,EAAM1J,QAAQ0K,EAAQoQ,MAAM,OAAO,SAAgB0f,GAKjD,GAJAj9B,EAAIi9B,EAAKnwB,QAAQ,KACjBvL,EAAM4K,EAAMtH,KAAKo4B,EAAKC,OAAO,EAAGl9B,IAAI4Q,cACpCzO,EAAMgK,EAAMtH,KAAKo4B,EAAKC,OAAOl9B,EAAI,IAE7BuB,EAAK,CACP,GAAIy7B,EAAOz7B,IAAQw7B,EAAkBjwB,QAAQvL,IAAQ,EACnD,OAGAy7B,EAAOz7B,GADG,eAARA,GACay7B,EAAOz7B,GAAOy7B,EAAOz7B,GAAO,IAAIuI,OAAO,CAAC3H,IAEzC66B,EAAOz7B,GAAOy7B,EAAOz7B,GAAO,KAAOY,EAAMA,MAKtD66B,GAnBgBA,I,6BC9BzB,IAAI7wB,EAAQnK,EAAQ,GAEpBjC,EAAOD,QACLqM,EAAMpI,uBAIH,WACC,IAEIo5B,EAFAC,EAAO,kBAAkBzmB,KAAK3S,UAAUmwB,WACxCkJ,EAAiBl5B,SAAS8e,cAAc,KAS5C,SAASqa,EAAWjxB,GAClB,IAAIkxB,EAAOlxB,EAWX,OATI+wB,IAEFC,EAAexgB,aAAa,OAAQ0gB,GACpCA,EAAOF,EAAeE,MAGxBF,EAAexgB,aAAa,OAAQ0gB,GAG7B,CACLA,KAAMF,EAAeE,KACrBpe,SAAUke,EAAele,SAAWke,EAAele,SAASpa,QAAQ,KAAM,IAAM,GAChFy4B,KAAMH,EAAeG,KACrBC,OAAQJ,EAAeI,OAASJ,EAAeI,OAAO14B,QAAQ,MAAO,IAAM,GAC3E24B,KAAML,EAAeK,KAAOL,EAAeK,KAAK34B,QAAQ,KAAM,IAAM,GACpEqa,SAAUie,EAAeje,SACzBC,KAAMge,EAAehe,KACrBse,SAAiD,MAAtCN,EAAeM,SAASlpB,OAAO,GACxC4oB,EAAeM,SACf,IAAMN,EAAeM,UAY3B,OARAR,EAAYG,EAAWp5B,OAAO8a,SAASue,MAQhC,SAAyBK,GAC9B,IAAIZ,EAAU7wB,EAAM7I,SAASs6B,GAAeN,EAAWM,GAAcA,EACrE,OAAQZ,EAAO7d,WAAage,EAAUhe,UAClC6d,EAAOQ,OAASL,EAAUK,MAhDjC,GAsDQ,WACL,OAAO,I,6BC9Df,IAAI7qB,EAAS3Q,EAAQ,IAQrB,SAASi2B,EAAY4F,GACnB,GAAwB,mBAAbA,EACT,MAAM,IAAIl4B,UAAU,gCAGtB,IAAIm4B,EACJlrB,KAAKzB,QAAU,IAAIxI,SAAQ,SAAyBiG,GAClDkvB,EAAiBlvB,KAGnB,IAAImvB,EAAQnrB,KACZirB,GAAS,SAAgBpsB,GACnBssB,EAAMxE,SAKVwE,EAAMxE,OAAS,IAAI5mB,EAAOlB,GAC1BqsB,EAAeC,EAAMxE,YAOzBtB,EAAYr2B,UAAU03B,iBAAmB,WACvC,GAAI1mB,KAAK2mB,OACP,MAAM3mB,KAAK2mB,QAQftB,EAAY7lB,OAAS,WACnB,IAAIf,EAIJ,MAAO,CACL0sB,MAJU,IAAI9F,GAAY,SAAkB53B,GAC5CgR,EAAShR,KAITgR,OAAQA,IAIZtR,EAAOD,QAAUm4B,G,6BClCjBl4B,EAAOD,QAAU,SAAgBmb,GAC/B,OAAO,SAAc7E,GACnB,OAAO6E,EAASjR,MAAM,KAAMoM,M,kQCtBhC,IAAI4nB,EAAiBh8B,EAAQ,IACzBmK,EAAQnK,EAAQ,IAChBqD,EAAUrD,EAAQ,GAClB6Q,EAAMnS,OAAOkB,UAAUC,eAEvBo8B,EAAwB,CACxBC,SAAU,SAAkBC,GACxB,OAAOA,EAAS,MAEpBC,MAAO,QACPC,QAAS,SAAiBF,EAAQ58B,GAC9B,OAAO48B,EAAS,IAAM58B,EAAM,KAEhC21B,OAAQ,SAAgBiH,GACpB,OAAOA,IAIXj8B,EAAU6E,MAAM7E,QAChByK,EAAO5F,MAAMnF,UAAU+K,KACvB2xB,EAAc,SAAUloB,EAAKmoB,GAC7B5xB,EAAK3C,MAAMoM,EAAKlU,EAAQq8B,GAAgBA,EAAe,CAACA,KAGxDC,EAAQn3B,KAAKzF,UAAU8K,YAEvB+xB,EAAgBp5B,EAAO,QACvBgI,EAAW,CACXqxB,gBAAgB,EAChBC,WAAW,EACX1qB,QAAS,QACT2qB,iBAAiB,EACjBC,UAAW,IACXzyB,QAAQ,EACR0yB,QAAS3yB,EAAMC,OACf2yB,kBAAkB,EAClB1qB,OAAQoqB,EACRO,UAAW35B,EAAQ0G,WAAW0yB,GAE9BJ,SAAS,EACTY,cAAe,SAAuBC,GAClC,OAAOV,EAAMr+B,KAAK++B,IAEtBC,WAAW,EACXC,oBAAoB,GAWpBj6B,EAAY,SAASA,EACrBzD,EACAy8B,EACAkB,EACAD,EACAD,EACAL,EACApsB,EACA4sB,EACAX,EACAM,EACA5qB,EACA2qB,EACAD,EACA9qB,EACAsrB,GAEA,IAzBuD9yB,EAyBnD/J,EAAMhB,EAEV,GAAI69B,EAAY1sB,IAAInR,GAChB,MAAM,IAAImH,WAAW,uBAgBzB,GAbsB,mBAAX6J,EACPhQ,EAAMgQ,EAAOyrB,EAAQz7B,GACdA,aAAe2E,KACtB3E,EAAMu8B,EAAcv8B,GACW,UAAxB28B,GAAmCn9B,EAAQQ,KAClDA,EAAMyJ,EAAMwI,SAASjS,GAAK,SAAUzB,GAChC,OAAIA,aAAiBoG,KACV43B,EAAch+B,GAElBA,MAIH,OAARyB,EAAc,CACd,GAAI08B,EACA,OAAON,IAAYC,EAAmBD,EAAQX,EAAQ9wB,EAASyxB,QAAS7qB,EAAS,MAAOI,GAAU8pB,EAGtGz7B,EAAM,GAGV,GAnDoB,iBADmC+J,EAoD7B/J,IAlDN,iBAAN+J,GACM,kBAANA,GACM,WAAb,EAAOA,IACM,iBAANA,GA+CoBN,EAAMrJ,SAASJ,GAC7C,OAAIo8B,EAEO,CAACE,EADOD,EAAmBZ,EAASW,EAAQX,EAAQ9wB,EAASyxB,QAAS7qB,EAAS,MAAOI,IAC/D,IAAM2qB,EAAUF,EAAQp8B,EAAK2K,EAASyxB,QAAS7qB,EAAS,QAASI,KAE5F,CAAC2qB,EAAUb,GAAU,IAAMa,EAAU71B,OAAOzG,KAGvD,IAMI88B,EANAxf,EAAS,GAEb,QAAmB,IAARtd,EACP,OAAOsd,EAIX,GAA4B,UAAxBqf,GAAmCn9B,EAAQQ,GAE3C88B,EAAU,CAAC,CAAEv+B,MAAOyB,EAAIE,OAAS,EAAIF,EAAIkK,KAAK,MAAQ,UAAOhG,SAC1D,GAAI1E,EAAQwQ,GACf8sB,EAAU9sB,MACP,CACH,IAAID,EAAO/R,OAAO+R,KAAK/P,GACvB88B,EAAUF,EAAO7sB,EAAK6sB,KAAKA,GAAQ7sB,EAGvC,IAAK,IAAIzS,EAAI,EAAGA,EAAIw/B,EAAQ58B,SAAU5C,EAAG,CACrC,IAAIuB,EAAMi+B,EAAQx/B,GACdiB,EAAuB,WAAf,EAAOM,SAAkCqF,IAAdrF,EAAIN,MAAsBM,EAAIN,MAAQyB,EAAInB,GAEjF,IAAI49B,GAAuB,OAAVl+B,EAAjB,CAIA,IAAIw+B,EAAYv9B,EAAQQ,GACa,mBAAxB28B,EAAqCA,EAAoBlB,EAAQ58B,GAAO48B,EAC/EA,GAAUQ,EAAY,IAAMp9B,EAAM,IAAMA,EAAM,KAEpDg+B,EAAYpiB,IAAIzb,GAAQ,GACxB,IAAIg+B,EAAmB1B,IACvBM,EAAYte,EAAQ7a,EAChBlE,EACAw+B,EACAJ,EACAD,EACAD,EACAL,EACApsB,EACA4sB,EACAX,EACAM,EACA5qB,EACA2qB,EACAD,EACA9qB,EACAyrB,KAIR,OAAO1f,GAkDXjgB,EAAOD,QAAU,SAAU4B,EAAQi+B,GAC/B,IAGIH,EAHA98B,EAAMhB,EACNuR,EAjDwB,SAAmC0sB,GAC/D,IAAKA,EACD,OAAOtyB,EAGX,GAAqB,OAAjBsyB,EAAKb,cAAqCl4B,IAAjB+4B,EAAKb,SAAiD,mBAAjBa,EAAKb,QACnE,MAAM,IAAIn5B,UAAU,iCAGxB,IAAIsO,EAAU0rB,EAAK1rB,SAAW5G,EAAS4G,QACvC,QAA4B,IAAjB0rB,EAAK1rB,SAA4C,UAAjB0rB,EAAK1rB,SAAwC,eAAjB0rB,EAAK1rB,QACxE,MAAM,IAAItO,UAAU,qEAGxB,IAAI0O,EAAShP,EAAO,QACpB,QAA2B,IAAhBs6B,EAAKtrB,OAAwB,CACpC,IAAKxB,EAAI1S,KAAKkF,EAAQ0G,WAAY4zB,EAAKtrB,QACnC,MAAM,IAAI1O,UAAU,mCAExB0O,EAASsrB,EAAKtrB,OAElB,IAAI2qB,EAAY35B,EAAQ0G,WAAWsI,GAE/B3B,EAASrF,EAASqF,OAKtB,OAJ2B,mBAAhBitB,EAAKjtB,QAAyBxQ,EAAQy9B,EAAKjtB,WAClDA,EAASitB,EAAKjtB,QAGX,CACHgsB,eAA+C,kBAAxBiB,EAAKjB,eAA+BiB,EAAKjB,eAAiBrxB,EAASqxB,eAC1FC,eAAqC,IAAnBgB,EAAKhB,UAA4BtxB,EAASsxB,YAAcgB,EAAKhB,UAC/E1qB,QAASA,EACT2qB,gBAAiD,kBAAzBe,EAAKf,gBAAgCe,EAAKf,gBAAkBvxB,EAASuxB,gBAC7FC,eAAqC,IAAnBc,EAAKd,UAA4BxxB,EAASwxB,UAAYc,EAAKd,UAC7EzyB,OAA+B,kBAAhBuzB,EAAKvzB,OAAuBuzB,EAAKvzB,OAASiB,EAASjB,OAClE0yB,QAAiC,mBAAjBa,EAAKb,QAAyBa,EAAKb,QAAUzxB,EAASyxB,QACtEC,iBAAmD,kBAA1BY,EAAKZ,iBAAiCY,EAAKZ,iBAAmB1xB,EAAS0xB,iBAChGrsB,OAAQA,EACR2B,OAAQA,EACR2qB,UAAWA,EACXC,cAA6C,mBAAvBU,EAAKV,cAA+BU,EAAKV,cAAgB5xB,EAAS4xB,cACxFE,UAAqC,kBAAnBQ,EAAKR,UAA0BQ,EAAKR,UAAY9xB,EAAS8xB,UAC3EG,KAA2B,mBAAdK,EAAKL,KAAsBK,EAAKL,KAAO,KACpDF,mBAAuD,kBAA5BO,EAAKP,mBAAmCO,EAAKP,mBAAqB/xB,EAAS+xB,oBAM5FQ,CAA0BD,GAKV,mBAAnB1sB,EAAQP,OAEfhQ,GADAgQ,EAASO,EAAQP,QACJ,GAAIhQ,GACVR,EAAQ+Q,EAAQP,UAEvB8sB,EADSvsB,EAAQP,QAIrB,IAMImtB,EANAptB,EAAO,GAEX,GAAmB,WAAf,EAAO/P,IAA4B,OAARA,EAC3B,MAAO,GAKPm9B,EADAF,GAAQA,EAAKE,eAAe5B,EACd0B,EAAKE,YACZF,GAAQ,YAAaA,EACdA,EAAKtB,QAAU,UAAY,SAE3B,UAGlB,IAAIgB,EAAsBpB,EAAsB4B,GAE3CL,IACDA,EAAU9+B,OAAO+R,KAAK/P,IAGtBuQ,EAAQqsB,MACRE,EAAQF,KAAKrsB,EAAQqsB,MAIzB,IADA,IAAIC,EAAcvB,IACTh+B,EAAI,EAAGA,EAAIw/B,EAAQ58B,SAAU5C,EAAG,CACrC,IAAIuB,EAAMi+B,EAAQx/B,GAEdiT,EAAQksB,WAA0B,OAAbz8B,EAAInB,IAG7B+8B,EAAY7rB,EAAMtN,EACdzC,EAAInB,GACJA,EACA89B,EACApsB,EAAQmsB,mBACRnsB,EAAQksB,UACRlsB,EAAQ7G,OAAS6G,EAAQ6rB,QAAU,KACnC7rB,EAAQP,OACRO,EAAQqsB,KACRrsB,EAAQ0rB,UACR1rB,EAAQgsB,cACRhsB,EAAQoB,OACRpB,EAAQ+rB,UACR/rB,EAAQ8rB,iBACR9rB,EAAQgB,QACRsrB,IAIR,IAAIO,EAASrtB,EAAK7F,KAAKqG,EAAQ4rB,WAC3BV,GAAoC,IAA3BlrB,EAAQyrB,eAA0B,IAAM,GAYrD,OAVIzrB,EAAQ2rB,kBACgB,eAApB3rB,EAAQgB,QAERkqB,GAAU,uBAGVA,GAAU,mBAIX2B,EAAOl9B,OAAS,EAAIu7B,EAAS2B,EAAS,K,kQC9RjD,IAAIC,EAAe/9B,EAAQ,GACvBg+B,EAAYh+B,EAAQ,IACpBi+B,EAAUj+B,EAAQ,IAElB0D,EAAaq6B,EAAa,eAC1BG,EAAWH,EAAa,aAAa,GACrCI,EAAOJ,EAAa,SAAS,GAE7BK,EAAcJ,EAAU,yBAAyB,GACjDK,EAAcL,EAAU,yBAAyB,GACjDM,EAAcN,EAAU,yBAAyB,GACjDO,EAAUP,EAAU,qBAAqB,GACzCQ,EAAUR,EAAU,qBAAqB,GACzCS,EAAUT,EAAU,qBAAqB,GAUzCU,EAAc,SAAUC,EAAMp/B,GACjC,IAAK,IAAiBq/B,EAAbC,EAAOF,EAAmC,QAAtBC,EAAOC,EAAKC,MAAgBD,EAAOD,EAC/D,GAAIA,EAAKr/B,MAAQA,EAIhB,OAHAs/B,EAAKC,KAAOF,EAAKE,KACjBF,EAAKE,KAAOH,EAAKG,KACjBH,EAAKG,KAAOF,EACLA,GA0BV7gC,EAAOD,QAAU,WAChB,IAAIihC,EACAC,EACAC,EACAC,EAAU,CACbC,OAAQ,SAAU5/B,GACjB,IAAK2/B,EAAQruB,IAAItR,GAChB,MAAM,IAAImE,EAAW,iCAAmCu6B,EAAQ1+B,KAGlEV,IAAK,SAAUU,GACd,GAAI2+B,GAAY3+B,IAAuB,WAAf,EAAOA,IAAmC,mBAARA,IACzD,GAAIw/B,EACH,OAAOX,EAAYW,EAAKx/B,QAEnB,GAAI4+B,GACV,GAAIa,EACH,OAAOT,EAAQS,EAAIz/B,QAGpB,GAAI0/B,EACH,OA1CS,SAAUG,EAAS7/B,GAChC,IAAI8/B,EAAOX,EAAYU,EAAS7/B,GAChC,OAAO8/B,GAAQA,EAAKpgC,MAwCTqgC,CAAQL,EAAI1/B,IAItBsR,IAAK,SAAUtR,GACd,GAAI2+B,GAAY3+B,IAAuB,WAAf,EAAOA,IAAmC,mBAARA,IACzD,GAAIw/B,EACH,OAAOT,EAAYS,EAAKx/B,QAEnB,GAAI4+B,GACV,GAAIa,EACH,OAAOP,EAAQO,EAAIz/B,QAGpB,GAAI0/B,EACH,OAxCS,SAAUG,EAAS7/B,GAChC,QAASm/B,EAAYU,EAAS7/B,GAuCnBggC,CAAQN,EAAI1/B,GAGrB,OAAO,GAER4b,IAAK,SAAU5b,EAAKN,GACfi/B,GAAY3+B,IAAuB,WAAf,EAAOA,IAAmC,mBAARA,IACpDw/B,IACJA,EAAM,IAAIb,GAEXG,EAAYU,EAAKx/B,EAAKN,IACZk/B,GACLa,IACJA,EAAK,IAAIb,GAEVK,EAAQQ,EAAIz/B,EAAKN,KAEZggC,IAMJA,EAAK,CAAE1/B,IAAK,GAAIu/B,KAAM,OA5Eb,SAAUM,EAAS7/B,EAAKN,GACrC,IAAIogC,EAAOX,EAAYU,EAAS7/B,GAC5B8/B,EACHA,EAAKpgC,MAAQA,EAGbmgC,EAAQN,KAAO,CACdv/B,IAAKA,EACLu/B,KAAMM,EAAQN,KACd7/B,MAAOA,GAqENugC,CAAQP,EAAI1/B,EAAKN,MAIpB,OAAOigC,I,kQCxHR,IAAIO,EAA+B,oBAAX1gC,QAA0BA,OAC9C2gC,EAAgB1/B,EAAQ,IAE5BjC,EAAOD,QAAU,WAChB,MAA0B,mBAAf2hC,IACW,mBAAX1gC,SACsB,WAA7B,EAAO0gC,EAAW,UACO,WAAzB,EAAO1gC,OAAO,SAEX2gC,S,kQCRR3hC,EAAOD,QAAU,WAChB,GAAsB,mBAAXiB,QAAiE,mBAAjCL,OAAOyU,sBAAwC,OAAO,EACjG,GAA+B,WAA3B,EAAOpU,OAAOiG,UAAyB,OAAO,EAElD,IAAItE,EAAM,GACN2S,EAAMtU,OAAO,QACb4gC,EAASjhC,OAAO2U,GACpB,GAAmB,iBAARA,EAAoB,OAAO,EAEtC,GAA4C,oBAAxC3U,OAAOkB,UAAUK,SAAS9B,KAAKkV,GAA8B,OAAO,EACxE,GAA+C,oBAA3C3U,OAAOkB,UAAUK,SAAS9B,KAAKwhC,GAAiC,OAAO,EAY3E,IAAKtsB,KADL3S,EAAI2S,GADS,GAED3S,EAAO,OAAO,EAC1B,GAA2B,mBAAhBhC,OAAO+R,MAAmD,IAA5B/R,OAAO+R,KAAK/P,GAAKE,OAAgB,OAAO,EAEjF,GAA0C,mBAA/BlC,OAAOkhC,qBAAiF,IAA3ClhC,OAAOkhC,oBAAoBl/B,GAAKE,OAAgB,OAAO,EAE/G,IAAIi/B,EAAOnhC,OAAOyU,sBAAsBzS,GACxC,GAAoB,IAAhBm/B,EAAKj/B,QAAgBi/B,EAAK,KAAOxsB,EAAO,OAAO,EAEnD,IAAK3U,OAAOkB,UAAUkgC,qBAAqB3hC,KAAKuC,EAAK2S,GAAQ,OAAO,EAEpE,GAA+C,mBAApC3U,OAAOsF,yBAAyC,CAC1D,IAAIgQ,EAAatV,OAAOsF,yBAAyBtD,EAAK2S,GACtD,GAdY,KAcRW,EAAW/U,QAA8C,IAA1B+U,EAAWpV,WAAuB,OAAO,EAG7E,OAAO,I,6BCpCR,IAAImhC,EAAgB,kDAChBx9B,EAAQwC,MAAMnF,UAAU2C,MACxBy9B,EAAQthC,OAAOkB,UAAUK,SAG7BlC,EAAOD,QAAU,SAAcmiC,GAC3B,IAAI9vB,EAASS,KACb,GAAsB,mBAAXT,GAJA,sBAIyB6vB,EAAM7hC,KAAKgS,GAC3C,MAAM,IAAIxM,UAAUo8B,EAAgB5vB,GAyBxC,IAvBA,IAEI+vB,EAFAh2B,EAAO3H,EAAMpE,KAAKqE,UAAW,GAG7B29B,EAAS,WACT,GAAIvvB,gBAAgBsvB,EAAO,CACvB,IAAI79B,EAAS8N,EAAOnI,MAChB4I,KACA1G,EAAKpC,OAAOvF,EAAMpE,KAAKqE,aAE3B,OAAI9D,OAAO2D,KAAYA,EACZA,EAEJuO,KAEP,OAAOT,EAAOnI,MACVi4B,EACA/1B,EAAKpC,OAAOvF,EAAMpE,KAAKqE,cAK/B49B,EAAc75B,KAAKgb,IAAI,EAAGpR,EAAOvP,OAASsJ,EAAKtJ,QAC/Cy/B,EAAY,GACPriC,EAAI,EAAGA,EAAIoiC,EAAapiC,IAC7BqiC,EAAU11B,KAAK,IAAM3M,GAKzB,GAFAkiC,EAAQz8B,SAAS,SAAU,oBAAsB48B,EAAUz1B,KAAK,KAAO,4CAA/DnH,CAA4G08B,GAEhHhwB,EAAOvQ,UAAW,CAClB,IAAI0gC,EAAQ,aACZA,EAAM1gC,UAAYuQ,EAAOvQ,UACzBsgC,EAAMtgC,UAAY,IAAI0gC,EACtBA,EAAM1gC,UAAY,KAGtB,OAAOsgC,I,6BChDX,IAAI1gC,EAAOQ,EAAQ,GAEnBjC,EAAOD,QAAU0B,EAAKrB,KAAKsF,SAAStF,KAAMO,OAAOkB,UAAUC,iB,6BCF3D,IAAIk+B,EAAe/9B,EAAQ,GAEvBugC,EAAWvgC,EAAQ,IAEnBwgC,EAAWD,EAASxC,EAAa,6BAErChgC,EAAOD,QAAU,SAA4BS,EAAMwK,GAClD,IAAIO,EAAYy0B,EAAax/B,IAAQwK,GACrC,MAAyB,mBAAdO,GAA4Bk3B,EAASjiC,EAAM,gBAAkB,EAChEgiC,EAASj3B,GAEVA,I,6BCXR,IAAI9J,EAAOQ,EAAQ,GACf+9B,EAAe/9B,EAAQ,GAEvBygC,EAAS1C,EAAa,8BACtB2C,EAAQ3C,EAAa,6BACrB4C,EAAgB5C,EAAa,mBAAmB,IAASv+B,EAAKrB,KAAKuiC,EAAOD,GAE1E18B,EAAQg6B,EAAa,qCAAqC,GAC1D6C,EAAkB7C,EAAa,2BAA2B,GAC1D8C,EAAO9C,EAAa,cAExB,GAAI6C,EACH,IACCA,EAAgB,GAAI,IAAK,CAAE3hC,MAAO,IACjC,MAAO6E,GAER88B,EAAkB,KAIpB7iC,EAAOD,QAAU,SAAkBgjC,GAClC,IAAIC,EAAOJ,EAAcnhC,EAAMkhC,EAAOl+B,WACtC,GAAIuB,GAAS68B,EAAiB,CAC7B,IAAIj3B,EAAO5F,EAAMg9B,EAAM,UACnBp3B,EAAKsK,cAER2sB,EACCG,EACA,SACA,CAAE9hC,MAAO,EAAI4hC,EAAK,EAAGC,EAAiBlgC,QAAU4B,UAAU5B,OAAS,MAItE,OAAOmgC,GAGR,IAAIC,EAAY,WACf,OAAOL,EAAcnhC,EAAMihC,EAAQj+B,YAGhCo+B,EACHA,EAAgB7iC,EAAOD,QAAS,QAAS,CAAEmB,MAAO+hC,IAElDjjC,EAAOD,QAAQkK,MAAQg5B,G,qPC7CxB,IAAIC,EAAwB,mBAAR36B,KAAsBA,IAAI1G,UAC1CshC,EAAoBxiC,OAAOsF,0BAA4Bi9B,EAASviC,OAAOsF,yBAAyBsC,IAAI1G,UAAW,QAAU,KACzHuhC,EAAUF,GAAUC,GAAsD,mBAA1BA,EAAkBriC,IAAqBqiC,EAAkBriC,IAAM,KAC/GuiC,EAAaH,GAAU36B,IAAI1G,UAAUa,QACrC4gC,EAAwB,mBAARp6B,KAAsBA,IAAIrH,UAC1C0hC,EAAoB5iC,OAAOsF,0BAA4Bq9B,EAAS3iC,OAAOsF,yBAAyBiD,IAAIrH,UAAW,QAAU,KACzH2hC,EAAUF,GAAUC,GAAsD,mBAA1BA,EAAkBziC,IAAqByiC,EAAkBziC,IAAM,KAC/G2iC,EAAaH,GAAUp6B,IAAIrH,UAAUa,QAErCghC,EADgC,mBAAZj6B,SAA0BA,QAAQ5H,UAC5B4H,QAAQ5H,UAAUiR,IAAM,KAElD6wB,EADgC,mBAAZh6B,SAA0BA,QAAQ9H,UAC5B8H,QAAQ9H,UAAUiR,IAAM,KAElD8wB,EADgC,mBAAZl6B,SAA0BA,QAAQ7H,UAC1B6H,QAAQ7H,UAAUgiC,MAAQ,KACtDC,EAAiB18B,QAAQvF,UAAUo1B,QACnC8M,EAAiBpjC,OAAOkB,UAAUK,SAClC8hC,EAAmBt+B,SAAS7D,UAAUK,SACtCyI,EAAQvB,OAAOvH,UAAU8I,MACzBs5B,EAAkC,mBAAX98B,OAAwBA,OAAOtF,UAAUo1B,QAAU,KAC1EiN,EAAOvjC,OAAOyU,sBACd+uB,EAAgC,mBAAXnjC,QAAoD,WAA3B,EAAOA,OAAOiG,UAAwBjG,OAAOa,UAAUK,SAAW,KAChHkiC,EAAsC,mBAAXpjC,QAAoD,WAA3B,EAAOA,OAAOiG,UAClEo9B,EAAe1jC,OAAOkB,UAAUkgC,qBAEhCuC,GAA0B,mBAAZt7B,QAAyBA,QAAQxG,eAAiB7B,OAAO6B,kBACvE,GAAGiE,YAAcO,MAAMnF,UACjB,SAAU0iC,GACR,OAAOA,EAAE99B,WAEX,MAGN+9B,EAAgBviC,EAAQ,IAAkBwiC,OAC1CC,EAAgBF,GAAiBG,EAASH,GAAiBA,EAAgB,KAC3EvjC,EAAgC,mBAAXD,aAAuD,IAAvBA,OAAOC,YAA8BD,OAAOC,YAAc,KA+KnH,SAAS2jC,EAAW5iC,EAAG6iC,EAAcjF,GACjC,IAAIkF,EAAkD,YAArClF,EAAKmF,YAAcF,GAA6B,IAAM,IACvE,OAAOC,EAAY9iC,EAAI8iC,EAG3B,SAASj6B,EAAM7I,GACX,OAAOoH,OAAOpH,GAAGgD,QAAQ,KAAM,UAGnC,SAAS7C,EAAQQ,GAAO,QAAsB,mBAAfs/B,EAAMt/B,IAA+B1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GAS/H,SAASgiC,EAAShiC,GACd,GAAIyhC,EACA,OAAOzhC,GAAsB,WAAf,EAAOA,IAAoBA,aAAe3B,OAE5D,GAAmB,WAAf,EAAO2B,GACP,OAAO,EAEX,IAAKA,GAAsB,WAAf,EAAOA,KAAqBwhC,EACpC,OAAO,EAEX,IAEI,OADAA,EAAY/jC,KAAKuC,IACV,EACT,MAAOoD,IACT,OAAO,EA7MX/F,EAAOD,QAAU,SAASilC,EAASriC,EAAKuQ,EAAS+xB,EAAOC,GACpD,IAAItF,EAAO1sB,GAAW,GAEtB,GAAIJ,EAAI8sB,EAAM,eAAsC,WAApBA,EAAKmF,YAA+C,WAApBnF,EAAKmF,WACjE,MAAM,IAAIn/B,UAAU,oDAExB,GACIkN,EAAI8sB,EAAM,qBAAuD,iBAAzBA,EAAKuF,gBACvCvF,EAAKuF,gBAAkB,GAAKvF,EAAKuF,kBAAoBpqB,IAC5B,OAAzB6kB,EAAKuF,iBAGX,MAAM,IAAIv/B,UAAU,0FAExB,IAAIw/B,GAAgBtyB,EAAI8sB,EAAM,kBAAmBA,EAAKwF,cACtD,GAA6B,kBAAlBA,GAAiD,WAAlBA,EACtC,MAAM,IAAIx/B,UAAU,iFAGxB,GACIkN,EAAI8sB,EAAM,WACS,OAAhBA,EAAKyF,QACW,OAAhBzF,EAAKyF,UACH18B,SAASi3B,EAAKyF,OAAQ,MAAQzF,EAAKyF,QAAUzF,EAAKyF,OAAS,GAEhE,MAAM,IAAIz/B,UAAU,6DAGxB,QAAmB,IAARjD,EACP,MAAO,YAEX,GAAY,OAARA,EACA,MAAO,OAEX,GAAmB,kBAARA,EACP,OAAOA,EAAM,OAAS,QAG1B,GAAmB,iBAARA,EACP,OA+RR,SAAS2iC,EAAcvgC,EAAK66B,GACxB,GAAI76B,EAAIlC,OAAS+8B,EAAKuF,gBAAiB,CACnC,IAAII,EAAYxgC,EAAIlC,OAAS+8B,EAAKuF,gBAC9BK,EAAU,OAASD,EAAY,mBAAqBA,EAAY,EAAI,IAAM,IAC9E,OAAOD,EAAcvgC,EAAIP,MAAM,EAAGo7B,EAAKuF,iBAAkBvF,GAAQ4F,EAIrE,OAAOZ,EADC7/B,EAAIC,QAAQ,WAAY,QAAQA,QAAQ,eAAgBygC,GAC3C,SAAU7F,GAvSpB0F,CAAc3iC,EAAKi9B,GAE9B,GAAmB,iBAARj9B,EACP,OAAY,IAARA,EACOoY,IAAWpY,EAAM,EAAI,IAAM,KAE/ByG,OAAOzG,GAElB,GAAmB,iBAARA,EACP,OAAOyG,OAAOzG,GAAO,IAGzB,IAAI+iC,OAAiC,IAAf9F,EAAKqF,MAAwB,EAAIrF,EAAKqF,MAE5D,QADqB,IAAVA,IAAyBA,EAAQ,GACxCA,GAASS,GAAYA,EAAW,GAAoB,WAAf,EAAO/iC,GAC5C,OAAOR,EAAQQ,GAAO,UAAY,WAGtC,IAAI0iC,EA2TR,SAAmBzF,EAAMqF,GACrB,IAAIU,EACJ,GAAoB,OAAhB/F,EAAKyF,OACLM,EAAa,SACV,MAA2B,iBAAhB/F,EAAKyF,QAAuBzF,EAAKyF,OAAS,GAGxD,OAAO,KAFPM,EAAa3+B,MAAM44B,EAAKyF,OAAS,GAAGx4B,KAAK,KAI7C,MAAO,CACH+4B,KAAMD,EACN7E,KAAM95B,MAAMi+B,EAAQ,GAAGp4B,KAAK84B,IAtUnBE,CAAUjG,EAAMqF,GAE7B,QAAoB,IAATC,EACPA,EAAO,QACJ,GAAIn4B,EAAQm4B,EAAMviC,IAAQ,EAC7B,MAAO,aAGX,SAASu9B,EAAQh/B,EAAOuV,EAAMqvB,GAK1B,GAJIrvB,IACAyuB,EAAOA,EAAK1gC,SACPoI,KAAK6J,GAEVqvB,EAAU,CACV,IAAIC,EAAU,CACVd,MAAOrF,EAAKqF,OAKhB,OAHInyB,EAAI8sB,EAAM,gBACVmG,EAAQhB,WAAanF,EAAKmF,YAEvBC,EAAS9jC,EAAO6kC,EAASd,EAAQ,EAAGC,GAE/C,OAAOF,EAAS9jC,EAAO0+B,EAAMqF,EAAQ,EAAGC,GAG5C,GAAmB,mBAARviC,EAAoB,CAC3B,IAAInC,EAiJZ,SAAgBwlC,GACZ,GAAIA,EAAExlC,KAAQ,OAAOwlC,EAAExlC,KACvB,IAAIH,EAAIsK,EAAMvK,KAAK4jC,EAAiB5jC,KAAK4lC,GAAI,wBAC7C,GAAI3lC,EAAK,OAAOA,EAAE,GAClB,OAAO,KArJQ4lC,CAAOtjC,GACd+P,EAAOwzB,EAAWvjC,EAAKu9B,GAC3B,MAAO,aAAe1/B,EAAO,KAAOA,EAAO,gBAAkB,KAAOkS,EAAK7P,OAAS,EAAI,MAAQ6P,EAAK7F,KAAK,MAAQ,KAAO,IAE3H,GAAI83B,EAAShiC,GAAM,CACf,IAAIwjC,EAAY/B,EAAoBh7B,OAAOzG,GAAKqC,QAAQ,yBAA0B,MAAQm/B,EAAY/jC,KAAKuC,GAC3G,MAAsB,WAAf,EAAOA,IAAqByhC,EAA2C+B,EAAvBC,EAAUD,GAErE,GAmOJ,SAAmB3/B,GACf,IAAKA,GAAkB,WAAb,EAAOA,GAAkB,OAAO,EAC1C,GAA2B,oBAAhB6/B,aAA+B7/B,aAAa6/B,YACnD,OAAO,EAEX,MAA6B,iBAAf7/B,EAAE8/B,UAAmD,mBAAnB9/B,EAAEoW,aAxO9C2pB,CAAU5jC,GAAM,CAGhB,IAFA,IAAIX,EAAI,IAAMoH,OAAOzG,EAAI2jC,UAAUz1B,cAC/B21B,EAAQ7jC,EAAI8jC,YAAc,GACrBxmC,EAAI,EAAGA,EAAIumC,EAAM3jC,OAAQ5C,IAC9B+B,GAAK,IAAMwkC,EAAMvmC,GAAGO,KAAO,IAAMokC,EAAW/5B,EAAM27B,EAAMvmC,GAAGiB,OAAQ,SAAU0+B,GAKjF,OAHA59B,GAAK,IACDW,EAAI+jC,YAAc/jC,EAAI+jC,WAAW7jC,SAAUb,GAAK,OACpDA,GAAK,KAAOoH,OAAOzG,EAAI2jC,UAAUz1B,cAAgB,IAGrD,GAAI1O,EAAQQ,GAAM,CACd,GAAmB,IAAfA,EAAIE,OAAgB,MAAO,KAC/B,IAAI8jC,EAAKT,EAAWvjC,EAAKu9B,GACzB,OAAImF,IAkQZ,SAA0BsB,GACtB,IAAK,IAAI1mC,EAAI,EAAGA,EAAI0mC,EAAG9jC,OAAQ5C,IAC3B,GAAI8M,EAAQ45B,EAAG1mC,GAAI,OAAS,EACxB,OAAO,EAGf,OAAO,EAxQY2mC,CAAiBD,GACrB,IAAME,EAAaF,EAAItB,GAAU,IAErC,KAAOsB,EAAG95B,KAAK,MAAQ,KAElC,GA2EJ,SAAiBlK,GAAO,QAAsB,mBAAfs/B,EAAMt/B,IAA+B1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GA3EvHmkC,CAAQnkC,GAAM,CACd,IAAI0I,EAAQ66B,EAAWvjC,EAAKu9B,GAC5B,OAAqB,IAAjB70B,EAAMxI,OAAuB,IAAMuG,OAAOzG,GAAO,IAC9C,MAAQyG,OAAOzG,GAAO,KAAO0I,EAAMwB,KAAK,MAAQ,KAE3D,GAAmB,WAAf,EAAOlK,IAAoByiC,EAAe,CAC1C,GAAIV,GAA+C,mBAAvB/hC,EAAI+hC,GAC5B,OAAO/hC,EAAI+hC,KACR,GAAsB,WAAlBU,GAAqD,mBAAhBziC,EAAIu9B,QAChD,OAAOv9B,EAAIu9B,UAGnB,GAyHJ,SAAe15B,GACX,IAAK48B,IAAY58B,GAAkB,WAAb,EAAOA,GACzB,OAAO,EAEX,IACI48B,EAAQhjC,KAAKoG,GACb,IACIg9B,EAAQpjC,KAAKoG,GACf,MAAOxE,GACL,OAAO,EAEX,OAAOwE,aAAa+B,IACtB,MAAOxC,IACT,OAAO,EAtIHghC,CAAMpkC,GAAM,CACZ,IAAIqkC,EAAW,GAIf,OAHA3D,EAAWjjC,KAAKuC,GAAK,SAAUzB,EAAOM,GAClCwlC,EAASp6B,KAAKszB,EAAQ1+B,EAAKmB,GAAK,GAAQ,OAASu9B,EAAQh/B,EAAOyB,OAE7DskC,EAAa,MAAO7D,EAAQhjC,KAAKuC,GAAMqkC,EAAU3B,GAE5D,GA6JJ,SAAe7+B,GACX,IAAKg9B,IAAYh9B,GAAkB,WAAb,EAAOA,GACzB,OAAO,EAEX,IACIg9B,EAAQpjC,KAAKoG,GACb,IACI48B,EAAQhjC,KAAKoG,GACf,MAAOnG,GACL,OAAO,EAEX,OAAOmG,aAAa0C,IACtB,MAAOnD,IACT,OAAO,EA1KHmhC,CAAMvkC,GAAM,CACZ,IAAIwkC,EAAW,GAIf,OAHA1D,EAAWrjC,KAAKuC,GAAK,SAAUzB,GAC3BimC,EAASv6B,KAAKszB,EAAQh/B,EAAOyB,OAE1BskC,EAAa,MAAOzD,EAAQpjC,KAAKuC,GAAMwkC,EAAU9B,GAE5D,GA2HJ,SAAmB7+B,GACf,IAAKk9B,IAAel9B,GAAkB,WAAb,EAAOA,GAC5B,OAAO,EAEX,IACIk9B,EAAWtjC,KAAKoG,EAAGk9B,GACnB,IACIC,EAAWvjC,KAAKoG,EAAGm9B,GACrB,MAAO3hC,GACL,OAAO,EAEX,OAAOwE,aAAaiD,QACtB,MAAO1D,IACT,OAAO,EAxIHqhC,CAAUzkC,GACV,OAAO0kC,EAAiB,WAE5B,GAmKJ,SAAmB7gC,GACf,IAAKm9B,IAAen9B,GAAkB,WAAb,EAAOA,GAC5B,OAAO,EAEX,IACIm9B,EAAWvjC,KAAKoG,EAAGm9B,GACnB,IACID,EAAWtjC,KAAKoG,EAAGk9B,GACrB,MAAO1hC,GACL,OAAO,EAEX,OAAOwE,aAAamD,QACtB,MAAO5D,IACT,OAAO,EAhLHuhC,CAAU3kC,GACV,OAAO0kC,EAAiB,WAE5B,GAqIJ,SAAmB7gC,GACf,IAAKo9B,IAAiBp9B,GAAkB,WAAb,EAAOA,GAC9B,OAAO,EAEX,IAEI,OADAo9B,EAAaxjC,KAAKoG,IACX,EACT,MAAOT,IACT,OAAO,EA7IHwhC,CAAU5kC,GACV,OAAO0kC,EAAiB,WAE5B,GA0CJ,SAAkB1kC,GAAO,QAAsB,oBAAfs/B,EAAMt/B,IAAgC1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GA1CzHa,CAASb,GACT,OAAOyjC,EAAUlG,EAAQz3B,OAAO9F,KAEpC,GA4DJ,SAAkBA,GACd,IAAKA,GAAsB,WAAf,EAAOA,KAAqBshC,EACpC,OAAO,EAEX,IAEI,OADAA,EAAc7jC,KAAKuC,IACZ,EACT,MAAOoD,IACT,OAAO,EApEHyhC,CAAS7kC,GACT,OAAOyjC,EAAUlG,EAAQ+D,EAAc7jC,KAAKuC,KAEhD,GAqCJ,SAAmBA,GAAO,QAAsB,qBAAfs/B,EAAMt/B,IAAiC1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GArC3H8kC,CAAU9kC,GACV,OAAOyjC,EAAUtC,EAAe1jC,KAAKuC,IAEzC,GAgCJ,SAAkBA,GAAO,QAAsB,oBAAfs/B,EAAMt/B,IAAgC1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GAhCzHY,CAASZ,GACT,OAAOyjC,EAAUlG,EAAQ92B,OAAOzG,KAEpC,IA0BJ,SAAgBA,GAAO,QAAsB,kBAAfs/B,EAAMt/B,IAA8B1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GA1BpHc,CAAOd,KA2BhB,SAAkBA,GAAO,QAAsB,oBAAfs/B,EAAMt/B,IAAgC1B,GAAgC,WAAf,EAAO0B,IAAoB1B,KAAe0B,GA3BxGgS,CAAShS,GAAM,CAChC,IAAI+kC,EAAKxB,EAAWvjC,EAAKu9B,GACrB39B,EAAgB+hC,EAAMA,EAAI3hC,KAAShC,OAAOkB,UAAYc,aAAehC,QAAUgC,EAAIK,cAAgBrC,OACnGgnC,EAAWhlC,aAAehC,OAAS,GAAK,iBACxCinC,GAAarlC,GAAiBtB,GAAeN,OAAOgC,KAASA,GAAO1B,KAAe0B,EAAMs/B,EAAMt/B,GAAK6B,MAAM,GAAI,GAAKmjC,EAAW,SAAW,GAEzIE,IADiBtlC,GAA4C,mBAApBI,EAAIK,YAA6B,GAAKL,EAAIK,YAAYxC,KAAOmC,EAAIK,YAAYxC,KAAO,IAAM,KAC3GonC,GAAaD,EAAW,IAAM,GAAG59B,OAAO69B,GAAa,GAAID,GAAY,IAAI96B,KAAK,MAAQ,KAAO,IACzH,OAAkB,IAAd66B,EAAG7kC,OAAuBglC,GAAM,KAChCxC,EACOwC,GAAM,IAAMhB,EAAaa,EAAIrC,GAAU,IAE3CwC,GAAM,KAAOH,EAAG76B,KAAK,MAAQ,KAExC,OAAOzD,OAAOzG,IAiDlB,IAAIkH,EAASlJ,OAAOkB,UAAUC,gBAAkB,SAAUN,GAAO,OAAOA,KAAOqR,MAC/E,SAASC,EAAInQ,EAAKnB,GACd,OAAOqI,EAAOzJ,KAAKuC,EAAKnB,GAG5B,SAASygC,EAAMt/B,GACX,OAAOohC,EAAe3jC,KAAKuC,GAU/B,SAASoK,EAAQ45B,EAAIngC,GACjB,GAAImgC,EAAG55B,QAAW,OAAO45B,EAAG55B,QAAQvG,GACpC,IAAK,IAAIvG,EAAI,EAAGC,EAAIymC,EAAG9jC,OAAQ5C,EAAIC,EAAGD,IAClC,GAAI0mC,EAAG1mC,KAAOuG,EAAK,OAAOvG,EAE9B,OAAQ,EAiGZ,SAASwlC,EAAQnlC,GACb,IAAIoB,EAAIpB,EAAE6E,WAAW,GACjBqB,EAAI,CACJshC,EAAG,IACHC,EAAG,IACHC,GAAI,IACJC,GAAI,IACJC,GAAI,KACNxmC,GACF,OAAI8E,EAAY,KAAOA,EAChB,OAAS9E,EAAI,GAAO,IAAM,IAAMA,EAAEQ,SAAS,IAAI0N,cAG1D,SAASw2B,EAAUrhC,GACf,MAAO,UAAYA,EAAM,IAG7B,SAASsiC,EAAiBp4B,GACtB,OAAOA,EAAO,SAGlB,SAASg4B,EAAah4B,EAAMk5B,EAAMC,EAAS/C,GAEvC,OAAOp2B,EAAO,KAAOk5B,EAAO,OADR9C,EAASwB,EAAauB,EAAS/C,GAAU+C,EAAQv7B,KAAK,OACtB,IA2BxD,SAASg6B,EAAaF,EAAItB,GACtB,GAAkB,IAAdsB,EAAG9jC,OAAgB,MAAO,GAC9B,IAAIwlC,EAAa,KAAOhD,EAAOvE,KAAOuE,EAAOO,KAC7C,OAAOyC,EAAa1B,EAAG95B,KAAK,IAAMw7B,GAAc,KAAOhD,EAAOvE,KAGlE,SAASoF,EAAWvjC,EAAKu9B,GACrB,IAAIoI,EAAQnmC,EAAQQ,GAChBgkC,EAAK,GACT,GAAI2B,EAAO,CACP3B,EAAG9jC,OAASF,EAAIE,OAChB,IAAK,IAAI5C,EAAI,EAAGA,EAAI0C,EAAIE,OAAQ5C,IAC5B0mC,EAAG1mC,GAAK6S,EAAInQ,EAAK1C,GAAKigC,EAAQv9B,EAAI1C,GAAI0C,GAAO,GAGrD,IACI4lC,EADAzG,EAAuB,mBAAToC,EAAsBA,EAAKvhC,GAAO,GAEpD,GAAIyhC,EAAmB,CACnBmE,EAAS,GACT,IAAK,IAAIpS,EAAI,EAAGA,EAAI2L,EAAKj/B,OAAQszB,IAC7BoS,EAAO,IAAMzG,EAAK3L,IAAM2L,EAAK3L,GAIrC,IAAK,IAAI30B,KAAOmB,EACPmQ,EAAInQ,EAAKnB,KACV8mC,GAASl/B,OAAOX,OAAOjH,MAAUA,GAAOA,EAAMmB,EAAIE,QAClDuhC,GAAqBmE,EAAO,IAAM/mC,aAAgBR,SAG1C,SAAU4V,KAAKpV,GACvBmlC,EAAG/5B,KAAKszB,EAAQ1+B,EAAKmB,GAAO,KAAOu9B,EAAQv9B,EAAInB,GAAMmB,IAErDgkC,EAAG/5B,KAAKpL,EAAM,KAAO0+B,EAAQv9B,EAAInB,GAAMmB,MAG/C,GAAoB,mBAATuhC,EACP,IAAK,IAAItwB,EAAI,EAAGA,EAAIkuB,EAAKj/B,OAAQ+Q,IACzBywB,EAAajkC,KAAKuC,EAAKm/B,EAAKluB,KAC5B+yB,EAAG/5B,KAAK,IAAMszB,EAAQ4B,EAAKluB,IAAM,MAAQssB,EAAQv9B,EAAIm/B,EAAKluB,IAAKjR,IAI3E,OAAOgkC,I,6CChdX,IAAIv6B,EAAQnK,EAAQ,IAEhB6Q,EAAMnS,OAAOkB,UAAUC,eACvBK,EAAU6E,MAAM7E,QAEhBmL,EAAW,CACXsxB,WAAW,EACX9pB,iBAAiB,EACjB0zB,aAAa,EACbC,WAAY,GACZv0B,QAAS,QACT2qB,iBAAiB,EACjBR,OAAO,EACPpqB,QAAS7H,EAAM4H,OACf8qB,UAAW,IACXmG,MAAO,EACPyD,mBAAmB,EACnBC,0BAA0B,EAC1BC,eAAgB,IAChBC,aAAa,EACb11B,cAAc,EACdksB,oBAAoB,GAGpBsJ,EAA2B,SAAU5jC,GACrC,OAAOA,EAAIC,QAAQ,aAAa,SAAUwP,EAAIs0B,GAC1C,OAAO1/B,OAAOmb,aAAa5b,SAASmgC,EAAW,SAInDC,EAAkB,SAAU3mC,EAAK8Q,GACjC,OAAI9Q,GAAsB,iBAARA,GAAoB8Q,EAAQmrB,OAASj8B,EAAI2K,QAAQ,MAAQ,EAChE3K,EAAIob,MAAM,KAGdpb,GAgHP4mC,EAAY,SAA8BC,EAAU7mC,EAAK8Q,EAASg2B,GAClE,GAAKD,EAAL,CAKA,IAAIznC,EAAM0R,EAAQ0rB,UAAYqK,EAASjkC,QAAQ,cAAe,QAAUikC,EAKpEE,EAAQ,gBAIRC,EAAUl2B,EAAQ+xB,MAAQ,GALf,eAK6BzN,KAAKh2B,GAC7C6nC,EAASD,EAAU5nC,EAAIgD,MAAM,EAAG4kC,EAAQE,OAAS9nC,EAIjDkR,EAAO,GACX,GAAI22B,EAAQ,CAER,IAAKn2B,EAAQC,cAAgBL,EAAI1S,KAAKO,OAAOkB,UAAWwnC,KAC/Cn2B,EAAQ4B,gBACT,OAIRpC,EAAK9F,KAAKy8B,GAMd,IADA,IAAIppC,EAAI,EACDiT,EAAQ+xB,MAAQ,GAAqC,QAA/BmE,EAAUD,EAAM3R,KAAKh2B,KAAkBvB,EAAIiT,EAAQ+xB,OAAO,CAEnF,GADAhlC,GAAK,GACAiT,EAAQC,cAAgBL,EAAI1S,KAAKO,OAAOkB,UAAWunC,EAAQ,GAAG5kC,MAAM,GAAI,MACpE0O,EAAQ4B,gBACT,OAGRpC,EAAK9F,KAAKw8B,EAAQ,IAStB,OAJIA,GACA12B,EAAK9F,KAAK,IAAMpL,EAAIgD,MAAM4kC,EAAQE,OAAS,KAnFjC,SAAU5Q,EAAOt2B,EAAK8Q,EAASg2B,GAG7C,IAFA,IAAIK,EAAOL,EAAe9mC,EAAM2mC,EAAgB3mC,EAAK8Q,GAE5CjT,EAAIy4B,EAAM71B,OAAS,EAAG5C,GAAK,IAAKA,EAAG,CACxC,IAAI0C,EACA6mC,EAAO9Q,EAAMz4B,GAEjB,GAAa,OAATupC,GAAiBt2B,EAAQ21B,YACzBlmC,EAAM,GAAGoH,OAAOw/B,OACb,CACH5mC,EAAMuQ,EAAQC,aAAexS,OAAOY,OAAO,MAAQ,GACnD,IAAIkoC,EAA+B,MAAnBD,EAAK90B,OAAO,IAA+C,MAAjC80B,EAAK90B,OAAO80B,EAAK3mC,OAAS,GAAa2mC,EAAKhlC,MAAM,GAAI,GAAKglC,EACjGF,EAAQ3gC,SAAS8gC,EAAW,IAC3Bv2B,EAAQ21B,aAA6B,KAAdY,GAGvBphC,MAAMihC,IACJE,IAASC,GACTrgC,OAAOkgC,KAAWG,GAClBH,GAAS,GACRp2B,EAAQ21B,aAAeS,GAASp2B,EAAQu1B,YAE5C9lC,EAAM,IACF2mC,GAASC,EAEb5mC,EAAI8mC,GAAaF,EAXjB5mC,EAAM,CAAE+mC,EAAGH,GAenBA,EAAO5mC,EAGX,OAAO4mC,EAsDAI,CAAYj3B,EAAMtQ,EAAK8Q,EAASg2B,KAsC3ClpC,EAAOD,QAAU,SAAUgF,EAAK66B,GAC5B,IAAI1sB,EApCoB,SAA+B0sB,GACvD,IAAKA,EACD,OAAOtyB,EAGX,GAAqB,OAAjBsyB,EAAK3rB,cAAqCpN,IAAjB+4B,EAAK3rB,SAAiD,mBAAjB2rB,EAAK3rB,QACnE,MAAM,IAAIrO,UAAU,iCAGxB,QAA4B,IAAjBg6B,EAAK1rB,SAA4C,UAAjB0rB,EAAK1rB,SAAwC,eAAjB0rB,EAAK1rB,QACxE,MAAM,IAAItO,UAAU,qEAExB,IAAIsO,OAAkC,IAAjB0rB,EAAK1rB,QAA0B5G,EAAS4G,QAAU0rB,EAAK1rB,QAE5E,MAAO,CACH0qB,eAAqC,IAAnBgB,EAAKhB,UAA4BtxB,EAASsxB,YAAcgB,EAAKhB,UAC/E9pB,gBAAiD,kBAAzB8qB,EAAK9qB,gBAAgC8qB,EAAK9qB,gBAAkBxH,EAASwH,gBAC7F0zB,YAAyC,kBAArB5I,EAAK4I,YAA4B5I,EAAK4I,YAAcl7B,EAASk7B,YACjFC,WAAuC,iBAApB7I,EAAK6I,WAA0B7I,EAAK6I,WAAan7B,EAASm7B,WAC7Ev0B,QAASA,EACT2qB,gBAAiD,kBAAzBe,EAAKf,gBAAgCe,EAAKf,gBAAkBvxB,EAASuxB,gBAC7FR,MAA6B,kBAAfuB,EAAKvB,MAAsBuB,EAAKvB,MAAQ/wB,EAAS+wB,MAC/DpqB,QAAiC,mBAAjB2rB,EAAK3rB,QAAyB2rB,EAAK3rB,QAAU3G,EAAS2G,QACtE6qB,UAAqC,iBAAnBc,EAAKd,WAA0B1yB,EAAMuI,SAASirB,EAAKd,WAAac,EAAKd,UAAYxxB,EAASwxB,UAE5GmG,MAA8B,iBAAfrF,EAAKqF,QAAqC,IAAfrF,EAAKqF,OAAoBrF,EAAKqF,MAAQ33B,EAAS23B,MACzFyD,mBAA8C,IAA3B9I,EAAK8I,kBACxBC,yBAAmE,kBAAlC/I,EAAK+I,yBAAyC/I,EAAK+I,yBAA2Br7B,EAASq7B,yBACxHC,eAA+C,iBAAxBhJ,EAAKgJ,eAA8BhJ,EAAKgJ,eAAiBt7B,EAASs7B,eACzFC,aAAkC,IAArBjJ,EAAKiJ,YAClB11B,aAA2C,kBAAtBysB,EAAKzsB,aAA6BysB,EAAKzsB,aAAe7F,EAAS6F,aACpFksB,mBAAuD,kBAA5BO,EAAKP,mBAAmCO,EAAKP,mBAAqB/xB,EAAS+xB,oBAK5FuK,CAAsBhK,GAEpC,GAAY,KAAR76B,SAAcA,EACd,OAAOmO,EAAQC,aAAexS,OAAOY,OAAO,MAAQ,GASxD,IANA,IAAIsoC,EAAyB,iBAAR9kC,EAnMP,SAAgCA,EAAKmO,GACnD,IAKIjT,EALA0C,EAAM,GACNmnC,EAAW52B,EAAQw1B,kBAAoB3jC,EAAIC,QAAQ,MAAO,IAAMD,EAChEglC,EAAQ72B,EAAQ01B,iBAAmB7tB,SAAWlU,EAAYqM,EAAQ01B,eAClEv9B,EAAQy+B,EAAStsB,MAAMtK,EAAQ4rB,UAAWiL,GAC1CC,GAAa,EAGb91B,EAAUhB,EAAQgB,QACtB,GAAIhB,EAAQ2rB,gBACR,IAAK5+B,EAAI,EAAGA,EAAIoL,EAAMxI,SAAU5C,EACM,IAA9BoL,EAAMpL,GAAG8M,QAAQ,WAbX,mBAcF1B,EAAMpL,GACNiU,EAAU,QAlBZ,wBAmBS7I,EAAMpL,KACbiU,EAAU,cAEd81B,EAAY/pC,EACZA,EAAIoL,EAAMxI,QAKtB,IAAK5C,EAAI,EAAGA,EAAIoL,EAAMxI,SAAU5C,EAC5B,GAAIA,IAAM+pC,EAAV,CAGA,IAKIxoC,EAAKY,EALLuJ,EAAON,EAAMpL,GAEbgqC,EAAmBt+B,EAAKoB,QAAQ,MAChCm9B,GAA4B,IAAtBD,EAA0Bt+B,EAAKoB,QAAQ,KAAOk9B,EAAmB,GAG9D,IAATC,GACA1oC,EAAM0R,EAAQe,QAAQtI,EAAM2B,EAAS2G,QAASC,EAAS,OACvD9R,EAAM8Q,EAAQmsB,mBAAqB,KAAO,KAE1C79B,EAAM0R,EAAQe,QAAQtI,EAAKnH,MAAM,EAAG0lC,GAAM58B,EAAS2G,QAASC,EAAS,OACrE9R,EAAMgK,EAAMwI,SACRm0B,EAAgBp9B,EAAKnH,MAAM0lC,EAAM,GAAIh3B,IACrC,SAAUi3B,GACN,OAAOj3B,EAAQe,QAAQk2B,EAAY78B,EAAS2G,QAASC,EAAS,aAKtE9R,GAAO8Q,EAAQy1B,0BAAwC,eAAZz0B,IAC3C9R,EAAMumC,EAAyBvmC,IAG/BuJ,EAAKoB,QAAQ,QAAU,IACvB3K,EAAMD,EAAQC,GAAO,CAACA,GAAOA,GAG7B0Q,EAAI1S,KAAKuC,EAAKnB,GACdmB,EAAInB,GAAO4K,EAAMmH,QAAQ5Q,EAAInB,GAAMY,GAEnCO,EAAInB,GAAOY,EAInB,OAAOO,EAsIiCynC,CAAYrlC,EAAKmO,GAAWnO,EAChEpC,EAAMuQ,EAAQC,aAAexS,OAAOY,OAAO,MAAQ,GAInDmR,EAAO/R,OAAO+R,KAAKm3B,GACd5pC,EAAI,EAAGA,EAAIyS,EAAK7P,SAAU5C,EAAG,CAClC,IAAIuB,EAAMkR,EAAKzS,GACXoqC,EAASrB,EAAUxnC,EAAKqoC,EAAQroC,GAAM0R,EAAwB,iBAARnO,GAC1DpC,EAAMyJ,EAAM/H,MAAM1B,EAAK0nC,EAAQn3B,GAGnC,OAA4B,IAAxBA,EAAQs1B,YACD7lC,EAGJyJ,EAAMoH,QAAQ7Q,K,cCrQzB3C,EAAOD,QAAU,qkB,cCAjBC,EAAOD,QAAU,2T,eCAjB,SAAUuqC,GAGR,IAAMC,EAAuB,SAACC,EAAa5uB,GAMzC,QALoB4uB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAK9uB,GAEaxZ,OAGjBuoC,EAAsB,SAACH,EAAa5uB,GACpB4uB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAK9uB,GACMrJ,KAAK,YAAY,IAG3Bq4B,EAAsB,SAACJ,EAAa5uB,GACpB4uB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAK9uB,GACMrJ,KAAK,YAAY,IAG3Bs4B,EAAe,SAACL,EAAa5uB,GACb4uB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAK9uB,GACMxZ,IAAI,KAGd0oC,EAAsB,SAACN,EAAa5uB,GACpB4uB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAK9uB,GACMrJ,KAAK,YAAY,IAG3Bw4B,EAAyB,SAACP,EAAa5uB,GACvB4uB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAK9uB,GACMrJ,KAAK,YAAY,IAG3By4B,EAAmB,SAACR,EAAaS,EAAUC,GAC/C,IAAIC,EAAiBX,EAClBC,QAAQ,6CACRhgC,QACAigC,KAAKO,GAEJG,EAAiBZ,EAClBC,QAAQ,6CACRhgC,QACAigC,KAAKQ,GAEJC,EAAe/oC,QACjBgpC,EAAehpC,IAAI+oC,EAAe/oC,OAClCgpC,EAAeC,KAAK,QAASF,EAAe/oC,SAI1CkpC,EAAkB,SAACd,EAAaS,GAKpC,OAJqBT,EAClBC,QAAQ,6CACRhgC,QACAigC,KAAKO,GACc7oC,OAGlBmpC,EAAkB,SAACf,EAAaS,EAAU/pC,GAC9C,IAAIsqC,EAAgBhB,EACjBC,QAAQ,6CACRhgC,QACAigC,KAAKO,GAERO,EAAcppC,IAAIlB,GAClBsqC,EAAcH,KAAK,QAASnqC,IA0D9B,SAASuqC,EAAe7vB,EAAS8vB,GAC/B,IAAIC,EAAWrB,EAAE1uB,GACb3M,EAAO08B,EAASvpC,MAEP,eAAT6M,IACF47B,EAAac,EAAU,iBACvBhB,EAAoBgB,EAAU,iBAE9Bd,EAAac,EAAU,kBACvBhB,EAAoBgB,EAAU,kBAE9Bd,EAAac,EAAU,iBACvBX,EAAiBW,EAAU,yBAA0B,iBACrDhB,EAAoBgB,EAAU,iBAE9Bd,EAAac,EAAU,kBACvBX,EAAiBW,EAAU,0BAA2B,kBACtDhB,EAAoBgB,EAAU,mBAEnB,iBAAT18B,GAAoC,cAATA,IACV,UAAfy8B,GACFb,EAAac,EAAU,iBAEzBf,EAAoBe,EAAU,iBACX,UAAfD,GACFb,EAAac,EAAU,kBAEzBf,EAAoBe,EAAU,kBACX,UAAfD,GACFb,EAAac,EAAU,iBAEzBf,EAAoBe,EAAU,iBACX,UAAfD,GACFb,EAAac,EAAU,kBAEzBf,EAAoBe,EAAU,mBAEnB,cAAT18B,IACF87B,EAAuBY,EAAU,0BACjCZ,EAAuBY,EAAU,2BAEjCd,EAAac,EAAU,iBACvBhB,EAAoBgB,EAAU,iBAE9Bd,EAAac,EAAU,kBACvBhB,EAAoBgB,EAAU,kBAE9Bd,EAAac,EAAU,iBACvBhB,EAAoBgB,EAAU,iBAE9Bd,EAAac,EAAU,kBACvBhB,EAAoBgB,EAAU,mBAEnB,iBAAT18B,GAAoC,eAATA,IAC7B67B,EAAoBa,EAAU,0BAC9Bb,EAAoBa,EAAU,4BAGnB,iBAAT18B,IAEAs7B,EAAqBoB,EAAU,2BAC/BpB,EAAqBoB,EAAU,4BAE/Bf,EAAoBe,EAAU,iBAC9Bf,EAAoBe,EAAU,kBAC9Bf,EAAoBe,EAAU,iBAC9Bf,EAAoBe,EAAU,oBAE9BhB,EAAoBgB,EAAU,iBAC9BhB,EAAoBgB,EAAU,kBAC9BhB,EAAoBgB,EAAU,iBAC9BhB,EAAoBgB,EAAU,oBA7HpCrB,EAAElmC,UAAUoW,OAAM,WAChB8vB,EAAE,+DAA+DsB,MAC/D,WACEH,EAAe54B,KAAM,eAM3Bg5B,IAAIC,WAAW,UAAU,WACvBxB,EAAE,+DAA+DsB,MAC/D,WACEH,EAAe54B,KAAM,gBAM3By3B,EAAElmC,UAAU62B,GACV,SACA,+DACA,SAASxd,GACPguB,EAAe54B,KAAM,aAKzBy3B,EAAElmC,UAAU62B,GACV,eACA,qEACA,SAASxd,GACPguB,EACEnB,EAAEz3B,MACC43B,QAAQ,6CACRhgC,QACAigC,KAAK,yBAMdJ,EAAElmC,UAAU62B,GACV,eACA,oEACA,SAASxd,GACPguB,EACEnB,EAAEz3B,MACC43B,QAAQ,6CACRhgC,QACAigC,KAAK,yBAiFd,IAAMqB,EAAY,SAACnwB,EAASowB,EAAeR,GACzC,IAAIG,EAAWrB,EAAE1uB,GAEbqwB,EAAmBX,EAAgBK,EAAU,0BAC7CO,EAAoBZ,EACtBK,EACA,2BAEE7oB,EAAWwoB,EAAgBK,EAAUK,GAEzC,GAAIE,GAAqBD,GAAoBnpB,EAAU,CACrD,IAAIE,EAAYxa,KAAKiT,MAClBywB,EAAoBD,EAAoBnpB,GAE3CyoB,EAAgBI,EAAUH,EAAexoB,QAEzCuoB,EAAgBI,EAAUH,EAAe,KAIvCW,EAAW,SAACvwB,EAASowB,EAAeR,GACxC,IAAIG,EAAWrB,EAAE1uB,GAEbqwB,EAAmBX,EAAgBK,EAAU,0BAC7CO,EAAoBZ,EACtBK,EACA,2BAEE3oB,EAAYsoB,EAAgBK,EAAUK,GAE1C,GAAIE,GAAqBD,GAAoBjpB,EAAW,CACtD,IAAIF,EAAWta,KAAKiT,MACjBwwB,EAAmBC,EAAqBlpB,GAE3CuoB,EAAgBI,EAAUH,EAAe1oB,QAEzCyoB,EAAgBI,EAAUH,EAAe,KAK7ClB,EAAElmC,UAAU62B,GACV,eACA,2DACA,SAASxd,GACPsuB,EAAUl5B,KAAM,gBAAiB,qBAKrCy3B,EAAElmC,UAAU62B,GACV,eACA,4DACA,SAASxd,GACP0uB,EAASt5B,KAAM,iBAAkB,oBAKrCy3B,EAAElmC,UAAU62B,GACV,eACA,2DACA,SAASxd,GACPsuB,EAAUl5B,KAAM,gBAAiB,qBAKrCy3B,EAAElmC,UAAU62B,GACV,eACA,4DACA,SAASxd,GACP0uB,EAASt5B,KAAM,iBAAkB,oBAlSvC,CAqSGu5B,S;;;;;;CC1RH,SAAU9B,GACR,IAAI+B,EAAQ,KA2yBZ,SAASC,EAAiBC,IAzyB1BV,IAAIW,OAAOC,wBAA0BZ,IAAIQ,MAAM3nC,OAAO,CACpDuK,KAAM,0BACNs9B,IAAK,KACLG,OAAQ,KACRC,KAAM,KAENC,QAAS,CACPpyB,MAAO,aACPqyB,OAAQ,cAGV/hB,OAAQ,CACN,2BAA4B,MAC5B,4BAA6B,OAC7B,8BAA+B,SAC/B,4BAA6B,SAC7B,4BAA6B,aAC7B,0BAA2B,oBAG7BgiB,iBAAkB,SAASrvB,GAAO,WAC5BsvB,EAAgBtvB,EAAMuvB,cAEtBC,EAAS3C,EAAEz3B,KAAKq6B,QACjBxC,KAAK,yCACLh9B,KAAK,OAEJy/B,EAAQJ,EAAcI,MACtBC,EAAW,IAAIlqC,SAInB,GAFA2P,KAAKw6B,aAAc,EAEdF,EAAMtqC,OAAX,CAIAmE,MAAMyP,KAAKzP,MAAMmmC,EAAMtqC,QAAQ6P,QAAQ0R,KAAI,SAAAklB,GACzC8D,EAASP,OAAO,QAASM,EAAM7D,GAAQ6D,EAAM7D,GAAO9oC,MACpD4sC,EAASP,OAAO,MAAOI,MAGzBF,EAAc7rC,MAAQ,GAEtB,IAAIosC,EAAW,CACbr8B,iBAAkB,SAAAs8B,GAChB,IAAIC,EAAmBhlC,KAAKiT,MACF,IAAvB8xB,EAAcE,OAAgBF,EAAcG,OAG/C,EAAKnB,IACF7B,KAAK,6BACLxgB,KACC0L,kBACEzxB,OAAOwpC,mBAAmBC,gBAC1BJ,KAIRpgC,QAAS,CACP,gBAAiBjJ,OAAO0pC,MAAMC,MAC9B,aAAc3pC,OAAO0pC,MAAME,gBAI/BzD,EAAEz3B,KAAK05B,KACJ7B,KAAK,oBACLsD,OAEH1D,EAAEz3B,KAAK05B,KACJ7B,KAAK,6BACLuD,OAEHjW,IACGkW,KADH,UACW/pC,OAAO0pC,MAAMM,SADxB,oBACoDf,EAAUE,GAC3Dj8B,MAAK,SAAAnB,GAEJ8nB,IACGl3B,IADH,UAEOqD,OAAO0pC,MAAMM,SAFpB,yBAE6Cj+B,EAASxC,KAAK0gC,gBAExD/8B,MAAK,SAAAnB,GACJ,IAAIm+B,EAAa,IAAIlqC,OAAOmqC,SAASC,MAAMr+B,EAASxC,MACpD,EAAKmY,OAAOwoB,MAGhB/D,EAAE,EAAKiC,KACJ7B,KAAK,6BACLsD,OAEH1D,EAAE,EAAKiC,KACJ7B,KAAK,oBACLuD,OAEH,IAAIf,EAAS,EAAKA,OAGlBA,EACGxC,KAAK,yCACLh9B,KAAK,oBAAqBwC,EAASxC,KAAK0gC,eACxC/C,KAAK,yBAA0Bn7B,EAASxC,KAAK0gC,eAEhDpW,IACGl3B,IADH,UAEOqD,OAAO0pC,MAAMM,SAFpB,yBAE6Cj+B,EAASxC,KAAK0gC,gBAExD/8B,MAAK,SAAAnB,GACJ,IAAIm+B,EAAa,IAAIlqC,OAAOmqC,SAASC,MAAMr+B,EAASxC,MAEpD,EAAKmY,OAAOwoB,GACZ,EAAKG,UAAU,CAAEH,WAAYA,EAAYhC,MAAOa,UArCxD,OAwCS,SAAAt7B,GACL04B,EAAE,EAAKiC,KACJ7B,KAAK,6BACLsD,OAEH1D,EAAE,EAAKiC,KACJ7B,KAAK,oBACLuD,OAEH,IAAIQ,EAAetqC,OAAOwpC,mBAAmBe,cAG3C98B,EAAM1B,UACN0B,EAAM1B,SAASxC,MACfkE,EAAM1B,SAASxC,KAAKgE,UAEpB+8B,EAAe78B,EAAM1B,SAASxC,KAAKgE,SAGrCvN,OAAOwqC,MAAMF,QAiBnBG,MAAO,WAEL/7B,KAAK05B,IAAM15B,KAAKq6B,OAAOxC,KAAK,yCAC5B73B,KAAK65B,OAAS75B,KAAK05B,IAAI7B,KAAK,wBAC5B73B,KAAK85B,KAAO95B,KAAK05B,IAAI7B,KAAK,OAG1B73B,KAAKnS,EAAImrC,IAAIgD,SAASh8B,KAAK05B,MAgB7BuC,WAAY,WAAW,WACrBj8B,KAAKw6B,YAAc,KACnB,IAAI0B,EAAOl8B,KAGY,SAAnBA,KAAKnS,EAAEsuC,UACTn8B,KAAK05B,IAAI0C,QAAQ,QAAQ5D,KAAK,UAAW,uBAG3Cx4B,KAAKq8B,mBAAqBr8B,KAAKs8B,cAAc1tC,KAAKoR,MAElDy3B,EAAElmC,UAAU62B,GAAG,QAAS,0CAA0C,kBAChE,EAAKmU,gBAGP9E,EAAElmC,UACC82B,IAAI,QAAS,yCACbD,GAAG,QAAS,yCAAyC,WACpD,EAAK5U,QAAQ4I,WAGjBqb,EAAElmC,UACC82B,IAAI,QAAS,wCACbD,GAAG,QAAS,wCAAwC,WACnD,IAAIoU,EAAWN,EAAK1oB,QAAQ3J,SAAQ,GAEpC4tB,EAAE,yCAAyCgF,IACzC,YACAP,EAAK1oB,QAAQC,cAAcpF,OAGdopB,EAAE+B,GACd3B,KAAK,yCACLh9B,KAAK,aAFR,IAIIu/B,EAAS3C,EAAE+B,GACZ3B,KAAK,yCACLh9B,KAAK,OAEJA,EAAO,CACTyrB,GAAImR,EAAEz3B,MAAMnF,KAAK,MACjBw+B,kBAAmB5B,EAAEz3B,MAAMnF,KAAK,uBAChCu+B,iBAAkB3B,EAAEz3B,MAAMnF,KAAK,sBAC/B6hC,SAAUjF,EAAEz3B,MAAMnF,KAAK,aACvBlH,EAAG6oC,EAAS7oC,EACZ0nB,EAAGmhB,EAASnhB,EACZhN,MAAOmuB,EAASnuB,MAChBD,OAAQouB,EAASpuB,OACjBuuB,aAAc3B,MAAM2B,aACpBhuC,IAAKyrC,GAGP3C,EAAE,wCAAwC/3B,KAAK,YAAY,GAC3D+3B,EAAE,yCAAyC/3B,KAAK,YAAY,GAG5D,IAAIk9B,EAAU,ouBAQE9B,mBAAmB+B,qBARrB,eAaV99B,EAAQ,4iBAQE+7B,mBAAmBgC,gBARrB,eAYZrF,EAAE,uDAAuDsF,QACzDtF,EAAE,uDAAuDpgB,KACvDulB,GAEFV,EAAK1oB,QAAQqJ,UAEb,IAAIxc,EAAU,GAEV5G,EAAM,KAEoC,KAA1CnI,OAAO0rC,eAAeC,kBACxBxjC,EAAM,GAAH,OAAMnI,OAAO0pC,MAAMM,SAAnB,kBACHj7B,EAAU,CACR9F,QAAS,CACP,gBAAiBjJ,OAAO0pC,MAAMC,MAC9B,aAAc3pC,OAAO0pC,MAAME,iBAKa,MAA1C5pC,OAAO0rC,eAAeC,kBACxBxjC,EAAMyjC,QACNriC,EAAOsiC,IAAG5qC,UAAU,CAClB+nB,OAAQ,mCACRzf,KAAMpF,KAAKlD,UAAUsI,MAIzBsqB,IACGkW,KAAK5hC,EAAKoB,EAAMwF,GAChB7B,MAAK,SAAAnB,GACJ6+B,EAAKkB,aAAa//B,EAASxC,MAC3B48B,EAAE,wCAAwC/3B,KAAK,YAAY,GAC3D+3B,EAAE,yCAAyC/3B,KACzC,YACA,GAEF+3B,EAAE,uDAAuDsF,WAT7D,OAWS,SAAA1/B,GACLggC,QAAQt+B,MAAM1B,GACd6+B,EAAK1oB,QAAQoJ,SACb6a,EAAE,wCAAwC/3B,KAAK,YAAY,GAC3D+3B,EAAE,yCAAyC/3B,KACzC,YACA,GAEF+3B,EAAE,uDAAuDsF,QACzDtF,EAAE,uDAAuDpgB,KACvDtY,UAqBZu+B,QAAS,SAAS9B,GAKhB,IAHAA,EAAaA,GAAc,IAGZ+B,OAAQ,OAAO/B,EAG9B,IAAI3gC,EAAO,CACTpB,IAAK,GACLid,IAAK,GACLqR,MAAO,GACPyV,QAAS,GACTtU,YAAa,GACb7a,MAAO,EACPD,OAAQ,GAoBV,OAhBIotB,EAAWlV,KAEbzrB,EAAO2gC,EAAW5H,YAWpB/4B,EAAK0iC,QAAS,EAGP1iC,GAgBTmY,OAAQ,SAASnY,GAGfA,EAAOmF,KAAKs9B,QAAQziC,GAGpBmF,KAAK85B,KAAKtB,KAAK,CACb7hB,IAAK9b,EAAKpB,IACVid,IAAK7b,EAAK6b,IACVqR,MAAOltB,EAAKktB,QAId,IAAIx4B,EAAM,GAGNsL,EAAKyrB,KACP/2B,EAAMsL,EAAKyrB,IAIb0S,IAAIzpC,IAAIyQ,KAAK65B,OAAQtqC,GAGjBA,EACFyQ,KAAK05B,IAAIxwB,SAAS,aAElBlJ,KAAK05B,IAAInwB,YAAY,cAiBzBF,IAAK,WAEH,IAAI6yB,EAAOl8B,KACTq6B,EAASr6B,KAAKq6B,OAGZoD,EAAYzE,IAAI0E,kBAAkB19B,KAAKq6B,OAAQ,YAGvCrB,IAAI2E,MAAMC,MAAM,CAC1B7V,MAAOiR,IAAI6E,GAAG,QAAS,UACvBtvC,KAAM,SACN6N,KAAM,QACNo9B,MAAOa,EAAOx/B,KAAK,OACnBijC,UAAU,EACVC,QAAS/9B,KAAKnS,EAAEkwC,QAChBC,WAAYh+B,KAAKnS,EAAEmwC,WAEnBC,OAAQ,SAASzC,EAAYpuC,GAE3B,GAAIA,EAAI,EAAG,CAET,IAAIuB,EAAM0rC,EAAOx/B,KAAK,OACpBqjC,EAAM7D,EAAO+B,QAAQ,YA4BvB,GAzBA/B,GAAS,EAGT6D,EAAIC,QAAQ,oBAAoBpF,MAAK,WAKnC,GAHAsB,EAASrB,IAAIoF,UAAUzvC,EAAK8oC,EAAEz3B,OAG9B,CAGA,IACEq6B,EACGxC,KAAK,mDACLwG,SAOL,OAAO,EALLhE,GAAS,OASRA,EAAQ,CAIX,KAHA6D,EAAMlF,IAAIW,OAAO2E,SAASC,QAAQd,GAAWp0B,OAGnC,OAAO,EAGjBgxB,EAASrB,IAAIoF,UAAUzvC,EAAKuvC,IAIhChC,EAAK1B,aAAc,EAGnBH,EACGxC,KAAK,yCACLh9B,KAAK,oBAAqB2gC,EAAWlV,IACrCkS,KAAK,yBAA0BgD,EAAWlV,IAE7C4V,EAAKP,UAAU,CAAEH,WAAYA,EAAYhC,MAAOa,IAGhD6B,EAAK3xB,IAAI,SAAU8vB,GAAQrnB,OAAOwoB,OAKxCgD,WAAY,WAAW,WACrBx+B,KAAKw6B,aAAc,EACnB,IAAIiE,EAAkBhH,EAAEz3B,KAAKq6B,QAC1BxC,KAAK,yCACLh9B,KAAK,qBAEJwN,EAAW,SAAAhL,GACb,IAAIm+B,EAAa,IAAIlqC,OAAOmqC,SAASC,MAAMr+B,EAASxC,MAChDw/B,EAAS,EAAKA,OAClB,EAAKsB,UAAU,CAAEH,WAAYA,EAAYhC,MAAOa,KASlD,GAN8C,KAA1C/oC,OAAO0rC,eAAeC,iBACxB9X,IACGl3B,IADH,UACUqD,OAAO0pC,MAAMM,SADvB,yBACgDmD,IAC7CjgC,MAAK,SAAAnB,GAAQ,OAAIgL,EAAShL,MAGe,MAA1C/L,OAAO0rC,eAAeC,gBAAyB,CACjD,IAAIpiC,EAAOsiC,IAAG5qC,UAAU,CACtB+nB,OAAQ,6CACRzf,KAAMpF,KAAKlD,UAAU,CAAEgpC,cAAekD,MAExCtZ,IAAMkW,KAAK6B,QAASriC,GAAM2D,MAAK,SAAAnB,GAAQ,OAAIgL,EAAShL,QAiBxDqhC,KAAM,WAGO1+B,KAAKq6B,OADhB,IAII9qC,EAAM,KAWV,GANEA,EAHAyQ,KAAK65B,OAAOrD,SAASgC,KAAK,2BACW,aAArClnC,OAAO0rC,eAAe2B,WAEhB3+B,KAAK65B,OAAOrD,SAASgC,KAAK,0BAE1Bx4B,KAAK65B,OAAOtqC,MAORypC,IAAI2E,MAAMC,MAAM,CAC1B7V,MAAOiR,IAAI6E,GAAG,QAAS,QACvB1jB,OAAQ6e,IAAI6E,GAAG,QAAS,UACxBtvC,KAAM,OACNitC,WAAYjsC,KAiBhBia,OAAQ,WAENxJ,KAAKq6B,OACFxC,KAAK,yCACLh9B,KAAK,oBAAqB,MAC1B29B,KAAK,yBAA0B,MAC/B39B,KAAK,cAAe,MACpB29B,KAAK,mBAAoB,MAM5Bx4B,KAAKgT,OAHY,KAmBnB8H,OAAQ,SAAS5nB,KAIjBopC,cAAe,SAAS1xB,GACJ,WAAdA,EAAMjc,KACRqR,KAAKu8B,cAITZ,UAAW,SAAS9gC,GAClB,IAAIpB,EAAMoB,EAAK2gC,WAAW5H,WAAWn6B,IACjC6sB,EAAKzrB,EAAK2gC,WAAW5H,WAAWtN,GACpCkT,EAAQ3+B,EAAK2+B,MAEbjoC,SAAS4M,iBAAiB,UAAW6B,KAAKq8B,oBAE1C,IAAIjD,EAAmB3B,EAAE+B,GACtB3B,KAAK,yCACLh9B,KAAK,sBACJw+B,EAAoB5B,EAAE+B,GACvB3B,KAAK,yCACLh9B,KAAK,uBACJ6hC,EAAWjF,EAAE+B,GACd3B,KAAK,yCACLh9B,KAAK,aACJoV,EAAWwnB,EAAE+B,GACd3B,KAAK,yCACLh9B,KAAK,aACJsV,EAAYsnB,EAAE+B,GACf3B,KAAK,yCACLh9B,KAAK,cAEJwF,EAAU,CACZyF,YAAaszB,EAAmBC,EAChC3zB,SAAU,EACVgB,aAAc,EACdI,UAAU,EACVZ,kBAAkB,EAClBC,kBAAkB,EAClBH,YAAY,GAGG,eAAb02B,IACFr8B,EAAQ0H,KAAO,SAAS6C,GACtB,IAAIyD,EAAQzD,EAAMU,OAAO+C,MACrBD,EAASxD,EAAMU,OAAO8C,QACtBC,EAAQ+qB,GAAoBhrB,EAASirB,IACvCr5B,KAAKwT,QAAQxJ,QAAQ,CACnBqE,MAAO+qB,EACPhrB,OAAQirB,MAMC,iBAAbqD,GAA6C,IAAdvsB,GAAgC,IAAbF,IACpD5P,EAAQ0H,KAAO,SAAS6C,GACtB,IAAIyD,EAAQzD,EAAMU,OAAO+C,MACrBD,EAASxD,EAAMU,OAAO8C,QACtBC,EAAQ4B,GAAY7B,EAAS+B,IAC/BnQ,KAAKwT,QAAQxJ,QAAQ,CACnBqE,MAAO4B,EACP7B,OAAQ+B,MAMhB,IAAIyuB,EAAcnH,EAAE+B,GACjB3B,KAAK,yCACLh9B,KAAK,eAEJ+jC,IACFv+B,EAAQxF,KAAO+jC,GAIjBnH,EAAE,QAAQuC,OAAV,iXAQMc,mBAAmB+D,YARzB,0MAcMzvC,EAAQ,IAdd,wPAoBWqK,EApBX,4eAgCQrK,EAAQ,IAhChB,yBAiCQ0rC,mBAAmB1e,MAjC3B,kJAoCQ0e,mBAAmBr8B,OApC3B,qKAwCiB6nB,EAxCjB,oDAyCkC+S,EAzClC,mDA0CiCD,EA1CjC,0CA2CwBsD,EA3CxB,uCA6CQ5B,mBAAmB/yB,KA7C3B,wFAsDA/H,KAAKwT,QAAU,IAAIiM,IACjBgY,EAAE,+CAA+C,GACjDp3B,GAIF/O,OAAOwtC,gCAAkC9+B,KAAKwT,SAGhD4pB,aAAc,SAASviC,GAAM,WAE3B48B,EAAE+B,GACC3B,KAAK,yCACLh9B,KAAK,cAAemF,KAAKwT,QAAQ3J,SAAQ,IACzC2uB,KAAK,mBAAoB/iC,KAAKlD,UAAUyN,KAAKwT,QAAQ3J,SAAQ,KAGhE7J,KAAKwT,QAAQsJ,UAEb2a,EAAE+B,GACC3B,KAAK,SACLjgC,QACArI,IAAIsL,EAAKyrB,IAEZ,IAAIje,EAAW,SAAAhL,GACb,IAAIm+B,EAAa,IAAIlqC,OAAOmqC,SAASC,MAAMr+B,EAASxC,MAEpD,EAAKmY,OAAOwoB,GACZ,EAAKhB,aAAc,EACnB,EAAK+B,cASP,GAN8C,KAA1CjrC,OAAO0rC,eAAeC,iBACxB9X,IACGl3B,IADH,UACUqD,OAAO0pC,MAAMM,SADvB,yBACgDzgC,EAAKyrB,KAClD9nB,MAAK,SAAAnB,GAAQ,OAAIgL,EAAShL,MAGe,MAA1C/L,OAAO0rC,eAAeC,gBAAyB,CACjD,IAAI8B,EAAW5B,IAAG5qC,UAAU,CAC1B+nB,OAAQ,6CACRzf,KAAMpF,KAAKlD,UAAU,CAAEgpC,cAAe1gC,EAAKyrB,OAE7CnB,IAAMkW,KAAK6B,QAAS6B,GAAUvgC,MAAK,SAAAnB,GAAQ,OAAIgL,EAAShL,QAI5Dk/B,WAAY,WACNv8B,KAAKw6B,cAGPxB,IAAIzpC,IAAIyQ,KAAK65B,OAAQ,IACrB75B,KAAKgT,OAAO,KAEdykB,EAAE,yCAAyCjuB,SAC3CjY,SAASiZ,oBAAoB,UAAWxK,KAAKq8B,oBAC7Cr8B,KAAKwT,QAAQsJ,aAgCjBkc,IAAIC,WAAW,2CAA4CQ,GAC3DT,IAAIC,WAAW,4CAA6CQ,GA7zB9D,CA8zBGF,Q", "file": "input-script.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 49);\n", "'use strict';\n\nvar bind = require('./helpers/bind');\n\n/*global toString:true*/\n\n// utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Array, otherwise false\n */\nfunction isArray(val) {\n  return toString.call(val) === '[object Array]';\n}\n\n/**\n * Determine if a value is undefined\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nfunction isUndefined(val) {\n  return typeof val === 'undefined';\n}\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nfunction isArrayBuffer(val) {\n  return toString.call(val) === '[object ArrayBuffer]';\n}\n\n/**\n * Determine if a value is a FormData\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nfunction isFormData(val) {\n  return (typeof FormData !== 'undefined') && (val instanceof FormData);\n}\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  var result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (val.buffer instanceof ArrayBuffer);\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a String, otherwise false\n */\nfunction isString(val) {\n  return typeof val === 'string';\n}\n\n/**\n * Determine if a value is a Number\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Number, otherwise false\n */\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\n\n/**\n * Determine if a value is an Object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is an Object, otherwise false\n */\nfunction isObject(val) {\n  return val !== null && typeof val === 'object';\n}\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {Object} val The value to test\n * @return {boolean} True if value is a plain Object, otherwise false\n */\nfunction isPlainObject(val) {\n  if (toString.call(val) !== '[object Object]') {\n    return false;\n  }\n\n  var prototype = Object.getPrototypeOf(val);\n  return prototype === null || prototype === Object.prototype;\n}\n\n/**\n * Determine if a value is a Date\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Date, otherwise false\n */\nfunction isDate(val) {\n  return toString.call(val) === '[object Date]';\n}\n\n/**\n * Determine if a value is a File\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a File, otherwise false\n */\nfunction isFile(val) {\n  return toString.call(val) === '[object File]';\n}\n\n/**\n * Determine if a value is a Blob\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nfunction isBlob(val) {\n  return toString.call(val) === '[object Blob]';\n}\n\n/**\n * Determine if a value is a Function\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nfunction isFunction(val) {\n  return toString.call(val) === '[object Function]';\n}\n\n/**\n * Determine if a value is a Stream\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nfunction isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {Object} val The value to test\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nfunction isURLSearchParams(val) {\n  return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams;\n}\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n * @returns {String} The String freed of excess whitespace\n */\nfunction trim(str) {\n  return str.replace(/^\\s*/, '').replace(/\\s*$/, '');\n}\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n */\nfunction isStandardBrowserEnv() {\n  if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' ||\n                                           navigator.product === 'NativeScript' ||\n                                           navigator.product === 'NS')) {\n    return false;\n  }\n  return (\n    typeof window !== 'undefined' &&\n    typeof document !== 'undefined'\n  );\n}\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n */\nfunction forEach(obj, fn) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (var i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    for (var key in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        fn.call(null, obj[key], key, obj);\n      }\n    }\n  }\n}\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  var result = {};\n  function assignValue(val, key) {\n    if (isPlainObject(result[key]) && isPlainObject(val)) {\n      result[key] = merge(result[key], val);\n    } else if (isPlainObject(val)) {\n      result[key] = merge({}, val);\n    } else if (isArray(val)) {\n      result[key] = val.slice();\n    } else {\n      result[key] = val;\n    }\n  }\n\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n * @return {Object} The resulting value of object a\n */\nfunction extend(a, b, thisArg) {\n  forEach(b, function assignValue(val, key) {\n    if (thisArg && typeof val === 'function') {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  });\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n * @return {string} content value without BOM\n */\nfunction stripBOM(content) {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\nmodule.exports = {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isObject: isObject,\n  isPlainObject: isPlainObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isStandardBrowserEnv: isStandardBrowserEnv,\n  forEach: forEach,\n  merge: merge,\n  extend: extend,\n  trim: trim,\n  stripBOM: stripBOM\n};\n", "module.exports = require('./lib/axios');", "'use strict';\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\n\nmodule.exports = {\n    formats: formats,\n    parse: parse,\n    stringify: stringify\n};\n", "'use strict';\n\nvar undefined;\n\nvar $SyntaxError = SyntaxError;\nvar $Function = Function;\nvar $TypeError = TypeError;\n\n// eslint-disable-next-line consistent-return\nvar getEvalledConstructor = function (expressionSyntax) {\n\ttry {\n\t\treturn $Function('\"use strict\"; return (' + expressionSyntax + ').constructor;')();\n\t} catch (e) {}\n};\n\nvar $gOPD = Object.getOwnPropertyDescriptor;\nif ($gOPD) {\n\ttry {\n\t\t$gOPD({}, '');\n\t} catch (e) {\n\t\t$gOPD = null; // this is IE 8, which has a broken gOPD\n\t}\n}\n\nvar throwTypeError = function () {\n\tthrow new $TypeError();\n};\nvar ThrowTypeError = $gOPD\n\t? (function () {\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties\n\t\t\targuments.callee; // IE 8 does not throw here\n\t\t\treturn throwTypeError;\n\t\t} catch (calleeThrows) {\n\t\t\ttry {\n\t\t\t\t// IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')\n\t\t\t\treturn $gOPD(arguments, 'callee').get;\n\t\t\t} catch (gOPDthrows) {\n\t\t\t\treturn throwTypeError;\n\t\t\t}\n\t\t}\n\t}())\n\t: throwTypeError;\n\nvar hasSymbols = require('has-symbols')();\n\nvar getProto = Object.getPrototypeOf || function (x) { return x.__proto__; }; // eslint-disable-line no-proto\n\nvar needsEval = {};\n\nvar TypedArray = typeof Uint8Array === 'undefined' ? undefined : getProto(Uint8Array);\n\nvar INTRINSICS = {\n\t'%AggregateError%': typeof AggregateError === 'undefined' ? undefined : AggregateError,\n\t'%Array%': Array,\n\t'%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n\t'%ArrayIteratorPrototype%': hasSymbols ? getProto([][Symbol.iterator]()) : undefined,\n\t'%AsyncFromSyncIteratorPrototype%': undefined,\n\t'%AsyncFunction%': needsEval,\n\t'%AsyncGenerator%': needsEval,\n\t'%AsyncGeneratorFunction%': needsEval,\n\t'%AsyncIteratorPrototype%': needsEval,\n\t'%Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n\t'%BigInt%': typeof BigInt === 'undefined' ? undefined : BigInt,\n\t'%Boolean%': Boolean,\n\t'%DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n\t'%Date%': Date,\n\t'%decodeURI%': decodeURI,\n\t'%decodeURIComponent%': decodeURIComponent,\n\t'%encodeURI%': encodeURI,\n\t'%encodeURIComponent%': encodeURIComponent,\n\t'%Error%': Error,\n\t'%eval%': eval, // eslint-disable-line no-eval\n\t'%EvalError%': EvalError,\n\t'%Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n\t'%Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n\t'%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined : FinalizationRegistry,\n\t'%Function%': $Function,\n\t'%GeneratorFunction%': needsEval,\n\t'%Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n\t'%Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n\t'%Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n\t'%isFinite%': isFinite,\n\t'%isNaN%': isNaN,\n\t'%IteratorPrototype%': hasSymbols ? getProto(getProto([][Symbol.iterator]())) : undefined,\n\t'%JSON%': typeof JSON === 'object' ? JSON : undefined,\n\t'%Map%': typeof Map === 'undefined' ? undefined : Map,\n\t'%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols ? undefined : getProto(new Map()[Symbol.iterator]()),\n\t'%Math%': Math,\n\t'%Number%': Number,\n\t'%Object%': Object,\n\t'%parseFloat%': parseFloat,\n\t'%parseInt%': parseInt,\n\t'%Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n\t'%Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n\t'%RangeError%': RangeError,\n\t'%ReferenceError%': ReferenceError,\n\t'%Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n\t'%RegExp%': RegExp,\n\t'%Set%': typeof Set === 'undefined' ? undefined : Set,\n\t'%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols ? undefined : getProto(new Set()[Symbol.iterator]()),\n\t'%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n\t'%String%': String,\n\t'%StringIteratorPrototype%': hasSymbols ? getProto(''[Symbol.iterator]()) : undefined,\n\t'%Symbol%': hasSymbols ? Symbol : undefined,\n\t'%SyntaxError%': $SyntaxError,\n\t'%ThrowTypeError%': ThrowTypeError,\n\t'%TypedArray%': TypedArray,\n\t'%TypeError%': $TypeError,\n\t'%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n\t'%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n\t'%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n\t'%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n\t'%URIError%': URIError,\n\t'%WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n\t'%WeakRef%': typeof WeakRef === 'undefined' ? undefined : WeakRef,\n\t'%WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet\n};\n\nvar doEval = function doEval(name) {\n\tvar value;\n\tif (name === '%AsyncFunction%') {\n\t\tvalue = getEvalledConstructor('async function () {}');\n\t} else if (name === '%GeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('function* () {}');\n\t} else if (name === '%AsyncGeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('async function* () {}');\n\t} else if (name === '%AsyncGenerator%') {\n\t\tvar fn = doEval('%AsyncGeneratorFunction%');\n\t\tif (fn) {\n\t\t\tvalue = fn.prototype;\n\t\t}\n\t} else if (name === '%AsyncIteratorPrototype%') {\n\t\tvar gen = doEval('%AsyncGenerator%');\n\t\tif (gen) {\n\t\t\tvalue = getProto(gen.prototype);\n\t\t}\n\t}\n\n\tINTRINSICS[name] = value;\n\n\treturn value;\n};\n\nvar LEGACY_ALIASES = {\n\t'%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],\n\t'%ArrayPrototype%': ['Array', 'prototype'],\n\t'%ArrayProto_entries%': ['Array', 'prototype', 'entries'],\n\t'%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],\n\t'%ArrayProto_keys%': ['Array', 'prototype', 'keys'],\n\t'%ArrayProto_values%': ['Array', 'prototype', 'values'],\n\t'%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],\n\t'%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],\n\t'%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],\n\t'%BooleanPrototype%': ['Boolean', 'prototype'],\n\t'%DataViewPrototype%': ['DataView', 'prototype'],\n\t'%DatePrototype%': ['Date', 'prototype'],\n\t'%ErrorPrototype%': ['Error', 'prototype'],\n\t'%EvalErrorPrototype%': ['EvalError', 'prototype'],\n\t'%Float32ArrayPrototype%': ['Float32Array', 'prototype'],\n\t'%Float64ArrayPrototype%': ['Float64Array', 'prototype'],\n\t'%FunctionPrototype%': ['Function', 'prototype'],\n\t'%Generator%': ['GeneratorFunction', 'prototype'],\n\t'%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],\n\t'%Int8ArrayPrototype%': ['Int8Array', 'prototype'],\n\t'%Int16ArrayPrototype%': ['Int16Array', 'prototype'],\n\t'%Int32ArrayPrototype%': ['Int32Array', 'prototype'],\n\t'%JSONParse%': ['JSON', 'parse'],\n\t'%JSONStringify%': ['JSON', 'stringify'],\n\t'%MapPrototype%': ['Map', 'prototype'],\n\t'%NumberPrototype%': ['Number', 'prototype'],\n\t'%ObjectPrototype%': ['Object', 'prototype'],\n\t'%ObjProto_toString%': ['Object', 'prototype', 'toString'],\n\t'%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],\n\t'%PromisePrototype%': ['Promise', 'prototype'],\n\t'%PromiseProto_then%': ['Promise', 'prototype', 'then'],\n\t'%Promise_all%': ['Promise', 'all'],\n\t'%Promise_reject%': ['Promise', 'reject'],\n\t'%Promise_resolve%': ['Promise', 'resolve'],\n\t'%RangeErrorPrototype%': ['RangeError', 'prototype'],\n\t'%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],\n\t'%RegExpPrototype%': ['RegExp', 'prototype'],\n\t'%SetPrototype%': ['Set', 'prototype'],\n\t'%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],\n\t'%StringPrototype%': ['String', 'prototype'],\n\t'%SymbolPrototype%': ['Symbol', 'prototype'],\n\t'%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],\n\t'%TypedArrayPrototype%': ['TypedArray', 'prototype'],\n\t'%TypeErrorPrototype%': ['TypeError', 'prototype'],\n\t'%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],\n\t'%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],\n\t'%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],\n\t'%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],\n\t'%URIErrorPrototype%': ['URIError', 'prototype'],\n\t'%WeakMapPrototype%': ['WeakMap', 'prototype'],\n\t'%WeakSetPrototype%': ['WeakSet', 'prototype']\n};\n\nvar bind = require('function-bind');\nvar hasOwn = require('has');\nvar $concat = bind.call(Function.call, Array.prototype.concat);\nvar $spliceApply = bind.call(Function.apply, Array.prototype.splice);\nvar $replace = bind.call(Function.call, String.prototype.replace);\nvar $strSlice = bind.call(Function.call, String.prototype.slice);\n\n/* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */\nvar rePropName = /[^%.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|%$))/g;\nvar reEscapeChar = /\\\\(\\\\)?/g; /** Used to match backslashes in property paths. */\nvar stringToPath = function stringToPath(string) {\n\tvar first = $strSlice(string, 0, 1);\n\tvar last = $strSlice(string, -1);\n\tif (first === '%' && last !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected closing `%`');\n\t} else if (last === '%' && first !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected opening `%`');\n\t}\n\tvar result = [];\n\t$replace(string, rePropName, function (match, number, quote, subString) {\n\t\tresult[result.length] = quote ? $replace(subString, reEscapeChar, '$1') : number || match;\n\t});\n\treturn result;\n};\n/* end adaptation */\n\nvar getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {\n\tvar intrinsicName = name;\n\tvar alias;\n\tif (hasOwn(LEGACY_ALIASES, intrinsicName)) {\n\t\talias = LEGACY_ALIASES[intrinsicName];\n\t\tintrinsicName = '%' + alias[0] + '%';\n\t}\n\n\tif (hasOwn(INTRINSICS, intrinsicName)) {\n\t\tvar value = INTRINSICS[intrinsicName];\n\t\tif (value === needsEval) {\n\t\t\tvalue = doEval(intrinsicName);\n\t\t}\n\t\tif (typeof value === 'undefined' && !allowMissing) {\n\t\t\tthrow new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n\t\t}\n\n\t\treturn {\n\t\t\talias: alias,\n\t\t\tname: intrinsicName,\n\t\t\tvalue: value\n\t\t};\n\t}\n\n\tthrow new $SyntaxError('intrinsic ' + name + ' does not exist!');\n};\n\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n\tif (typeof name !== 'string' || name.length === 0) {\n\t\tthrow new $TypeError('intrinsic name must be a non-empty string');\n\t}\n\tif (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n\t\tthrow new $TypeError('\"allowMissing\" argument must be a boolean');\n\t}\n\n\tvar parts = stringToPath(name);\n\tvar intrinsicBaseName = parts.length > 0 ? parts[0] : '';\n\n\tvar intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);\n\tvar intrinsicRealName = intrinsic.name;\n\tvar value = intrinsic.value;\n\tvar skipFurtherCaching = false;\n\n\tvar alias = intrinsic.alias;\n\tif (alias) {\n\t\tintrinsicBaseName = alias[0];\n\t\t$spliceApply(parts, $concat([0, 1], alias));\n\t}\n\n\tfor (var i = 1, isOwn = true; i < parts.length; i += 1) {\n\t\tvar part = parts[i];\n\t\tvar first = $strSlice(part, 0, 1);\n\t\tvar last = $strSlice(part, -1);\n\t\tif (\n\t\t\t(\n\t\t\t\t(first === '\"' || first === \"'\" || first === '`')\n\t\t\t\t|| (last === '\"' || last === \"'\" || last === '`')\n\t\t\t)\n\t\t\t&& first !== last\n\t\t) {\n\t\t\tthrow new $SyntaxError('property names with quotes must have matching quotes');\n\t\t}\n\t\tif (part === 'constructor' || !isOwn) {\n\t\t\tskipFurtherCaching = true;\n\t\t}\n\n\t\tintrinsicBaseName += '.' + part;\n\t\tintrinsicRealName = '%' + intrinsicBaseName + '%';\n\n\t\tif (hasOwn(INTRINSICS, intrinsicRealName)) {\n\t\t\tvalue = INTRINSICS[intrinsicRealName];\n\t\t} else if (value != null) {\n\t\t\tif (!(part in value)) {\n\t\t\t\tif (!allowMissing) {\n\t\t\t\t\tthrow new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');\n\t\t\t\t}\n\t\t\t\treturn void undefined;\n\t\t\t}\n\t\t\tif ($gOPD && (i + 1) >= parts.length) {\n\t\t\t\tvar desc = $gOPD(value, part);\n\t\t\t\tisOwn = !!desc;\n\n\t\t\t\t// By convention, when a data property is converted to an accessor\n\t\t\t\t// property to emulate a data property that does not suffer from\n\t\t\t\t// the override mistake, that accessor's getter is marked with\n\t\t\t\t// an `originalValue` property. Here, when we detect this, we\n\t\t\t\t// uphold the illusion by pretending to see that original data\n\t\t\t\t// property, i.e., returning the value rather than the getter\n\t\t\t\t// itself.\n\t\t\t\tif (isOwn && 'get' in desc && !('originalValue' in desc.get)) {\n\t\t\t\t\tvalue = desc.get;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = value[part];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tisOwn = hasOwn(value, part);\n\t\t\t\tvalue = value[part];\n\t\t\t}\n\n\t\t\tif (isOwn && !skipFurtherCaching) {\n\t\t\t\tINTRINSICS[intrinsicRealName] = value;\n\t\t\t}\n\t\t}\n\t}\n\treturn value;\n};\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n", "'use strict';\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\n\nvar Format = {\n    RFC1738: 'RFC1738',\n    RFC3986: 'RFC3986'\n};\n\nmodule.exports = {\n    'default': Format.RFC3986,\n    formatters: {\n        RFC1738: function (value) {\n            return replace.call(value, percentTwenties, '+');\n        },\n        RFC3986: function (value) {\n            return String(value);\n        }\n    },\n    RFC1738: Format.RFC1738,\n    RFC3986: Format.RFC3986\n};\n", "'use strict';\n\nmodule.exports = function bind(fn, thisArg) {\n  return function wrap() {\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n    return fn.apply(thisArg, args);\n  };\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @returns {string} The formatted url\n */\nmodule.exports = function buildURL(url, params, paramsSerializer) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n\n  var serializedParams;\n  if (paramsSerializer) {\n    serializedParams = paramsSerializer(params);\n  } else if (utils.isURLSearchParams(params)) {\n    serializedParams = params.toString();\n  } else {\n    var parts = [];\n\n    utils.forEach(params, function serialize(val, key) {\n      if (val === null || typeof val === 'undefined') {\n        return;\n      }\n\n      if (utils.isArray(val)) {\n        key = key + '[]';\n      } else {\n        val = [val];\n      }\n\n      utils.forEach(val, function parseValue(v) {\n        if (utils.isDate(v)) {\n          v = v.toISOString();\n        } else if (utils.isObject(v)) {\n          v = JSON.stringify(v);\n        }\n        parts.push(encode(key) + '=' + encode(v));\n      });\n    });\n\n    serializedParams = parts.join('&');\n  }\n\n  if (serializedParams) {\n    var hashmarkIndex = url.indexOf('#');\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n};\n", "'use strict';\n\nmodule.exports = function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n};\n", "'use strict';\n\nvar utils = require('./utils');\nvar normalizeHeaderName = require('./helpers/normalizeHeaderName');\n\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\nfunction setContentTypeIfUnset(headers, value) {\n  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {\n    headers['Content-Type'] = value;\n  }\n}\n\nfunction getDefaultAdapter() {\n  var adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = require('./adapters/xhr');\n  } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {\n    // For node use HTTP adapter\n    adapter = require('./adapters/http');\n  }\n  return adapter;\n}\n\nvar defaults = {\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    normalizeHeaderName(headers, 'Accept');\n    normalizeHeaderName(headers, 'Content-Type');\n    if (utils.isFormData(data) ||\n      utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');\n      return data.toString();\n    }\n    if (utils.isObject(data)) {\n      setContentTypeIfUnset(headers, 'application/json;charset=utf-8');\n      return JSON.stringify(data);\n    }\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    /*eslint no-param-reassign:0*/\n    if (typeof data === 'string') {\n      try {\n        data = JSON.parse(data);\n      } catch (e) { /* Ignore */ }\n    }\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  }\n};\n\ndefaults.headers = {\n  common: {\n    'Accept': 'application/json, text/plain, */*'\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nmodule.exports = defaults;\n", "'use strict';\n\nvar utils = require('./../utils');\nvar settle = require('./../core/settle');\nvar cookies = require('./../helpers/cookies');\nvar buildURL = require('./../helpers/buildURL');\nvar buildFullPath = require('../core/buildFullPath');\nvar parseHeaders = require('./../helpers/parseHeaders');\nvar isURLSameOrigin = require('./../helpers/isURLSameOrigin');\nvar createError = require('../core/createError');\n\nmodule.exports = function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = config.headers;\n\n    if (utils.isFormData(requestData)) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    if (\n      (utils.isBlob(requestData) || utils.isFile(requestData)) &&\n      requestData.type\n    ) {\n      delete requestHeaders['Content-Type']; // Let the browser set it\n    }\n\n    var request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = unescape(encodeURIComponent(config.auth.password)) || '';\n      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);\n    }\n\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    // Listen for ready state\n    request.onreadystatechange = function handleLoad() {\n      if (!request || request.readyState !== 4) {\n        return;\n      }\n\n      // The request errored out and we didn't get a response, this will be\n      // handled by onerror instead\n      // With one exception: request that using file: protocol, most browsers\n      // will return status as 0 even though it's a successful request\n      if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n        return;\n      }\n\n      // Prepare the response\n      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;\n      var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n\n      settle(resolve, reject, response);\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(createError('Request aborted', config, 'ECONNABORTED', request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(createError('Network Error', config, null, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(createError(timeoutErrorMessage, config, 'ECONNABORTED',\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (utils.isStandardBrowserEnv()) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ?\n        cookies.read(config.xsrfCookieName) :\n        undefined;\n\n      if (xsrfValue) {\n        requestHeaders[config.xsrfHeaderName] = xsrfValue;\n      }\n    }\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders, function setRequestHeader(val, key) {\n        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {\n          // Remove Content-Type if data is undefined\n          delete requestHeaders[key];\n        } else {\n          // Otherwise add header to the request\n          request.setRequestHeader(key, val);\n        }\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (config.responseType) {\n      try {\n        request.responseType = config.responseType;\n      } catch (e) {\n        // Expected DOMException thrown by browsers not compatible XMLHttpRequest Level 2.\n        // But, this can be suppressed for 'json' type as it can be parsed by default 'transformResponse' function.\n        if (config.responseType !== 'json') {\n          throw e;\n        }\n      }\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', config.onDownloadProgress);\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', config.onUploadProgress);\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n\n        request.abort();\n        reject(cancel);\n        // Clean up request\n        request = null;\n      });\n    }\n\n    if (!requestData) {\n      requestData = null;\n    }\n\n    // Send the request\n    request.send(requestData);\n  });\n};\n", "'use strict';\n\nvar enhanceError = require('./enhanceError');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nmodule.exports = function createError(message, config, code, request, response) {\n  var error = new Error(message);\n  return enhanceError(error, config, code, request, response);\n};\n", "'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n\n  var valueFromConfig2Keys = ['url', 'method', 'data'];\n  var mergeDeepPropertiesKeys = ['headers', 'auth', 'proxy', 'params'];\n  var defaultToConfig2Keys = [\n    'baseURL', 'transformRequest', 'transformResponse', 'paramsSerializer',\n    'timeout', 'timeoutMessage', 'withCredentials', 'adapter', 'responseType', 'xsrfCookieName',\n    'xsrfHeaderName', 'onUploadProgress', 'onDownloadProgress', 'decompress',\n    'maxContentLength', 'maxBodyLength', 'maxRedirects', 'transport', 'httpAgent',\n    'httpsAgent', 'cancelToken', 'socketPath', 'responseEncoding'\n  ];\n  var directMergeKeys = ['validateStatus'];\n\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    }\n  });\n\n  utils.forEach(mergeDeepPropertiesKeys, mergeDeepProperties);\n\n  utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      config[prop] = getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n\n  utils.forEach(directMergeKeys, function merge(prop) {\n    if (prop in config2) {\n      config[prop] = getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      config[prop] = getMergedValue(undefined, config1[prop]);\n    }\n  });\n\n  var axiosKeys = valueFromConfig2Keys\n    .concat(mergeDeepPropertiesKeys)\n    .concat(defaultToConfig2Keys)\n    .concat(directMergeKeys);\n\n  var otherKeys = Object\n    .keys(config1)\n    .concat(Object.keys(config2))\n    .filter(function filterAxiosKeys(key) {\n      return axiosKeys.indexOf(key) === -1;\n    });\n\n  utils.forEach(otherKeys, mergeDeepProperties);\n\n  return config;\n};\n", "'use strict';\n\n/**\n * A `Cancel` is an object that is thrown when an operation is canceled.\n *\n * @class\n * @param {string=} message The message.\n */\nfunction Cancel(message) {\n  this.message = message;\n}\n\nCancel.prototype.toString = function toString() {\n  return 'Cancel' + (this.message ? ': ' + this.message : '');\n};\n\nCancel.prototype.__CANCEL__ = true;\n\nmodule.exports = Cancel;\n", "'use strict';\n\nvar formats = require('./formats');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar hexTable = (function () {\n    var array = [];\n    for (var i = 0; i < 256; ++i) {\n        array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n    }\n\n    return array;\n}());\n\nvar compactQueue = function compactQueue(queue) {\n    while (queue.length > 1) {\n        var item = queue.pop();\n        var obj = item.obj[item.prop];\n\n        if (isArray(obj)) {\n            var compacted = [];\n\n            for (var j = 0; j < obj.length; ++j) {\n                if (typeof obj[j] !== 'undefined') {\n                    compacted.push(obj[j]);\n                }\n            }\n\n            item.obj[item.prop] = compacted;\n        }\n    }\n};\n\nvar arrayToObject = function arrayToObject(source, options) {\n    var obj = options && options.plainObjects ? Object.create(null) : {};\n    for (var i = 0; i < source.length; ++i) {\n        if (typeof source[i] !== 'undefined') {\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\nvar merge = function merge(target, source, options) {\n    /* eslint no-param-reassign: 0 */\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object') {\n        if (isArray(target)) {\n            target.push(source);\n        } else if (target && typeof target === 'object') {\n            if ((options && (options.plainObjects || options.allowPrototypes)) || !has.call(Object.prototype, source)) {\n                target[source] = true;\n            }\n        } else {\n            return [target, source];\n        }\n\n        return target;\n    }\n\n    if (!target || typeof target !== 'object') {\n        return [target].concat(source);\n    }\n\n    var mergeTarget = target;\n    if (isArray(target) && !isArray(source)) {\n        mergeTarget = arrayToObject(target, options);\n    }\n\n    if (isArray(target) && isArray(source)) {\n        source.forEach(function (item, i) {\n            if (has.call(target, i)) {\n                var targetItem = target[i];\n                if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {\n                    target[i] = merge(targetItem, item, options);\n                } else {\n                    target.push(item);\n                }\n            } else {\n                target[i] = item;\n            }\n        });\n        return target;\n    }\n\n    return Object.keys(source).reduce(function (acc, key) {\n        var value = source[key];\n\n        if (has.call(acc, key)) {\n            acc[key] = merge(acc[key], value, options);\n        } else {\n            acc[key] = value;\n        }\n        return acc;\n    }, mergeTarget);\n};\n\nvar assign = function assignSingleSource(target, source) {\n    return Object.keys(source).reduce(function (acc, key) {\n        acc[key] = source[key];\n        return acc;\n    }, target);\n};\n\nvar decode = function (str, decoder, charset) {\n    var strWithoutPlus = str.replace(/\\+/g, ' ');\n    if (charset === 'iso-8859-1') {\n        // unescape never throws, no try...catch needed:\n        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n    }\n    // utf-8\n    try {\n        return decodeURIComponent(strWithoutPlus);\n    } catch (e) {\n        return strWithoutPlus;\n    }\n};\n\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    var string = str;\n    if (typeof str === 'symbol') {\n        string = Symbol.prototype.toString.call(str);\n    } else if (typeof str !== 'string') {\n        string = String(str);\n    }\n\n    if (charset === 'iso-8859-1') {\n        return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n            return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n        });\n    }\n\n    var out = '';\n    for (var i = 0; i < string.length; ++i) {\n        var c = string.charCodeAt(i);\n\n        if (\n            c === 0x2D // -\n            || c === 0x2E // .\n            || c === 0x5F // _\n            || c === 0x7E // ~\n            || (c >= 0x30 && c <= 0x39) // 0-9\n            || (c >= 0x41 && c <= 0x5A) // a-z\n            || (c >= 0x61 && c <= 0x7A) // A-Z\n            || (format === formats.RFC1738 && (c === 0x28 || c === 0x29)) // ( )\n        ) {\n            out += string.charAt(i);\n            continue;\n        }\n\n        if (c < 0x80) {\n            out = out + hexTable[c];\n            continue;\n        }\n\n        if (c < 0x800) {\n            out = out + (hexTable[0xC0 | (c >> 6)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        if (c < 0xD800 || c >= 0xE000) {\n            out = out + (hexTable[0xE0 | (c >> 12)] + hexTable[0x80 | ((c >> 6) & 0x3F)] + hexTable[0x80 | (c & 0x3F)]);\n            continue;\n        }\n\n        i += 1;\n        c = 0x10000 + (((c & 0x3FF) << 10) | (string.charCodeAt(i) & 0x3FF));\n        out += hexTable[0xF0 | (c >> 18)]\n            + hexTable[0x80 | ((c >> 12) & 0x3F)]\n            + hexTable[0x80 | ((c >> 6) & 0x3F)]\n            + hexTable[0x80 | (c & 0x3F)];\n    }\n\n    return out;\n};\n\nvar compact = function compact(value) {\n    var queue = [{ obj: { o: value }, prop: 'o' }];\n    var refs = [];\n\n    for (var i = 0; i < queue.length; ++i) {\n        var item = queue[i];\n        var obj = item.obj[item.prop];\n\n        var keys = Object.keys(obj);\n        for (var j = 0; j < keys.length; ++j) {\n            var key = keys[j];\n            var val = obj[key];\n            if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {\n                queue.push({ obj: obj, prop: key });\n                refs.push(val);\n            }\n        }\n    }\n\n    compactQueue(queue);\n\n    return value;\n};\n\nvar isRegExp = function isRegExp(obj) {\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\nvar isBuffer = function isBuffer(obj) {\n    if (!obj || typeof obj !== 'object') {\n        return false;\n    }\n\n    return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\n\nvar combine = function combine(a, b) {\n    return [].concat(a, b);\n};\n\nvar maybeMap = function maybeMap(val, fn) {\n    if (isArray(val)) {\n        var mapped = [];\n        for (var i = 0; i < val.length; i += 1) {\n            mapped.push(fn(val[i]));\n        }\n        return mapped;\n    }\n    return fn(val);\n};\n\nmodule.exports = {\n    arrayToObject: arrayToObject,\n    assign: assign,\n    combine: combine,\n    compact: compact,\n    decode: decode,\n    encode: encode,\n    isBuffer: isBuffer,\n    isRegExp: isRegExp,\n    maybeMap: maybeMap,\n    merge: merge\n};\n", "/*!\n * Cropper.js v1.5.12\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2021-06-12T08:00:17.411Z\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.Cropper = factory());\n}(this, (function () { 'use strict';\n\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n\n      keys.push.apply(keys, symbols);\n    }\n\n    return keys;\n  }\n\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n\n    return target;\n  }\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n\n    return _typeof(obj);\n  }\n\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n  }\n\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n\n    return obj;\n  }\n\n  function _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n  }\n\n  function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n  }\n\n  function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  var IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\n  var WINDOW = IS_BROWSER ? window : {};\n  var IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\n  var HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\n  var NAMESPACE = 'cropper'; // Actions\n\n  var ACTION_ALL = 'all';\n  var ACTION_CROP = 'crop';\n  var ACTION_MOVE = 'move';\n  var ACTION_ZOOM = 'zoom';\n  var ACTION_EAST = 'e';\n  var ACTION_WEST = 'w';\n  var ACTION_SOUTH = 's';\n  var ACTION_NORTH = 'n';\n  var ACTION_NORTH_EAST = 'ne';\n  var ACTION_NORTH_WEST = 'nw';\n  var ACTION_SOUTH_EAST = 'se';\n  var ACTION_SOUTH_WEST = 'sw'; // Classes\n\n  var CLASS_CROP = \"\".concat(NAMESPACE, \"-crop\");\n  var CLASS_DISABLED = \"\".concat(NAMESPACE, \"-disabled\");\n  var CLASS_HIDDEN = \"\".concat(NAMESPACE, \"-hidden\");\n  var CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\n  var CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\n  var CLASS_MODAL = \"\".concat(NAMESPACE, \"-modal\");\n  var CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\"); // Data keys\n\n  var DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\n  var DATA_PREVIEW = \"\".concat(NAMESPACE, \"Preview\"); // Drag modes\n\n  var DRAG_MODE_CROP = 'crop';\n  var DRAG_MODE_MOVE = 'move';\n  var DRAG_MODE_NONE = 'none'; // Events\n\n  var EVENT_CROP = 'crop';\n  var EVENT_CROP_END = 'cropend';\n  var EVENT_CROP_MOVE = 'cropmove';\n  var EVENT_CROP_START = 'cropstart';\n  var EVENT_DBLCLICK = 'dblclick';\n  var EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\n  var EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\n  var EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\n  var EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\n  var EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\n  var EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\n  var EVENT_READY = 'ready';\n  var EVENT_RESIZE = 'resize';\n  var EVENT_WHEEL = 'wheel';\n  var EVENT_ZOOM = 'zoom'; // Mime types\n\n  var MIME_TYPE_JPEG = 'image/jpeg'; // RegExps\n\n  var REGEXP_ACTIONS = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;\n  var REGEXP_DATA_URL = /^data:/;\n  var REGEXP_DATA_URL_JPEG = /^data:image\\/jpeg;base64,/;\n  var REGEXP_TAG_NAME = /^img|canvas$/i; // Misc\n  // Inspired by the default width and height of a canvas element.\n\n  var MIN_CONTAINER_WIDTH = 200;\n  var MIN_CONTAINER_HEIGHT = 100;\n\n  var DEFAULTS = {\n    // Define the view mode of the cropper\n    viewMode: 0,\n    // 0, 1, 2, 3\n    // Define the dragging mode of the cropper\n    dragMode: DRAG_MODE_CROP,\n    // 'crop', 'move' or 'none'\n    // Define the initial aspect ratio of the crop box\n    initialAspectRatio: NaN,\n    // Define the aspect ratio of the crop box\n    aspectRatio: NaN,\n    // An object with the previous cropping result data\n    data: null,\n    // A selector for adding extra containers to preview\n    preview: '',\n    // Re-render the cropper when resize the window\n    responsive: true,\n    // Restore the cropped area after resize the window\n    restore: true,\n    // Check if the current image is a cross-origin image\n    checkCrossOrigin: true,\n    // Check the current image's Exif Orientation information\n    checkOrientation: true,\n    // Show the black modal\n    modal: true,\n    // Show the dashed lines for guiding\n    guides: true,\n    // Show the center indicator for guiding\n    center: true,\n    // Show the white modal to highlight the crop box\n    highlight: true,\n    // Show the grid background\n    background: true,\n    // Enable to crop the image automatically when initialize\n    autoCrop: true,\n    // Define the percentage of automatic cropping area when initializes\n    autoCropArea: 0.8,\n    // Enable to move the image\n    movable: true,\n    // Enable to rotate the image\n    rotatable: true,\n    // Enable to scale the image\n    scalable: true,\n    // Enable to zoom the image\n    zoomable: true,\n    // Enable to zoom the image by dragging touch\n    zoomOnTouch: true,\n    // Enable to zoom the image by wheeling mouse\n    zoomOnWheel: true,\n    // Define zoom ratio when zoom the image by wheeling mouse\n    wheelZoomRatio: 0.1,\n    // Enable to move the crop box\n    cropBoxMovable: true,\n    // Enable to resize the crop box\n    cropBoxResizable: true,\n    // Toggle drag mode between \"crop\" and \"move\" when click twice on the cropper\n    toggleDragModeOnDblclick: true,\n    // Size limitation\n    minCanvasWidth: 0,\n    minCanvasHeight: 0,\n    minCropBoxWidth: 0,\n    minCropBoxHeight: 0,\n    minContainerWidth: MIN_CONTAINER_WIDTH,\n    minContainerHeight: MIN_CONTAINER_HEIGHT,\n    // Shortcuts of events\n    ready: null,\n    cropstart: null,\n    cropmove: null,\n    cropend: null,\n    crop: null,\n    zoom: null\n  };\n\n  var TEMPLATE = '<div class=\"cropper-container\" touch-action=\"none\">' + '<div class=\"cropper-wrap-box\">' + '<div class=\"cropper-canvas\"></div>' + '</div>' + '<div class=\"cropper-drag-box\"></div>' + '<div class=\"cropper-crop-box\">' + '<span class=\"cropper-view-box\"></span>' + '<span class=\"cropper-dashed dashed-h\"></span>' + '<span class=\"cropper-dashed dashed-v\"></span>' + '<span class=\"cropper-center\"></span>' + '<span class=\"cropper-face\"></span>' + '<span class=\"cropper-line line-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-line line-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-line line-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-line line-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-point point-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-point point-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-point point-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-ne\" data-cropper-action=\"ne\"></span>' + '<span class=\"cropper-point point-nw\" data-cropper-action=\"nw\"></span>' + '<span class=\"cropper-point point-sw\" data-cropper-action=\"sw\"></span>' + '<span class=\"cropper-point point-se\" data-cropper-action=\"se\"></span>' + '</div>' + '</div>';\n\n  /**\n   * Check if the given value is not a number.\n   */\n\n  var isNaN = Number.isNaN || WINDOW.isNaN;\n  /**\n   * Check if the given value is a number.\n   * @param {*} value - The value to check.\n   * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n   */\n\n  function isNumber(value) {\n    return typeof value === 'number' && !isNaN(value);\n  }\n  /**\n   * Check if the given value is a positive number.\n   * @param {*} value - The value to check.\n   * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.\n   */\n\n  var isPositiveNumber = function isPositiveNumber(value) {\n    return value > 0 && value < Infinity;\n  };\n  /**\n   * Check if the given value is undefined.\n   * @param {*} value - The value to check.\n   * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n   */\n\n  function isUndefined(value) {\n    return typeof value === 'undefined';\n  }\n  /**\n   * Check if the given value is an object.\n   * @param {*} value - The value to check.\n   * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n   */\n\n  function isObject(value) {\n    return _typeof(value) === 'object' && value !== null;\n  }\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  /**\n   * Check if the given value is a plain object.\n   * @param {*} value - The value to check.\n   * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n   */\n\n  function isPlainObject(value) {\n    if (!isObject(value)) {\n      return false;\n    }\n\n    try {\n      var _constructor = value.constructor;\n      var prototype = _constructor.prototype;\n      return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n    } catch (error) {\n      return false;\n    }\n  }\n  /**\n   * Check if the given value is a function.\n   * @param {*} value - The value to check.\n   * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n   */\n\n  function isFunction(value) {\n    return typeof value === 'function';\n  }\n  var slice = Array.prototype.slice;\n  /**\n   * Convert array-like or iterable object to an array.\n   * @param {*} value - The value to convert.\n   * @returns {Array} Returns a new array.\n   */\n\n  function toArray(value) {\n    return Array.from ? Array.from(value) : slice.call(value);\n  }\n  /**\n   * Iterate the given data.\n   * @param {*} data - The data to iterate.\n   * @param {Function} callback - The process function for each element.\n   * @returns {*} The original data.\n   */\n\n  function forEach(data, callback) {\n    if (data && isFunction(callback)) {\n      if (Array.isArray(data) || isNumber(data.length)\n      /* array-like */\n      ) {\n          toArray(data).forEach(function (value, key) {\n            callback.call(data, value, key, data);\n          });\n        } else if (isObject(data)) {\n        Object.keys(data).forEach(function (key) {\n          callback.call(data, data[key], key, data);\n        });\n      }\n    }\n\n    return data;\n  }\n  /**\n   * Extend the given object.\n   * @param {*} target - The target object to extend.\n   * @param {*} args - The rest objects for merging to the target object.\n   * @returns {Object} The extended object.\n   */\n\n  var assign = Object.assign || function assign(target) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (isObject(target) && args.length > 0) {\n      args.forEach(function (arg) {\n        if (isObject(arg)) {\n          Object.keys(arg).forEach(function (key) {\n            target[key] = arg[key];\n          });\n        }\n      });\n    }\n\n    return target;\n  };\n  var REGEXP_DECIMALS = /\\.\\d*(?:0|9){12}\\d*$/;\n  /**\n   * Normalize decimal number.\n   * Check out {@link https://0.30000000000000004.com/}\n   * @param {number} value - The value to normalize.\n   * @param {number} [times=100000000000] - The times for normalizing.\n   * @returns {number} Returns the normalized number.\n   */\n\n  function normalizeDecimalNumber(value) {\n    var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100000000000;\n    return REGEXP_DECIMALS.test(value) ? Math.round(value * times) / times : value;\n  }\n  var REGEXP_SUFFIX = /^width|height|left|top|marginLeft|marginTop$/;\n  /**\n   * Apply styles to the given element.\n   * @param {Element} element - The target element.\n   * @param {Object} styles - The styles for applying.\n   */\n\n  function setStyle(element, styles) {\n    var style = element.style;\n    forEach(styles, function (value, property) {\n      if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n        value = \"\".concat(value, \"px\");\n      }\n\n      style[property] = value;\n    });\n  }\n  /**\n   * Check if the given element has a special class.\n   * @param {Element} element - The element to check.\n   * @param {string} value - The class to search.\n   * @returns {boolean} Returns `true` if the special class was found.\n   */\n\n  function hasClass(element, value) {\n    return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n  }\n  /**\n   * Add classes to the given element.\n   * @param {Element} element - The target element.\n   * @param {string} value - The classes to be added.\n   */\n\n  function addClass(element, value) {\n    if (!value) {\n      return;\n    }\n\n    if (isNumber(element.length)) {\n      forEach(element, function (elem) {\n        addClass(elem, value);\n      });\n      return;\n    }\n\n    if (element.classList) {\n      element.classList.add(value);\n      return;\n    }\n\n    var className = element.className.trim();\n\n    if (!className) {\n      element.className = value;\n    } else if (className.indexOf(value) < 0) {\n      element.className = \"\".concat(className, \" \").concat(value);\n    }\n  }\n  /**\n   * Remove classes from the given element.\n   * @param {Element} element - The target element.\n   * @param {string} value - The classes to be removed.\n   */\n\n  function removeClass(element, value) {\n    if (!value) {\n      return;\n    }\n\n    if (isNumber(element.length)) {\n      forEach(element, function (elem) {\n        removeClass(elem, value);\n      });\n      return;\n    }\n\n    if (element.classList) {\n      element.classList.remove(value);\n      return;\n    }\n\n    if (element.className.indexOf(value) >= 0) {\n      element.className = element.className.replace(value, '');\n    }\n  }\n  /**\n   * Add or remove classes from the given element.\n   * @param {Element} element - The target element.\n   * @param {string} value - The classes to be toggled.\n   * @param {boolean} added - Add only.\n   */\n\n  function toggleClass(element, value, added) {\n    if (!value) {\n      return;\n    }\n\n    if (isNumber(element.length)) {\n      forEach(element, function (elem) {\n        toggleClass(elem, value, added);\n      });\n      return;\n    } // IE10-11 doesn't support the second parameter of `classList.toggle`\n\n\n    if (added) {\n      addClass(element, value);\n    } else {\n      removeClass(element, value);\n    }\n  }\n  var REGEXP_CAMEL_CASE = /([a-z\\d])([A-Z])/g;\n  /**\n   * Transform the given string from camelCase to kebab-case\n   * @param {string} value - The value to transform.\n   * @returns {string} The transformed value.\n   */\n\n  function toParamCase(value) {\n    return value.replace(REGEXP_CAMEL_CASE, '$1-$2').toLowerCase();\n  }\n  /**\n   * Get data from the given element.\n   * @param {Element} element - The target element.\n   * @param {string} name - The data key to get.\n   * @returns {string} The data value.\n   */\n\n  function getData(element, name) {\n    if (isObject(element[name])) {\n      return element[name];\n    }\n\n    if (element.dataset) {\n      return element.dataset[name];\n    }\n\n    return element.getAttribute(\"data-\".concat(toParamCase(name)));\n  }\n  /**\n   * Set data to the given element.\n   * @param {Element} element - The target element.\n   * @param {string} name - The data key to set.\n   * @param {string} data - The data value.\n   */\n\n  function setData(element, name, data) {\n    if (isObject(data)) {\n      element[name] = data;\n    } else if (element.dataset) {\n      element.dataset[name] = data;\n    } else {\n      element.setAttribute(\"data-\".concat(toParamCase(name)), data);\n    }\n  }\n  /**\n   * Remove data from the given element.\n   * @param {Element} element - The target element.\n   * @param {string} name - The data key to remove.\n   */\n\n  function removeData(element, name) {\n    if (isObject(element[name])) {\n      try {\n        delete element[name];\n      } catch (error) {\n        element[name] = undefined;\n      }\n    } else if (element.dataset) {\n      // #128 Safari not allows to delete dataset property\n      try {\n        delete element.dataset[name];\n      } catch (error) {\n        element.dataset[name] = undefined;\n      }\n    } else {\n      element.removeAttribute(\"data-\".concat(toParamCase(name)));\n    }\n  }\n  var REGEXP_SPACES = /\\s\\s*/;\n\n  var onceSupported = function () {\n    var supported = false;\n\n    if (IS_BROWSER) {\n      var once = false;\n\n      var listener = function listener() {};\n\n      var options = Object.defineProperty({}, 'once', {\n        get: function get() {\n          supported = true;\n          return once;\n        },\n\n        /**\n         * This setter can fix a `TypeError` in strict mode\n         * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n         * @param {boolean} value - The value to set\n         */\n        set: function set(value) {\n          once = value;\n        }\n      });\n      WINDOW.addEventListener('test', listener, options);\n      WINDOW.removeEventListener('test', listener, options);\n    }\n\n    return supported;\n  }();\n  /**\n   * Remove event listener from the target element.\n   * @param {Element} element - The event target.\n   * @param {string} type - The event type(s).\n   * @param {Function} listener - The event listener.\n   * @param {Object} options - The event options.\n   */\n\n\n  function removeListener(element, type, listener) {\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    var handler = listener;\n    type.trim().split(REGEXP_SPACES).forEach(function (event) {\n      if (!onceSupported) {\n        var listeners = element.listeners;\n\n        if (listeners && listeners[event] && listeners[event][listener]) {\n          handler = listeners[event][listener];\n          delete listeners[event][listener];\n\n          if (Object.keys(listeners[event]).length === 0) {\n            delete listeners[event];\n          }\n\n          if (Object.keys(listeners).length === 0) {\n            delete element.listeners;\n          }\n        }\n      }\n\n      element.removeEventListener(event, handler, options);\n    });\n  }\n  /**\n   * Add event listener to the target element.\n   * @param {Element} element - The event target.\n   * @param {string} type - The event type(s).\n   * @param {Function} listener - The event listener.\n   * @param {Object} options - The event options.\n   */\n\n  function addListener(element, type, listener) {\n    var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    var _handler = listener;\n    type.trim().split(REGEXP_SPACES).forEach(function (event) {\n      if (options.once && !onceSupported) {\n        var _element$listeners = element.listeners,\n            listeners = _element$listeners === void 0 ? {} : _element$listeners;\n\n        _handler = function handler() {\n          delete listeners[event][listener];\n          element.removeEventListener(event, _handler, options);\n\n          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n            args[_key2] = arguments[_key2];\n          }\n\n          listener.apply(element, args);\n        };\n\n        if (!listeners[event]) {\n          listeners[event] = {};\n        }\n\n        if (listeners[event][listener]) {\n          element.removeEventListener(event, listeners[event][listener], options);\n        }\n\n        listeners[event][listener] = _handler;\n        element.listeners = listeners;\n      }\n\n      element.addEventListener(event, _handler, options);\n    });\n  }\n  /**\n   * Dispatch event on the target element.\n   * @param {Element} element - The event target.\n   * @param {string} type - The event type(s).\n   * @param {Object} data - The additional event data.\n   * @returns {boolean} Indicate if the event is default prevented or not.\n   */\n\n  function dispatchEvent(element, type, data) {\n    var event; // Event and CustomEvent on IE9-11 are global objects, not constructors\n\n    if (isFunction(Event) && isFunction(CustomEvent)) {\n      event = new CustomEvent(type, {\n        detail: data,\n        bubbles: true,\n        cancelable: true\n      });\n    } else {\n      event = document.createEvent('CustomEvent');\n      event.initCustomEvent(type, true, true, data);\n    }\n\n    return element.dispatchEvent(event);\n  }\n  /**\n   * Get the offset base on the document.\n   * @param {Element} element - The target element.\n   * @returns {Object} The offset data.\n   */\n\n  function getOffset(element) {\n    var box = element.getBoundingClientRect();\n    return {\n      left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n      top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n    };\n  }\n  var location = WINDOW.location;\n  var REGEXP_ORIGINS = /^(\\w+:)\\/\\/([^:/?#]*):?(\\d*)/i;\n  /**\n   * Check if the given URL is a cross origin URL.\n   * @param {string} url - The target URL.\n   * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.\n   */\n\n  function isCrossOriginURL(url) {\n    var parts = url.match(REGEXP_ORIGINS);\n    return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);\n  }\n  /**\n   * Add timestamp to the given URL.\n   * @param {string} url - The target URL.\n   * @returns {string} The result URL.\n   */\n\n  function addTimestamp(url) {\n    var timestamp = \"timestamp=\".concat(new Date().getTime());\n    return url + (url.indexOf('?') === -1 ? '?' : '&') + timestamp;\n  }\n  /**\n   * Get transforms base on the given object.\n   * @param {Object} obj - The target object.\n   * @returns {string} A string contains transform values.\n   */\n\n  function getTransforms(_ref) {\n    var rotate = _ref.rotate,\n        scaleX = _ref.scaleX,\n        scaleY = _ref.scaleY,\n        translateX = _ref.translateX,\n        translateY = _ref.translateY;\n    var values = [];\n\n    if (isNumber(translateX) && translateX !== 0) {\n      values.push(\"translateX(\".concat(translateX, \"px)\"));\n    }\n\n    if (isNumber(translateY) && translateY !== 0) {\n      values.push(\"translateY(\".concat(translateY, \"px)\"));\n    } // Rotate should come first before scale to match orientation transform\n\n\n    if (isNumber(rotate) && rotate !== 0) {\n      values.push(\"rotate(\".concat(rotate, \"deg)\"));\n    }\n\n    if (isNumber(scaleX) && scaleX !== 1) {\n      values.push(\"scaleX(\".concat(scaleX, \")\"));\n    }\n\n    if (isNumber(scaleY) && scaleY !== 1) {\n      values.push(\"scaleY(\".concat(scaleY, \")\"));\n    }\n\n    var transform = values.length ? values.join(' ') : 'none';\n    return {\n      WebkitTransform: transform,\n      msTransform: transform,\n      transform: transform\n    };\n  }\n  /**\n   * Get the max ratio of a group of pointers.\n   * @param {string} pointers - The target pointers.\n   * @returns {number} The result ratio.\n   */\n\n  function getMaxZoomRatio(pointers) {\n    var pointers2 = _objectSpread2({}, pointers);\n\n    var maxRatio = 0;\n    forEach(pointers, function (pointer, pointerId) {\n      delete pointers2[pointerId];\n      forEach(pointers2, function (pointer2) {\n        var x1 = Math.abs(pointer.startX - pointer2.startX);\n        var y1 = Math.abs(pointer.startY - pointer2.startY);\n        var x2 = Math.abs(pointer.endX - pointer2.endX);\n        var y2 = Math.abs(pointer.endY - pointer2.endY);\n        var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n        var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n        var ratio = (z2 - z1) / z1;\n\n        if (Math.abs(ratio) > Math.abs(maxRatio)) {\n          maxRatio = ratio;\n        }\n      });\n    });\n    return maxRatio;\n  }\n  /**\n   * Get a pointer from an event object.\n   * @param {Object} event - The target event object.\n   * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n   * @returns {Object} The result pointer contains start and/or end point coordinates.\n   */\n\n  function getPointer(_ref2, endOnly) {\n    var pageX = _ref2.pageX,\n        pageY = _ref2.pageY;\n    var end = {\n      endX: pageX,\n      endY: pageY\n    };\n    return endOnly ? end : _objectSpread2({\n      startX: pageX,\n      startY: pageY\n    }, end);\n  }\n  /**\n   * Get the center point coordinate of a group of pointers.\n   * @param {Object} pointers - The target pointers.\n   * @returns {Object} The center point coordinate.\n   */\n\n  function getPointersCenter(pointers) {\n    var pageX = 0;\n    var pageY = 0;\n    var count = 0;\n    forEach(pointers, function (_ref3) {\n      var startX = _ref3.startX,\n          startY = _ref3.startY;\n      pageX += startX;\n      pageY += startY;\n      count += 1;\n    });\n    pageX /= count;\n    pageY /= count;\n    return {\n      pageX: pageX,\n      pageY: pageY\n    };\n  }\n  /**\n   * Get the max sizes in a rectangle under the given aspect ratio.\n   * @param {Object} data - The original sizes.\n   * @param {string} [type='contain'] - The adjust type.\n   * @returns {Object} The result sizes.\n   */\n\n  function getAdjustedSizes(_ref4) // or 'cover'\n  {\n    var aspectRatio = _ref4.aspectRatio,\n        height = _ref4.height,\n        width = _ref4.width;\n    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'contain';\n    var isValidWidth = isPositiveNumber(width);\n    var isValidHeight = isPositiveNumber(height);\n\n    if (isValidWidth && isValidHeight) {\n      var adjustedWidth = height * aspectRatio;\n\n      if (type === 'contain' && adjustedWidth > width || type === 'cover' && adjustedWidth < width) {\n        height = width / aspectRatio;\n      } else {\n        width = height * aspectRatio;\n      }\n    } else if (isValidWidth) {\n      height = width / aspectRatio;\n    } else if (isValidHeight) {\n      width = height * aspectRatio;\n    }\n\n    return {\n      width: width,\n      height: height\n    };\n  }\n  /**\n   * Get the new sizes of a rectangle after rotated.\n   * @param {Object} data - The original sizes.\n   * @returns {Object} The result sizes.\n   */\n\n  function getRotatedSizes(_ref5) {\n    var width = _ref5.width,\n        height = _ref5.height,\n        degree = _ref5.degree;\n    degree = Math.abs(degree) % 180;\n\n    if (degree === 90) {\n      return {\n        width: height,\n        height: width\n      };\n    }\n\n    var arc = degree % 90 * Math.PI / 180;\n    var sinArc = Math.sin(arc);\n    var cosArc = Math.cos(arc);\n    var newWidth = width * cosArc + height * sinArc;\n    var newHeight = width * sinArc + height * cosArc;\n    return degree > 90 ? {\n      width: newHeight,\n      height: newWidth\n    } : {\n      width: newWidth,\n      height: newHeight\n    };\n  }\n  /**\n   * Get a canvas which drew the given image.\n   * @param {HTMLImageElement} image - The image for drawing.\n   * @param {Object} imageData - The image data.\n   * @param {Object} canvasData - The canvas data.\n   * @param {Object} options - The options.\n   * @returns {HTMLCanvasElement} The result canvas.\n   */\n\n  function getSourceCanvas(image, _ref6, _ref7, _ref8) {\n    var imageAspectRatio = _ref6.aspectRatio,\n        imageNaturalWidth = _ref6.naturalWidth,\n        imageNaturalHeight = _ref6.naturalHeight,\n        _ref6$rotate = _ref6.rotate,\n        rotate = _ref6$rotate === void 0 ? 0 : _ref6$rotate,\n        _ref6$scaleX = _ref6.scaleX,\n        scaleX = _ref6$scaleX === void 0 ? 1 : _ref6$scaleX,\n        _ref6$scaleY = _ref6.scaleY,\n        scaleY = _ref6$scaleY === void 0 ? 1 : _ref6$scaleY;\n    var aspectRatio = _ref7.aspectRatio,\n        naturalWidth = _ref7.naturalWidth,\n        naturalHeight = _ref7.naturalHeight;\n    var _ref8$fillColor = _ref8.fillColor,\n        fillColor = _ref8$fillColor === void 0 ? 'transparent' : _ref8$fillColor,\n        _ref8$imageSmoothingE = _ref8.imageSmoothingEnabled,\n        imageSmoothingEnabled = _ref8$imageSmoothingE === void 0 ? true : _ref8$imageSmoothingE,\n        _ref8$imageSmoothingQ = _ref8.imageSmoothingQuality,\n        imageSmoothingQuality = _ref8$imageSmoothingQ === void 0 ? 'low' : _ref8$imageSmoothingQ,\n        _ref8$maxWidth = _ref8.maxWidth,\n        maxWidth = _ref8$maxWidth === void 0 ? Infinity : _ref8$maxWidth,\n        _ref8$maxHeight = _ref8.maxHeight,\n        maxHeight = _ref8$maxHeight === void 0 ? Infinity : _ref8$maxHeight,\n        _ref8$minWidth = _ref8.minWidth,\n        minWidth = _ref8$minWidth === void 0 ? 0 : _ref8$minWidth,\n        _ref8$minHeight = _ref8.minHeight,\n        minHeight = _ref8$minHeight === void 0 ? 0 : _ref8$minHeight;\n    var canvas = document.createElement('canvas');\n    var context = canvas.getContext('2d');\n    var maxSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: maxWidth,\n      height: maxHeight\n    });\n    var minSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: minWidth,\n      height: minHeight\n    }, 'cover');\n    var width = Math.min(maxSizes.width, Math.max(minSizes.width, naturalWidth));\n    var height = Math.min(maxSizes.height, Math.max(minSizes.height, naturalHeight)); // Note: should always use image's natural sizes for drawing as\n    // imageData.naturalWidth === canvasData.naturalHeight when rotate % 180 === 90\n\n    var destMaxSizes = getAdjustedSizes({\n      aspectRatio: imageAspectRatio,\n      width: maxWidth,\n      height: maxHeight\n    });\n    var destMinSizes = getAdjustedSizes({\n      aspectRatio: imageAspectRatio,\n      width: minWidth,\n      height: minHeight\n    }, 'cover');\n    var destWidth = Math.min(destMaxSizes.width, Math.max(destMinSizes.width, imageNaturalWidth));\n    var destHeight = Math.min(destMaxSizes.height, Math.max(destMinSizes.height, imageNaturalHeight));\n    var params = [-destWidth / 2, -destHeight / 2, destWidth, destHeight];\n    canvas.width = normalizeDecimalNumber(width);\n    canvas.height = normalizeDecimalNumber(height);\n    context.fillStyle = fillColor;\n    context.fillRect(0, 0, width, height);\n    context.save();\n    context.translate(width / 2, height / 2);\n    context.rotate(rotate * Math.PI / 180);\n    context.scale(scaleX, scaleY);\n    context.imageSmoothingEnabled = imageSmoothingEnabled;\n    context.imageSmoothingQuality = imageSmoothingQuality;\n    context.drawImage.apply(context, [image].concat(_toConsumableArray(params.map(function (param) {\n      return Math.floor(normalizeDecimalNumber(param));\n    }))));\n    context.restore();\n    return canvas;\n  }\n  var fromCharCode = String.fromCharCode;\n  /**\n   * Get string from char code in data view.\n   * @param {DataView} dataView - The data view for read.\n   * @param {number} start - The start index.\n   * @param {number} length - The read length.\n   * @returns {string} The read result.\n   */\n\n  function getStringFromCharCode(dataView, start, length) {\n    var str = '';\n    length += start;\n\n    for (var i = start; i < length; i += 1) {\n      str += fromCharCode(dataView.getUint8(i));\n    }\n\n    return str;\n  }\n  var REGEXP_DATA_URL_HEAD = /^data:.*,/;\n  /**\n   * Transform Data URL to array buffer.\n   * @param {string} dataURL - The Data URL to transform.\n   * @returns {ArrayBuffer} The result array buffer.\n   */\n\n  function dataURLToArrayBuffer(dataURL) {\n    var base64 = dataURL.replace(REGEXP_DATA_URL_HEAD, '');\n    var binary = atob(base64);\n    var arrayBuffer = new ArrayBuffer(binary.length);\n    var uint8 = new Uint8Array(arrayBuffer);\n    forEach(uint8, function (value, i) {\n      uint8[i] = binary.charCodeAt(i);\n    });\n    return arrayBuffer;\n  }\n  /**\n   * Transform array buffer to Data URL.\n   * @param {ArrayBuffer} arrayBuffer - The array buffer to transform.\n   * @param {string} mimeType - The mime type of the Data URL.\n   * @returns {string} The result Data URL.\n   */\n\n  function arrayBufferToDataURL(arrayBuffer, mimeType) {\n    var chunks = []; // Chunk Typed Array for better performance (#435)\n\n    var chunkSize = 8192;\n    var uint8 = new Uint8Array(arrayBuffer);\n\n    while (uint8.length > 0) {\n      // XXX: Babel's `toConsumableArray` helper will throw error in IE or Safari 9\n      // eslint-disable-next-line prefer-spread\n      chunks.push(fromCharCode.apply(null, toArray(uint8.subarray(0, chunkSize))));\n      uint8 = uint8.subarray(chunkSize);\n    }\n\n    return \"data:\".concat(mimeType, \";base64,\").concat(btoa(chunks.join('')));\n  }\n  /**\n   * Get orientation value from given array buffer.\n   * @param {ArrayBuffer} arrayBuffer - The array buffer to read.\n   * @returns {number} The read orientation value.\n   */\n\n  function resetAndGetOrientation(arrayBuffer) {\n    var dataView = new DataView(arrayBuffer);\n    var orientation; // Ignores range error when the image does not have correct Exif information\n\n    try {\n      var littleEndian;\n      var app1Start;\n      var ifdStart; // Only handle JPEG image (start by 0xFFD8)\n\n      if (dataView.getUint8(0) === 0xFF && dataView.getUint8(1) === 0xD8) {\n        var length = dataView.byteLength;\n        var offset = 2;\n\n        while (offset + 1 < length) {\n          if (dataView.getUint8(offset) === 0xFF && dataView.getUint8(offset + 1) === 0xE1) {\n            app1Start = offset;\n            break;\n          }\n\n          offset += 1;\n        }\n      }\n\n      if (app1Start) {\n        var exifIDCode = app1Start + 4;\n        var tiffOffset = app1Start + 10;\n\n        if (getStringFromCharCode(dataView, exifIDCode, 4) === 'Exif') {\n          var endianness = dataView.getUint16(tiffOffset);\n          littleEndian = endianness === 0x4949;\n\n          if (littleEndian || endianness === 0x4D4D\n          /* bigEndian */\n          ) {\n              if (dataView.getUint16(tiffOffset + 2, littleEndian) === 0x002A) {\n                var firstIFDOffset = dataView.getUint32(tiffOffset + 4, littleEndian);\n\n                if (firstIFDOffset >= 0x00000008) {\n                  ifdStart = tiffOffset + firstIFDOffset;\n                }\n              }\n            }\n        }\n      }\n\n      if (ifdStart) {\n        var _length = dataView.getUint16(ifdStart, littleEndian);\n\n        var _offset;\n\n        var i;\n\n        for (i = 0; i < _length; i += 1) {\n          _offset = ifdStart + i * 12 + 2;\n\n          if (dataView.getUint16(_offset, littleEndian) === 0x0112\n          /* Orientation */\n          ) {\n              // 8 is the offset of the current tag's value\n              _offset += 8; // Get the original orientation value\n\n              orientation = dataView.getUint16(_offset, littleEndian); // Override the orientation with its default value\n\n              dataView.setUint16(_offset, 1, littleEndian);\n              break;\n            }\n        }\n      }\n    } catch (error) {\n      orientation = 1;\n    }\n\n    return orientation;\n  }\n  /**\n   * Parse Exif Orientation value.\n   * @param {number} orientation - The orientation to parse.\n   * @returns {Object} The parsed result.\n   */\n\n  function parseOrientation(orientation) {\n    var rotate = 0;\n    var scaleX = 1;\n    var scaleY = 1;\n\n    switch (orientation) {\n      // Flip horizontal\n      case 2:\n        scaleX = -1;\n        break;\n      // Rotate left 180°\n\n      case 3:\n        rotate = -180;\n        break;\n      // Flip vertical\n\n      case 4:\n        scaleY = -1;\n        break;\n      // Flip vertical and rotate right 90°\n\n      case 5:\n        rotate = 90;\n        scaleY = -1;\n        break;\n      // Rotate right 90°\n\n      case 6:\n        rotate = 90;\n        break;\n      // Flip horizontal and rotate right 90°\n\n      case 7:\n        rotate = 90;\n        scaleX = -1;\n        break;\n      // Rotate left 90°\n\n      case 8:\n        rotate = -90;\n        break;\n    }\n\n    return {\n      rotate: rotate,\n      scaleX: scaleX,\n      scaleY: scaleY\n    };\n  }\n\n  var render = {\n    render: function render() {\n      this.initContainer();\n      this.initCanvas();\n      this.initCropBox();\n      this.renderCanvas();\n\n      if (this.cropped) {\n        this.renderCropBox();\n      }\n    },\n    initContainer: function initContainer() {\n      var element = this.element,\n          options = this.options,\n          container = this.container,\n          cropper = this.cropper;\n      var minWidth = Number(options.minContainerWidth);\n      var minHeight = Number(options.minContainerHeight);\n      addClass(cropper, CLASS_HIDDEN);\n      removeClass(element, CLASS_HIDDEN);\n      var containerData = {\n        width: Math.max(container.offsetWidth, minWidth >= 0 ? minWidth : MIN_CONTAINER_WIDTH),\n        height: Math.max(container.offsetHeight, minHeight >= 0 ? minHeight : MIN_CONTAINER_HEIGHT)\n      };\n      this.containerData = containerData;\n      setStyle(cropper, {\n        width: containerData.width,\n        height: containerData.height\n      });\n      addClass(element, CLASS_HIDDEN);\n      removeClass(cropper, CLASS_HIDDEN);\n    },\n    // Canvas (image wrapper)\n    initCanvas: function initCanvas() {\n      var containerData = this.containerData,\n          imageData = this.imageData;\n      var viewMode = this.options.viewMode;\n      var rotated = Math.abs(imageData.rotate) % 180 === 90;\n      var naturalWidth = rotated ? imageData.naturalHeight : imageData.naturalWidth;\n      var naturalHeight = rotated ? imageData.naturalWidth : imageData.naturalHeight;\n      var aspectRatio = naturalWidth / naturalHeight;\n      var canvasWidth = containerData.width;\n      var canvasHeight = containerData.height;\n\n      if (containerData.height * aspectRatio > containerData.width) {\n        if (viewMode === 3) {\n          canvasWidth = containerData.height * aspectRatio;\n        } else {\n          canvasHeight = containerData.width / aspectRatio;\n        }\n      } else if (viewMode === 3) {\n        canvasHeight = containerData.width / aspectRatio;\n      } else {\n        canvasWidth = containerData.height * aspectRatio;\n      }\n\n      var canvasData = {\n        aspectRatio: aspectRatio,\n        naturalWidth: naturalWidth,\n        naturalHeight: naturalHeight,\n        width: canvasWidth,\n        height: canvasHeight\n      };\n      this.canvasData = canvasData;\n      this.limited = viewMode === 1 || viewMode === 2;\n      this.limitCanvas(true, true);\n      canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n      canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n      canvasData.left = (containerData.width - canvasData.width) / 2;\n      canvasData.top = (containerData.height - canvasData.height) / 2;\n      canvasData.oldLeft = canvasData.left;\n      canvasData.oldTop = canvasData.top;\n      this.initialCanvasData = assign({}, canvasData);\n    },\n    limitCanvas: function limitCanvas(sizeLimited, positionLimited) {\n      var options = this.options,\n          containerData = this.containerData,\n          canvasData = this.canvasData,\n          cropBoxData = this.cropBoxData;\n      var viewMode = options.viewMode;\n      var aspectRatio = canvasData.aspectRatio;\n      var cropped = this.cropped && cropBoxData;\n\n      if (sizeLimited) {\n        var minCanvasWidth = Number(options.minCanvasWidth) || 0;\n        var minCanvasHeight = Number(options.minCanvasHeight) || 0;\n\n        if (viewMode > 1) {\n          minCanvasWidth = Math.max(minCanvasWidth, containerData.width);\n          minCanvasHeight = Math.max(minCanvasHeight, containerData.height);\n\n          if (viewMode === 3) {\n            if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n              minCanvasWidth = minCanvasHeight * aspectRatio;\n            } else {\n              minCanvasHeight = minCanvasWidth / aspectRatio;\n            }\n          }\n        } else if (viewMode > 0) {\n          if (minCanvasWidth) {\n            minCanvasWidth = Math.max(minCanvasWidth, cropped ? cropBoxData.width : 0);\n          } else if (minCanvasHeight) {\n            minCanvasHeight = Math.max(minCanvasHeight, cropped ? cropBoxData.height : 0);\n          } else if (cropped) {\n            minCanvasWidth = cropBoxData.width;\n            minCanvasHeight = cropBoxData.height;\n\n            if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n              minCanvasWidth = minCanvasHeight * aspectRatio;\n            } else {\n              minCanvasHeight = minCanvasWidth / aspectRatio;\n            }\n          }\n        }\n\n        var _getAdjustedSizes = getAdjustedSizes({\n          aspectRatio: aspectRatio,\n          width: minCanvasWidth,\n          height: minCanvasHeight\n        });\n\n        minCanvasWidth = _getAdjustedSizes.width;\n        minCanvasHeight = _getAdjustedSizes.height;\n        canvasData.minWidth = minCanvasWidth;\n        canvasData.minHeight = minCanvasHeight;\n        canvasData.maxWidth = Infinity;\n        canvasData.maxHeight = Infinity;\n      }\n\n      if (positionLimited) {\n        if (viewMode > (cropped ? 0 : 1)) {\n          var newCanvasLeft = containerData.width - canvasData.width;\n          var newCanvasTop = containerData.height - canvasData.height;\n          canvasData.minLeft = Math.min(0, newCanvasLeft);\n          canvasData.minTop = Math.min(0, newCanvasTop);\n          canvasData.maxLeft = Math.max(0, newCanvasLeft);\n          canvasData.maxTop = Math.max(0, newCanvasTop);\n\n          if (cropped && this.limited) {\n            canvasData.minLeft = Math.min(cropBoxData.left, cropBoxData.left + (cropBoxData.width - canvasData.width));\n            canvasData.minTop = Math.min(cropBoxData.top, cropBoxData.top + (cropBoxData.height - canvasData.height));\n            canvasData.maxLeft = cropBoxData.left;\n            canvasData.maxTop = cropBoxData.top;\n\n            if (viewMode === 2) {\n              if (canvasData.width >= containerData.width) {\n                canvasData.minLeft = Math.min(0, newCanvasLeft);\n                canvasData.maxLeft = Math.max(0, newCanvasLeft);\n              }\n\n              if (canvasData.height >= containerData.height) {\n                canvasData.minTop = Math.min(0, newCanvasTop);\n                canvasData.maxTop = Math.max(0, newCanvasTop);\n              }\n            }\n          }\n        } else {\n          canvasData.minLeft = -canvasData.width;\n          canvasData.minTop = -canvasData.height;\n          canvasData.maxLeft = containerData.width;\n          canvasData.maxTop = containerData.height;\n        }\n      }\n    },\n    renderCanvas: function renderCanvas(changed, transformed) {\n      var canvasData = this.canvasData,\n          imageData = this.imageData;\n\n      if (transformed) {\n        var _getRotatedSizes = getRotatedSizes({\n          width: imageData.naturalWidth * Math.abs(imageData.scaleX || 1),\n          height: imageData.naturalHeight * Math.abs(imageData.scaleY || 1),\n          degree: imageData.rotate || 0\n        }),\n            naturalWidth = _getRotatedSizes.width,\n            naturalHeight = _getRotatedSizes.height;\n\n        var width = canvasData.width * (naturalWidth / canvasData.naturalWidth);\n        var height = canvasData.height * (naturalHeight / canvasData.naturalHeight);\n        canvasData.left -= (width - canvasData.width) / 2;\n        canvasData.top -= (height - canvasData.height) / 2;\n        canvasData.width = width;\n        canvasData.height = height;\n        canvasData.aspectRatio = naturalWidth / naturalHeight;\n        canvasData.naturalWidth = naturalWidth;\n        canvasData.naturalHeight = naturalHeight;\n        this.limitCanvas(true, false);\n      }\n\n      if (canvasData.width > canvasData.maxWidth || canvasData.width < canvasData.minWidth) {\n        canvasData.left = canvasData.oldLeft;\n      }\n\n      if (canvasData.height > canvasData.maxHeight || canvasData.height < canvasData.minHeight) {\n        canvasData.top = canvasData.oldTop;\n      }\n\n      canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n      canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n      this.limitCanvas(false, true);\n      canvasData.left = Math.min(Math.max(canvasData.left, canvasData.minLeft), canvasData.maxLeft);\n      canvasData.top = Math.min(Math.max(canvasData.top, canvasData.minTop), canvasData.maxTop);\n      canvasData.oldLeft = canvasData.left;\n      canvasData.oldTop = canvasData.top;\n      setStyle(this.canvas, assign({\n        width: canvasData.width,\n        height: canvasData.height\n      }, getTransforms({\n        translateX: canvasData.left,\n        translateY: canvasData.top\n      })));\n      this.renderImage(changed);\n\n      if (this.cropped && this.limited) {\n        this.limitCropBox(true, true);\n      }\n    },\n    renderImage: function renderImage(changed) {\n      var canvasData = this.canvasData,\n          imageData = this.imageData;\n      var width = imageData.naturalWidth * (canvasData.width / canvasData.naturalWidth);\n      var height = imageData.naturalHeight * (canvasData.height / canvasData.naturalHeight);\n      assign(imageData, {\n        width: width,\n        height: height,\n        left: (canvasData.width - width) / 2,\n        top: (canvasData.height - height) / 2\n      });\n      setStyle(this.image, assign({\n        width: imageData.width,\n        height: imageData.height\n      }, getTransforms(assign({\n        translateX: imageData.left,\n        translateY: imageData.top\n      }, imageData))));\n\n      if (changed) {\n        this.output();\n      }\n    },\n    initCropBox: function initCropBox() {\n      var options = this.options,\n          canvasData = this.canvasData;\n      var aspectRatio = options.aspectRatio || options.initialAspectRatio;\n      var autoCropArea = Number(options.autoCropArea) || 0.8;\n      var cropBoxData = {\n        width: canvasData.width,\n        height: canvasData.height\n      };\n\n      if (aspectRatio) {\n        if (canvasData.height * aspectRatio > canvasData.width) {\n          cropBoxData.height = cropBoxData.width / aspectRatio;\n        } else {\n          cropBoxData.width = cropBoxData.height * aspectRatio;\n        }\n      }\n\n      this.cropBoxData = cropBoxData;\n      this.limitCropBox(true, true); // Initialize auto crop area\n\n      cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n      cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight); // The width/height of auto crop area must large than \"minWidth/Height\"\n\n      cropBoxData.width = Math.max(cropBoxData.minWidth, cropBoxData.width * autoCropArea);\n      cropBoxData.height = Math.max(cropBoxData.minHeight, cropBoxData.height * autoCropArea);\n      cropBoxData.left = canvasData.left + (canvasData.width - cropBoxData.width) / 2;\n      cropBoxData.top = canvasData.top + (canvasData.height - cropBoxData.height) / 2;\n      cropBoxData.oldLeft = cropBoxData.left;\n      cropBoxData.oldTop = cropBoxData.top;\n      this.initialCropBoxData = assign({}, cropBoxData);\n    },\n    limitCropBox: function limitCropBox(sizeLimited, positionLimited) {\n      var options = this.options,\n          containerData = this.containerData,\n          canvasData = this.canvasData,\n          cropBoxData = this.cropBoxData,\n          limited = this.limited;\n      var aspectRatio = options.aspectRatio;\n\n      if (sizeLimited) {\n        var minCropBoxWidth = Number(options.minCropBoxWidth) || 0;\n        var minCropBoxHeight = Number(options.minCropBoxHeight) || 0;\n        var maxCropBoxWidth = limited ? Math.min(containerData.width, canvasData.width, canvasData.width + canvasData.left, containerData.width - canvasData.left) : containerData.width;\n        var maxCropBoxHeight = limited ? Math.min(containerData.height, canvasData.height, canvasData.height + canvasData.top, containerData.height - canvasData.top) : containerData.height; // The min/maxCropBoxWidth/Height must be less than container's width/height\n\n        minCropBoxWidth = Math.min(minCropBoxWidth, containerData.width);\n        minCropBoxHeight = Math.min(minCropBoxHeight, containerData.height);\n\n        if (aspectRatio) {\n          if (minCropBoxWidth && minCropBoxHeight) {\n            if (minCropBoxHeight * aspectRatio > minCropBoxWidth) {\n              minCropBoxHeight = minCropBoxWidth / aspectRatio;\n            } else {\n              minCropBoxWidth = minCropBoxHeight * aspectRatio;\n            }\n          } else if (minCropBoxWidth) {\n            minCropBoxHeight = minCropBoxWidth / aspectRatio;\n          } else if (minCropBoxHeight) {\n            minCropBoxWidth = minCropBoxHeight * aspectRatio;\n          }\n\n          if (maxCropBoxHeight * aspectRatio > maxCropBoxWidth) {\n            maxCropBoxHeight = maxCropBoxWidth / aspectRatio;\n          } else {\n            maxCropBoxWidth = maxCropBoxHeight * aspectRatio;\n          }\n        } // The minWidth/Height must be less than maxWidth/Height\n\n\n        cropBoxData.minWidth = Math.min(minCropBoxWidth, maxCropBoxWidth);\n        cropBoxData.minHeight = Math.min(minCropBoxHeight, maxCropBoxHeight);\n        cropBoxData.maxWidth = maxCropBoxWidth;\n        cropBoxData.maxHeight = maxCropBoxHeight;\n      }\n\n      if (positionLimited) {\n        if (limited) {\n          cropBoxData.minLeft = Math.max(0, canvasData.left);\n          cropBoxData.minTop = Math.max(0, canvasData.top);\n          cropBoxData.maxLeft = Math.min(containerData.width, canvasData.left + canvasData.width) - cropBoxData.width;\n          cropBoxData.maxTop = Math.min(containerData.height, canvasData.top + canvasData.height) - cropBoxData.height;\n        } else {\n          cropBoxData.minLeft = 0;\n          cropBoxData.minTop = 0;\n          cropBoxData.maxLeft = containerData.width - cropBoxData.width;\n          cropBoxData.maxTop = containerData.height - cropBoxData.height;\n        }\n      }\n    },\n    renderCropBox: function renderCropBox() {\n      var options = this.options,\n          containerData = this.containerData,\n          cropBoxData = this.cropBoxData;\n\n      if (cropBoxData.width > cropBoxData.maxWidth || cropBoxData.width < cropBoxData.minWidth) {\n        cropBoxData.left = cropBoxData.oldLeft;\n      }\n\n      if (cropBoxData.height > cropBoxData.maxHeight || cropBoxData.height < cropBoxData.minHeight) {\n        cropBoxData.top = cropBoxData.oldTop;\n      }\n\n      cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n      cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n      this.limitCropBox(false, true);\n      cropBoxData.left = Math.min(Math.max(cropBoxData.left, cropBoxData.minLeft), cropBoxData.maxLeft);\n      cropBoxData.top = Math.min(Math.max(cropBoxData.top, cropBoxData.minTop), cropBoxData.maxTop);\n      cropBoxData.oldLeft = cropBoxData.left;\n      cropBoxData.oldTop = cropBoxData.top;\n\n      if (options.movable && options.cropBoxMovable) {\n        // Turn to move the canvas when the crop box is equal to the container\n        setData(this.face, DATA_ACTION, cropBoxData.width >= containerData.width && cropBoxData.height >= containerData.height ? ACTION_MOVE : ACTION_ALL);\n      }\n\n      setStyle(this.cropBox, assign({\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      }, getTransforms({\n        translateX: cropBoxData.left,\n        translateY: cropBoxData.top\n      })));\n\n      if (this.cropped && this.limited) {\n        this.limitCanvas(true, true);\n      }\n\n      if (!this.disabled) {\n        this.output();\n      }\n    },\n    output: function output() {\n      this.preview();\n      dispatchEvent(this.element, EVENT_CROP, this.getData());\n    }\n  };\n\n  var preview = {\n    initPreview: function initPreview() {\n      var element = this.element,\n          crossOrigin = this.crossOrigin;\n      var preview = this.options.preview;\n      var url = crossOrigin ? this.crossOriginUrl : this.url;\n      var alt = element.alt || 'The image to preview';\n      var image = document.createElement('img');\n\n      if (crossOrigin) {\n        image.crossOrigin = crossOrigin;\n      }\n\n      image.src = url;\n      image.alt = alt;\n      this.viewBox.appendChild(image);\n      this.viewBoxImage = image;\n\n      if (!preview) {\n        return;\n      }\n\n      var previews = preview;\n\n      if (typeof preview === 'string') {\n        previews = element.ownerDocument.querySelectorAll(preview);\n      } else if (preview.querySelector) {\n        previews = [preview];\n      }\n\n      this.previews = previews;\n      forEach(previews, function (el) {\n        var img = document.createElement('img'); // Save the original size for recover\n\n        setData(el, DATA_PREVIEW, {\n          width: el.offsetWidth,\n          height: el.offsetHeight,\n          html: el.innerHTML\n        });\n\n        if (crossOrigin) {\n          img.crossOrigin = crossOrigin;\n        }\n\n        img.src = url;\n        img.alt = alt;\n        /**\n         * Override img element styles\n         * Add `display:block` to avoid margin top issue\n         * Add `height:auto` to override `height` attribute on IE8\n         * (Occur only when margin-top <= -height)\n         */\n\n        img.style.cssText = 'display:block;' + 'width:100%;' + 'height:auto;' + 'min-width:0!important;' + 'min-height:0!important;' + 'max-width:none!important;' + 'max-height:none!important;' + 'image-orientation:0deg!important;\"';\n        el.innerHTML = '';\n        el.appendChild(img);\n      });\n    },\n    resetPreview: function resetPreview() {\n      forEach(this.previews, function (element) {\n        var data = getData(element, DATA_PREVIEW);\n        setStyle(element, {\n          width: data.width,\n          height: data.height\n        });\n        element.innerHTML = data.html;\n        removeData(element, DATA_PREVIEW);\n      });\n    },\n    preview: function preview() {\n      var imageData = this.imageData,\n          canvasData = this.canvasData,\n          cropBoxData = this.cropBoxData;\n      var cropBoxWidth = cropBoxData.width,\n          cropBoxHeight = cropBoxData.height;\n      var width = imageData.width,\n          height = imageData.height;\n      var left = cropBoxData.left - canvasData.left - imageData.left;\n      var top = cropBoxData.top - canvasData.top - imageData.top;\n\n      if (!this.cropped || this.disabled) {\n        return;\n      }\n\n      setStyle(this.viewBoxImage, assign({\n        width: width,\n        height: height\n      }, getTransforms(assign({\n        translateX: -left,\n        translateY: -top\n      }, imageData))));\n      forEach(this.previews, function (element) {\n        var data = getData(element, DATA_PREVIEW);\n        var originalWidth = data.width;\n        var originalHeight = data.height;\n        var newWidth = originalWidth;\n        var newHeight = originalHeight;\n        var ratio = 1;\n\n        if (cropBoxWidth) {\n          ratio = originalWidth / cropBoxWidth;\n          newHeight = cropBoxHeight * ratio;\n        }\n\n        if (cropBoxHeight && newHeight > originalHeight) {\n          ratio = originalHeight / cropBoxHeight;\n          newWidth = cropBoxWidth * ratio;\n          newHeight = originalHeight;\n        }\n\n        setStyle(element, {\n          width: newWidth,\n          height: newHeight\n        });\n        setStyle(element.getElementsByTagName('img')[0], assign({\n          width: width * ratio,\n          height: height * ratio\n        }, getTransforms(assign({\n          translateX: -left * ratio,\n          translateY: -top * ratio\n        }, imageData))));\n      });\n    }\n  };\n\n  var events = {\n    bind: function bind() {\n      var element = this.element,\n          options = this.options,\n          cropper = this.cropper;\n\n      if (isFunction(options.cropstart)) {\n        addListener(element, EVENT_CROP_START, options.cropstart);\n      }\n\n      if (isFunction(options.cropmove)) {\n        addListener(element, EVENT_CROP_MOVE, options.cropmove);\n      }\n\n      if (isFunction(options.cropend)) {\n        addListener(element, EVENT_CROP_END, options.cropend);\n      }\n\n      if (isFunction(options.crop)) {\n        addListener(element, EVENT_CROP, options.crop);\n      }\n\n      if (isFunction(options.zoom)) {\n        addListener(element, EVENT_ZOOM, options.zoom);\n      }\n\n      addListener(cropper, EVENT_POINTER_DOWN, this.onCropStart = this.cropStart.bind(this));\n\n      if (options.zoomable && options.zoomOnWheel) {\n        addListener(cropper, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n          passive: false,\n          capture: true\n        });\n      }\n\n      if (options.toggleDragModeOnDblclick) {\n        addListener(cropper, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n      }\n\n      addListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove = this.cropMove.bind(this));\n      addListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd = this.cropEnd.bind(this));\n\n      if (options.responsive) {\n        addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n      }\n    },\n    unbind: function unbind() {\n      var element = this.element,\n          options = this.options,\n          cropper = this.cropper;\n\n      if (isFunction(options.cropstart)) {\n        removeListener(element, EVENT_CROP_START, options.cropstart);\n      }\n\n      if (isFunction(options.cropmove)) {\n        removeListener(element, EVENT_CROP_MOVE, options.cropmove);\n      }\n\n      if (isFunction(options.cropend)) {\n        removeListener(element, EVENT_CROP_END, options.cropend);\n      }\n\n      if (isFunction(options.crop)) {\n        removeListener(element, EVENT_CROP, options.crop);\n      }\n\n      if (isFunction(options.zoom)) {\n        removeListener(element, EVENT_ZOOM, options.zoom);\n      }\n\n      removeListener(cropper, EVENT_POINTER_DOWN, this.onCropStart);\n\n      if (options.zoomable && options.zoomOnWheel) {\n        removeListener(cropper, EVENT_WHEEL, this.onWheel, {\n          passive: false,\n          capture: true\n        });\n      }\n\n      if (options.toggleDragModeOnDblclick) {\n        removeListener(cropper, EVENT_DBLCLICK, this.onDblclick);\n      }\n\n      removeListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove);\n      removeListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd);\n\n      if (options.responsive) {\n        removeListener(window, EVENT_RESIZE, this.onResize);\n      }\n    }\n  };\n\n  var handlers = {\n    resize: function resize() {\n      if (this.disabled) {\n        return;\n      }\n\n      var options = this.options,\n          container = this.container,\n          containerData = this.containerData;\n      var ratioX = container.offsetWidth / containerData.width;\n      var ratioY = container.offsetHeight / containerData.height;\n      var ratio = Math.abs(ratioX - 1) > Math.abs(ratioY - 1) ? ratioX : ratioY; // Resize when width changed or height changed\n\n      if (ratio !== 1) {\n        var canvasData;\n        var cropBoxData;\n\n        if (options.restore) {\n          canvasData = this.getCanvasData();\n          cropBoxData = this.getCropBoxData();\n        }\n\n        this.render();\n\n        if (options.restore) {\n          this.setCanvasData(forEach(canvasData, function (n, i) {\n            canvasData[i] = n * ratio;\n          }));\n          this.setCropBoxData(forEach(cropBoxData, function (n, i) {\n            cropBoxData[i] = n * ratio;\n          }));\n        }\n      }\n    },\n    dblclick: function dblclick() {\n      if (this.disabled || this.options.dragMode === DRAG_MODE_NONE) {\n        return;\n      }\n\n      this.setDragMode(hasClass(this.dragBox, CLASS_CROP) ? DRAG_MODE_MOVE : DRAG_MODE_CROP);\n    },\n    wheel: function wheel(event) {\n      var _this = this;\n\n      var ratio = Number(this.options.wheelZoomRatio) || 0.1;\n      var delta = 1;\n\n      if (this.disabled) {\n        return;\n      }\n\n      event.preventDefault(); // Limit wheel speed to prevent zoom too fast (#21)\n\n      if (this.wheeling) {\n        return;\n      }\n\n      this.wheeling = true;\n      setTimeout(function () {\n        _this.wheeling = false;\n      }, 50);\n\n      if (event.deltaY) {\n        delta = event.deltaY > 0 ? 1 : -1;\n      } else if (event.wheelDelta) {\n        delta = -event.wheelDelta / 120;\n      } else if (event.detail) {\n        delta = event.detail > 0 ? 1 : -1;\n      }\n\n      this.zoom(-delta * ratio, event);\n    },\n    cropStart: function cropStart(event) {\n      var buttons = event.buttons,\n          button = event.button;\n\n      if (this.disabled // Handle mouse event and pointer event and ignore touch event\n      || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && ( // No primary button (Usually the left button)\n      isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0 // Open context menu\n      || event.ctrlKey)) {\n        return;\n      }\n\n      var options = this.options,\n          pointers = this.pointers;\n      var action;\n\n      if (event.changedTouches) {\n        // Handle touch event\n        forEach(event.changedTouches, function (touch) {\n          pointers[touch.identifier] = getPointer(touch);\n        });\n      } else {\n        // Handle mouse event and pointer event\n        pointers[event.pointerId || 0] = getPointer(event);\n      }\n\n      if (Object.keys(pointers).length > 1 && options.zoomable && options.zoomOnTouch) {\n        action = ACTION_ZOOM;\n      } else {\n        action = getData(event.target, DATA_ACTION);\n      }\n\n      if (!REGEXP_ACTIONS.test(action)) {\n        return;\n      }\n\n      if (dispatchEvent(this.element, EVENT_CROP_START, {\n        originalEvent: event,\n        action: action\n      }) === false) {\n        return;\n      } // This line is required for preventing page zooming in iOS browsers\n\n\n      event.preventDefault();\n      this.action = action;\n      this.cropping = false;\n\n      if (action === ACTION_CROP) {\n        this.cropping = true;\n        addClass(this.dragBox, CLASS_MODAL);\n      }\n    },\n    cropMove: function cropMove(event) {\n      var action = this.action;\n\n      if (this.disabled || !action) {\n        return;\n      }\n\n      var pointers = this.pointers;\n      event.preventDefault();\n\n      if (dispatchEvent(this.element, EVENT_CROP_MOVE, {\n        originalEvent: event,\n        action: action\n      }) === false) {\n        return;\n      }\n\n      if (event.changedTouches) {\n        forEach(event.changedTouches, function (touch) {\n          // The first parameter should not be undefined (#432)\n          assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n        });\n      } else {\n        assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n      }\n\n      this.change(event);\n    },\n    cropEnd: function cropEnd(event) {\n      if (this.disabled) {\n        return;\n      }\n\n      var action = this.action,\n          pointers = this.pointers;\n\n      if (event.changedTouches) {\n        forEach(event.changedTouches, function (touch) {\n          delete pointers[touch.identifier];\n        });\n      } else {\n        delete pointers[event.pointerId || 0];\n      }\n\n      if (!action) {\n        return;\n      }\n\n      event.preventDefault();\n\n      if (!Object.keys(pointers).length) {\n        this.action = '';\n      }\n\n      if (this.cropping) {\n        this.cropping = false;\n        toggleClass(this.dragBox, CLASS_MODAL, this.cropped && this.options.modal);\n      }\n\n      dispatchEvent(this.element, EVENT_CROP_END, {\n        originalEvent: event,\n        action: action\n      });\n    }\n  };\n\n  var change = {\n    change: function change(event) {\n      var options = this.options,\n          canvasData = this.canvasData,\n          containerData = this.containerData,\n          cropBoxData = this.cropBoxData,\n          pointers = this.pointers;\n      var action = this.action;\n      var aspectRatio = options.aspectRatio;\n      var left = cropBoxData.left,\n          top = cropBoxData.top,\n          width = cropBoxData.width,\n          height = cropBoxData.height;\n      var right = left + width;\n      var bottom = top + height;\n      var minLeft = 0;\n      var minTop = 0;\n      var maxWidth = containerData.width;\n      var maxHeight = containerData.height;\n      var renderable = true;\n      var offset; // Locking aspect ratio in \"free mode\" by holding shift key\n\n      if (!aspectRatio && event.shiftKey) {\n        aspectRatio = width && height ? width / height : 1;\n      }\n\n      if (this.limited) {\n        minLeft = cropBoxData.minLeft;\n        minTop = cropBoxData.minTop;\n        maxWidth = minLeft + Math.min(containerData.width, canvasData.width, canvasData.left + canvasData.width);\n        maxHeight = minTop + Math.min(containerData.height, canvasData.height, canvasData.top + canvasData.height);\n      }\n\n      var pointer = pointers[Object.keys(pointers)[0]];\n      var range = {\n        x: pointer.endX - pointer.startX,\n        y: pointer.endY - pointer.startY\n      };\n\n      var check = function check(side) {\n        switch (side) {\n          case ACTION_EAST:\n            if (right + range.x > maxWidth) {\n              range.x = maxWidth - right;\n            }\n\n            break;\n\n          case ACTION_WEST:\n            if (left + range.x < minLeft) {\n              range.x = minLeft - left;\n            }\n\n            break;\n\n          case ACTION_NORTH:\n            if (top + range.y < minTop) {\n              range.y = minTop - top;\n            }\n\n            break;\n\n          case ACTION_SOUTH:\n            if (bottom + range.y > maxHeight) {\n              range.y = maxHeight - bottom;\n            }\n\n            break;\n        }\n      };\n\n      switch (action) {\n        // Move crop box\n        case ACTION_ALL:\n          left += range.x;\n          top += range.y;\n          break;\n        // Resize crop box\n\n        case ACTION_EAST:\n          if (range.x >= 0 && (right >= maxWidth || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_EAST);\n          width += range.x;\n\n          if (width < 0) {\n            action = ACTION_WEST;\n            width = -width;\n            left -= width;\n          }\n\n          if (aspectRatio) {\n            height = width / aspectRatio;\n            top += (cropBoxData.height - height) / 2;\n          }\n\n          break;\n\n        case ACTION_NORTH:\n          if (range.y <= 0 && (top <= minTop || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n\n          if (height < 0) {\n            action = ACTION_SOUTH;\n            height = -height;\n            top -= height;\n          }\n\n          if (aspectRatio) {\n            width = height * aspectRatio;\n            left += (cropBoxData.width - width) / 2;\n          }\n\n          break;\n\n        case ACTION_WEST:\n          if (range.x <= 0 && (left <= minLeft || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_WEST);\n          width -= range.x;\n          left += range.x;\n\n          if (width < 0) {\n            action = ACTION_EAST;\n            width = -width;\n            left -= width;\n          }\n\n          if (aspectRatio) {\n            height = width / aspectRatio;\n            top += (cropBoxData.height - height) / 2;\n          }\n\n          break;\n\n        case ACTION_SOUTH:\n          if (range.y >= 0 && (bottom >= maxHeight || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n            renderable = false;\n            break;\n          }\n\n          check(ACTION_SOUTH);\n          height += range.y;\n\n          if (height < 0) {\n            action = ACTION_NORTH;\n            height = -height;\n            top -= height;\n          }\n\n          if (aspectRatio) {\n            width = height * aspectRatio;\n            left += (cropBoxData.width - width) / 2;\n          }\n\n          break;\n\n        case ACTION_NORTH_EAST:\n          if (aspectRatio) {\n            if (range.y <= 0 && (top <= minTop || right >= maxWidth)) {\n              renderable = false;\n              break;\n            }\n\n            check(ACTION_NORTH);\n            height -= range.y;\n            top += range.y;\n            width = height * aspectRatio;\n          } else {\n            check(ACTION_NORTH);\n            check(ACTION_EAST);\n\n            if (range.x >= 0) {\n              if (right < maxWidth) {\n                width += range.x;\n              } else if (range.y <= 0 && top <= minTop) {\n                renderable = false;\n              }\n            } else {\n              width += range.x;\n            }\n\n            if (range.y <= 0) {\n              if (top > minTop) {\n                height -= range.y;\n                top += range.y;\n              }\n            } else {\n              height -= range.y;\n              top += range.y;\n            }\n          }\n\n          if (width < 0 && height < 0) {\n            action = ACTION_SOUTH_WEST;\n            height = -height;\n            width = -width;\n            top -= height;\n            left -= width;\n          } else if (width < 0) {\n            action = ACTION_NORTH_WEST;\n            width = -width;\n            left -= width;\n          } else if (height < 0) {\n            action = ACTION_SOUTH_EAST;\n            height = -height;\n            top -= height;\n          }\n\n          break;\n\n        case ACTION_NORTH_WEST:\n          if (aspectRatio) {\n            if (range.y <= 0 && (top <= minTop || left <= minLeft)) {\n              renderable = false;\n              break;\n            }\n\n            check(ACTION_NORTH);\n            height -= range.y;\n            top += range.y;\n            width = height * aspectRatio;\n            left += cropBoxData.width - width;\n          } else {\n            check(ACTION_NORTH);\n            check(ACTION_WEST);\n\n            if (range.x <= 0) {\n              if (left > minLeft) {\n                width -= range.x;\n                left += range.x;\n              } else if (range.y <= 0 && top <= minTop) {\n                renderable = false;\n              }\n            } else {\n              width -= range.x;\n              left += range.x;\n            }\n\n            if (range.y <= 0) {\n              if (top > minTop) {\n                height -= range.y;\n                top += range.y;\n              }\n            } else {\n              height -= range.y;\n              top += range.y;\n            }\n          }\n\n          if (width < 0 && height < 0) {\n            action = ACTION_SOUTH_EAST;\n            height = -height;\n            width = -width;\n            top -= height;\n            left -= width;\n          } else if (width < 0) {\n            action = ACTION_NORTH_EAST;\n            width = -width;\n            left -= width;\n          } else if (height < 0) {\n            action = ACTION_SOUTH_WEST;\n            height = -height;\n            top -= height;\n          }\n\n          break;\n\n        case ACTION_SOUTH_WEST:\n          if (aspectRatio) {\n            if (range.x <= 0 && (left <= minLeft || bottom >= maxHeight)) {\n              renderable = false;\n              break;\n            }\n\n            check(ACTION_WEST);\n            width -= range.x;\n            left += range.x;\n            height = width / aspectRatio;\n          } else {\n            check(ACTION_SOUTH);\n            check(ACTION_WEST);\n\n            if (range.x <= 0) {\n              if (left > minLeft) {\n                width -= range.x;\n                left += range.x;\n              } else if (range.y >= 0 && bottom >= maxHeight) {\n                renderable = false;\n              }\n            } else {\n              width -= range.x;\n              left += range.x;\n            }\n\n            if (range.y >= 0) {\n              if (bottom < maxHeight) {\n                height += range.y;\n              }\n            } else {\n              height += range.y;\n            }\n          }\n\n          if (width < 0 && height < 0) {\n            action = ACTION_NORTH_EAST;\n            height = -height;\n            width = -width;\n            top -= height;\n            left -= width;\n          } else if (width < 0) {\n            action = ACTION_SOUTH_EAST;\n            width = -width;\n            left -= width;\n          } else if (height < 0) {\n            action = ACTION_NORTH_WEST;\n            height = -height;\n            top -= height;\n          }\n\n          break;\n\n        case ACTION_SOUTH_EAST:\n          if (aspectRatio) {\n            if (range.x >= 0 && (right >= maxWidth || bottom >= maxHeight)) {\n              renderable = false;\n              break;\n            }\n\n            check(ACTION_EAST);\n            width += range.x;\n            height = width / aspectRatio;\n          } else {\n            check(ACTION_SOUTH);\n            check(ACTION_EAST);\n\n            if (range.x >= 0) {\n              if (right < maxWidth) {\n                width += range.x;\n              } else if (range.y >= 0 && bottom >= maxHeight) {\n                renderable = false;\n              }\n            } else {\n              width += range.x;\n            }\n\n            if (range.y >= 0) {\n              if (bottom < maxHeight) {\n                height += range.y;\n              }\n            } else {\n              height += range.y;\n            }\n          }\n\n          if (width < 0 && height < 0) {\n            action = ACTION_NORTH_WEST;\n            height = -height;\n            width = -width;\n            top -= height;\n            left -= width;\n          } else if (width < 0) {\n            action = ACTION_SOUTH_WEST;\n            width = -width;\n            left -= width;\n          } else if (height < 0) {\n            action = ACTION_NORTH_EAST;\n            height = -height;\n            top -= height;\n          }\n\n          break;\n        // Move canvas\n\n        case ACTION_MOVE:\n          this.move(range.x, range.y);\n          renderable = false;\n          break;\n        // Zoom canvas\n\n        case ACTION_ZOOM:\n          this.zoom(getMaxZoomRatio(pointers), event);\n          renderable = false;\n          break;\n        // Create crop box\n\n        case ACTION_CROP:\n          if (!range.x || !range.y) {\n            renderable = false;\n            break;\n          }\n\n          offset = getOffset(this.cropper);\n          left = pointer.startX - offset.left;\n          top = pointer.startY - offset.top;\n          width = cropBoxData.minWidth;\n          height = cropBoxData.minHeight;\n\n          if (range.x > 0) {\n            action = range.y > 0 ? ACTION_SOUTH_EAST : ACTION_NORTH_EAST;\n          } else if (range.x < 0) {\n            left -= width;\n            action = range.y > 0 ? ACTION_SOUTH_WEST : ACTION_NORTH_WEST;\n          }\n\n          if (range.y < 0) {\n            top -= height;\n          } // Show the crop box if is hidden\n\n\n          if (!this.cropped) {\n            removeClass(this.cropBox, CLASS_HIDDEN);\n            this.cropped = true;\n\n            if (this.limited) {\n              this.limitCropBox(true, true);\n            }\n          }\n\n          break;\n      }\n\n      if (renderable) {\n        cropBoxData.width = width;\n        cropBoxData.height = height;\n        cropBoxData.left = left;\n        cropBoxData.top = top;\n        this.action = action;\n        this.renderCropBox();\n      } // Override\n\n\n      forEach(pointers, function (p) {\n        p.startX = p.endX;\n        p.startY = p.endY;\n      });\n    }\n  };\n\n  var methods = {\n    // Show the crop box manually\n    crop: function crop() {\n      if (this.ready && !this.cropped && !this.disabled) {\n        this.cropped = true;\n        this.limitCropBox(true, true);\n\n        if (this.options.modal) {\n          addClass(this.dragBox, CLASS_MODAL);\n        }\n\n        removeClass(this.cropBox, CLASS_HIDDEN);\n        this.setCropBoxData(this.initialCropBoxData);\n      }\n\n      return this;\n    },\n    // Reset the image and crop box to their initial states\n    reset: function reset() {\n      if (this.ready && !this.disabled) {\n        this.imageData = assign({}, this.initialImageData);\n        this.canvasData = assign({}, this.initialCanvasData);\n        this.cropBoxData = assign({}, this.initialCropBoxData);\n        this.renderCanvas();\n\n        if (this.cropped) {\n          this.renderCropBox();\n        }\n      }\n\n      return this;\n    },\n    // Clear the crop box\n    clear: function clear() {\n      if (this.cropped && !this.disabled) {\n        assign(this.cropBoxData, {\n          left: 0,\n          top: 0,\n          width: 0,\n          height: 0\n        });\n        this.cropped = false;\n        this.renderCropBox();\n        this.limitCanvas(true, true); // Render canvas after crop box rendered\n\n        this.renderCanvas();\n        removeClass(this.dragBox, CLASS_MODAL);\n        addClass(this.cropBox, CLASS_HIDDEN);\n      }\n\n      return this;\n    },\n\n    /**\n     * Replace the image's src and rebuild the cropper\n     * @param {string} url - The new URL.\n     * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.\n     * @returns {Cropper} this\n     */\n    replace: function replace(url) {\n      var hasSameSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (!this.disabled && url) {\n        if (this.isImg) {\n          this.element.src = url;\n        }\n\n        if (hasSameSize) {\n          this.url = url;\n          this.image.src = url;\n\n          if (this.ready) {\n            this.viewBoxImage.src = url;\n            forEach(this.previews, function (element) {\n              element.getElementsByTagName('img')[0].src = url;\n            });\n          }\n        } else {\n          if (this.isImg) {\n            this.replaced = true;\n          }\n\n          this.options.data = null;\n          this.uncreate();\n          this.load(url);\n        }\n      }\n\n      return this;\n    },\n    // Enable (unfreeze) the cropper\n    enable: function enable() {\n      if (this.ready && this.disabled) {\n        this.disabled = false;\n        removeClass(this.cropper, CLASS_DISABLED);\n      }\n\n      return this;\n    },\n    // Disable (freeze) the cropper\n    disable: function disable() {\n      if (this.ready && !this.disabled) {\n        this.disabled = true;\n        addClass(this.cropper, CLASS_DISABLED);\n      }\n\n      return this;\n    },\n\n    /**\n     * Destroy the cropper and remove the instance from the image\n     * @returns {Cropper} this\n     */\n    destroy: function destroy() {\n      var element = this.element;\n\n      if (!element[NAMESPACE]) {\n        return this;\n      }\n\n      element[NAMESPACE] = undefined;\n\n      if (this.isImg && this.replaced) {\n        element.src = this.originalUrl;\n      }\n\n      this.uncreate();\n      return this;\n    },\n\n    /**\n     * Move the canvas with relative offsets\n     * @param {number} offsetX - The relative offset distance on the x-axis.\n     * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.\n     * @returns {Cropper} this\n     */\n    move: function move(offsetX) {\n      var offsetY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : offsetX;\n      var _this$canvasData = this.canvasData,\n          left = _this$canvasData.left,\n          top = _this$canvasData.top;\n      return this.moveTo(isUndefined(offsetX) ? offsetX : left + Number(offsetX), isUndefined(offsetY) ? offsetY : top + Number(offsetY));\n    },\n\n    /**\n     * Move the canvas to an absolute point\n     * @param {number} x - The x-axis coordinate.\n     * @param {number} [y=x] - The y-axis coordinate.\n     * @returns {Cropper} this\n     */\n    moveTo: function moveTo(x) {\n      var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n      var canvasData = this.canvasData;\n      var changed = false;\n      x = Number(x);\n      y = Number(y);\n\n      if (this.ready && !this.disabled && this.options.movable) {\n        if (isNumber(x)) {\n          canvasData.left = x;\n          changed = true;\n        }\n\n        if (isNumber(y)) {\n          canvasData.top = y;\n          changed = true;\n        }\n\n        if (changed) {\n          this.renderCanvas(true);\n        }\n      }\n\n      return this;\n    },\n\n    /**\n     * Zoom the canvas with a relative ratio\n     * @param {number} ratio - The target ratio.\n     * @param {Event} _originalEvent - The original event if any.\n     * @returns {Cropper} this\n     */\n    zoom: function zoom(ratio, _originalEvent) {\n      var canvasData = this.canvasData;\n      ratio = Number(ratio);\n\n      if (ratio < 0) {\n        ratio = 1 / (1 - ratio);\n      } else {\n        ratio = 1 + ratio;\n      }\n\n      return this.zoomTo(canvasData.width * ratio / canvasData.naturalWidth, null, _originalEvent);\n    },\n\n    /**\n     * Zoom the canvas to an absolute ratio\n     * @param {number} ratio - The target ratio.\n     * @param {Object} pivot - The zoom pivot point coordinate.\n     * @param {Event} _originalEvent - The original event if any.\n     * @returns {Cropper} this\n     */\n    zoomTo: function zoomTo(ratio, pivot, _originalEvent) {\n      var options = this.options,\n          canvasData = this.canvasData;\n      var width = canvasData.width,\n          height = canvasData.height,\n          naturalWidth = canvasData.naturalWidth,\n          naturalHeight = canvasData.naturalHeight;\n      ratio = Number(ratio);\n\n      if (ratio >= 0 && this.ready && !this.disabled && options.zoomable) {\n        var newWidth = naturalWidth * ratio;\n        var newHeight = naturalHeight * ratio;\n\n        if (dispatchEvent(this.element, EVENT_ZOOM, {\n          ratio: ratio,\n          oldRatio: width / naturalWidth,\n          originalEvent: _originalEvent\n        }) === false) {\n          return this;\n        }\n\n        if (_originalEvent) {\n          var pointers = this.pointers;\n          var offset = getOffset(this.cropper);\n          var center = pointers && Object.keys(pointers).length ? getPointersCenter(pointers) : {\n            pageX: _originalEvent.pageX,\n            pageY: _originalEvent.pageY\n          }; // Zoom from the triggering point of the event\n\n          canvasData.left -= (newWidth - width) * ((center.pageX - offset.left - canvasData.left) / width);\n          canvasData.top -= (newHeight - height) * ((center.pageY - offset.top - canvasData.top) / height);\n        } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n          canvasData.left -= (newWidth - width) * ((pivot.x - canvasData.left) / width);\n          canvasData.top -= (newHeight - height) * ((pivot.y - canvasData.top) / height);\n        } else {\n          // Zoom from the center of the canvas\n          canvasData.left -= (newWidth - width) / 2;\n          canvasData.top -= (newHeight - height) / 2;\n        }\n\n        canvasData.width = newWidth;\n        canvasData.height = newHeight;\n        this.renderCanvas(true);\n      }\n\n      return this;\n    },\n\n    /**\n     * Rotate the canvas with a relative degree\n     * @param {number} degree - The rotate degree.\n     * @returns {Cropper} this\n     */\n    rotate: function rotate(degree) {\n      return this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n    },\n\n    /**\n     * Rotate the canvas to an absolute degree\n     * @param {number} degree - The rotate degree.\n     * @returns {Cropper} this\n     */\n    rotateTo: function rotateTo(degree) {\n      degree = Number(degree);\n\n      if (isNumber(degree) && this.ready && !this.disabled && this.options.rotatable) {\n        this.imageData.rotate = degree % 360;\n        this.renderCanvas(true, true);\n      }\n\n      return this;\n    },\n\n    /**\n     * Scale the image on the x-axis.\n     * @param {number} scaleX - The scale ratio on the x-axis.\n     * @returns {Cropper} this\n     */\n    scaleX: function scaleX(_scaleX) {\n      var scaleY = this.imageData.scaleY;\n      return this.scale(_scaleX, isNumber(scaleY) ? scaleY : 1);\n    },\n\n    /**\n     * Scale the image on the y-axis.\n     * @param {number} scaleY - The scale ratio on the y-axis.\n     * @returns {Cropper} this\n     */\n    scaleY: function scaleY(_scaleY) {\n      var scaleX = this.imageData.scaleX;\n      return this.scale(isNumber(scaleX) ? scaleX : 1, _scaleY);\n    },\n\n    /**\n     * Scale the image\n     * @param {number} scaleX - The scale ratio on the x-axis.\n     * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n     * @returns {Cropper} this\n     */\n    scale: function scale(scaleX) {\n      var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n      var imageData = this.imageData;\n      var transformed = false;\n      scaleX = Number(scaleX);\n      scaleY = Number(scaleY);\n\n      if (this.ready && !this.disabled && this.options.scalable) {\n        if (isNumber(scaleX)) {\n          imageData.scaleX = scaleX;\n          transformed = true;\n        }\n\n        if (isNumber(scaleY)) {\n          imageData.scaleY = scaleY;\n          transformed = true;\n        }\n\n        if (transformed) {\n          this.renderCanvas(true, true);\n        }\n      }\n\n      return this;\n    },\n\n    /**\n     * Get the cropped area position and size data (base on the original image)\n     * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n     * @returns {Object} The result cropped data.\n     */\n    getData: function getData() {\n      var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var options = this.options,\n          imageData = this.imageData,\n          canvasData = this.canvasData,\n          cropBoxData = this.cropBoxData;\n      var data;\n\n      if (this.ready && this.cropped) {\n        data = {\n          x: cropBoxData.left - canvasData.left,\n          y: cropBoxData.top - canvasData.top,\n          width: cropBoxData.width,\n          height: cropBoxData.height\n        };\n        var ratio = imageData.width / imageData.naturalWidth;\n        forEach(data, function (n, i) {\n          data[i] = n / ratio;\n        });\n\n        if (rounded) {\n          // In case rounding off leads to extra 1px in right or bottom border\n          // we should round the top-left corner and the dimension (#343).\n          var bottom = Math.round(data.y + data.height);\n          var right = Math.round(data.x + data.width);\n          data.x = Math.round(data.x);\n          data.y = Math.round(data.y);\n          data.width = right - data.x;\n          data.height = bottom - data.y;\n        }\n      } else {\n        data = {\n          x: 0,\n          y: 0,\n          width: 0,\n          height: 0\n        };\n      }\n\n      if (options.rotatable) {\n        data.rotate = imageData.rotate || 0;\n      }\n\n      if (options.scalable) {\n        data.scaleX = imageData.scaleX || 1;\n        data.scaleY = imageData.scaleY || 1;\n      }\n\n      return data;\n    },\n\n    /**\n     * Set the cropped area position and size with new data\n     * @param {Object} data - The new data.\n     * @returns {Cropper} this\n     */\n    setData: function setData(data) {\n      var options = this.options,\n          imageData = this.imageData,\n          canvasData = this.canvasData;\n      var cropBoxData = {};\n\n      if (this.ready && !this.disabled && isPlainObject(data)) {\n        var transformed = false;\n\n        if (options.rotatable) {\n          if (isNumber(data.rotate) && data.rotate !== imageData.rotate) {\n            imageData.rotate = data.rotate;\n            transformed = true;\n          }\n        }\n\n        if (options.scalable) {\n          if (isNumber(data.scaleX) && data.scaleX !== imageData.scaleX) {\n            imageData.scaleX = data.scaleX;\n            transformed = true;\n          }\n\n          if (isNumber(data.scaleY) && data.scaleY !== imageData.scaleY) {\n            imageData.scaleY = data.scaleY;\n            transformed = true;\n          }\n        }\n\n        if (transformed) {\n          this.renderCanvas(true, true);\n        }\n\n        var ratio = imageData.width / imageData.naturalWidth;\n\n        if (isNumber(data.x)) {\n          cropBoxData.left = data.x * ratio + canvasData.left;\n        }\n\n        if (isNumber(data.y)) {\n          cropBoxData.top = data.y * ratio + canvasData.top;\n        }\n\n        if (isNumber(data.width)) {\n          cropBoxData.width = data.width * ratio;\n        }\n\n        if (isNumber(data.height)) {\n          cropBoxData.height = data.height * ratio;\n        }\n\n        this.setCropBoxData(cropBoxData);\n      }\n\n      return this;\n    },\n\n    /**\n     * Get the container size data.\n     * @returns {Object} The result container data.\n     */\n    getContainerData: function getContainerData() {\n      return this.ready ? assign({}, this.containerData) : {};\n    },\n\n    /**\n     * Get the image position and size data.\n     * @returns {Object} The result image data.\n     */\n    getImageData: function getImageData() {\n      return this.sized ? assign({}, this.imageData) : {};\n    },\n\n    /**\n     * Get the canvas position and size data.\n     * @returns {Object} The result canvas data.\n     */\n    getCanvasData: function getCanvasData() {\n      var canvasData = this.canvasData;\n      var data = {};\n\n      if (this.ready) {\n        forEach(['left', 'top', 'width', 'height', 'naturalWidth', 'naturalHeight'], function (n) {\n          data[n] = canvasData[n];\n        });\n      }\n\n      return data;\n    },\n\n    /**\n     * Set the canvas position and size with new data.\n     * @param {Object} data - The new canvas data.\n     * @returns {Cropper} this\n     */\n    setCanvasData: function setCanvasData(data) {\n      var canvasData = this.canvasData;\n      var aspectRatio = canvasData.aspectRatio;\n\n      if (this.ready && !this.disabled && isPlainObject(data)) {\n        if (isNumber(data.left)) {\n          canvasData.left = data.left;\n        }\n\n        if (isNumber(data.top)) {\n          canvasData.top = data.top;\n        }\n\n        if (isNumber(data.width)) {\n          canvasData.width = data.width;\n          canvasData.height = data.width / aspectRatio;\n        } else if (isNumber(data.height)) {\n          canvasData.height = data.height;\n          canvasData.width = data.height * aspectRatio;\n        }\n\n        this.renderCanvas(true);\n      }\n\n      return this;\n    },\n\n    /**\n     * Get the crop box position and size data.\n     * @returns {Object} The result crop box data.\n     */\n    getCropBoxData: function getCropBoxData() {\n      var cropBoxData = this.cropBoxData;\n      var data;\n\n      if (this.ready && this.cropped) {\n        data = {\n          left: cropBoxData.left,\n          top: cropBoxData.top,\n          width: cropBoxData.width,\n          height: cropBoxData.height\n        };\n      }\n\n      return data || {};\n    },\n\n    /**\n     * Set the crop box position and size with new data.\n     * @param {Object} data - The new crop box data.\n     * @returns {Cropper} this\n     */\n    setCropBoxData: function setCropBoxData(data) {\n      var cropBoxData = this.cropBoxData;\n      var aspectRatio = this.options.aspectRatio;\n      var widthChanged;\n      var heightChanged;\n\n      if (this.ready && this.cropped && !this.disabled && isPlainObject(data)) {\n        if (isNumber(data.left)) {\n          cropBoxData.left = data.left;\n        }\n\n        if (isNumber(data.top)) {\n          cropBoxData.top = data.top;\n        }\n\n        if (isNumber(data.width) && data.width !== cropBoxData.width) {\n          widthChanged = true;\n          cropBoxData.width = data.width;\n        }\n\n        if (isNumber(data.height) && data.height !== cropBoxData.height) {\n          heightChanged = true;\n          cropBoxData.height = data.height;\n        }\n\n        if (aspectRatio) {\n          if (widthChanged) {\n            cropBoxData.height = cropBoxData.width / aspectRatio;\n          } else if (heightChanged) {\n            cropBoxData.width = cropBoxData.height * aspectRatio;\n          }\n        }\n\n        this.renderCropBox();\n      }\n\n      return this;\n    },\n\n    /**\n     * Get a canvas drawn the cropped image.\n     * @param {Object} [options={}] - The config options.\n     * @returns {HTMLCanvasElement} - The result canvas.\n     */\n    getCroppedCanvas: function getCroppedCanvas() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      if (!this.ready || !window.HTMLCanvasElement) {\n        return null;\n      }\n\n      var canvasData = this.canvasData;\n      var source = getSourceCanvas(this.image, this.imageData, canvasData, options); // Returns the source canvas if it is not cropped.\n\n      if (!this.cropped) {\n        return source;\n      }\n\n      var _this$getData = this.getData(),\n          initialX = _this$getData.x,\n          initialY = _this$getData.y,\n          initialWidth = _this$getData.width,\n          initialHeight = _this$getData.height;\n\n      var ratio = source.width / Math.floor(canvasData.naturalWidth);\n\n      if (ratio !== 1) {\n        initialX *= ratio;\n        initialY *= ratio;\n        initialWidth *= ratio;\n        initialHeight *= ratio;\n      }\n\n      var aspectRatio = initialWidth / initialHeight;\n      var maxSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.maxWidth || Infinity,\n        height: options.maxHeight || Infinity\n      });\n      var minSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.minWidth || 0,\n        height: options.minHeight || 0\n      }, 'cover');\n\n      var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.width || (ratio !== 1 ? source.width : initialWidth),\n        height: options.height || (ratio !== 1 ? source.height : initialHeight)\n      }),\n          width = _getAdjustedSizes.width,\n          height = _getAdjustedSizes.height;\n\n      width = Math.min(maxSizes.width, Math.max(minSizes.width, width));\n      height = Math.min(maxSizes.height, Math.max(minSizes.height, height));\n      var canvas = document.createElement('canvas');\n      var context = canvas.getContext('2d');\n      canvas.width = normalizeDecimalNumber(width);\n      canvas.height = normalizeDecimalNumber(height);\n      context.fillStyle = options.fillColor || 'transparent';\n      context.fillRect(0, 0, width, height);\n      var _options$imageSmoothi = options.imageSmoothingEnabled,\n          imageSmoothingEnabled = _options$imageSmoothi === void 0 ? true : _options$imageSmoothi,\n          imageSmoothingQuality = options.imageSmoothingQuality;\n      context.imageSmoothingEnabled = imageSmoothingEnabled;\n\n      if (imageSmoothingQuality) {\n        context.imageSmoothingQuality = imageSmoothingQuality;\n      } // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D.drawImage\n\n\n      var sourceWidth = source.width;\n      var sourceHeight = source.height; // Source canvas parameters\n\n      var srcX = initialX;\n      var srcY = initialY;\n      var srcWidth;\n      var srcHeight; // Destination canvas parameters\n\n      var dstX;\n      var dstY;\n      var dstWidth;\n      var dstHeight;\n\n      if (srcX <= -initialWidth || srcX > sourceWidth) {\n        srcX = 0;\n        srcWidth = 0;\n        dstX = 0;\n        dstWidth = 0;\n      } else if (srcX <= 0) {\n        dstX = -srcX;\n        srcX = 0;\n        srcWidth = Math.min(sourceWidth, initialWidth + srcX);\n        dstWidth = srcWidth;\n      } else if (srcX <= sourceWidth) {\n        dstX = 0;\n        srcWidth = Math.min(initialWidth, sourceWidth - srcX);\n        dstWidth = srcWidth;\n      }\n\n      if (srcWidth <= 0 || srcY <= -initialHeight || srcY > sourceHeight) {\n        srcY = 0;\n        srcHeight = 0;\n        dstY = 0;\n        dstHeight = 0;\n      } else if (srcY <= 0) {\n        dstY = -srcY;\n        srcY = 0;\n        srcHeight = Math.min(sourceHeight, initialHeight + srcY);\n        dstHeight = srcHeight;\n      } else if (srcY <= sourceHeight) {\n        dstY = 0;\n        srcHeight = Math.min(initialHeight, sourceHeight - srcY);\n        dstHeight = srcHeight;\n      }\n\n      var params = [srcX, srcY, srcWidth, srcHeight]; // Avoid \"IndexSizeError\"\n\n      if (dstWidth > 0 && dstHeight > 0) {\n        var scale = width / initialWidth;\n        params.push(dstX * scale, dstY * scale, dstWidth * scale, dstHeight * scale);\n      } // All the numerical parameters should be integer for `drawImage`\n      // https://github.com/fengyuanchen/cropper/issues/476\n\n\n      context.drawImage.apply(context, [source].concat(_toConsumableArray(params.map(function (param) {\n        return Math.floor(normalizeDecimalNumber(param));\n      }))));\n      return canvas;\n    },\n\n    /**\n     * Change the aspect ratio of the crop box.\n     * @param {number} aspectRatio - The new aspect ratio.\n     * @returns {Cropper} this\n     */\n    setAspectRatio: function setAspectRatio(aspectRatio) {\n      var options = this.options;\n\n      if (!this.disabled && !isUndefined(aspectRatio)) {\n        // 0 -> NaN\n        options.aspectRatio = Math.max(0, aspectRatio) || NaN;\n\n        if (this.ready) {\n          this.initCropBox();\n\n          if (this.cropped) {\n            this.renderCropBox();\n          }\n        }\n      }\n\n      return this;\n    },\n\n    /**\n     * Change the drag mode.\n     * @param {string} mode - The new drag mode.\n     * @returns {Cropper} this\n     */\n    setDragMode: function setDragMode(mode) {\n      var options = this.options,\n          dragBox = this.dragBox,\n          face = this.face;\n\n      if (this.ready && !this.disabled) {\n        var croppable = mode === DRAG_MODE_CROP;\n        var movable = options.movable && mode === DRAG_MODE_MOVE;\n        mode = croppable || movable ? mode : DRAG_MODE_NONE;\n        options.dragMode = mode;\n        setData(dragBox, DATA_ACTION, mode);\n        toggleClass(dragBox, CLASS_CROP, croppable);\n        toggleClass(dragBox, CLASS_MOVE, movable);\n\n        if (!options.cropBoxMovable) {\n          // Sync drag mode to crop box when it is not movable\n          setData(face, DATA_ACTION, mode);\n          toggleClass(face, CLASS_CROP, croppable);\n          toggleClass(face, CLASS_MOVE, movable);\n        }\n      }\n\n      return this;\n    }\n  };\n\n  var AnotherCropper = WINDOW.Cropper;\n\n  var Cropper = /*#__PURE__*/function () {\n    /**\n     * Create a new Cropper.\n     * @param {Element} element - The target element for cropping.\n     * @param {Object} [options={}] - The configuration options.\n     */\n    function Cropper(element) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n      _classCallCheck(this, Cropper);\n\n      if (!element || !REGEXP_TAG_NAME.test(element.tagName)) {\n        throw new Error('The first argument is required and must be an <img> or <canvas> element.');\n      }\n\n      this.element = element;\n      this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n      this.cropped = false;\n      this.disabled = false;\n      this.pointers = {};\n      this.ready = false;\n      this.reloading = false;\n      this.replaced = false;\n      this.sized = false;\n      this.sizing = false;\n      this.init();\n    }\n\n    _createClass(Cropper, [{\n      key: \"init\",\n      value: function init() {\n        var element = this.element;\n        var tagName = element.tagName.toLowerCase();\n        var url;\n\n        if (element[NAMESPACE]) {\n          return;\n        }\n\n        element[NAMESPACE] = this;\n\n        if (tagName === 'img') {\n          this.isImg = true; // e.g.: \"img/picture.jpg\"\n\n          url = element.getAttribute('src') || '';\n          this.originalUrl = url; // Stop when it's a blank image\n\n          if (!url) {\n            return;\n          } // e.g.: \"https://example.com/img/picture.jpg\"\n\n\n          url = element.src;\n        } else if (tagName === 'canvas' && window.HTMLCanvasElement) {\n          url = element.toDataURL();\n        }\n\n        this.load(url);\n      }\n    }, {\n      key: \"load\",\n      value: function load(url) {\n        var _this = this;\n\n        if (!url) {\n          return;\n        }\n\n        this.url = url;\n        this.imageData = {};\n        var element = this.element,\n            options = this.options;\n\n        if (!options.rotatable && !options.scalable) {\n          options.checkOrientation = false;\n        } // Only IE10+ supports Typed Arrays\n\n\n        if (!options.checkOrientation || !window.ArrayBuffer) {\n          this.clone();\n          return;\n        } // Detect the mime type of the image directly if it is a Data URL\n\n\n        if (REGEXP_DATA_URL.test(url)) {\n          // Read ArrayBuffer from Data URL of JPEG images directly for better performance\n          if (REGEXP_DATA_URL_JPEG.test(url)) {\n            this.read(dataURLToArrayBuffer(url));\n          } else {\n            // Only a JPEG image may contains Exif Orientation information,\n            // the rest types of Data URLs are not necessary to check orientation at all.\n            this.clone();\n          }\n\n          return;\n        } // 1. Detect the mime type of the image by a XMLHttpRequest.\n        // 2. Load the image as ArrayBuffer for reading orientation if its a JPEG image.\n\n\n        var xhr = new XMLHttpRequest();\n        var clone = this.clone.bind(this);\n        this.reloading = true;\n        this.xhr = xhr; // 1. Cross origin requests are only supported for protocol schemes:\n        // http, https, data, chrome, chrome-extension.\n        // 2. Access to XMLHttpRequest from a Data URL will be blocked by CORS policy\n        // in some browsers as IE11 and Safari.\n\n        xhr.onabort = clone;\n        xhr.onerror = clone;\n        xhr.ontimeout = clone;\n\n        xhr.onprogress = function () {\n          // Abort the request directly if it not a JPEG image for better performance\n          if (xhr.getResponseHeader('content-type') !== MIME_TYPE_JPEG) {\n            xhr.abort();\n          }\n        };\n\n        xhr.onload = function () {\n          _this.read(xhr.response);\n        };\n\n        xhr.onloadend = function () {\n          _this.reloading = false;\n          _this.xhr = null;\n        }; // Bust cache when there is a \"crossOrigin\" property to avoid browser cache error\n\n\n        if (options.checkCrossOrigin && isCrossOriginURL(url) && element.crossOrigin) {\n          url = addTimestamp(url);\n        } // The third parameter is required for avoiding side-effect (#682)\n\n\n        xhr.open('GET', url, true);\n        xhr.responseType = 'arraybuffer';\n        xhr.withCredentials = element.crossOrigin === 'use-credentials';\n        xhr.send();\n      }\n    }, {\n      key: \"read\",\n      value: function read(arrayBuffer) {\n        var options = this.options,\n            imageData = this.imageData; // Reset the orientation value to its default value 1\n        // as some iOS browsers will render image with its orientation\n\n        var orientation = resetAndGetOrientation(arrayBuffer);\n        var rotate = 0;\n        var scaleX = 1;\n        var scaleY = 1;\n\n        if (orientation > 1) {\n          // Generate a new URL which has the default orientation value\n          this.url = arrayBufferToDataURL(arrayBuffer, MIME_TYPE_JPEG);\n\n          var _parseOrientation = parseOrientation(orientation);\n\n          rotate = _parseOrientation.rotate;\n          scaleX = _parseOrientation.scaleX;\n          scaleY = _parseOrientation.scaleY;\n        }\n\n        if (options.rotatable) {\n          imageData.rotate = rotate;\n        }\n\n        if (options.scalable) {\n          imageData.scaleX = scaleX;\n          imageData.scaleY = scaleY;\n        }\n\n        this.clone();\n      }\n    }, {\n      key: \"clone\",\n      value: function clone() {\n        var element = this.element,\n            url = this.url;\n        var crossOrigin = element.crossOrigin;\n        var crossOriginUrl = url;\n\n        if (this.options.checkCrossOrigin && isCrossOriginURL(url)) {\n          if (!crossOrigin) {\n            crossOrigin = 'anonymous';\n          } // Bust cache when there is not a \"crossOrigin\" property (#519)\n\n\n          crossOriginUrl = addTimestamp(url);\n        }\n\n        this.crossOrigin = crossOrigin;\n        this.crossOriginUrl = crossOriginUrl;\n        var image = document.createElement('img');\n\n        if (crossOrigin) {\n          image.crossOrigin = crossOrigin;\n        }\n\n        image.src = crossOriginUrl || url;\n        image.alt = element.alt || 'The image to crop';\n        this.image = image;\n        image.onload = this.start.bind(this);\n        image.onerror = this.stop.bind(this);\n        addClass(image, CLASS_HIDE);\n        element.parentNode.insertBefore(image, element.nextSibling);\n      }\n    }, {\n      key: \"start\",\n      value: function start() {\n        var _this2 = this;\n\n        var image = this.image;\n        image.onload = null;\n        image.onerror = null;\n        this.sizing = true; // Match all browsers that use WebKit as the layout engine in iOS devices,\n        // such as Safari for iOS, Chrome for iOS, and in-app browsers.\n\n        var isIOSWebKit = WINDOW.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);\n\n        var done = function done(naturalWidth, naturalHeight) {\n          assign(_this2.imageData, {\n            naturalWidth: naturalWidth,\n            naturalHeight: naturalHeight,\n            aspectRatio: naturalWidth / naturalHeight\n          });\n          _this2.initialImageData = assign({}, _this2.imageData);\n          _this2.sizing = false;\n          _this2.sized = true;\n\n          _this2.build();\n        }; // Most modern browsers (excepts iOS WebKit)\n\n\n        if (image.naturalWidth && !isIOSWebKit) {\n          done(image.naturalWidth, image.naturalHeight);\n          return;\n        }\n\n        var sizingImage = document.createElement('img');\n        var body = document.body || document.documentElement;\n        this.sizingImage = sizingImage;\n\n        sizingImage.onload = function () {\n          done(sizingImage.width, sizingImage.height);\n\n          if (!isIOSWebKit) {\n            body.removeChild(sizingImage);\n          }\n        };\n\n        sizingImage.src = image.src; // iOS WebKit will convert the image automatically\n        // with its orientation once append it into DOM (#279)\n\n        if (!isIOSWebKit) {\n          sizingImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n          body.appendChild(sizingImage);\n        }\n      }\n    }, {\n      key: \"stop\",\n      value: function stop() {\n        var image = this.image;\n        image.onload = null;\n        image.onerror = null;\n        image.parentNode.removeChild(image);\n        this.image = null;\n      }\n    }, {\n      key: \"build\",\n      value: function build() {\n        if (!this.sized || this.ready) {\n          return;\n        }\n\n        var element = this.element,\n            options = this.options,\n            image = this.image; // Create cropper elements\n\n        var container = element.parentNode;\n        var template = document.createElement('div');\n        template.innerHTML = TEMPLATE;\n        var cropper = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n        var canvas = cropper.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n        var dragBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-drag-box\"));\n        var cropBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-crop-box\"));\n        var face = cropBox.querySelector(\".\".concat(NAMESPACE, \"-face\"));\n        this.container = container;\n        this.cropper = cropper;\n        this.canvas = canvas;\n        this.dragBox = dragBox;\n        this.cropBox = cropBox;\n        this.viewBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-view-box\"));\n        this.face = face;\n        canvas.appendChild(image); // Hide the original image\n\n        addClass(element, CLASS_HIDDEN); // Inserts the cropper after to the current image\n\n        container.insertBefore(cropper, element.nextSibling); // Show the image if is hidden\n\n        if (!this.isImg) {\n          removeClass(image, CLASS_HIDE);\n        }\n\n        this.initPreview();\n        this.bind();\n        options.initialAspectRatio = Math.max(0, options.initialAspectRatio) || NaN;\n        options.aspectRatio = Math.max(0, options.aspectRatio) || NaN;\n        options.viewMode = Math.max(0, Math.min(3, Math.round(options.viewMode))) || 0;\n        addClass(cropBox, CLASS_HIDDEN);\n\n        if (!options.guides) {\n          addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-dashed\")), CLASS_HIDDEN);\n        }\n\n        if (!options.center) {\n          addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-center\")), CLASS_HIDDEN);\n        }\n\n        if (options.background) {\n          addClass(cropper, \"\".concat(NAMESPACE, \"-bg\"));\n        }\n\n        if (!options.highlight) {\n          addClass(face, CLASS_INVISIBLE);\n        }\n\n        if (options.cropBoxMovable) {\n          addClass(face, CLASS_MOVE);\n          setData(face, DATA_ACTION, ACTION_ALL);\n        }\n\n        if (!options.cropBoxResizable) {\n          addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-line\")), CLASS_HIDDEN);\n          addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-point\")), CLASS_HIDDEN);\n        }\n\n        this.render();\n        this.ready = true;\n        this.setDragMode(options.dragMode);\n\n        if (options.autoCrop) {\n          this.crop();\n        }\n\n        this.setData(options.data);\n\n        if (isFunction(options.ready)) {\n          addListener(element, EVENT_READY, options.ready, {\n            once: true\n          });\n        }\n\n        dispatchEvent(element, EVENT_READY);\n      }\n    }, {\n      key: \"unbuild\",\n      value: function unbuild() {\n        if (!this.ready) {\n          return;\n        }\n\n        this.ready = false;\n        this.unbind();\n        this.resetPreview();\n        this.cropper.parentNode.removeChild(this.cropper);\n        removeClass(this.element, CLASS_HIDDEN);\n      }\n    }, {\n      key: \"uncreate\",\n      value: function uncreate() {\n        if (this.ready) {\n          this.unbuild();\n          this.ready = false;\n          this.cropped = false;\n        } else if (this.sizing) {\n          this.sizingImage.onload = null;\n          this.sizing = false;\n          this.sized = false;\n        } else if (this.reloading) {\n          this.xhr.onabort = null;\n          this.xhr.abort();\n        } else if (this.image) {\n          this.stop();\n        }\n      }\n      /**\n       * Get the no conflict cropper class.\n       * @returns {Cropper} The cropper class.\n       */\n\n    }], [{\n      key: \"noConflict\",\n      value: function noConflict() {\n        window.Cropper = AnotherCropper;\n        return Cropper;\n      }\n      /**\n       * Change the default options.\n       * @param {Object} options - The new default options.\n       */\n\n    }, {\n      key: \"setDefaults\",\n      value: function setDefaults(options) {\n        assign(DEFAULTS, isPlainObject(options) && options);\n      }\n    }]);\n\n    return Cropper;\n  }();\n\n  assign(Cropper.prototype, render, preview, events, handlers, change, methods);\n\n  return Cropper;\n\n})));\n", "/* global window, exports, define */\n\n!function() {\n    'use strict'\n\n    var re = {\n        not_string: /[^s]/,\n        not_bool: /[^t]/,\n        not_type: /[^T]/,\n        not_primitive: /[^v]/,\n        number: /[diefg]/,\n        numeric_arg: /[bcdiefguxX]/,\n        json: /[j]/,\n        not_json: /[^j]/,\n        text: /^[^\\x25]+/,\n        modulo: /^\\x25{2}/,\n        placeholder: /^\\x25(?:([1-9]\\d*)\\$|\\(([^)]+)\\))?(\\+)?(0|'[^$])?(-)?(\\d+)?(?:\\.(\\d+))?([b-gijostTuvxX])/,\n        key: /^([a-z_][a-z_\\d]*)/i,\n        key_access: /^\\.([a-z_][a-z_\\d]*)/i,\n        index_access: /^\\[(\\d+)\\]/,\n        sign: /^[+-]/\n    }\n\n    function sprintf(key) {\n        // `arguments` is not an array, but should be fine for this call\n        return sprintf_format(sprintf_parse(key), arguments)\n    }\n\n    function vsprintf(fmt, argv) {\n        return sprintf.apply(null, [fmt].concat(argv || []))\n    }\n\n    function sprintf_format(parse_tree, argv) {\n        var cursor = 1, tree_length = parse_tree.length, arg, output = '', i, k, ph, pad, pad_character, pad_length, is_positive, sign\n        for (i = 0; i < tree_length; i++) {\n            if (typeof parse_tree[i] === 'string') {\n                output += parse_tree[i]\n            }\n            else if (typeof parse_tree[i] === 'object') {\n                ph = parse_tree[i] // convenience purposes only\n                if (ph.keys) { // keyword argument\n                    arg = argv[cursor]\n                    for (k = 0; k < ph.keys.length; k++) {\n                        if (arg == undefined) {\n                            throw new Error(sprintf('[sprintf] Cannot access property \"%s\" of undefined value \"%s\"', ph.keys[k], ph.keys[k-1]))\n                        }\n                        arg = arg[ph.keys[k]]\n                    }\n                }\n                else if (ph.param_no) { // positional argument (explicit)\n                    arg = argv[ph.param_no]\n                }\n                else { // positional argument (implicit)\n                    arg = argv[cursor++]\n                }\n\n                if (re.not_type.test(ph.type) && re.not_primitive.test(ph.type) && arg instanceof Function) {\n                    arg = arg()\n                }\n\n                if (re.numeric_arg.test(ph.type) && (typeof arg !== 'number' && isNaN(arg))) {\n                    throw new TypeError(sprintf('[sprintf] expecting number but found %T', arg))\n                }\n\n                if (re.number.test(ph.type)) {\n                    is_positive = arg >= 0\n                }\n\n                switch (ph.type) {\n                    case 'b':\n                        arg = parseInt(arg, 10).toString(2)\n                        break\n                    case 'c':\n                        arg = String.fromCharCode(parseInt(arg, 10))\n                        break\n                    case 'd':\n                    case 'i':\n                        arg = parseInt(arg, 10)\n                        break\n                    case 'j':\n                        arg = JSON.stringify(arg, null, ph.width ? parseInt(ph.width) : 0)\n                        break\n                    case 'e':\n                        arg = ph.precision ? parseFloat(arg).toExponential(ph.precision) : parseFloat(arg).toExponential()\n                        break\n                    case 'f':\n                        arg = ph.precision ? parseFloat(arg).toFixed(ph.precision) : parseFloat(arg)\n                        break\n                    case 'g':\n                        arg = ph.precision ? String(Number(arg.toPrecision(ph.precision))) : parseFloat(arg)\n                        break\n                    case 'o':\n                        arg = (parseInt(arg, 10) >>> 0).toString(8)\n                        break\n                    case 's':\n                        arg = String(arg)\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 't':\n                        arg = String(!!arg)\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 'T':\n                        arg = Object.prototype.toString.call(arg).slice(8, -1).toLowerCase()\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 'u':\n                        arg = parseInt(arg, 10) >>> 0\n                        break\n                    case 'v':\n                        arg = arg.valueOf()\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 'x':\n                        arg = (parseInt(arg, 10) >>> 0).toString(16)\n                        break\n                    case 'X':\n                        arg = (parseInt(arg, 10) >>> 0).toString(16).toUpperCase()\n                        break\n                }\n                if (re.json.test(ph.type)) {\n                    output += arg\n                }\n                else {\n                    if (re.number.test(ph.type) && (!is_positive || ph.sign)) {\n                        sign = is_positive ? '+' : '-'\n                        arg = arg.toString().replace(re.sign, '')\n                    }\n                    else {\n                        sign = ''\n                    }\n                    pad_character = ph.pad_char ? ph.pad_char === '0' ? '0' : ph.pad_char.charAt(1) : ' '\n                    pad_length = ph.width - (sign + arg).length\n                    pad = ph.width ? (pad_length > 0 ? pad_character.repeat(pad_length) : '') : ''\n                    output += ph.align ? sign + arg + pad : (pad_character === '0' ? sign + pad + arg : pad + sign + arg)\n                }\n            }\n        }\n        return output\n    }\n\n    var sprintf_cache = Object.create(null)\n\n    function sprintf_parse(fmt) {\n        if (sprintf_cache[fmt]) {\n            return sprintf_cache[fmt]\n        }\n\n        var _fmt = fmt, match, parse_tree = [], arg_names = 0\n        while (_fmt) {\n            if ((match = re.text.exec(_fmt)) !== null) {\n                parse_tree.push(match[0])\n            }\n            else if ((match = re.modulo.exec(_fmt)) !== null) {\n                parse_tree.push('%')\n            }\n            else if ((match = re.placeholder.exec(_fmt)) !== null) {\n                if (match[2]) {\n                    arg_names |= 1\n                    var field_list = [], replacement_field = match[2], field_match = []\n                    if ((field_match = re.key.exec(replacement_field)) !== null) {\n                        field_list.push(field_match[1])\n                        while ((replacement_field = replacement_field.substring(field_match[0].length)) !== '') {\n                            if ((field_match = re.key_access.exec(replacement_field)) !== null) {\n                                field_list.push(field_match[1])\n                            }\n                            else if ((field_match = re.index_access.exec(replacement_field)) !== null) {\n                                field_list.push(field_match[1])\n                            }\n                            else {\n                                throw new SyntaxError('[sprintf] failed to parse named argument key')\n                            }\n                        }\n                    }\n                    else {\n                        throw new SyntaxError('[sprintf] failed to parse named argument key')\n                    }\n                    match[2] = field_list\n                }\n                else {\n                    arg_names |= 2\n                }\n                if (arg_names === 3) {\n                    throw new Error('[sprintf] mixing positional and named placeholders is not (yet) supported')\n                }\n\n                parse_tree.push(\n                    {\n                        placeholder: match[0],\n                        param_no:    match[1],\n                        keys:        match[2],\n                        sign:        match[3],\n                        pad_char:    match[4],\n                        align:       match[5],\n                        width:       match[6],\n                        precision:   match[7],\n                        type:        match[8]\n                    }\n                )\n            }\n            else {\n                throw new SyntaxError('[sprintf] unexpected placeholder')\n            }\n            _fmt = _fmt.substring(match[0].length)\n        }\n        return sprintf_cache[fmt] = parse_tree\n    }\n\n    /**\n     * export to either browser or node.js\n     */\n    /* eslint-disable quote-props */\n    if (typeof exports !== 'undefined') {\n        exports['sprintf'] = sprintf\n        exports['vsprintf'] = vsprintf\n    }\n    if (typeof window !== 'undefined') {\n        window['sprintf'] = sprintf\n        window['vsprintf'] = vsprintf\n\n        if (typeof define === 'function' && define['amd']) {\n            define(function() {\n                return {\n                    'sprintf': sprintf,\n                    'vsprintf': vsprintf\n                }\n            })\n        }\n    }\n    /* eslint-enable quote-props */\n}(); // eslint-disable-line\n", "'use strict';\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Factory for creating new instances\naxios.create = function create(instanceConfig) {\n  return createInstance(mergeConfig(axios.defaults, instanceConfig));\n};\n\n// Expose Cancel & CancelToken\naxios.Cancel = require('./cancel/Cancel');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;\n", "'use strict';\n\nvar utils = require('./../utils');\nvar buildURL = require('../helpers/buildURL');\nvar InterceptorManager = require('./InterceptorManager');\nvar dispatchRequest = require('./dispatchRequest');\nvar mergeConfig = require('./mergeConfig');\n\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n */\nfunction Axios(instanceConfig) {\n  this.defaults = instanceConfig;\n  this.interceptors = {\n    request: new InterceptorManager(),\n    response: new InterceptorManager()\n  };\n}\n\n/**\n * Dispatch a request\n *\n * @param {Object} config The config specific for this request (merged with this.defaults)\n */\nAxios.prototype.request = function request(config) {\n  /*eslint no-param-reassign:0*/\n  // Allow for axios('example/url'[, config]) a la fetch API\n  if (typeof config === 'string') {\n    config = arguments[1] || {};\n    config.url = arguments[0];\n  } else {\n    config = config || {};\n  }\n\n  config = mergeConfig(this.defaults, config);\n\n  // Set config.method\n  if (config.method) {\n    config.method = config.method.toLowerCase();\n  } else if (this.defaults.method) {\n    config.method = this.defaults.method.toLowerCase();\n  } else {\n    config.method = 'get';\n  }\n\n  // Hook up interceptors middleware\n  var chain = [dispatchRequest, undefined];\n  var promise = Promise.resolve(config);\n\n  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n    chain.unshift(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n    chain.push(interceptor.fulfilled, interceptor.rejected);\n  });\n\n  while (chain.length) {\n    promise = promise.then(chain.shift(), chain.shift());\n  }\n\n  return promise;\n};\n\nAxios.prototype.getUri = function getUri(config) {\n  config = mergeConfig(this.defaults, config);\n  return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\\?/, '');\n};\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, data, config) {\n    return this.request(mergeConfig(config || {}, {\n      method: method,\n      url: url,\n      data: data\n    }));\n  };\n});\n\nmodule.exports = Axios;\n", "'use strict';\n\nvar utils = require('./../utils');\n\nfunction InterceptorManager() {\n  this.handlers = [];\n}\n\n/**\n * Add a new interceptor to the stack\n *\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\n * @param {Function} rejected The function to handle `reject` for a `Promise`\n *\n * @return {Number} An ID used to remove interceptor later\n */\nInterceptorManager.prototype.use = function use(fulfilled, rejected) {\n  this.handlers.push({\n    fulfilled: fulfilled,\n    rejected: rejected\n  });\n  return this.handlers.length - 1;\n};\n\n/**\n * Remove an interceptor from the stack\n *\n * @param {Number} id The ID that was returned by `use`\n */\nInterceptorManager.prototype.eject = function eject(id) {\n  if (this.handlers[id]) {\n    this.handlers[id] = null;\n  }\n};\n\n/**\n * Iterate over all the registered interceptors\n *\n * This method is particularly useful for skipping over any\n * interceptors that may have become `null` calling `eject`.\n *\n * @param {Function} fn The function to call for each interceptor\n */\nInterceptorManager.prototype.forEach = function forEach(fn) {\n  utils.forEach(this.handlers, function forEachHandler(h) {\n    if (h !== null) {\n      fn(h);\n    }\n  });\n};\n\nmodule.exports = InterceptorManager;\n", "'use strict';\n\nvar utils = require('./../utils');\nvar transformData = require('./transformData');\nvar isCancel = require('../cancel/isCancel');\nvar defaults = require('../defaults');\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n * @returns {Promise} The Promise to be fulfilled\n */\nmodule.exports = function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  // Ensure headers exist\n  config.headers = config.headers || {};\n\n  // Transform request data\n  config.data = transformData(\n    config.data,\n    config.headers,\n    config.transformRequest\n  );\n\n  // Flatten headers\n  config.headers = utils.merge(\n    config.headers.common || {},\n    config.headers[config.method] || {},\n    config.headers\n  );\n\n  utils.forEach(\n    ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n    function cleanHeaderConfig(method) {\n      delete config.headers[method];\n    }\n  );\n\n  var adapter = config.adapter || defaults.adapter;\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData(\n      response.data,\n      response.headers,\n      config.transformResponse\n    );\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData(\n          reason.response.data,\n          reason.response.headers,\n          config.transformResponse\n        );\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn(data, headers);\n  });\n\n  return data;\n};\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "'use strict';\n\nvar utils = require('../utils');\n\nmodule.exports = function normalizeHeaderName(headers, normalizedName) {\n  utils.forEach(headers, function processHeader(value, name) {\n    if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {\n      headers[normalizedName] = value;\n      delete headers[name];\n    }\n  });\n};\n", "'use strict';\n\nvar createError = require('./createError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(createError(\n      'Request failed with status code ' + response.status,\n      response.config,\n      null,\n      response.request,\n      response\n    ));\n  }\n};\n", "'use strict';\n\n/**\n * Update an Error with the specified config, error code, and response.\n *\n * @param {Error} error The error to update.\n * @param {Object} config The config.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The error.\n */\nmodule.exports = function enhanceError(error, config, code, request, response) {\n  error.config = config;\n  if (code) {\n    error.code = code;\n  }\n\n  error.request = request;\n  error.response = response;\n  error.isAxiosError = true;\n\n  error.toJSON = function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code\n    };\n  };\n  return error;\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs support document.cookie\n    (function standardBrowserEnv() {\n      return {\n        write: function write(name, value, expires, path, domain, secure) {\n          var cookie = [];\n          cookie.push(name + '=' + encodeURIComponent(value));\n\n          if (utils.isNumber(expires)) {\n            cookie.push('expires=' + new Date(expires).toGMTString());\n          }\n\n          if (utils.isString(path)) {\n            cookie.push('path=' + path);\n          }\n\n          if (utils.isString(domain)) {\n            cookie.push('domain=' + domain);\n          }\n\n          if (secure === true) {\n            cookie.push('secure');\n          }\n\n          document.cookie = cookie.join('; ');\n        },\n\n        read: function read(name) {\n          var match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n          return (match ? decodeURIComponent(match[3]) : null);\n        },\n\n        remove: function remove(name) {\n          this.write(name, '', Date.now() - 86400000);\n        }\n      };\n    })() :\n\n  // Non standard browser env (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return {\n        write: function write() {},\n        read: function read() { return null; },\n        remove: function remove() {}\n      };\n    })()\n);\n", "'use strict';\n\nvar isAbsoluteURL = require('../helpers/isAbsoluteURL');\nvar combineURLs = require('../helpers/combineURLs');\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n * @returns {string} The combined full path\n */\nmodule.exports = function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n};\n", "'use strict';\n\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */\nmodule.exports = function isAbsoluteURL(url) {\n  // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n  // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n  // by any combination of letters, digits, plus, period, or hyphen.\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url);\n};\n", "'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n * @returns {string} The combined URL\n */\nmodule.exports = function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\n// Headers whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nvar ignoreDuplicateOf = [\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n];\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} headers Headers needing to be parsed\n * @returns {Object} Headers parsed into an object\n */\nmodule.exports = function parseHeaders(headers) {\n  var parsed = {};\n  var key;\n  var val;\n  var i;\n\n  if (!headers) { return parsed; }\n\n  utils.forEach(headers.split('\\n'), function parser(line) {\n    i = line.indexOf(':');\n    key = utils.trim(line.substr(0, i)).toLowerCase();\n    val = utils.trim(line.substr(i + 1));\n\n    if (key) {\n      if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {\n        return;\n      }\n      if (key === 'set-cookie') {\n        parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);\n      } else {\n        parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n      }\n    }\n  });\n\n  return parsed;\n};\n", "'use strict';\n\nvar utils = require('./../utils');\n\nmodule.exports = (\n  utils.isStandardBrowserEnv() ?\n\n  // Standard browser envs have full support of the APIs needed to test\n  // whether the request URL is of the same origin as current location.\n    (function standardBrowserEnv() {\n      var msie = /(msie|trident)/i.test(navigator.userAgent);\n      var urlParsingNode = document.createElement('a');\n      var originURL;\n\n      /**\n    * Parse a URL to discover it's components\n    *\n    * @param {String} url The URL to be parsed\n    * @returns {Object}\n    */\n      function resolveURL(url) {\n        var href = url;\n\n        if (msie) {\n        // IE needs attribute set twice to normalize properties\n          urlParsingNode.setAttribute('href', href);\n          href = urlParsingNode.href;\n        }\n\n        urlParsingNode.setAttribute('href', href);\n\n        // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n        return {\n          href: urlParsingNode.href,\n          protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n          host: urlParsingNode.host,\n          search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n          hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n          hostname: urlParsingNode.hostname,\n          port: urlParsingNode.port,\n          pathname: (urlParsingNode.pathname.charAt(0) === '/') ?\n            urlParsingNode.pathname :\n            '/' + urlParsingNode.pathname\n        };\n      }\n\n      originURL = resolveURL(window.location.href);\n\n      /**\n    * Determine if a URL shares the same origin as the current location\n    *\n    * @param {String} requestURL The URL to test\n    * @returns {boolean} True if URL shares the same origin, otherwise false\n    */\n      return function isURLSameOrigin(requestURL) {\n        var parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;\n        return (parsed.protocol === originURL.protocol &&\n            parsed.host === originURL.host);\n      };\n    })() :\n\n  // Non standard browser envs (web workers, react-native) lack needed support.\n    (function nonStandardBrowserEnv() {\n      return function isURLSameOrigin() {\n        return true;\n      };\n    })()\n);\n", "'use strict';\n\nvar Cancel = require('./Cancel');\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @class\n * @param {Function} executor The executor function.\n */\nfunction CancelToken(executor) {\n  if (typeof executor !== 'function') {\n    throw new TypeError('executor must be a function.');\n  }\n\n  var resolvePromise;\n  this.promise = new Promise(function promiseExecutor(resolve) {\n    resolvePromise = resolve;\n  });\n\n  var token = this;\n  executor(function cancel(message) {\n    if (token.reason) {\n      // Cancellation has already been requested\n      return;\n    }\n\n    token.reason = new Cancel(message);\n    resolvePromise(token.reason);\n  });\n}\n\n/**\n * Throws a `Cancel` if cancellation has been requested.\n */\nCancelToken.prototype.throwIfRequested = function throwIfRequested() {\n  if (this.reason) {\n    throw this.reason;\n  }\n};\n\n/**\n * Returns an object that contains a new `CancelToken` and a function that, when called,\n * cancels the `CancelToken`.\n */\nCancelToken.source = function source() {\n  var cancel;\n  var token = new CancelToken(function executor(c) {\n    cancel = c;\n  });\n  return {\n    token: token,\n    cancel: cancel\n  };\n};\n\nmodule.exports = CancelToken;\n", "'use strict';\n\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n * @returns {Function}\n */\nmodule.exports = function spread(callback) {\n  return function wrap(arr) {\n    return callback.apply(null, arr);\n  };\n};\n", "'use strict';\n\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\n\nvar arrayPrefixGenerators = {\n    brackets: function brackets(prefix) {\n        return prefix + '[]';\n    },\n    comma: 'comma',\n    indices: function indices(prefix, key) {\n        return prefix + '[' + key + ']';\n    },\n    repeat: function repeat(prefix) {\n        return prefix;\n    }\n};\n\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n    push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\n\nvar toISO = Date.prototype.toISOString;\n\nvar defaultFormat = formats['default'];\nvar defaults = {\n    addQueryPrefix: false,\n    allowDots: false,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    delimiter: '&',\n    encode: true,\n    encoder: utils.encode,\n    encodeValuesOnly: false,\n    format: defaultFormat,\n    formatter: formats.formatters[defaultFormat],\n    // deprecated\n    indices: false,\n    serializeDate: function serializeDate(date) {\n        return toISO.call(date);\n    },\n    skipNulls: false,\n    strictNullHandling: false\n};\n\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n    return typeof v === 'string'\n        || typeof v === 'number'\n        || typeof v === 'boolean'\n        || typeof v === 'symbol'\n        || typeof v === 'bigint';\n};\n\nvar stringify = function stringify(\n    object,\n    prefix,\n    generateArrayPrefix,\n    strictNullHandling,\n    skipNulls,\n    encoder,\n    filter,\n    sort,\n    allowDots,\n    serializeDate,\n    format,\n    formatter,\n    encodeValuesOnly,\n    charset,\n    sideChannel\n) {\n    var obj = object;\n\n    if (sideChannel.has(object)) {\n        throw new RangeError('Cyclic object value');\n    }\n\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    } else if (obj instanceof Date) {\n        obj = serializeDate(obj);\n    } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        obj = utils.maybeMap(obj, function (value) {\n            if (value instanceof Date) {\n                return serializeDate(value);\n            }\n            return value;\n        });\n    }\n\n    if (obj === null) {\n        if (strictNullHandling) {\n            return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n        if (encoder) {\n            var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n            return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n        }\n        return [formatter(prefix) + '=' + formatter(String(obj))];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        // we need to join elements in\n        objKeys = [{ value: obj.length > 0 ? obj.join(',') || null : undefined }];\n    } else if (isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n        var value = typeof key === 'object' && key.value !== undefined ? key.value : obj[key];\n\n        if (skipNulls && value === null) {\n            continue;\n        }\n\n        var keyPrefix = isArray(obj)\n            ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(prefix, key) : prefix\n            : prefix + (allowDots ? '.' + key : '[' + key + ']');\n\n        sideChannel.set(object, true);\n        var valueSideChannel = getSideChannel();\n        pushToArray(values, stringify(\n            value,\n            keyPrefix,\n            generateArrayPrefix,\n            strictNullHandling,\n            skipNulls,\n            encoder,\n            filter,\n            sort,\n            allowDots,\n            serializeDate,\n            format,\n            formatter,\n            encodeValuesOnly,\n            charset,\n            valueSideChannel\n        ));\n    }\n\n    return values;\n};\n\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (opts.encoder !== null && opts.encoder !== undefined && typeof opts.encoder !== 'function') {\n        throw new TypeError('Encoder has to be a function.');\n    }\n\n    var charset = opts.charset || defaults.charset;\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    var format = formats['default'];\n    if (typeof opts.format !== 'undefined') {\n        if (!has.call(formats.formatters, opts.format)) {\n            throw new TypeError('Unknown format option provided.');\n        }\n        format = opts.format;\n    }\n    var formatter = formats.formatters[format];\n\n    var filter = defaults.filter;\n    if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n        filter = opts.filter;\n    }\n\n    return {\n        addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n        allowDots: typeof opts.allowDots === 'undefined' ? defaults.allowDots : !!opts.allowDots,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n        encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n        encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n        encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n        filter: filter,\n        format: format,\n        formatter: formatter,\n        serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n        skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n        sort: typeof opts.sort === 'function' ? opts.sort : null,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (object, opts) {\n    var obj = object;\n    var options = normalizeStringifyOptions(opts);\n\n    var objKeys;\n    var filter;\n\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    } else if (isArray(options.filter)) {\n        filter = options.filter;\n        objKeys = filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' || obj === null) {\n        return '';\n    }\n\n    var arrayFormat;\n    if (opts && opts.arrayFormat in arrayPrefixGenerators) {\n        arrayFormat = opts.arrayFormat;\n    } else if (opts && 'indices' in opts) {\n        arrayFormat = opts.indices ? 'indices' : 'repeat';\n    } else {\n        arrayFormat = 'indices';\n    }\n\n    var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (options.sort) {\n        objKeys.sort(options.sort);\n    }\n\n    var sideChannel = getSideChannel();\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n\n        if (options.skipNulls && obj[key] === null) {\n            continue;\n        }\n        pushToArray(keys, stringify(\n            obj[key],\n            key,\n            generateArrayPrefix,\n            options.strictNullHandling,\n            options.skipNulls,\n            options.encode ? options.encoder : null,\n            options.filter,\n            options.sort,\n            options.allowDots,\n            options.serializeDate,\n            options.format,\n            options.formatter,\n            options.encodeValuesOnly,\n            options.charset,\n            sideChannel\n        ));\n    }\n\n    var joined = keys.join(options.delimiter);\n    var prefix = options.addQueryPrefix === true ? '?' : '';\n\n    if (options.charsetSentinel) {\n        if (options.charset === 'iso-8859-1') {\n            // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n            prefix += 'utf8=%26%2310003%3B&';\n        } else {\n            // encodeURIComponent('✓')\n            prefix += 'utf8=%E2%9C%93&';\n        }\n    }\n\n    return joined.length > 0 ? prefix + joined : '';\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bind/callBound');\nvar inspect = require('object-inspect');\n\nvar $TypeError = GetIntrinsic('%TypeError%');\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\nvar $Map = GetIntrinsic('%Map%', true);\n\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\nvar $mapGet = callBound('Map.prototype.get', true);\nvar $mapSet = callBound('Map.prototype.set', true);\nvar $mapHas = callBound('Map.prototype.has', true);\n\n/*\n * This function traverses the list returning the node corresponding to the\n * given key.\n *\n * That node is also moved to the head of the list, so that if it's accessed\n * again we don't need to traverse the whole list. By doing so, all the recently\n * used nodes can be accessed relatively quickly.\n */\nvar listGetNode = function (list, key) { // eslint-disable-line consistent-return\n\tfor (var prev = list, curr; (curr = prev.next) !== null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tcurr.next = list.next;\n\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\nvar listGet = function (objects, key) {\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = { // eslint-disable-line no-param-reassign\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t};\n\t}\n};\nvar listHas = function (objects, key) {\n\treturn !!listGetNode(objects, key);\n};\n\nmodule.exports = function getSideChannel() {\n\tvar $wm;\n\tvar $m;\n\tvar $o;\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\tget: function (key) { // eslint-disable-line consistent-return\n\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\tif ($wm) {\n\t\t\t\t\treturn $weakMapGet($wm, key);\n\t\t\t\t}\n\t\t\t} else if ($Map) {\n\t\t\t\tif ($m) {\n\t\t\t\t\treturn $mapGet($m, key);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif ($o) { // eslint-disable-line no-lonely-if\n\t\t\t\t\treturn listGet($o, key);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\thas: function (key) {\n\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\tif ($wm) {\n\t\t\t\t\treturn $weakMapHas($wm, key);\n\t\t\t\t}\n\t\t\t} else if ($Map) {\n\t\t\t\tif ($m) {\n\t\t\t\t\treturn $mapHas($m, key);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif ($o) { // eslint-disable-line no-lonely-if\n\t\t\t\t\treturn listHas($o, key);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\tif (!$wm) {\n\t\t\t\t\t$wm = new $WeakMap();\n\t\t\t\t}\n\t\t\t\t$weakMapSet($wm, key, value);\n\t\t\t} else if ($Map) {\n\t\t\t\tif (!$m) {\n\t\t\t\t\t$m = new $Map();\n\t\t\t\t}\n\t\t\t\t$mapSet($m, key, value);\n\t\t\t} else {\n\t\t\t\tif (!$o) {\n\t\t\t\t\t/*\n\t\t\t\t\t * Initialize the linked list as an empty node, so that we don't have\n\t\t\t\t\t * to special-case handling of the first node: we can always refer to\n\t\t\t\t\t * it as (previous node).next, instead of something like (list).head\n\t\t\t\t\t */\n\t\t\t\t\t$o = { key: {}, next: null };\n\t\t\t\t}\n\t\t\t\tlistSet($o, key, value);\n\t\t\t}\n\t\t}\n\t};\n\treturn channel;\n};\n", "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n", "'use strict';\n\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (sym in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\tvar descriptor = Object.getOwnPropertyDescriptor(obj, sym);\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n", "'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar slice = Array.prototype.slice;\nvar toStr = Object.prototype.toString;\nvar funcType = '[object Function]';\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.call(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slice.call(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                args.concat(slice.call(arguments))\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        } else {\n            return target.apply(\n                that,\n                args.concat(slice.call(arguments))\n            );\n        }\n    };\n\n    var boundLength = Math.max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs.push('$' + i);\n    }\n\n    bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n", "'use strict';\n\nvar bind = require('function-bind');\n\nmodule.exports = bind.call(Function.call, Object.prototype.hasOwnProperty);\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBind = require('./');\n\nvar $indexOf = callBind(GetIntrinsic('String.prototype.indexOf'));\n\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\tvar intrinsic = GetIntrinsic(name, !!allowMissing);\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBind(intrinsic);\n\t}\n\treturn intrinsic;\n};\n", "'use strict';\n\nvar bind = require('function-bind');\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\n\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\nvar $defineProperty = GetIntrinsic('%Object.defineProperty%', true);\nvar $max = GetIntrinsic('%Math.max%');\n\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = null;\n\t}\n}\n\nmodule.exports = function callBind(originalFunction) {\n\tvar func = $reflectApply(bind, $call, arguments);\n\tif ($gOPD && $defineProperty) {\n\t\tvar desc = $gOPD(func, 'length');\n\t\tif (desc.configurable) {\n\t\t\t// original length, plus the receiver, minus any additional arguments (after the receiver)\n\t\t\t$defineProperty(\n\t\t\t\tfunc,\n\t\t\t\t'length',\n\t\t\t\t{ value: 1 + $max(0, originalFunction.length - (arguments.length - 1)) }\n\t\t\t);\n\t\t}\n\t}\n\treturn func;\n};\n\nvar applyBind = function applyBind() {\n\treturn $reflectApply(bind, $apply, arguments);\n};\n\nif ($defineProperty) {\n\t$defineProperty(module.exports, 'apply', { value: applyBind });\n} else {\n\tmodule.exports.apply = applyBind;\n}\n", "var hasMap = typeof Map === 'function' && Map.prototype;\nvar mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, 'size') : null;\nvar mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === 'function' ? mapSizeDescriptor.get : null;\nvar mapForEach = hasMap && Map.prototype.forEach;\nvar hasSet = typeof Set === 'function' && Set.prototype;\nvar setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, 'size') : null;\nvar setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === 'function' ? setSizeDescriptor.get : null;\nvar setForEach = hasSet && Set.prototype.forEach;\nvar hasWeakMap = typeof WeakMap === 'function' && WeakMap.prototype;\nvar weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;\nvar hasWeakSet = typeof WeakSet === 'function' && WeakSet.prototype;\nvar weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;\nvar hasWeakRef = typeof WeakRef === 'function' && WeakRef.prototype;\nvar weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;\nvar booleanValueOf = Boolean.prototype.valueOf;\nvar objectToString = Object.prototype.toString;\nvar functionToString = Function.prototype.toString;\nvar match = String.prototype.match;\nvar bigIntValueOf = typeof BigInt === 'function' ? BigInt.prototype.valueOf : null;\nvar gOPS = Object.getOwnPropertySymbols;\nvar symToString = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? Symbol.prototype.toString : null;\nvar hasShammedSymbols = typeof Symbol === 'function' && typeof Symbol.iterator === 'object';\nvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\nvar gPO = (typeof Reflect === 'function' ? Reflect.getPrototypeOf : Object.getPrototypeOf) || (\n    [].__proto__ === Array.prototype // eslint-disable-line no-proto\n        ? function (O) {\n            return O.__proto__; // eslint-disable-line no-proto\n        }\n        : null\n);\n\nvar inspectCustom = require('./util.inspect').custom;\nvar inspectSymbol = inspectCustom && isSymbol(inspectCustom) ? inspectCustom : null;\nvar toStringTag = typeof Symbol === 'function' && typeof Symbol.toStringTag !== 'undefined' ? Symbol.toStringTag : null;\n\nmodule.exports = function inspect_(obj, options, depth, seen) {\n    var opts = options || {};\n\n    if (has(opts, 'quoteStyle') && (opts.quoteStyle !== 'single' && opts.quoteStyle !== 'double')) {\n        throw new TypeError('option \"quoteStyle\" must be \"single\" or \"double\"');\n    }\n    if (\n        has(opts, 'maxStringLength') && (typeof opts.maxStringLength === 'number'\n            ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity\n            : opts.maxStringLength !== null\n        )\n    ) {\n        throw new TypeError('option \"maxStringLength\", if provided, must be a positive integer, Infinity, or `null`');\n    }\n    var customInspect = has(opts, 'customInspect') ? opts.customInspect : true;\n    if (typeof customInspect !== 'boolean' && customInspect !== 'symbol') {\n        throw new TypeError('option \"customInspect\", if provided, must be `true`, `false`, or `\\'symbol\\'`');\n    }\n\n    if (\n        has(opts, 'indent')\n        && opts.indent !== null\n        && opts.indent !== '\\t'\n        && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)\n    ) {\n        throw new TypeError('options \"indent\" must be \"\\\\t\", an integer > 0, or `null`');\n    }\n\n    if (typeof obj === 'undefined') {\n        return 'undefined';\n    }\n    if (obj === null) {\n        return 'null';\n    }\n    if (typeof obj === 'boolean') {\n        return obj ? 'true' : 'false';\n    }\n\n    if (typeof obj === 'string') {\n        return inspectString(obj, opts);\n    }\n    if (typeof obj === 'number') {\n        if (obj === 0) {\n            return Infinity / obj > 0 ? '0' : '-0';\n        }\n        return String(obj);\n    }\n    if (typeof obj === 'bigint') {\n        return String(obj) + 'n';\n    }\n\n    var maxDepth = typeof opts.depth === 'undefined' ? 5 : opts.depth;\n    if (typeof depth === 'undefined') { depth = 0; }\n    if (depth >= maxDepth && maxDepth > 0 && typeof obj === 'object') {\n        return isArray(obj) ? '[Array]' : '[Object]';\n    }\n\n    var indent = getIndent(opts, depth);\n\n    if (typeof seen === 'undefined') {\n        seen = [];\n    } else if (indexOf(seen, obj) >= 0) {\n        return '[Circular]';\n    }\n\n    function inspect(value, from, noIndent) {\n        if (from) {\n            seen = seen.slice();\n            seen.push(from);\n        }\n        if (noIndent) {\n            var newOpts = {\n                depth: opts.depth\n            };\n            if (has(opts, 'quoteStyle')) {\n                newOpts.quoteStyle = opts.quoteStyle;\n            }\n            return inspect_(value, newOpts, depth + 1, seen);\n        }\n        return inspect_(value, opts, depth + 1, seen);\n    }\n\n    if (typeof obj === 'function') {\n        var name = nameOf(obj);\n        var keys = arrObjKeys(obj, inspect);\n        return '[Function' + (name ? ': ' + name : ' (anonymous)') + ']' + (keys.length > 0 ? ' { ' + keys.join(', ') + ' }' : '');\n    }\n    if (isSymbol(obj)) {\n        var symString = hasShammedSymbols ? String(obj).replace(/^(Symbol\\(.*\\))_[^)]*$/, '$1') : symToString.call(obj);\n        return typeof obj === 'object' && !hasShammedSymbols ? markBoxed(symString) : symString;\n    }\n    if (isElement(obj)) {\n        var s = '<' + String(obj.nodeName).toLowerCase();\n        var attrs = obj.attributes || [];\n        for (var i = 0; i < attrs.length; i++) {\n            s += ' ' + attrs[i].name + '=' + wrapQuotes(quote(attrs[i].value), 'double', opts);\n        }\n        s += '>';\n        if (obj.childNodes && obj.childNodes.length) { s += '...'; }\n        s += '</' + String(obj.nodeName).toLowerCase() + '>';\n        return s;\n    }\n    if (isArray(obj)) {\n        if (obj.length === 0) { return '[]'; }\n        var xs = arrObjKeys(obj, inspect);\n        if (indent && !singleLineValues(xs)) {\n            return '[' + indentedJoin(xs, indent) + ']';\n        }\n        return '[ ' + xs.join(', ') + ' ]';\n    }\n    if (isError(obj)) {\n        var parts = arrObjKeys(obj, inspect);\n        if (parts.length === 0) { return '[' + String(obj) + ']'; }\n        return '{ [' + String(obj) + '] ' + parts.join(', ') + ' }';\n    }\n    if (typeof obj === 'object' && customInspect) {\n        if (inspectSymbol && typeof obj[inspectSymbol] === 'function') {\n            return obj[inspectSymbol]();\n        } else if (customInspect !== 'symbol' && typeof obj.inspect === 'function') {\n            return obj.inspect();\n        }\n    }\n    if (isMap(obj)) {\n        var mapParts = [];\n        mapForEach.call(obj, function (value, key) {\n            mapParts.push(inspect(key, obj, true) + ' => ' + inspect(value, obj));\n        });\n        return collectionOf('Map', mapSize.call(obj), mapParts, indent);\n    }\n    if (isSet(obj)) {\n        var setParts = [];\n        setForEach.call(obj, function (value) {\n            setParts.push(inspect(value, obj));\n        });\n        return collectionOf('Set', setSize.call(obj), setParts, indent);\n    }\n    if (isWeakMap(obj)) {\n        return weakCollectionOf('WeakMap');\n    }\n    if (isWeakSet(obj)) {\n        return weakCollectionOf('WeakSet');\n    }\n    if (isWeakRef(obj)) {\n        return weakCollectionOf('WeakRef');\n    }\n    if (isNumber(obj)) {\n        return markBoxed(inspect(Number(obj)));\n    }\n    if (isBigInt(obj)) {\n        return markBoxed(inspect(bigIntValueOf.call(obj)));\n    }\n    if (isBoolean(obj)) {\n        return markBoxed(booleanValueOf.call(obj));\n    }\n    if (isString(obj)) {\n        return markBoxed(inspect(String(obj)));\n    }\n    if (!isDate(obj) && !isRegExp(obj)) {\n        var ys = arrObjKeys(obj, inspect);\n        var isPlainObject = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;\n        var protoTag = obj instanceof Object ? '' : 'null prototype';\n        var stringTag = !isPlainObject && toStringTag && Object(obj) === obj && toStringTag in obj ? toStr(obj).slice(8, -1) : protoTag ? 'Object' : '';\n        var constructorTag = isPlainObject || typeof obj.constructor !== 'function' ? '' : obj.constructor.name ? obj.constructor.name + ' ' : '';\n        var tag = constructorTag + (stringTag || protoTag ? '[' + [].concat(stringTag || [], protoTag || []).join(': ') + '] ' : '');\n        if (ys.length === 0) { return tag + '{}'; }\n        if (indent) {\n            return tag + '{' + indentedJoin(ys, indent) + '}';\n        }\n        return tag + '{ ' + ys.join(', ') + ' }';\n    }\n    return String(obj);\n};\n\nfunction wrapQuotes(s, defaultStyle, opts) {\n    var quoteChar = (opts.quoteStyle || defaultStyle) === 'double' ? '\"' : \"'\";\n    return quoteChar + s + quoteChar;\n}\n\nfunction quote(s) {\n    return String(s).replace(/\"/g, '&quot;');\n}\n\nfunction isArray(obj) { return toStr(obj) === '[object Array]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\nfunction isDate(obj) { return toStr(obj) === '[object Date]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\nfunction isRegExp(obj) { return toStr(obj) === '[object RegExp]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\nfunction isError(obj) { return toStr(obj) === '[object Error]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\nfunction isString(obj) { return toStr(obj) === '[object String]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\nfunction isNumber(obj) { return toStr(obj) === '[object Number]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\nfunction isBoolean(obj) { return toStr(obj) === '[object Boolean]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }\n\n// Symbol and BigInt do have Symbol.toStringTag by spec, so that can't be used to eliminate false positives\nfunction isSymbol(obj) {\n    if (hasShammedSymbols) {\n        return obj && typeof obj === 'object' && obj instanceof Symbol;\n    }\n    if (typeof obj === 'symbol') {\n        return true;\n    }\n    if (!obj || typeof obj !== 'object' || !symToString) {\n        return false;\n    }\n    try {\n        symToString.call(obj);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nfunction isBigInt(obj) {\n    if (!obj || typeof obj !== 'object' || !bigIntValueOf) {\n        return false;\n    }\n    try {\n        bigIntValueOf.call(obj);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nvar hasOwn = Object.prototype.hasOwnProperty || function (key) { return key in this; };\nfunction has(obj, key) {\n    return hasOwn.call(obj, key);\n}\n\nfunction toStr(obj) {\n    return objectToString.call(obj);\n}\n\nfunction nameOf(f) {\n    if (f.name) { return f.name; }\n    var m = match.call(functionToString.call(f), /^function\\s*([\\w$]+)/);\n    if (m) { return m[1]; }\n    return null;\n}\n\nfunction indexOf(xs, x) {\n    if (xs.indexOf) { return xs.indexOf(x); }\n    for (var i = 0, l = xs.length; i < l; i++) {\n        if (xs[i] === x) { return i; }\n    }\n    return -1;\n}\n\nfunction isMap(x) {\n    if (!mapSize || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        mapSize.call(x);\n        try {\n            setSize.call(x);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof Map; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakMap(x) {\n    if (!weakMapHas || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakMapHas.call(x, weakMapHas);\n        try {\n            weakSetHas.call(x, weakSetHas);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof WeakMap; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakRef(x) {\n    if (!weakRefDeref || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakRefDeref.call(x);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nfunction isSet(x) {\n    if (!setSize || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        setSize.call(x);\n        try {\n            mapSize.call(x);\n        } catch (m) {\n            return true;\n        }\n        return x instanceof Set; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakSet(x) {\n    if (!weakSetHas || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakSetHas.call(x, weakSetHas);\n        try {\n            weakMapHas.call(x, weakMapHas);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof WeakSet; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isElement(x) {\n    if (!x || typeof x !== 'object') { return false; }\n    if (typeof HTMLElement !== 'undefined' && x instanceof HTMLElement) {\n        return true;\n    }\n    return typeof x.nodeName === 'string' && typeof x.getAttribute === 'function';\n}\n\nfunction inspectString(str, opts) {\n    if (str.length > opts.maxStringLength) {\n        var remaining = str.length - opts.maxStringLength;\n        var trailer = '... ' + remaining + ' more character' + (remaining > 1 ? 's' : '');\n        return inspectString(str.slice(0, opts.maxStringLength), opts) + trailer;\n    }\n    // eslint-disable-next-line no-control-regex\n    var s = str.replace(/(['\\\\])/g, '\\\\$1').replace(/[\\x00-\\x1f]/g, lowbyte);\n    return wrapQuotes(s, 'single', opts);\n}\n\nfunction lowbyte(c) {\n    var n = c.charCodeAt(0);\n    var x = {\n        8: 'b',\n        9: 't',\n        10: 'n',\n        12: 'f',\n        13: 'r'\n    }[n];\n    if (x) { return '\\\\' + x; }\n    return '\\\\x' + (n < 0x10 ? '0' : '') + n.toString(16).toUpperCase();\n}\n\nfunction markBoxed(str) {\n    return 'Object(' + str + ')';\n}\n\nfunction weakCollectionOf(type) {\n    return type + ' { ? }';\n}\n\nfunction collectionOf(type, size, entries, indent) {\n    var joinedEntries = indent ? indentedJoin(entries, indent) : entries.join(', ');\n    return type + ' (' + size + ') {' + joinedEntries + '}';\n}\n\nfunction singleLineValues(xs) {\n    for (var i = 0; i < xs.length; i++) {\n        if (indexOf(xs[i], '\\n') >= 0) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getIndent(opts, depth) {\n    var baseIndent;\n    if (opts.indent === '\\t') {\n        baseIndent = '\\t';\n    } else if (typeof opts.indent === 'number' && opts.indent > 0) {\n        baseIndent = Array(opts.indent + 1).join(' ');\n    } else {\n        return null;\n    }\n    return {\n        base: baseIndent,\n        prev: Array(depth + 1).join(baseIndent)\n    };\n}\n\nfunction indentedJoin(xs, indent) {\n    if (xs.length === 0) { return ''; }\n    var lineJoiner = '\\n' + indent.prev + indent.base;\n    return lineJoiner + xs.join(',' + lineJoiner) + '\\n' + indent.prev;\n}\n\nfunction arrObjKeys(obj, inspect) {\n    var isArr = isArray(obj);\n    var xs = [];\n    if (isArr) {\n        xs.length = obj.length;\n        for (var i = 0; i < obj.length; i++) {\n            xs[i] = has(obj, i) ? inspect(obj[i], obj) : '';\n        }\n    }\n    var syms = typeof gOPS === 'function' ? gOPS(obj) : [];\n    var symMap;\n    if (hasShammedSymbols) {\n        symMap = {};\n        for (var k = 0; k < syms.length; k++) {\n            symMap['$' + syms[k]] = syms[k];\n        }\n    }\n\n    for (var key in obj) { // eslint-disable-line no-restricted-syntax\n        if (!has(obj, key)) { continue; } // eslint-disable-line no-restricted-syntax, no-continue\n        if (isArr && String(Number(key)) === key && key < obj.length) { continue; } // eslint-disable-line no-restricted-syntax, no-continue\n        if (hasShammedSymbols && symMap['$' + key] instanceof Symbol) {\n            // this is to prevent shammed Symbols, which are stored as strings, from being included in the string key section\n            continue; // eslint-disable-line no-restricted-syntax, no-continue\n        } else if ((/[^\\w$]/).test(key)) {\n            xs.push(inspect(key, obj) + ': ' + inspect(obj[key], obj));\n        } else {\n            xs.push(key + ': ' + inspect(obj[key], obj));\n        }\n    }\n    if (typeof gOPS === 'function') {\n        for (var j = 0; j < syms.length; j++) {\n            if (isEnumerable.call(obj, syms[j])) {\n                xs.push('[' + inspect(syms[j]) + ']: ' + inspect(obj[syms[j]], obj));\n            }\n        }\n    }\n    return xs;\n}\n", "'use strict';\n\nvar utils = require('./utils');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar defaults = {\n    allowDots: false,\n    allowPrototypes: false,\n    allowSparse: false,\n    arrayLimit: 20,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    comma: false,\n    decoder: utils.decode,\n    delimiter: '&',\n    depth: 5,\n    ignoreQueryPrefix: false,\n    interpretNumericEntities: false,\n    parameterLimit: 1000,\n    parseArrays: true,\n    plainObjects: false,\n    strictNullHandling: false\n};\n\nvar interpretNumericEntities = function (str) {\n    return str.replace(/&#(\\d+);/g, function ($0, numberStr) {\n        return String.fromCharCode(parseInt(numberStr, 10));\n    });\n};\n\nvar parseArrayValue = function (val, options) {\n    if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {\n        return val.split(',');\n    }\n\n    return val;\n};\n\n// This is what browsers will submit when the ✓ character occurs in an\n// application/x-www-form-urlencoded body and the encoding of the page containing\n// the form is iso-8859-1, or when the submitted form has an accept-charset\n// attribute of iso-8859-1. Presumably also with other charsets that do not contain\n// the ✓ character, such as us-ascii.\nvar isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')\n\n// These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.\nvar charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')\n\nvar parseValues = function parseQueryStringValues(str, options) {\n    var obj = {};\n    var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n    var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n    var parts = cleanStr.split(options.delimiter, limit);\n    var skipIndex = -1; // Keep track of where the utf8 sentinel was found\n    var i;\n\n    var charset = options.charset;\n    if (options.charsetSentinel) {\n        for (i = 0; i < parts.length; ++i) {\n            if (parts[i].indexOf('utf8=') === 0) {\n                if (parts[i] === charsetSentinel) {\n                    charset = 'utf-8';\n                } else if (parts[i] === isoSentinel) {\n                    charset = 'iso-8859-1';\n                }\n                skipIndex = i;\n                i = parts.length; // The eslint settings do not allow break;\n            }\n        }\n    }\n\n    for (i = 0; i < parts.length; ++i) {\n        if (i === skipIndex) {\n            continue;\n        }\n        var part = parts[i];\n\n        var bracketEqualsPos = part.indexOf(']=');\n        var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n\n        var key, val;\n        if (pos === -1) {\n            key = options.decoder(part, defaults.decoder, charset, 'key');\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = options.decoder(part.slice(0, pos), defaults.decoder, charset, 'key');\n            val = utils.maybeMap(\n                parseArrayValue(part.slice(pos + 1), options),\n                function (encodedVal) {\n                    return options.decoder(encodedVal, defaults.decoder, charset, 'value');\n                }\n            );\n        }\n\n        if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {\n            val = interpretNumericEntities(val);\n        }\n\n        if (part.indexOf('[]=') > -1) {\n            val = isArray(val) ? [val] : val;\n        }\n\n        if (has.call(obj, key)) {\n            obj[key] = utils.combine(obj[key], val);\n        } else {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\nvar parseObject = function (chain, val, options, valuesParsed) {\n    var leaf = valuesParsed ? val : parseArrayValue(val, options);\n\n    for (var i = chain.length - 1; i >= 0; --i) {\n        var obj;\n        var root = chain[i];\n\n        if (root === '[]' && options.parseArrays) {\n            obj = [].concat(leaf);\n        } else {\n            obj = options.plainObjects ? Object.create(null) : {};\n            var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n            var index = parseInt(cleanRoot, 10);\n            if (!options.parseArrays && cleanRoot === '') {\n                obj = { 0: leaf };\n            } else if (\n                !isNaN(index)\n                && root !== cleanRoot\n                && String(index) === cleanRoot\n                && index >= 0\n                && (options.parseArrays && index <= options.arrayLimit)\n            ) {\n                obj = [];\n                obj[index] = leaf;\n            } else {\n                obj[cleanRoot] = leaf;\n            }\n        }\n\n        leaf = obj;\n    }\n\n    return leaf;\n};\n\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {\n    if (!givenKey) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n    var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n    // The regex chunks\n\n    var brackets = /(\\[[^[\\]]*])/;\n    var child = /(\\[[^[\\]]*])/g;\n\n    // Get the parent\n\n    var segment = options.depth > 0 && brackets.exec(key);\n    var parent = segment ? key.slice(0, segment.index) : key;\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (parent) {\n        // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties\n        if (!options.plainObjects && has.call(Object.prototype, parent)) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(parent);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {\n        i += 1;\n        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, just add whatever is left\n\n    if (segment) {\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return parseObject(keys, val, options, valuesParsed);\n};\n\nvar normalizeParseOptions = function normalizeParseOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (opts.decoder !== null && opts.decoder !== undefined && typeof opts.decoder !== 'function') {\n        throw new TypeError('Decoder has to be a function.');\n    }\n\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n    var charset = typeof opts.charset === 'undefined' ? defaults.charset : opts.charset;\n\n    return {\n        allowDots: typeof opts.allowDots === 'undefined' ? defaults.allowDots : !!opts.allowDots,\n        allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults.allowPrototypes,\n        allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults.allowSparse,\n        arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults.arrayLimit,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        comma: typeof opts.comma === 'boolean' ? opts.comma : defaults.comma,\n        decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults.decoder,\n        delimiter: typeof opts.delimiter === 'string' || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,\n        // eslint-disable-next-line no-implicit-coercion, no-extra-parens\n        depth: (typeof opts.depth === 'number' || opts.depth === false) ? +opts.depth : defaults.depth,\n        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,\n        interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults.interpretNumericEntities,\n        parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults.parameterLimit,\n        parseArrays: opts.parseArrays !== false,\n        plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults.plainObjects,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (str, opts) {\n    var options = normalizeParseOptions(opts);\n\n    if (str === '' || str === null || typeof str === 'undefined') {\n        return options.plainObjects ? Object.create(null) : {};\n    }\n\n    var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n    var obj = options.plainObjects ? Object.create(null) : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');\n        obj = utils.merge(obj, newObj, options);\n    }\n\n    if (options.allowSparse === true) {\n        return obj;\n    }\n\n    return utils.compact(obj);\n};\n", "module.exports = \"<!-- Icon from https://github.com/google/material-design-icons -->\\n<!-- Licensed under Apache License 2.0 -->\\n<!-- Copyright (c) Google Inc. -->\\n<svg\\n        width=\\\"20\\\"\\n        height=\\\"20\\\"\\n        viewBox=\\\"0 0 20 20\\\"\\n        xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n>\\n    <g fill=\\\"none\\\" fill-rule=\\\"evenodd\\\">\\n        <path\\n                fill=\\\"#666\\\"\\n                fill-rule=\\\"nonzero\\\"\\n                d=\\\"M12.12 10l3.53 3.53-2.12 2.12L10 12.12l-3.54 3.54-2.12-2.12L7.88 10 4.34 6.46l2.12-2.12L10 7.88l3.54-3.53 2.12 2.12z\\\"\\n        />\\n    </g>\\n</svg>\"", "module.exports = \"<svg\\n        xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n        width=\\\"24\\\"\\n        height=\\\"24\\\"\\n        viewBox=\\\"0 0 24 24\\\"\\n>\\n    <path d=\\\"M0 0h24v24H0z\\\" fill=\\\"none\\\" />\\n    <path\\n            d=\\\"M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z\\\"\\n    />\\n</svg>\"", "(function($) {\n  // This is for the field group admin. I would have preferred to do this in PHP but I could't find an ACF hook\n\n  const checkElementHasValue = (rootElement, element) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element);\n\n    return targetElement.val() ? true : false;\n  };\n\n  const makeElementReadOnly = (rootElement, element) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element);\n    targetElement.prop('readonly', true);\n  };\n\n  const makeElementEditable = (rootElement, element) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element);\n    targetElement.prop('readonly', false);\n  };\n\n  const clearElement = (rootElement, element) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element);\n    targetElement.val('');\n  };\n\n  const makeElementRequired = (rootElement, element) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element);\n    targetElement.prop('required', true);\n  };\n\n  const makeElementNotRequired = (rootElement, element) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element);\n    targetElement.prop('required', false);\n  };\n\n  const copyElementValue = (rootElement, element1, element2) => {\n    let targetElement1 = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element1);\n\n    let targetElement2 = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element2);\n\n    if (targetElement1.val()) {\n      targetElement2.val(targetElement1.val());\n      targetElement2.attr('value', targetElement1.val());\n    }\n  };\n\n  const getElementValue = (rootElement, element1) => {\n    let targetElement1 = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element1);\n    return targetElement1.val();\n  };\n\n  const setElementValue = (rootElement, element1, value) => {\n    let targetElement = rootElement\n      .parents('.acf-field-object-image-aspect-ratio-crop')\n      .first()\n      .find(element1);\n\n    targetElement.val(value);\n    targetElement.attr('value', value);\n  };\n\n  // On page ready\n  $(document).ready(() => {\n    $('.acf-field-object-image-aspect-ratio-crop .crop-type-select').each(\n      function() {\n        toggleCropType(this, 'ready');\n      },\n    );\n  });\n\n  // When field is added / changed\n  acf.add_action('append', function() {\n    $('.acf-field-object-image-aspect-ratio-crop .crop-type-select').each(\n      function() {\n        toggleCropType(this, 'append');\n      },\n    );\n  });\n\n  // When crop type is changed\n  $(document).on(\n    'change',\n    '.acf-field-object-image-aspect-ratio-crop .crop-type-select',\n    function(event) {\n      toggleCropType(this, 'change');\n    },\n  );\n\n  // When height is changed\n  $(document).on(\n    'input change',\n    '.acf-field-object-image-aspect-ratio-crop .js-aspect-ratio-height',\n    function(event) {\n      toggleCropType(\n        $(this)\n          .parents('.acf-field-object-image-aspect-ratio-crop')\n          .first()\n          .find('.crop-type-select'),\n      );\n    },\n  );\n\n  // When width is changed\n  $(document).on(\n    'input change',\n    '.acf-field-object-image-aspect-ratio-crop .js-aspect-ratio-width',\n    function(event) {\n      toggleCropType(\n        $(this)\n          .parents('.acf-field-object-image-aspect-ratio-crop')\n          .first()\n          .find('.crop-type-select'),\n      );\n    },\n  );\n\n  function toggleCropType(element, actionType) {\n    let $element = $(element);\n    let type = $element.val();\n\n    if (type === 'pixel_size') {\n      clearElement($element, '.js-max-width');\n      makeElementReadOnly($element, '.js-max-width');\n\n      clearElement($element, '.js-max-height');\n      makeElementReadOnly($element, '.js-max-height');\n\n      clearElement($element, '.js-min-width');\n      copyElementValue($element, '.js-aspect-ratio-width', '.js-min-width');\n      makeElementReadOnly($element, '.js-min-width');\n\n      clearElement($element, '.js-min-height');\n      copyElementValue($element, '.js-aspect-ratio-height', '.js-min-height');\n      makeElementReadOnly($element, '.js-min-height');\n    }\n    if (type === 'aspect_ratio' || type === 'free_crop') {\n      if (actionType !== 'ready') {\n        clearElement($element, '.js-min-width');\n      }\n      makeElementEditable($element, '.js-min-width');\n      if (actionType !== 'ready') {\n        clearElement($element, '.js-min-height');\n      }\n      makeElementEditable($element, '.js-min-height');\n      if (actionType !== 'ready') {\n        clearElement($element, '.js-max-width');\n      }\n      makeElementEditable($element, '.js-max-width');\n      if (actionType !== 'ready') {\n        clearElement($element, '.js-max-height');\n      }\n      makeElementEditable($element, '.js-max-height');\n    }\n    if (type === 'free_crop') {\n      makeElementNotRequired($element, '.js-aspect-ratio-width');\n      makeElementNotRequired($element, '.js-aspect-ratio-height');\n\n      clearElement($element, '.js-max-width');\n      makeElementReadOnly($element, '.js-max-width');\n\n      clearElement($element, '.js-max-height');\n      makeElementReadOnly($element, '.js-max-height');\n\n      clearElement($element, '.js-min-width');\n      makeElementReadOnly($element, '.js-min-width');\n\n      clearElement($element, '.js-min-height');\n      makeElementReadOnly($element, '.js-min-height');\n    }\n    if (type === 'aspect_ratio' || type === 'pixel_size') {\n      makeElementRequired($element, '.js-aspect-ratio-width');\n      makeElementRequired($element, '.js-aspect-ratio-height');\n    }\n\n    if (type === 'aspect_ratio') {\n      if (\n        checkElementHasValue($element, '.js-aspect-ratio-width') &&\n        checkElementHasValue($element, '.js-aspect-ratio-height')\n      ) {\n        makeElementEditable($element, '.js-max-width');\n        makeElementEditable($element, '.js-max-height');\n        makeElementEditable($element, '.js-min-width');\n        makeElementEditable($element, '.js-min-height');\n      } else {\n        makeElementReadOnly($element, '.js-max-width');\n        makeElementReadOnly($element, '.js-max-height');\n        makeElementReadOnly($element, '.js-min-width');\n        makeElementReadOnly($element, '.js-min-height');\n      }\n    }\n  }\n\n  const setHeight = (element, sourceElement, targetElement) => {\n    let $element = $(element);\n\n    let aspectRatioWidth = getElementValue($element, '.js-aspect-ratio-width');\n    let aspectRatioHeight = getElementValue(\n      $element,\n      '.js-aspect-ratio-height',\n    );\n    let minWidth = getElementValue($element, sourceElement);\n\n    if (aspectRatioHeight && aspectRatioWidth && minWidth) {\n      let minHeight = Math.round(\n        (aspectRatioHeight / aspectRatioWidth) * minWidth,\n      );\n      setElementValue($element, targetElement, minHeight);\n    } else {\n      setElementValue($element, targetElement, '');\n    }\n  };\n\n  const setWidth = (element, sourceElement, targetElement) => {\n    let $element = $(element);\n\n    let aspectRatioWidth = getElementValue($element, '.js-aspect-ratio-width');\n    let aspectRatioHeight = getElementValue(\n      $element,\n      '.js-aspect-ratio-height',\n    );\n    let minHeight = getElementValue($element, sourceElement);\n\n    if (aspectRatioHeight && aspectRatioWidth && minHeight) {\n      let minWidth = Math.round(\n        (aspectRatioWidth / aspectRatioHeight) * minHeight,\n      );\n      setElementValue($element, targetElement, minWidth);\n    } else {\n      setElementValue($element, targetElement, '');\n    }\n  };\n\n  // When min height is changed\n  $(document).on(\n    'input change',\n    '.acf-field-object-image-aspect-ratio-crop .js-min-width',\n    function(event) {\n      setHeight(this, '.js-min-width', '.js-min-height');\n    },\n  );\n\n  // When min width is changed\n  $(document).on(\n    'input change',\n    '.acf-field-object-image-aspect-ratio-crop .js-min-height',\n    function(event) {\n      setWidth(this, '.js-min-height', '.js-min-width');\n    },\n  );\n\n  // When max height is changed\n  $(document).on(\n    'input change',\n    '.acf-field-object-image-aspect-ratio-crop .js-max-width',\n    function(event) {\n      setHeight(this, '.js-max-width', '.js-max-height');\n    },\n  );\n\n  // When max width is changed\n  $(document).on(\n    'input change',\n    '.acf-field-object-image-aspect-ratio-crop .js-max-height',\n    function(event) {\n      setWidth(this, '.js-max-height', '.js-max-width');\n    },\n  );\n})(jQuery);\n", "/*!\n * Based on assets/js/acf-input.js from\n * https://github.com/AdvancedCustomFields/acf by <PERSON><PERSON><PERSON><PERSON><PERSON>, licensed\n * under GPLv2 or later\n */\n\nimport <PERSON><PERSON><PERSON> from 'cropperjs';\nimport axios from 'axios';\nimport qs from 'qs';\nimport { sprintf } from 'sprintf-js';\n\n(function($) {\n  var field = null;\n\n  acf.fields.image_aspect_ratio_crop = acf.field.extend({\n    type: 'image_aspect_ratio_crop',\n    $el: null,\n    $input: null,\n    $img: null,\n\n    actions: {\n      ready: 'initialize',\n      append: 'initialize',\n    },\n\n    events: {\n      'click a[data-name=\"add\"]': 'add',\n      'click a[data-name=\"edit\"]': 'edit',\n      'click a[data-name=\"remove\"]': 'remove',\n      'change input[type=\"file\"]': 'change',\n      'click a[data-name=\"crop\"]': 'changeCrop',\n      'change .js-aiarc-upload': 'front_end_upload',\n    },\n\n    front_end_upload: function(event) {\n      let uploadElement = event.currentTarget;\n\n      var acfKey = $(this.$field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('key');\n\n      let files = uploadElement.files;\n      let formData = new FormData();\n\n      this.isFirstCrop = true;\n\n      if (!files.length) {\n        return;\n      }\n\n      Array.from(Array(files.length).keys()).map(index => {\n        formData.append('image', files[index], files[index].name);\n        formData.append('key', acfKey);\n      });\n\n      uploadElement.value = '';\n\n      let settings = {\n        onUploadProgress: progressEvent => {\n          let percentCompleted = Math.round(\n            (progressEvent.loaded * 100) / progressEvent.total,\n          );\n\n          this.$el\n            .find('.js-aiarc-upload-progress')\n            .html(\n              sprintf(\n                window.aiarc_translations.upload_progress,\n                percentCompleted,\n              ),\n            );\n        },\n        headers: {\n          'X-Aiarc-Nonce': window.aiarc.nonce,\n          'X-WP-Nonce': window.aiarc.wp_rest_nonce,\n        },\n      };\n\n      $(this.$el)\n        .find('.js-aiarc-upload')\n        .hide();\n\n      $(this.$el)\n        .find('.js-aiarc-upload-progress')\n        .show();\n\n      axios\n        .post(`${window.aiarc.api_root}/aiarc/v1/upload`, formData, settings)\n        .then(response => {\n          // This is just for the preview\n          axios\n            .get(\n              `${window.aiarc.api_root}/aiarc/v1/get/${response.data.attachment_id}`,\n            )\n            .then(response => {\n              let attachment = new window.Backbone.Model(response.data);\n              this.render(attachment);\n            });\n\n          $(this.$el)\n            .find('.js-aiarc-upload-progress')\n            .hide();\n\n          $(this.$el)\n            .find('.js-aiarc-upload')\n            .show();\n\n          let $field = this.$field;\n\n          // Add original id attribute to the image so we can recrop it right away without saving the post\n          $field\n            .find('.acf-image-uploader-aspect-ratio-crop')\n            .data('original-image-id', response.data.attachment_id)\n            .attr('data-original-image-id', response.data.attachment_id);\n\n          axios\n            .get(\n              `${window.aiarc.api_root}/aiarc/v1/get/${response.data.attachment_id}`,\n            )\n            .then(response => {\n              let attachment = new window.Backbone.Model(response.data);\n\n              this.render(attachment);\n              this.openModal({ attachment: attachment, field: $field });\n            });\n        })\n        .catch(error => {\n          $(this.$el)\n            .find('.js-aiarc-upload-progress')\n            .hide();\n\n          $(this.$el)\n            .find('.js-aiarc-upload')\n            .show();\n\n          let errorMessage = window.aiarc_translations.upload_failed;\n\n          if (\n            error.response &&\n            error.response.data &&\n            error.response.data.message\n          ) {\n            errorMessage = error.response.data.message;\n          }\n\n          window.alert(errorMessage);\n        });\n    },\n\n    /*\n     *  focus\n     *\n     *  This function will setup variables when focused on a field\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\tn/a\n     *  @return\tn/a\n     */\n\n    focus: function() {\n      // vars\n      this.$el = this.$field.find('.acf-image-uploader-aspect-ratio-crop');\n      this.$input = this.$el.find('input[type=\"hidden\"]');\n      this.$img = this.$el.find('img');\n\n      // options\n      this.o = acf.get_data(this.$el);\n    },\n\n    /*\n     *  initialize\n     *\n     *  This function is used to setup basic upload form attributes\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\tn/a\n     *  @return\tn/a\n     */\n\n    initialize: function() {\n      this.isFirstCrop = null;\n      var self = this;\n\n      // add attribute to form\n      if (this.o.uploader == 'basic') {\n        this.$el.closest('form').attr('enctype', 'multipart/form-data');\n      }\n\n      this.escapeHandlerBound = this.escapeHandler.bind(this);\n\n      $(document).on('click', '.js-acf-image-aspect-ratio-crop-cancel', () =>\n        this.closeModal(),\n      );\n\n      $(document)\n        .off('click', '.js-acf-image-aspect-ratio-crop-reset')\n        .on('click', '.js-acf-image-aspect-ratio-crop-reset', () => {\n          this.cropper.reset();\n        });\n\n      $(document)\n        .off('click', '.js-acf-image-aspect-ratio-crop-crop')\n        .on('click', '.js-acf-image-aspect-ratio-crop-crop', function() {\n          var cropData = self.cropper.getData(true);\n\n          $('.js-acf-image-aspect-ratio-crop-modal').css(\n            'max-width',\n            self.cropper.containerData.width,\n          );\n\n          var cropType = $(field)\n            .find('.acf-image-uploader-aspect-ratio-crop')\n            .data('crop_type');\n\n          let acfKey = $(field)\n            .find('.acf-image-uploader-aspect-ratio-crop')\n            .data('key');\n\n          var data = {\n            id: $(this).data('id'),\n            aspectRatioHeight: $(this).data('aspect-ratio-height'),\n            aspectRatioWidth: $(this).data('aspect-ratio-width'),\n            cropType: $(this).data('crop-type'),\n            x: cropData.x,\n            y: cropData.y,\n            width: cropData.width,\n            height: cropData.height,\n            temp_post_id: aiarc.temp_post_id,\n            key: acfKey,\n          };\n\n          $('.js-acf-image-aspect-ratio-crop-crop').prop('disabled', true);\n          $('.js-acf-image-aspect-ratio-crop-reset').prop('disabled', true);\n\n          // prettier-ignore\n          var loading = '<div class=\"acf-image-aspect-ratio-crop-modal-loading\">' +\n                          '<div class=\"acf-image-aspect-ratio-crop-modal-loading-icon\">' +\n                          '<!-- Icon from https://github.com/google/material-design-icons -->' +\n                          '<!-- Licensed under Apache License 2.0 -->' +\n                          '<!-- Copyright (c) Google Inc. -->' +\n                          '<svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 2.64V1L4.75 3.18 7 5.36V3.73A3.33 3.33 0 0 1 10.38 7c0 .55-.15 1.07-.4 1.53l.82.8c.44-.68.7-1.47.7-2.33A4.43 4.43 0 0 0 7 2.64zm0 7.63A3.33 3.33 0 0 1 3.62 7c0-.55.15-1.07.4-1.53l-.82-.8c-.44.68-.7 1.47-.7 2.33A4.43 4.43 0 0 0 7 11.36V13l2.25-2.18L7 8.64v1.63z\" fill=\"#FFF\" fill-rule=\"nonzero\"/></svg>' +\n                          '</div>' +\n                          '<div class=\"acf-image-aspect-ratio-crop-modal-loading-text\">' +\n                          aiarc_translations.cropping_in_progress +\n                          '</div>' +\n                        '</div>';\n\n          // prettier-ignore\n          var error = '<div class=\"acf-image-aspect-ratio-crop-modal-error\">' +\n                        '<div class=\"acf-image-aspect-ratio-crop-modal-error-icon\">' +\n                        '<!-- Icon from https://github.com/google/material-design-icons -->' +\n                        '<!-- Licensed under Apache License 2.0 -->' +\n                        '<!-- Copyright (c) Google Inc. -->' +\n                        '<svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1 20.14h20l-10-17-10 17zm10.9-2.69h-1.8v-1.79h1.8v1.8zm0-3.58h-1.8V10.3h1.8v3.58z\" fill=\"#F44336\" fill-rule=\"nonzero\"/></svg>' +\n                        '</div>' +\n                        '<div class=\"acf-image-aspect-ratio-crop-modal-error-text\">' +\n                        aiarc_translations.cropping_failed +\n                        '</div>' +\n                      '</div>';\n\n          $('.js-acf-image-aspect-ratio-crop-modal-footer-status').empty();\n          $('.js-acf-image-aspect-ratio-crop-modal-footer-status').html(\n            loading,\n          );\n          self.cropper.disable();\n\n          let options = {};\n\n          let url = null;\n\n          if (window.aiarc_settings.rest_api_compat === '') {\n            url = `${window.aiarc.api_root}/aiarc/v1/crop`;\n            options = {\n              headers: {\n                'X-Aiarc-Nonce': window.aiarc.nonce,\n                'X-WP-Nonce': window.aiarc.wp_rest_nonce,\n              },\n            };\n          }\n\n          if (window.aiarc_settings.rest_api_compat === '1') {\n            url = ajaxurl;\n            data = qs.stringify({\n              action: 'acf_image_aspect_ratio_crop_crop',\n              data: JSON.stringify(data),\n            });\n          }\n\n          axios\n            .post(url, data, options)\n            .then(response => {\n              self.cropComplete(response.data);\n              $('.js-acf-image-aspect-ratio-crop-crop').prop('disabled', false);\n              $('.js-acf-image-aspect-ratio-crop-reset').prop(\n                'disabled',\n                false,\n              );\n              $('.js-acf-image-aspect-ratio-crop-modal-footer-status').empty();\n            })\n            .catch(response => {\n              console.error(response);\n              self.cropper.enable();\n              $('.js-acf-image-aspect-ratio-crop-crop').prop('disabled', false);\n              $('.js-acf-image-aspect-ratio-crop-reset').prop(\n                'disabled',\n                false,\n              );\n              $('.js-acf-image-aspect-ratio-crop-modal-footer-status').empty();\n              $('.js-acf-image-aspect-ratio-crop-modal-footer-status').html(\n                error,\n              );\n            });\n        });\n    },\n\n    /*\n     *  prepare\n     *\n     *  This function will prepare an object of attachment data\n     *  selecting a library image vs embed an image via url return different data\n     *  this function will keep the 2 consistent\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\tattachment (object)\n     *  @return\tdata (object)\n     */\n\n    prepare: function(attachment) {\n      // defaults\n      attachment = attachment || {};\n\n      // bail ealry if already valid\n      if (attachment._valid) return attachment;\n\n      // vars\n      var data = {\n        url: '',\n        alt: '',\n        title: '',\n        caption: '',\n        description: '',\n        width: 0,\n        height: 0,\n      };\n\n      // wp image\n      if (attachment.id) {\n        // update data\n        data = attachment.attributes;\n\n        // maybe get preview size\n        //data.url = acf.maybe_get(\n        //  data,\n        //  'sizes.' + this.o.preview_size + '.url',\n        //  data.url,\n        //);\n      }\n\n      // valid\n      data._valid = true;\n\n      // return\n      return data;\n    },\n\n    /*\n     *  render\n     *\n     *  This function will render the UI\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\tattachment (obj)\n     *  @return\tn/a\n     */\n\n    render: function(data) {\n      // prepare\n\n      data = this.prepare(data);\n\n      // update image\n      this.$img.attr({\n        src: data.url,\n        alt: data.alt,\n        title: data.title,\n      });\n\n      // vars\n      var val = '';\n\n      // WP attachment\n      if (data.id) {\n        val = data.id;\n      }\n\n      // update val\n      acf.val(this.$input, val);\n\n      // update class\n      if (val) {\n        this.$el.addClass('has-value');\n      } else {\n        this.$el.removeClass('has-value');\n      }\n    },\n\n    /*\n     *  add\n     *\n     *  event listener\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\te (event)\n     *  @return\tn/a\n     */\n\n    add: function() {\n      // reference\n      var self = this,\n        $field = this.$field;\n\n      // get repeater\n      var $repeater = acf.get_closest_field(this.$field, 'repeater');\n\n      // popup\n      var frame = acf.media.popup({\n        title: acf._e('image', 'select'),\n        mode: 'select',\n        type: 'image',\n        field: $field.data('key'),\n        multiple: false,\n        library: this.o.library,\n        mime_types: this.o.mime_types,\n\n        select: function(attachment, i) {\n          // select / add another image field?\n          if (i > 0) {\n            // vars\n            var key = $field.data('key'),\n              $tr = $field.closest('.acf-row');\n\n            // reset field\n            $field = false;\n\n            // find next image field\n            $tr.nextAll('.acf-row:visible').each(function() {\n              // get next $field\n              $field = acf.get_field(key, $(this));\n\n              // bail early if $next was not found\n              if (!$field) return;\n\n              // bail early if next file uploader has value\n              if (\n                $field\n                  .find('.acf-image-uploader-aspect-ratio-crop.has-value')\n                  .exists()\n              ) {\n                $field = false;\n                return;\n              }\n\n              // end loop if $next is found\n              return false;\n            });\n\n            // add extra row if next is not found\n            if (!$field) {\n              $tr = acf.fields.repeater.doFocus($repeater).add();\n\n              // bail early if no $tr (maximum rows hit)\n              if (!$tr) return false;\n\n              // get next $field\n              $field = acf.get_field(key, $tr);\n            }\n          }\n\n          self.isFirstCrop = true;\n\n          // Add original id attribute to the image so we can recrop it right away without saving the post\n          $field\n            .find('.acf-image-uploader-aspect-ratio-crop')\n            .data('original-image-id', attachment.id)\n            .attr('data-original-image-id', attachment.id);\n\n          self.openModal({ attachment: attachment, field: $field });\n\n          // render\n          self.set('$field', $field).render(attachment);\n        },\n      });\n    },\n\n    changeCrop: function() {\n      this.isFirstCrop = false;\n      var originalImageId = $(this.$field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('original-image-id');\n\n      let callback = response => {\n        let attachment = new window.Backbone.Model(response.data);\n        let $field = this.$field;\n        this.openModal({ attachment: attachment, field: $field });\n      };\n\n      if (window.aiarc_settings.rest_api_compat === '') {\n        axios\n          .get(`${window.aiarc.api_root}/aiarc/v1/get/${originalImageId}`)\n          .then(response => callback(response));\n      }\n\n      if (window.aiarc_settings.rest_api_compat === '1') {\n        let data = qs.stringify({\n          action: 'acf_image_aspect_ratio_crop_get_attachment',\n          data: JSON.stringify({ attachment_id: originalImageId }),\n        });\n        axios.post(ajaxurl, data).then(response => callback(response));\n      }\n    },\n\n    /*\n     *  edit\n     *\n     *  event listener\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\te (event)\n     *  @return\tn/a\n     */\n\n    edit: function() {\n      // reference\n      var self = this,\n        $field = this.$field;\n\n      // vars\n      var val = null;\n      if (\n        this.$input.parent().attr('data-original-image-id') &&\n        window.aiarc_settings.modal_type === 'original'\n      ) {\n        val = this.$input.parent().attr('data-original-image-id');\n      } else {\n        val = this.$input.val();\n      }\n\n      // bail early if no val\n      if (!val) return;\n\n      // popup\n      var frame = acf.media.popup({\n        title: acf._e('image', 'edit'),\n        button: acf._e('image', 'update'),\n        mode: 'edit',\n        attachment: val,\n      });\n    },\n\n    /*\n     *  remove\n     *\n     *  event listener\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\te (event)\n     *  @return\tn/a\n     */\n\n    remove: function() {\n      // Remove all data attributes from the previous image\n      this.$field\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('original-image-id', null)\n        .attr('data-original-image-id', null)\n        .data('coordinates', null)\n        .attr('data-coordinates', null);\n\n      // vars\n      var attachment = {};\n\n      // add file to field\n      this.render(attachment);\n    },\n\n    /*\n     *  change\n     *\n     *  This function will update the hidden input when selecting a basic file to add basic validation\n     *\n     *  @type\tfunction\n     *  @date\t12/04/2016\n     *  @since\t5.3.8\n     *\n     *  @param\te (event)\n     *  @return\tn/a\n     */\n\n    change: function(e) {\n      //acf.fields.file.get_file_info(e.$el, this.$input);\n    },\n\n    escapeHandler: function(event) {\n      if (event.key === 'Escape') {\n        this.closeModal();\n      }\n    },\n\n    openModal: function(data) {\n      var url = data.attachment.attributes.url;\n      var id = data.attachment.attributes.id;\n      field = data.field;\n\n      document.addEventListener('keydown', this.escapeHandlerBound);\n\n      var aspectRatioWidth = $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('aspect_ratio_width');\n      var aspectRatioHeight = $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('aspect_ratio_height');\n      var cropType = $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('crop_type');\n      var minWidth = $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('min_width');\n      var minHeight = $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('min_height');\n\n      var options = {\n        aspectRatio: aspectRatioWidth / aspectRatioHeight,\n        viewMode: 1,\n        autoCropArea: 1,\n        zoomable: false,\n        checkCrossOrigin: false,\n        checkOrientation: false,\n        responsive: true,\n      };\n\n      if (cropType === 'pixel_size') {\n        options.crop = function(event) {\n          let width = event.detail.width;\n          let height = event.detail.height;\n          if (width < aspectRatioWidth || height < aspectRatioHeight) {\n            this.cropper.setData({\n              width: aspectRatioWidth,\n              height: aspectRatioHeight,\n            });\n          }\n        };\n      }\n\n      if (cropType === 'aspect_ratio' && minHeight !== 0 && minWidth !== 0) {\n        options.crop = function(event) {\n          let width = event.detail.width;\n          let height = event.detail.height;\n          if (width < minWidth || height < minHeight) {\n            this.cropper.setData({\n              width: minWidth,\n              height: minHeight,\n            });\n          }\n        };\n      }\n\n      let coordinates = $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('coordinates');\n\n      if (coordinates) {\n        options.data = coordinates;\n      }\n\n      // prettier-ignore\n      $('body').append(`\n<div class=\"acf-image-aspect-ratio-crop-backdrop\">\n  <div class=\"acf-image-aspect-ratio-crop-modal-wrapper\">\n    <div\n      class=\"acf-image-aspect-ratio-crop-modal js-acf-image-aspect-ratio-crop-modal\"\n    >\n      <div class=\"acf-image-aspect-ratio-crop-modal-heading\">\n        <div class=\"acf-image-aspect-ratio-crop-modal-heading-text\">\n          ${aiarc_translations.modal_title}\n        </div>\n        <button\n          class=\"acf-image-aspect-ratio-crop-modal-heading-close js-acf-image-aspect-ratio-crop-cancel\"\n          aria-label=\"Close\"\n        >\n          ${require('!raw-loader!./close.svg')}\n        </button>\n      </div>\n      <div class=\"acf-image-aspect-ratio-crop-modal-image-container\">\n        <img\n          class=\"acf-image-aspect-ratio-crop-modal-image js-acf-image-aspect-ratio-crop-modal-image\"\n          src=\"${url}\"\n        />\n      </div>\n\n      <div class=\"acf-image-aspect-ratio-crop-modal-footer\">\n        <div\n          class=\"acf-image-aspect-ratio-crop-modal-footer-status js-acf-image-aspect-ratio-crop-modal-footer-status\"\n        ></div>\n        <div class=\"acf-image-aspect-ratio-crop-modal-footer-buttons\">\n          <button\n            class=\"aiarc-button aiarc-button-link acf-image-aspect-ratio-crop-reset js-acf-image-aspect-ratio-crop-reset\"\n          >\n            ${require('!raw-loader!./reset.svg')}\n            ${aiarc_translations.reset}\n          </button>\n          <button class=\"aiarc-button aiarc-button-default js-acf-image-aspect-ratio-crop-cancel\">\n            ${aiarc_translations.cancel}\n          </button>\n          <button\n            class=\"aiarc-button aiarc-button-primary js-acf-image-aspect-ratio-crop-crop\"\n            data-id=\"${id}\"\n            data-aspect-ratio-height=\"${aspectRatioHeight}\"\n            data-aspect-ratio-width=\"${aspectRatioWidth}\"\n            data-crop-type=\"${cropType}\"\n          >\n            ${aiarc_translations.crop}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n`);\n\n      this.cropper = new Cropper(\n        $('.js-acf-image-aspect-ratio-crop-modal-image')[0],\n        options,\n      );\n\n      // Test helper\n      window._acf_image_aspect_ratio_cropper = this.cropper;\n    },\n\n    cropComplete: function(data) {\n      // Save coordinates so they are remembered even without saving the post first\n      $(field)\n        .find('.acf-image-uploader-aspect-ratio-crop')\n        .data('coordinates', this.cropper.getData(true))\n        .attr('data-coordinates', JSON.stringify(this.cropper.getData(true)));\n\n      // Cropping successful, change image to cropped version\n      this.cropper.destroy();\n\n      $(field)\n        .find('input')\n        .first()\n        .val(data.id);\n\n      let callback = response => {\n        let attachment = new window.Backbone.Model(response.data);\n\n        this.render(attachment);\n        this.isFirstCrop = false;\n        this.closeModal();\n      };\n\n      if (window.aiarc_settings.rest_api_compat === '') {\n        axios\n          .get(`${window.aiarc.api_root}/aiarc/v1/get/${data.id}`)\n          .then(response => callback(response));\n      }\n\n      if (window.aiarc_settings.rest_api_compat === '1') {\n        let postData = qs.stringify({\n          action: 'acf_image_aspect_ratio_crop_get_attachment',\n          data: JSON.stringify({ attachment_id: data.id }),\n        });\n        axios.post(ajaxurl, postData).then(response => callback(response));\n      }\n    },\n\n    closeModal: function() {\n      if (this.isFirstCrop) {\n        // If it's the first time cropping an image, we don't want to\n        // leave the incorrect aspect ratio image in the field\n        acf.val(this.$input, '');\n        this.render({});\n      }\n      $('.acf-image-aspect-ratio-crop-backdrop').remove();\n      document.removeEventListener('keydown', this.escapeHandlerBound);\n      this.cropper.destroy();\n    },\n  });\n\n  /**\n   *  initialize_field\n   *\n   *  This function will initialize the $field.\n   *\n   *  @date\t30/11/17\n   *  @since\t5.6.5\n   *\n   *  @param\tn/a\n   *  @return\tn/a\n   */\n\n  function initialize_field($el) {\n    //$field.doStuff();\n    //var $field = $el, $options = $el.find('.acf-field-image-aspect-ratio-crop');\n  }\n\n  /*\n   *  ready & append (ACF5)\n   *\n   *  These two events are called when a field element is ready for initizliation.\n   *  - ready: on page load similar to $(document).ready()\n   *  - append: on new DOM elements appended via repeater field or other AJAX calls\n   *\n   *  @param\tn/a\n   *  @return\tn/a\n   */\n\n  acf.add_action('ready_field/type=image_aspect_ratio_crop', initialize_field);\n  acf.add_action('append_field/type=image_aspect_ratio_crop', initialize_field);\n})(jQuery);\n"], "sourceRoot": ""}