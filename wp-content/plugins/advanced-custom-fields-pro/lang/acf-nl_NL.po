# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-19T16:45:13+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""
"Gemaakte opslaan opties terug naar de instelling \"Keuzes\" in de "
"velddefinitie."

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr "Opties opslaan"

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""
"Laat inhoud redacteuren nieuwe opties maken door te typen in de selecteer "
"invoer. Er kunnen meerdere opties worden gemaakt van een door komma's "
"gescheiden string."

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr "Maak opties"

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr "ACF veldgroepen bewerken"

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr "Ontvang 4 maanden gratis op elk WP Engine plan"

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr "Aantal veldgroepen met blokken en andere locaties"

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr "Aantal veldgroepen met meerdere bloklocaties"

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr "Aantal veldgroepen met een enkele bloklocatie"

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr "Alle locatie regels"

#: includes/validation.php:144
msgid "Learn more"
msgstr "Meer informatie"

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF kon geen validatie uitvoeren omdat de verstrekte nonce niet geverifieerd "
"kon worden."

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"ACF kon geen validatie uitvoeren omdat de server geen nonce heeft ontvangen."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr "worden ontwikkeld en onderhouden door"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "Bron updaten"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "Standaard kunnen alleen beheer gebruikers deze instelling bewerken."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""
"Standaard kunnen alleen super beheer gebruikers deze instelling bewerken."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Sluiten en veld toevoegen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Een PHP functienaam die moet worden aangeroepen om de inhoud van een meta "
"vak op je taxonomie te verwerken. Voor de veiligheid wordt deze callback "
"uitgevoerd in een speciale context zonder toegang tot superglobals zoals "
"$_POST of $_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Een PHP functienaam die wordt aangeroepen bij het instellen van de meta "
"vakken voor het bewerk scherm. Voor de veiligheid wordt deze callback "
"uitgevoerd in een speciale context zonder toegang tot superglobals zoals "
"$_POST of $_GET."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "Toegang tot waarde toestaan in UI van editor"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "Leer meer."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Toestaan dat inhoud editors de veldwaarde openen en weergeven in de editor "
"UI met behulp van Block Bindings of de ACF shortcode. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"Het aangevraagde ACF veldtype ondersteunt geen uitvoer in blok bindingen of "
"de ACF shortcode."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"Het aangevraagde ACF veld uitgevoerd in bindingen of de ACF shortcode zijn "
"niet toegestaan."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"Het aangevraagde ACF veldtype ondersteunt geen uitvoer in bindingen of de "
"ACF shortcode."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[De ACF shortcode kan geen velden van niet-openbare berichten tonen]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[De ACF shortcode is uitgeschakeld op deze site]"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr "Zakenman icoon"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr "Forums icoon"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr "YouTube icoon"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr "Ja (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr "Xing icoon"

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr "WordPress (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr "WhatsApp icoon"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr "Schrijf blog icoon"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr "Widgets menu's icoon"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr "Bekijk site icoon"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr "Meer leren icoon"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr "Toevoegen pagina icoon"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr "Video (alt3) icoon"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr "Video (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr "Video (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr "Updaten (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr "Universele toegang (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr "Twitter (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr "Twitch icoon"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr "Tide icoon"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr "Tickets (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr "Tekstpagina icoon"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr "Tabelrij verwijderen icoon"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr "Tabelrij voor icoon"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr "Tabelrij na icoon"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr "Tabel kolom verwijderen icoon"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr "Tabel kolom voor icoon"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr "Tabel kolom na icoon"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr "Superhero (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr "Superhero icoon"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr "Spotify icoon"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr "Shortcode icoon"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr "Schild (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr "Deel (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr "Deel (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr "Opgeslagen icoon"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr "RSS icoon"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr "REST API icoon"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr "Verwijderen icoon"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr "Reddit icoon"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr "Privacy icoon"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr "Printer icoon"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr "Podio icoon"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr "Plus (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr "Plus (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr "Plugins gecontroleerd icoon"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr "Pinterest icoon"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr "Huisdieren icoon"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr "PDF icoon"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr "Palmboom icoon"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr "Open map icoon"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr "Geen (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr "Geld (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr "Menu (alt3) icoon"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr "Menu (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr "Menu (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr "Spreadsheet icoon"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr "Interactieve icoon"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr "Document icoon"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr "Standaard icoon"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr "Locatie (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr "LinkedIn icoon"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr "Instagram icoon"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr "Voeg in voor icoon"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr "Voeg in na icoon"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr "Voeg icoon in"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr "Info outline icoon"

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr "Afbeeldingen (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr "Afbeeldingen (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr "Roteren rechts icoon"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr "Roteren links icoon"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr "Roteren icoon"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr "Spiegelen verticaal icoon"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr "Spiegelen horizontaal icoon"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr "Bijsnijden icoon"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr "ID (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr "HTML icoon"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr "Zandloper icoon"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr "Koptekst icoon"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr "Google icoon"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr "Games icoon"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr "Volledig scherm afsluiten (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr "Volledig scherm (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr "Status icoon"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr "Afbeelding icoon"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr "Galerij icoon"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr "Chat icoon"

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr "Audio icoon"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr "Aside icoon"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr "Voedsel icoon"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr "Exit icoon"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr "Samenvattingweergave icoon"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr "Insluiten video icoon"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr "Insluiten bericht icoon"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr "Insluiten foto icoon"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr "Insluiten generiek icoon"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr "Insluiten audio icoon"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr "E-mail (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr "Ellipsis icoon"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr "Ongeordende lijst icoon"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr "RTL icoon"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr "Geordende lijst RTL icoon"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr "Geordende lijst icoon"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr "LTR icoon"

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr "Aangepast karakter icoon"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr "Bewerken pagina icoon"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr "Bewerken groot Icoon"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr "Drumstick icoon"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr "Database weergave icoon"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr "Database verwijderen icoon"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr "Database import icoon"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr "Database export icoon"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr "Database icoon toevoegen"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr "Database icoon"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr "Omslagafbeelding icoon"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr "Volume aan icoon"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr "Volume uit icoon"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr "Vooruitspoelen icoon"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr "Terugspoel icoon"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr "Herhaal icoon"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr "Speel icoon"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr "Pauze icoon"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr "Vooruit icoon"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr "Terug icoon"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr "Kolommen icoon"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr "Kleurkiezer icoon"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr "Koffie icoon"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr "Code standaarden icoon"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr "Cloud upload icoon"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr "Cloud opgeslagen icoon"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr "Auto icoon"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr "Camera (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr "Rekenmachine icoon"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr "Knop icoon"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr "Zakelijk icoon"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr "Tracking icoon"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr "Onderwerpen icoon"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr "Antwoorden icoon"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr "PM icoon"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr "Vrienden icoon"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr "Community icoon"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr "BuddyPress icoon"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr "bbPress icoon"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr "Activiteit icoon"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr "Boek (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr "Blok standaard icoon"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr "Bel icoon"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr "Bier icoon"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr "Bank icoon"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr "Pijl omhoog (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr "Pijl omhoog (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr "Pijl naar rechts (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr "Pijl naar rechts (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr "Pijl naar links (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr "Pijl naar links (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr "Pijl omlaag (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr "Pijl omlaag (alt) icoon"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr "Amazon icoon"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr "Uitlijnen breed icoon"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr "Uitlijnen trek naar rechts icoon"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr "Uitlijnen trek naar links icoon"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr "Uitlijnen volledige breedte icoon"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr "Vliegtuig icoon"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr "Site (alt3) icoon"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr "Site (alt2) icoon"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr "Site (alt) icoon"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""
"Upgrade naar ACF Pro om opties pagina's te maken in slechts een paar klikken"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Ongeldige aanvraag args."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Je hebt geen toestemming om dat te doen."

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr "Blokken met behulp van bericht meta"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO logo"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "ACF PRO logo"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"%s vereist een geldig bijlage ID wanneer type is ingesteld op media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr "%s is een vereiste eigenschap van ACF."

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr "De waarde van icoon om op te slaan."

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr "Het type icoon om op te slaan."

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr "Ja icoon"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr "WordPress icoon"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr "Waarschuwingsicoon"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr "Zichtbaarheid icoon"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr "Kluis icoon"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr "Upload icoon"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr "Updaten icoon"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr "Ontgrendel icoon"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr "Universeel toegankelijkheid icoon"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr "Ongedaan maken icoon"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr "Twitter icoon"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr "Prullenbak icoon"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr "Vertaal icoon"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr "Tickets icoon"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr "Duim omhoog icoon"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr "Duim omlaag icoon"

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr "Tekst icoon"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr "Aanbeveling icoon"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr "Tag cloud icoon"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr "Tag icoon"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr "Tablet icoon"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr "Winkel icoon"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr "Sticky icoon"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr "Ster half icoon"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr "Ster gevuld icoon"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr "Ster leeg icoon"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr "Sos icoon"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr "Sorteer icoon"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr "Smiley icoon"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr "Smartphone icoon"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr "Slides icoon"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr "Schild icoon"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr "Deel icoon"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr "Zoek icoon"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr "Schermopties icoon"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr "Schema icoon"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr "Opnieuw icoon"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr "Willekeurig icoon"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr "Producten icoon"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr "Pressthis icoon"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr "Berichtstatus icoon"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr "Portfolio icoon"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr "Plus icoon"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr "Afspeellijst video icoon"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr "Afspeellijst audio icoon"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr "Telefoon icoon"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr "Prestatie icoon"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr "Paperclip icoon"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr "Geen icoon"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr "Netwerk icoon"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr "Naamplaat icoon"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr "Verplaats icoon"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr "Geld icoon"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr "Min icoon"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr "Migreer icoon"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr "Microfoon icoon"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr "Megafoon icoon"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr "Marker icoon"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr "Vergrendel icoon"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr "Locatie icoon"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr "Lijstweergave icoon"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr "Gloeilamp icoon"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr "Linkerrechter icoon"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr "Lay-out icoon"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr "Laptop icoon"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr "Info icoon"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr "Indexkaart icoon"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr "ID icoon"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr "Verborgen icoon"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr "Hart icoon"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr "Hamer icoon"

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr "Groepen icoon"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr "Rasterweergave icoon"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr "Formulieren icoon"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr "Vlag icoon"

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr "Filter icoon"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr "Feedback icoon"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr "Facebook alt icoon"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr "Facebook icoon"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr "Extern icoon"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr "E-mail alt icoon"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr "E-mail icoon"

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr "Video icoon"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr "Link verwijderen icoon"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr "Onderstreep icoon"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr "Tekstkleur icoon"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr "Tabel icoon"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr "Doorstreep icoon"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr "Spellingscontrole icoon"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr "Verwijder lay-out icoon"

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr "Quote icoon"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr "Plak woord icoon"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr "Plak tekst icoon"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr "Paragraaf icoon"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr "Uitspring icoon"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr "Keuken afwasbak icoon"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr "Uitlijn icoon"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr "Schuin icoon"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr "Voeg meer in icoon"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr "Inspring icoon"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr "Hulp icoon"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr "Uitvouw icoon"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr "Contract icoon"

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr "Code icoon"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr "Breek icoon"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr "Vet icoon"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr "Bewerken icoon"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr "Download icoon"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr "Verwijder icoon"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr "Desktop icoon"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr "Dashboard icoon"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr "Cloud icoon"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr "Klok icoon"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr "Klembord icoon"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr "Diagram taart icoon"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr "Grafieklijn icoon"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr "Grafiekbalk icoon"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr "Grafiek gebied icoon"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr "Categorie icoon"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr "Winkelwagen icoon"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr "Wortel icoon"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr "Camera icoon"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr "Kalender alt icoon"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr "Kalender icoon"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr "Zakenman icoon"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr "Gebouw icoon"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr "Boek icoon"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr "Back-up icoon"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr "Prijzen icoon"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr "Kunsticoon"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr "Pijl omhoog icoon"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr "Pijl naar rechts icoon"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr "Pijl naar links icoon"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr "Pijl omlaag icoon"

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr "Archief icoon"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr "Analytics icoon"

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr "Uitlijnen rechts icoon"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr "Uitlijnen geen icoon"

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr "Uitlijnen links icoon"

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr "Uitlijnen midden icoon"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr "Album icoon"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr "Gebruikers icoon"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr "Gereedschap icoon"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr "Site icoon"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr "Instellingen icoon"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr "Bericht icoon"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr "Plugins icoon"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr "Pagina icoon"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr "Netwerk icoon"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr "Multisite icoon"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr "Media icoon"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr "Links icoon"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr "Home icoon"

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr "Customizer icoon"

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr "Reacties icoon"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr "Samenvouw icoon"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr "Weergave icoon"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr "Generiek icoon"

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr "Icoon kiezer vereist een waarde."

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr "Icoon kiezer vereist een icoon type."

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"De beschikbare iconen die overeenkomen met je zoekopdracht zijn geüpdatet in "
"de icoon kiezer hieronder."

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr "Geen resultaten gevonden voor die zoekterm"

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr "String"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr "Specificeer het retour format voor het icoon. %s"

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr "Selecteer waar inhoudseditors het icoon kunnen kiezen."

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "De URL naar het icoon dat je wil gebruiken, of svg als gegevens URI"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr "Blader door mediabibliotheek"

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr "De momenteel geselecteerde afbeelding voorbeeld"

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr "Klik om het icoon in de mediabibliotheek te wijzigen"

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr "Iconen zoeken..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Mediabibliotheek"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Een interactieve UI voor het selecteren van een icoon. Selecteer uit "
"Dashicons, de mediatheek, of een zelfstandige URL invoer."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Icoon kiezer"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr "JSON laadpaden"

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr "JSON opslaan paden"

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr "Geregistreerde ACF formulieren"

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr "Shortcode ingeschakeld"

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr "Veldinstellingen tabs ingeschakeld"

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr "Veldtype modal ingeschakeld"

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr "Beheer UI ingeschakeld"

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr "Blok preloading ingeschakeld"

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr "Blokken per ACF block versie"

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr "Blokken per API versie"

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr "Geregistreerde ACF blokken"

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr "Licht"

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr "Standaard"

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr "REST API format"

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr "Geregistreerde opties pagina's (PHP)"

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr "Geregistreerde optie pagina's (JSON)"

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr "Geregistreerde optie pagina's (UI)"

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr "Opties pagina's UI ingeschakeld"

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr "Geregistreerde taxonomieën (JSON)"

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr "Geregistreerde taxonomieën (UI)"

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr "Geregistreerde berichttypen (JSON)"

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr "Geregistreerde berichttypen (UI)"

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr "Berichttypen en taxonomieën ingeschakeld"

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr "Aantal velden van derden per veldtype"

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr "Aantal velden per veldtype"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "Veldgroepen ingeschakeld voor GraphQL"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "Veldgroepen ingeschakeld voor REST API"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Geregistreerde veldgroepen (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Geregistreerde veldgroepen (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Geregistreerde veldgroepen (UI)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Actieve plugins"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Hoofdthema"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Actief thema"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Is multisite"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "MySQL versie"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "WordPress versie"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Vervaldatum abonnement"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Licentiestatus"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Licentietype"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "Gelicentieerde URL"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Licentie geactiveerd"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Gratis"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Plugin type"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Plugin versie"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Deze sectie bevat debuginformatie over je ACF configuratie die nuttig kan "
"zijn om aan ondersteuning te verstrekken."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr "Een ACF Block op deze pagina vereist aandacht voordat je kunt opslaan."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Deze gegevens worden gelogd terwijl we waarden detecteren die tijdens de "
"uitvoer zijn gewijzigd. %1$sClear log en sluiten%2$s na het ontsnappen van "
"de waarden in je code. De melding verschijnt opnieuw als we opnieuw "
"gewijzigde waarden detecteren."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Permanent negeren"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""
"Instructies voor inhoud editors. Getoond bij het indienen van gegevens."

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "Heeft geen term geselecteerd"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "Heeft een term geselecteerd"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Voorwaarden bevatten niet"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Voorwaarden bevatten"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "Term is niet gelijk aan"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "Term is gelijk aan"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "Heeft geen gebruiker geselecteerd"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "Heeft een gebruiker geselecteerd"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "Gebruikers bevatten niet"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Gebruikers bevatten"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "Gebruiker is niet gelijk aan"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "Gebruiker is gelijk aan"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "Heeft geen pagina geselecteerd"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "Heeft een pagina geselecteerd"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Pagina's bevatten niet"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Pagina's bevatten"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "Pagina is niet gelijk aan"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "Pagina is gelijk aan"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "Heeft geen relatie geselecteerd"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "Heeft een relatie geselecteerd"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "Heeft geen bericht geselecteerd"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "Heeft een bericht geselecteerd"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Berichten bevatten niet"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Berichten bevatten"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "Bericht is niet gelijk aan"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "Bericht is gelijk aan"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Relaties bevatten niet"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Relaties bevatten"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "Relatie is niet gelijk aan"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "Relatie is gelijk aan"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF velden"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO functie"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Vernieuw PRO om te ontgrendelen"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Vernieuw PRO licentie"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO velden kunnen niet bewerkt worden zonder een actieve licentie."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Activeer je ACF PRO licentie om veldgroepen toegewezen aan een ACF blok te "
"bewerken."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "Activeer je ACF PRO licentie om deze optiepagina te bewerken."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Het teruggeven van geëscaped HTML waarden is alleen mogelijk als "
"format_value ook true is. De veldwaarden zijn niet teruggegeven voor de "
"veiligheid."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Het teruggeven van een escaped HTML waarde is alleen mogelijk als "
"format_value ook waar is. De veldwaarde is niet teruggegeven voor de "
"veiligheid."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF escapes nu automatisch aan onveilige HTML bij weergave door "
"<code>the_field</code> of de ACF shortcode. We hebben vastgesteld dat de "
"uitvoer van sommige van je velden is gewijzigd door deze aanpassing, maar "
"dit hoeft geen brekende verandering te zijn. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Neem contact op met je sitebeheerder of ontwikkelaar voor meer informatie."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Leer meer"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Verberg&nbsp;details"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Toon&nbsp;details"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - weergegeven via %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Vernieuw ACF PRO licentie"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Vernieuw licentie"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Beheer licentie"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "'Hoge' positie wordt niet ondersteund in de blok-editor"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Upgrade naar ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF <a href=\"%s\" target=\"_blank\">opties pagina's</a> zijn aangepaste "
"beheerpagina's voor het beheren van globale instellingen via velden. Je kunt "
"meerdere pagina's en subpagina's maken."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Opties pagina toevoegen"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "In de editor gebruikt als plaatshouder van de titel."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Titel plaatshouder"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 maanden gratis"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Gekopieerd van %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Opties pagina's selecteren"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Dubbele taxonomie"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Creëer taxonomie"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplicaat berichttype"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Berichttype maken"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Veldgroepen linken"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Velden toevoegen"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Dit veld"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Feedback"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Ondersteuning"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "is ontwikkeld en wordt onderhouden door"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""
"Voeg deze %s toe aan de locatieregels van de geselecteerde veldgroepen."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Als je de bidirectionele instelling inschakelt, kun je een waarde updaten in "
"de doelvelden voor elke waarde die voor dit veld is geselecteerd, door het "
"bericht ID, taxonomie ID of gebruiker ID van het item dat wordt geüpdatet "
"toe te voegen of te verwijderen. Lees voor meer informatie de <a href=\"%s\" "
"target=\"_blank\">documentatie</a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Selecteer veld(en) om de verwijzing terug naar het item dat wordt geüpdatet "
"op te slaan. Je kunt dit veld selecteren. Doelvelden moeten compatibel zijn "
"met waar dit veld wordt weergegeven. Als dit veld bijvoorbeeld wordt "
"weergegeven in een taxonomie, dan moet je doelveld van het type taxonomie "
"zijn"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Doelveld"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Update een veld met de geselecteerde waarden en verwijs terug naar deze ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidirectioneel"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s veld"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Selecteer meerdere"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "WP engine logo"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Alleen kleine letters, underscores en streepjes, maximaal 32 karakters."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "De rechten naam voor het toewijzen van termen van deze taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Termen rechten toewijzen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "De rechten naam voor het verwijderen van termen van deze taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Termen rechten verwijderen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "De rechten naam voor het bewerken van termen van deze taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Termen rechten bewerken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "De naam van de rechten voor het beheren van termen van deze taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Beheer termen rechten"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Stelt in of berichten moeten worden uitgesloten van zoekresultaten en "
"pagina's van taxonomie archieven."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Meer gereedschappen van WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Gemaakt voor degenen die bouwen met WordPress, door het team van %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Bekijk prijzen & upgrade"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "Leer meer"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Versnel je workflow en ontwikkel betere sites met functies als ACF Blocks en "
"Options Pages, en geavanceerde veldtypen als Repeater, Flexible Content, "
"Clone en Gallery."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Ontgrendel geavanceerde functies en bouw nog meer met ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s velden"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Geen termen"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Geen berichttypen"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Geen berichten"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Geen taxonomieën"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Geen veld groepen"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Geen velden"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Geen beschrijving"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Elke bericht status"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Deze taxonomie sleutel is al in gebruik door een andere taxonomie die buiten "
"ACF is geregistreerd en kan daarom niet worden gebruikt."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Deze taxonomie sleutel is al in gebruik door een andere taxonomie in ACF en "
"kan daarom niet worden gebruikt."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"De taxonomie sleutel mag alleen kleine alfanumerieke tekens, underscores of "
"streepjes bevatten."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "De taxonomie sleutel moet minder dan 32 karakters bevatten."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Geen taxonomieën gevonden in prullenbak"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Geen taxonomieën gevonden"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Taxonomieën zoeken"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Taxonomie bekijken"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nieuwe taxonomie"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Taxonomie bewerken"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Nieuwe taxonomie toevoegen"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Geen berichttypen gevonden in prullenbak"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Geen berichttypen gevonden"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Berichttypen zoeken"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Berichttype bekijken"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Nieuw berichttype"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Berichttype bewerken"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Nieuw berichttype toevoegen"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Deze berichttype sleutel is al in gebruik door een ander berichttype dat "
"buiten ACF is geregistreerd en kan niet worden gebruikt."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Deze berichttype sleutel is al in gebruik door een ander berichttype in ACF "
"en kan niet worden gebruikt."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Dit veld mag geen door WordPress <a href=\"%s\" "
"target=\"_blank\">gereserveerde term</a> zijn."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Het berichttype mag alleen kleine alfanumerieke tekens, underscores of "
"streepjes bevatten."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "De berichttype sleutel moet minder dan 20 karakters bevatten."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Wij raden het gebruik van dit veld in ACF blokken af."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Toont de WordPress WYSIWYG editor zoals in berichten en pagina's voor een "
"rijke tekst bewerking ervaring die ook multi media inhoud mogelijk maakt."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG editor"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Maakt het mogelijk een of meer gebruikers te selecteren die kunnen worden "
"gebruikt om relaties te leggen tussen gegeven objecten."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "Een tekst invoer speciaal ontworpen voor het opslaan van web adressen."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Een toggle waarmee je een waarde van 1 of 0 kunt kiezen (aan of uit, waar of "
"onwaar, enz.). Kan worden gepresenteerd als een gestileerde schakelaar of "
"selectievakje."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Een interactieve UI voor het kiezen van een tijd. De tijd format kan worden "
"aangepast via de veldinstellingen."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Een basis tekstgebied voor het opslaan van alinea's tekst."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Een basis tekstveld, handig voor het opslaan van een enkele string waarde."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Maakt de selectie mogelijk van een of meer taxonomie termen op basis van de "
"criteria en opties die zijn opgegeven in de veldinstellingen."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Hiermee kun je in het bewerking scherm velden groeperen in secties met tabs. "
"Nuttig om velden georganiseerd en gestructureerd te houden."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Een dropdown lijst met een selectie van keuzes die je aangeeft."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Een interface met twee kolommen om een of meer berichten, pagina's of "
"aangepaste extra berichttype items te selecteren om een relatie te leggen "
"met het item dat je nu aan het bewerken bent. Inclusief opties om te zoeken "
"en te filteren."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Een veld voor het selecteren van een numerieke waarde binnen een "
"gespecificeerd bereik met behulp van een bereik slider element."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Een groep keuzerondjes waarmee de gebruiker één keuze kan maken uit waarden "
"die je opgeeft."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Een interactieve en aanpasbare UI voor het kiezen van één of meerdere "
"berichten, pagina's of berichttype-items met de optie om te zoeken. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""
"Een invoer voor het verstrekken van een wachtwoord via een afgeschermd veld."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filter op berichtstatus"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Een interactieve dropdown om een of meer berichten, pagina's, extra "
"berichttype items of archief URL's te selecteren, met de optie om te zoeken."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Een interactieve component voor het insluiten van video's, afbeeldingen, "
"tweets, audio en andere inhoud door gebruik te maken van de standaard "
"WordPress oEmbed functionaliteit."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Een invoer die beperkt is tot numerieke waarden."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Gebruikt om een bericht te tonen aan editors naast andere velden. Nuttig om "
"extra context of instructies te geven rond je velden."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Hiermee kun je een link en zijn eigenschappen zoals titel en doel "
"specificeren met behulp van de WordPress native link picker."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Gebruikt de standaard WordPress mediakiezer om afbeeldingen te uploaden of "
"te kiezen."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Biedt een manier om velden te structureren in groepen om de gegevens en het "
"bewerking scherm beter te organiseren."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Een interactieve UI voor het selecteren van een locatie met Google Maps. "
"Vereist een Google Maps API-sleutel en aanvullende instellingen om correct "
"te worden weergegeven."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Gebruikt de standaard WordPress mediakiezer om bestanden te uploaden of te "
"kiezen."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Een tekstinvoer speciaal ontworpen voor het opslaan van e-mailadressen."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Een interactieve UI voor het kiezen van een datum en tijd. De datum retour "
"format en tijd kunnen worden aangepast via de veldinstellingen."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Een interactieve UI voor het kiezen van een datum. Het format van de datum "
"kan worden aangepast via de veldinstellingen."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Een interactieve UI voor het selecteren van een kleur, of het opgeven van "
"een hex waarde."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Een groep selectievakjes waarmee de gebruiker één of meerdere waarden kan "
"selecteren die je opgeeft."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Een groep knoppen met waarden die je opgeeft, gebruikers kunnen één optie "
"kiezen uit de opgegeven waarden."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Hiermee kun je aangepaste velden groeperen en organiseren in inklapbare "
"panelen die worden getoond tijdens het bewerken van inhoud. Handig om grote "
"datasets netjes te houden."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Dit biedt een oplossing voor het herhalen van inhoud zoals slides, teamleden "
"en Call To Action tegels, door te fungeren als een hoofd voor een string sub "
"velden die steeds opnieuw kunnen worden herhaald."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Dit biedt een interactieve interface voor het beheerder van een verzameling "
"bijlagen. De meeste instellingen zijn vergelijkbaar met die van het veld "
"type afbeelding. Met extra instellingen kun je aangeven waar nieuwe bijlagen "
"in de galerij worden toegevoegd en het minimum/maximum aantal toegestane "
"bijlagen."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Dit biedt een eenvoudige, gestructureerde, op lay-out gebaseerde editor. Met "
"het veld flexibele inhoud kun je inhoud definiëren, creëren en beheren met "
"volledige controle door lay-outs en sub velden te gebruiken om de "
"beschikbare blokken vorm te geven."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Hiermee kun je bestaande velden selecteren en weergeven. Het dupliceert geen "
"velden in de database, maar laadt en toont de geselecteerde velden bij run "
"time. Het kloon veld kan zichzelf vervangen door de geselecteerde velden of "
"de geselecteerde velden weergeven als een groep sub velden."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Kloon"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Geavanceerd"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (nieuwer)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Origineel"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Ongeldig bericht ID."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Ongeldig berichttype geselecteerd voor beoordeling."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Meer"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Selecteer veld"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Probeer een andere zoekterm of blader door %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Populaire velden"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "Geen zoekresultaten voor '%s'"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Velden zoeken..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Selecteer veldtype"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Populair"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Taxonomie toevoegen"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Maak aangepaste taxonomieën aan om inhoud van berichttypen te classificeren"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Voeg je eerste taxonomie toe"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Hiërarchische taxonomieën kunnen afstammelingen hebben (zoals categorieën)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Maakt een taxonomie zichtbaar op de voorkant en in de beheerder dashboard."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Eén of vele berichttypes die met deze taxonomie kunnen worden ingedeeld."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Genres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Optionele aangepaste controller om te gebruiken in plaats van "
"`WP_REST_Terms_Controller `."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Stel dit berichttype bloot in de REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "De naam van de query variabele aanpassen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Termen zijn toegankelijk via de niet pretty permalink, bijvoorbeeld "
"{query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Hoofd sub termen in URL's voor hiërarchische taxonomieën."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Pas de slug in de URL aan"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Permalinks voor deze taxonomie zijn uitgeschakeld."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Herschrijf de URL met de taxonomie sleutel als slug. Je permalinkstructuur "
"zal zijn"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Taxonomie sleutel"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Selecteer het type permalink dat je voor deze taxonomie wil gebruiken."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Toon een kolom voor de taxonomie op de schermen voor het tonen van "
"berichttypes."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Toon beheerder kolom"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Toon de taxonomie in het snel/bulk bewerken paneel."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Snel bewerken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Vermeld de taxonomie in de tag cloud widget besturing elementen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Tag cloud"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Een PHP functienaam die moet worden aangeroepen om taxonomie gegevens "
"opgeslagen in een meta box te zuiveren."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Meta box sanitatie callback"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Meta box callback registreren"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Geen meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Aangepaste meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Bepaalt het meta box op het inhoud editor scherm. Standaard wordt het "
"categorie meta box getoond voor hiërarchische taxonomieën, en het tags meta "
"box voor niet hiërarchische taxonomieën."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Categorieën meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Tags meta box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Een link naar een tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Beschrijft een navigatie link blok variatie gebruikt in de blok-editor."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Een link naar een %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Tag link"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Wijst een titel toe aan de navigatie link blok variatie gebruikt in de blok-"
"editor."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Ga naar tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Wijst de tekst toe die wordt gebruikt om terug te linken naar de hoofd index "
"na het updaten van een term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Terug naar items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Ga naar %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Tags lijst"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Wijst tekst toe aan de verborgen koptekst van de tabel."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Tags lijst navigatie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""
"Wijst tekst toe aan de verborgen koptekst van de paginering van de tabel."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filter op categorie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Wijst tekst toe aan de filterknop in de lijst met berichten."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filter op item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filter op %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"De beschrijving is standaard niet prominent aanwezig; sommige thema's kunnen "
"hem echter wel tonen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Beschrijft het veld beschrijving in het scherm bewerken tags."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Omschrijving veld beschrijving"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Wijs een hoofdterm toe om een hiërarchie te creëren. De term jazz is "
"bijvoorbeeld het hoofd van Bebop en Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Beschrijft het hoofd veld op het bewerken tags scherm."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Hoofdveld beschrijving"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"De \"slug\" is de URL vriendelijke versie van de naam. Het zijn meestal "
"allemaal kleine letters en bevat alleen letters, cijfers en koppeltekens."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Beschrijft het slug veld op het bewerken tags scherm."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Slug veld beschrijving"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "De naam is zoals hij op je site staat"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Beschrijft het naamveld op het bewerken tags scherm."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Naamveld beschrijving"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Geen tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Wijst de tekst toe die wordt weergegeven in de tabellen met berichten en "
"media lijsten als er geen tags of categorieën beschikbaar zijn."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Geen termen"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Geen %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Geen tags gevonden"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Wijst de tekst toe die wordt weergegeven wanneer je klikt op de 'kies uit "
"meest gebruikte' tekst in het taxonomie meta box wanneer er geen tags "
"beschikbaar zijn, en wijst de tekst toe die wordt gebruikt in de termen "
"lijst tabel wanneer er geen items zijn voor een taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Niet gevonden"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Wijst tekst toe aan het titelveld van de tab meest gebruikt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Meest gebruikt"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Kies uit de meest gebruikte tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Wijst de 'kies uit meest gebruikte' tekst toe die wordt gebruikt in het meta "
"box wanneer JavaScript is uitgeschakeld. Alleen gebruikt op niet "
"hiërarchische taxonomieën."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Kies uit de meest gebruikte"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Kies uit de meest gebruikte %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Tags toevoegen of verwijderen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Wijst de tekst voor het toevoegen of verwijderen van items toe die wordt "
"gebruikt in het meta box wanneer JavaScript is uitgeschakeld. Alleen "
"gebruikt op niet hiërarchische taxonomieën"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Items toevoegen of verwijderen"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "%s toevoegen of verwijderen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Scheid tags met komma's"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Wijst de gescheiden item met komma's tekst toe die wordt gebruikt in het "
"taxonomie meta box. Alleen gebruikt op niet hiërarchische taxonomieën."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Scheid items met komma's"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Scheid %s met komma's"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Populaire tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Wijst populaire items tekst toe. Alleen gebruikt voor niet hiërarchische "
"taxonomieën."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Populaire items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Populaire %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Tags zoeken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Wijst zoek items tekst toe."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Hoofdcategorie:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Wijst hoofd item tekst toe, maar met een dubbele punt (:) toegevoegd aan het "
"einde."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Hoofditem met dubbele punt"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Hoofdcategorie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Wijst hoofd item tekst toe. Alleen gebruikt bij hiërarchische taxonomieën."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Hoofditem"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Hoofd %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nieuwe tagnaam"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Wijst de nieuwe item naam tekst toe."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nieuw item naam"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nieuwe %s naam"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Nieuwe tag toevoegen"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Wijst de tekst van het nieuwe item toe."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Tag updaten"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Wijst de tekst van het update item toe."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Item updaten"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "%s updaten"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Tag bekijken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "In de toolbar om de term te bekijken tijdens het bewerken."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Tag bewerken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Aan de bovenkant van het editor scherm bij het bewerken van een term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Alle tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Wijst de tekst van alle items toe."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Wijst de tekst van de menu naam toe."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Menulabel"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Actieve taxonomieën zijn ingeschakeld en geregistreerd bij WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Een beschrijvende samenvatting van de taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Een beschrijvende samenvatting van de term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Term beschrijving"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Eén woord, geen spaties. Underscores en streepjes zijn toegestaan."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Term slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "De naam van de standaard term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Term naam"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Maak een term aan voor de taxonomie die niet verwijderd kan worden. Deze zal "
"niet standaard worden geselecteerd voor berichten."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Standaard term"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Of termen in deze taxonomie moeten worden gesorteerd in de volgorde waarin "
"ze worden aangeleverd aan `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Termen sorteren"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Berichttype toevoegen"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Breid de functionaliteit van WordPress uit tot meer dan standaard berichten "
"en pagina's met aangepaste berichttypes."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Je eerste berichttype toevoegen"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Ik weet wat ik doe, laat me alle opties zien."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Geavanceerde configuratie"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""
"Hiërarchische bericht types kunnen afstammelingen hebben (zoals pagina's)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hiërarchisch"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Zichtbaar op de voorkant en in het beheerder dashboard."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Publiek"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Alleen kleine letters, underscores en streepjes, maximaal 20 karakters."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Enkelvoudig label"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Films"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Meervoud label"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Optionele aangepaste controller om te gebruiken in plaats van "
"`WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Controller klasse"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "De namespace sectie van de REST API URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Namespace route"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "De basis URL voor de berichttype REST API URL's."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "Basis URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Geeft dit berichttype weer in de REST API. Vereist om de blok-editor te "
"gebruiken."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Weergeven in REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Pas de naam van de query variabele aan."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Vraag variabele"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Geen ondersteuning voor query variabele"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Aangepaste query variabele"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Items zijn toegankelijk via de niet pretty permalink, bijv. {bericht_type}"
"={bericht_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Ondersteuning voor query variabelen"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"URL's voor een item en items kunnen worden benaderd met een query string."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Openbaar opvraagbaar"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Aangepaste slug voor het archief URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Archief slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Heeft een item archief dat kan worden aangepast met een archief template "
"bestand in je thema."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Archief"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Paginatie ondersteuning voor de items URL's zoals de archieven."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Paginering"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "RSS feed URL voor de items van het berichttype."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "Feed URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Wijzigt de permalink structuur om het `WP_Rewrite::$front` voorvoegsel toe "
"te voegen aan URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Front URL voorvoegsel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Pas de slug in de URL aan."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "URL slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Permalinks voor dit berichttype zijn uitgeschakeld."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Herschrijf de URL met behulp van een aangepaste slug, gedefinieerd in de "
"onderstaande invoer. Je permalink structuur zal zijn"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Geen permalink (voorkom URL herschrijving)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Aangepaste permalink"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Berichttype sleutel"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Herschrijf de URL met de berichttype sleutel als slug. Je permalink "
"structuur zal zijn"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Permalink herschrijven"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr ""
"Verwijder items van een gebruiker wanneer die gebruiker wordt verwijderd."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Verwijder met gebruiker"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Laat het berichttype exporteren via 'Gereedschap' > 'Exporteren'."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Kan geëxporteerd worden"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Geef desgewenst een meervoud dat in rechten moet worden gebruikt."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Meervoudige rechten naam"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Kies een ander berichttype om de rechten voor dit berichttype te baseren."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Enkelvoudige rechten naam"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Standaard erven de rechten van het berichttype de namen van de 'Bericht' "
"rechten, bv. Edit_bericht, delete_berichten. Activeer om berichttype "
"specifieke rechten te gebruiken, bijv. Edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Rechten hernoemen"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Uitsluiten van zoeken"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Sta toe dat items worden toegevoegd aan menu's in het scherm 'Weergave' > "
"'Menu's'. Moet ingeschakeld zijn in 'Scherminstellingen'."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Ondersteuning voor weergave menu's"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Verschijnt als een item in het menu \"Nieuw\" in de toolbar."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Toon in toolbar"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Aangepaste meta box callback"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Menu icoon"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "De positie in het zijbalk menu in het beheerder dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Menu positie"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Standaard krijgt het berichttype een nieuw top niveau item in het beheerder "
"menu. Als een bestaand top niveau item hier wordt aangeleverd, zal het "
"berichttype worden toegevoegd als een submenu item eronder."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Beheerder hoofd menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Beheerder editor navigatie in het zijbalk menu."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Toon in beheerder menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Items kunnen worden bewerkt en beheerd in het beheerder dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Weergeven in UI"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Een link naar een bericht."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Beschrijving voor een navigatie link blok variatie."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Item link beschrijving"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Een link naar een %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Bericht link"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Titel voor een navigatie link blok variatie."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Item link"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s link"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Bericht geüpdatet."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "In het editor bericht nadat een item is geüpdatet."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Item geüpdatet"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s geüpdatet."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Bericht ingepland."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "In het editor bericht na het plannen van een item."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Item gepland"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s gepland."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Bericht teruggezet naar concept."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "In het editor bericht na het terugdraaien van een item naar concept."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Item teruggezet naar concept"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s teruggezet naar het concept."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Bericht privé gepubliceerd."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "In het editor bericht na het publiceren van een privé item."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Item privé gepubliceerd"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s privé gepubliceerd."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Bericht gepubliceerd."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "In het editor bericht na het publiceren van een item."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Item gepubliceerd"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s gepubliceerd."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Berichtenlijst"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Gebruikt door scherm lezers voor de item lijst op het scherm van de "
"berichttypen lijst."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Items lijst"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s lijst"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Berichten lijst navigatie"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Gebruikt door scherm lezers voor de paginering van de filter lijst op het "
"scherm van de lijst met berichttypes."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Items lijst navigatie"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s lijst navigatie"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filter berichten op datum"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Gebruikt door scherm lezers voor de filter op datum koptekst in de lijst met "
"berichttypes."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filter items op datum"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filter %s op datum"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filter berichtenlijst"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Gebruikt door scherm lezers voor de koptekst filter links op het scherm van "
"de lijst met berichttypes."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filter itemlijst"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filter %s lijst"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"In het media modaal worden alle media getoond die naar dit item zijn "
"geüpload."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Geüpload naar dit item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Geüpload naar deze %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Invoegen in bericht"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Als knop label bij het toevoegen van media aan inhoud."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Invoegen in media knop"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Invoegen in %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Gebruik als uitgelichte afbeelding"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Als knop label voor het selecteren van een afbeelding als uitgelichte "
"afbeelding."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Gebruik uitgelichte afbeelding"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Verwijder uitgelichte afbeelding"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Als het knop label bij het verwijderen van de uitgelichte afbeelding."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Verwijder uitgelichte afbeelding"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Uitgelichte afbeelding instellen"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Als knop label bij het instellen van de uitgelichte afbeelding."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Uitgelichte afbeelding instellen"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Uitgelichte afbeelding"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"In de editor gebruikt voor de titel van de uitgelichte afbeelding meta box."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Uitgelichte afbeelding meta box"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Bericht attributen"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"In de editor gebruikt voor de titel van het bericht attributen meta box."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Attributen meta box"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s attributen"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Bericht archieven"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Voegt 'Berichttype archief' items met dit label toe aan de lijst van "
"berichten die getoond worden bij het toevoegen van items aan een bestaand "
"menu in een CPT met archieven ingeschakeld. Verschijnt alleen bij het "
"bewerken van menu's in 'Live voorbeeld' modus en wanneer een aangepaste "
"archief slug is opgegeven."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Archief nav menu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s archieven"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Geen berichten gevonden in de prullenbak"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"Aan de bovenkant van het scherm van de lijst met berichttypes wanneer er "
"geen berichten in de prullenbak zitten."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Geen items gevonden in de prullenbak"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Geen %s gevonden in de prullenbak"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Geen berichten gevonden"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"Aan de bovenkant van het scherm van de lijst met berichttypes wanneer er "
"geen berichten zijn om weer te geven."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Geen items gevonden"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Geen %s gevonden"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Berichten zoeken"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "Aan de bovenkant van het item scherm bij het zoeken naar een item."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Items zoeken"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "%s zoeken"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Hoofdpagina:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Voor hiërarchische types in het scherm van de berichttypen lijst."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Hoofditem voorvoegsel"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Hoofd %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Nieuw bericht"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Nieuw item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Nieuw %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Nieuw bericht toevoegen"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""
"Aan de bovenkant van het editor scherm bij het toevoegen van een nieuw item."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Nieuw item toevoegen"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Nieuwe %s toevoegen"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Berichten bekijken"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Verschijnt in de toolbar in de weergave 'Alle berichten', als het "
"berichttype archieven ondersteunt en de voorpagina geen archief is van dat "
"berichttype."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Items bekijken"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Bericht bekijken"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "In de toolbar om het item te bekijken wanneer je het bewerkt."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Item bekijken"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "%s bekijken"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Bericht bewerken"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "Aan de bovenkant van het editor scherm bij het bewerken van een item."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Item bewerken"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "%s bewerken"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Alle berichten"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "In het sub menu van het berichttype in het beheerder dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Alle items"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Alle %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Beheerder menu naam voor het berichttype."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Menu naam"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Alle labels opnieuw genereren met behulp van de labels voor enkelvoud en "
"meervoud"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Regenereren"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "Actieve berichttypes zijn ingeschakeld en geregistreerd bij WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Een beschrijvende samenvatting van het berichttype."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Aangepaste toevoegen"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Verschillende functies in de inhoud editor inschakelen."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Berichtformaten"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Selecteer bestaande taxonomieën om items van het berichttype te "
"classificeren."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Bladeren door velden"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Er is niets om te importeren"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". De Custom Post Type UI plugin kan worden gedeactiveerd."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "%d item geïmporteerd uit Custom Post Type UI -"
msgstr[1] "%d items geïmporteerd uit Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Kan taxonomieën niet importeren."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Kan berichttypen niet importeren."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "Niets van extra berichttype UI plugin geselecteerd voor import."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "1 item geïmporteerd"
msgstr[1] "%s items geïmporteerd"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Als je een berichttype of taxonomie importeert met dezelfde sleutel als een "
"reeds bestaand berichttype of taxonomie, worden de instellingen voor het "
"bestaande berichttype of de bestaande taxonomie overschreven met die van de "
"import."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importeer vanuit Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"De volgende code kan worden gebruikt om een lokale versie van de "
"geselecteerde items te registreren. Het lokaal opslaan van veldgroepen, "
"berichttypen of taxonomieën kan veel voordelen bieden, zoals snellere "
"laadtijden, versiebeheer en dynamische velden/instellingen. Kopieer en plak "
"de volgende code in het functions.php bestand van je thema of neem het op in "
"een extern bestand, en deactiveer of verwijder vervolgens de items uit de "
"ACF beheer."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Exporteren - PHP genereren"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Exporteren"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Taxonomieën selecteren"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Berichttypen selecteren"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "1 item geëxporteerd."
msgstr[1] "%s items geëxporteerd."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Categorie"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Tag"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s taxonomie aangemaakt"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s taxonomie geüpdatet"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Taxonomie concept geüpdatet."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomie ingepland voor."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomie ingediend."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomie opgeslagen."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomie verwijderd."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomie geüpdatet."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Deze taxonomie kon niet worden geregistreerd omdat de sleutel ervan in "
"gebruik is door een andere taxonomie die door een andere plugin of thema is "
"geregistreerd."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomie gesynchroniseerd."
msgstr[1] "%s taxonomieën gesynchroniseerd."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomie gedupliceerd."
msgstr[1] "%s taxonomieën gedupliceerd."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomie gedeactiveerd."
msgstr[1] "%s taxonomieën gedeactiveerd."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomie geactiveerd."
msgstr[1] "%s taxonomieën geactiveerd."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Termen"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Berichttype gesynchroniseerd."
msgstr[1] "%s berichttypen gesynchroniseerd."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Berichttype gedupliceerd."
msgstr[1] "%s berichttypen gedupliceerd."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Berichttype gedeactiveerd."
msgstr[1] "%s berichttypen gedeactiveerd."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Berichttype geactiveerd."
msgstr[1] "%s berichttypen geactiveerd."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Berichttypen"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Geavanceerde instellingen"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Basisinstellingen"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Dit berichttype kon niet worden geregistreerd omdat de sleutel ervan in "
"gebruik is door een ander berichttype dat door een andere plugin of een "
"ander thema is geregistreerd."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Pagina's"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Link bestaande veld groepen"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s berichttype aangemaakt"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Velden toevoegen aan %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s berichttype geüpdatet"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Berichttype concept geüpdatet."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Berichttype ingepland voor."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Berichttype ingediend."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Berichttype opgeslagen."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Berichttype geüpdatet."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Berichttype verwijderd."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Typ om te zoeken..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Alleen in PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Veldgroepen succesvol gelinkt."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importeer berichttypen en taxonomieën die zijn geregistreerd met extra "
"berichttype UI en beheerder ze met ACF. <a href=\"%s\">Aan de slag</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taxonomie"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "berichttype"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Klaar"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Veld groep(en)"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Selecteer één of meerdere veldgroepen..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Selecteer de veldgroepen om te linken."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Veldgroep succesvol gelinkt."
msgstr[1] "Veldgroepen succesvol gelinkt."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Registratie mislukt"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Dit item kon niet worden geregistreerd omdat zijn sleutel in gebruik is door "
"een ander item geregistreerd door een andere plugin of thema."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Rechten"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL's"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Zichtbaarheid"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Labels"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Tabs voor veldinstellingen"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[ACF shortcode waarde uitgeschakeld voor voorbeeld]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Modal sluiten"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Veld verplaatst naar andere groep"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Modal sluiten"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Begin een nieuwe groep van tabs bij dit tab."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nieuwe tabgroep"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Een gestileerde checkbox gebruiken met select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Andere keuze opslaan"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Andere keuze toestaan"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Toevoegen toggle alle"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Aangepaste waarden opslaan"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Aangepaste waarden toestaan"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Aangepaste waarden van het selectievakje mogen niet leeg zijn. Vink lege "
"waarden uit."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Updates"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields logo"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Wijzigingen opslaan"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Veldgroep titel"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Titel toevoegen"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Ben je nieuw bij ACF? Bekijk onze <a href=\"%s\" "
"target=\"_blank\">startersgids</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Veldgroep toevoegen"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF gebruikt <a href=\"%s\" target=\"_blank\">veldgroepen</a> om aangepaste "
"velden te groeperen, en die velden vervolgens te koppelen aan "
"bewerkingsschermen."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Voeg je eerste veldgroep toe"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Opties pagina's"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF blokken"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Galerij veld"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Flexibel inhoudsveld"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Herhaler veld"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Ontgrendel extra functies met ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Veldgroep verwijderen"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Gemaakt op %1$s om %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Groepsinstellingen"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Locatieregels"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Kies uit meer dan 30 veldtypes. <a href=\"%s\" target=\"_blank\">Meer "
"informatie</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Ga aan de slag met het maken van nieuwe aangepaste velden voor je berichten, "
"pagina's, extra berichttypes en andere WordPress inhoud."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Voeg je eerste veld toe"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Veld toevoegen"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Presentatie"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validatie"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Algemeen"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "JSON importeren"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Als JSON exporteren"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Veldgroep gedeactiveerd."
msgstr[1] "%s veldgroepen gedeactiveerd."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Veldgroep geactiveerd."
msgstr[1] "%s veldgroepen geactiveerd."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Deactiveren"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Deactiveer dit item"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Activeren"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Activeer dit item"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Veldgroep naar prullenbak verplaatsen?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inactief"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields en Advanced Custom Fields PRO kunnen niet "
"tegelijkertijd actief zijn. We hebben Advanced Custom Fields PRO automatisch "
"gedeactiveerd."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields en Advanced Custom Fields PRO kunnen niet "
"tegelijkertijd actief zijn. We hebben Advanced Custom Fields automatisch "
"gedeactiveerd."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s moet een gebruiker hebben met de rol %2$s."
msgstr[1] "%1$s moet een gebruiker hebben met een van de volgende rollen %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s moet een geldig gebruikers ID hebben."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Ongeldige aanvraag."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s is niet een van %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s moet term %2$s hebben."
msgstr[1] "%1$s moet een van de volgende termen hebben %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s moet van het berichttype %2$s zijn."
msgstr[1] "%1$s moet van een van de volgende berichttypes zijn %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s moet een geldig bericht ID hebben."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s vereist een geldig bijlage ID."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Toon in REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Transparantie inschakelen"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA array"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA string"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Hex string"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Upgrade naar PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Actief"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' is geen geldig e-mailadres"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Kleurwaarde"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Selecteer standaardkleur"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Kleur wissen"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blokken"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opties"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Gebruikers"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Menu-items"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Bijlagen"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomieën"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Berichten"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Laatst geüpdatet: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Dit bericht is niet beschikbaar voor verschil vergelijking."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Ongeldige veldgroep parameter(s)."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "In afwachting van opslaan"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Opgeslagen"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importeren"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Beoordeel wijzigingen"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Bevindt zich in: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Bevindt zich in plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Bevindt zich in thema: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Diverse"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Synchroniseer wijzigingen"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Diff laden"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Lokale JSON wijzigingen beoordelen"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Bezoek site"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Details bekijken"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Versie %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Informatie"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Helpdesk</a>. De ondersteuning "
"professionals op onze helpdesk zullen je helpen met meer diepgaande, "
"technische uitdagingen."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussies</a>. We hebben een actieve en "
"vriendelijke community op onze community forums die je misschien kunnen "
"helpen met de 'how-tos' van de ACF wereld."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentatie</a>. Onze uitgebreide "
"documentatie bevat referenties en handleidingen voor de meeste situaties die "
"je kunt tegenkomen."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Wij zijn fanatiek in ondersteuning en willen dat je met ACF het beste uit je "
"site haalt. Als je problemen ondervindt, zijn er verschillende plaatsen waar "
"je hulp kan vinden:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Hulp & ondersteuning"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Gebruik de tab Hulp & ondersteuning om contact op te nemen als je hulp nodig "
"hebt."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Voordat je je eerste veldgroep maakt, raden we je aan om eerst onze <a "
"href=\"%s\" target=\"_blank\">Aan de slag</a> gids te lezen om je vertrouwd "
"te maken met de filosofie en best practices van de plugin."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"De Advanced Custom Fields plugin biedt een visuele formulierbouwer om "
"WordPress bewerkingsschermen aan te passen met extra velden, en een "
"intuïtieve API om aangepaste veldwaarden weer te geven in elk thema template "
"bestand."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Overzicht"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Locatietype \"%s\" is al geregistreerd."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Klasse \"%s\" bestaat niet."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Ongeldige nonce."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Fout tijdens laden van veld."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Fout</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Gebruikersrol"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Reactie"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Berichtformat"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menu-item"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Berichtstatus"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menu's"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menulocaties"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Bericht taxonomie"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Subpagina (heeft hoofdpagina)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Hoofdpagina (heeft subpagina's)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Pagina op hoogste niveau (geen hoofdpagina)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Berichtenpagina"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Voorpagina"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Paginatype"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Back-end aan het bekijken"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Front-end aan het bekijken"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Ingelogd"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Huidige gebruiker"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Pagina template"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registreren"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Toevoegen / bewerken"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Gebruikersformulier"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Hoofdpagina"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Superbeheerder"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Huidige gebruikersrol"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Standaard template"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Bericht template"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Berichtcategorie"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Alle %s formats"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Bijlage"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "%s waarde is verplicht"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Toon dit veld als"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Voorwaardelijke logica"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "en"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Lokale JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Veld klonen"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Controleer ook of alle premium add-ons (%s) zijn geüpdatet naar de nieuwste "
"versie."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Deze versie bevat verbeteringen voor je database en vereist een upgrade."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Bedankt voor het updaten naar %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Database-upgrade vereist"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Opties pagina"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galerij"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Flexibele inhoud"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Herhaler"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Terug naar alle gereedschappen"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Als er meerdere veldgroepen op een bewerkingsscherm verschijnen, dan worden "
"de opties van de eerste veldgroep gebruikt (degene met het laagste volgorde "
"nummer)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecteer</b> items om ze te <b>verbergen</b> in het bewerkingsscherm."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Verberg op scherm"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Trackbacks verzenden"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Tags"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Categorieën"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Pagina attributen"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Auteur"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisies"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Reacties"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discussie"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Samenvatting"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Inhoudseditor"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Weergegeven in lijst met veldgroepen"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Veldgroepen met een lagere volgorde verschijnen als eerste"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Volgorde nr."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Onder velden"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Onder labels"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Instructie plaatsing"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Label plaatsing"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Zijkant"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normaal (na inhoud)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Hoog (na titel)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Positie"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Naadloos (geen meta box)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standaard (met metabox)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Stijl"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Type"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Sleutel"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Volgorde"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Veld sluiten"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "ID"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "klasse"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "breedte"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Wrapper attributen"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Vereist"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instructies"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Veldtype"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Eén woord, geen spaties. Underscores en verbindingsstrepen toegestaan"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Veldnaam"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Dit is de naam die op de BEWERK pagina zal verschijnen"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Veldlabel"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Verwijderen"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Veld verwijderen"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Verplaatsen"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Veld naar een andere groep verplaatsen"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Veld dupliceren"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Veld bewerken"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Sleep om te herschikken"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Deze veldgroep weergeven als"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Er zijn geen updates beschikbaar."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Database upgrade afgerond. <a href=\"%s\">Bekijk wat er nieuw is</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Upgradetaken lezen..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Upgrade mislukt."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Upgrade afgerond."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Gegevens upgraden naar versie %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Het is sterk aan te raden om eerst een back-up van de database te maken "
"voordat je de update uitvoert. Weet je zeker dat je de update nu wilt "
"uitvoeren?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Selecteer ten minste één site om te upgraden."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Database upgrade afgerond. <a href=\"%s\">Terug naar netwerk dashboard</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Site is up-to-date"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Site vereist database upgrade van %1$s naar %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Site"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Sites upgraden"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"De volgende sites vereisen een upgrade van de database. Selecteer de sites "
"die je wilt updaten en klik vervolgens op %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Regelgroep toevoegen"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Maak een set met regels aan om te bepalen welke aangepaste schermen deze "
"extra velden zullen gebruiken"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regels"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Gekopieerd"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Naar klembord kopiëren"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Selecteer de items die je wilt exporteren en selecteer dan je export "
"methode. Exporteer als JSON om te exporteren naar een .json bestand dat je "
"vervolgens kunt importeren in een andere ACF installatie. Genereer PHP om te "
"exporteren naar PHP code die je in je thema kunt plaatsen."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Veldgroepen selecteren"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Geen veldgroepen geselecteerd"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "PHP genereren"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Veldgroepen exporteren"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Importbestand is leeg"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Onjuist bestandstype"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Fout bij uploaden van bestand. Probeer het opnieuw"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Selecteer het Advanced Custom Fields JSON bestand dat je wilt importeren. "
"Wanneer je op de onderstaande import knop klikt, importeert ACF de items in "
"dat bestand."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Veldgroepen importeren"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sync"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Selecteer %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Dupliceren"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Dit item dupliceren"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Ondersteunt"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentatie"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Beschrijving"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Synchronisatie beschikbaar"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Veldgroep gesynchroniseerd."
msgstr[1] "%s veld groepen gesynchroniseerd."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Veldgroep gedupliceerd."
msgstr[1] "%s veldgroepen gedupliceerd."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actief <span class=\"count\">(%s)</span>"
msgstr[1] "Actief <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Beoordeel sites & upgrade"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Database upgraden"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Aangepaste velden"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Veld verplaatsen"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Selecteer de bestemming voor dit veld"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Het %1$s veld is nu te vinden in de %2$s veldgroep"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Verplaatsen afgerond."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Actief"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Veldsleutels"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Instellingen"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Locatie"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "kopie"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(dit veld)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Aangevinkt"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Aangepast veld verplaatsen"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Geen toggle velden beschikbaar"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Veldgroep titel is vereist"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Dit veld kan niet worden verplaatst totdat de wijzigingen zijn opgeslagen"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "De string \"field_\" mag niet voor de veldnaam staan"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Veldgroep concept geüpdatet."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Veldgroep gepland voor."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Veldgroep ingediend."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Veldgroep opgeslagen."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Veldgroep gepubliceerd."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Veldgroep verwijderd."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Veldgroep geüpdatet."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Gereedschap"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "is niet gelijk aan"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "is gelijk aan"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formulieren"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Pagina"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Bericht"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relationeel"

#: includes/fields.php:327
msgid "Choice"
msgstr "Keuze"

#: includes/fields.php:325
msgid "Basic"
msgstr "Basis"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Onbekend"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Veldtype bestaat niet"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Spam gevonden"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Bericht geüpdatet"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Updaten"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "E-mailadres valideren"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Inhoud"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Titel"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Veldgroep bewerken"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "Selectie is minder dan"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "Selectie is groter dan"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "Waarde is minder dan"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "Waarde is groter dan"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "Waarde bevat"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "Waarde komt overeen met patroon"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "Waarde is niet gelijk aan"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "Waarde is gelijk aan"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Heeft geen waarde"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Heeft een waarde"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Annuleren"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Weet je het zeker?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d velden vereisen aandacht"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 veld vereist aandacht"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Validatie mislukt"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Validatie geslaagd"

#: includes/media.php:54
msgid "Restricted"
msgstr "Beperkt"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Details dichtklappen"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Details uitklappen"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Geüpload naar dit bericht"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Updaten"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Bewerken"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "De aangebrachte wijzigingen gaan verloren als je deze pagina verlaat"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Het bestandstype moet %s zijn."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "of"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "De bestandsgrootte mag niet groter zijn dan %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "De bestandsgrootte moet minimaal %s zijn."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "De hoogte van de afbeelding mag niet hoger zijn dan %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "De hoogte van de afbeelding moet minimaal %dpx zijn."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "De breedte van de afbeelding mag niet groter zijn dan %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "De breedte van de afbeelding moet ten minste %dpx zijn."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(geen titel)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Volledige grootte"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Groot"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Medium"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(geen label)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Bepaalt de hoogte van het tekstgebied"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Rijen"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Tekstgebied"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Voeg ervoor een extra selectievakje toe om alle keuzes te togglen"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Sla 'aangepaste' waarden op in de keuzes van het veld"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Toestaan dat 'aangepaste' waarden worden toegevoegd"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Nieuwe keuze toevoegen"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Toggle alles"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Archief URL's toestaan"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archieven"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Pagina link"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Toevoegen"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Naam"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s toegevoegd"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s bestaat al"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "Gebruiker kan geen nieuwe %s toevoegen"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Term object"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Laad waarde van bericht termen"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Laad termen"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Geselecteerde termen aan het bericht koppelen"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Termen opslaan"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Toestaan dat nieuwe termen worden gemaakt tijdens het bewerken"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Termen maken"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Keuzerondjes"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Eén waarde"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Multi selecteren"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Selectievakje"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Meerdere waarden"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Selecteer de weergave van dit veld"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Weergave"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Selecteer de taxonomie die moet worden weergegeven"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Geen %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "De waarde moet gelijk zijn aan of lager zijn dan %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "De waarde moet gelijk zijn aan of hoger zijn dan %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Waarde moet een getal zijn"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Nummer"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "'Andere' waarden opslaan in de keuzes van het veld"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Voeg de keuze 'overig' toe om aangepaste waarden toe te staan"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Ander"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Keuzerondje"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definieer een endpoint waar de vorige accordeon moet stoppen. Deze accordeon "
"is niet zichtbaar."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Deze accordeon openen zonder anderen te sluiten."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Multi uitvouwen"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Geef deze accordeon weer als geopend bij het laden van de pagina."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Openen"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Accordeon"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Beperken welke bestanden kunnen worden geüpload"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "Bestands ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "Bestands URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Bestands array"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Bestand toevoegen"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Geen bestand geselecteerd"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Bestandsnaam"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Bestand updaten"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Bestand bewerken"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Bestand selecteren"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Bestand"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Wachtwoord"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Geef de geretourneerde waarde op"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Ajax gebruiken om keuzes te lazy-loaden?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Zet elke standaardwaarde op een nieuwe regel"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Selecteren"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Laden mislukt"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Zoeken&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Meer resultaten laden&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Je kunt slechts %d items selecteren"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Je kan slechts 1 item selecteren"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Verwijder %d tekens"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Verwijder 1 teken"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Voer %d of meer tekens in"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Voer 1 of meer tekens in"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Geen overeenkomsten gevonden"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultaten zijn beschikbaar, gebruik de pijltoetsen omhoog en omlaag om "
"te navigeren."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Er is één resultaat beschikbaar, druk op enter om het te selecteren."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Selecteer"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "Gebruikers-ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Gebruikersobject"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Gebruiker array"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Alle gebruikersrollen"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filter op rol"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Gebruiker"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Scheidingsteken"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Selecteer kleur"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Standaard"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Wissen"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Kleurkiezer"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selecteer"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Klaar"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nu"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Tijdzone"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microseconde"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milliseconde"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Seconde"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuut"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Uur"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tijd"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Kies tijd"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Datum tijd kiezer"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Links uitgelijnd"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Boven uitgelijnd"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Plaatsing"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Waarde moet een geldige URL zijn"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Link array"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Opent in een nieuw venster/tab"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Link selecteren"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "E-mailadres"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Stapgrootte"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Maximale waarde"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Minimum waarde"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Bereik"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Beide (array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Label"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Waarde"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Verticaal"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontaal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "rood : Rood"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Voor meer controle kan je zowel een waarde als een label als volgt "
"specificeren:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Voer elke keuze in op een nieuwe regel."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Keuzes"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Knopgroep"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Null toestaan"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Hoofd"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE wordt niet geïnitialiseerd totdat er op het veld wordt geklikt"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Initialisatie uitstellen"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Media upload knoppen weergeven"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Toolbar"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Alleen tekst"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Alleen visueel"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visueel & tekst"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Tabs"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Klik om TinyMCE te initialiseren"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visueel"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "De waarde mag niet langer zijn dan %d karakters"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Laat leeg voor geen limiet"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Karakterlimiet"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Verschijnt na de invoer"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Toevoegen"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Verschijnt vóór de invoer"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Voorvoegen"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Wordt weergegeven in de invoer"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Plaatshouder tekst"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Wordt weergegeven bij het maken van een nieuw bericht"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s vereist minimaal %2$s selectie"
msgstr[1] "%1$s vereist minimaal %2$s selecties"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "Bericht ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Bericht object"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Maximum aantal berichten"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Minimum aantal berichten"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Uitgelichte afbeelding"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Geselecteerde elementen worden weergegeven in elk resultaat"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elementen"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Berichttype"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filters"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Alle taxonomieën"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filter op taxonomie"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Alle berichttypen"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filter op berichttype"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Zoeken..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Taxonomie selecteren"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Selecteer berichttype"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Geen overeenkomsten gevonden"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Laden"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Maximale waarden bereikt ( {max} waarden )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Verwantschap"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Komma gescheiden lijst. Laat leeg voor alle typen"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Toegestane bestandstypen"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Bestandsgrootte"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Beperken welke afbeeldingen kunnen worden geüpload"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Geüpload naar bericht"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Alle"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Beperk de keuze van de mediabibliotheek"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Bibliotheek"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Voorbeeld grootte"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Afbeelding ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "Afbeelding URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Afbeelding array"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "De geretourneerde waarde op de front-end opgeven"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Retour waarde"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Afbeelding toevoegen"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Geen afbeelding geselecteerd"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Verwijderen"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Bewerken"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Alle afbeeldingen"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Afbeelding updaten"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Afbeelding bewerken"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Selecteer afbeelding"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Afbeelding"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Sta toe dat HTML markeringen worden weergegeven als zichtbare tekst in "
"plaats van als weergave"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "HTML escapen"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Geen opmaak"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Automatisch &lt;br&gt; toevoegen;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Automatisch alinea's toevoegen"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Bepaalt hoe nieuwe regels worden weergegeven"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nieuwe regels"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Week begint op"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Het format dat wordt gebruikt bij het opslaan van een waarde"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Format opslaan"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Vorige"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Volgende"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Vandaag"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Klaar"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Datumkiezer"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Breedte"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Insluit grootte"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "URL invoeren"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Tekst getoond indien inactief"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Uit tekst"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Tekst getoond indien actief"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Op tekst"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Gestileerde UI"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Standaardwaarde"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Toont tekst naast het selectievakje"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Bericht"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "Nee"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Rij"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Tabel"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr ""
"Geef de stijl op die wordt gebruikt om de geselecteerde velden weer te geven"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Lay-out"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Subvelden"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Groep"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "De kaarthoogte aanpassen"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Hoogte"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Het initiële zoomniveau instellen"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "De eerste kaart centreren"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Midden"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Zoek naar adres..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Huidige locatie opzoeken"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Locatie wissen"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Zoeken"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Deze browser ondersteunt geen geolocatie"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Het format dat wordt geretourneerd via templatefuncties"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Retour format"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Aangepast:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Het format dat wordt weergegeven bij het bewerken van een bericht"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Weergave format"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Tijdkiezer"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactief <span class=\"count\">(%s)</span>"
msgstr[1] "Inactief <span class=\"count\">(%s)</span>"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "Geen velden gevonden in de prullenbak"

#: acf.php:486
msgid "No Fields found"
msgstr "Geen velden gevonden"

#: acf.php:485
msgid "Search Fields"
msgstr "Velden zoeken"

#: acf.php:484
msgid "View Field"
msgstr "Veld bekijken"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nieuw veld"

#: acf.php:482
msgid "Edit Field"
msgstr "Veld bewerken"

#: acf.php:481
msgid "Add New Field"
msgstr "Nieuw veld toevoegen"

#: acf.php:479
msgid "Field"
msgstr "Veld"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Velden"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "Geen veldgroepen gevonden in de prullenbak"

#: acf.php:452
msgid "No Field Groups found"
msgstr "Geen veldgroepen gevonden"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Veldgroepen zoeken"

#: acf.php:450
msgid "View Field Group"
msgstr "Veldgroep bekijken"

#: acf.php:449
msgid "New Field Group"
msgstr "Nieuwe veldgroep"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Veldgroep bewerken"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Nieuwe veldgroep toevoegen"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Nieuwe toevoegen"

#: acf.php:445
msgid "Field Group"
msgstr "Veldgroep"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Veldgroepen"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Pas WordPress aan met krachtige, professionele en intuïtieve velden."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:21
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/acf-pro.php:174
msgid ""
"Your ACF PRO license is no longer active. Please renew to continue to have "
"access to updates, support, & PRO features."
msgstr ""
"Je ACF PRO licentie is niet langer actief. Verleng om toegang te blijven "
"houden tot updates, ondersteuning en PRO functies."

#: pro/acf-pro.php:171
msgid ""
"Your license has expired. Please renew to continue to have access to "
"updates, support &amp; PRO features."
msgstr ""
"Je licentie is verlopen. Verleng om toegang te blijven houden tot updates, "
"ondersteuning &amp; PRO functies."

#: pro/acf-pro.php:168
msgid ""
"Activate your license to enable access to updates, support &amp; PRO "
"features."
msgstr ""
"Activeer je licentie om toegang te krijgen tot updates, ondersteuning &amp; "
"PRO functies."

#: pro/acf-pro.php:257
msgid "A valid license is required to edit options pages."
msgstr "Je hebt een geldige licentie nodig om opties pagina's te bewerken."

#: pro/acf-pro.php:255
msgid "A valid license is required to edit field groups assigned to a block."
msgstr ""
"Je hebt een geldige licentie nodig om veldgroepen, die aan een blok zijn "
"toegewezen, te bewerken."

#: pro/blocks.php:186
msgid "Block type name is required."
msgstr "De naam van het bloktype is verplicht."

#. translators: The name of the block type
#: pro/blocks.php:194
msgid "Block type \"%s\" is already registered."
msgstr "Bloktype “%s” is al geregistreerd."

#: pro/blocks.php:740
msgid "The render template for this ACF Block was not found"
msgstr "De rendertemplate voor dit ACF blok is niet gevonden"

#: pro/blocks.php:790
msgid "Switch to Edit"
msgstr "Schakel naar bewerken"

#: pro/blocks.php:791
msgid "Switch to Preview"
msgstr "Schakel naar voorbeeld"

#: pro/blocks.php:792
msgid "Change content alignment"
msgstr "Inhoudsuitlijning wijzigen"

#: pro/blocks.php:793
msgid "An error occurred when loading the preview for this block."
msgstr ""
"Er is een fout opgetreden bij het laden van het voorbeeld voor dit blok."

#: pro/blocks.php:794
msgid "An error occurred when loading the block in edit mode."
msgstr ""
"Er is een fout opgetreden bij het laden van het blok in bewerkingsmodus."

#. translators: %s: Block type title
#: pro/blocks.php:797
msgid "%s settings"
msgstr "%s instellingen"

#: pro/blocks.php:1039
msgid "This block contains no editable fields."
msgstr "Dit blok bevat geen bewerkbare velden."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:1045
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Wijs een <a href=“%s” target=“_blank”>veldgroep</a> toe om velden aan dit "
"blok toe te voegen."

#: pro/options-page.php:74, pro/post-types/acf-ui-options-page.php:174
msgid "Options Updated"
msgstr "Opties geüpdatet"

#. translators: %1 A link to the updates page. %2 link to the pricing page
#: pro/updates.php:75
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a license key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Om updates te ontvangen, vul je hieronder je licentiecode in. Als je geen "
"licentiesleutel hebt, raadpleeg dan <a href=“%2$s” target=“_blank”>details & "
"prijzen</a>."

#: pro/updates.php:71
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page of the main site. If you don't have a license "
"key, please see <a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Om updates in te schakelen, voer je je licentiesleutel in op de <a "
"href=\"%1$s\">Updates</a> pagina van de hoofdsite. Als je geen "
"licentiesleutel hebt, raadpleeg dan <a href=“%2$s” target=“_blank”>details & "
"prijzen</a>."

#: pro/updates.php:136
msgid ""
"Your defined license key has changed, but an error occurred when "
"deactivating your old license"
msgstr ""
"Je gedefinieerde licentiesleutel is gewijzigd, maar er is een fout "
"opgetreden bij het deactiveren van je oude licentie"

#: pro/updates.php:133
msgid ""
"Your defined license key has changed, but an error occurred when connecting "
"to activation server"
msgstr ""
"Je gedefinieerde licentiesleutel is gewijzigd, maar er is een fout "
"opgetreden bij het verbinden met de activeringsserver"

#: pro/updates.php:168
msgid ""
"<strong>ACF PRO &mdash;</strong> Your license key has been activated "
"successfully. Access to updates, support &amp; PRO features is now enabled."
msgstr ""
"<strong>ACF PRO &mdash;</strong> Je licentiesleutel is succesvol "
"geactiveerd. Toegang tot updates, ondersteuning &amp; PRO functies is nu "
"ingeschakeld."

#: pro/updates.php:159
msgid "There was an issue activating your license key."
msgstr ""
"Er is een probleem opgetreden bij het activeren van je licentiesleutel."

#: pro/updates.php:155
msgid "An error occurred when connecting to activation server"
msgstr "Er is een fout opgetreden bij het verbinden met de activeringsserver"

#: pro/updates.php:258
msgid ""
"The ACF activation service is temporarily unavailable. Please try again "
"later."
msgstr ""
"De ACF activeringsservice is tijdelijk niet beschikbaar. Probeer het later "
"nog eens."

#: pro/updates.php:256
msgid ""
"The ACF activation service is temporarily unavailable for scheduled "
"maintenance. Please try again later."
msgstr ""
"De ACF activeringsservice is tijdelijk niet beschikbaar voor gepland "
"onderhoud. Probeer het later nog eens."

#: pro/updates.php:254
msgid ""
"An upstream API error occurred when checking your ACF PRO license status. We "
"will retry again shortly."
msgstr ""
"Er is een API fout opgetreden bij het controleren van je ACF PRO "
"licentiestatus. We zullen het binnenkort opnieuw proberen."

#: pro/updates.php:224
msgid "You have reached the activation limit for the license."
msgstr "Je hebt de activeringslimiet voor de licentie bereikt."

#: pro/updates.php:233, pro/updates.php:205
msgid "View your licenses"
msgstr "Je licenties bekijken"

#: pro/updates.php:246
msgid "check again"
msgstr "opnieuw controleren"

#: pro/updates.php:250
msgid "%1$s or %2$s."
msgstr "%1$s of %2$s."

#: pro/updates.php:210
msgid "Your license key has expired and cannot be activated."
msgstr "Je licentiesleutel is verlopen en kan niet worden geactiveerd."

#: pro/updates.php:219
msgid "View your subscriptions"
msgstr "Je abonnementen bekijken"

#: pro/updates.php:196
msgid ""
"License key not found. Make sure you have copied your license key exactly as "
"it appears in your receipt or your account."
msgstr ""
"Licentiesleutel niet gevonden. Zorg ervoor dat je de licentiesleutel precies "
"zo hebt gekopieerd als op je ontvangstbewijs of in je account."

#: pro/updates.php:194
msgid "Your license key has been deactivated."
msgstr "Je licentiesleutel is gedeactiveerd."

#: pro/updates.php:192
msgid ""
"Your license key has been activated successfully. Access to updates, support "
"&amp; PRO features is now enabled."
msgstr ""
"Je licentiesleutel is succesvol geactiveerd. Toegang tot updates, "
"ondersteuning &amp; PRO functies is nu ingeschakeld."

#. translators: %s an untranslatable internal upstream error message
#: pro/updates.php:262
msgid ""
"An unknown error occurred while trying to communicate with the ACF "
"activation service: %s."
msgstr ""
"Er is een onbekende fout opgetreden tijdens het communiceren met de ACF "
"activeringsservice: %s."

#: pro/updates.php:333, pro/updates.php:949
msgid "<strong>ACF PRO &mdash;</strong>"
msgstr "<strong>ACF PRO &mdash;</strong>"

#: pro/updates.php:342
msgid "Check again"
msgstr "Opnieuw controleren"

#: pro/updates.php:657
msgid "Could not connect to the activation server"
msgstr "Kon niet verbinden met de activeringsserver"

#. translators: %s - URL to ACF updates page
#: pro/updates.php:727
msgid ""
"Your license key is valid but not activated on this site. Please <a "
"href=\"%s\">deactivate</a> and then reactivate the license."
msgstr ""
"Je licentiesleutel is geldig maar niet geactiveerd op deze site. <a "
"href=\"%s\">Deactiveer</a> de licentie en activeer deze opnieuw."

#: pro/updates.php:949
msgid ""
"Your site URL has changed since last activating your license. We've "
"automatically activated it for this site URL."
msgstr ""
"De URL van je site is veranderd sinds de laatste keer dat je je licentie "
"hebt geactiveerd. We hebben het automatisch geactiveerd voor deze site URL."

#: pro/updates.php:941
msgid ""
"Your site URL has changed since last activating your license, but we weren't "
"able to automatically reactivate it: %s"
msgstr ""
"De URL van je site is veranderd sinds de laatste keer dat je je licentie "
"hebt geactiveerd, maar we hebben hem niet automatisch opnieuw kunnen "
"activeren: %s"

#: pro/admin/admin-options-page.php:159
msgid "Publish"
msgstr "Publiceren"

#: pro/admin/admin-options-page.php:162
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Er zijn geen groepen gevonden voor deze opties pagina. <a href=\"%s\">Maak "
"een extra veldgroep</a>"

#: pro/admin/admin-updates.php:52
msgid "<strong>Error</strong>. Could not connect to the update server"
msgstr "<b>Fout</b>. Kon niet verbinden met de updateserver"

#. translators: %s the version of WordPress required for this ACF update
#: pro/admin/admin-updates.php:203
msgid ""
"An update to ACF is available, but it is not compatible with your version of "
"WordPress. Please upgrade to WordPress %s or newer to update ACF."
msgstr ""
"Er is een update voor ACF beschikbaar, maar deze is niet compatibel met jouw "
"versie van WordPress. Upgrade naar WordPress %s of nieuwer om ACF te updaten."

#: pro/admin/admin-updates.php:224
msgid ""
"<strong>Error</strong>. Could not authenticate update package. Please check "
"again or deactivate and reactivate your ACF PRO license."
msgstr ""
"<strong>Fout</strong>. Kan het updatepakket niet verifiëren. Controleer "
"opnieuw of deactiveer en heractiveer je ACF PRO licentie."

#: pro/admin/admin-updates.php:214
msgid ""
"<strong>Error</strong>. Your license for this site has expired or been "
"deactivated. Please reactivate your ACF PRO license."
msgstr ""
"<strong>Fout</strong>. Je licentie voor deze site is verlopen of "
"gedeactiveerd. Activeer je ACF PRO licentie opnieuw."

#: pro/fields/class-acf-field-clone.php:24
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""
"Hiermee kan je bestaande velden selecteren en weergeven. Het dupliceert geen "
"velden in de database, maar laadt en toont de geselecteerde velden bij run-"
"time. Het kloonveld kan zichzelf vervangen door de geselecteerde velden of "
"de geselecteerde velden weergeven als een groep subvelden."

#: pro/fields/class-acf-field-clone.php:725
msgid "Select one or more fields you wish to clone"
msgstr "Selecteer één of meer velden om te klonen"

#: pro/fields/class-acf-field-clone.php:745
msgid "Display"
msgstr "Weergeven"

#: pro/fields/class-acf-field-clone.php:746
msgid "Specify the style used to render the clone field"
msgstr "Kies de gebruikte stijl bij het renderen van het gekloonde veld"

#: pro/fields/class-acf-field-clone.php:751
msgid "Group (displays selected fields in a group within this field)"
msgstr "Groep (toont geselecteerde velden in een groep binnen dit veld)"

#: pro/fields/class-acf-field-clone.php:752
msgid "Seamless (replaces this field with selected fields)"
msgstr "Naadloos (vervangt dit veld met de geselecteerde velden)"

#: pro/fields/class-acf-field-clone.php:775
msgid "Labels will be displayed as %s"
msgstr "Labels worden weergegeven als %s"

#: pro/fields/class-acf-field-clone.php:780
msgid "Prefix Field Labels"
msgstr "Prefix veld labels"

#: pro/fields/class-acf-field-clone.php:790
msgid "Values will be saved as %s"
msgstr "Waarden worden opgeslagen als %s"

#: pro/fields/class-acf-field-clone.php:795
msgid "Prefix Field Names"
msgstr "Prefix veld namen"

#: pro/fields/class-acf-field-clone.php:892
msgid "Unknown field"
msgstr "Onbekend veld"

#: pro/fields/class-acf-field-clone.php:925
msgid "Unknown field group"
msgstr "Onbekend groep"

#: pro/fields/class-acf-field-clone.php:929
msgid "All fields from %s field group"
msgstr "Alle velden van %s veldgroep"

#: pro/fields/class-acf-field-flexible-content.php:24
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""
"Hiermee kan je inhoud definiëren, creëren en beheren met volledige controle "
"door lay-outs te maken die subvelden bevatten waaruit inhoudsredacteuren "
"kunnen kiezen."

#: pro/fields/class-acf-field-flexible-content.php:34,
#: pro/fields/class-acf-field-repeater.php:104,
#: pro/fields/class-acf-field-repeater.php:298
msgid "Add Row"
msgstr "Nieuwe rij"

#: pro/fields/class-acf-field-flexible-content.php:70,
#: pro/fields/class-acf-field-flexible-content.php:867,
#: pro/fields/class-acf-field-flexible-content.php:949
msgid "layout"
msgid_plural "layouts"
msgstr[0] "lay-out"
msgstr[1] "lay-outs"

#: pro/fields/class-acf-field-flexible-content.php:71
msgid "layouts"
msgstr "lay-outs"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:866,
#: pro/fields/class-acf-field-flexible-content.php:948
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Dit veld vereist op zijn minst {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Dit veld heeft een limiet van {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} beschikbaar (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} verplicht (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibele inhoud vereist minimaal 1 lay-out"

#. translators: %s the button label used for adding a new layout.
#: pro/fields/class-acf-field-flexible-content.php:255
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klik op de \"%s\" knop om een nieuwe lay-out te maken"

#: pro/fields/class-acf-field-flexible-content.php:378
msgid "Add layout"
msgstr "Lay-out toevoegen"

#: pro/fields/class-acf-field-flexible-content.php:379
msgid "Duplicate layout"
msgstr "Lay-out dupliceren"

#: pro/fields/class-acf-field-flexible-content.php:380
msgid "Remove layout"
msgstr "Lay-out verwijderen"

#: pro/fields/class-acf-field-flexible-content.php:381,
#: pro/fields/class-acf-repeater-table.php:380
msgid "Click to toggle"
msgstr "Klik om in/uit te klappen"

#: pro/fields/class-acf-field-flexible-content.php:517
msgid "Delete Layout"
msgstr "Lay-out verwijderen"

#: pro/fields/class-acf-field-flexible-content.php:518
msgid "Duplicate Layout"
msgstr "Lay-out dupliceren"

#: pro/fields/class-acf-field-flexible-content.php:519
msgid "Add New Layout"
msgstr "Nieuwe layout"

#: pro/fields/class-acf-field-flexible-content.php:519
msgid "Add Layout"
msgstr "Lay-out toevoegen"

#: pro/fields/class-acf-field-flexible-content.php:603
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:618
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:659
msgid "Minimum Layouts"
msgstr "Minimale layouts"

#: pro/fields/class-acf-field-flexible-content.php:670
msgid "Maximum Layouts"
msgstr "Maximale lay-outs"

#: pro/fields/class-acf-field-flexible-content.php:681,
#: pro/fields/class-acf-field-repeater.php:294
msgid "Button Label"
msgstr "Knop label"

#: pro/fields/class-acf-field-flexible-content.php:1552,
#: pro/fields/class-acf-field-repeater.php:913
msgid "%s must be of type array or null."
msgstr "%s moet van het type array of null zijn."

#: pro/fields/class-acf-field-flexible-content.php:1563
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s moet minstens %2$s %3$s lay-out bevatten."
msgstr[1] "%1$s moet minstens %2$s %3$s lay-outs bevatten."

#: pro/fields/class-acf-field-flexible-content.php:1579
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s moet hoogstens %2$s %3$s lay-out bevatten."
msgstr[1] "%1$s moet hoogstens %2$s %3$s lay-outs bevatten."

#: pro/fields/class-acf-field-gallery.php:24
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""
"Een interactieve interface voor het beheer van een verzameling van bijlagen, "
"zoals afbeeldingen."

#: pro/fields/class-acf-field-gallery.php:72
msgid "Add Image to Gallery"
msgstr "Afbeelding toevoegen aan galerij"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Maximum selection reached"
msgstr "Maximale selectie bereikt"

#: pro/fields/class-acf-field-gallery.php:282
msgid "Length"
msgstr "Lengte"

#: pro/fields/class-acf-field-gallery.php:326
msgid "Caption"
msgstr "Onderschrift"

#: pro/fields/class-acf-field-gallery.php:338
msgid "Alt Text"
msgstr "Alt tekst"

#: pro/fields/class-acf-field-gallery.php:460
msgid "Add to gallery"
msgstr "Afbeelding(en) toevoegen"

#: pro/fields/class-acf-field-gallery.php:464
msgid "Bulk actions"
msgstr "Bulkacties"

#: pro/fields/class-acf-field-gallery.php:465
msgid "Sort by date uploaded"
msgstr "Sorteren op datum geüpload"

#: pro/fields/class-acf-field-gallery.php:466
msgid "Sort by date modified"
msgstr "Sorteren op datum aangepast"

#: pro/fields/class-acf-field-gallery.php:467
msgid "Sort by title"
msgstr "Sorteren op titel"

#: pro/fields/class-acf-field-gallery.php:468
msgid "Reverse current order"
msgstr "Volgorde omkeren"

#: pro/fields/class-acf-field-gallery.php:480
msgid "Close"
msgstr "Sluiten"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Minimum Selection"
msgstr "Minimale selectie"

#: pro/fields/class-acf-field-gallery.php:577
msgid "Maximum Selection"
msgstr "Maximale selectie"

#: pro/fields/class-acf-field-gallery.php:679
msgid "Insert"
msgstr "Invoegen"

#: pro/fields/class-acf-field-gallery.php:680
msgid "Specify where new attachments are added"
msgstr "Geef aan waar nieuwe bijlagen worden toegevoegd"

#: pro/fields/class-acf-field-gallery.php:684
msgid "Append to the end"
msgstr "Toevoegen aan het einde"

#: pro/fields/class-acf-field-gallery.php:685
msgid "Prepend to the beginning"
msgstr "Toevoegen aan het begin"

#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Provides a solution for repeating content such as slides, team members, and "
"call-to-action tiles, by acting as a parent to a set of subfields which can "
"be repeated again and again."
msgstr ""
"Dit biedt een oplossing voor herhalende inhoud zoals slides, teamleden en "
"call-to-action tegels, door te dienen als een hoofditem voor een set "
"subvelden die steeds opnieuw kunnen worden herhaald."

#: pro/fields/class-acf-field-repeater.php:67,
#: pro/fields/class-acf-field-repeater.php:462
msgid "Minimum rows not reached ({min} rows)"
msgstr "Minimum aantal rijen bereikt ({min} rijen)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Maximum rows reached ({max} rows)"
msgstr "Maximum aantal rijen bereikt ({max} rijen)"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Error loading page"
msgstr "Fout bij het laden van de pagina"

#: pro/fields/class-acf-field-repeater.php:70
msgid "Order will be assigned upon save"
msgstr "Volgorde zal worden toegewezen bij opslaan"

#: pro/fields/class-acf-field-repeater.php:197
msgid "Useful for fields with a large number of rows."
msgstr "Nuttig voor velden met een groot aantal rijen."

#: pro/fields/class-acf-field-repeater.php:208
msgid "Rows Per Page"
msgstr "Rijen per pagina"

#: pro/fields/class-acf-field-repeater.php:209
msgid "Set the number of rows to be displayed on a page."
msgstr "Stel het aantal rijen in om weer te geven op een pagina."

#: pro/fields/class-acf-field-repeater.php:241
msgid "Minimum Rows"
msgstr "Minimum aantal rijen"

#: pro/fields/class-acf-field-repeater.php:252
msgid "Maximum Rows"
msgstr "Maximum aantal rijen"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Collapsed"
msgstr "Ingeklapt"

#: pro/fields/class-acf-field-repeater.php:283
msgid "Select a sub field to show when row is collapsed"
msgstr "Selecteer een subveld om weer te geven wanneer rij dichtgeklapt is"

#: pro/fields/class-acf-field-repeater.php:1055
msgid "Invalid field key or name."
msgstr "Ongeldige veldsleutel of -naam."

#: pro/fields/class-acf-field-repeater.php:1064
msgid "There was an error retrieving the field."
msgstr "Er is een fout opgetreden bij het ophalen van het veld."

#: pro/fields/class-acf-repeater-table.php:367
msgid "Click to reorder"
msgstr "Klik om te herschikken"

#: pro/fields/class-acf-repeater-table.php:400
msgid "Add row"
msgstr "Nieuwe rij"

#: pro/fields/class-acf-repeater-table.php:401
msgid "Duplicate row"
msgstr "Rij dupliceren"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Remove row"
msgstr "Regel verwijderen"

#: pro/fields/class-acf-repeater-table.php:446,
#: pro/fields/class-acf-repeater-table.php:463,
#: pro/fields/class-acf-repeater-table.php:464
msgid "Current Page"
msgstr "Huidige pagina"

#: pro/fields/class-acf-repeater-table.php:454,
#: pro/fields/class-acf-repeater-table.php:455
msgid "First Page"
msgstr "Eerste pagina"

#: pro/fields/class-acf-repeater-table.php:458,
#: pro/fields/class-acf-repeater-table.php:459
msgid "Previous Page"
msgstr "Vorige pagina"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:468
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s van %2$s"

#: pro/fields/class-acf-repeater-table.php:475,
#: pro/fields/class-acf-repeater-table.php:476
msgid "Next Page"
msgstr "Volgende pagina"

#: pro/fields/class-acf-repeater-table.php:479,
#: pro/fields/class-acf-repeater-table.php:480
msgid "Last Page"
msgstr "Laatste pagina"

#: pro/locations/class-acf-location-block.php:73
msgid "No block types exist"
msgstr "Er bestaan geen bloktypes"

#: pro/locations/class-acf-location-options-page.php:70
msgid "Select options page..."
msgstr "Opties pagina selecteren…"

#: pro/locations/class-acf-location-options-page.php:74,
#: pro/post-types/acf-ui-options-page.php:95,
#: pro/admin/post-types/admin-ui-options-page.php:482
msgid "Add New Options Page"
msgstr "Nieuwe opties pagina toevoegen"

#: pro/post-types/acf-ui-options-page.php:96
msgid "Edit Options Page"
msgstr "Opties pagina bewerken"

#: pro/post-types/acf-ui-options-page.php:97
msgid "New Options Page"
msgstr "Nieuwe opties pagina"

#: pro/post-types/acf-ui-options-page.php:98
msgid "View Options Page"
msgstr "Opties pagina bekijken"

#: pro/post-types/acf-ui-options-page.php:99
msgid "Search Options Pages"
msgstr "Opties pagina’s zoeken"

#: pro/post-types/acf-ui-options-page.php:100
msgid "No Options Pages found"
msgstr "Geen opties pagina’s gevonden"

#: pro/post-types/acf-ui-options-page.php:101
msgid "No Options Pages found in Trash"
msgstr "Geen opties pagina’s gevonden in prullenbak"

#: pro/post-types/acf-ui-options-page.php:203
msgid ""
"The menu slug must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"De menuslug mag alleen kleine alfanumerieke tekens, underscores of streepjes "
"bevatten."

#: pro/post-types/acf-ui-options-page.php:235
msgid "This Menu Slug is already in use by another ACF Options Page."
msgstr "Deze menuslug wordt al gebruikt door een andere ACF opties pagina."

#: pro/admin/post-types/admin-ui-options-page.php:56
msgid "Options page deleted."
msgstr "Opties pagina verwijderd."

#: pro/admin/post-types/admin-ui-options-page.php:57
msgid "Options page updated."
msgstr "Opties pagina geüpdatet."

#: pro/admin/post-types/admin-ui-options-page.php:60
msgid "Options page saved."
msgstr "Opties pagina opgeslagen."

#: pro/admin/post-types/admin-ui-options-page.php:61
msgid "Options page submitted."
msgstr "Opties pagina ingediend."

#: pro/admin/post-types/admin-ui-options-page.php:62
msgid "Options page scheduled for."
msgstr "Opties pagina gepland voor."

#: pro/admin/post-types/admin-ui-options-page.php:63
msgid "Options page draft updated."
msgstr "Opties pagina concept geüpdatet."

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:83
msgid "%s options page updated"
msgstr "%s opties pagina geüpdatet"

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:89
msgid "%s options page created"
msgstr "%s opties pagina aangemaakt"

#: pro/admin/post-types/admin-ui-options-page.php:102
msgid "Link existing field groups"
msgstr "Koppel bestaande veldgroepen"

#: pro/admin/post-types/admin-ui-options-page.php:361
msgid "No Parent"
msgstr "Geen hoofditem"

#: pro/admin/post-types/admin-ui-options-page.php:450
msgid "The provided Menu Slug already exists."
msgstr "De opgegeven menuslug bestaat al."

#. translators: %s number of post types activated
#: pro/admin/post-types/admin-ui-options-pages.php:179
msgid "Options page activated."
msgid_plural "%s options pages activated."
msgstr[0] "Opties pagina geactiveerd."
msgstr[1] "%s opties pagina’s geactiveerd."

#. translators: %s number of post types deactivated
#: pro/admin/post-types/admin-ui-options-pages.php:186
msgid "Options page deactivated."
msgid_plural "%s options pages deactivated."
msgstr[0] "Opties pagina gedeactiveerd."
msgstr[1] "%s opties pagina’s gedeactiveerd."

#. translators: %s number of post types duplicated
#: pro/admin/post-types/admin-ui-options-pages.php:193
msgid "Options page duplicated."
msgid_plural "%s options pages duplicated."
msgstr[0] "Opties pagina gedupliceerd."
msgstr[1] "%s opties pagina’s gedupliceerd."

#. translators: %s number of post types synchronized
#: pro/admin/post-types/admin-ui-options-pages.php:200
msgid "Options page synchronized."
msgid_plural "%s options pages synchronized."
msgstr[0] "Opties pagina gesynchroniseerd."
msgstr[1] "%s opties pagina's gesynchroniseerd."

#: pro/admin/views/html-settings-updates.php:9
msgid "Deactivate License"
msgstr "Licentiecode deactiveren"

#: pro/admin/views/html-settings-updates.php:9
msgid "Activate License"
msgstr "Licentie activeren"

#: pro/admin/views/html-settings-updates.php:26
msgctxt "license status"
msgid "Inactive"
msgstr "Inactief"

#: pro/admin/views/html-settings-updates.php:34
msgctxt "license status"
msgid "Cancelled"
msgstr "Geannuleerd"

#: pro/admin/views/html-settings-updates.php:32
msgctxt "license status"
msgid "Expired"
msgstr "Verlopen"

#: pro/admin/views/html-settings-updates.php:30
msgctxt "license status"
msgid "Active"
msgstr "Actief"

#: pro/admin/views/html-settings-updates.php:47
msgid "Subscription Status"
msgstr "Abonnementsstatus"

#: pro/admin/views/html-settings-updates.php:60
msgid "Subscription Type"
msgstr "Abonnementstype"

#: pro/admin/views/html-settings-updates.php:67
msgid "Lifetime - "
msgstr "Levenslang - "

#: pro/admin/views/html-settings-updates.php:81
msgid "Subscription Expires"
msgstr "Abonnement verloopt"

#: pro/admin/views/html-settings-updates.php:79
msgid "Subscription Expired"
msgstr "Abonnement verlopen"

#: pro/admin/views/html-settings-updates.php:118
msgid "Renew Subscription"
msgstr "Abonnement verlengen"

#: pro/admin/views/html-settings-updates.php:136
msgid "License Information"
msgstr "Licentie informatie"

#: pro/admin/views/html-settings-updates.php:170
msgid "License Key"
msgstr "Licentiecode"

#: pro/admin/views/html-settings-updates.php:191,
#: pro/admin/views/html-settings-updates.php:157
msgid "Recheck License"
msgstr "Licentie opnieuw controleren"

#: pro/admin/views/html-settings-updates.php:142
msgid "Your license key is defined in wp-config.php."
msgstr "Je licentiesleutel wordt gedefinieerd in wp-config.php."

#: pro/admin/views/html-settings-updates.php:211
msgid "View pricing & purchase"
msgstr "Prijzen bekijken & kopen"

#. translators: %s - link to ACF website
#: pro/admin/views/html-settings-updates.php:220
msgid "Don't have an ACF PRO license? %s"
msgstr "Heb je geen ACF PRO licentie? %s"

#: pro/admin/views/html-settings-updates.php:235
msgid "Update Information"
msgstr "Informatie updaten"

#: pro/admin/views/html-settings-updates.php:242
msgid "Current Version"
msgstr "Huidige versie"

#: pro/admin/views/html-settings-updates.php:250
msgid "Latest Version"
msgstr "Nieuwste versie"

#: pro/admin/views/html-settings-updates.php:258
msgid "Update Available"
msgstr "Update beschikbaar"

#: pro/admin/views/html-settings-updates.php:272
msgid "Upgrade Notice"
msgstr "Upgrade opmerking"

#: pro/admin/views/html-settings-updates.php:303
msgid "Check For Updates"
msgstr "Controleren op updates"

#: pro/admin/views/html-settings-updates.php:300
msgid "Enter your license key to unlock updates"
msgstr "Vul je licentiecode hierboven in om updates te ontgrendelen"

#: pro/admin/views/html-settings-updates.php:298
msgid "Update Plugin"
msgstr "Plugin updaten"

#: pro/admin/views/html-settings-updates.php:296
msgid "Updates must be manually installed in this configuration"
msgstr "Updates moeten handmatig worden geïnstalleerd in deze configuratie"

#: pro/admin/views/html-settings-updates.php:294
msgid "Update ACF in Network Admin"
msgstr "ACF updaten in netwerkbeheer"

#: pro/admin/views/html-settings-updates.php:292
msgid "Please reactivate your license to unlock updates"
msgstr "Activeer je licentie opnieuw om updates te ontgrendelen"

#: pro/admin/views/html-settings-updates.php:290
msgid "Please upgrade WordPress to update ACF"
msgstr "Upgrade WordPress om ACF te updaten"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:20
msgid "Dashicon class name"
msgstr "Dashicon class naam"

#. translators: %s = "dashicon class name", link to the WordPress dashicon documentation.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:25
msgid ""
"The icon used for the options page menu item in the admin dashboard. Can be "
"a URL or %s to use for the icon."
msgstr ""
"Het icoon dat wordt gebruikt voor het menu-item op de opties pagina in het "
"beheerdashboard. Kan een URL of %s zijn om te gebruiken voor het icoon."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:80
msgid "Menu Title"
msgstr "Menutitel"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:94
msgid "Learn more about menu positions."
msgstr "Meer informatie over menuposities."

#. translators: %s - link to WordPress docs to learn more about menu positions.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:98,
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:104
msgid "The position in the menu where this page should appear. %s"
msgstr "De positie in het menu waar deze pagina moet verschijnen. %s"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:108
msgid ""
"The position in the menu where this child page should appear. The first "
"child page is 0, the next is 1, etc."
msgstr ""
"De positie in het menu waar deze subpagina moet verschijnen. De eerste "
"subpagina is 0, de volgende is 1, etc."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:129
msgid "Redirect to Child Page"
msgstr "Doorverwijzen naar subpagina"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:130
msgid ""
"When child pages exist for this parent page, this page will redirect to the "
"first child page."
msgstr ""
"Als er subpagina's bestaan voor deze hoofdpagina, zal deze pagina doorsturen "
"naar de eerste subpagina."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:154
msgid "A descriptive summary of the options page."
msgstr "Een beschrijvende samenvatting van de opties pagina."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:163
msgid "Update Button Label"
msgstr "Update knop label"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:164
msgid ""
"The label used for the submit button which updates the fields on the options "
"page."
msgstr ""
"Het label dat wordt gebruikt voor de verzendknop waarmee de velden op de "
"opties pagina worden geüpdatet."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:178
msgid "Updated Message"
msgstr "Bericht geüpdatet"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:179
msgid ""
"The message that is displayed after successfully updating the options page."
msgstr ""
"Het bericht dat wordt weergegeven na het succesvol updaten van de opties "
"pagina."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:180
msgid "Updated Options"
msgstr "Geüpdatete opties"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:217
msgid "Capability"
msgstr "Rechten"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:218
msgid "The capability required for this menu to be displayed to the user."
msgstr "De rechten die nodig zijn om dit menu weer te geven aan de gebruiker."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:234
msgid "Data Storage"
msgstr "Gegevensopslag"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:235
msgid ""
"By default, the option page stores field data in the options table. You can "
"make the page load field data from a post, user, or term."
msgstr ""
"Standaard slaat de opties pagina veldgegevens op in de optietabel. Je kunt "
"de pagina veldgegevens laten laden van een bericht, gebruiker of term."

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:238,
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:269
msgid "Custom Storage"
msgstr "Aangepaste opslag"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:258
msgid "Learn more about available settings."
msgstr "Meer informatie over beschikbare instellingen."

#. translators: %s = link to learn more about storage locations.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:263
msgid ""
"Set a custom storage location. Can be a numeric post ID (123), or a string "
"(`user_2`). %s"
msgstr ""
"Stel een aangepaste opslaglocatie in. Kan een numerieke bericht ID zijn "
"(123) of een tekenreeks (`user_2`). %s"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:288
msgid "Autoload Options"
msgstr "Autoload opties"

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:289
msgid ""
"Improve performance by loading the fields in the option records "
"automatically when WordPress loads."
msgstr ""
"Verbeter de prestaties door de velden in de optie-records automatisch te "
"laden wanneer WordPress wordt geladen."

#: pro/admin/views/acf-ui-options-page/basic-settings.php:20,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:16
msgid "Page Title"
msgstr "Paginatitel"

#. translators: example options page name
#: pro/admin/views/acf-ui-options-page/basic-settings.php:22,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:18
msgid "Site Settings"
msgstr "Site instellingen"

#: pro/admin/views/acf-ui-options-page/basic-settings.php:37,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:33
msgid "Menu Slug"
msgstr "Menuslug"

#: pro/admin/views/acf-ui-options-page/basic-settings.php:52,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:47
msgid "Parent Page"
msgstr "Hoofdpagina"

#: pro/admin/views/acf-ui-options-page/list-empty.php:30
msgid "Add Your First Options Page"
msgstr "Voeg je eerste opties pagina toe"
