# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-19T16:45:13+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:144
msgid "Learn more"
msgstr ""

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr ""

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr ""

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr ""

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr ""

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr ""

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr ""

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr ""

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr ""

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr ""

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr ""

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr ""

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr ""

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr ""

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr ""

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr ""

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr ""

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr ""

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr ""

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr ""

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr ""

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr ""

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr ""

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr ""

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr ""

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr ""

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr ""

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr ""

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr ""

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Camps de l'ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Característiques de l'ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Renoveu a PRO per desbloquejar"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Renova la llicència PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "Els camps PRO no es poden editar sense una llicència activa."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Activeu la llicència ACF PRO per a editar els grups de camps assignats a un "
"bloc ACF."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "Activeu la llicència ACF PRO per editar aquesta pàgina d'opcions."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Retornar els valors HTML escapats només és possible quan format_value també "
"és true. Els valors del camp no s'han retornat per seguretat."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Retornar un valor HTML escapat només és possible quan format_value també és "
"true. El valor del camp no s'ha retornat per seguretat."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"L'ACF %1$s ara escapa automàticament de l'HTML insegur quan es renderitza "
"amb <code>the_field</code> o el codi de substitució de l'ACF. Hem detectat "
"que la sortida d'alguns dels vostres camps ha estat modificada per aquest "
"canvi, però pot ser que no s'hagi trencat res. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Contacteu amb l'administrador o el desenvolupador del vostre lloc per a més "
"detalls."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Amaga&nbsp;detalls"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Mostra&nbsp;detalls"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - renderitzat via %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Renova la llicència ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Renova la llicència"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Gestiona la llicència"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "No s'admet la posició \"Alta\" a l'editor de blocs."

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Actualitza a ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"Les <a href=\"%s\" target=\"_blank\">pàgines d'opcions</a> d'ACF són pàgines "
"d'administració personalitzades per a gestionar la configuració global "
"mitjançant camps. Podeu crear diverses pàgines i subpàgines."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Afegeix una pàgina d'opcions"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "A l'editor s'utilitza com a marcador de posició del títol."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Marcador de posició del títol"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 mesos gratuïts"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Duplicat de %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Selecciona les pàgines d'opcions"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Duplica la taxonomia"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Crea una taxonomia"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplica el tipus d'entrada"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Crea un tipus de contingut"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Enllaça grups de camps"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Afegeix camps"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Aquest camp"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Opinions"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Suport"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "és desenvolupat i mantingut per"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Afegeix %s a les regles d'ubicació dels grups de camps seleccionats."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Activar el paràmetre bidireccional permet actualitzar un valor als camps de "
"destinació per cada valor seleccionat per aquest camp, afegint o suprimint "
"l'ID d'entrada, l'ID de taxonomia o l'ID d'usuari de l'element que s'està "
"actualitzant. Per a més informació, llegeix la <a href=\"%s\" "
"target=\"_blank\">documentació</a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Selecciona els camps per emmagatzemar la referència a l'element que s'està "
"actualitzant. Podeu seleccionar aquest camp. Els camps de destinació han de "
"ser compatibles amb el lloc on s'està mostrant aquest camp. Per exemple, si "
"aquest camp es mostra a una taxonomia, el camp de destinació ha de ser del "
"tipus taxonomia"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Camp de destinació"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Actualitza un camp amb els valors seleccionats, fent referència a aquest "
"identificador"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidireccional"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "Camp %s"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Selecciona múltiples"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "Logotip de WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "Només minúscules, subratllats i guions, màxim 32 caràcters."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "El nom de la capacitat per assignar termes d'aquesta taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Capacitat d'assignar termes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "El nom de la capacitat per suprimir termes d'aquesta taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Capacitat de suprimir termes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "El nom de la capacitat per editar termes d'aquesta taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Capacitat d'editar termes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "El nom de la capacitat per gestionar termes d'aquesta taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Gestiona la capacitat dels termes"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Estableix si les entrades s'han d'excloure dels resultats de la cerca i de "
"les pàgines d'arxiu de taxonomia."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Més eines de WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "S'ha fet per als que construeixen amb el WordPress, per l'equip a %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Mostra els preus i actualitzacions"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "Més informació"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Accelera el flux de treball i desenvolupa millors llocs web amb funcions com "
"ara blocs ACF i pàgines d'opcions, i tipus de camps sofisticats com "
"repetidor, contingut flexible, clonar i galeria."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""
"Desbloqueja característiques avançades i construeix encara més amb ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s camps"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "No hi ha termes"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "No existeixen tipus d'entrada"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Sense entrades"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "No hi ha taxonomies"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "No hi ha grups de camp"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "No hi ha camps"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "No hi ha descripció"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Qualsevol estat d'entrada"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Aquesta clau de taxonomia ja l'està utilitzant una altra taxonomia "
"registrada fora d'ACF i no es pot utilitzar."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Aquesta clau de taxonomia ja l'està utilitzant una altra taxonomia a ACF i "
"no es pot fer servir."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clau de taxonomia només ha de contenir caràcters alfanumèrics en "
"minúscules, guions baixos o guions."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "La clau de taxonomia hauria de tenir menys de 20 caràcters"

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "No s'ha trobat cap taxonomia a la paperera."

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "No s'ha trobat cap taxonomia"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Cerca taxonomies"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Visualitza la taxonomia"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nova taxonomia"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Edita la taxonomia"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Afegeix una nova taxonomia"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "No s'ha trobat cap tipus de contingut a la paperera."

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "No s'ha trobat cap tipus de contingut"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Cerca tipus de contingut"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Visualitza el tipus de contingut"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Nou tipus de contingut"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Edita el tipus de contingut"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Afegeix un nou tipus de contingut"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Aquesta clau de tipus de contingut ja s'està utilitzant per un altre tipus "
"de contingut registrat fora d'ACF i no es pot utilitzar."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Aquesta clau de tipus de contingut ja s'està utilitzant en un atre tipus de "
"contingut d'ACF i no es pot utilitzar."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Aquest camp no ha de ser un <a href=\"%s\" target=\"_blank\">terme reservat</"
"a> de WordPress."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clau de tipus d'entrada només ha de contenir caràcters alfanumèrics en "
"minúscules, guions baixos o guions."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "La clau de tipus de contingut hauria de tenir menys de 20 caràcters."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "No recomanem fer servir aquest camp en el block d'ACF"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Mostra l'editor WYSIWYG de WordPress tal com es veu a publicacions i pàgines "
"que permet una experiència d'edició de text rica i que també permet "
"contingut multimèdia."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Editor WYSIWYG"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Permet la selecció d'un o més usuaris que es poden utilitzar per crear "
"relacions entre objectes de dades."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "Un camp de text dissenyat especificament per emmagatzemar adreces web."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Un commutador que us permet triar un valor d'1 o 0 (activat o desactivat, "
"cert o fals, etc.). Es pot presentar com un interruptor estilitzat o una "
"casella de selecció."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Una interfície d'usuari interactiva per triar una hora. El format de l'hora "
"es pot personalitzar mitjançant la configuració de camp."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Un camp d'àrea de text bàsic per emmagatzemar paràgrafs de text."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "Un camp bàsic de text, útil per emmagatzemar valors de cadena simple."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Permet la selecció d'un o més termes de taxonomia en funció dels criteris i "
"opcions especificats a la configuració dels camps."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Us permet agrupar camps en seccions amb pestanyes a la pantalla d'edició. "
"Útil per mantenir els camps organitzats i estructurats."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Una llista desplegable amb una selecció d'opcions que especifiqueu."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Una interfície de dues columnes per seleccionar una o més entrades, pàgines "
"o elements de tipus de contingut personalitzats per crear una relació amb "
"l'element que esteu editant actualment. Inclou opcions per cercar i filtrar."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Un camp per seleccionar un valor numèric dins d'un interval especificat "
"mitjançant un element de control d'interval."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Un grup de camps de botons d'opció que permet a l'usuari fer una selecció "
"única dels valors que especifiqueu."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Una interfície d'usuari interactiva i personalitzable per escollir una o més "
"publicacions, pàgines o elements de tipus d'entrada amb l'opció de cercar. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""
"Un camp per proporcionar una contrasenya mitjançant un camp emmascarat."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtra per estat d'entrada"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Un menú desplegable interactiu per seleccionar una o més entrades, pàgines, "
"elements de tipus de contingut personalitzats o URL d'arxiu, amb l'opció de "
"cercar."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Un component interactiu per incrustar vídeos, imatges, tuits, àudio i altres "
"continguts fent ús de la funcionalitat nativa de WordPress oEmbed."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Un camp limitat per valors numèrics."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"S'utilitza per mostrar un missatge als editors juntament amb altres camps. "
"Útil per proporcionar context o instruccions addicionals al voltant dels "
"vostres camps."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Us permet especificar un enllaç i les seves propietats, com ara el títol i "
"l'objectiu mitjançant el selector d'enllaços natiu de WordPress."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Utilitza el selector de mitjans natiu de WordPress per pujar o triar imatges."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Proporciona una manera d'estructurar els camps en grups per organitzar "
"millor les dades i la pantalla d'edició."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Una interfície d'usuari interactiva per seleccionar una ubicació mitjançant "
"Google Maps. Requereix una clau de l'API de Google Maps i una configuració "
"addicional per mostrar-se correctament."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Utilitza el selector multimèdia natiu de WordPress per pujar o triar fitxers."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Una camp de text dissenyat específicament per emmagatzemar adreces de correu "
"electrònic."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Una interfície d'usuari interactiva per triar una data i una hora. El format "
"de devolució de la data es pot personalitzar mitjançant la configuració de "
"camp."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Una interfície d'usuari interactiva per triar una data. El format de "
"devolució de la data es pot personalitzar mitjançant la configuració de camp."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Una interfície d'usuari interactiva per seleccionar un color o especificar "
"un valor hexadecimal."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Un grup de camps de caselles de selecció que permeten a l'usuari seleccionar "
"un o diversos valors que especifiqueu."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Un grup de botons amb valors que especifiqueu, els usuaris poden triar una "
"opció entre els valors proporcionats."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Us permet agrupar i organitzar camps personalitzats en panells plegables que "
"es mostren mentre editeu el contingut. Útil per mantenir ordenats grans "
"conjunts de dades."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Això proporciona una solució per repetir contingut com ara diapositives, "
"membres de l'equip i fitxes de crida d'acció, actuant com a pare d'un "
"conjunt de subcamps que es poden repetir una i altra vegada."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Això proporciona una interfície interactiva per gestionar una col·lecció de "
"fitxers adjunts. La majoria de la configuració és similar al tipus de camp "
"Imatge. La configuració addicional us permet especificar on s'afegeixen nous "
"fitxers adjunts a la galeria i el nombre mínim/màxim de fitxers adjunts "
"permesos."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Proporciona un editor senzill, estructurat i basat en dissenys. El camp "
"contingut flexible us permet definir, crear i gestionar contingut amb "
"control total mitjançant l'ús de dissenys i subcamps per dissenyar els blocs "
"disponibles."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Us permet seleccionar i mostrar els camps existents. No duplica cap camp de "
"la base de dades, sinó que carrega i mostra els camps seleccionats en temps "
"d'execució. El camp Clonar pot substituir-se amb els camps seleccionats o "
"mostrar els camps seleccionats com un grup de subcamps."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Clona"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Avançat"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (més nou)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Identificador de l'entrada no vàlid."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "S'ha seleccionat un tipus de contingut no vàlid per a la revisió."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Més"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Camp selector"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Proveu un terme de cerca diferent o navegueu per %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Camps populars"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "No hi ha resultats de cerca per a '%s'"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Cerca camps"

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Selecciona un tipus de camp"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Popular"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Afegeix taxonomia"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Crea taxonomies personalitzades per clasificar el contingut del tipus de "
"contingut"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Afegeix la primera taxonomia"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Les taxonomies jeràrquiques poden tenir descendents (com les categories)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Fa que una taxonomia sigui visible a la part pública de la web i al tauler "
"d'administració."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Un o varis tipus d'entrada que poden ser classificats amb aquesta taxonomia."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "gènere"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Gènere"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Gèneres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Controlador personalitzat opcional per utilitzar-lo en lloc del "
"`WP_REST_Terms_Controller `."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Exposa aquest tipus d'entrada a la REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Personalitza el nom de la variable de consulta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Es pot accedir als termes utilitzant l'enllaç permanent no bonic, per "
"exemple, {query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Termes pare-fill en URLs per a taxonomies jeràrquiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Personalitza el segment d'URL utilitzat a la URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Els enllaços permanents d'aquesta taxonomia estan desactivats."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Reescriu l'URL utilitzant la clau de la taxonomia com a segment d'URL. "
"L'estructura de l'enllaç permanent serà"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Clau de la taxonomia"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""
"Seleccioneu el tipus d'enllaç permanent que voleu utilitzar per aquesta "
"taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Mostra una columna per a la taxonomia a les pantalles de llista de tipus de "
"contingut."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Mostra la columna d'administració"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Mostra la taxonomia en el panell d'edició ràpida/massiva."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Edició ràpida"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Mostra la taxonomia als controls del giny núvol d'etiquetes."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Núvol d'etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"El nom de la funció PHP que s'executarà per sanejar les dades de taxonomia "
"desades a una meta box."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Crida de retorn de sanejament de la caixa meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Registra la crida de retorn de la caixa meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Sense caixa meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Caixa meta personalitzada"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Controla la meta caixa a la pantalla de l'editor de contingut. Per defecte, "
"la meta caixa Categories es mostra per a taxonomies jeràrquiques i la meta "
"caixa Etiquetes es mostra per a taxonomies no jeràrquiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Caixa meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Caixa meta de categories"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Caixa meta d'etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Un enllaç a una etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Descriu una variació del bloc d'enllaços de navegació utilitzada a l'editor "
"de blocs."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Un enllaç a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Enllaç de l'etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Assigna un títol a la variació del bloc d'enllaços de navegació utilitzada a "
"l'editor de blocs."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Ves a etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Assigna el text utilitzat per enllaçar de nou a l'índex principal després "
"d'actualitzar un terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Torna a Elements"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Anar a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Llista d'etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Assigna text a l'encapçalament ocult de la taula."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navegació per la llista d'Etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Assigna text a la capçalera oculta de la paginació de la taula."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtra per Categoria"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Assigna text al botó de filtre a la taula de llistes d'entrades."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtra per element"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtra per %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"La descripció no es mostra per defecte; no obstant, hi ha alguns temes que "
"poden mostrar-la."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Descriu el camp descripció a la pantalla d'edició d'etiquetes."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Descripció del camp descripció"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Assigna un terme pare per crear una jerarquia. El terme Jazz, per exemple, "
"seria el pare de Bebop i Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Descriu el camp superior de la pantalla Editar etiquetes."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Descripció del camp pare"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"El segment d’URL és la versió URL amigable del nom. Normalment està tot en "
"minúscules i només conté lletres, números i guions."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Descriu el camp Segment d'URL de la pantalla Editar etiquetes."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Descripció del camp de l'àlies"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "El nom és tal com apareix al lloc web"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Descriu el camp Nom de la pantalla Editar etiquetes."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Descripció del camp nom"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Sense etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Assigna el text que es mostra a les taules d'entrades i llista de mèdia quan "
"no hi ha etiquetes o categories disponibles."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "No hi ha termes"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "No hi ha %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "No s'han trobat etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Assigna el text que es mostra quan es fa clic a \"tria entre els més "
"utilitzats\" a la caixa meta de la taxonomia quan no hi ha etiquetes "
"disponibles, i assigna el text utilitzat a la taula de llista de termes quan "
"no hi ha elements per a una taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "No s'ha trobat"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Assigna text al camp Títol de la pestanya Més Utilitzat."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Més utilitzats"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Tria entre les etiquetes més utilitzades"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Assigna el text \"tria entre els més utilitzats\" que s'utilitza a la caixa "
"meta quan JavaScript està desactivat. Només s'utilitza en taxonomies no "
"jeràrquiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Tria entre els més utilitzats"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Tria entre els %s més utilitzats"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Afegeix o suprimeix etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Assigna el text d'afegir o suprimir elements utilitzat a la caixa meta quan "
"JavaScript està desactivat. Només s'utilitza en taxonomies no jeràrquiques"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Afegeix o Suprimeix Elements"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Afegeix o suprimeix %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Separa les etiquetes amb comes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Assigna a l'element separat amb comes el text utilitzat a la caixa meta de "
"taxonomia. Només s'utilitza en taxonomies no jeràrquiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Separa elements amb comes"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Separa %s amb comes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Etiquetes populars"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Assigna text als elements populars. Només s'utilitza en taxonomies no "
"jeràrquiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Elements populars"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "%s populars"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Cerca etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Assigna el text de cercar elements."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Categoria mare:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Assigna el text de l'element pare, però afegint dos punts (:) al final."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Element pare amb dos punts"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Categoria mare"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Assigna el text de l'element pare. Només s'utilitza en taxonomies "
"jeràrquiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Element pare"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "%s pare"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nom de l'etiqueta nova"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Assigna el text del nom de l'element nou."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nom de l'element nou"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nom de la taxonomia %s nova"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Afegeix una etiqueta nova"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Assigna el text d'afegir un element nou."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Actualitza l'etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Assigna el text d'actualitzar un element."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Actualiza l'element"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Actualitza %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Mostra l'etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "A barra d'administració per a veure el terme durant l'edició."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Edita l'etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "A la part superior de la pantalla de l'editor, quan s'edita un terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Totes les etiquetes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Assigna el text de tots els elements."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Assigna el text del nom del menú."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Etiqueta del menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Les taxonomies actives estan activades i registrades a WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Un resum descriptiu de la taxonomia."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Un resum descriptiu del terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Descripció del terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Una sola paraula, sense espais. S’admeten barres baixes i guions."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Àlies del terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "El nom del terme per defecte."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Nom del terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Crea un terme per a la taxonomia que no es pugui suprimir. No serà "
"seleccionada per defecte per a les entrades."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Terme per defecte"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Si els termes d'aquesta taxonomia s'han d'ordenar en l'ordre en què es "
"proporciona a `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Ordena termes"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Afegeix un tipus d'entrada personalitzat"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Amplia la funcionalitat de WordPress més enllà de les entrades i pàgines "
"estàndard amb tipus de contingut personalitzats."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Afegiu el vostre primer tipus de contingut"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Sé el que estic fent, mostra'm totes les opcions."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Configuració avançada"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""
"Els tipus de contingut jeràrquics poden tenir descendents (com les pàgines)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Jeràrquica"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Visible a la part pública de la web i al tauler d'administració."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Públic"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "pel·lícula"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "Només minúscules, guions baixos i guions, màxim 20 caràcters."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Pel·lícula"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Etiqueta singular"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Pel·lícules"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Etiqueta plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Controlador personalitzat opcional per utilitzar-lo en lloc del "
"`WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Classe de controlador"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "La part de l'espai de noms de la URL de la API REST."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Ruta de l'espai de noms"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "URL base per als URL de la REST API del tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "URL base"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Exposa aquest tipus de contingut a la REST API. Necessari per a utilitzar "
"l'editor de blocs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Mostra a l'API REST"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Personalitza el nom de la variable de consulta."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Variable de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "No admet variables de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Variable de consulta personalitzada"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Es pot accedir als elements utilitzant l'enllaç permanent no bonic, per "
"exemple {post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Admet variables de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"Es pot accedir als URL d'un element i dels elements mitjançant una cadena de "
"consulta."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Consultable públicament"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Segment d'URL personalitzat per a la URL de l'arxiu."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Segment d'URL de l'arxiu"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Té un arxiu d'elements que es pot personalitzar amb un fitxer de plantilla "
"d'arxiu al tema."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Arxiu"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Suport de paginació per als URL dels elements, com els arxius."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Paginació"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "URL del canal RSS per als elements del tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL del canal"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Altera l'estructura d'enllaços permanents per afegir el prefix `WP_Rewrite::"
"$front` a les URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Prefix de les URLs"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Personalitza el segment d'URL utilitzat a la URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Àlies d'URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Els enllaços permanents d'aquest tipus de contingut estan desactivats."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Reescriu l'URL utilitzant un segment d'URL personalitzat definit al camp de "
"sota. L'estructura d'enllaç permanent serà"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Sense enllaç permanent (evita la reescriptura de URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Enllaç permanent personalitzat"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Clau del tipus de contingut"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Reescriu l'URL utilitzant la clau del tipus de contingut com a segment "
"d'URL. L'estructura d'enllaç permanent serà"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Reescriptura d'enllaç permanent"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Suprimeix els elements d'un usuari quan se suprimeixi."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Suprimeix amb usuari"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""
"Permet que el tipus de contingut es pugui exportar des de 'Eines' > "
"'Exportar'."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Es pot exportar"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""
"Opcionalment, proporciona un plural per a utilitzar-lo a les capacitats."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Nom de la capacitat en plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Tria un altre tipus de contingut per basar les capacitats d'aquest tipus de "
"contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Nom de la capacitat en singular"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Per defecte, les capacitats del tipus de contingut heretaran els noms de les "
"capacitats 'Entrada', per exemple edit_post, delete_posts. Activa'l per "
"utilitzar capacitats específiques del tipus de contingut, per exemple "
"edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Canvia el nom de les capacitats"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Exclou de la cerca"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Permet afegir elements als menús a la pantalla 'Aparença' > 'Menús'. S'ha "
"d'activar a 'Opcions de pantalla'."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Compatibilitat amb menús d'aparença"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Apareix com a un element al menú 'Nou' de la barra d'administració."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Mostra a la barra d'administració"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Crida de retorn de caixa meta personalitzada"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Icona de menú"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "La posició al menú de la barra lateral al tauler d'administració."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Posició en el menú"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Per defecte, el tipus de contingut obtindrà un element nou de nivell "
"superior en el menú d'administració. Si aquí es proporciona un element de "
"nivell superior existent, el tipus de contingut s'afegirà com a un element "
"de submenú a sota."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Menú d'administració pare"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Navegació de l'editor d'administració al menú de la barra lateral."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Mostra al menú d'administració"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Els elements es poden editar i gestionar al tauler d'administració."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Mostra a l'UI"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Un enllaç a una entrada."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Descripció d'una variació del bloc d'enllaços de navegació."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Descripció de l'enllaç de l'element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Un enllaç a %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Enllaç de l'entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Títol d'una variació del bloc d'enllaços de navegació."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Enllaç de l'element"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Enllaç de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "S'ha actualitzat l'entrada."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "A l'avís de l'editor després d'actualitzar un element."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "S'ha actualitzat l'element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "S'ha actualitzat %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "S'ha programat l'entrada."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "A l'avís de l'editor després de programar un element."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "S'ha programat l'element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s programat."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "S'ha revertit l'entrada a esborrany."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "A l'avís de l'editor després de revertir un element a esborrany."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "S'ha tornat l'element a esborrany"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "S'ha revertit %s a esborrany."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "S'ha publicat l'entrada en privat."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "A l'avís de l'editor després de publicar un element privat."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "S'ha publicat l'element en privat"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s publicats en privat."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "S'ha publicat l'entrada."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "A l'avís de l'editor després de publicar un element."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "S'ha publicat l'element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "S'ha publicat %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Llista d'entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Utilitzat pels lectors de pantalla per a la llista d'elements a la pantalla "
"de llista de tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Llista d'elements"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Llista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navegació per la llista d'entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Utilitzat pels lectors de pantalla per a la paginació de la llista de "
"filtres a la pantalla de llista de tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Navegació per la llista d'elements"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Navegació per la lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtra les entrades per data"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Utilitzat pels lectors de pantalla per a la capçalera de filtrar per data a "
"la pantalla de llista de tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtra els elements per data"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtra %s per data"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtra la llista d'entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Utilitzat pels lectors de pantalla per a la capçalera dels enllaços de "
"filtre a la pantalla de llista de tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtra la llista d'elements"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtra la llista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"A la finestra emergent de mèdia es mostra tota la mèdia pujada a aquest "
"element."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "S'ha penjat a aquest element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "S'ha penjat a aquest %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Insereix a l'entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Com a etiqueta del botó quan s'afegeix mèdia al contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Botó Insereix a mèdia"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Insereix a %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Utilitza com a imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Com a etiqueta del botó per a seleccionar l'ús d'una imatge com a imatge "
"destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Utilitza la imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Suprimeix la imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Com a etiqueta del botó quan s'elimina la imatge destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Suprimeix la imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Estableix la imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Com a etiqueta del botó quan s'estableix la imatge destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Estableix la imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"A l'editor utilitzat per al títol de la caixa meta de la imatge destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Caixa meta d'imatge destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Atributs de l'entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"A l'editor utilitzat per al títol de la caixa meta d'atributs de l'entrada."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Caixa meta d'atributs"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Atributs de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Arxius d'entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Afegeix elements \"Arxiu de tipus de contingut\" amb aquesta etiqueta a la "
"llista d'entrades que es mostra quan s'afegeix elements a un menú existent a "
"un CPT amb arxius activats. Només apareix quan s'editen menús en mode "
"\"Vista prèvia en viu\" i s'ha proporcionat un segment d'URL d'arxiu "
"personalitzat."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Menú de navegació d'arxius"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Arxius de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "No s'han trobat entrades a la paperera"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"A la part superior de la pantalla de la llista de tipus de contingut quan no "
"hi ha entrades a la paperera."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "No s'han trobat entrades a la paperera"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "No s'han trobat %s a la paperera"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "No s'han trobat entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"A la part superior de la pantalla de la llista de tipus de contingut quan no "
"hi ha publicacions que mostrar."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "No s'han trobat elements"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "No s'han trobat %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Cerca entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""
"A la part superior de la pantalla d'elements, quan es cerca un element."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Cerca elements"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Cerca %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Pàgina mare:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""
"Per als tipus jeràrquics a la pantalla de la llista de tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Prefix de l'element superior"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "%s pare:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Entrada nova"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Element nou"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Nou %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Afegeix una nova entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""
"A la part superior de la pantalla de l'editor, quan s'afegeix un element nou."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Afegeix un element nou"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Afegeix nou %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Mostra les entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Apareix a la barra d'administració a la vista 'Totes les entrades', sempre "
"que el tipus de contingut admeti arxius i la pàgina d'inici no sigui un "
"arxiu d'aquell tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Visualitza els elements"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Mostra l'entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "A la barra d'administració per a visualitzar l'element en editar-lo."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Visualitza l'element"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Mostra %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Edita l'entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""
"A la part superior de la pantalla de l'editor, quan s'edita un element."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Edita l'element"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Edita %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Totes les entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "En el submenú de tipus de contingut del tauler d'administració."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Tots els elements"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Tots %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Nom del menú d'administració per al tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Nom del menú"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Regenera totes les etiquetes utilitzant les etiquetes singular i plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Regenera"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""
"Els tipus de contingut actius estan activats i registrats amb WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Un resum descriptiu del tipus de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Afegeix personalització"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Habilita diverses característiques a l'editor de contingut."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Formats de les entrades"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Retroenllaços"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Selecciona les taxonomies existents per a classificar els elements del tipus "
"de contingut."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Cerca camps"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Res a importar"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr "L'extensió Custom Post Type UI es pot desactivar."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "S'ha importat %d element de Custom Post Type UI -"
msgstr[1] "S'ha importat %d elements de Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "No s'han pogut importar les taxonomies."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "No s'han pogut importar els tipus de contingut."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"No s'ha seleccionat res de l'extensió Custom Post Type UI per a importar."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "S'ha importat 1 element"
msgstr[1] "S'han importat %s elements"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"La importació d'un tipus de contingut o taxonomia amb la mateixa clau que "
"una que ja existeix sobreescriurà la configuració del tipus de contingut o "
"taxonomia existents amb la de la importació."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importar des de Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"El codi següent es pot utilitzar per registrar una versió local dels "
"elements seleccionats. L'emmagatzematge local de grups de camps, tipus de "
"contingut o taxonomies pot proporcionar molts avantatges, com ara temps de "
"càrrega més ràpids, control de versions i camps/configuracions dinàmiques. "
"Simplement copia i enganxa el codi següent al fitxer functions.php del tema "
"o inclou-lo dins d'un fitxer extern i, a continuació, desactiva o suprimeix "
"els elements de l'administrador de l'ACF."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Exporta - Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Exporta"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Selecciona taxonomies"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Selecciona els tipus de contingut"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "S'ha exportat 1 element."
msgstr[1] "S'han exportat %s elements."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Categoria"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Etiqueta"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "S'ha creat la taxonomia %s"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "S'ha actualitzat la taxonomia %s"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "S'ha actualitzat l'esborrany de la taxonomia."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomia programada per a."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "S'ha enviat la taxonomia."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "S'ha desat la taxonomia."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "S'ha suprimit la taxonomia."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "S'ha actualitzat la taxonomia."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Aquesta taxonomia no s'ha pogut registrar perquè la seva clau s'està "
"utilitzant a una altra taxonomia registrada per una altra extensió o tema."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomia sincronitzada."
msgstr[1] "%s taxonomies sincronitzades."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomia duplicada."
msgstr[1] "%s taxonomies duplicades."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomia desactivada."
msgstr[1] "%s taxonomies desactivades."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomia activada."
msgstr[1] "%s taxonomies activades."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Termes"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Tipus de contingut sincronitzat."
msgstr[1] "%s tipus de continguts sincronitzats."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Tipus de contingut duplicat."
msgstr[1] "%s tipus de continguts duplicats."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Tipus de contingut desactivat."
msgstr[1] "%s tipus de continguts desactivats."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Tipus de contingut activat."
msgstr[1] "%s tipus de continguts activats."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Tipus de contingut"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Configuració avançada"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Configuració bàsica"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Aquest tipus de contingut no s'ha pogut registrar perquè la seva clau s'està "
"utilitzant a un altre tipus de contingut registrat per una altra extensió o "
"tema."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Pàgines"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Enllaça grups de camps existents"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s tipus de contingut creat"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Afegeix camps a %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Tipus de contingut %s actualitzat"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "S'ha actualitzat l'esborrany del tipus de contingut."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Tipus de contingut programat per."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Tipus de contingut enviat."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Tipus de contingut desat."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Tipus de contingut actualitzat."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Tipus de contingut suprimit."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Tecleja per cercar..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Només a PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Grups de camps enllaçats correctament."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importa tipus de contingut i taxonomies registrades amb Custom Post Type UI "
"i gestiona-les amb ACF. <a href=\"%s\">Comença</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taxonomia"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "tipus de contingut"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Fet"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Grup(s) de camp(s)"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Selecciona un o diversos grups de camps..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Selecciona els grups de camps a enllaçar."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Grup de camps enllaçat correctament."
msgstr[1] "Grups de camps enllaçats correctament."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Error de registre"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Aquest element no s'ha pogut registrar perquè la seva clau s'està utilitzant "
"a un altre element registrat per una altra extensió o tema."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "API REST"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Permisos"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Visibilitat"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Etiquetes"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Pestanyes de configuracions de camps"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Valor del codi de substitució d'ACF desactivat a la previsualització]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Tanca la finestra emergent"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Camp mogut a un altre grup"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Tanca la finestra emergent"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Comença un nou grup de pestanyes en aquesta pestanya."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nou grup de pestanyes"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Utilitza una casella de selecció estilitzada utilitzant select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Desa l'opció «Altre»"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Permitir l'opció «Altre»"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Afegeix un «Alternar tots»"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Desa els valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Permet valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Els valors personalitzats de les caselles de selecció no poden estar buits. "
"Desmarqueu els valors buits."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Actualitzacions"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Logotip de l'Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Desa els canvis"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Títol del grup de camps"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Afegeix un títol"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Sou nou a l'ACF? Feu una ullada a la nostra <a href=\"%s\" "
"target=\"_blank\">guia d'inici</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Afegeix un grup de camps"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"L'ACF utilitza <a href=\"%s\" target=\"_blank\">grups de camps</a> per "
"agrupar camps personalitzats i, a continuació, adjuntar aquests camps per "
"editar pantalles."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Afegiu el vostre primer grup de camps"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Pàgines d'opcions"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Blocs ACF"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Camp de galeria"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Camp de contingut flexible"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Camp repetible"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Desbloquegeu característiques addicionals amb l'ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Suprimeix el grup de camps"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Creat el %1$s a les %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Configuració del grup"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Regles d'ubicació"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Trieu entre més de 30 tipus de camps. <a href=\"%s\" target=\"_blank\">Més "
"informació</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Comenceu a crear nous camps personalitzats per a les vostres entrades, "
"pàgines, tipus de contingut personalitzats i altres continguts del WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Afegiu el vostre primer camp"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Afegeix un camp"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Presentació"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validació"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "General"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importa JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Exporta com a JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "S'ha desactivat el grup de camps."
msgstr[1] "S'han desactivat %s grups de camps."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "S'ha activat el grup de camps."
msgstr[1] "S'han activat %s grups de camps."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Desactiva"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Desactiva aquest element"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Activa"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Activa aquest element"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Voleu moure el grup de camps a la paperera?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inactiva"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields i Advanced Custom Fields PRO no haurien d'estar "
"actius al mateix temps. Hem desactivat Advanced Custom Fields PRO "
"automàticament."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields i Advanced Custom Fields PRO no haurien d'estar "
"actius al mateix temps. Hem desactivat Advanced Custom Fields automàticament."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s ha de tenir un usuari amb el rol %2$s."
msgstr[1] "%1$s ha de tenir un usuari amb un dels següents rols: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s ha de tenir un identificador d'usuari vàlid."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Sol·licitud no vàlida."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s no és un dels %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s ha de tenir el terme %2$s."
msgstr[1] "%1$s ha de tenir un dels següents termes: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s ha de ser del tipus de contingut %2$s."
msgstr[1] "%1$s ha de ser d'un dels següents tipus de contingut: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s ha de tenir un identificador d'entrada vàlid."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s requereix un identificador d'adjunt vàlid."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Mostra a l'API REST"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Activa la transparència"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Matriu RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Cadena RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Cadena hexadecimal"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Actualitza a la versió Pro"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Activa"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "«%s» no és una adreça electrònica vàlida"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Valor de color"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Seleccioneu el color per defecte"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Neteja el color"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blocs"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opcions"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Usuaris"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Elements del menú"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Ginys"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Adjunts"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Entrades"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Darrera actualització: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""
"Aquest grup de camps no està disponible per a la comparació de diferències."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Paràmetre/s del grup de camps no vàlids."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Esperant desar"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "S'ha desat"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importa"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Revisa els canvis"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Ubicat a: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Ubicat a l'extensió: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Ubicat al tema: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Diversos"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Sincronitza els canvis"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "S'està carregant el diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Revisa els canvis JSON locals"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Visiteu el lloc web"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Visualitza els detalls"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Versió %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Informació"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Servei d'ajuda</a>. Els professionals de "
"suport al servei d'ajuda us ajudaran amb els problemes tècnics més profunds."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Debats</a>. Tenim una comunitat activa i "
"amistosa als nostres fòrums comunitaris que pot ajudar-vos a descobrir com "
"es fan les coses al món de l'ACF."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentació</a>. La nostra extensa "
"documentació conté referències i guies per a la majoria de situacions que "
"podeu trobar."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Som fanàtics del suport i volem que tragueu el màxim profit del vostre lloc "
"web amb l'ACF. Si trobeu alguna dificultat, hi ha diversos llocs on podeu "
"trobar ajuda:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Ajuda i suport"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Utilitzeu la pestanya d'ajuda i suport per posar-vos en contacte amb "
"nosaltres si necessiteu ajuda."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Abans de crear el vostre primer grup de camps recomanem llegir abans la "
"nostra guia <a href=\"%s\" target=\"_blank\">Primers passos</a> per "
"familiaritzar-vos amb la filosofia i les millors pràctiques de l'extensió."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"L'extensió Advanced Custom Fields proporciona un maquetador de formularis "
"visual per personalitzar les pantalles d'edició del WordPress amb camps "
"addicionals i una API intuïtiva per mostrar els valors dels camps "
"personalitzats en qualsevol fitxer de plantilla de tema."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Resum"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "El tipus d'ubicació «%s» ja està registrat."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "La classe «%s» no existeix."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "El nonce no és vàlid."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Error en carregar el camp."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Giny"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rol de l'usuari"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentari"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format de l'entrada"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Element del menú"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estat de l'entrada"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Ubicacions dels menús"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomia de l'entrada"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Pàgina filla (té mare)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Pàgina mare (té filles)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Pàgina de primer nivell (no té mare)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Pàgina de les entrades"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Portada"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipus de pàgina"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Veient l’administració"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Veient la part frontal"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Connectat"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Usuari actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Plantilla de la pàgina"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registra"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Afegeix / Edita"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulari d'usuari"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Pàgina mare"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Superadministrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rol de l'usuari actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Plantilla per defecte"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Plantilla de l'entrada"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoria de l'entrada"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tots els formats de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Adjunt"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "Cal introduir un valor a %s"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Mostra aquest camp si"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Lògica condicional"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "i"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON local"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Clona el camp"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Comproveu que tots els complements prèmium (%s) estan actualitzats a la "
"darrera versió."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Aquesta versió inclou millores a la base de dades i necessita una "
"actualització."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Gràcies per actualitzar a %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Cal actualitzar la base de dades"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Pàgina d'opcions"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Contingut flexible"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Repetible"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Torna a totes les eines"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si hi ha diversos grups de camps a la pantalla d'edició, s'utilitzaran les "
"opcions del primer grup de camps (el que tingui el nombre d'ordre més baix)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Seleccioneu</b> els elements a <b>amagar</b>de la pantalla d'edició."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Amaga a la pantalla"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Envia retroenllaços"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Etiquetes"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Categories"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Atributs de la pàgina"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Àlies"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisions"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Comentaris"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Debats"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Extracte"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Editor de contingut"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Enllaç permanent"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Es mostra a la llista de grups de camps"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Els grups de camps amb un ordre més baix apareixeran primer"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Núm. d’ordre"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Sota els camps"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Sota les etiquetes"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Posició de les instruccions"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Posició de les etiquetes"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (després del contingut)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Alta (després del títol)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Posició"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Fluid (sense la caixa meta)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Estàndard (en una caixa meta de WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Estil"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tipus"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Clau"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Ordre"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Tanca el camp"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "classe"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "amplada"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Atributs del contenidor"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Obligatori"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instruccions"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Tipus de camp"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Una sola paraula, sense espais. S’admeten barres baixes i guions"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nom del camp"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Aquest és el nom que apareixerà a la pàgina d'edició"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Etiqueta del camp"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Suprimeix"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Suprimeix el camp"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Mou"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Mou el camp a un altre grup"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplica el camp"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Edita el camp"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Arrossegueu per reordenar"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Mostra aquest grup de camps si"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No hi ha actualitzacions disponibles."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"S'ha completat l'actualització de la base de dades. <a href=\"%s\">Feu una "
"ullada a les novetats</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "S'estan llegint les tasques d'actualització…"

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "L'actualització ha fallat."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "S'ha completat l'actualització."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "S'estan actualitzant les dades a la versió %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"És recomanable que feu una còpia de seguretat de la base de dades abans de "
"continuar. Segur que voleu executar l'actualitzador ara?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Seleccioneu almenys un lloc web per actualitzar."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"S'ha completat l'actualització de la base de dades. <a href=\"%s\">Torna al "
"tauler de la xarxa</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "El lloc web està actualitzat"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"El lloc web requereix una actualització de la base de dades de %1$s a %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Lloc"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Actualitza els llocs"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Els següents llocs web necessiten una actualització de la base de dades. "
"Marqueu els que voleu actualitzar i feu clic a %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Afegeix un grup de regles"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un grup de regles que determinaran quines pantalles d’edició mostraran "
"aquests camps personalitzats"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regles"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copiat"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copia-ho al porta-retalls"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Seleccioneu els grups de camps que voleu exportar i, a continuació, "
"seleccioneu el mètode d'exportació. Exporteu com a JSON per exportar a un "
"fitxer .json que després podeu importar a una altra instal·lació d'ACF. "
"Genereu PHP per exportar a codi PHP que podeu col·locar al vostre tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Seleccioneu grups de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "No s'ha seleccionat cap grup de camps"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exporta els grups de camps"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "El fitxer d'importació és buit"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Tipus de fitxer incorrecte"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "S'ha produït un error en penjar el fitxer. Torneu-ho a provar"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Seleccioneu el fitxer JSON de l'Advanced Custom Fields que voleu importar. "
"En fer clic al botó d'importació, l'ACF importarà els grups de camps."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importa grups de camps"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sincronitza"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplica"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplica aquest element"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Suports"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentació"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Descripció"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Sincronització disponible"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "S'ha sincronitzat el grup de camps."
msgstr[1] "S'han sincronitzat %s grups de camps."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "S'ha duplicat el grup de camps."
msgstr[1] "S'han duplicat %s grups de camps."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actiu <span class=\"count\">(%s)</span>"
msgstr[1] "Actius <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Revisa els llocs web i actualitza"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Actualitza la base de dades"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Camps personalitzats"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Mou el camp"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Seleccioneu la destinació d'aquest camp"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "El camp %1$s ara es pot trobar al grup de camps %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "S’ha completat el moviment."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Actiu"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Claus dels camps"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Configuració"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Ubicació"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Nul"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "copia"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(aquest camp)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Marcat"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Mou el grup de camps"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "No hi ha camps commutables disponibles"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "El títol del grup de camps és obligatori"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "Aquest camp no es pot moure fins que no se n’hagin desat els canvis"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "La cadena «field_» no es pot utilitzar al principi del nom d'un camp"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "S'ha actualitzat l’esborrany del grup de camps."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "S'ha programat el grup de camps."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "S’ha tramès el grup de camps."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "S'ha desat el grup de camps."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "S'ha publicat el grup de camps."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "S'ha suprimit el grup de camps."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "S'ha actualitzat el grup de camps."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Eines"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "no és igual a"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "és igual a"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formularis"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Pàgina"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Entrada"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:327
msgid "Choice"
msgstr "Elecció"

#: includes/fields.php:325
msgid "Basic"
msgstr "Bàsic"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Desconegut"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "El tipus de camp no existeix"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "S'ha detectat brossa"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "S'ha actualitzat l'entrada"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Actualitza"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Valida el correu electrònic"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Contingut"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Títol"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Edita el grup de camps"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "La selecció és inferior a"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "La selecció és superior a"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "El valor és inferior a"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "El valor és superior a"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "El valor conté"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "El valor coincideix amb el patró"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "El valor no és igual a"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "El valor és igual a"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "No té cap valor"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Té algun valor"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Cancel·la"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "N'esteu segur?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "Cal revisar %d camps"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "Cal revisar un camp"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "La validació ha fallat"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Validació correcta"

#: includes/media.php:54
msgid "Restricted"
msgstr "Restringit"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Amaga els detalls"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Expandeix els detalls"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "S'ha penjat a aquesta entrada"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Actualitza"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Edita"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Perdreu els canvis que heu fet si abandoneu aquesta pàgina"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "El tipus de fitxer ha de ser %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "o"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "La mida del fitxer no ha de superar %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "La mida del fitxer ha de ser almenys %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "L'alçada de la imatge no pot ser superior a %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "L'alçada de la imatge ha de ser almenys de %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "L'amplada de la imatge no pot ser superior a %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "L'amplada de la imatge ha de ser almenys de %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(sense títol)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Mida completa"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Gran"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Mitjana"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(sense etiqueta)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Estableix l'alçada de l'àrea de text"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Files"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Àrea de text"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Afegeix una casella de selecció addicional per commutar totes les opcions"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Desa els valors personalitzats a les opcions del camp"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Permet afegir-hi valors personalitzats"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Afegeix una nova opció"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Commuta'ls tots"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Permet les URLs dels arxius"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Arxius"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Enllaç de la pàgina"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Afegeix"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Nom"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s afegit"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s ja existeix"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "L'usuari no pot afegir nous %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "Identificador de terme"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Objecte de terme"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Carrega el valor dels termes de l’entrada"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Carrega els termes"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Connecta els termes seleccionats a l'entrada"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Desa els termes"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Permet crear nous termes mentre s’està editant"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Crea els termes"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Botons d'opció"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Un sol valor"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Selecció múltiple"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Casella de selecció"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Múltiples valors"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Seleccioneu l'aparença d'aquest camp"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Aparença"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Seleccioneu la taxonomia a mostrar"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "No hi ha %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "El valor ha de ser igual o inferior a %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "El valor ha de ser igual o superior a %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "El valor ha de ser un nombre"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Desa els valors d’’Altres’ a les opcions del camp"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Afegeix l'opció «altres» per permetre valors personalitzats"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Altres"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Botó d'opció"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definiu un punt final per a aturar l’acordió previ. Aquest acordió no serà "
"visible."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Permet que aquest acordió s'obri sense tancar els altres."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Expansió múltiple"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Mostra aquest acordió obert en carregar la pàgina."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Obert"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordió"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Restringeix quins fitxers es poden penjar"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "Identificador de fitxer"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL del fitxer"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Matriu de fitxer"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Afegeix un fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "No s'ha seleccionat cap fitxer"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nom del fitxer"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Actualitza el fitxer"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Edita el fitxer"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Escull el fitxer"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Fitxer"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Contrasenya"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Especifiqueu el valor a retornar"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Usa AJAX per a carregar opcions de manera relaxada?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Introduïu cada valor per defecte en una línia nova"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "No s'ha pogut carregar"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "S'està cercant&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "S'estan carregant més resultats&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Només podeu seleccionar %d elements"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Només podeu seleccionar un element"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Suprimiu %d caràcters"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Suprimiu un caràcter"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Introduïu %d o més caràcters"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Introduïu un o més caràcters"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No s'ha trobat cap coincidència"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"Hi ha %d resultats disponibles, utilitzeu les fletxes amunt i avall per "
"navegar-hi."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hi ha un resultat disponible, premeu retorn per seleccionar-lo."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Selecció"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "Identificador de l'usuari"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Objecte d'usuari"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Matriu d’usuari"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Tots els rols d'usuari"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtra per rol"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Usuari"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Escolliu un color"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Predeterminat"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Neteja"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fet"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ara"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fus horari"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegon"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Mil·lisegon"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segon"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Trieu l'hora"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Selector de data i hora"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Punt final"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Alineat a l'esquerra"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Alineat a la part superior"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Ubicació"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Pestanya"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "El valor ha de ser un URL vàlid"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL de l'enllaç"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Matriu d’enllaç"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "S'obre en una nova finestra/pestanya"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Escolliu l’enllaç"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Enllaç"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Correu electrònic"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Mida del pas"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Valor màxim"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Valor mínim"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Interval"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Ambdós (matriu)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Etiqueta"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horitzontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "vermell : Vermell"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Per a més control, podeu especificar tant un valor com una etiqueta "
"d'aquesta manera:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Introduïu cada opció en una línia nova."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Opcions"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Grup de botons"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Permet nul?"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Pare"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "El TinyMCE no s'inicialitzarà fins que no es faci clic al camp"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Endarrereix la inicialització?"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Mostra els botons de penjar mèdia?"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Barra d'eines"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Només Text"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Només visual"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visual i text"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Pestanyes"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Feu clic per inicialitzar el TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "El valor no ha de superar els %d caràcters"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Deixeu-lo en blanc per no establir cap límit"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Límit de caràcters"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Apareix després del camp"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Afegeix al final"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Apareix abans del camp"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Afegeix al principi"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Apareix a dins del camp"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Text de mostra"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Apareix quan es crea una nova entrada"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requereix com a mínim %2$s selecció"
msgstr[1] "%1$s requereix com a mínim %2$s seleccions"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID de l’entrada"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Objecte de l'entrada"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Màxim d'entrades"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Mínim d'entrades"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Imatge destacada"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Els elements seleccionats es mostraran a cada resultat"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elements"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtres"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Totes les taxonomies"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtra per taxonomia"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Tots els tipus de contingut"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtra per tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Cerca…"

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Seleccioneu una taxonomia"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Seleccioneu el tipus de contingut"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "No s'ha trobat cap coincidència"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "S'està carregant"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "S’ha arribat al màxim de valors ({max} valors)"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relació"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Llista separada per comes. Deixeu-ho en blanc per a tots els tipus"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Tipus de fitxers permesos"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Màxim"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Mida del fitxer"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Restringeix quines imatges es poden penjar"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Mínim"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "S'ha penjat a l'entrada"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tots"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limita l'elecció d'elements de la mediateca"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Mediateca"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Mida de la vista prèvia"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID de la imatge"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL de la imatge"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Matriu d'imatges"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Especifiqueu el valor a retornar a la interfície frontal"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Valor de retorn"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Afegeix imatge"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "No s'ha seleccionat cap imatge"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Suprimeix"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Edita"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Totes les imatges"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Actualitza la imatge"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Edita la imatge"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Escolliu una imatge"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Imatge"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permet que el marcat HTML es mostri com a text visible en comptes de "
"renderitzat"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Escapa l’HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Sense format"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Afegeix &lt;br&gt; automàticament"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Afegeix paràgrafs automàticament"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Controla com es mostren les noves línies"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Noves línies"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "La setmana comença el"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "El format que s’usarà en desar el valor"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Format de desat"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Stm"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Següent"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Avui"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fet"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Selector de data"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Amplada"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Mida de la incrustació"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Introduïu la URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "El text que es mostrarà quan està inactiu"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Text d’inactiu"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "El text que es mostrarà quan està actiu"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Text d’actiu"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "IU estilitzada"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Valor per defecte"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Mostra el text al costat de la casella de selecció"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Missatge"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "No"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Sí"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Cert / Fals"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Taula"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Especifiqueu l’estil usat per a mostrar els camps escollits"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Disposició"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Sub camps"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Personalitzeu l'alçada del mapa"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Alçada"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Estableix el valor inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Centra el mapa inicial"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Centra"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Cerca l'adreça…"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Cerca la ubicació actual"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Neteja la ubicació"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Cerca"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Aquest navegador no és compatible amb la geolocalització"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "El format que es retornarà a través de les funcions del tema"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Format de retorn"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Personalitzat:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "El format que es mostrarà quan editeu una entrada"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Format a mostrar"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Selector d'hora"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactiu <span class=\"count\">(%s)</span>"
msgstr[1] "Inactius <span class=\"count\">(%s)</span>"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "No s'ha trobat cap camp a la paperera"

#: acf.php:486
msgid "No Fields found"
msgstr "No s'ha trobat cap camp"

#: acf.php:485
msgid "Search Fields"
msgstr "Cerca camps"

#: acf.php:484
msgid "View Field"
msgstr "Visualitza el camp"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nou camp"

#: acf.php:482
msgid "Edit Field"
msgstr "Edita el camp"

#: acf.php:481
msgid "Add New Field"
msgstr "Afegeix un nou camp"

#: acf.php:479
msgid "Field"
msgstr "Camp"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Camps"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "No s'ha trobat cap grup de camps a la paperera"

#: acf.php:452
msgid "No Field Groups found"
msgstr "No s'ha trobat cap grup de camps"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Cerca grups de camps"

#: acf.php:450
msgid "View Field Group"
msgstr "Visualitza el grup de camps"

#: acf.php:449
msgid "New Field Group"
msgstr "Nou grup de camps"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Edita el grup de camps"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Afegeix un nou grup de camps"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Afegeix"

#: acf.php:445
msgid "Field Group"
msgstr "Grup de camps"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Grups de camps"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personalitzeu el WordPress amb camps potents, professionals i intuïtius."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
#, fuzzy
#| msgid "%s value is required"
msgid "Block type name is required."
msgstr "Cal introduir un valor a %s"

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Canvia a edició"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Canvia a previsualització"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
#, fuzzy
#| msgid "Settings"
msgid "%s settings"
msgstr "Paràmetres"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "S’han actualitzat les opcions"

#: pro/updates.php:99
#, fuzzy
#| msgid ""
#| "To enable updates, please enter your license key on the <a "
#| "href=\"%s\">Updates</a> page. If you don't have a licence key, please see "
#| "<a href=\"%s\">details & pricing</a>."
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Per a activar les actualitzacions, introduïu la clau de llicència a la "
"pàgina d’<a href=\"%s\">Actualitzacions</a>. Si no teniu cap clau de "
"llicència, vegeu-ne els<a href=\"%s\">detalls i preu</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr "<b>Error</b>. No s’ha pogut connectar al servidor d’actualitzacions"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Torneu-ho a comprovar"

#: pro/updates.php:593
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>Error</b>. No s’ha pogut connectar al servidor d’actualitzacions"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publica"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"No s’han trobat grups de camps personalitzats per a aquesta pàgina "
"d’opcions. <a href=\"%s\">Creeu un grup de camps nou</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Error</b>. No s’ha pogut connectar al servidor d’actualitzacions"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. No s’ha pogut verificar el paquet d’actualització. Torneu-ho a "
"intentar o desactiveu i torneu a activar la vostra llicència de l’ACF PRO."

#: pro/admin/admin-updates.php:199
#, fuzzy
#| msgid ""
#| "<b>Error</b>. Could not authenticate update package. Please check again "
#| "or deactivate and reactivate your ACF PRO license."
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. No s’ha pogut verificar el paquet d’actualització. Torneu-ho a "
"intentar o desactiveu i torneu a activar la vostra llicència de l’ACF PRO."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Escolliu un o més camps a clonar"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Mostra"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Indiqueu l’estil que s’usarà per a mostrar el camp clonat"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (mostra els camps escollits en un grup dins d’aquest camp)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Fluid (reemplaça aquest camp amb els camps escollits)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Les etiquetes es mostraran com a %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Prefixa les etiquetes dels camps"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Els valors es desaran com a %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Prefixa els noms dels camps"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Camp desconegut"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Grup de camps desconegut"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Tots els camps del grup de camps %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Afegeix una fila"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "disposició"
msgstr[1] "disposicions"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "disposicions"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Aquest camp requereix almenys {min} {label} de {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Aquest camp té un límit de {max} {label} de {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} de {identifier} disponible (màx {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} de {identifier} necessari (mín {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "El contingut flexible necessita almenys una disposició"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Feu clic al botó “%s” de sota per a començar a crear el vostre disseny"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:424
#, fuzzy
#| msgid "Duplicate Layout"
msgid "Duplicate layout"
msgstr "Duplica la disposició"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Esborra la disposició"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Feu clic per alternar"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Esborra la disposició"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplica la disposició"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Afegeix una disposició"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Mín"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Màx"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Mínim de disposicions"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Màxim de disposicions"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Etiqueta del botó"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Afegeix una imatge a la galeria"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "S’ha arribat al màxim d’elements seleccionats"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Llargada"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Llegenda"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Text alternatiu"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Afegeix a la galeria"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Accions massives"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Ordena per la data de càrrega"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Ordena per la data de modificació"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Ordena pel títol"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Inverteix l’ordre actual"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Tanca"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Selecció mínima"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Selecció màxima"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Tipus de fitxers permesos"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Insereix"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Especifiqueu on s’afegeixen els nous fitxers adjunts"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Afegeix-los al final"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Afegeix-los al principi"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "No s’ha arribat al mínim de files ({min} files)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "S’ha superat el màxim de files ({max} files)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
#, fuzzy
#| msgid "Posts Page"
msgid "Rows Per Page"
msgstr "Pàgina de les entrades"

#: pro/fields/class-acf-field-repeater.php:208
#, fuzzy
#| msgid "Select the taxonomy to be displayed"
msgid "Set the number of rows to be displayed on a page."
msgstr "Escolliu la taxonomia a mostrar"

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Mínim de files"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Màxim de files"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Replegat"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Escull un subcamp per a mostrar quan la fila estigui replegada"

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Arrossegueu per a reordenar"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Afegeix una fila"

#: pro/fields/class-acf-repeater-table.php:403
#, fuzzy
#| msgid "Duplicate"
msgid "Duplicate row"
msgstr "Duplica"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Esborra la fila"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
#, fuzzy
#| msgid "Current User"
msgid "Current Page"
msgstr "Usuari actual"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Portada"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Pàgina de les entrades"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Portada"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Pàgina de les entrades"

#: pro/locations/class-acf-location-block.php:71
#, fuzzy
#| msgid "No options pages exist"
msgid "No block types exist"
msgstr "No hi ha pàgines d’opcions"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "No hi ha pàgines d’opcions"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Desactiva la llicència"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activa la llicència"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informació de la llicència"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per a desbloquejar les actualitzacions, introduïu la clau de llicència a "
"continuació. Si no teniu cap clau de llicència, vegeu els <a "
"href=\"%s\">detalls i preu</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Clau de llicència"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
#, fuzzy
#| msgid "Better Validation"
msgid "Retry Activation"
msgstr "Validació millorada"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informació de l'actualització"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versió actual"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Darrera versió"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Actualització disponible"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Avís d’actualització"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr ""
"Introduïu la clau de llicència al damunt per a desbloquejar les "
"actualitzacions"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Actualitza l’extensió"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Please reactivate your license to unlock updates"
msgstr ""
"Introduïu la clau de llicència al damunt per a desbloquejar les "
"actualitzacions"
