<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'en_GB','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2025-05-08T10:32:33+00:00','po-revision-date'=>'2025-05-08T10:20:52+00:00','x-generator'=>'gettext','messages'=>['Learn more'=>'Learn more','ACF was unable to perform validation because the provided nonce failed verification.'=>'ACF was unable to perform validation because the provided nonce failed verification.','ACF was unable to perform validation because no nonce was received by the server.'=>'ACF was unable to perform validation because no nonce was received by the server.','are developed and maintained by'=>'are developed and maintained by','By default only admin users can edit this setting.'=>'By default, only admin users can edit this setting.','By default only super admin users can edit this setting.'=>'By default, only super admin users can edit this setting.','Close and Add Field'=>'Close and Add Field','A PHP function name to be called to handle the content of a meta box on your taxonomy. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.'=>'A PHP function name to be called to handle the content of a meta box on your taxonomy. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.','A PHP function name to be called when setting up the meta boxes for the edit screen. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.'=>'A PHP function name to be called when setting up the meta boxes for the edit screen. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.','Allow Access to Value in Editor UI'=>'Allow Access to Value in Editor UI','Learn more.'=>'Learn more.','Allow content editors to access and display the field value in the editor UI using Block Bindings or the ACF Shortcode. %s'=>'Allow content editors to access and display the field value in the editor UI using Block Bindings or the ACF Shortcode. %s','The requested ACF field type does not support output in Block Bindings or the ACF shortcode.'=>'The requested ACF field type does not support output in Block Bindings or the ACF shortcode.','The requested ACF field is not allowed to be output in bindings or the ACF Shortcode.'=>'The requested ACF field is not allowed to be output in bindings or the ACF Shortcode.','The requested ACF field type does not support output in bindings or the ACF Shortcode.'=>'The requested ACF field type does not support output in bindings or the ACF Shortcode.','[The ACF shortcode cannot display fields from non-public posts]'=>'[The ACF shortcode cannot display fields from non-public posts]','[The ACF shortcode is disabled on this site]'=>'[The ACF shortcode is disabled on this site]','Businessman Icon'=>'Businessman Icon','Forums Icon'=>'Forums Icon','YouTube Icon'=>'YouTube Icon','Yes (alt) Icon'=>'Yes (alt) Icon','Xing Icon'=>'Xing Icon','WordPress (alt) Icon'=>'WordPress (alt) Icon','WhatsApp Icon'=>'WhatsApp Icon','Write Blog Icon'=>'Write Blog Icon','Widgets Menus Icon'=>'Widgets Menus Icon','View Site Icon'=>'View Site Icon','Learn More Icon'=>'Learn More Icon','Add Page Icon'=>'Add Page Icon','Video (alt3) Icon'=>'Video (alt3) Icon','Video (alt2) Icon'=>'Video (alt2) Icon','Video (alt) Icon'=>'Video (alt) Icon','Update (alt) Icon'=>'Update (alt) Icon','Universal Access (alt) Icon'=>'Universal Access (alt) Icon','Twitter (alt) Icon'=>'Twitter (alt) Icon','Twitch Icon'=>'Twitch Icon','Tide Icon'=>'Tide Icon','Tickets (alt) Icon'=>'Tickets (alt) Icon','Text Page Icon'=>'Text Page Icon','Table Row Delete Icon'=>'Table Row Delete Icon','Table Row Before Icon'=>'Table Row Before Icon','Table Row After Icon'=>'Table Row After Icon','Table Col Delete Icon'=>'Table Col Delete Icon','Table Col Before Icon'=>'Table Col Before Icon','Table Col After Icon'=>'Table Col After Icon','Superhero (alt) Icon'=>'Superhero (alt) Icon','Superhero Icon'=>'Superhero Icon','Spotify Icon'=>'Spotify Icon','Shortcode Icon'=>'Shortcode Icon','Shield (alt) Icon'=>'Shield (alt) Icon','Share (alt2) Icon'=>'Share (alt2) Icon','Share (alt) Icon'=>'Share (alt) Icon','Saved Icon'=>'Saved Icon','RSS Icon'=>'RSS Icon','REST API Icon'=>'REST API Icon','Remove Icon'=>'Remove Icon','Reddit Icon'=>'Reddit Icon','Privacy Icon'=>'Privacy Icon','Printer Icon'=>'Printer Icon','Podio Icon'=>'Podio Icon','Plus (alt2) Icon'=>'Plus (alt2) Icon','Plus (alt) Icon'=>'Plus (alt) Icon','Plugins Checked Icon'=>'Plugins Checked Icon','Pinterest Icon'=>'Pinterest Icon','Pets Icon'=>'Pets Icon','PDF Icon'=>'PDF Icon','Palm Tree Icon'=>'Palm Tree Icon','Open Folder Icon'=>'Open Folder Icon','No (alt) Icon'=>'No (alt) Icon','Money (alt) Icon'=>'Money (alt) Icon','Menu (alt3) Icon'=>'Menu (alt3) Icon','Menu (alt2) Icon'=>'Menu (alt2) Icon','Menu (alt) Icon'=>'Menu (alt) Icon','Spreadsheet Icon'=>'Spreadsheet Icon','Interactive Icon'=>'Interactive Icon','Document Icon'=>'Document Icon','Default Icon'=>'Default Icon','Location (alt) Icon'=>'Location (alt) Icon','LinkedIn Icon'=>'LinkedIn Icon','Instagram Icon'=>'Instagram Icon','Insert Before Icon'=>'Insert Before Icon','Insert After Icon'=>'Insert After Icon','Insert Icon'=>'Insert Icon','Info Outline Icon'=>'Info Outline Icon','Images (alt2) Icon'=>'Images (alt2) Icon','Images (alt) Icon'=>'Images (alt) Icon','Rotate Right Icon'=>'Rotate Right Icon','Rotate Left Icon'=>'Rotate Left Icon','Rotate Icon'=>'Rotate Icon','Flip Vertical Icon'=>'Flip Vertical Icon','Flip Horizontal Icon'=>'Flip Horizontal Icon','Crop Icon'=>'Crop Icon','ID (alt) Icon'=>'ID (alt) icon','HTML Icon'=>'HTML Icon','Hourglass Icon'=>'Hourglass Icon','Heading Icon'=>'Heading Icon','Google Icon'=>'Google Icon','Games Icon'=>'Games Icon','Fullscreen Exit (alt) Icon'=>'Fullscreen Exit (alt) Icon','Fullscreen (alt) Icon'=>'Fullscreen (alt) Icon','Status Icon'=>'Status Icon','Image Icon'=>'Image Icon','Gallery Icon'=>'Gallery Icon','Chat Icon'=>'Chat Icon','Audio Icon'=>'Audio Icon','Aside Icon'=>'Aside Icon','Food Icon'=>'Food Icon','Exit Icon'=>'Exit Icon','Excerpt View Icon'=>'Excerpt View Icon','Embed Video Icon'=>'Embed Video Icon','Embed Post Icon'=>'Embed Post Icon','Embed Photo Icon'=>'Embed Photo Icon','Embed Generic Icon'=>'Embed Generic Icon','Embed Audio Icon'=>'Embed Audio Icon','Email (alt2) Icon'=>'Email (alt2) Icon','Ellipsis Icon'=>'Ellipsis Icon','Unordered List Icon'=>'Unordered List Icon','RTL Icon'=>'RTL Icon','Ordered List RTL Icon'=>'Ordered List RTL Icon','Ordered List Icon'=>'Ordered List Icon','LTR Icon'=>'LTR Icon','Custom Character Icon'=>'Custom Character Icon','Edit Page Icon'=>'Edit Page Icon','Edit Large Icon'=>'Edit Large Icon','Drumstick Icon'=>'Drumstick Icon','Database View Icon'=>'Database View Icon','Database Remove Icon'=>'Database Remove Icon','Database Import Icon'=>'Database Import Icon','Database Export Icon'=>'Database Export Icon','Database Add Icon'=>'Database Add Icon','Database Icon'=>'Database Icon','Cover Image Icon'=>'Cover Image Icon','Volume On Icon'=>'Volume On Icon','Volume Off Icon'=>'Volume Off Icon','Skip Forward Icon'=>'Skip Forward Icon','Skip Back Icon'=>'Skip Back Icon','Repeat Icon'=>'Repeat Icon','Play Icon'=>'Play Icon','Pause Icon'=>'Pause Icon','Forward Icon'=>'Forward Icon','Back Icon'=>'Back Icon','Columns Icon'=>'Columns Icon','Color Picker Icon'=>'Colour Picker Icon','Coffee Icon'=>'Coffee Icon','Code Standards Icon'=>'Code Standards Icon','Cloud Upload Icon'=>'Cloud Upload Icon','Cloud Saved Icon'=>'Cloud Saved Icon','Car Icon'=>'Car Icon','Camera (alt) Icon'=>'Camera (alt) Icon','Calculator Icon'=>'Calculator Icon','Button Icon'=>'Button Icon','Businessperson Icon'=>'Businessperson Icon','Tracking Icon'=>'Tracking Icon','Topics Icon'=>'Topics Icon','Replies Icon'=>'Replies Icon','PM Icon'=>'PM icon','Friends Icon'=>'Friends Icon','Community Icon'=>'Community Icon','BuddyPress Icon'=>'BuddyPress Icon','bbPress Icon'=>'bbPress icon','Activity Icon'=>'Activity Icon','Book (alt) Icon'=>'Book (alt) Icon','Block Default Icon'=>'Block Default Icon','Bell Icon'=>'Bell Icon','Beer Icon'=>'Beer Icon','Bank Icon'=>'Bank Icon','Arrow Up (alt2) Icon'=>'Arrow Up (alt2) Icon','Arrow Up (alt) Icon'=>'Arrow Up (alt) Icon','Arrow Right (alt2) Icon'=>'Arrow Right (alt2) Icon','Arrow Right (alt) Icon'=>'Arrow Right (alt) Icon','Arrow Left (alt2) Icon'=>'Arrow Left (alt2) Icon','Arrow Left (alt) Icon'=>'Arrow Left (alt) Icon','Arrow Down (alt2) Icon'=>'Arrow Down (alt2) Icon','Arrow Down (alt) Icon'=>'Arrow Down (alt) Icon','Amazon Icon'=>'Amazon Icon','Align Wide Icon'=>'Align Wide Icon','Align Pull Right Icon'=>'Align Pull Right Icon','Align Pull Left Icon'=>'Align Pull Left Icon','Align Full Width Icon'=>'Align Full Width Icon','Airplane Icon'=>'Aeroplane Icon','Site (alt3) Icon'=>'Site (alt3) Icon','Site (alt2) Icon'=>'Site (alt2) Icon','Site (alt) Icon'=>'Site (alt) Icon','Upgrade to ACF PRO to create options pages in just a few clicks'=>'Upgrade to ACF PRO to create options pages in just a few clicks','Invalid request args.'=>'Invalid request args.','Sorry, you do not have permission to do that.'=>'Sorry, you do not have permission to do that.','Blocks Using Post Meta'=>'Blocks Using Post Meta','ACF PRO logo'=>'ACF PRO logo','ACF PRO Logo'=>'ACF PRO Logo','%s requires a valid attachment ID when type is set to media_library.'=>'%s requires a valid attachment ID when type is set to media_library.','%s is a required property of acf.'=>'%s is a required property of acf.','The value of icon to save.'=>'The value of icon to save.','The type of icon to save.'=>'The type of icon to save.','Yes Icon'=>'Yes icon','WordPress Icon'=>'WordPress icon','Warning Icon'=>'Warning icon','Visibility Icon'=>'Visibility icon','Vault Icon'=>'Vault icon','Upload Icon'=>'Upload icon','Update Icon'=>'Update icon','Unlock Icon'=>'Unlock icon','Universal Access Icon'=>'Universal access icon','Undo Icon'=>'Undo icon','Twitter Icon'=>'X icon','Trash Icon'=>'Bin icon','Translation Icon'=>'Translation icon','Tickets Icon'=>'Tickets icon','Thumbs Up Icon'=>'Thumbs up icon','Thumbs Down Icon'=>'Thumbs down icon','Text Icon'=>'Text icon','Testimonial Icon'=>'Testimonial icon','Tagcloud Icon'=>'Tag cloud icon','Tag Icon'=>'Tag icon','Tablet Icon'=>'Tablet icon','Store Icon'=>'Store icon','Sticky Icon'=>'Sticky icon','Star Half Icon'=>'Star half icon','Star Filled Icon'=>'Star filled icon','Star Empty Icon'=>'Star empty icon','Sos Icon'=>'SOS icon','Sort Icon'=>'Sort icon','Smiley Icon'=>'Smiley icon','Smartphone Icon'=>'Smartphone icon','Slides Icon'=>'Slides icon','Shield Icon'=>'Shield icon','Share Icon'=>'Share icon','Search Icon'=>'Search icon','Screen Options Icon'=>'Screen options icon','Schedule Icon'=>'Schedule icon','Redo Icon'=>'Redo icon','Randomize Icon'=>'Randomise icon','Products Icon'=>'Products icon','Pressthis Icon'=>'Pressthis icon','Post Status Icon'=>'Post status icon','Portfolio Icon'=>'Portfolio icon','Plus Icon'=>'Plus icon','Playlist Video Icon'=>'Playlist video icon','Playlist Audio Icon'=>'Playlist audio icon','Phone Icon'=>'Phone icon','Performance Icon'=>'Performance icon','Paperclip Icon'=>'Paper clip icon','No Icon'=>'No icon','Networking Icon'=>'Networking icon','Nametag Icon'=>'Name tag icon','Move Icon'=>'Move icon','Money Icon'=>'Money icon','Minus Icon'=>'Minus icon','Migrate Icon'=>'Migrate icon','Microphone Icon'=>'Microphone icon','Megaphone Icon'=>'Megaphone icon','Marker Icon'=>'Marker icon','Lock Icon'=>'Lock icon','Location Icon'=>'Location icon','List View Icon'=>'List view icon','Lightbulb Icon'=>'Lightbulb icon','Left Right Icon'=>'Left right icon','Layout Icon'=>'Layout icon','Laptop Icon'=>'Laptop icon','Info Icon'=>'Info icon','Index Card Icon'=>'Index card icon','ID Icon'=>'ID icon','Hidden Icon'=>'Hidden icon','Heart Icon'=>'Heart icon','Hammer Icon'=>'Hammer icon','Groups Icon'=>'Groups icon','Grid View Icon'=>'Grid view icon','Forms Icon'=>'Forms icon','Flag Icon'=>'Flag icon','Filter Icon'=>'Filter icon','Feedback Icon'=>'Feedback icon','Facebook (alt) Icon'=>'Facebook (alt) icon','Facebook Icon'=>'Facebook icon','External Icon'=>'External icon','Email (alt) Icon'=>'Email (alt) icon','Email Icon'=>'Email icon','Video Icon'=>'Video icon','Unlink Icon'=>'Unlink icon','Underline Icon'=>'Underline icon','Text Color Icon'=>'Text colour icon','Table Icon'=>'Table icon','Strikethrough Icon'=>'Strikethrough icon','Spellcheck Icon'=>'Spellcheck icon','Remove Formatting Icon'=>'Remove formatting icon','Quote Icon'=>'Quote icon','Paste Word Icon'=>'Paste word icon','Paste Text Icon'=>'Paste text icon','Paragraph Icon'=>'Paragraph icon','Outdent Icon'=>'Outdent icon','Kitchen Sink Icon'=>'Kitchen sink icon','Justify Icon'=>'Justify icon','Italic Icon'=>'Italic icon','Insert More Icon'=>'Insert more icon','Indent Icon'=>'Indent icon','Help Icon'=>'Help icon','Expand Icon'=>'Expand icon','Contract Icon'=>'Contract icon','Code Icon'=>'Code icon','Break Icon'=>'Break icon','Bold Icon'=>'Bold icon','Edit Icon'=>'Edit icon','Download Icon'=>'Download icon','Dismiss Icon'=>'Dismiss icon','Desktop Icon'=>'Desktop icon','Dashboard Icon'=>'Dashboard icon','Cloud Icon'=>'Cloud icon','Clock Icon'=>'Clock icon','Clipboard Icon'=>'Clipboard icon','Chart Pie Icon'=>'Chart pie icon','Chart Line Icon'=>'Chart line icon','Chart Bar Icon'=>'Chart bar icon','Chart Area Icon'=>'Chart area icon','Category Icon'=>'Category icon','Cart Icon'=>'Basket icon','Carrot Icon'=>'Carrot icon','Camera Icon'=>'Camera icon','Calendar (alt) Icon'=>'Calendar (alt) icon','Calendar Icon'=>'Calendar icon','Businesswoman Icon'=>'Businesswoman icon','Building Icon'=>'Building icon','Book Icon'=>'Book icon','Backup Icon'=>'Backup icon','Awards Icon'=>'Awards icon','Art Icon'=>'Art icon','Arrow Up Icon'=>'Arrow up icon','Arrow Right Icon'=>'Arrow right icon','Arrow Left Icon'=>'Arrow left icon','Arrow Down Icon'=>'Arrow down icon','Archive Icon'=>'Archive icon','Analytics Icon'=>'Analytics icon','Align Right Icon'=>'Align right icon','Align None Icon'=>'Align none icon','Align Left Icon'=>'Align left icon','Align Center Icon'=>'Align centre icon','Album Icon'=>'Album icon','Users Icon'=>'Users icon','Tools Icon'=>'Tools icon','Site Icon'=>'Site icon','Settings Icon'=>'Settings icon','Post Icon'=>'Post icon','Plugins Icon'=>'Plugins icon','Page Icon'=>'Page icon','Network Icon'=>'Network icon','Multisite Icon'=>'Multisite icon','Media Icon'=>'Media icon','Links Icon'=>'Links icon','Home Icon'=>'Home icon','Customizer Icon'=>'Customiser icon','Comments Icon'=>'Comments icon','Collapse Icon'=>'Collapse icon','Appearance Icon'=>'Appearance icon','Generic Icon'=>'Generic icon','Icon picker requires a value.'=>'Icon picker requires a value.','Icon picker requires an icon type.'=>'Icon picker requires an icon type.','The available icons matching your search query have been updated in the icon picker below.'=>'The available icons matching your search query have been updated in the icon picker below.','No results found for that search term'=>'No results found for that search term','Array'=>'Array','String'=>'String','Specify the return format for the icon. %s'=>'Specify the return format for the icon. %s','Select where content editors can choose the icon from.'=>'Select where content editors can choose the icon from.','The URL to the icon you\'d like to use, or svg as Data URI'=>'The URL to the icon you\'d like to use, or svg as Data URI','Browse Media Library'=>'Browse Media Library','The currently selected image preview'=>'The currently selected image preview','Click to change the icon in the Media Library'=>'Click to change the icon in the Media Library','Search icons...'=>'Search icons...','Media Library'=>'Media Library','Dashicons'=>'Dashicons','An interactive UI for selecting an icon. Select from Dashicons, the media library, or a standalone URL input.'=>'An interactive UI for selecting an icon. Select from Dashicons, the media library, or a standalone URL input.','Icon Picker'=>'Icon Picker','JSON Load Paths'=>'JSON Load Paths','JSON Save Paths'=>'JSON Save Paths','Registered ACF Forms'=>'Registered ACF Forms','Shortcode Enabled'=>'Shortcode Enabled','Field Settings Tabs Enabled'=>'Field Settings Tabs Enabled','Field Type Modal Enabled'=>'Field Type Modal Enabled','Admin UI Enabled'=>'Admin UI Enabled','Block Preloading Enabled'=>'Block Preloading Enabled','Blocks Per ACF Block Version'=>'Blocks Per ACF Block Version','Blocks Per API Version'=>'Blocks Per API Version','Registered ACF Blocks'=>'Registered ACF Blocks','Light'=>'Light','Standard'=>'Standard','REST API Format'=>'REST API Format','Registered Options Pages (PHP)'=>'Registered Options Pages (PHP)','Registered Options Pages (JSON)'=>'Registered Options Pages (JSON)','Registered Options Pages (UI)'=>'Registered Options Pages (UI)','Options Pages UI Enabled'=>'Options Pages UI Enabled','Registered Taxonomies (JSON)'=>'Registered Taxonomies (JSON)','Registered Taxonomies (UI)'=>'Registered Taxonomies (UI)','Registered Post Types (JSON)'=>'Registered Post Types (JSON)','Registered Post Types (UI)'=>'Registered Post Types (UI)','Post Types and Taxonomies Enabled'=>'Post Types and Taxonomies Enabled','Number of Third Party Fields by Field Type'=>'Number of Third Party Fields by Field Type','Number of Fields by Field Type'=>'Number of Fields by Field Type','Field Groups Enabled for GraphQL'=>'Field Groups Enabled for GraphQL','Field Groups Enabled for REST API'=>'Field Groups Enabled for REST API','Registered Field Groups (JSON)'=>'Registered Field Groups (JSON)','Registered Field Groups (PHP)'=>'Registered Field Groups (PHP)','Registered Field Groups (UI)'=>'Registered Field Groups (UI)','Active Plugins'=>'Active Plugins','Parent Theme'=>'Parent Theme','Active Theme'=>'Active Theme','Is Multisite'=>'Is Multisite','MySQL Version'=>'MySQL Version','WordPress Version'=>'WordPress Version','Subscription Expiry Date'=>'Subscription Expiry Date','License Status'=>'Licence Status','License Type'=>'Licence Type','Licensed URL'=>'Licensed URL','License Activated'=>'Licence Activated','Free'=>'Free','Plugin Type'=>'Plugin Type','Plugin Version'=>'Plugin Version','This section contains debug information about your ACF configuration which can be useful to provide to support.'=>'This section contains debug information about your ACF configuration which can be useful to provide to support.','An ACF Block on this page requires attention before you can save.'=>'An ACF Block on this page requires attention before you can save.','This data is logged as we detect values that have been changed during output. %1$sClear log and dismiss%2$s after escaping the values in your code. The notice will reappear if we detect changed values again.'=>'This data is logged as we detect values that have been changed during output. %1$sClear log and dismiss%2$s after escaping the values in your code. The notice will reappear if we detect changed values again.','Dismiss permanently'=>'Dismiss permanently','Instructions for content editors. Shown when submitting data.'=>'Instructions for content editors. Shown when submitting data.','Has no term selected'=>'Has no term selected','Has any term selected'=>'Has any term selected','Terms do not contain'=>'Terms do not contain','Terms contain'=>'Terms contain','Term is not equal to'=>'Term is not equal to','Term is equal to'=>'Term is equal to','Has no user selected'=>'Has no user selected','Has any user selected'=>'Has any user selected','Users do not contain'=>'Users do not contain','Users contain'=>'Users contain','User is not equal to'=>'User is not equal to','User is equal to'=>'User is equal to','Has no page selected'=>'Has no page selected','Has any page selected'=>'Has any page selected','Pages do not contain'=>'Pages do not contain','Pages contain'=>'Pages contain','Page is not equal to'=>'Page is not equal to','Page is equal to'=>'Page is equal to','Has no relationship selected'=>'Has no relationship selected','Has any relationship selected'=>'Has any relationship selected','Has no post selected'=>'Has no post selected','Has any post selected'=>'Has any post selected','Posts do not contain'=>'Posts do not contain','Posts contain'=>'Posts contain','Post is not equal to'=>'Post is not equal to','Post is equal to'=>'Post is equal to','Relationships do not contain'=>'Relationships do not contain','Relationships contain'=>'Relationships contain','Relationship is not equal to'=>'Relationship is not equal to','Relationship is equal to'=>'Relationship is equal to','The core ACF block binding source name for fields on the current pageACF Fields'=>'ACF Fields','ACF PRO Feature'=>'ACF PRO Feature','Renew PRO to Unlock'=>'Renew PRO to Unlock','Renew PRO License'=>'Renew PRO Licence','PRO fields cannot be edited without an active license.'=>'PRO fields cannot be edited without an active licence.','Please activate your ACF PRO license to edit field groups assigned to an ACF Block.'=>'Please activate your ACF PRO licence to edit field groups assigned to an ACF Block.','Please activate your ACF PRO license to edit this options page.'=>'Please activate your ACF PRO licence to edit this options page.','Returning escaped HTML values is only possible when format_value is also true. The field values have not been returned for security.'=>'Returning escaped HTML values is only possible when format_value is also true. The field values have not been returned for security.','Returning an escaped HTML value is only possible when format_value is also true. The field value has not been returned for security.'=>'Returning an escaped HTML value is only possible when format_value is also true. The field value has not been returned for security.','%1$s ACF now automatically escapes unsafe HTML when rendered by <code>the_field</code> or the ACF shortcode. We\'ve detected the output of some of your fields has been modified by this change, but this may not be a breaking change. %2$s.'=>'%1$s ACF now automatically escapes unsafe HTML when rendered by <code>the_field</code> or the ACF shortcode. We\'ve detected the output of some of your fields has been modified by this change, but this may not be a breaking change. %2$s.','Please contact your site administrator or developer for more details.'=>'Please contact your site administrator or developer for more details.','Learn&nbsp;more'=>'Learn&nbsp;more','Hide&nbsp;details'=>'Hide&nbsp;details','Show&nbsp;details'=>'Show&nbsp;details','%1$s (%2$s) - rendered via %3$s'=>'%1$s (%2$s) - rendered via %3$s','Renew ACF PRO License'=>'Renew ACF PRO Licence','Renew License'=>'Renew Licence','Manage License'=>'Manage Licence','\'High\' position not supported in the Block Editor'=>'\'High\' position not supported in the Block Editor','Upgrade to ACF PRO'=>'Upgrade to ACF PRO','ACF <a href="%s" target="_blank">options pages</a> are custom admin pages for managing global settings via fields. You can create multiple pages and sub-pages.'=>'ACF <a href="%s" target="_blank">options pages</a> are custom admin pages for managing global settings via fields. You can create multiple pages and sub-pages.','Add Options Page'=>'Add Options Page','In the editor used as the placeholder of the title.'=>'In the editor used as the placeholder of the title.','Title Placeholder'=>'Title Placeholder','4 Months Free'=>'4 Months Free','(Duplicated from %s)'=>'(Duplicated from %s)','Select Options Pages'=>'Select Options Pages','Duplicate taxonomy'=>'Duplicate taxonomy','Create taxonomy'=>'Create taxonomy','Duplicate post type'=>'Duplicate post type','Create post type'=>'Create post type','Link field groups'=>'Link field groups','Add fields'=>'Add fields','This Field'=>'This Field','ACF PRO'=>'ACF PRO','Feedback'=>'Feedback','Support'=>'Support','is developed and maintained by'=>'is developed and maintained by','Add this %s to the location rules of the selected field groups.'=>'Add this %s to the location rules of the selected field groups.','Enabling the bidirectional setting allows you to update a value in the target fields for each value selected for this field, adding or removing the Post ID, Taxonomy ID or User ID of the item being updated. For more information, please read the <a href="%s" target="_blank">documentation</a>.'=>'Enabling the bidirectional setting allows you to update a value in the target fields for each value selected for this field, adding or removing the Post ID, Taxonomy ID or User ID of the item being updated. For more information, please read the <a href="%s" target="_blank">documentation</a>.','Select field(s) to store the reference back to the item being updated. You may select this field. Target fields must be compatible with where this field is being displayed. For example, if this field is displayed on a Taxonomy, your target field should be of type Taxonomy'=>'Select field(s) to store the reference back to the item being updated. You may select this field. Target fields must be compatible with where this field is being displayed. For example, if this field is displayed on a Taxonomy, your target field should be of type Taxonomy','Target Field'=>'Target Field','Update a field on the selected values, referencing back to this ID'=>'Update a field on the selected values, referencing back to this ID','Bidirectional'=>'Bidirectional','%s Field'=>'%s Field','Select Multiple'=>'Select Multiple','WP Engine logo'=>'WP Engine logo','Lower case letters, underscores and dashes only, Max 32 characters.'=>'Lower case letters, underscores and dashes only, Max 32 characters.','The capability name for assigning terms of this taxonomy.'=>'The capability name for assigning terms of this taxonomy.','Assign Terms Capability'=>'Assign Terms Capability','The capability name for deleting terms of this taxonomy.'=>'The capability name for deleting terms of this taxonomy.','Delete Terms Capability'=>'Delete Terms Capability','The capability name for editing terms of this taxonomy.'=>'The capability name for editing terms of this taxonomy.','Edit Terms Capability'=>'Edit Terms Capability','The capability name for managing terms of this taxonomy.'=>'The capability name for managing terms of this taxonomy.','Manage Terms Capability'=>'Manage Terms Capability','Sets whether posts should be excluded from search results and taxonomy archive pages.'=>'Sets whether posts should be excluded from search results and taxonomy archive pages.','More Tools from WP Engine'=>'More Tools from WP Engine','Built for those that build with WordPress, by the team at %s'=>'Built for those that build with WordPress, by the team at %s','View Pricing & Upgrade'=>'View Pricing & Upgrade','Learn More'=>'Learn More','Speed up your workflow and develop better websites with features like ACF Blocks and Options Pages, and sophisticated field types like Repeater, Flexible Content, Clone, and Gallery.'=>'Speed up your workflow and develop better websites with features like ACF Blocks and Options Pages, and sophisticated field types like Repeater, Flexible Content, Clone, and Gallery.','Unlock Advanced Features and Build Even More with ACF PRO'=>'Unlock Advanced Features and Build Even More with ACF PRO','%s fields'=>'%s fields','No terms'=>'No terms','No post types'=>'No post types','No posts'=>'No posts','No taxonomies'=>'No taxonomies','No field groups'=>'No field groups','No fields'=>'No fields','No description'=>'No description','Any post status'=>'Any post status','This taxonomy key is already in use by another taxonomy registered outside of ACF and cannot be used.'=>'This taxonomy key is already in use by another taxonomy registered outside of ACF and cannot be used.','This taxonomy key is already in use by another taxonomy in ACF and cannot be used.'=>'This taxonomy key is already in use by another taxonomy in ACF and cannot be used.','The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.'=>'The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.','The taxonomy key must be under 32 characters.'=>'The taxonomy key must be under 32 characters.','No Taxonomies found in Trash'=>'No Taxonomies found in the bin','No Taxonomies found'=>'No Taxonomies found','Search Taxonomies'=>'Search Taxonomies','View Taxonomy'=>'View Taxonomy','New Taxonomy'=>'New Taxonomy','Edit Taxonomy'=>'Edit Taxonomy','Add New Taxonomy'=>'Add New Taxonomy','No Post Types found in Trash'=>'No Post Types found in the bin','No Post Types found'=>'No Post Types found','Search Post Types'=>'Search Post Types','View Post Type'=>'View Post Type','New Post Type'=>'New Post Type','Edit Post Type'=>'Edit Post Type','Add New Post Type'=>'Add New Post Type','This post type key is already in use by another post type registered outside of ACF and cannot be used.'=>'This post type key is already in use by another post type registered outside of ACF and cannot be used.','This post type key is already in use by another post type in ACF and cannot be used.'=>'This post type key is already in use by another post type in ACF and cannot be used.','This field must not be a WordPress <a href="%s" target="_blank">reserved term</a>.'=>'This field must not be a WordPress <a href="%s" target="_blank">reserved term</a>.','The post type key must only contain lower case alphanumeric characters, underscores or dashes.'=>'The post type key must only contain lower case alphanumeric characters, underscores or dashes.','The post type key must be under 20 characters.'=>'The post type key must be under 20 characters.','We do not recommend using this field in ACF Blocks.'=>'We do not recommend using this field in ACF Blocks.','Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing for a rich text-editing experience that also allows for multimedia content.'=>'Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing for a rich text-editing experience that also allows for multimedia content.','WYSIWYG Editor'=>'WYSIWYG Editor','Allows the selection of one or more users which can be used to create relationships between data objects.'=>'Allows the selection of one or more users which can be used to create relationships between data objects.','A text input specifically designed for storing web addresses.'=>'A text input specifically designed for storing web addresses.','URL'=>'URL','A toggle that allows you to pick a value of 1 or 0 (on or off, true or false, etc). Can be presented as a stylized switch or checkbox.'=>'A toggle that allows you to pick a value of 1 or 0 (on or off, true or false, etc). Can be presented as a stylised switch or checkbox.','An interactive UI for picking a time. The time format can be customized using the field settings.'=>'An interactive UI for picking a time. The time format can be customised using the field settings.','A basic textarea input for storing paragraphs of text.'=>'A basic textarea input for storing paragraphs of text.','A basic text input, useful for storing single string values.'=>'A basic text input, useful for storing single string values.','Allows the selection of one or more taxonomy terms based on the criteria and options specified in the fields settings.'=>'Allows the selection of one or more taxonomy terms based on the criteria and options specified in the fields settings.','Allows you to group fields into tabbed sections in the edit screen. Useful for keeping fields organized and structured.'=>'Allows you to group fields into tabbed sections in the edit screen. Useful for keeping fields organised and structured.','A dropdown list with a selection of choices that you specify.'=>'A dropdown list with a selection of choices that you specify.','A dual-column interface to select one or more posts, pages, or custom post type items to create a relationship with the item that you\'re currently editing. Includes options to search and filter.'=>'A dual-column interface to select one or more posts, pages, or custom post type items to create a relationship with the item that you\'re currently editing. Includes options to search and filter.','An input for selecting a numerical value within a specified range using a range slider element.'=>'An input for selecting a numerical value within a specified range using a range slider element.','A group of radio button inputs that allows the user to make a single selection from values that you specify.'=>'A group of radio button inputs that allows the user to make a single selection from values that you specify.','An interactive and customizable UI for picking one or many posts, pages or post type items with the option to search. '=>'An interactive and customisable UI for picking one or many posts, pages or post type items with the option to search. ','An input for providing a password using a masked field.'=>'An input for providing a password using a masked field.','Filter by Post Status'=>'Filter by Post Status','An interactive dropdown to select one or more posts, pages, custom post type items or archive URLs, with the option to search.'=>'An interactive dropdown to select one or more posts, pages, custom post type items or archive URLs, with the option to search.','An interactive component for embedding videos, images, tweets, audio and other content by making use of the native WordPress oEmbed functionality.'=>'An interactive component for embedding videos, images, tweets, audio and other content by making use of the native WordPress oEmbed functionality.','An input limited to numerical values.'=>'An input limited to numerical values.','Used to display a message to editors alongside other fields. Useful for providing additional context or instructions around your fields.'=>'Used to display a message to editors alongside other fields. Useful for providing additional context or instructions around your fields.','Allows you to specify a link and its properties such as title and target using the WordPress native link picker.'=>'Allows you to specify a link and its properties such as title and target using the WordPress native link picker.','Uses the native WordPress media picker to upload, or choose images.'=>'Uses the native WordPress media picker to upload, or choose images.','Provides a way to structure fields into groups to better organize the data and the edit screen.'=>'Provides a way to structure fields into groups to better organise the data and the edit screen.','An interactive UI for selecting a location using Google Maps. Requires a Google Maps API key and additional configuration to display correctly.'=>'An interactive UI for selecting a location using Google Maps. Requires a Google Maps API key and additional configuration to display correctly.','Uses the native WordPress media picker to upload, or choose files.'=>'Uses the native WordPress media picker to upload, or choose files.','A text input specifically designed for storing email addresses.'=>'A text input specifically designed for storing email addresses.','An interactive UI for picking a date and time. The date return format can be customized using the field settings.'=>'An interactive UI for picking a date and time. The date return format can be customised using the field settings.','An interactive UI for picking a date. The date return format can be customized using the field settings.'=>'An interactive UI for picking a date. The date return format can be customised using the field settings.','An interactive UI for selecting a color, or specifying a Hex value.'=>'An interactive UI for selecting a colour, or specifying a Hex value.','A group of checkbox inputs that allow the user to select one, or multiple values that you specify.'=>'A group of checkbox inputs that allow the user to select one, or multiple values that you specify.','A group of buttons with values that you specify, users can choose one option from the values provided.'=>'A group of buttons with values that you specify, users can choose one option from the values provided.','Allows you to group and organize custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.'=>'Allows you to group and organise custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.','This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.'=>'This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.','This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.'=>'This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.','This provides a simple, structured, layout-based editor. The Flexible Content field allows you to define, create and manage content with total control by using layouts and subfields to design the available blocks.'=>'This provides a simple, structured, layout-based editor. The Flexible Content field allows you to define, create and manage content with total control by using layouts and subfields to design the available blocks.','This allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields.'=>'This allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields.','nounClone'=>'Clone','PRO'=>'PRO','Advanced'=>'Advanced','JSON (newer)'=>'JSON (newer)','Original'=>'Original','Invalid post ID.'=>'Invalid post ID.','Invalid post type selected for review.'=>'Invalid post type selected for review.','More'=>'More','Tutorial'=>'Tutorial','Select Field'=>'Select Field','Try a different search term or browse %s'=>'Try a different search term or browse %s','Popular fields'=>'Popular fields','No search results for \'%s\''=>'No search results for \'%s\'','Search fields...'=>'Search fields...','Select Field Type'=>'Select Field Type','Popular'=>'Popular','Add Taxonomy'=>'Add Taxonomy','Create custom taxonomies to classify post type content'=>'Create custom taxonomies to classify post type content','Add Your First Taxonomy'=>'Add Your First Taxonomy','Hierarchical taxonomies can have descendants (like categories).'=>'Hierarchical taxonomies can have descendants (like categories).','Makes a taxonomy visible on the frontend and in the admin dashboard.'=>'Makes a taxonomy visible on the frontend and in the admin dashboard.','One or many post types that can be classified with this taxonomy.'=>'One or many post types that can be classified with this taxonomy.','genre'=>'genre','Genre'=>'Genre','Genres'=>'Genres','Optional custom controller to use instead of `WP_REST_Terms_Controller `.'=>'Optional custom controller to use instead of `WP_REST_Terms_Controller `.','Expose this post type in the REST API.'=>'Expose this post type in the REST API.','Customize the query variable name'=>'Customise the query variable name','Terms can be accessed using the non-pretty permalink, e.g., {query_var}={term_slug}.'=>'Terms can be accessed using the non-pretty permalink, e.g., {query_var}={term_slug}.','Parent-child terms in URLs for hierarchical taxonomies.'=>'Parent-child terms in URLs for hierarchical taxonomies.','Customize the slug used in the URL'=>'Customise the slug used in the URL','Permalinks for this taxonomy are disabled.'=>'Permalinks for this taxonomy are disabled.','Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be'=>'Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be','Taxonomy Key'=>'Taxonomy Key','Select the type of permalink to use for this taxonomy.'=>'Select the type of permalink to use for this taxonomy.','Display a column for the taxonomy on post type listing screens.'=>'Display a column for the taxonomy on post type listing screens.','Show Admin Column'=>'Show Admin Column','Show the taxonomy in the quick/bulk edit panel.'=>'Show the taxonomy in the quick/bulk edit panel.','Quick Edit'=>'Quick Edit','List the taxonomy in the Tag Cloud Widget controls.'=>'List the taxonomy in the Tag Cloud Widget controls.','Tag Cloud'=>'Tag Cloud','A PHP function name to be called for sanitizing taxonomy data saved from a meta box.'=>'A PHP function name to be called for sanitising taxonomy data saved from a meta box.','Meta Box Sanitization Callback'=>'Meta Box Sanitisation Callback','Register Meta Box Callback'=>'Register Meta Box Callback','No Meta Box'=>'No Meta Box','Custom Meta Box'=>'Custom Meta Box','Controls the meta box on the content editor screen. By default, the Categories meta box is shown for hierarchical taxonomies, and the Tags meta box is shown for non-hierarchical taxonomies.'=>'Controls the meta box on the content editor screen. By default, the Categories meta box is shown for hierarchical taxonomies, and the Tags meta box is shown for non-hierarchical taxonomies.','Meta Box'=>'Meta Box','Categories Meta Box'=>'Categories Meta Box','Tags Meta Box'=>'Tags Meta Box','A link to a tag'=>'A link to a tag','Describes a navigation link block variation used in the block editor.'=>'Describes a navigation link block variation used in the block editor.','A link to a %s'=>'A link to a %s','Tag Link'=>'Tag Link','Assigns a title for navigation link block variation used in the block editor.'=>'Assigns a title for navigation link block variation used in the block editor.','← Go to tags'=>'← Go to tags','Assigns the text used to link back to the main index after updating a term.'=>'Assigns the text used to link back to the main index after updating a term.','Back To Items'=>'Back To Items','← Go to %s'=>'← Go to %s','Tags list'=>'Tags list','Assigns text to the table hidden heading.'=>'Assigns text to the table hidden heading.','Tags list navigation'=>'Tags list navigation','Assigns text to the table pagination hidden heading.'=>'Assigns text to the table pagination hidden heading.','Filter by category'=>'Filter by category','Assigns text to the filter button in the posts lists table.'=>'Assigns text to the filter button in the posts lists table.','Filter By Item'=>'Filter By Item','Filter by %s'=>'Filter by %s','The description is not prominent by default; however, some themes may show it.'=>'The description is not prominent by default; however, some themes may show it.','Describes the Description field on the Edit Tags screen.'=>'Describes the Description field on the Edit Tags screen.','Description Field Description'=>'Description Field Description','Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band'=>'Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band','Describes the Parent field on the Edit Tags screen.'=>'Describes the Parent field on the Edit Tags screen.','Parent Field Description'=>'Parent Field Description','The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.'=>'The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.','Describes the Slug field on the Edit Tags screen.'=>'Describes the Slug field on the Edit Tags screen.','Slug Field Description'=>'Slug Field Description','The name is how it appears on your site'=>'The name is how it appears on your site','Describes the Name field on the Edit Tags screen.'=>'Describes the Name field on the Edit Tags screen.','Name Field Description'=>'Name Field Description','No tags'=>'No tags','Assigns the text displayed in the posts and media list tables when no tags or categories are available.'=>'Assigns the text displayed in the posts and media list tables when no tags or categories are available.','No Terms'=>'No Terms','No %s'=>'No %s','No tags found'=>'No tags found','Assigns the text displayed when clicking the \'choose from most used\' text in the taxonomy meta box when no tags are available, and assigns the text used in the terms list table when there are no items for a taxonomy.'=>'Assigns the text displayed when clicking the \'choose from most used\' text in the taxonomy meta box when no tags are available, and assigns the text used in the terms list table when there are no items for a taxonomy.','Not Found'=>'Not Found','Assigns text to the Title field of the Most Used tab.'=>'Assigns text to the Title field of the Most Used tab.','Most Used'=>'Most Used','Choose from the most used tags'=>'Choose from the most used tags','Assigns the \'choose from most used\' text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies.'=>'Assigns the \'choose from most used\' text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies.','Choose From Most Used'=>'Choose From Most Used','Choose from the most used %s'=>'Choose from the most used %s','Add or remove tags'=>'Add or remove tags','Assigns the add or remove items text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies'=>'Assigns the add or remove items text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies','Add Or Remove Items'=>'Add Or Remove Items','Add or remove %s'=>'Add or remove %s','Separate tags with commas'=>'Separate tags with commas','Assigns the separate item with commas text used in the taxonomy meta box. Only used on non-hierarchical taxonomies.'=>'Assigns the separate item with commas text used in the taxonomy meta box. Only used on non-hierarchical taxonomies.','Separate Items With Commas'=>'Separate Items With Commas','Separate %s with commas'=>'Separate %s with commas','Popular Tags'=>'Popular Tags','Assigns popular items text. Only used for non-hierarchical taxonomies.'=>'Assigns popular items text. Only used for non-hierarchical taxonomies.','Popular Items'=>'Popular Items','Popular %s'=>'Popular %s','Search Tags'=>'Search Tags','Assigns search items text.'=>'Assigns search items text.','Parent Category:'=>'Parent Category:','Assigns parent item text, but with a colon (:) added to the end.'=>'Assigns parent item text, but with a colon (:) added to the end.','Parent Item With Colon'=>'Parent Item With Colon','Parent Category'=>'Parent Category','Assigns parent item text. Only used on hierarchical taxonomies.'=>'Assigns parent item text. Only used on hierarchical taxonomies.','Parent Item'=>'Parent Item','Parent %s'=>'Parent %s','New Tag Name'=>'New Tag Name','Assigns the new item name text.'=>'Assigns the new item name text.','New Item Name'=>'New Item Name','New %s Name'=>'New %s Name','Add New Tag'=>'Add New Tag','Assigns the add new item text.'=>'Assigns the add new item text.','Update Tag'=>'Update Tag','Assigns the update item text.'=>'Assigns the update item text.','Update Item'=>'Update Item','Update %s'=>'Update %s','View Tag'=>'View Tag','In the admin bar to view term during editing.'=>'In the admin bar to view term during editing.','Edit Tag'=>'Edit Tag','At the top of the editor screen when editing a term.'=>'At the top of the editor screen when editing a term.','All Tags'=>'All Tags','Assigns the all items text.'=>'Assigns the all items text.','Assigns the menu name text.'=>'Assigns the menu name text.','Menu Label'=>'Menu Label','Active taxonomies are enabled and registered with WordPress.'=>'Active taxonomies are enabled and registered with WordPress.','A descriptive summary of the taxonomy.'=>'A descriptive summary of the taxonomy.','A descriptive summary of the term.'=>'A descriptive summary of the term.','Term Description'=>'Term Description','Single word, no spaces. Underscores and dashes allowed.'=>'Single word, no spaces. Underscores and dashes allowed.','Term Slug'=>'Term Slug','The name of the default term.'=>'The name of the default term.','Term Name'=>'Term Name','Create a term for the taxonomy that cannot be deleted. It will not be selected for posts by default.'=>'Create a term for the taxonomy that cannot be deleted. It will not be selected for posts by default.','Default Term'=>'Default Term','Whether terms in this taxonomy should be sorted in the order they are provided to `wp_set_object_terms()`.'=>'Whether terms in this taxonomy should be sorted in the order they are provided to `wp_set_object_terms()`.','Sort Terms'=>'Sort Terms','Add Post Type'=>'Add Post Type','Expand the functionality of WordPress beyond standard posts and pages with custom post types.'=>'Expand the functionality of WordPress beyond standard posts and pages with custom post types.','Add Your First Post Type'=>'Add Your First Post Type','I know what I\'m doing, show me all the options.'=>'I know what I\'m doing, show me all the options.','Advanced Configuration'=>'Advanced Configuration','Hierarchical post types can have descendants (like pages).'=>'Hierarchical post types can have descendants (like pages).','Hierarchical'=>'Hierarchical','Visible on the frontend and in the admin dashboard.'=>'Visible on the frontend and in the admin dashboard.','Public'=>'Public','movie'=>'movie','Lower case letters, underscores and dashes only, Max 20 characters.'=>'Lower case letters, underscores and dashes only, Max 20 characters.','Movie'=>'Movie','Singular Label'=>'Singular Label','Movies'=>'Movies','Plural Label'=>'Plural Label','Optional custom controller to use instead of `WP_REST_Posts_Controller`.'=>'Optional custom controller to use instead of `WP_REST_Posts_Controller`.','Controller Class'=>'Controller Class','The namespace part of the REST API URL.'=>'The namespace part of the REST API URL.','Namespace Route'=>'Namespace Route','The base URL for the post type REST API URLs.'=>'The base URL for the post type REST API URLs.','Base URL'=>'Base URL','Exposes this post type in the REST API. Required to use the block editor.'=>'Exposes this post type in the REST API. Required to use the block editor.','Show In REST API'=>'Show In REST API','Customize the query variable name.'=>'Customise the query variable name.','Query Variable'=>'Query Variable','No Query Variable Support'=>'No Query Variable Support','Custom Query Variable'=>'Custom Query Variable','Items can be accessed using the non-pretty permalink, eg. {post_type}={post_slug}.'=>'Items can be accessed using the non-pretty permalink, eg. {post_type}={post_slug}.','Query Variable Support'=>'Query Variable Support','URLs for an item and items can be accessed with a query string.'=>'URLs for an item and items can be accessed with a query string.','Publicly Queryable'=>'Publicly Queryable','Custom slug for the Archive URL.'=>'Custom slug for the Archive URL.','Archive Slug'=>'Archive Slug','Has an item archive that can be customized with an archive template file in your theme.'=>'Has an item archive that can be customised with an archive template file in your theme.','Archive'=>'Archive','Pagination support for the items URLs such as the archives.'=>'Pagination support for the items URLs such as the archives.','Pagination'=>'Pagination','RSS feed URL for the post type items.'=>'RSS feed URL for the post type items.','Feed URL'=>'Feed URL','Alters the permalink structure to add the `WP_Rewrite::$front` prefix to URLs.'=>'Alters the permalink structure to add the `WP_Rewrite::$front` prefix to URLs.','Front URL Prefix'=>'Front URL Prefix','Customize the slug used in the URL.'=>'Customise the slug used in the URL.','URL Slug'=>'URL Slug','Permalinks for this post type are disabled.'=>'Permalinks for this post type are disabled.','Rewrite the URL using a custom slug defined in the input below. Your permalink structure will be'=>'Rewrite the URL using a custom slug defined in the input below. Your permalink structure will be','No Permalink (prevent URL rewriting)'=>'No Permalink (prevent URL rewriting)','Custom Permalink'=>'Custom Permalink','Post Type Key'=>'Post Type Key','Rewrite the URL using the post type key as the slug. Your permalink structure will be'=>'Rewrite the URL using the post type key as the slug. Your permalink structure will be','Permalink Rewrite'=>'Permalink Rewrite','Delete items by a user when that user is deleted.'=>'Delete items by a user when that user is deleted.','Delete With User'=>'Delete With User','Allow the post type to be exported from \'Tools\' > \'Export\'.'=>'Allow the post type to be exported from \'Tools\' > \'Export\'.','Can Export'=>'Can Export','Optionally provide a plural to be used in capabilities.'=>'Optionally provide a plural to be used in capabilities.','Plural Capability Name'=>'Plural Capability Name','Choose another post type to base the capabilities for this post type.'=>'Choose another post type to base the capabilities for this post type.','Singular Capability Name'=>'Singular Capability Name','By default the capabilities of the post type will inherit the \'Post\' capability names, eg. edit_post, delete_posts. Enable to use post type specific capabilities, eg. edit_{singular}, delete_{plural}.'=>'By default the capabilities of the post type will inherit the \'Post\' capability names, eg. edit_post, delete_posts. Enable to use post type specific capabilities, eg. edit_{singular}, delete_{plural}.','Rename Capabilities'=>'Rename Capabilities','Exclude From Search'=>'Exclude From Search','Allow items to be added to menus in the \'Appearance\' > \'Menus\' screen. Must be turned on in \'Screen options\'.'=>'Allow items to be added to menus in the \'Appearance\' > \'Menus\' screen. Must be turned on in \'Screen options\'.','Appearance Menus Support'=>'Appearance Menus Support','Appears as an item in the \'New\' menu in the admin bar.'=>'Appears as an item in the \'New\' menu in the admin bar.','Show In Admin Bar'=>'Show In Admin Bar','Custom Meta Box Callback'=>'Custom Meta Box Callback','Menu Icon'=>'Menu Icon','The position in the sidebar menu in the admin dashboard.'=>'The position in the sidebar menu in the admin dashboard.','Menu Position'=>'Menu Position','By default the post type will get a new top level item in the admin menu. If an existing top level item is supplied here, the post type will be added as a submenu item under it.'=>'By default the post type will get a new top level item in the admin menu. If an existing top level item is supplied here, the post type will be added as a submenu item under it.','Admin Menu Parent'=>'Admin Menu Parent','Admin editor navigation in the sidebar menu.'=>'Admin editor navigation in the sidebar menu.','Show In Admin Menu'=>'Show In Admin Menu','Items can be edited and managed in the admin dashboard.'=>'Items can be edited and managed in the admin dashboard.','Show In UI'=>'Show In UI','A link to a post.'=>'A link to a post.','Description for a navigation link block variation.'=>'Description for a navigation link block variation.','Item Link Description'=>'Item Link Description','A link to a %s.'=>'A link to a %s.','Post Link'=>'Post Link','Title for a navigation link block variation.'=>'Title for a navigation link block variation.','Item Link'=>'Item Link','%s Link'=>'%s Link','Post updated.'=>'Post updated.','In the editor notice after an item is updated.'=>'In the editor notice after an item is updated.','Item Updated'=>'Item Updated','%s updated.'=>'%s updated.','Post scheduled.'=>'Post scheduled.','In the editor notice after scheduling an item.'=>'In the editor notice after scheduling an item.','Item Scheduled'=>'Item Scheduled','%s scheduled.'=>'%s scheduled.','Post reverted to draft.'=>'Post reverted to draft.','In the editor notice after reverting an item to draft.'=>'In the editor notice after reverting an item to draft.','Item Reverted To Draft'=>'Item Reverted To Draft','%s reverted to draft.'=>'%s reverted to draft.','Post published privately.'=>'Post published privately.','In the editor notice after publishing a private item.'=>'In the editor notice after publishing a private item.','Item Published Privately'=>'Item Published Privately','%s published privately.'=>'%s published privately.','Post published.'=>'Post published.','In the editor notice after publishing an item.'=>'In the editor notice after publishing an item.','Item Published'=>'Item Published','%s published.'=>'%s published.','Posts list'=>'Posts list','Used by screen readers for the items list on the post type list screen.'=>'Used by screen readers for the items list on the post type list screen.','Items List'=>'Items List','%s list'=>'%s list','Posts list navigation'=>'Posts list navigation','Used by screen readers for the filter list pagination on the post type list screen.'=>'Used by screen readers for the filter list pagination on the post type list screen.','Items List Navigation'=>'Items List Navigation','%s list navigation'=>'%s list navigation','Filter posts by date'=>'Filter posts by date','Used by screen readers for the filter by date heading on the post type list screen.'=>'Used by screen readers for the filter by date heading on the post type list screen.','Filter Items By Date'=>'Filter Items By Date','Filter %s by date'=>'Filter %s by date','Filter posts list'=>'Filter posts list','Used by screen readers for the filter links heading on the post type list screen.'=>'Used by screen readers for the filter links heading on the post type list screen.','Filter Items List'=>'Filter Items List','Filter %s list'=>'Filter %s list','In the media modal showing all media uploaded to this item.'=>'In the media modal showing all media uploaded to this item.','Uploaded To This Item'=>'Uploaded To This Item','Uploaded to this %s'=>'Uploaded to this %s','Insert into post'=>'Insert into post','As the button label when adding media to content.'=>'As the button label when adding media to content.','Insert Into Media Button'=>'Insert Into Media Button','Insert into %s'=>'Insert into %s','Use as featured image'=>'Use as featured image','As the button label for selecting to use an image as the featured image.'=>'As the button label for selecting to use an image as the featured image.','Use Featured Image'=>'Use Featured Image','Remove featured image'=>'Remove featured image','As the button label when removing the featured image.'=>'As the button label when removing the featured image.','Remove Featured Image'=>'Remove Featured Image','Set featured image'=>'Set featured image','As the button label when setting the featured image.'=>'As the button label when setting the featured image.','Set Featured Image'=>'Set Featured Image','Featured image'=>'Featured image','In the editor used for the title of the featured image meta box.'=>'In the editor used for the title of the featured image meta box.','Featured Image Meta Box'=>'Featured Image Meta Box','Post Attributes'=>'Post Attributes','In the editor used for the title of the post attributes meta box.'=>'In the editor used for the title of the post attributes meta box.','Attributes Meta Box'=>'Attributes Meta Box','%s Attributes'=>'%s Attributes','Post Archives'=>'Post Archives','Adds \'Post Type Archive\' items with this label to the list of posts shown when adding items to an existing menu in a CPT with archives enabled. Only appears when editing menus in \'Live Preview\' mode and a custom archive slug has been provided.'=>'Adds \'Post Type Archive\' items with this label to the list of posts shown when adding items to an existing menu in a post type with archives enabled. Only appears when editing menus in \'Live Preview\' mode and a custom archive slug has been provided.','Archives Nav Menu'=>'Archives Nav Menu','%s Archives'=>'%s Archives','No posts found in Trash'=>'No posts found in the bin','At the top of the post type list screen when there are no posts in the trash.'=>'At the top of the post type list screen when there are no posts in the bin.','No Items Found in Trash'=>'No Items Found in the bin','No %s found in Trash'=>'No %s found in the bin','No posts found'=>'No posts found','At the top of the post type list screen when there are no posts to display.'=>'At the top of the post type list screen when there are no posts to display.','No Items Found'=>'No Items Found','No %s found'=>'No %s found','Search Posts'=>'Search Posts','At the top of the items screen when searching for an item.'=>'At the top of the items screen when searching for an item.','Search Items'=>'Search Items','Search %s'=>'Search %s','Parent Page:'=>'Parent Page:','For hierarchical types in the post type list screen.'=>'For hierarchical types in the post type list screen.','Parent Item Prefix'=>'Parent Item Prefix','Parent %s:'=>'Parent %s:','New Post'=>'New Post','New Item'=>'New Item','New %s'=>'New %s','Add New Post'=>'Add New Post','At the top of the editor screen when adding a new item.'=>'At the top of the editor screen when adding a new item.','Add New Item'=>'Add New Item','Add New %s'=>'Add New %s','View Posts'=>'View Posts','Appears in the admin bar in the \'All Posts\' view, provided the post type supports archives and the home page is not an archive of that post type.'=>'Appears in the admin bar in the \'All Posts\' view, provided the post type supports archives and the home page is not an archive of that post type.','View Items'=>'View Items','View Post'=>'View Post','In the admin bar to view item when editing it.'=>'In the admin bar to view item when editing it.','View Item'=>'View Item','View %s'=>'View %s','Edit Post'=>'Edit Post','At the top of the editor screen when editing an item.'=>'At the top of the editor screen when editing an item.','Edit Item'=>'Edit Item','Edit %s'=>'Edit %s','All Posts'=>'All Posts','In the post type submenu in the admin dashboard.'=>'In the post type submenu in the admin dashboard.','All Items'=>'All Items','All %s'=>'All %s','Admin menu name for the post type.'=>'Admin menu name for the post type.','Menu Name'=>'Menu Name','Regenerate all labels using the Singular and Plural labels'=>'Regenerate all labels using the Singular and Plural labels','Regenerate'=>'Regenerate','Active post types are enabled and registered with WordPress.'=>'Active post types are enabled and registered with WordPress.','A descriptive summary of the post type.'=>'A descriptive summary of the post type.','Add Custom'=>'Add Custom','Enable various features in the content editor.'=>'Enable various features in the content editor.','Post Formats'=>'Post Formats','Editor'=>'Editor','Trackbacks'=>'Trackbacks','Select existing taxonomies to classify items of the post type.'=>'Select existing taxonomies to classify items of the post type.','Browse Fields'=>'Browse Fields','Nothing to import'=>'Nothing to import','. The Custom Post Type UI plugin can be deactivated.'=>'. The Custom Post Type UI plugin can be deactivated.','Imported %d item from Custom Post Type UI -'=>'Imported %d item from Custom Post Type UI -' . "\0" . 'Imported %d items from Custom Post Type UI -','Failed to import taxonomies.'=>'Failed to import taxonomies.','Failed to import post types.'=>'Failed to import post types.','Nothing from Custom Post Type UI plugin selected for import.'=>'Nothing from Custom Post Type UI plugin selected for import.','Imported 1 item'=>'Imported 1 item' . "\0" . 'Imported %s items','Importing a Post Type or Taxonomy with the same key as one that already exists will overwrite the settings for the existing Post Type or Taxonomy with those of the import.'=>'Importing a Post Type or Taxonomy with the same key as one that already exists will overwrite the settings for the existing Post Type or Taxonomy with those of the import.','Import from Custom Post Type UI'=>'Import from Custom Post Type UI','The following code can be used to register a local version of the selected items. Storing field groups, post types, or taxonomies locally can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme\'s functions.php file or include it within an external file, then deactivate or delete the items from the ACF admin.'=>'The following code can be used to register a local version of the selected items. Storing field groups, post types, or taxonomies locally can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme\'s functions.php file or include it within an external file, then deactivate or delete the items from the ACF admin.','Export - Generate PHP'=>'Export - Generate PHP','Export'=>'Export','Select Taxonomies'=>'Select Taxonomies','Select Post Types'=>'Select Post Types','Exported 1 item.'=>'Exported 1 item.' . "\0" . 'Exported %s items.','Category'=>'Category','Tag'=>'Tag','%s taxonomy created'=>'%s taxonomy created','%s taxonomy updated'=>'%s taxonomy updated','Taxonomy draft updated.'=>'Taxonomy draft updated.','Taxonomy scheduled for.'=>'Taxonomy scheduled for.','Taxonomy submitted.'=>'Taxonomy submitted.','Taxonomy saved.'=>'Taxonomy saved.','Taxonomy deleted.'=>'Taxonomy deleted.','Taxonomy updated.'=>'Taxonomy updated.','This taxonomy could not be registered because its key is in use by another taxonomy registered by another plugin or theme.'=>'This taxonomy could not be registered because its key is in use by another taxonomy registered by another plugin or theme.','Taxonomy synchronized.'=>'Taxonomy synchronised.' . "\0" . '%s taxonomies synchronised.','Taxonomy duplicated.'=>'Taxonomy duplicated.' . "\0" . '%s taxonomies duplicated.','Taxonomy deactivated.'=>'Taxonomy deactivated.' . "\0" . '%s taxonomies deactivated.','Taxonomy activated.'=>'Taxonomy activated.' . "\0" . '%s taxonomies activated.','Terms'=>'Terms','Post type synchronized.'=>'Post type synchronised.' . "\0" . '%s post types synchronised.','Post type duplicated.'=>'Post type duplicated.' . "\0" . '%s post types duplicated.','Post type deactivated.'=>'Post type deactivated.' . "\0" . '%s post types deactivated.','Post type activated.'=>'Post type activated.' . "\0" . '%s post types activated.','Post Types'=>'Post Types','Advanced Settings'=>'Advanced Settings','Basic Settings'=>'Basic Settings','This post type could not be registered because its key is in use by another post type registered by another plugin or theme.'=>'This post type could not be registered because its key is in use by another post type registered by another plugin or theme.','Pages'=>'Pages','Link Existing Field Groups'=>'Link Existing Field Groups','%s post type created'=>'%s post type created','Add fields to %s'=>'Add fields to %s','%s post type updated'=>'%s post type updated','Post type draft updated.'=>'Post type draft updated.','Post type scheduled for.'=>'Post type scheduled for.','Post type submitted.'=>'Post type submitted.','Post type saved.'=>'Post type saved.','Post type updated.'=>'Post type updated.','Post type deleted.'=>'Post type deleted.','Type to search...'=>'Type to search...','PRO Only'=>'PRO Only','Field groups linked successfully.'=>'Field groups linked successfully.','Import Post Types and Taxonomies registered with Custom Post Type UI and manage them with ACF. <a href="%s">Get Started</a>.'=>'Import Post Types and Taxonomies registered with Custom Post Type UI and manage them with ACF. <a href="%s">Get Started</a>.','ACF'=>'ACF','taxonomy'=>'taxonomy','post type'=>'post type','Done'=>'Done','Field Group(s)'=>'Field Group(s)','Select one or many field groups...'=>'Select one or many field groups...','Please select the field groups to link.'=>'Please select the field groups to link.','Field group linked successfully.'=>'Field group linked successfully.' . "\0" . 'Field groups linked successfully.','post statusRegistration Failed'=>'Registration Failed','This item could not be registered because its key is in use by another item registered by another plugin or theme.'=>'This item could not be registered because its key is in use by another item registered by another plugin or theme.','REST API'=>'REST API','Permissions'=>'Permissions','URLs'=>'URLs','Visibility'=>'Visibility','Labels'=>'Labels','Field Settings Tabs'=>'Field Settings Tabs','https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields'=>'https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields','[ACF shortcode value disabled for preview]'=>'[ACF shortcode value disabled for preview]','Close Modal'=>'Close Modal','Field moved to other group'=>'Field moved to other group','Close modal'=>'Close modal','Start a new group of tabs at this tab.'=>'Start a new group of tabs at this tab.','New Tab Group'=>'New Tab Group','Use a stylized checkbox using select2'=>'Use a stylised checkbox using select2','Save Other Choice'=>'Save Other Choice','Allow Other Choice'=>'Allow Other Choice','Add Toggle All'=>'Add Toggle All','Save Custom Values'=>'Save Custom Values','Allow Custom Values'=>'Allow Custom Values','Checkbox custom values cannot be empty. Uncheck any empty values.'=>'Checkbox custom values cannot be empty. Uncheck any empty values.','Updates'=>'Updates','Advanced Custom Fields logo'=>'Advanced Custom Fields logo','Save Changes'=>'Save Changes','Field Group Title'=>'Field Group Title','Add title'=>'Add title','New to ACF? Take a look at our <a href="%s" target="_blank">getting started guide</a>.'=>'New to ACF? Take a look at our <a href="%s" target="_blank">getting started guide</a>.','Add Field Group'=>'Add Field Group','ACF uses <a href="%s" target="_blank">field groups</a> to group custom fields together, and then attach those fields to edit screens.'=>'ACF uses <a href="%s" target="_blank">field groups</a> to group custom fields together, and then attach those fields to edit screens.','Add Your First Field Group'=>'Add Your First Field Group','Options Pages'=>'Options Pages','ACF Blocks'=>'ACF Blocks','Gallery Field'=>'Gallery Field','Flexible Content Field'=>'Flexible Content Field','Repeater Field'=>'Repeater Field','Unlock Extra Features with ACF PRO'=>'Unlock Extra Features with ACF PRO','Delete Field Group'=>'Delete Field Group','Created on %1$s at %2$s'=>'Created on %1$s at %2$s','Group Settings'=>'Group Settings','Location Rules'=>'Location Rules','Choose from over 30 field types. <a href="%s" target="_blank">Learn more</a>.'=>'Choose from over 30 field types. <a href="%s" target="_blank">Learn more</a>.','Get started creating new custom fields for your posts, pages, custom post types and other WordPress content.'=>'Get started creating new custom fields for your posts, pages, custom post types and other WordPress content.','Add Your First Field'=>'Add Your First Field','#'=>'#','Add Field'=>'Add Field','Presentation'=>'Presentation','Validation'=>'Validation','General'=>'General','Import JSON'=>'Import JSON','Export As JSON'=>'Export As JSON','Field group deactivated.'=>'Field group deactivated.' . "\0" . '%s field groups deactivated.','Field group activated.'=>'Field group activated.' . "\0" . '%s field groups activated.','Deactivate'=>'Deactivate','Deactivate this item'=>'Deactivate this item','Activate'=>'Activate','Activate this item'=>'Activate this item','Move field group to trash?'=>'Move field group to trash?','post statusInactive'=>'Inactive','WP Engine'=>'WP Engine','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.'=>'Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.'=>'Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.','%1$s must have a user with the %2$s role.'=>'%1$s must have a user with the %2$s role.' . "\0" . '%1$s must have a user with one of the following roles: %2$s','%1$s must have a valid user ID.'=>'%1$s must have a valid user ID.','Invalid request.'=>'Invalid request.','%1$s is not one of %2$s'=>'%1$s is not one of %2$s','%1$s must have term %2$s.'=>'%1$s must have term %2$s.' . "\0" . '%1$s must have one of the following terms: %2$s','%1$s must be of post type %2$s.'=>'%1$s must be of post type %2$s.' . "\0" . '%1$s must be of one of the following post types: %2$s','%1$s must have a valid post ID.'=>'%1$s must have a valid post ID.','%s requires a valid attachment ID.'=>'%s requires a valid attachment ID.','Show in REST API'=>'Show in REST API','Enable Transparency'=>'Enable Transparency','RGBA Array'=>'RGBA Array','RGBA String'=>'RGBA String','Hex String'=>'Hex String','Upgrade to PRO'=>'Upgrade to PRO','post statusActive'=>'Active','\'%s\' is not a valid email address'=>'\'%s\' is not a valid email address','Color value'=>'Colour value','Select default color'=>'Select default colour','Clear color'=>'Clear colour','Blocks'=>'Blocks','Options'=>'Options','Users'=>'Users','Menu items'=>'Menu items','Widgets'=>'Widgets','Attachments'=>'Attachments','Taxonomies'=>'Taxonomies','Posts'=>'Posts','Last updated: %s'=>'Last updated: %s','Sorry, this post is unavailable for diff comparison.'=>'Sorry, this post is unavailable for diff comparison.','Invalid field group parameter(s).'=>'Invalid field group parameter(s).','Awaiting save'=>'Awaiting save','Saved'=>'Saved','Import'=>'Import','Review changes'=>'Review changes','Located in: %s'=>'Located in: %s','Located in plugin: %s'=>'Located in plugin: %s','Located in theme: %s'=>'Located in theme: %s','Various'=>'Various','Sync changes'=>'Sync changes','Loading diff'=>'Loading diff','Review local JSON changes'=>'Review local JSON changes','Visit website'=>'Visit website','View details'=>'View details','Version %s'=>'Version %s','Information'=>'Information','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.','<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.'=>'<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:','Help & Support'=>'Help & Support','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'Please use the Help & Support tab to get in touch should you find yourself requiring assistance.','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarise yourself with the plugin\'s philosophy and best practises.','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'The Advanced Custom Fields plugin provides a visual form builder to customise WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.','Overview'=>'Overview','Location type "%s" is already registered.'=>'Location type "%s" is already registered.','Class "%s" does not exist.'=>'Class "%s" does not exist.','Invalid nonce.'=>'Invalid nonce.','Error loading field.'=>'Error loading field.','<strong>Error</strong>: %s'=>'<strong>Error</strong>: %s','Widget'=>'Widget','User Role'=>'User Role','Comment'=>'Comment','Post Format'=>'Post Format','Menu Item'=>'Menu Item','Post Status'=>'Post Status','Menus'=>'Menus','Menu Locations'=>'Menu Locations','Menu'=>'Menu','Post Taxonomy'=>'Post Taxonomy','Child Page (has parent)'=>'Child Page (has parent)','Parent Page (has children)'=>'Parent Page (has children)','Top Level Page (no parent)'=>'Top Level Page (no parent)','Posts Page'=>'Posts Page','Front Page'=>'Front Page','Page Type'=>'Page Type','Viewing back end'=>'Viewing back end','Viewing front end'=>'Viewing front end','Logged in'=>'Logged in','Current User'=>'Current User','Page Template'=>'Page Template','Register'=>'Register','Add / Edit'=>'Add / Edit','User Form'=>'User Form','Page Parent'=>'Page Parent','Super Admin'=>'Super Admin','Current User Role'=>'Current User Role','Default Template'=>'Default Template','Post Template'=>'Post Template','Post Category'=>'Post Category','All %s formats'=>'All %s formats','Attachment'=>'Attachment','%s value is required'=>'%s value is required','Show this field if'=>'Show this field if','Conditional Logic'=>'Conditional Logic','and'=>'and','Local JSON'=>'Local JSON','Clone Field'=>'Clone Field','Please also check all premium add-ons (%s) are updated to the latest version.'=>'Please also check all premium add-ons (%s) are updated to the latest version.','This version contains improvements to your database and requires an upgrade.'=>'This version contains improvements to your database and requires an upgrade.','Thank you for updating to %1$s v%2$s!'=>'Thank you for updating to %1$s v%2$s!','Database Upgrade Required'=>'Database Upgrade Required','Options Page'=>'Options Page','Gallery'=>'Gallery','Flexible Content'=>'Flexible Content','Repeater'=>'Repeater','Back to all tools'=>'Back to all tools','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'<b>Select</b> items to <b>hide</b> them from the edit screen.','Hide on screen'=>'Hide on screen','Send Trackbacks'=>'Send Trackbacks','Tags'=>'Tags','Categories'=>'Categories','Page Attributes'=>'Page Attributes','Format'=>'Format','Author'=>'Author','Slug'=>'Slug','Revisions'=>'Revisions','Comments'=>'Comments','Discussion'=>'Discussion','Excerpt'=>'Excerpt','Content Editor'=>'Content Editor','Permalink'=>'Permalink','Shown in field group list'=>'Shown in field group list','Field groups with a lower order will appear first'=>'Field groups with a lower order will appear first','Order No.'=>'Order No.','Below fields'=>'Below fields','Below labels'=>'Below labels','Instruction Placement'=>'Instruction Placement','Label Placement'=>'Label Placement','Side'=>'Side','Normal (after content)'=>'Normal (after content)','High (after title)'=>'High (after title)','Position'=>'Position','Seamless (no metabox)'=>'Seamless (no metabox)','Standard (WP metabox)'=>'Standard (WP metabox)','Style'=>'Style','Type'=>'Type','Key'=>'Key','Order'=>'Order','Close Field'=>'Close Field','id'=>'id','class'=>'class','width'=>'width','Wrapper Attributes'=>'Wrapper Attributes','Required'=>'Required','Instructions'=>'Instructions','Field Type'=>'Field Type','Single word, no spaces. Underscores and dashes allowed'=>'Single word, no spaces. Underscores and dashes allowed','Field Name'=>'Field Name','This is the name which will appear on the EDIT page'=>'This is the name which will appear on the EDIT page','Field Label'=>'Field Label','Delete'=>'Delete','Delete field'=>'Delete field','Move'=>'Move','Move field to another group'=>'Move field to another group','Duplicate field'=>'Duplicate field','Edit field'=>'Edit field','Drag to reorder'=>'Drag to reorder','Show this field group if'=>'Show this field group if','No updates available.'=>'No updates available.','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'Database upgrade complete. <a href="%s">See what\'s new</a>','Reading upgrade tasks...'=>'Reading upgrade tasks...','Upgrade failed.'=>'Upgrade failed.','Upgrade complete.'=>'Upgrade complete.','Upgrading data to version %s'=>'Upgrading data to version %s','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?','Please select at least one site to upgrade.'=>'Please select at least one site to upgrade.','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'Database Upgrade complete. <a href="%s">Return to network dashboard</a>','Site is up to date'=>'Site is up to date','Site requires database upgrade from %1$s to %2$s'=>'Site requires database upgrade from %1$s to %2$s','Site'=>'Site','Upgrade Sites'=>'Upgrade Sites','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'The following sites require a DB upgrade. Check the ones you want to update and then click %s.','Add rule group'=>'Add rule group','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'Create a set of rules to determine which edit screens will use these advanced custom fields','Rules'=>'Rules','Copied'=>'Copied','Copy to clipboard'=>'Copy to clipboard','Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.'=>'Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.','Select Field Groups'=>'Select Field Groups','No field groups selected'=>'No field groups selected','Generate PHP'=>'Generate PHP','Export Field Groups'=>'Export Field Groups','Import file empty'=>'Import file empty','Incorrect file type'=>'Incorrect file type','Error uploading file. Please try again'=>'Error uploading file. Please try again','Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.'=>'Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.','Import Field Groups'=>'Import Field Groups','Sync'=>'Sync','Select %s'=>'Select %s','Duplicate'=>'Duplicate','Duplicate this item'=>'Duplicate this item','Supports'=>'Supports','Documentation'=>'Documentation','Description'=>'Description','Sync available'=>'Sync available','Field group synchronized.'=>'Field group synchronised.' . "\0" . '%s field groups synchronised.','Field group duplicated.'=>'Field group duplicated.' . "\0" . '%s field groups duplicated.','Active <span class="count">(%s)</span>'=>'Active <span class="count">(%s)</span>' . "\0" . 'Active <span class="count">(%s)</span>','Review sites & upgrade'=>'Review sites & upgrade','Upgrade Database'=>'Upgrade Database','Custom Fields'=>'Custom Fields','Move Field'=>'Move Field','Please select the destination for this field'=>'Please select the destination for this field','The %1$s field can now be found in the %2$s field group'=>'The %1$s field can now be found in the %2$s field group','Move Complete.'=>'Move Complete.','Active'=>'Active','Field Keys'=>'Field Keys','Settings'=>'Settings','Location'=>'Location','Null'=>'Null','copy'=>'copy','(this field)'=>'(this field)','Checked'=>'Checked','Move Custom Field'=>'Move Custom Field','No toggle fields available'=>'No toggle fields available','Field group title is required'=>'Field group title is required','This field cannot be moved until its changes have been saved'=>'This field cannot be moved until its changes have been saved','The string "field_" may not be used at the start of a field name'=>'The string "field_" may not be used at the start of a field name','Field group draft updated.'=>'Field group draft updated.','Field group scheduled for.'=>'Field group scheduled for.','Field group submitted.'=>'Field group submitted.','Field group saved.'=>'Field group saved.','Field group published.'=>'Field group published.','Field group deleted.'=>'Field group deleted.','Field group updated.'=>'Field group updated.','Tools'=>'Tools','is not equal to'=>'is not equal to','is equal to'=>'is equal to','Forms'=>'Forms','Page'=>'Page','Post'=>'Post','Relational'=>'Relational','Choice'=>'Choice','Basic'=>'Basic','Unknown'=>'Unknown','Field type does not exist'=>'Field type does not exist','Spam Detected'=>'Spam Detected','Post updated'=>'Post updated','Update'=>'Update','Validate Email'=>'Validate Email','Content'=>'Content','Title'=>'Title','Edit field group'=>'Edit field group','Selection is less than'=>'Selection is less than','Selection is greater than'=>'Selection is greater than','Value is less than'=>'Value is less than','Value is greater than'=>'Value is greater than','Value contains'=>'Value contains','Value matches pattern'=>'Value matches pattern','Value is not equal to'=>'Value is not equal to','Value is equal to'=>'Value is equal to','Has no value'=>'Has no value','Has any value'=>'Has any value','Cancel'=>'Cancel','Are you sure?'=>'Are you sure?','%d fields require attention'=>'%d fields require attention','1 field requires attention'=>'1 field requires attention','Validation failed'=>'Validation failed','Validation successful'=>'Validation successful','Restricted'=>'Restricted','Collapse Details'=>'Collapse Details','Expand Details'=>'Expand Details','Uploaded to this post'=>'Uploaded to this post','verbUpdate'=>'Update','verbEdit'=>'Edit','The changes you made will be lost if you navigate away from this page'=>'The changes you made will be lost if you navigate away from this page','File type must be %s.'=>'File type must be %s.','or'=>'or','File size must not exceed %s.'=>'File size must not exceed %s.','File size must be at least %s.'=>'File size must be at least %s.','Image height must not exceed %dpx.'=>'Image height must not exceed %dpx.','Image height must be at least %dpx.'=>'Image height must be at least %dpx.','Image width must not exceed %dpx.'=>'Image width must not exceed %dpx.','Image width must be at least %dpx.'=>'Image width must be at least %dpx.','(no title)'=>'(no title)','Full Size'=>'Full Size','Large'=>'Large','Medium'=>'Medium','Thumbnail'=>'Thumbnail','(no label)'=>'(no label)','Sets the textarea height'=>'Sets the textarea height','Rows'=>'Rows','Text Area'=>'Text Area','Prepend an extra checkbox to toggle all choices'=>'Prepend an extra checkbox to toggle all choices','Save \'custom\' values to the field\'s choices'=>'Save \'custom\' values to the field\'s choices','Allow \'custom\' values to be added'=>'Allow \'custom\' values to be added','Add new choice'=>'Add new choice','Toggle All'=>'Toggle All','Allow Archives URLs'=>'Allow Archive URLs','Archives'=>'Archives','Page Link'=>'Page Link','Add'=>'Add','Name'=>'Name','%s added'=>'%s added','%s already exists'=>'%s already exists','User unable to add new %s'=>'User unable to add new %s','Term ID'=>'Term ID','Term Object'=>'Term Object','Load value from posts terms'=>'Load value from posts terms','Load Terms'=>'Load Terms','Connect selected terms to the post'=>'Connect selected terms to the post','Save Terms'=>'Save Terms','Allow new terms to be created whilst editing'=>'Allow new terms to be created whilst editing','Create Terms'=>'Create Terms','Radio Buttons'=>'Radio Buttons','Single Value'=>'Single Value','Multi Select'=>'Multi Select','Checkbox'=>'Checkbox','Multiple Values'=>'Multiple Values','Select the appearance of this field'=>'Select the appearance of this field','Appearance'=>'Appearance','Select the taxonomy to be displayed'=>'Select the taxonomy to be displayed','No TermsNo %s'=>'No %s','Value must be equal to or lower than %d'=>'Value must be equal to or lower than %d','Value must be equal to or higher than %d'=>'Value must be equal to or higher than %d','Value must be a number'=>'Value must be a number','Number'=>'Number','Save \'other\' values to the field\'s choices'=>'Save \'other\' values to the field\'s choices','Add \'other\' choice to allow for custom values'=>'Add \'other\' choice to allow for custom values','Other'=>'Other','Radio Button'=>'Radio Button','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'Define an endpoint for the previous accordion to stop. This accordion will not be visible.','Allow this accordion to open without closing others.'=>'Allow this accordion to open without closing others.','Multi-Expand'=>'Multi-Expand','Display this accordion as open on page load.'=>'Display this accordion as open on page load.','Open'=>'Open','Accordion'=>'Accordion','Restrict which files can be uploaded'=>'Restrict which files can be uploaded','File ID'=>'File ID','File URL'=>'File URL','File Array'=>'File Array','Add File'=>'Add File','No file selected'=>'No file selected','File name'=>'File name','Update File'=>'Update File','Edit File'=>'Edit File','Select File'=>'Select File','File'=>'File','Password'=>'Password','Specify the value returned'=>'Specify the value returned','Use AJAX to lazy load choices?'=>'Use AJAX to lazy load choices?','Enter each default value on a new line'=>'Enter each default value on a new line','verbSelect'=>'Select','Select2 JS load_failLoading failed'=>'Loading failed','Select2 JS searchingSearching&hellip;'=>'Searching&hellip;','Select2 JS load_moreLoading more results&hellip;'=>'Loading more results&hellip;','Select2 JS selection_too_long_nYou can only select %d items'=>'You can only select %d items','Select2 JS selection_too_long_1You can only select 1 item'=>'You can only select 1 item','Select2 JS input_too_long_nPlease delete %d characters'=>'Please delete %d characters','Select2 JS input_too_long_1Please delete 1 character'=>'Please delete 1 character','Select2 JS input_too_short_nPlease enter %d or more characters'=>'Please enter %d or more characters','Select2 JS input_too_short_1Please enter 1 or more characters'=>'Please enter 1 or more characters','Select2 JS matches_0No matches found'=>'No matches found','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'%d results are available, use up and down arrow keys to navigate.','Select2 JS matches_1One result is available, press enter to select it.'=>'One result is available, press enter to select it.','nounSelect'=>'Select','User ID'=>'User ID','User Object'=>'User Object','User Array'=>'User Array','All user roles'=>'All user roles','Filter by Role'=>'Filter by Role','User'=>'User','Separator'=>'Separator','Select Color'=>'Select Colour','Default'=>'Default','Clear'=>'Clear','Color Picker'=>'Colour Picker','Date Time Picker JS pmTextShortP'=>'P','Date Time Picker JS pmTextPM'=>'PM','Date Time Picker JS amTextShortA'=>'A','Date Time Picker JS amTextAM'=>'AM','Date Time Picker JS selectTextSelect'=>'Select','Date Time Picker JS closeTextDone'=>'Done','Date Time Picker JS currentTextNow'=>'Now','Date Time Picker JS timezoneTextTime Zone'=>'Time Zone','Date Time Picker JS microsecTextMicrosecond'=>'Microsecond','Date Time Picker JS millisecTextMillisecond'=>'Millisecond','Date Time Picker JS secondTextSecond'=>'Second','Date Time Picker JS minuteTextMinute'=>'Minute','Date Time Picker JS hourTextHour'=>'Hour','Date Time Picker JS timeTextTime'=>'Time','Date Time Picker JS timeOnlyTitleChoose Time'=>'Choose Time','Date Time Picker'=>'Date Time Picker','Endpoint'=>'Endpoint','Left aligned'=>'Left aligned','Top aligned'=>'Top aligned','Placement'=>'Placement','Tab'=>'Tab','Value must be a valid URL'=>'Value must be a valid URL','Link URL'=>'Link URL','Link Array'=>'Link Array','Opens in a new window/tab'=>'Opens in a new window/tab','Select Link'=>'Select Link','Link'=>'Link','Email'=>'Email','Step Size'=>'Step Size','Maximum Value'=>'Maximum Value','Minimum Value'=>'Minimum Value','Range'=>'Range','Both (Array)'=>'Both (Array)','Label'=>'Label','Value'=>'Value','Vertical'=>'Vertical','Horizontal'=>'Horizontal','red : Red'=>'red : Red','For more control, you may specify both a value and label like this:'=>'For more control, you may specify both a value and label like this:','Enter each choice on a new line.'=>'Enter each choice on a new line.','Choices'=>'Choices','Button Group'=>'Button Group','Allow Null'=>'Allow Null','Parent'=>'Parent','TinyMCE will not be initialized until field is clicked'=>'TinyMCE will not be initialised until field is clicked','Delay Initialization'=>'Delay Initialisation','Show Media Upload Buttons'=>'Show Media Upload Buttons','Toolbar'=>'Toolbar','Text Only'=>'Text Only','Visual Only'=>'Visual Only','Visual & Text'=>'Visual and Text','Tabs'=>'Tabs','Click to initialize TinyMCE'=>'Click to initialise TinyMCE','Name for the Text editor tab (formerly HTML)Text'=>'Text','Visual'=>'Visual','Value must not exceed %d characters'=>'Value must not exceed %d characters','Leave blank for no limit'=>'Leave blank for no limit','Character Limit'=>'Character Limit','Appears after the input'=>'Appears after the input','Append'=>'Append','Appears before the input'=>'Appears before the input','Prepend'=>'Prepend','Appears within the input'=>'Appears within the input','Placeholder Text'=>'Placeholder Text','Appears when creating a new post'=>'Appears when creating a new post','Text'=>'Text','%1$s requires at least %2$s selection'=>'%1$s requires at least %2$s selection' . "\0" . '%1$s requires at least %2$s selections','Post ID'=>'Post ID','Post Object'=>'Post Object','Maximum Posts'=>'Maximum Posts','Minimum Posts'=>'Minimum Posts','Featured Image'=>'Featured Image','Selected elements will be displayed in each result'=>'Selected elements will be displayed in each result','Elements'=>'Elements','Taxonomy'=>'Taxonomy','Post Type'=>'Post Type','Filters'=>'Filters','All taxonomies'=>'All taxonomies','Filter by Taxonomy'=>'Filter by Taxonomy','All post types'=>'All post types','Filter by Post Type'=>'Filter by Post Type','Search...'=>'Search...','Select taxonomy'=>'Select taxonomy','Select post type'=>'Select post type','No matches found'=>'No matches found','Loading'=>'Loading','Maximum values reached ( {max} values )'=>'Maximum values reached ( {max} values )','Relationship'=>'Relationship','Comma separated list. Leave blank for all types'=>'Comma separated list. Leave blank for all types','Allowed File Types'=>'Allowed File Types','Maximum'=>'Maximum','File size'=>'File size','Restrict which images can be uploaded'=>'Restrict which images can be uploaded','Minimum'=>'Minimum','Uploaded to post'=>'Uploaded to post','All'=>'All','Limit the media library choice'=>'Limit the media library choice','Library'=>'Library','Preview Size'=>'Preview Size','Image ID'=>'Image ID','Image URL'=>'Image URL','Image Array'=>'Image Array','Specify the returned value on front end'=>'Specify the returned value on front end','Return Value'=>'Return Value','Add Image'=>'Add Image','No image selected'=>'No image selected','Remove'=>'Remove','Edit'=>'Edit','All images'=>'All images','Update Image'=>'Update Image','Edit Image'=>'Edit Image','Select Image'=>'Select Image','Image'=>'Image','Allow HTML markup to display as visible text instead of rendering'=>'Allow HTML markup to display as visible text instead of rendering','Escape HTML'=>'Escape HTML','No Formatting'=>'No Formatting','Automatically add &lt;br&gt;'=>'Automatically add &lt;br&gt;','Automatically add paragraphs'=>'Automatically add paragraphs','Controls how new lines are rendered'=>'Controls how new lines are rendered','New Lines'=>'New Lines','Week Starts On'=>'Week Starts On','The format used when saving a value'=>'The format used when saving a value','Save Format'=>'Save Format','Date Picker JS weekHeaderWk'=>'Wk','Date Picker JS prevTextPrev'=>'Prev','Date Picker JS nextTextNext'=>'Next','Date Picker JS currentTextToday'=>'Today','Date Picker JS closeTextDone'=>'Done','Date Picker'=>'Date Picker','Width'=>'Width','Embed Size'=>'Embed Size','Enter URL'=>'Enter URL','oEmbed'=>'oEmbed','Text shown when inactive'=>'Text shown when inactive','Off Text'=>'Off Text','Text shown when active'=>'Text shown when active','On Text'=>'On Text','Stylized UI'=>'Stylised UI','Default Value'=>'Default Value','Displays text alongside the checkbox'=>'Displays text alongside the checkbox','Message'=>'Message','No'=>'No','Yes'=>'Yes','True / False'=>'True / False','Row'=>'Row','Table'=>'Table','Block'=>'Block','Specify the style used to render the selected fields'=>'Specify the style used to render the selected fields','Layout'=>'Layout','Sub Fields'=>'Sub Fields','Group'=>'Group','Customize the map height'=>'Customise the map height','Height'=>'Height','Set the initial zoom level'=>'Set the initial zoom level','Zoom'=>'Zoom','Center the initial map'=>'Centre the initial map','Center'=>'Centre','Search for address...'=>'Search for address...','Find current location'=>'Find current location','Clear location'=>'Clear location','Search'=>'Search','Sorry, this browser does not support geolocation'=>'Sorry, this browser does not support geolocation','Google Map'=>'Google Map','The format returned via template functions'=>'The format returned via template functions','Return Format'=>'Return Format','Custom:'=>'Custom:','The format displayed when editing a post'=>'The format displayed when editing a post','Display Format'=>'Display Format','Time Picker'=>'Time Picker','Inactive <span class="count">(%s)</span>'=>'Inactive <span class="count">(%s)</span>' . "\0" . 'Inactive <span class="count">(%s)</span>','No Fields found in Trash'=>'No Fields found in Bin','No Fields found'=>'No Fields found','Search Fields'=>'Search Fields','View Field'=>'View Field','New Field'=>'New Field','Edit Field'=>'Edit Field','Add New Field'=>'Add New Field','Field'=>'Field','Fields'=>'Fields','No Field Groups found in Trash'=>'No Field Groups found in bin','No Field Groups found'=>'No Field Groups found','Search Field Groups'=>'Search Field Groups','View Field Group'=>'View Field Group','New Field Group'=>'New Field Group','Edit Field Group'=>'Edit Field Group','Add New Field Group'=>'Add New Field Group','Add New'=>'Add New','Field Group'=>'Field Group','Field Groups'=>'Field Groups','Customize WordPress with powerful, professional and intuitive fields.'=>'Customise WordPress with powerful, professional and intuitive fields.','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'Advanced Custom Fields','Your ACF PRO license is no longer active. Please renew to continue to have access to updates, support, & PRO features.'=>'Your ACF PRO licence is no longer active. Please renew to continue to have access to updates, support, & PRO features.','Your license has expired. Please renew to continue to have access to updates, support &amp; PRO features.'=>'Your licence has expired. Please renew to continue to have access to updates, support &amp; PRO features.','Activate your license to enable access to updates, support &amp; PRO features.'=>'Activate your licence to enable access to updates, support &amp; PRO features.','A valid license is required to edit options pages.'=>'A valid licence is required to edit options pages.','A valid license is required to edit field groups assigned to a block.'=>'A valid licence is required to edit field groups assigned to a block.','To enable updates, please enter your license key on the <a href="%1$s">Updates</a> page. If you don\'t have a license key, please see <a href="%2$s" target="_blank">details & pricing</a>.'=>'To enable updates, please enter your licence key on the <a href="%1$s">Updates</a> page. If you don’t have a licence key, please see <a href="%2$s" target="_blank">details & pricing</a>.','To enable updates, please enter your license key on the <a href="%1$s">Updates</a> page of the main site. If you don\'t have a license key, please see <a href="%2$s" target="_blank">details & pricing</a>.'=>'To enable updates, please enter your licence key on the <a href="%1$s">Updates</a> page of the main site. If you don’t have a licence key, please see <a href="%2$s" target="_blank">details & pricing</a>.','Your defined license key has changed, but an error occurred when deactivating your old license'=>'Your defined licence key has changed, but an error occurred when deactivating your old licence','Your defined license key has changed, but an error occurred when connecting to activation server'=>'Your defined licence key has changed, but an error occurred when connecting to activation server','<strong>ACF PRO &mdash;</strong> Your license key has been activated successfully. Access to updates, support &amp; PRO features is now enabled.'=>'<strong>ACF PRO &mdash;</strong> Your licence key has been activated successfully. Access to updates, support &amp; PRO features is now enabled.','There was an issue activating your license key.'=>'There was an issue activating your licence key.','An upstream API error occurred when checking your ACF PRO license status. We will retry again shortly.'=>'An upstream API error occurred when checking your ACF PRO licence status. We will retry again shortly.','You have reached the activation limit for the license.'=>'You have reached the activation limit for the licence.','View your licenses'=>'View your licences','Your license key has expired and cannot be activated.'=>'Your licence key has expired and cannot be activated.','License key not found. Make sure you have copied your license key exactly as it appears in your receipt or your account.'=>'Licence key not found. Make sure you have copied your licence key exactly as it appears in your receipt or your account.','Your license key has been deactivated.'=>'Your licence key has been deactivated.','Your license key has been activated successfully. Access to updates, support &amp; PRO features is now enabled.'=>'Your licence key has been activated successfully. Access to updates, support &amp; PRO features is now enabled.','Your license key is valid but not activated on this site. Please <a href="%s">deactivate</a> and then reactivate the license.'=>'Your licence key is valid but not activated on this site. Please <a href="%s">deactivate</a> and then reactivate the licence.','Your site URL has changed since last activating your license. We\'ve automatically activated it for this site URL.'=>'Your site URL has changed since last activating your licence. We’ve automatically activated it for this site URL.','Your site URL has changed since last activating your license, but we weren\'t able to automatically reactivate it: %s'=>'Your site URL has changed since last activating your licence, but we weren’t able to automatically reactivate it: %s','<strong>Error</strong>. Could not authenticate update package. Please check again or deactivate and reactivate your ACF PRO license.'=>'<strong>Error</strong>. Could not authenticate update package. Please check again or deactivate and reactivate your ACF PRO licence.','<strong>Error</strong>. Your license for this site has expired or been deactivated. Please reactivate your ACF PRO license.'=>'<strong>Error</strong>. Your licence for this site has expired or been deactivated. Please reactivate your ACF PRO licence.','No Options Pages found in Trash'=>'No Options Pages found in Bin','Deactivate License'=>'Deactivate Licence','Activate License'=>'Activate Licence','License Information'=>'Licence Information','License Key'=>'Licence Key','Recheck License'=>'Recheck Licence','Your license key is defined in wp-config.php.'=>'Your licence key is defined in wp-config.php.','Don\'t have an ACF PRO license? %s'=>'Don’t have an ACF PRO licence? %s','Enter your license key to unlock updates'=>'Enter your licence key to unlock updates','Please reactivate your license to unlock updates'=>'Please reactivate your licence to unlock updates']];