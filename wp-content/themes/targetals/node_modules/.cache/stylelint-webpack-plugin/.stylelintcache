[{"/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/editor.scss": "1", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/style.scss": "2", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/_animations.scss": "3", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/_fonts.scss": "4", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/_icons.scss": "5", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/_reset.scss": "6", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/_mixins.scss": "7", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/components/_component.scss": "8", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/components/_footer.scss": "9", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/components/_socialshare.scss": "10", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/templates/_template-default.scss": "11", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/components/_header.scss": "12", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_animation.scss": "13", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_breakpoints.scss": "14", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_easing.scss": "15", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_colors.scss": "16", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_index.scss": "17", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_paths.scss": "18", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_spacing.scss": "19", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_transparency.scss": "20", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/constants/_typographic.scss": "21", "/Users/<USER>/Documents/GitHub/targetals/wp-content/themes/targetals/src/scss/base/functions/_index.scss": "22"}, {"size": 184, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 558, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 50, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 2398, "mtime": 1752026579276, "hashOfConfig": "23"}, {"size": 727, "mtime": 1752026609177, "hashOfConfig": "23"}, {"size": 870, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 4976, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 2964, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 8136, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 1041, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 3638, "mtime": 1751992845855, "hashOfConfig": "23"}, {"size": 11529, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 726, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 497, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 1364, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 3161, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 163, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 77, "mtime": 1751992845853, "hashOfConfig": "23"}, {"size": 572, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 55, "mtime": 1751992845854, "hashOfConfig": "23"}, {"size": 331, "mtime": 1752026294596, "hashOfConfig": "23"}, {"size": 1876, "mtime": 1751992845854, "hashOfConfig": "23"}, "168qzjj"]