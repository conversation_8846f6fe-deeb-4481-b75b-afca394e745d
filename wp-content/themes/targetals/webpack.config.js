const glob = require('glob');
const path = require('path');
const yaml = require('js-yaml');
const fs   = require('fs');
const package = require('./package.json');
const exec = require('child_process').exec;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const SVGSpritemapPlugin = require('svg-spritemap-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');
const StyleLintPlugin = require('stylelint-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const ImageMinimizerPlugin = require('image-minimizer-webpack-plugin');

/**
 * WordPress dependencies
 */
 const DependencyExtractionWebpackPlugin = require( '@wordpress/dependency-extraction-webpack-plugin' );

const landoName = 'targetals.lndo.site';

function getEntry() {
  const entry = {};
  // Match only blocks/*/index.js
  glob.sync('./blocks/*/index.js').forEach((file) => {
    // Extract the block name from the path
    const blockName = path.basename(path.dirname(file)); // e.g., 'ta-heading'
    entry[blockName] = path.resolve(__dirname, file);
  });
  entry.app = './src/js/index.js';
  entry.style = './src/js/index.js';
  entry.editor = './src/js/editor.js';
  return entry
}

module.exports = (env, argv) => ({
  entry: getEntry(),
  output: {
    path: path.resolve(__dirname, 'dist'),
    sourceMapFilename: '[file].map',
    filename: 'js/[name].js',
    assetModuleFilename: 'assets/[name][ext]',
    clean: true,
  },
  cache: {
    type: 'filesystem',
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@wordpress/babel-preset-default']
          }
        }
      },
      {
        test: /\.scss$/,
        use: [MiniCssExtractPlugin.loader, {
          loader: "css-loader",
          options: {
            import: {
              filter: (url, media, resourcePath) => {
                // Only exclude Google Fonts, allow local font files
                if (url.includes("https://fonts.googleapis.com/")) {
                  return false;
                }
                return true;
              },
            },
            sourceMap: true,
          },
        },
        {
          loader: 'sass-loader',
          options: {
            sourceMap: true
          }
        },
        {
          loader: 'string-replace-loader',
          options: {
            multiple: [
              { search: 'Theme Name: NAME', replace: `Theme Name: ${package.themename}` },
              { search: 'Theme URI: URI', replace: `Theme URI: ${package.themeuri}` },
              { search: 'Author: AUTHOR', replace: `Author: ${package.author}` },
              { search: 'Author URI: AUTHORURI', replace: `Author URI: ${package.authoruri}` },
              { search: 'Description: DESCRIPTION', replace: `Description: ${package.description}` },
              { search: 'Version: VERSION', replace: `Version: ${argv.mode == 'production' ? package.version : Date.now()}` },
            ]
          },
        }],
      },
      {
        test: /\.(woff(2)?|ttf|eot|svg)(\?v=\d+\.\d+\.\d+)?$/,
        type: 'asset/resource',
        exclude: [
          path.resolve(__dirname, './src/images/'),
          path.resolve(__dirname, './src/icons/')
        ],
        generator : {
          filename : 'assets/fonts/[name][ext]',
        }
      },
      {
        test: /\.(jpe?g|png|gif|svg|jpg)$/i,
        exclude: [
          path.resolve(__dirname, './src/fonts/')
        ],
        type: 'asset/resource',
        generator : {
          filename : 'assets/images/[name][ext]',
        }
      }
    ],
  },
  resolve: {
    cache: false,
    alias: {
      '@bs/utils': path.resolve(__dirname, 'src/js/utils'),
      '@bs/constants': path.resolve(__dirname, 'src/js/constants'),
      '@bs/components': path.resolve(__dirname, 'src/js/components'),
    },
  },
  optimization: {
    minimize: true,
    minimizer: [
      new CssMinimizerPlugin(),
      new TerserPlugin({
        test: /\.js(\?.*)?$/i,
        extractComments: false,
        terserOptions: {
          compress: {
            drop_console: false,
            drop_debugger: false,
            pure_funcs: [],
          },
        },
      }),
      new ImageMinimizerPlugin({
        loader: true,
        test: /\.(jpe?g|png|gif|svg|jpg)$/i,
        include: [
          path.resolve(__dirname, './src/images/')
        ],
        generator: [
          {
            preset: "webp",
            implementation: ImageMinimizerPlugin.imageminGenerate,
            options: {
              plugins: ["imagemin-webp"],
            },
          },
        ],
      }),
      new ImageMinimizerPlugin({
        generator: [
          {
            // You can apply generator using `?as=webp`, you can use any name and provide more options
            preset: "webp",
            implementation: ImageMinimizerPlugin.imageminGenerate,
            options: {
              plugins: ["imagemin-webp"],
            },
          },
        ],
        minimizer: {
          implementation: ImageMinimizerPlugin.imageminMinify,
          options: {
            plugins: [
              ['gifsicle', { interlaced: true }],
              ['jpegtran', { progressive: true }],
              ['optipng', { optimizationLevel: 5 }]
            ]
          }
        }
      })
    ],
  },
  devtool: argv.mode == 'production' ? "source-map" : "source-map",
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist'),
      watch: false,
    },
    host: landoName, // update with the appropriate local lando domain
    open: true,
    hot: true,
    allowedHosts: [
      '.lndo.site',
      'localhost',
    ],
    compress: true,
    port: 8001, // devserver port
    client: {
      overlay: {
        runtimeErrors: true,
        errors: true,
        warnings: false,
      },
      progress: false,
    },
    devMiddleware: {
      writeToDisk: true,
      index: false, // specify to enable root proxying
    },
    proxy: {
      context: () => true,
      target: `http://${landoName}:80`, // update with the appropriate local lando domain and app server port.
    },
    watchFiles: ['./**/*'],
  },
  watchOptions: {
    aggregateTimeout: 100,
    ignored: '**/node_modules',
  },
  plugins: [
    new DependencyExtractionWebpackPlugin(),
    new MiniCssExtractPlugin({ filename: 'css/[name].css' }),
    new CopyPlugin({
      patterns: [
        { from: "./src/images", to: "./assets/images" },
        { from: "./src/icons", to: "./assets/icons" },
        { from: "./src/fonts", to: "./assets/fonts" },
      ],
    }),
    new SVGSpritemapPlugin('src/icons/*.svg', {
      output: {
        filename: '../views/icon-sprite.twig', // Specify the output path for the sprite file
        svg4everybody: true, // Optional: Automatically include the svg4everybody polyfill
        svg: {
          attributes: {
            height: '0',
            position: 'absolute',
            width: '0'
          },
        },
      },
      sprite: {
        prefix: '',
      },
    }),
    new ESLintPlugin({
      fix: argv.mode == 'production',
      emitError: true,
      emitWarning: true,
      failOnError: true,
      overrideConfigFile: '.eslintrc.json'
    }),
    new StyleLintPlugin({
      configFile: path.resolve(__dirname, '.stylelintrc.js'),
      context: path.resolve(__dirname, './src/scss'),
      files: '**/*.scss',
      fix: true,
      failOnError: true,
    }),
    {
      apply: (compiler) => {
        compiler.hooks.afterEmit.tap('AfterEmitPlugin', (compilation) => {
          console.log('Copying theme style.css to root.')
          exec('npm run theme', (err, stdout, stderr) => {
            if (stdout) process.stdout.write(stdout);
            if (stderr) process.stderr.write(stderr);
          });
        });
      }
    }
  ]
});
