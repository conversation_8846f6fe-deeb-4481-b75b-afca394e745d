# Target ALS WordPress Theme

A custom WordPress theme developed for Target ALS by Phases & Blue State.

## Overview

The Target ALS theme is a modern, responsive WordPress theme built with a comprehensive build system using Webpack, Sass, JavaScript and custom dynamic and ACF Gutenberg blocks.

## Theme Information

- **Theme Name**: Target ALS
- **Version**: 1.0.0
- **Author**: Phases & Blue State
- **Description**: A theme for Target ALS
- **Theme URI**: https://www.targetals.org/
- **Node Version**: 20.19.3

## Features

- 🎨 Custom color palette with Target ALS brand colors
- 📱 Fully responsive design
- 🧱 Custom Gutenberg blocks (ta-heading, ta-caption, ta-verticalvideo)
- 🖼️ Advanced image optimization (WebP generation, compression)
- 🎯 SVG sprite system for icons
- 📦 Modern build system with Webpack 5
- 🎨 Sass/SCSS for styling
- ✅ Code quality tools (ESLint, Stylelint, Prettier)
- 🔧 PHP CodeSniffer with WordPress standards
- 🚀 Development server with hot reload
- 📐 CSS Grid and Flexbox layouts

## Color Palette

The theme uses a carefully crafted color system:

- **Primary Sand**: `#efeeeb`
- **Primary Orange**: `#ec6326`
- **Primary Navy**: `#000426`
- **Primary Royal Blue**: `#232e83`
- **Primary Bright Blue**: `#85d1eb`
- **Chow Yellow**: `#fad92f`
- **Neutral Colors**: Black, White, Blue variations

## Directory Structure

```
wp-content/themes/targetals/
├── src/                    # Source files
│   ├── scss/              # Sass stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Source images
│   ├── icons/             # SVG icons
│   └── fonts/             # Font files
├── dist/                  # Compiled assets
│   ├── css/               # Compiled CSS
│   ├── js/                # Compiled JavaScript
│   └── assets/            # Optimized assets
├── blocks/                # Custom Gutenberg blocks
│   └── acf-blocks/        # ACF-based blocks
├── includes/              # PHP includes
│   ├── theme_functions/   # Theme functionality
│   ├── post-types/        # Custom post types
│   └── utils/             # Utility functions
├── modules/               # Template modules
├── scripts/               # Build scripts
└── vendor/                # Composer dependencies
```

## Requirements

- **Node.js**: 20.19.3
- **WordPress**: 5.0+
- **PHP**: 7.4+
- **Composer**: For PHP dependencies

## Installation

1. Clone or download the theme to your WordPress themes directory:
   ```bash
   cd wp-content/themes/
   git clone [repository-url] targetals
   ```

2. Navigate to the theme directory:
   ```bash
   cd targetals
   ```

3. Install Node.js dependencies:
   ```bash
   npm install
   ```

4. Install PHP dependencies:
   ```bash
   composer install
   ```

5. Activate the theme in WordPress admin.

## Development

### Available Scripts

- **`npm run build`** - Production build with optimization
- **`npm run start`** - Development server with hot reload
- **`npm run lint`** - Run all linters (SCSS, JS, formatting)
- **`npm run lint:fix`** - Auto-fix linting issues
- **`npm run lint:php`** - PHP CodeSniffer check
- **`npm run lint:php:fix`** - Auto-fix PHP issues
- **`npm run optimize:images`** - Optimize and compress images
- **`npm run generate:webp`** - Generate WebP versions of images
- **`npm run pretty`** - Format code with Prettier

### Development Workflow

1. Start the development server:
   ```bash
   npm run start
   ```

2. Make changes to source files in the `src/` directory

3. The build system will automatically:
   - Compile Sass to CSS
   - Bundle and transpile JavaScript
   - Optimize images
   - Generate SVG sprites
   - Reload the browser

4. Before committing, run:
   ```bash
   npm run lint:fix
   npm run build
   ```

### Custom Blocks

The theme includes several custom Gutenberg blocks:

#### ta-heading
Custom heading block with Target ALS styling options and typography scales.

#### ta-caption
Enhanced caption block with additional formatting options.

#### ta-verticalvideo (ACF Block)
Custom video block for vertical video content.

### Image Optimization

The theme includes comprehensive image optimization:

- **Compression**: Automatic compression of JPEG, PNG, GIF, and SVG files
- **WebP Generation**: Automatic WebP format generation for better performance
- **SVG Sprites**: Icon system using SVG sprites for optimal loading

### Code Quality

The theme enforces code quality through:

- **ESLint**: JavaScript linting with Airbnb configuration
- **Stylelint**: SCSS/CSS linting with standard configuration
- **Prettier**: Code formatting
- **PHP CodeSniffer**: PHP code standards (WordPress Coding Standards)
- **Husky**: Pre-commit hooks for automated quality checks

## Browser Support

- Last 2 versions of major browsers
- \> 2% market share
- Excludes IE 11 and below

## Contributing

1. Follow the established coding standards
2. Run linting tools before committing
3. Test across supported browsers
4. Update documentation as needed

## License

ISC License

## Support

For support and questions, contact the development team at Phases & Blue State.
