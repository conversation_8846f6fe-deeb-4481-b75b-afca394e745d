<?php

if ( ! defined( 'ABSPATH' ) ) {
	$protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
	header( $protocol . ' 404 Not Found', true );
	echo '<h1>404 Not Found</h1>';
	exit;
}

class JSXBlock {
	private $name;
	private $data = [];
	private $render = false;
	private $attributes = [];

	public static function make( $name ) {
		$instance       = new self();
		$instance->name = $name;
		return $instance;
	}

	public function withRender() {
		$this->render = true;
		return $this;
	}

	public function withAttributes( $attributes ) {
		$this->attributes = $attributes;
		return $this;
	}

	public function withData( $data ) {
		$this->data = $data;
		return $this;
	}

	public function register() {
		add_action( 'init', function () {
			wp_register_script(
				$this->name,
				get_stylesheet_directory_uri() . "/dist/js/{$this->name}.js",
				[ 'wp-blocks', 'wp-editor' ]
			);

			if ( ! empty( $this->data ) ) {
				wp_localize_script( $this->name, $this->name, $this->data );
			}

			$args = [
				'editor_script' => $this->name,
			];

			if ( $this->render ) {
				$args['render_callback'] = function ( $attributes, $content, $block ) {
					ob_start();
					require get_theme_file_path( "/blocks/{$this->name}/render.php" );
					return ob_get_clean();
				};
			}

			if ( ! empty( $this->attributes ) ) {
				$args['attributes'] = $this->attributes;
			}

			register_block_type( "targetals/{$this->name}", $args );
		} );
	}
}
