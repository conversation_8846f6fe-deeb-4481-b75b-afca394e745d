<?php

if ( ! defined( 'ABSPATH' ) ) {
	$protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
	header( $protocol . ' 404 Not Found', true );
	echo '<h1>404 Not Found</h1>';
	exit;
}

// Disable G<PERSON>nberg on the specific post types
// add_filter( 'use_block_editor_for_post_type', 'prefix_disable_gutenberg', 10, 2 );
// function prefix_disable_gutenberg( $current_status, $post_type ) {
// 	if ( $post_type === 'oghome' ) {
// 		return false;
// 	}
// 	return $current_status;
// }

/**
 * Templates and Page IDs without editor
 */
function ta_disable_editor( $id = false ) {

	$excluded_templates = array(
		'template-example.php',
	);

	if ( empty( $id ) ) {
		return false;
	}

	$id       = intval( $id );
	$template = get_page_template_slug( $id );

	return in_array( $template, $excluded_templates );
}

/**
 * Disable <PERSON><PERSON> by template
 */
function ta_disable_gutenberg( $can_edit, $post_type ) {

	if ( ! ( is_admin() && ! empty( $_GET['post'] ) ) ) {
		return $can_edit;
	}

	if ( ta_disable_editor( $_GET['post'] ) ) {
		$can_edit = false;
	}

	return $can_edit;
}
add_filter( 'gutenberg_can_edit_post_type', 'ta_disable_gutenberg', 10, 2 );
add_filter( 'use_block_editor_for_post_type', 'ta_disable_gutenberg', 10, 2 );


function add_customizations_blocks( $block_content, $block ) {
	// Check if the block is a paragraph block
	if ( $block['blockName'] === 'core/paragraph' ) {
		// Add the custom class to the paragraph block
		$block_content = preg_replace_callback(
			'/<p(?:\s+class=["\']([^"\']*)["\'])?/',
			function ( $matches ) {
					$existing_classes = isset( $matches[1] ) ? $matches[1] . ' ' : '';
					return '<p class="' . trim( $existing_classes . 'wp-block-paragraph' ) . '"';
			},
			$block_content
		);
	}

	// Check if the block is a image block
	if ( $block['blockName'] === 'core/image' ) {
		if ( ! empty( $block['attrs']['width'] ) ) {
			// Check if <figure> already has a style attribute
			if ( preg_match( '/<figure[^>]+style=["\']([^"\']+)["\']/i', $block_content, $style_matches ) ) {
					$existing_styles = $style_matches[1];

					// Append width to existing styles
					$new_styles    = $existing_styles . '; width:' . $block['attrs']['width'] . ';';
					$block_content = preg_replace( '/<figure([^>]+style=["\'])([^"\']+)["\']/', '<figure\1' . esc_attr( $new_styles ) . '"', $block_content, 1 );
			} else {
					// No existing styles, add new style attribute
					$block_content = preg_replace( '/<figure/', '<figure style="width:' . $block['attrs']['width'] . ';"', $block_content, 1 );
			}
		}
	}

	return $block_content;
}
add_filter( 'render_block', 'add_customizations_blocks', 10, 2 );

// Adds background color meta fields to pages, posts, and news content types
function register_page_background_meta() {

	$post_types = array( 'post', 'page', 'news' ); // Add all desired post types

	foreach ( $post_types as $post_type ) {
		register_post_meta(
			$post_type,
			'background_color',
			array(
				'show_in_rest'      => true, // This is crucial to allow access via REST API
				'single'            => true, // Ensures it's a single value (not an array)
				'type'              => 'string', // The type of value (string for a color hex)
				'sanitize_callback' => 'sanitize_hex_color', // Ensures it's a valid hex color
			)
		);
		register_post_meta(
			$post_type,
			'background_gradient',
			array(
				'show_in_rest' => true, // This is crucial to allow access via REST API
				'single'       => true, // Ensures it's a single value (not an array)
				'type'         => 'string',
			)
		);
	}
}
add_action( 'init', 'register_page_background_meta' );

