<?php
/**
 * Load custom blocks for the theme.
 *
 * This file scans the 'blocks' directory in the theme and registers each block
 * with its attributes and render function.
 */

if ( ! defined( 'ABSPATH' ) ) {
	$protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
	header( $protocol . ' 404 Not Found', true );
	echo '<h1>404 Not Found</h1>';
	exit;
}

$jsx_block = get_template_directory() . '/includes/utils/JSXblock.php';
if ( file_exists( $jsx_block ) ) {
  require_once $jsx_block;
}

$block_dir = get_template_directory() . '/blocks';
$block_folders = array_filter( scandir( $block_dir ), function ( $item ) use ( $block_dir ) {
	return $item[0] !== '.' && is_dir( "$block_dir/$item" );
} );

foreach ( $block_folders as $block_name ) {
	$config_path = "$block_dir/$block_name/config.php";

	// Skip blocks without a JSX config (assumed to be ACF or legacy blocks)
	if ( ! file_exists( $config_path ) ) {
		continue;
	}

	$config = include $config_path;

	JSXBlock::make( $block_name )
		->withAttributes( $config['attributes'] ?? [] )
		->withData( $config['data'] ?? [] )
		->withRender()
		->register();
}

function register_acf_blocks() {
    foreach (glob(get_template_directory() . '/blocks/acf-blocks/*/fields.php') as $fields_file) {
        include_once $fields_file;
    }
    $acf_block_dir = get_template_directory() . '/blocks/acf-blocks'; // adjust path if needed

    if ( ! is_dir( $acf_block_dir ) ) {
        return;
    }

    $block_folders = array_filter( scandir( $acf_block_dir ), function( $item ) use ( $acf_block_dir ) {
        return $item[0] !== '.' && is_dir( "$acf_block_dir/$item" );
    } );

    foreach ( $block_folders as $block_name ) {
        $block_json = "$acf_block_dir/$block_name/block.json";

        if ( file_exists( $block_json ) ) {
            // Register block using block.json path
            register_block_type( $block_json );
        }
    }
}
add_action( 'acf/init', 'register_acf_blocks');