<?php
/**
 *  Theme control class
 */

if ( ! defined( 'ABSPATH' ) ) {
	$protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
	header( $protocol . ' 404 Not Found', true );
	echo '<h1>404 Not Found</h1>';
	exit;
}

class ThemeControl {
	/**
	 * Constructor for the ThemeControl class.
	 * This sets up theme support, registers menus, and enqueues styles and scripts.
	 */
	public function __construct() {
		add_action( 'after_setup_theme', array( $this, 'theme_supports' ) );
		add_action( 'init', array( $this, 'register_menus' ) );
		// add_filter('rest_authentication_errors', array($this, 'disable_rest_api_wo_auth'));
		add_action( 'wp_enqueue_scripts', array( $this, 'addStylesAndScripts' ), 999, true );
		add_action( 'after_setup_theme', array( $this, 'addEditorStyles' ), 999, true );
		// add_action( 'enqueue_block_editor_assets', array( $this, 'addGuteEditorJS' ), 999, true );
	}

	/** Disable rest api for not logged in users. */
	public function disable_rest_api_wo_auth( $result ) {
		$route = $_SERVER['REQUEST_URI'] ?? '';

		// List of routes that are allowed for unauthenticated users
		$allowed_routes = array(
			'/wp-json/wp/v2/pages/',
			'/wp-json/wpcom/v2/memberships/status/', // Add wpcom route that has its own auth layer
		);

		// Allow access to any of the allowed routes
		foreach ( $allowed_routes as $allowed_route ) {
			if ( strpos( $route, $allowed_route ) !== false ) {
				return $result; // Allow the request if it matches an allowed route
			}
		}

		if ( true === $result || is_wp_error( $result ) ) {
			return $result;
		}

		// Return an error if user is not logged in.
		if ( ! is_user_logged_in() ) {
			return new WP_Error(
				'rest_forbidden',
				__( 'REST API restricted to authenticated users only.', 'freedom' ),
				array( 'status' => 401 )
			);
		}

		return $result; // Allow the request if the user is logged in
	}

	public function register_menus() {
		register_nav_menus(
			array(
				'header-menu' => __( 'Header Menu' ),
				'footer-menu' => __( 'Footer Menu' ),
				'social-menu' => __( 'Social Menu' ),
			)
		);
	}

	public function theme_supports() {
		// Add default posts and comments RSS feed links to head.
		// add_theme_support('automatic-feed-links');

		/*
		 * Let WordPress manage the document title.
		 * By adding theme support, we declare that this theme does not use a
		 * hard-coded <title> tag in the document head, and expect WordPress to
		 * provide it for us.
		 */
		add_theme_support( 'title-tag' );

		/*
		 * Enable support for Post Thumbnails on posts and pages.
		 *
		 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		 */
		add_theme_support( 'post-thumbnails' );

		/*
		 * Switch default core markup for search form, comment form, and comments
		 * to output valid HTML5.
		 */
		add_theme_support(
			'html5',
			array(
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
			)
		);

		/*
		 * Enable support for Post Formats.
		 *
		 * See: https://codex.wordpress.org/Post_Formats
		 */
		add_theme_support(
			'post-formats',
			array(
				'aside',
				'image',
				'video',
				'quote',
				'link',
				'gallery',
				'audio',
			)
		);

		add_theme_support( 'menus' );
	}

	public function addEditorStyles() {
		add_theme_support( 'editor-styles' );
		add_editor_style( array( 'style.css', 'https://use.typekit.net/hnk4nmq.css', '/dist/css/editor.css' ) );
	}

	public function addStylesAndScripts() {
		global $wp_styles;

		$theme = wp_get_theme();
		wp_register_style( 'themestyles', get_template_directory_uri() . '/style.css', array(), $theme['Version'], 'all' );
		wp_enqueue_style( 'themestyles' ); // Enqueue it

		if ( ! is_admin() ) {
			// wp_deregister_style( 'dashicons' );
			// wp_dequeue_style('wp-block-library');
			// wp_dequeue_style('wp-block-library-theme');
			wp_deregister_script( 'jquery' );

			// Template specific scripts
			$template_scripts = array(
				'template-eventslanding.php' => 'volunteer.js',
			);

			$script_file = isset( $template_scripts[ get_page_template_slug() ] )
				? $template_scripts[ get_page_template_slug() ]
				: 'app.js';

			wp_enqueue_script(
				'site-js',
				get_template_directory_uri() . '/dist/js/' . $script_file,
				array(),
				$theme['Version'],
				true
			);
		}
	}

	// public function addGuteEditorJS() {
	// 	$theme = wp_get_theme();

	// 	wp_enqueue_script(
	// 		'custom-gutenberg-editor-js',
	// 		get_template_directory_uri() . '/dist/js/ta-blocks.js',
	// 		array( 'wp-plugins', 'wp-edit-post', 'wp-components', 'wp-data', 'wp-compose', 'wp-block-editor' ),
	// 		$theme['Version'],
	// 		true
	// 	);
	// }
}
