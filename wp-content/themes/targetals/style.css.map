{"version": 3, "file": "/style.css", "mappings": "AAAA;;;;;;;CAAA,CCKQ,wGCEN,sBAAwC,CACxC,wBAAwC,CACxC,sBAAmC,CACnC,4BAA4C,CAC5C,6BAA+C,CAC/C,qBAAsC,CACtC,gCAAkD,CAClD,yBAA2C,CAC3C,oBAAmC,CACnC,oBAAyC,CACzC,sBAAwC,CACxC,yBAA0B,CAG1B,qCAAsC,CACtC,wCAAyC,CACzC,uCAAwC,CAGxC,6CAA8C,CAC9C,2CAA4C,CAC5C,2CAA4C,CAC5C,4CAA6C,CAC7C,wCAAyC,CACzC,gDAAiD,CACjD,+CAAgD,CAChD,+CAAgD,CAChD,+CAAgD,CAChD,gDAAiD,CAGjD,8CAA+C,CAC/C,4CAA6C,CAC7C,4CAA6C,CAC7C,6CAA8C,CAC9C,8CAA+C,CAC/C,yDAA0D,CAC1D,2DAA4D,CAC5D,2DAA4D,CAC5D,wDAAyD,CACzD,yDAA0D,CAG1D,uCAAwC,CACxC,0CAA2C,CAC3C,0CAA2C,CAC3C,sCAAuC,CACvC,uCAAwC,CACxC,+CAAgD,CAChD,6CAA8C,CAC9C,6CAA8C,CAC9C,8CAA+C,CAC/C,+CAAgD,CAGhD,gCAAiC,CACjC,4CAA6C,CAC7C,sCAAuC,CACvC,qDAAsD,CACtD,6CAA8C,CAG9C,0CAA2C,CAC3C,wCAAyC,CACzC,wCAAyC,CACzC,yCAA0C,CAC1C,0CAA2C,CAC3C,0CAA2C,CAC3C,wCAAyC,CACzC,wCAAyC,CACzC,yCAA0C,CAC1C,0CAA2C,CC1E7C,WACE,kBAEA,iBAGA,CAHA,gBADA,yMAIA,kCAIF,sCAGE,6BACA,CACA,mBACA,CAFA,eACA,CAEA,aACA,CAFA,mBACA,CACA,kCAIA,kCACA,iDCtBA,kBACA,kBAGF,cAGE,mCAKA,kCACA,uCAGF,wCAUE,OAGF,eAEE,aAKA,iBAGF,uBAIE,4BAIA,aAGF,uBAIE,QAIA,QAGF,aACE,CAJA,iBAIA,8BAGA,gBACA,eACA,CAFA,sBAEA,oBAIF,SAHE,SAGF,iBACE,KAQF,QACE,CAJA,WAGF,CAPE,cAQA,cACA,WACA,gCCrFA,mCC2JA,sBACA,iBACA,yBACA,0BACA,+BD/JA,6CCkKE,+BDlKF,mDAIA,gCCyLA,qBACA,iBACA,oBACA,0BACA,+BD7LA,oDAIA,iCCkMA,sBACA,iBACA,yBACA,0BACA,+BDtMA,kDAIA,gCC2MA,CAEA,qBACA,CAHA,gBACA,oBAEA,0BACA,+BD/MA,iDAIA,kCCoNA,CAEA,qBACA,CAHA,gBACA,yBAEA,0BACA,+BDxNA,+BC2NE,oBCxOJ,kBACE,wBACA,uBAIA,4BACE,CACA,mCACA,kBACA,WACA,+BAEA,kBAGF,kBACE,WACA,kBAKA,kBACA,CACA,mBACA,sBAEA,uCATA,6BAIA,mBACA,eACA,CAEA,eAUA,CATA,oBAKA,mBACA,CACA,oBAEA,kBACA,8BAIA,4BACA,oCACA,gBACA,oBACA,mBAEA,uBACE,yBACA,wDAKJ,kCAGE,gBACA,wBACA,4BACA,CACA,kCACA,wFAME,+GAIA,iEAQA,kEAIA,sYAUA,seAYA,2BAEE,UAKN,yBACE,yBAEA,WDvCE,oBC9EN,qBAyHI,kBAEA,mBACE,iBAGF,qBACE,CACA,aACA,CAFA,mBACA,CACA,iBACA,qBACA,kBAGF,cACE,gBACA,kBACA,mBAGF,sBACE,cACA,kBACA,oBACA,mBAEA,uBACE,uBACA,+BAOR,wBACE,uCC3JU,CD0JZ,QC1JE,6BAKE,kCACE,mCAEA,qDAKF,cACA,wBACA,kCAMA,oCACE,uBAIJ,OACE,gBAGF,OACE,gBAGF,OACE,gBAGF,OACE,CAOF,gCACE,eAEA,oBACE,8BAKF,mCACA,sBACA,gBACA,8BAME,8BACE,4BACA,oCACA,qBACA,uEAGA,8DAEE,8CACA,2BAON,iCACA,oBACA,uBACA,wBACA,sBAIA,aACE,2BACA,8CAIE,mEAON,4BAGE,mCACA,gBACA,eACA,kEAGA,4BF9EF,UACA,4BACA,CACA,2BACA,CACA,UACA,kBACA,4bEmHM,qEAOF,4bClKJ,mBACA,62BAkGI,qBAEA,8CAGE,62BAwFE,yYA8BM,+DACE,wXAsBN,iDASA,uDAIA,+EHtLN,mBGsLM,gEAII,cASd,4BACE,gBACA,uBACA,uBAEA,8CACE,QH3LA,gCG0LF,gBH1LE,0CG0LF,8EH1LE,aG0LF,4CAaI,cAIJ,uCACE,uBACA,uBACA,uBACA,OAEA,6EAIE,qDACE,sCAIJ,YACE,6BACA,mBH1OF,gBGwOA,4CAKI,qBACA,6DAIF,+GHlPF,kBGkPE,mIASA,kFAEE,cACA,kBACA,sBACA,gBAEA,uFAEE,4BHpQN,kBG2PE,8FAgBI,aACA,qBACA,iBH7QN,+BGmMJ,iBAiFI,uBAKF,4BACE,wCACA,kBACA,oBACA,oBAGA,kDAEE,mCAGF,kBACE,qCAEA,+BAGE,yCACE,sBAKN,gaA2DQ,CAWR,2BACE,CAZM,qBAWR,CAtEA,eAuEE,gBACA,iBAEA,qCACE,2CHhYJ,uZI7EJ,gECEc,gBDEZ,sTACE,0BACA,iCACA,uCAGF,mHACE,8EAEA,uIACE,iDAIJ,qGACE,kCACA,CAIA,+WAGF,oHAIA,qRAIA,6JACE,kFAGF,iIACE,6CAGF,8IAEE,0BCtCgB,CDgDd,4hBAmBE,omDACE,QJoBN,sBIZF,gIAEE,WAEA,mIACE,6CJOF,oBICA,2CJDA,CICA,yKAIA,iOAIA,mMACE,WJVF,gCIgBF,oGACE,gDAGA,gHACE,4CACA,aAGF,uPACE,kBAGF,qNAIA,eAJA,wDACE,gBAGF,kLACE,iCAGF,mIACE,8BACA,uBAQE,+8CAOF,6RAUI,ipECzJgB", "sources": ["webpack://targetals/./src/scss/style.scss", "webpack://targetals/./src/scss/base/_fonts.scss", "webpack://targetals/./src/scss/base/constants/_colors.scss", "webpack://targetals/./src/scss/base/_icons.scss", "webpack://targetals/./src/scss/base/_reset.scss", "webpack://targetals/./src/scss/blocks/_ta-heading.scss", "webpack://targetals/./src/scss/base/_mixins.scss", "webpack://targetals/./src/scss/components/_component.scss", "webpack://targetals/./src/scss/components/_footer.scss", "webpack://targetals/./src/scss/components/_header.scss", "webpack://targetals/./src/scss/templates/_template-default.scss", "webpack://targetals/./src/scss/base/constants/_spacing.scss"], "sourcesContent": ["/*!\nTheme Name: Target ALS\nTheme URI: https://www.targetals.org/\nAuthor: Phases & Blue State\nAuthor URI: https://www.targetals.org/\nDescription: A theme for Target ALS\nVersion: 1.0.0\n*/\n\n/*\nSASS base\n*/\n@import 'base/fonts';\n@import 'base/constants';\n@import 'base/functions';\n@import 'base/mixins';\n@import 'base/animations';\n@import 'base/icons';\n@import 'base/reset';\n\n/*\nSASS blocks\n*/\n@import 'blocks/ta-heading';\n\n/*\nSASS components\n*/\n@import 'components/component';\n@import 'components/footer';\n@import 'components/header';\n@import 'components/socialshare';\n\n/*\nSASS templates\n*/\n@import 'templates/template-default';\n", "/**\n  IMPORT WEBFONTS\n**/\n\n// Import Google fonts example:\n@import 'https://fonts.googleapis.com/css?family=Montserrat:400,400i,500,600|Roboto+Condensed:700';\n\n// Locally-hosted font example\n\n/**\n@font-face {\n  font-family: FamilyName;\n  src: url('../fonts/FamilyName-Bold.woff2') format('woff2'),\n    url('../fonts/FamilyName-Bold.woff') format('woff');\n  font-weight: 700;\n  font-style: normal;\n}\n**/\n", "/**\n  COLORS\n  color palette\n**/\n\n:root {\n  // names correspond with Figma design names - set base values here\n  --primary-sand: rgba(239 238 235 / 100%);\n  --primary-orange: rgba(236 99 38 / 100%);\n  --primary-navy: rgba(0 4 38 / 100%);\n  --primary-royal-blue: rgba(35 46 131 / 100%);\n  --primary-bright-blue: rgba(133 209 235 / 100%);\n  --chow-yellow: rgba(250 217 47 / 100%);\n  --chow-bright-blue-shade: rgba(225 243 250 / 100%);\n  --chow-light-sand: rgba(250 250 249 / 100%);\n  --neutral-black: rgba(0 0 0 / 100%);\n  --neutral-white: rgba(255 255 255 / 100%);\n  --neutral-blue: rgba(222 222 236 / 100%);\n  --transparent: transparent;\n\n  // context for colors - use var() with the colors above\n  --site-background: var(--primary-sand);\n  --body-text-default: var(--neutral-black);\n  --headline-default: var(--neutral-black);\n\n  // text links\n  --text-link-default: var(--primary-royal-blue);\n  --text-link-hover: var(--primary-royal-blue);\n  --text-link-focus: var(--primary-royal-blue);\n  --text-link-active: var(--primary-royal-blue);\n  --text-link-visited: var(--neutral-black);\n  --text-link-default-underline: var(--transparent);\n  --text-link-hover-underline: var(--neutral-blue);\n  --text-link-focus-underline: var(--neutral-blue);\n  --text-link-active-underline: var(--transparent);\n  --text-link-visited-underline: var(--transparent);\n\n  // emphasized links\n  --emphasized-link-default: var(--neutral-black);\n  --emphasized-link-hover: var(--neutral-black);\n  --emphasized-link-focus: var(--neutral-black);\n  --emphasized-link-active: var(--neutral-black);\n  --emphasized-link-visited: var(--neutral-black);\n  --emphasized-link-default-underline: var(--primary-orange);\n  --emphasized-link-hover-underline: var(--primary-royal-blue);\n  --emphasized-link-focus-underline: var(--primary-royal-blue);\n  --emphasized-link-active-underline: var(--primary-orange);\n  --emphasized-link-visited-underline: var(--primary-orange);\n\n  // navigation links\n  --nav-link-default: var(--neutral-black);\n  --nav-link-hover: var(--primary-royal-blue);\n  --nav-link-focus: var(--primary-royal-blue);\n  --nav-link-active: var(--neutral-black);\n  --nav-link-visited: var(--neutral-black);\n  --nav-link-default-underline: var(--transparent);\n  --nav-link-hover-underline: var(--transparent);\n  --nav-link-focus-underline: var(--transparent);\n  --nav-link-active-underline: var(--transparent);\n  --nav-link-visited-underline: var(--transparent);\n\n  // forms\n  --form-label: var(--primary-navy);\n  --form-input-placeholder: var(--primary-navy);\n  --form-input-text: var(--neutral-black);\n  --form-input-background: var(--chow-bright-blue-shade);\n  --form-input-border: var(--primary-royal-blue);\n\n  // buttons\n  --button-default: var(--primary-royal-blue);\n  --button-hover: var(--primary-royal-blue);\n  --button-focus: var(--primary-royal-blue);\n  --button-active: var(--primary-royal-blue);\n  --button-visited: var(--primary-royal-blue);\n  --button-default-text: var(--neutral-white);\n  --button-hover-text: var(--neutral-white);\n  --button-focus-text: var(--neutral-white);\n  --button-active-text: var(--neutral-white);\n  --button-visited-text: var(--neutral-white);\n}\n", "/**\n  $ICONS\n**/\n\n@font-face {\n  font-family: icons;\n  src: url('../fonts/icons.eot');\n  src: url('../fonts/icons.eot?#iefix') format('embedded-opentype'),\n    url('../fonts/icons.ttf') format('truetype'),\n    url('../fonts/icons.woff') format('woff');\n  font-weight: normal;\n  font-style: normal;\n}\n\n[class^='icon-'],\n[class*=' icon-'] {\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: icons, sans-serif !important;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n", "/**\n  CSS RESET\n**/\n\nhtml {\n  text-size-adjust: 100%;\n  box-sizing: border-box;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\nbody {\n  margin: 0;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\nh1,\nh2,\nh3,\nh4,\np,\nblockquote,\nfigure,\nol,\nul {\n  margin: 0;\n  padding: 0;\n}\n\nmain,\nli {\n  display: block;\n}\n\nol,\nul {\n  list-style: none;\n}\n\nh1,\nh2,\nh3,\nh4 {\n  font-size: inherit;\n}\n\nstrong {\n  font-weight: bold;\n}\n\na,\n[role='button'],\n.ajax,\n.cta {\n  color: inherit;\n}\n\na {\n  text-decoration: none;\n}\n\nbutton {\n  overflow: visible;\n  border: 0;\n  font: inherit;\n  -webkit-font-smoothing: inherit;\n  letter-spacing: inherit;\n  background: none;\n  cursor: pointer;\n}\n\n::-moz-focus-inner {\n  padding: 0;\n  border: 0;\n}\n\n:focus {\n  outline: 0;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n  border: 0;\n}\n\nblockquote,\nq {\n  quotes: none;\n}\n", ".ta-heading {\n  // @include h2;\n\n  &.is-style-heading-1 {\n    @include h1;\n  }\n\n  &.is-style-heading-3 {\n    @include h3;\n  }\n\n  &.is-style-heading-4 {\n    @include h4;\n  }\n\n  &.is-style-heading-5 {\n    @include h5;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n", "@use 'sass:map';\n\n/**\n  MIXINS\n**/\n\n// ======================================\n// Vertical Align\n// ======================================\n\n@mixin vertical-align {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n// ======================================\n// Horizontal Align\n// ======================================\n\n@mixin horizontal-align {\n  left: 50%;\n  position: absolute;\n  transform: translateX(-50%);\n}\n\n@mixin vertical-horizontal-align {\n  left: 50%;\n  position: absolute;\n  top: 50%;\n  transform: translate(-50%, -50%);\n}\n\n// ======================================\n// Visually Hidden\n// Visually hide the element from the\n// screen but still have it accessible\n// via screenreaders\n// ======================================\n\n@mixin is-visually-hidden() {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n// ======================================\n// Ellipsis\n// ======================================\n\n@mixin ellipsis {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n// ======================================\n// IE MEDIA QUERY\n// ======================================\n\n@mixin ieonly() {\n  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n    @content;\n  }\n}\n\n// ======================================\n// Breakpoint Mixin\n// ======================================\n\n@mixin breakpoint($breakpoint, $direction) {\n  @if map-has-key($breakpoints, $breakpoint) {\n    $breakpoint-value: map.get($breakpoints, $breakpoint);\n\n    @if $direction == max {\n      @media (max-width: ($breakpoint-value - 1)) {\n        @content;\n      }\n    } @else if $direction == min {\n      @media (min-width: $breakpoint-value) {\n        @content;\n      }\n    } @else {\n      @media ($direction: $breakpoint-value) {\n        @content;\n      }\n    }\n  } @else {\n    @if $direction == max {\n      @media (max-width: $breakpoint) {\n        @content;\n      }\n    } @else if $direction == min {\n      @media (min-width: $breakpoint) {\n        @content;\n      }\n    } @else {\n      @media ($direction: $breakpoint) {\n        @content;\n      }\n    }\n  }\n}\n\n// ======================================\n// Mobile First\n// ======================================\n\n@mixin break-min($media) {\n  @if type-of($media) == 'number' {\n    @if unit($media) == 'px' {\n      @media screen and (min-width: #{$media}) {\n        @content;\n      }\n    } @else {\n      @media screen and (min-width: #{$media}em) {\n        @content;\n      }\n    }\n  } @else {\n    @media screen and (#{$media}) {\n      @content;\n    }\n  }\n}\n\n// Desktop First\n@mixin break-max($media) {\n  @if type-of($media) == 'number' {\n    @if unit($media) == 'px' {\n      // -1 px\n      @media screen and (max-width: #{$media - 0.063}) {\n        @content;\n      }\n    } @else {\n      // -1 px\n      @media screen and (max-width: #{$media - 0.063}em) {\n        @content;\n      }\n    }\n  } @else {\n    @media screen and (#{$media}) {\n      @content;\n    }\n  }\n}\n\n// ======================================\n// Headings\n// ======================================\n\n@mixin h1 {\n  // font-family: $roc-grotesque-font;\n  font-size: px-to-rem(45px);\n  font-weight: map.get($font-weights, bold);\n  letter-spacing: normal;\n  line-height: 100%;\n  text-transform: uppercase;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(100px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(130px);\n  }\n}\n\n@mixin h2 {\n  // font-family: $roc-grotesque-font;\n  font-size: px-to-rem(36px);\n  font-weight: map.get($font-weights, bold);\n  letter-spacing: normal;\n  line-height: 100%;\n  text-transform: uppercase;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(70px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(90px);\n  }\n}\n\n@mixin h3 {\n  // font-family: $gt-america-compressed-font;\n  font-size: px-to-rem(40px);\n  font-weight: map.get($font-weights, bolder);\n  letter-spacing: 0.01em;\n  line-height: 100%;\n  text-transform: none;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(65px);\n  }\n}\n\n@mixin h4 {\n  // font-family: $roc-grotesque-font;\n  font-size: px-to-rem(28px);\n  font-weight: map.get($font-weights, bold);\n  letter-spacing: normal;\n  line-height: 100%;\n  text-transform: uppercase;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(60px);\n  }\n}\n\n@mixin h5 {\n  // font-family: $gt-america-compressed-font;\n  font-size: px-to-rem(24px);\n  font-weight: map.get($font-weights, bolder);\n  line-height: 100%;\n  text-transform: none;\n  letter-spacing: normal;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(40px);\n  }\n}\n\n@mixin h6 {\n  // font-family: $roc-grotesque-font;\n  font-size: px-to-rem(18px);\n  font-weight: map.get($font-weights, bolder);\n  line-height: 120%;\n  text-transform: uppercase;\n  letter-spacing: normal;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(22px);\n  }\n}\n", "@use 'sass:map';\n\n/**\n  BASE COMPONENT\n**/\n\n.component {\n  margin: px-to-rem(30px) auto;\n  position: relative;\n\n  $self: &;\n\n  &__background {\n    background-position: center;\n    background-repeat: no-repeat;\n    background-size: contain;\n    height: 100%;\n    position: absolute;\n    width: 100%;\n    z-index: 0;\n  }\n\n  &__content {\n    padding: 0 px-to-rem(18px);\n    position: relative;\n    z-index: 1;\n  }\n\n  &__title {\n    color: var(--headline-default);\n    font-family: $display-font;\n    font-size: px-to-rem(30px);\n    font-weight: 900;\n    line-height: px-to-rem(36px);\n    margin-bottom: px-to-rem(20px);\n    text-align: left;\n  }\n\n  &__subhead {\n    color: var(--headline-default);\n    font-family: $display-font;\n    font-size: px-to-rem(25px);\n    font-weight: 900;\n    line-height: px-to-rem(30px);\n    text-align: left;\n  }\n\n  &__copy {\n    color: var(--body-text-default);\n    font-family: $copy-font;\n    font-size: px-to-rem(14.5px);\n    font-weight: 400;\n    text-align: left;\n\n    p {\n      font-size: px-to-rem(14.5px);\n      line-height: px-to-rem(20.3px);\n      margin-bottom: px-to-rem(20.3px);\n    }\n  }\n\n  &__title,\n  &__copy,\n  &__subhead {\n    margin-left: auto;\n    margin-right: auto;\n    text-align: left;\n    transition-duration: 0.5s;\n    transition-property: opacity;\n    transition-timing-function: ease-in;\n  }\n\n  &:first-of-type,\n  &:nth-of-type(n + 3) {\n    #{$self} {\n      &__copy {\n        transition-delay: 0.25s;\n      }\n\n      &__title {\n        transition-delay: 0.15s;\n      }\n    }\n  }\n\n  &:nth-of-type(2) {\n    #{$self} {\n      &__copy {\n        transition-delay: 0.75s;\n      }\n\n      &__title {\n        transition-delay: 0.5s;\n      }\n    }\n  }\n\n  &[data-position='below-viewport'],\n  &[data-visible='false']:first-of-type,\n  &[data-visible='false']:nth-of-type(2) {\n    #{$self} {\n      &__title,\n      &__copy {\n        opacity: 0;\n      }\n    }\n  }\n\n  &[data-position='in-viewport'],\n  &[data-position='above-viewport'],\n  &[data-visible='true']:first-of-type,\n  &[data-visible='true']:nth-of-type(2) {\n    #{$self} {\n      &__title,\n      &__copy {\n        opacity: 1;\n      }\n    }\n  }\n\n  &::after {\n    clear: both;\n    content: '';\n    display: table;\n  }\n\n  @include breakpoint(medium, min) {\n    margin: px-to-rem(50px) auto;\n\n    &__content {\n      padding: 0 px-to-rem(78px);\n    }\n\n    &__title {\n      font-size: px-to-rem(60px);\n      line-height: px-to-rem(70px);\n      padding-bottom: px-to-rem(32px);\n      max-width: 86%;\n      text-align: center;\n    }\n\n    &__subhead {\n      font-size: px-to-rem(30px);\n      max-width: 86%;\n      text-align: left;\n    }\n\n    &__copy {\n      font-size: px-to-rem(21px);\n      line-height: px-to-rem(29.4px);\n      max-width: 86%;\n      text-align: center;\n\n      p {\n        font-size: px-to-rem(21px);\n        line-height: px-to-rem(31.5px);\n        margin-bottom: px-to-rem(42px);\n      }\n    }\n  }\n}\n\nmain .component:last-of-type {\n  margin-bottom: 0;\n}\n", "@use 'sass:map';\n\n/**\n  FOOTER COMPONENT\n**/\n\n.footer {\n  margin: 0;\n  background-color: var(--site-background);\n\n  $self: &;\n\n  .component {\n    &__content {\n      display: flex;\n      flex-direction: column;\n      padding: px-to-rem(45px) px-to-rem(25px) px-to-rem(40px);\n    }\n  }\n\n  &__row {\n    display: grid;\n    grid-template-columns: auto;\n    grid-gap: 40px;\n  }\n\n  &__col {\n    width: 100%;\n\n    &:not(.footer__col-1) {\n      padding-left: px-to-rem(20px);\n    }\n  }\n\n  &__col-1 {\n    order: 1;\n  }\n\n  &__col-2 {\n    order: 2;\n  }\n\n  &__col-3 {\n    order: 4;\n  }\n\n  &__col-4 {\n    order: 5;\n  }\n\n  &__col-5 {\n    order: 3;\n  }\n\n  &__logo {\n    max-width: 320px;\n\n    svg {\n      max-width: 320px;\n    }\n  }\n\n  &__copyright {\n    color: var(--body-text-default);\n    font-size: px-to-rem(15px);\n    font-weight: 400;\n    line-height: px-to-rem(21px);\n    max-width: 320px;\n  }\n\n  &__links {\n    #{$self} {\n      &__link {\n        color: var(--text-link-default);\n        font-family: $copy-font;\n        font-size: px-to-rem(19px);\n        font-weight: 700;\n        line-height: px-to-rem(38px);\n\n        &:focus,\n        &:hover {\n          border-bottom: px-to-rem(1.5px) solid var(--text-link-hover-underline);\n          text-decoration: none;\n        }\n      }\n    }\n  }\n\n  &__heading-medium {\n    font-family: $copy-font;\n    font-size: px-to-rem(24px);\n    font-weight: 700;\n    line-height: px-to-rem(31.2px);\n    margin-bottom: px-to-rem(30px);\n  }\n\n  .social {\n    &__links {\n      align-items: flex-start;\n      display: flex;\n      justify-content: flex-start;\n\n      a {\n        &:not(:first-of-type) {\n          margin-left: px-to-rem(16px);\n        }\n      }\n    }\n  }\n\n  &__facebook,\n  &__twitter,\n  &__email {\n    background-repeat: no-repeat;\n    background-size: cover;\n    display: block;\n    height: px-to-rem(30px);\n    width: px-to-rem(30px);\n\n    span {\n      @include is-visually-hidden;\n    }\n  }\n\n  &__facebook {\n    background-image: url(\"data:image/svg+xml,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 18C0 8.0304 8.0304 0 18 0C27.9696 0 36 8.0304 36 18C36 27.9696 27.9696 36 18 36C8.0304 36 0 27.9696 0 18ZM20.9072 10.3847H24.7844V5.53906H20.9072C17.7224 5.53906 15.092 8.30746 15.092 11.7695V14.2619H11.2148V18.9695H15.092V30.3239H19.9376V18.9695H24.7832V14.2619H19.9376V11.7695C19.9376 10.9391 20.492 10.3847 20.9072 10.3847Z' fill='%23E66A1F'/%3E%3C/svg%3E\");\n\n    @media (hover: hover) {\n      &:hover,\n      &:focus {\n        background-image: url(\"data:image/svg+xml,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 18C0 8.0304 8.0304 0 18 0C27.9696 0 36 8.0304 36 18C36 27.9696 27.9696 36 18 36C8.0304 36 0 27.9696 0 18ZM20.9072 10.3847H24.7844V5.53906H20.9072C17.7224 5.53906 15.092 8.30746 15.092 11.7695V14.2619H11.2148V18.9695H15.092V30.3239H19.9376V18.9695H24.7832V14.2619H19.9376V11.7695C19.9376 10.9391 20.492 10.3847 20.9072 10.3847Z' fill='%23FF985A'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  &__twitter {\n    background-image: url(\"data:image/svg+xml,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 18C0 8.0304 8.0304 0 18 0C27.9696 0 36 8.0304 36 18C36 27.9696 27.9696 36 18 36C8.0304 36 0 27.9696 0 18ZM26.8624 13.9848V13.4304C27.8308 12.738 28.5232 12.0456 28.8016 10.938L28.8015 10.938C27.9711 11.2152 27.1407 11.4924 26.1712 11.6304C27.1408 11.076 27.9712 10.2456 28.2484 9.13804C27.418 9.69124 26.4484 10.1076 25.3408 10.2456C24.5104 9.41524 23.4028 8.86084 22.018 8.86084C19.5256 8.86084 17.4484 10.938 17.4484 13.4304C17.4484 13.8456 17.4484 14.2608 17.5864 14.538C13.8484 14.262 10.3864 12.462 8.17116 9.69244C7.89396 10.3848 7.61676 11.2164 7.61676 12.0468C7.61676 13.7088 8.44836 15.0936 9.69396 15.924C9.00156 15.924 8.17116 15.6468 7.61676 15.3696C7.61676 17.5848 9.27756 19.3848 11.3548 19.8C10.9384 19.938 10.5232 19.938 10.108 19.938C9.96936 19.938 9.83106 19.9035 9.69276 19.869C9.55446 19.8345 9.41616 19.8 9.27756 19.8C9.83196 21.6 11.632 22.9848 13.57 22.9848C12.0472 24.2304 9.96996 24.9228 7.89276 24.9228H6.78516C8.86236 26.1696 11.2168 27 13.8472 27C22.2928 27 26.8624 20.0772 26.8624 13.9848Z' fill='%23E66A1F'/%3E%3C/svg%3E\");\n\n    @media (hover: hover) {\n      &:hover,\n      &:focus {\n        background-image: url(\"data:image/svg+xml,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M0 18C0 8.0304 8.0304 0 18 0C27.9696 0 36 8.0304 36 18C36 27.9696 27.9696 36 18 36C8.0304 36 0 27.9696 0 18ZM26.8624 13.9848V13.4304C27.8308 12.738 28.5232 12.0456 28.8016 10.938L28.8015 10.938C27.9711 11.2152 27.1407 11.4924 26.1712 11.6304C27.1408 11.076 27.9712 10.2456 28.2484 9.13804C27.418 9.69124 26.4484 10.1076 25.3408 10.2456C24.5104 9.41524 23.4028 8.86084 22.018 8.86084C19.5256 8.86084 17.4484 10.938 17.4484 13.4304C17.4484 13.8456 17.4484 14.2608 17.5864 14.538C13.8484 14.262 10.3864 12.462 8.17116 9.69244C7.89396 10.3848 7.61676 11.2164 7.61676 12.0468C7.61676 13.7088 8.44836 15.0936 9.69396 15.924C9.00156 15.924 8.17116 15.6468 7.61676 15.3696C7.61676 17.5848 9.27756 19.3848 11.3548 19.8C10.9384 19.938 10.5232 19.938 10.108 19.938C9.96936 19.938 9.83106 19.9035 9.69276 19.869C9.55446 19.8345 9.41616 19.8 9.27756 19.8C9.83196 21.6 11.632 22.9848 13.57 22.9848C12.0472 24.2304 9.96996 24.9228 7.89276 24.9228H6.78516C8.86236 26.1696 11.2168 27 13.8472 27C22.2928 27 26.8624 20.0772 26.8624 13.9848Z' fill='%23FF985A'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  &__email {\n    background-image: url(\"data:image/svg+xml,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M18 0C8.0304 0 0 8.0304 0 18C0 27.9696 8.0304 36 18 36C27.9696 36 36 27.9696 36 18C36 8.0304 27.9696 0 18 0ZM29.3554 25.4774H6.50977V12.8774L17.8642 20.6318L29.2186 12.8774L29.3554 25.4774ZM6.64648 10.5229L18.0009 18.2773L29.4921 10.5229H6.64648Z' fill='%23E66A1F'/%3E%3C/svg%3E\");\n\n    @media (hover: hover) {\n      &:hover,\n      &:focus {\n        background-image: url(\"data:image/svg+xml,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M18 0C8.0304 0 0 8.0304 0 18C0 27.9696 8.0304 36 18 36C27.9696 36 36 27.9696 36 18C36 8.0304 27.9696 0 18 0ZM29.3554 25.4774H6.50977V12.8774L17.8642 20.6318L29.2186 12.8774L29.3554 25.4774ZM6.64648 10.5229L18.0009 18.2773L29.4921 10.5229H6.64648Z' fill='%23FF985A'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  &__signup-button {\n    .component {\n      &__button-text {\n        font-size: px-to-rem(19px);\n        font-weight: 800;\n        line-height: px-to-rem(20px);\n        padding: px-to-rem(15px) px-to-rem(34px);\n      }\n    }\n  }\n\n  @include breakpoint(medium, min) {\n    .component {\n      &__content {\n        padding: px-to-rem(80px) px-to-rem(120px);\n      }\n    }\n\n    &__row {\n      display: grid;\n      grid-template-columns: 3fr 1.5fr 2.5fr;\n      grid-gap: 15px;\n    }\n\n    &__col {\n      padding-left: 0;\n    }\n\n    &__col-1 {\n      order: 1;\n    }\n\n    &__col-2 {\n      order: 2;\n    }\n\n    &__col-3 {\n      order: 3;\n    }\n\n    &__col-4 {\n      order: 4;\n    }\n\n    &__col-5 {\n      order: 5;\n    }\n\n    &__logo {\n      max-width: 340px;\n\n      svg {\n        max-width: 340px;\n      }\n    }\n\n    .social {\n      &__links {\n        margin-top: px-to-rem(25px);\n      }\n    }\n\n    &__copyright {\n      margin-top: px-to-rem(25px);\n    }\n  }\n\n  @media screen and (min-width: '#{$min}px') and (max-width: '#{$large}px') {\n    &__row {\n      display: grid;\n      grid-template-columns: auto auto;\n      grid-gap: 15px;\n    }\n\n    &__col-1 {\n      order: 1;\n    }\n\n    &__col-2 {\n      order: 2;\n    }\n\n    &__col-3 {\n      order: 5;\n    }\n\n    &__col-4 {\n      order: 4;\n    }\n\n    &__col-5 {\n      order: 3;\n    }\n  }\n}\n", "@use 'sass:map';\n\n/**\n  HEADER COMPONENT\n**/\n\n.header {\n  background-image: url('../images/sample/landscape-16x9-mountains.jpg?as=webp');\n  margin: 0;\n  max-height: px-to-rem(100px);\n\n  &__signup {\n    &--button {\n      background-color: var(--button-default);\n      width: 100%;\n\n      &-text {\n        font-size: px-to-rem(17px);\n        font-weight: 800;\n        line-height: px-to-rem(20px);\n        padding-bottom: px-to-rem(13px);\n        padding-top: px-to-rem(13px);\n      }\n\n      &:hover,\n      &:focus {\n        background-color: var(--button-hover);\n\n        &::before {\n          transform: scale(0);\n        }\n\n        .component {\n          &__button-text {\n            background: transparent;\n            -webkit-text-fill-color: var(--button-hover-text);\n          }\n        }\n      }\n    }\n  }\n\n  &__logo {\n    align-items: center;\n    display: flex;\n    flex-direction: row;\n    justify-content: start;\n    margin-bottom: 0;\n  }\n\n  &__logo-icon {\n    margin-right: 10px;\n  }\n\n  &__logo-link {\n    color: var(--body-text-default);\n    font-family: $copy-font;\n    font-size: px-to-rem(12px);\n    font-weight: 600;\n    line-height: 1;\n    padding-top: 0;\n\n    &:hover,\n    &:focus {\n      color: inherit;\n      text-decoration: none;\n    }\n  }\n\n  &__search {\n    align-items: center;\n    display: none;\n    width: px-to-rem(345px);\n\n    &-input {\n      margin-right: 5px;\n      width: px-to-rem(320px);\n\n      input {\n        border: none;\n        font-family: $display-font;\n        font-size: px-to-rem(19px);\n        line-height: px-to-rem(20px);\n        width: 0;\n\n        &::placeholder {\n          color: var(--form-input-placeholder);\n          font-family: $display-font;\n        }\n      }\n\n      &--active {\n        input {\n          border-bottom: 2px solid #1d1c1d;\n          transition: width 0.3s 0.3s ease;\n          width: 100%;\n        }\n      }\n    }\n\n    &-icon {\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3Csvg width='27' height='25' viewBox='0 0 26 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11ZM17.9338 19.5399C16.0417 21.0781 13.6285 22 11 22C4.92487 22 0 17.0751 0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 13.3511 21.2624 15.53 20.0059 17.3177L25.772 22.5088L23.6772 24.7106L17.9338 19.5399Z' fill='%231D1C1D'/%3E%3C/svg%3E%0A\");\n      background-size: cover;\n      background-repeat: no-repeat;\n      height: px-to-rem(25px);\n      overflow: hidden;\n      position: relative;\n      text-indent: -999rem;\n      width: px-to-rem(26px);\n\n      &:hover {\n        background-image: url(\"data:image/svg+xml,%3Csvg width='27' height='25' viewBox='0 0 27 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M19.8438 11C19.8438 15.4183 16.262 19 11.8438 19C7.42547 19 3.84375 15.4183 3.84375 11C3.84375 6.58172 7.42547 3 11.8438 3C16.262 3 19.8438 6.58172 19.8438 11ZM18.7776 19.5399C16.8854 21.0781 14.4723 22 11.8438 22C5.76862 22 0.84375 17.0751 0.84375 11C0.84375 4.92487 5.76862 0 11.8438 0C17.9189 0 22.8438 4.92487 22.8438 11C22.8438 13.3511 22.1061 15.53 20.8497 17.3177L26.6157 22.5088L24.5209 24.7106L18.7776 19.5399Z' fill='%23E66A1F'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  &__hamburger-menu {\n    background-color: var(--site-background);\n    height: px-to-rem(3px);\n    position: relative;\n    transition: 0.3s ease;\n    width: px-to-rem(15px);\n\n    &::after,\n    &::before {\n      background-color: var(--site-background);\n      content: '';\n      height: px-to-rem(3px);\n      position: absolute;\n      top: px-to-rem(7px);\n      transition: 0.3s ease;\n    }\n\n    &::after {\n      width: px-to-rem(19px);\n    }\n\n    &::before {\n      top: px-to-rem(-7px);\n      width: px-to-rem(24px);\n    }\n\n    &--active {\n      background-color: var(--site-background);\n\n      &::after,\n      &::before {\n        background-color: var(--neutral-white);\n        top: 0;\n        transition: 0.3s ease;\n        width: px-to-rem(24px);\n      }\n\n      &::after {\n        transform: rotate(45deg);\n        transition: top 0.3s ease, transform 0.3s 0.3s ease;\n      }\n\n      &::before {\n        transform: rotate(-45deg);\n        transition: bottom 0.3s ease, transform 0.3s 0.3s ease;\n      }\n    }\n  }\n\n  &__hamburger {\n    align-items: center;\n    cursor: pointer;\n    display: flex;\n    height: 100%;\n    justify-content: center;\n    transition: 0.3s ease;\n    width: px-to-rem(60px);\n\n    &:hover {\n      .header__hamburger-menu {\n        &::after,\n        &::before {\n          background-color: var(--text-link-hover);\n        }\n      }\n\n      &:not(.header__hamburger--active) {\n        .header__hamburger-menu {\n          background-color: var(--text-link-hover);\n\n          &::after,\n          &::before {\n            background-color: var(--text-link-hover);\n          }\n        }\n      }\n    }\n  }\n\n  &__menu-active {\n    .header {\n      &__logo-link {\n        svg {\n          path {\n            fill: var(--text-link-hover);\n          }\n        }\n      }\n    }\n\n    .component {\n      &__content {\n        background-color: var(--body-text-default);\n\n        &--bottom {\n          display: block;\n\n          .header {\n            &__search,\n            &__signup {\n              display: block;\n              margin-top: px-to-rem(30px);\n            }\n\n            &__search {\n              display: flex;\n              margin-bottom: px-to-rem(55px);\n              margin-top: px-to-rem(45px);\n              width: 100%;\n\n              &-input {\n                width: calc(100% - 25px);\n\n                input {\n                  background-color: transparent;\n                  border-bottom: 2px solid var(--neutral-white);\n                  color: var(--neutral-white);\n                  width: calc(100% - 25px);\n\n                  &::placeholder {\n                    color: var(--neutral-white);\n                  }\n                }\n              }\n\n              &-icon {\n                background-image: url(\"data:image/svg+xml,%3Csvg width='26' height='25' viewBox='0 0 26 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11ZM17.6699 19.7478C15.8197 21.1608 13.5078 22 11 22C4.92487 22 0 17.0751 0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 13.4849 21.1761 15.7773 19.7865 17.619L25.0699 22.5088L23.0322 24.7106L17.6699 19.7478Z' fill='white'/%3E%3C/svg%3E\");\n\n                &:hover {\n                  background-image: url(\"data:image/svg+xml,%3Csvg width='26' height='25' viewBox='0 0 26 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11ZM17.6699 19.7478C15.8197 21.1608 13.5078 22 11 22C4.92487 22 0 17.0751 0 11C0 4.92487 4.92487 0 11 0C17.0751 0 22 4.92487 22 11C22 13.4849 21.1761 15.7773 19.7865 17.619L25.0699 22.5088L23.0322 24.7106L17.6699 19.7478Z' fill='white'/%3E%3C/svg%3E\");\n                }\n              }\n            }\n          }\n\n          @include breakpoint(min, min) {\n            .header__search,\n            .header__signup {\n              display: none;\n            }\n          }\n        }\n\n        &--top {\n          .header {\n            &__search {\n              display: none;\n            }\n\n            &__signup {\n              display: none;\n\n              @include breakpoint(min, min) {\n                display: block;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  &__navigation-row {\n    display: flex;\n    flex-flow: column wrap;\n    justify-content: space-between;\n\n    .header__navigation-col {\n      padding: 20px 0;\n      width: 100%;\n\n      @include breakpoint(mobile, min) {\n        width: 30%;\n      }\n\n      @include breakpoint(tablet, min) {\n        width: 50%;\n      }\n\n      @include breakpoint(desktop, min) {\n        width: 100%;\n      }\n    }\n\n    &__nav-item {\n      font-family: $copy-font;\n      font-size: px-to-rem(19px);\n      font-weight: 400;\n      line-height: px-to-rem(38px);\n\n      a {\n        color: var(--neutral-white);\n        width: 100%;\n\n        &:hover {\n          text-decoration: none;\n        }\n      }\n\n      &--heading {\n        font-size: px-to-rem(24px);\n        line-height: px-to-rem(34px);\n\n        @include breakpoint(min, min) {\n          font-size: px-to-rem(36px);\n          font-weight: 800;\n          line-height: px-to-rem(38px);\n        }\n\n        .icon__up-arrow,\n        .icon__down-arrow {\n          display: block;\n\n          @include breakpoint(min, min) {\n            display: none;\n          }\n        }\n\n        .header__nav-child-items {\n          height: 0;\n          margin-left: px-to-rem(35px);\n          opacity: 0;\n          transition: all 0.3s ease;\n          visibility: hidden;\n\n          &-active {\n            height: 100%;\n            opacity: 1;\n            visibility: visible;\n          }\n\n          @include breakpoint(min, min) {\n            height: 100%;\n            margin-left: 0;\n            opacity: 1;\n            padding-top: px-to-rem(3px);\n            visibility: visible;\n          }\n        }\n      }\n    }\n\n    @include breakpoint(min, min) {\n      flex-direction: row;\n    }\n  }\n\n  .component {\n    &__content {\n      background-color: var(--neutral-white);\n      padding: 3.5px 22.5px;\n      position: relative;\n      width: 100%;\n      z-index: 100;\n\n      &--right-align {\n        display: flex;\n        flex-direction: row;\n      }\n\n      &--top {\n        display: flex;\n        flex-direction: row;\n        justify-content: space-between;\n\n        .header {\n          &__signup {\n            display: none;\n          }\n        }\n      }\n\n      &--bottom {\n        background-color: var(--site-background);\n        color: var(--neutral-white);\n        display: none;\n        left: 0;\n        height: calc(100vh - 65px);\n        overflow-y: auto;\n        padding: 30px 35px;\n        position: absolute;\n        top: px-to-rem(68px);\n        visibility: visible;\n        width: 100%;\n      }\n    }\n\n    &__background {\n      background-color: var(--neutral-white);\n    }\n  }\n\n  &[data-visible='false'] {\n    .component__content {\n      position: fixed;\n    }\n  }\n\n  &[data-visible='true'] {\n    .component__content {\n      position: relative;\n    }\n  }\n\n  @include breakpoint(min, min) {\n    &__search {\n      display: flex;\n    }\n\n    &__hamburger {\n      &--active {\n        margin-top: -15px;\n      }\n    }\n\n    &__signup {\n      &--button {\n        min-width: px-to-rem(170px);\n        top: px-to-rem(-20px);\n\n        &-text {\n          padding: px-to-rem(30px) px-to-rem(42.5px);\n        }\n      }\n    }\n\n    &__menu-active {\n      .component {\n        &__content {\n          &--bottom {\n            .header {\n              &__search,\n              &__signup {\n                display: none;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .component {\n      &__content {\n        max-height: px-to-rem(100px);\n        padding: 20px 45px;\n\n        &--bottom {\n          padding: 40px 80px;\n          top: px-to-rem(100px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(min, max) {\n    &__logo-link {\n      svg {\n        width: px-to-rem(195px);\n      }\n    }\n  }\n}\n", ".page-template-default,\n.news-template-default,\n.error404 {\n  .main-content {\n    padding-left: $mobile-gutter;\n    padding-right: $mobile-gutter;\n\n    > :where(:not(.alignleft, .alignright, .alignfull, #wpadminbar, .footer)) {\n      max-width: $max-context-width;\n      margin-left: auto !important;\n      margin-right: auto !important;\n    }\n\n    > .alignwide {\n      max-width: $max-wide-content;\n\n      &.ta-cta-panel {\n        max-width: $max-xwide-content;\n      }\n    }\n\n    > .alignfull {\n      width: calc(100% + $mobile-gutter + $mobile-gutter);\n      margin-left: -$mobile-gutter;\n    }\n\n    > .alignfull.has-background + .alignfull.has-background {\n      margin-top: 0;\n    }\n\n    > .alignfull.has-background:has(+ .alignfull.has-background) {\n      margin-bottom: 0;\n    }\n\n    > .has-background.alignfull:first-child {\n      margin-top: 0;\n    }\n\n    > .has-background.alignfull:last-child {\n      margin-bottom: 0;\n    }\n\n    > *:last-child:not(.alignfull) {\n      margin-bottom: $mobile-component-spacing;\n    }\n\n    > .ta-emphasized-link {\n      display: block;\n      margin-bottom: $mobile-text-spacing;\n      margin-top: $mobile-text-spacing;\n      width: 100%;\n\n      + h1,\n      + h2,\n      + h3,\n      + h4,\n      + h5,\n      + h6 {\n        &.wp-block-heading {\n          margin-top: $mobile-component-spacing;\n        }\n      }\n    }\n\n    > .ta-colored-section {\n      > .ta-emphasized-link {\n        display: block;\n        margin-bottom: $mobile-text-spacing;\n        margin-top: $mobile-text-spacing;\n        width: 100%;\n\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5,\n        + h6 {\n          &.wp-block-heading {\n            margin-top: $mobile-component-spacing;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    .main-content {\n      padding-left: $tablet-gutter;\n      padding-right: $tablet-gutter;\n\n      > .alignfull {\n        width: calc(100% + $tablet-gutter + $tablet-gutter);\n        margin-left: -$tablet-gutter;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    .main-content {\n      > .has-background.alignfull:first-child {\n        margin-top: 0;\n      }\n\n      > .has-background.alignfull:last-child {\n        margin-bottom: 0;\n      }\n\n      > *:last-child:not(.alignfull) {\n        margin-bottom: $desktop-component-spacing;\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    .main-content {\n      padding-left: $desktop-gutter;\n      padding-right: $desktop-gutter;\n\n      > .alignfull {\n        width: calc(100% + $desktop-gutter + $desktop-gutter);\n        margin-left: -$desktop-gutter;\n      }\n\n      > .alignfull.has-background + .alignfull.has-background {\n        margin-top: 0;\n      }\n\n      > .alignfull.has-background:has(+ .alignfull.has-background) {\n        margin-bottom: 0;\n      }\n\n      > *:last-child:not(.alignfull) {\n        margin-bottom: $desktop-component-spacing;\n      }\n\n      > .ta-emphasized-link {\n        margin-bottom: $desktop-text-spacing;\n        margin-top: $desktop-text-spacing;\n\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5,\n        + h6 {\n          &.wp-block-heading {\n            margin-top: $desktop-component-spacing;\n          }\n        }\n      }\n\n      > .ta-colored-section {\n        > .ta-emphasized-link {\n          margin-bottom: $desktop-text-spacing;\n          margin-top: $desktop-text-spacing;\n\n          + h1,\n          + h2,\n          + h3,\n          + h4,\n          + h5,\n          + h6 {\n            &.wp-block-heading {\n              margin-top: $desktop-component-spacing;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "/**\n  PADDING CONSTANTS\n  global padding constants\n**/\n\n$mobile-gutter: 25px;\n$tablet-gutter: 40px;\n$desktop-gutter: 50px;\n$mobile-component-spacing: 50px;\n$desktop-component-spacing: 100px;\n$mobile-text-spacing: 30px;\n$desktop-text-spacing: 40px;\n$mobile-nav-height: 105px;\n$desktop-nav-height: 105px;\n\n/* stylelint-disable custom-property-pattern */\n$max-context-width: var(--wp--style--global--content-size);\n$max-wide-content: var(--wp--style--global--wide-size);\n$max-xwide-content: 1340px;\n$max-xxwide-content: 1440px;\n/* stylelint-enable custom-property-pattern */\n"], "names": [], "sourceRoot": ""}