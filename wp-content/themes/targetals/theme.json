{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "settings": {"color": {"defaultPalette": false, "defaultGradients": false, "customDuotone": false, "customGradient": false, "palette": [{"slug": "foreground", "color": "#000000", "name": "Foreground"}, {"slug": "background", "color": "#ffffff", "name": "Background"}, {"slug": "primary", "color": "#0000EE", "name": "Primary"}, {"slug": "secondary", "color": "#00FFFF", "name": "Secondary"}, {"slug": "tertiary", "color": "#00FFFF", "name": "Tertiary"}, {"slug": "quaternary", "color": "#00FFFF", "name": "Quaternary"}, {"slug": "quinary", "color": "#00FFFF", "name": "Quinary"}, {"slug": "senary", "color": "#00FFFF", "name": "Senary"}, {"slug": "septenary", "color": "#00FFFF", "name": "Septenary"}, {"slug": "octonary", "color": "#00FFFF", "name": "Octonary"}, {"slug": "nonary", "color": "#00FFFF", "name": "Nonary"}, {"slug": "denary", "color": "#00FFFF", "name": "Denary"}], "gradients": [], "link": false}, "custom": {"border-radius": {"tiny": "3px", "small": "8px", "medium": "12px", "large": "50%"}, "border-width": {"tiny": "1px", "small": "2px", "medium": "3px", "large": "4px"}, "box-shadow": {"1": "0px 2px 8px rgba(33, 33, 33, 0.12)", "2": "0px 3px 10px rgba(33, 33, 33, 0.25)"}, "color": {"link": "#0000EE", "neutral-50": "#FAFAFA", "neutral-100": "#F5F5F5", "neutral-200": "#EEEEEE", "neutral-300": "#E0E0E0", "neutral-400": "#BDBDBD", "neutral-500": "#9E9E9E", "neutral-600": "#757575", "neutral-700": "#616161", "neutral-800": "#424242", "neutral-900": "#212121"}, "layout": {"content": "800px", "wide": "1200px", "sidebar": "336px", "page": "var(--wp--custom--layout--content)", "padding": "16px", "block-gap": "16px", "block-gap-large": "40px"}, "letter-spacing": {"none": "normal", "tight": "-.01em", "loose": ".05em", "looser": ".1em"}, "line-height": {"tiny": 1.1, "small": 1.2, "medium": 1.4, "normal": 1.6}}, "spacing": {"blockGap": false, "margin": false, "padding": false, "units": ["%", "px", "em", "rem", "vh", "vw"]}, "typography": {"dropCap": false, "lineHeight": false, "customFontSize": false, "letterSpacing": false, "fontFamilies": [{"fontFamily": "-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif", "name": "System Sans Serif", "slug": "system-sans-serif"}, {"fontFamily": "-apple-system-ui-serif, ui-serif, <PERSON><PERSON>, Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol", "name": "System Serif", "slug": "system-serif"}], "fontSizes": [{"name": "Gargantuan", "size": "clamp(2.75rem, 5.2vw, 3.25rem)", "slug": "gargantuan"}, {"name": "Colossal", "size": "clamp(2.5rem, 4.8vw, 3rem)", "slug": "colossal"}, {"name": "Gigantic", "size": "clamp(2.125rem, 4.4vw, 2.75rem)", "slug": "gigantic"}, {"name": "Jumbo", "size": "clamp(2rem, 4vw, 2.5rem)", "slug": "jumbo"}, {"name": "<PERSON>ge", "size": "clamp(1.875rem, 3.6vw, 2.25rem)", "slug": "huge"}, {"name": "Big", "size": "clamp(1.75rem, 3.2vw, 2rem)", "slug": "big"}, {"name": "Extra Large", "size": "clamp(1.5rem, 2.8vw, 1.75rem)", "slug": "x-large"}, {"name": "Large", "size": "1.25rem", "slug": "large"}, {"name": "Medium", "size": "1.125rem", "slug": "medium"}, {"name": "Small", "size": "1rem", "slug": "small"}, {"name": "Tiny", "size": "0.875rem", "slug": "tiny"}, {"name": "Min", "size": "0.75rem", "slug": "min"}]}, "layout": {"contentSize": "var(--wp--custom--layout--content)", "wideSize": "var(--wp--custom--layout--wide)"}, "border": {"color": false, "radius": false, "style": false, "width": false}, "blocks": {"core/button": {"border": {"radius": false}}, "core/group": {"color": {"background": true}}}}, "styles": {"color": {"background": "var(--wp--preset--color--background)", "text": "var(--wp--preset--color--foreground)"}, "elements": {"link": {"color": {"text": "var(--wp--custom--color--link)"}}, "button": {"color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--background)"}, "border": {"width": "0px", "radius": "var(--wp--custom--border-radius--tiny)"}, "spacing": {"padding": "11px 16px"}, "typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontWeight": "700", "lineHeight": "var(--wp--custom--line-height--medium)"}}, "heading": {"typography": {"fontFamily": "var(--wp--preset--font-family--system-serif)", "fontWeight": "900", "lineHeight": "var(--wp--custom--line-height--small)"}}, "h1": {"typography": {"fontSize": "var(--wp--preset--font-size--colossal)", "lineHeight": "var(--wp--custom--line-height--tiny)"}}, "h2": {"typography": {"fontSize": "var(--wp--preset--font-size--huge)"}}, "h3": {"typography": {"fontSize": "var(--wp--preset--font-size--big)"}}, "h4": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)"}}, "h5": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)"}}, "h6": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)"}}}, "spacing": {"blockGap": "var(--wp--custom--layout--block-gap)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--system-sans-serif)", "lineHeight": "var(--wp--custom--line-height--normal)", "fontSize": "var(--wp--preset--font-size--medium)"}}}