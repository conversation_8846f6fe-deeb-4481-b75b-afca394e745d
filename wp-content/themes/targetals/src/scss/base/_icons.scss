/**
  $ICONS
**/

@font-face {
  font-family: icons;
  src: url('../../fonts/icons.eot');
  src: url('../../fonts/icons.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/icons.ttf') format('truetype'),
    url('../../fonts/icons.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

[class^='icon-'],
[class*=' icon-'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: icons, sans-serif !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
