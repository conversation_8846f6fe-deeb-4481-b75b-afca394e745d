<?php
/**
 * Vertical Video Block template.
 *
 * @param array $block The block settings and attributes.
 */

$video = get_field( 'video' );

$template = array(
  array( 'targetals/ta-caption', 
    array(
      'placeholder' => 'Add a video caption.',
    )
  ),
);

$allowed_blocks = array( 'targetals/ta-caption' );

// Add your own class to the block wrapper
$wrapper_attributes = get_block_wrapper_attributes([
  'class' => 'verticalvideo',
]);

?>

<?php // Block preview
if( !empty( $block['data']['use_preview_image'] ) ) { ?>
  <figure>
    <img src="<?php echo get_template_directory_uri(); ?>/blocks/acf-blocks/ta-verticalvideo/preview.jpg" alt="Preview of what the vertical video block appears minimized">
  </figure>
<?php } else { 
  // Base gutenberg block settings
  $template = esc_attr( wp_json_encode( $template ) );
  $allowed_blocks = esc_attr( wp_json_encode( $allowed_blocks ));
  
  // Render the block.
 ?>

<div <?php echo $wrapper_attributes; ?>>
  <figure class="verticalvideo__figure">
    <?php if(!empty($video['url'])) { ?>
      <div class="video_wrapper paused">
        <video class="verticalvideo__video" controlslist="nofullscreen" controls="false" playsinline metadata>
          <source src="<?= $video['url'] ?>" type="video/mp4">
        </video>
        <button class="play-button">
          <span><svg xmlns="http://www.w3.org/2000/svg" width="51" height="50" viewBox="0 0 51 50" fill="none">
            <path d="M37.4773 25.238L18.4297 13.3332V37.1427L37.4773 25.238Z" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="25.332" cy="25" r="24.5" />
          </svg></span>
        </button>
      </div>
    <?php } ?>
    <InnerBlocks template="<?= $template ?>" templateLock="all" allowedBlocks="<?= $allowed_blocks ?>"/>
  </figure>
</div

<?php } ?>