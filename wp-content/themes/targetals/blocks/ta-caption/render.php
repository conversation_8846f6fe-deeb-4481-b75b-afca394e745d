<?php
/**
 * Caption Block template.
 */

// Supports a custom class and styles classes
$classes = array( 'caption' );
$styles = '';

if ( ! empty( $attributes['style']['color']['text'] ) ) {
	$styles = $styles . 'color:' . esc_attr( $attributes['style']['color']['text'] ) . ';';
} elseif ( ! empty( $attributes['textColor'] ) ) {
	$classes[] = 'has-text-color';
	$classes[] = 'has-' . $attributes['textColor'] . '-color';
}

// Support custom "anchor" values.
$anchor = '';
if ( ! empty( $attributes['anchor'] ) ) {
  $anchor = 'id="' . esc_attr( $attributes['anchor'] ) . '" ';
}

// Add your own class to the block wrapper
$wrapper_attributes = get_block_wrapper_attributes([
  'class' => implode( ' ', $classes ),
  'style' => $styles,
]);

?>

<figcaption <?php echo $wrapper_attributes; ?> <?php echo $anchor; ?>>
  <?php if (!empty($attributes['title'])) {?>
    <p class="caption__title"><?php echo $attributes['title'] ?></p>    
  <?php }?>
  <?php if (!empty($attributes['text'])) {?>
    <p class="caption__text"><?php echo $attributes['text'] ?></p>    
  <?php }?>
</figcaption>