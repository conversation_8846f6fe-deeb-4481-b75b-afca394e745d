<?php

if ( ! defined( 'ABSPATH' ) ) {
	$protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
	header( $protocol . ' 404 Not Found', true );
	echo '<h1>404 Not Found</h1>';
	exit;
}

return [
	'attributes' => [
		'anchor' => array(
			'type'      => 'string',
			'default'   => ''
		),
		'text' => array(
			'type'      => 'string',
			'default'   => ''
		),
		'title' => array(
			'type'      => 'string',
			'default'   => ''
		)
	],
	'render' => true,
];
