import { RichText, useBlockProps } from '@wordpress/block-editor';
import { registerBlockType } from '@wordpress/blocks';
import { useEffect } from '@wordpress/element';
import metadata from './block.json';

registerBlockType(metadata.name, {
  ...metadata,
  edit: EditComponent,
  save: () => null // dynamic block; rendering handled by P<PERSON>
});

function EditComponent({ attributes, setAttributes }) {
  const {
    anchor,
    titlePlaceholder,
    title,
    placeholder,
    text,
    textColor,
    style = {}
  } = attributes;

  // Sync textColor to style.color.text
  useEffect(() => {
    if (textColor && (!style.color || style.color.text !== textColor)) {
      setAttributes({
        style: {
          ...style,
          color: {
            ...style.color,
            text: textColor
          }
        }
      });
    }
  }, [textColor]);

  // Get block props, including anchor, className, style (text color), etc.
  const blockProps = useBlockProps({
    id: anchor || undefined,
    className: 'caption' // your custom block class
  });

  return (
    <figcaption {...blockProps}>
      {titlePlaceholder && (
        <RichText
          placeholder={titlePlaceholder}
          tagName="p"
          allowedFormats={[
            'core/bold',
            'core/italic',
            'core/link',
            'core/footnote',
            'core/subscript',
            'core/superscript'
          ]}
          className="caption__title"
          value={title}
          onChange={(text) => setAttributes({ title: text })}
        />
      )}
      <RichText
        placeholder={placeholder}
        tagName="p"
        allowedFormats={[
          'core/bold',
          'core/italic',
          'core/link',
          'core/footnote',
          'core/subscript',
          'core/superscript'
        ]}
        className="caption__text"
        value={text}
        onChange={(text) => setAttributes({ text: text })}
      />
    </figcaption>
  );
}
