<?php

if ( ! defined( 'ABSPATH' ) ) {
	$protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
	header( $protocol . ' 404 Not Found', true );
	echo '<h1>404 Not Found</h1>';
	exit;
}

// add_action( 'acf/init', 'ta_acf_init' );
// function ta_acf_init() {
// 	// check function exists
// 	if (function_exists('acf_register_block')) {
// 		register_block_type( get_template_directory() . '/blocks/ta-verticalvideo' );
// 	}
// }

add_filter( 'block_categories_all', 'ta_block_category', 6, 2 );
function ta_block_category( $categories, $post ) {
	return array_merge(
		array(
			array(
				'slug'  => 'targetals-blocks',
				'title' => __( 'Target ALS Blocks', 'targetals-blocks' ),
			),
			array(
				'slug'  => 'targetals-lists',
				'title' => __( 'Target ALS List Blocks', 'targetals-lists' ),
			),
			array(
				'slug'  => 'targetals-sections',
				'title' => __( 'Target ALS Section Blocks', 'targetals-sections' ),
			),
			array(
				'slug'  => 'targetals-heroes',
				'title' => __( 'Target ALS Hero Blocks', 'targetals-lists' ),
			),
		),
		$categories,
	);
}

add_filter( 'allowed_block_types_all', 'ta_allowed_block_types', 10, 2 );
function ta_allowed_block_types() {
	return array(
		// allow core blocks
		'core/html',
		'core/paragraph',
		'core/post-title',
		'core/button',
		'core/buttons',
		'core/column',
		'core/columns',
		'core/group',
		'core/details',
		'core/heading',
		'core/list',
		'core/list-item',
		'core/image',
		'core/pullquote',
		'core/separator',
		'core/media-text',
		'core/embed',
		'core-embed/spotify',
		'core/spacer',
    	'core/block',
		'core/pattern',

		// allow target als blocks
		'targetals/ta-caption',
		'targetals/ta-heading',
		
		// allow target als acf blocks
		'targetals/ta-verticalvideo',
	);
}
