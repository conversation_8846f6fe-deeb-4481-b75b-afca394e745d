# Dynamic Gutenberg Blocks Guide

This directory contains custom dynamic Gutenberg blocks for the Target ALS theme. Dynamic blocks use JavaScript for the editor interface and PHP for frontend rendering, providing the best of both worlds.

## How Dynamic Blocks Work

Dynamic blocks in this theme use a custom `JSXBlock` system that:

1. **Automatically registers blocks** by scanning directories with `config.php` files
2. **Compiles JavaScript** using Webpack for the editor interface
3. **Renders with PHP** on the frontend for dynamic content
4. **Supports block.json** for modern block registration

## Block Types in This Theme

### Dynamic Blocks (JavaScript + PHP)
- `ta-heading/` - Custom heading block with level controls
- `ta-caption/` - Enhanced caption block with rich text

### ACF Blocks (PHP + ACF)
- `acf-blocks/ta-verticalvideo/` - Video block with ACF fields

## Creating a New Dynamic Block

Follow these steps to create a new dynamic Gutenberg block:

### 1. Create Block Directory

```bash
mkdir blocks/ta-yourblockname
```

### 2. Create Required Files

#### `block.json` (Required)
Modern block registration file:

```json
{
  "apiVersion": 2,
  "name": "targetals/ta-yourblockname",
  "title": "Your Block Title",
  "category": "targetals-blocks",
  "icon": "text",
  "description": "Description of your block",
  "keywords": ["keyword1", "keyword2"],
  "attributes": {
    "content": {
      "type": "string",
      "default": ""
    },
    "anchor": {
      "type": "string",
      "default": ""
    }
  },
  "supports": {
    "anchor": true,
    "color": {
      "text": true,
      "background": false
    }
  },
  "example": {
    "attributes": {
      "content": "Example content"
    }
  },
  "editorScript": "file:./index.js",
  "render": "file:./render.php"
}
```

#### `config.php` (Required)
Configuration for the JSXBlock system:

```php
<?php

if ( ! defined( 'ABSPATH' ) ) {
    $protocol = isset( $_SERVER['SERVER_PROTOCOL'] ) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.1';
    header( $protocol . ' 404 Not Found', true );
    echo '<h1>404 Not Found</h1>';
    exit;
}

return [
    'attributes' => [
        'content' => [
            'type' => 'string',
            'default' => '',
        ],
        'anchor' => [
            'type' => 'string',
            'default' => '',
        ],
    ],
    'render' => true, // Enable PHP rendering
];
```

#### `index.js` (Required)
JavaScript for the block editor:

```javascript
import { RichText, useBlockProps } from '@wordpress/block-editor';
import { registerBlockType } from '@wordpress/blocks';
import metadata from './block.json';

registerBlockType(metadata.name, {
  ...metadata,
  edit: EditComponent,
  save: () => null // Dynamic block; rendering handled by PHP
});

function EditComponent({ attributes, setAttributes }) {
  const { content, anchor } = attributes;

  const blockProps = useBlockProps({
    id: anchor || undefined,
    className: 'your-block-class'
  });

  return (
    <div {...blockProps}>
      <RichText
        placeholder="Enter your content..."
        tagName="p"
        allowedFormats={[
          'core/bold',
          'core/italic',
          'core/link'
        ]}
        value={content}
        onChange={(newContent) => setAttributes({ content: newContent })}
      />
    </div>
  );
}
```

#### `render.php` (Required)
PHP template for frontend rendering:

```php
<?php
/**
 * Your Block Name template.
 *
 * @param array $attributes Block attributes
 * @param string $content Block content
 * @param WP_Block $block Block instance
 */

// Get attributes
$content = $attributes['content'] ?? '';
$anchor = $attributes['anchor'] ?? '';

// Build wrapper attributes
$wrapper_attributes = get_block_wrapper_attributes([
    'class' => 'your-block-class',
    'id' => $anchor ?: null,
]);

?>

<div <?php echo $wrapper_attributes; ?>>
    <?php if (!empty($content)): ?>
        <p><?php echo wp_kses_post($content); ?></p>
    <?php endif; ?>
</div>
```

### 3. Register the Block

Add your block to the allowed blocks list in `/blocks/register-ta-blocks.php`:

```php
function ta_allowed_block_types() {
    return array(
        // ... existing blocks ...
        
        // allow target als blocks
        'targetals/ta-caption',
        'targetals/ta-heading',
        'targetals/ta-yourblockname', // Add your block here
    );
}
```

### 4. Build the Block

The block JavaScript will be automatically compiled by Webpack when you run:

```bash
npm run build
# or for development
npm run start
```

## Advanced Features

### Block Controls

Add toolbar controls to your block:

```javascript
import { BlockControls, ToolbarGroup, ToolbarButton } from '@wordpress/block-editor';

function EditComponent({ attributes, setAttributes }) {
  return (
    <>
      <BlockControls>
        <ToolbarGroup>
          <ToolbarButton
            icon="admin-bold"
            label="Toggle Bold"
            onClick={() => setAttributes({ isBold: !attributes.isBold })}
            isPressed={attributes.isBold}
          />
        </ToolbarGroup>
      </BlockControls>
      {/* Your block content */}
    </>
  );
}
```

### Inspector Controls

Add sidebar controls:

```javascript
import { InspectorControls } from '@wordpress/block-editor';
import { PanelBody, TextControl, ToggleControl } from '@wordpress/components';

function EditComponent({ attributes, setAttributes }) {
  return (
    <>
      <InspectorControls>
        <PanelBody title="Block Settings">
          <TextControl
            label="Custom Text"
            value={attributes.customText}
            onChange={(value) => setAttributes({ customText: value })}
          />
          <ToggleControl
            label="Enable Feature"
            checked={attributes.enableFeature}
            onChange={(value) => setAttributes({ enableFeature: value })}
          />
        </PanelBody>
      </InspectorControls>
      {/* Your block content */}
    </>
  );
}
```

### Color Support

Enable color support in block.json and handle in JavaScript:

```javascript
import { useBlockProps } from '@wordpress/block-editor';

function EditComponent({ attributes }) {
  const { textColor, style } = attributes;
  
  const blockProps = useBlockProps({
    className: textColor ? `has-${textColor}-color has-text-color` : '',
    style: {
      color: style?.color?.text
    }
  });

  return <div {...blockProps}>Content</div>;
}
```

### InnerBlocks Support

For blocks that contain other blocks:

```javascript
import { InnerBlocks, useBlockProps } from '@wordpress/block-editor';

function EditComponent() {
  const blockProps = useBlockProps();
  
  const ALLOWED_BLOCKS = ['core/paragraph', 'core/heading'];
  const TEMPLATE = [
    ['core/heading', { placeholder: 'Enter heading...' }],
    ['core/paragraph', { placeholder: 'Enter content...' }]
  ];

  return (
    <div {...blockProps}>
      <InnerBlocks
        allowedBlocks={ALLOWED_BLOCKS}
        template={TEMPLATE}
        templateLock="all"
      />
    </div>
  );
}
```

## Block Categories

Available categories for Target ALS blocks:

- `targetals-blocks` - General Target ALS blocks
- `targetals-lists` - List-related blocks
- `targetals-sections` - Section blocks
- `targetals-heroes` - Hero blocks

## Best Practices

1. **Use block.json**: Modern block registration method
2. **Dynamic rendering**: Use PHP for dynamic content
3. **Sanitize output**: Always sanitize content in render.php
4. **Responsive design**: Ensure blocks work on all devices
5. **Accessibility**: Include proper ARIA labels and semantic HTML
6. **Performance**: Minimize JavaScript bundle size
7. **Consistent naming**: Use `ta-` prefix for block names
8. **Error handling**: Handle missing attributes gracefully

## File Structure

```
blocks/ta-yourblock/
├── block.json          # Block registration (modern)
├── config.php          # JSXBlock configuration
├── index.js            # Editor JavaScript
└── render.php          # Frontend PHP template
```

## Webpack Integration

Blocks are automatically built by Webpack:

- **Entry points**: Automatically detected from `blocks/*/index.js`
- **Output**: Compiled to `dist/js/[blockname].js`
- **Dependencies**: WordPress dependencies are externalized
- **Hot reload**: Available in development mode

## Troubleshooting

- **Block not appearing**: Check `ta_allowed_block_types()` registration
- **JavaScript errors**: Check browser console and build output
- **Styling issues**: Ensure CSS is compiled and classes are correct
- **Attributes not saving**: Verify attribute definitions match in all files
- **PHP errors**: Check render.php for syntax errors

## Resources

- [WordPress Block Editor Handbook](https://developer.wordpress.org/block-editor/)
- [Block API Reference](https://developer.wordpress.org/block-editor/reference-guides/block-api/)
- [RichText Component](https://developer.wordpress.org/block-editor/reference-guides/components/rich-text/)
- [useBlockProps Hook](https://developer.wordpress.org/block-editor/reference-guides/packages/packages-block-editor/#useblockprops)
