!function(){var e={440:function(e,t){var r,o;o=this,void 0===(r=function(){return o.svg4everybody=function(){
/*! svg4everybody v2.1.9 | github.com/jonathantneal/svg4everybody */
function e(e,t,r){if(r){var o=document.createDocumentFragment(),a=!t.hasAttribute("viewBox")&&r.getAttribute("viewBox");a&&t.setAttribute("viewBox",a);for(var n=r.cloneNode(!0);n.childNodes.length;)o.appendChild(n.firstChild);e.appendChild(o)}}function t(t){t.onreadystatechange=function(){if(4===t.readyState){var r=t._cachedDocument;r||((r=t._cachedDocument=document.implementation.createHTMLDocument("")).body.innerHTML=t.responseText,t._cachedTarget={}),t._embeds.splice(0).map((function(o){var a=t._cachedTarget[o.id];a||(a=t._cachedTarget[o.id]=r.getElementById(o.id)),e(o.parent,o.svg,a)}))}},t.onreadystatechange()}function r(r){function a(){for(var r=0;r<f.length;){var c=f[r],l=c.parentNode,s=o(l),d=c.getAttribute("xlink:href")||c.getAttribute("href");if(!d&&i.attributeName&&(d=c.getAttribute(i.attributeName)),s&&d){if(n)if(!i.validate||i.validate(d,s,c)){l.removeChild(c);var p=d.split("#"),h=p.shift(),v=p.join("#");if(h.length){var b=u[h];b||((b=u[h]=new XMLHttpRequest).open("GET",h),b.send(),b._embeds=[]),b._embeds.push({parent:l,svg:s,id:v}),t(b)}else e(l,s,document.getElementById(v))}else++r,++m}else++r}(!f.length||f.length-m>0)&&g(a,67)}var n,i=Object(r),c=/\bTrident\/[567]\b|\bMSIE (?:9|10)\.0\b/,l=/\bAppleWebKit\/(\d+)\b/,s=/\bEdge\/12\.(\d+)\b/,d=/\bEdge\/.(\d+)\b/,p=window.top!==window.self;n="polyfill"in i?i.polyfill:c.test(navigator.userAgent)||(navigator.userAgent.match(s)||[])[1]<10547||(navigator.userAgent.match(l)||[])[1]<537||d.test(navigator.userAgent)&&p;var u={},g=window.requestAnimationFrame||setTimeout,f=document.getElementsByTagName("use"),m=0;n&&a()}function o(e){for(var t=e;"svg"!==t.nodeName.toLowerCase()&&(t=t.parentNode););return t}return r}()}.apply(t,[]))||(e.exports=r)}},t={};function r(o){var a=t[o];if(void 0!==a)return a.exports;var n=t[o]={exports:{}};return e[o].call(n.exports,n,n.exports,r),n.exports}!function(){"use strict";var e=window.React,t=window.wp.blockEditor,r=window.wp.blocks,o=window.wp.element,a=JSON.parse('{"apiVersion":2,"name":"targetals/ta-caption","title":"Caption","category":"targetals-blocks","icon":"text","supports":{"anchor":true,"color":{"text":true,"background":false,"gradients":false}},"attributes":{"text":{"type":"string"},"title":{"type":"string"},"anchor":{"type":"string","default":""},"placeholder":{"type":"string"},"titlePlaceholder":{"type":"string"}},"parent":["targetals/ta-verticalvideo"],"editorScript":"file:./index.js","render":"file:./render.php"}');(0,r.registerBlockType)(a.name,{...a,edit:function({attributes:r,setAttributes:a}){const{anchor:n,titlePlaceholder:i,title:c,placeholder:l,text:s,textColor:d,style:p={}}=r;(0,o.useEffect)((()=>{!d||p.color&&p.color.text===d||a({style:{...p,color:{...p.color,text:d}}})}),[d]);const u=(0,t.useBlockProps)({id:n||void 0,className:"caption"});return(0,e.createElement)("figcaption",{...u},i&&(0,e.createElement)(t.RichText,{placeholder:i,tagName:"p",allowedFormats:["core/bold","core/italic","core/link","core/footnote","core/subscript","core/superscript"],className:"caption__title",value:c,onChange:e=>a({title:e})}),(0,e.createElement)(t.RichText,{placeholder:l,tagName:"p",allowedFormats:["core/bold","core/italic","core/link","core/footnote","core/subscript","core/superscript"],className:"caption__text",value:s,onChange:e=>a({text:e})}))},save:()=>null})}(),r(440)({})}();
//# sourceMappingURL=ta-caption.js.map