{"version": 3, "file": "js/ta-caption.js", "mappings": "qCAAA,UAQEA,UAJG,KAFU,EAAF,WACL,OAAOC,EAAKC,cAKZ;;AAEJ,SAASC,EAAMC,EAAQC,EAAKC,GAExB,GAAIA,EAAQ,CAER,IAAIC,EAAWC,SAASC,yBAA0BC,GAAWL,EAAIM,aAAa,YAAcL,EAAOM,aAAa,WAEhHF,GAAWL,EAAIQ,aAAa,UAAWH,GAEvC,IACA,IAAII,EAAQR,EAAOS,WAAU,GAAKD,EAAME,WAAWC,QAC/CV,EAASW,YAAYJ,EAAMK,YAG/Bf,EAAOc,YAAYX,EACvB,CACJ,CACA,SAASa,EAAqBC,GAE1BA,EAAIC,mBAAqB,WAErB,GAAI,IAAMD,EAAIE,WAAY,CAEtB,IAAIC,EAAiBH,EAAII,gBAEzBD,KAAmBA,EAAiBH,EAAII,gBAAkBjB,SAASkB,eAAeC,mBAAmB,KACtFC,KAAKC,UAAYR,EAAIS,aAAcT,EAAIU,cAAgB,CAAC,GACvEV,EAAIW,QAAQC,OAAO,GAAGC,KAAI,SAASC,GAE/B,IAAI7B,EAASe,EAAIU,cAAcI,EAAKC,IAEpC9B,IAAWA,EAASe,EAAIU,cAAcI,EAAKC,IAAMZ,EAAea,eAAeF,EAAKC,KAEpFjC,EAAMgC,EAAK/B,OAAQ+B,EAAK9B,IAAKC,EACjC,GACJ,CACJ,EACAe,EAAIC,oBACR,CACA,SAASpB,EAAcoC,GACnB,SAASC,IAEL,IACA,IAAIC,EAAQ,EAAGA,EAAQC,EAAKxB,QAAU,CAElC,IAAIyB,EAAMD,EAAKD,GAAQpC,EAASsC,EAAIC,WAAYtC,EAAMuC,EAAexC,GAASyC,EAAMH,EAAI9B,aAAa,eAAiB8B,EAAI9B,aAAa,QACvI,IAAKiC,GAAOC,EAAKC,gBAAkBF,EAAMH,EAAI9B,aAAakC,EAAKC,gBAC/D1C,GAAOwC,GACH,GAAIG,EACA,IAAKF,EAAKG,UAAYH,EAAKG,SAASJ,EAAKxC,EAAKqC,GAAM,CAEhDtC,EAAO8C,YAAYR,GAEnB,IAAIS,EAAWN,EAAIO,MAAM,KAAMC,EAAMF,EAASG,QAASlB,EAAKe,EAASI,KAAK,KAE1E,GAAIF,EAAIpC,OAAQ,CAEZ,IAAII,EAAMmC,EAASH,GAEnBhC,KAAQA,EAAMmC,EAASH,GAAO,IAAII,gBAAsBC,KAAK,MAAOL,GAAMhC,EAAIsC,OAC9EtC,EAAIW,QAAU,IACdX,EAAIW,QAAQ4B,KAAK,CACbxD,OAAQA,EACRC,IAAKA,EACL+B,GAAIA,IAERhB,EAAqBC,EACzB,MAEIlB,EAAMC,EAAQC,EAAKG,SAAS6B,eAAeD,GAEnD,OAEMI,IAASqB,QAKjBrB,CAEV,GAEEC,EAAKxB,QAAUwB,EAAKxB,OAAS4C,EAAiC,IAAMC,EAAsBvB,EAAY,GAC5G,CACA,IAAIS,EAAUF,EAAOiB,OAAOzB,GAAU0B,EAAY,0CAA2CC,EAAW,yBAA0BC,EAAc,sBAAuBC,EAAS,mBAAoBC,EAAWC,OAAOC,MAAQD,OAAOE,KACrOvB,EAAW,aAAcF,EAAOA,EAAKE,SAAWgB,EAAUQ,KAAKC,UAAUC,aAAeD,UAAUC,UAAUC,MAAMT,IAAgB,IAAI,GAAK,QAAUO,UAAUC,UAAUC,MAAMV,IAAa,IAAI,GAAK,KAAOE,EAAOK,KAAKC,UAAUC,YAAcN,EAEhP,IAAIZ,EAAW,CAAC,EAAGM,EAAwBO,OAAOP,uBAAyBc,WAAYnC,EAAOjC,SAASqE,qBAAqB,OAAQhB,EAAiC,EAErKb,GAAYT,GAChB,CACA,SAASK,EAAekC,GACpB,IAAK,IAAIzE,EAAMyE,EAAM,QAAUzE,EAAI0E,SAASC,gBAAkB3E,EAAMA,EAAIsC,cACxE,OAAOtC,CACX,CACA,OAAOH,CACX,CAtGoC+E,EAC/B,UAFM,OAEN,Y,GCHDC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUM,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAGpEK,EAAOD,OACf,E,wBCtBA,IAAI,EAA+BlB,OAAc,MCA7C,EAA+BA,OAAW,GAAe,YCAzD,EAA+BA,OAAW,GAAU,OCApD,EAA+BA,OAAW,GAAW,Q,yeCKzDsB,EAAAA,EAAAA,mBAAkBC,EAAAA,KAAe,IAC5BA,EACHC,KAIF,UAAuB,WAAEC,EAAU,cAAEC,IACnC,MAAM,OACJC,EAAM,iBACNC,EAAgB,MAChBC,EAAK,YACLC,EAAW,KACXC,EAAI,UACJC,EAAS,MACTC,EAAQ,CAAC,GACPR,GAGJS,EAAAA,EAAAA,YAAU,MACJF,GAAeC,EAAME,OAASF,EAAME,MAAMJ,OAASC,GACrDN,EAAc,CACZO,MAAO,IACFA,EACHE,MAAO,IACFF,EAAME,MACTJ,KAAMC,KAId,GACC,CAACA,IAGJ,MAAMI,GAAaC,EAAAA,EAAAA,eAAc,CAC/BtE,GAAI4D,QAAUV,EACdqB,UAAW,YAGb,OACEC,EAAAA,EAAAA,eAAA,iBAAgBH,GACbR,IACCW,EAAAA,EAAAA,eAACC,EAAAA,SAAQ,CACPV,YAAaF,EACba,QAAQ,IACRC,eAAgB,CACd,YACA,cACA,YACA,gBACA,iBACA,oBAEFJ,UAAU,iBACVK,MAAOd,EACPe,SAAWb,GAASL,EAAc,CAAEG,MAAOE,OAG/CQ,EAAAA,EAAAA,eAACC,EAAAA,SAAQ,CACPV,YAAaA,EACbW,QAAQ,IACRC,eAAgB,CACd,YACA,cACA,YACA,gBACA,iBACA,oBAEFJ,UAAU,gBACVK,MAAOZ,EACPa,SAAWb,GAASL,EAAc,CAAEK,KAAMA,MAIlD,EAvEEc,KAAMA,IAAM,M,ICRd,EAAQ,IAAR,CAAyB,CAAC,E", "sources": ["webpack://targetals/./node_modules/svg4everybody/dist/svg4everybody.js", "webpack://targetals/webpack/bootstrap", "webpack://targetals/external window \"React\"", "webpack://targetals/external window [\"wp\",\"blockEditor\"]", "webpack://targetals/external window [\"wp\",\"blocks\"]", "webpack://targetals/external window [\"wp\",\"element\"]", "webpack://targetals/./blocks/ta-caption/index.js", "webpack://targetals/./node_modules/svg-spritemap-webpack-plugin/svg4everybody-helper.js"], "sourcesContent": ["!function(root, factory) {\n    \"function\" == typeof define && define.amd ? // AMD. Register as an anonymous module unless amdModuleId is set\n    define([], function() {\n        return root.svg4everybody = factory();\n    }) : \"object\" == typeof module && module.exports ? // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory() : root.svg4everybody = factory();\n}(this, function() {\n    /*! svg4everybody v2.1.9 | github.com/jonathantneal/svg4everybody */\n    function embed(parent, svg, target) {\n        // if the target exists\n        if (target) {\n            // create a document fragment to hold the contents of the target\n            var fragment = document.createDocumentFragment(), viewBox = !svg.hasAttribute(\"viewBox\") && target.getAttribute(\"viewBox\");\n            // conditionally set the viewBox on the svg\n            viewBox && svg.setAttribute(\"viewBox\", viewBox);\n            // copy the contents of the clone into the fragment\n            for (// clone the target\n            var clone = target.cloneNode(!0); clone.childNodes.length; ) {\n                fragment.appendChild(clone.firstChild);\n            }\n            // append the fragment into the svg\n            parent.appendChild(fragment);\n        }\n    }\n    function loadreadystatechange(xhr) {\n        // listen to changes in the request\n        xhr.onreadystatechange = function() {\n            // if the request is ready\n            if (4 === xhr.readyState) {\n                // get the cached html document\n                var cachedDocument = xhr._cachedDocument;\n                // ensure the cached html document based on the xhr response\n                cachedDocument || (cachedDocument = xhr._cachedDocument = document.implementation.createHTMLDocument(\"\"), \n                cachedDocument.body.innerHTML = xhr.responseText, xhr._cachedTarget = {}), // clear the xhr embeds list and embed each item\n                xhr._embeds.splice(0).map(function(item) {\n                    // get the cached target\n                    var target = xhr._cachedTarget[item.id];\n                    // ensure the cached target\n                    target || (target = xhr._cachedTarget[item.id] = cachedDocument.getElementById(item.id)), \n                    // embed the target into the svg\n                    embed(item.parent, item.svg, target);\n                });\n            }\n        }, // test the ready state change immediately\n        xhr.onreadystatechange();\n    }\n    function svg4everybody(rawopts) {\n        function oninterval() {\n            // while the index exists in the live <use> collection\n            for (// get the cached <use> index\n            var index = 0; index < uses.length; ) {\n                // get the current <use>\n                var use = uses[index], parent = use.parentNode, svg = getSVGAncestor(parent), src = use.getAttribute(\"xlink:href\") || use.getAttribute(\"href\");\n                if (!src && opts.attributeName && (src = use.getAttribute(opts.attributeName)), \n                svg && src) {\n                    if (polyfill) {\n                        if (!opts.validate || opts.validate(src, svg, use)) {\n                            // remove the <use> element\n                            parent.removeChild(use);\n                            // parse the src and get the url and id\n                            var srcSplit = src.split(\"#\"), url = srcSplit.shift(), id = srcSplit.join(\"#\");\n                            // if the link is external\n                            if (url.length) {\n                                // get the cached xhr request\n                                var xhr = requests[url];\n                                // ensure the xhr request exists\n                                xhr || (xhr = requests[url] = new XMLHttpRequest(), xhr.open(\"GET\", url), xhr.send(), \n                                xhr._embeds = []), // add the svg and id as an item to the xhr embeds list\n                                xhr._embeds.push({\n                                    parent: parent,\n                                    svg: svg,\n                                    id: id\n                                }), // prepare the xhr ready state change event\n                                loadreadystatechange(xhr);\n                            } else {\n                                // embed the local id into the svg\n                                embed(parent, svg, document.getElementById(id));\n                            }\n                        } else {\n                            // increase the index when the previous value was not \"valid\"\n                            ++index, ++numberOfSvgUseElementsToBypass;\n                        }\n                    }\n                } else {\n                    // increase the index when the previous value was not \"valid\"\n                    ++index;\n                }\n            }\n            // continue the interval\n            (!uses.length || uses.length - numberOfSvgUseElementsToBypass > 0) && requestAnimationFrame(oninterval, 67);\n        }\n        var polyfill, opts = Object(rawopts), newerIEUA = /\\bTrident\\/[567]\\b|\\bMSIE (?:9|10)\\.0\\b/, webkitUA = /\\bAppleWebKit\\/(\\d+)\\b/, olderEdgeUA = /\\bEdge\\/12\\.(\\d+)\\b/, edgeUA = /\\bEdge\\/.(\\d+)\\b/, inIframe = window.top !== window.self;\n        polyfill = \"polyfill\" in opts ? opts.polyfill : newerIEUA.test(navigator.userAgent) || (navigator.userAgent.match(olderEdgeUA) || [])[1] < 10547 || (navigator.userAgent.match(webkitUA) || [])[1] < 537 || edgeUA.test(navigator.userAgent) && inIframe;\n        // create xhr requests object\n        var requests = {}, requestAnimationFrame = window.requestAnimationFrame || setTimeout, uses = document.getElementsByTagName(\"use\"), numberOfSvgUseElementsToBypass = 0;\n        // conditionally start the interval if the polyfill is active\n        polyfill && oninterval();\n    }\n    function getSVGAncestor(node) {\n        for (var svg = node; \"svg\" !== svg.nodeName.toLowerCase() && (svg = svg.parentNode); ) {}\n        return svg;\n    }\n    return svg4everybody;\n});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "var __WEBPACK_NAMESPACE_OBJECT__ = window[\"React\"];", "var __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"blockEditor\"];", "var __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"blocks\"];", "var __WEBPACK_NAMESPACE_OBJECT__ = window[\"wp\"][\"element\"];", "import { RichText, useBlockProps } from '@wordpress/block-editor';\nimport { registerBlockType } from '@wordpress/blocks';\nimport { useEffect } from '@wordpress/element';\nimport metadata from './block.json';\n\nregisterBlockType(metadata.name, {\n  ...metadata,\n  edit: EditComponent,\n  save: () => null // dynamic block; rendering handled by P<PERSON>\n});\n\nfunction EditComponent({ attributes, setAttributes }) {\n  const {\n    anchor,\n    titlePlaceholder,\n    title,\n    placeholder,\n    text,\n    textColor,\n    style = {}\n  } = attributes;\n\n  // Sync textColor to style.color.text\n  useEffect(() => {\n    if (textColor && (!style.color || style.color.text !== textColor)) {\n      setAttributes({\n        style: {\n          ...style,\n          color: {\n            ...style.color,\n            text: textColor\n          }\n        }\n      });\n    }\n  }, [textColor]);\n\n  // Get block props, including anchor, className, style (text color), etc.\n  const blockProps = useBlockProps({\n    id: anchor || undefined,\n    className: 'caption' // your custom block class\n  });\n\n  return (\n    <figcaption {...blockProps}>\n      {titlePlaceholder && (\n        <RichText\n          placeholder={titlePlaceholder}\n          tagName=\"p\"\n          allowedFormats={[\n            'core/bold',\n            'core/italic',\n            'core/link',\n            'core/footnote',\n            'core/subscript',\n            'core/superscript'\n          ]}\n          className=\"caption__title\"\n          value={title}\n          onChange={(text) => setAttributes({ title: text })}\n        />\n      )}\n      <RichText\n        placeholder={placeholder}\n        tagName=\"p\"\n        allowedFormats={[\n          'core/bold',\n          'core/italic',\n          'core/link',\n          'core/footnote',\n          'core/subscript',\n          'core/superscript'\n        ]}\n        className=\"caption__text\"\n        value={text}\n        onChange={(text) => setAttributes({ text: text })}\n      />\n    </figcaption>\n  );\n}\n", "require('svg4everybody')({});\n"], "names": ["this", "root", "svg4everybody", "embed", "parent", "svg", "target", "fragment", "document", "createDocumentFragment", "viewBox", "hasAttribute", "getAttribute", "setAttribute", "clone", "cloneNode", "childNodes", "length", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loadreadystatechange", "xhr", "onreadystatechange", "readyState", "cachedDocument", "_cachedDocument", "implementation", "createHTMLDocument", "body", "innerHTML", "responseText", "_cachedTarget", "_embeds", "splice", "map", "item", "id", "getElementById", "rawopts", "oninterval", "index", "uses", "use", "parentNode", "getSVGAncestor", "src", "opts", "attributeName", "polyfill", "validate", "<PERSON><PERSON><PERSON><PERSON>", "srcSplit", "split", "url", "shift", "join", "requests", "XMLHttpRequest", "open", "send", "push", "numberOfSvgUseElementsToBypass", "requestAnimationFrame", "Object", "newerIEUA", "webkitUA", "olderEdgeUA", "edgeUA", "inIframe", "window", "top", "self", "test", "navigator", "userAgent", "match", "setTimeout", "getElementsByTagName", "node", "nodeName", "toLowerCase", "factory", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "call", "registerBlockType", "metadata", "edit", "attributes", "setAttributes", "anchor", "titlePlaceholder", "title", "placeholder", "text", "textColor", "style", "useEffect", "color", "blockProps", "useBlockProps", "className", "createElement", "RichText", "tagName", "allowedFormats", "value", "onChange", "save"], "sourceRoot": ""}