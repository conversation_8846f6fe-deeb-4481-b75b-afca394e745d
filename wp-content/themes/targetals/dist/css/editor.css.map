{"version": 3, "file": "css/editor.css", "mappings": "AAKQ,wGCEN,sBAAwC,CACxC,wBAAwC,CACxC,sBAAmC,CACnC,4BAA4C,CAC5C,6BAA+C,CAC/C,qBAAsC,CACtC,gCAAkD,CAClD,yBAA2C,CAC3C,oBAAmC,CACnC,oBAAyC,CACzC,sBAAwC,CACxC,yBAA0B,CAG1B,qCAAsC,CACtC,wCAAyC,CACzC,uCAAwC,CAGxC,6CAA8C,CAC9C,2CAA4C,CAC5C,2CAA4C,CAC5C,4CAA6C,CAC7C,wCAAyC,CACzC,gDAAiD,CACjD,+CAAgD,CAChD,+CAAgD,CAChD,+CAAgD,CAChD,gDAAiD,CAGjD,8CAA+C,CAC/C,4CAA6C,CAC7C,4CAA6C,CAC7C,6CAA8C,CAC9C,8CAA+C,CAC/C,yDAA0D,CAC1D,2DAA4D,CAC5D,2DAA4D,CAC5D,wDAAyD,CACzD,yDAA0D,CAG1D,uCAAwC,CACxC,0CAA2C,CAC3C,0CAA2C,CAC3C,sCAAuC,CACvC,uCAAwC,CACxC,+CAAgD,CAChD,6CAA8C,CAC9C,6CAA8C,CAC9C,8CAA+C,CAC/C,+CAAgD,CAGhD,gCAAiC,CACjC,4CAA6C,CAC7C,sCAAuC,CACvC,qDAAsD,CACtD,6CAA8C,CAG9C,0CAA2C,CAC3C,wCAAyC,CACzC,wCAAyC,CACzC,yCAA0C,CAC1C,0CAA2C,CAC3C,0CAA2C,CAC3C,wCAAyC,CACzC,wCAAyC,CACzC,yCAA0C,CAC1C,0CAA2C,CC1E7C,WACE,kBAEA,iBAGA,CAHA,gBADA,yMAIA,kCAIF,sCAGE,6BACA,CACA,mBACA,CAFA,eACA,CAEA,aACA,CAFA,mBACA,CACA,kCAIA,kCACA,iDCtBA,kBACA,kBAGF,cAGE,mCAKA,kCACA,uCAGF,wCAUE,OAGF,eAEE,aAKA,iBAGF,uBAIE,4BAIA,aAGF,uBAIE,QAIA,QAGF,aACE,CAJA,iBAIA,8BAGA,gBACA,eACA,CAFA,sBAEA,oBAIF,SAHE,SAGF,iBACE,KAQF,QACE,CAJA,WAGF,CAPE,cAQA,cACA,WACA", "sources": ["webpack://targetals/./src/scss/base/_fonts.scss", "webpack://targetals/./src/scss/base/constants/_colors.scss", "webpack://targetals/./src/scss/base/_icons.scss", "webpack://targetals/./src/scss/base/_reset.scss"], "sourcesContent": ["/**\n  IMPORT WEBFONTS\n**/\n\n// Import Google fonts example:\n@import 'https://fonts.googleapis.com/css?family=Montserrat:400,400i,500,600|Roboto+Condensed:700';\n\n// Locally-hosted font example\n\n/**\n@font-face {\n  font-family: FamilyName;\n  src: url('../fonts/FamilyName-Bold.woff2') format('woff2'),\n    url('../fonts/FamilyName-Bold.woff') format('woff');\n  font-weight: 700;\n  font-style: normal;\n}\n**/\n", "/**\n  COLORS\n  color palette\n**/\n\n:root {\n  // names correspond with Figma design names - set base values here\n  --primary-sand: rgba(239 238 235 / 100%);\n  --primary-orange: rgba(236 99 38 / 100%);\n  --primary-navy: rgba(0 4 38 / 100%);\n  --primary-royal-blue: rgba(35 46 131 / 100%);\n  --primary-bright-blue: rgba(133 209 235 / 100%);\n  --chow-yellow: rgba(250 217 47 / 100%);\n  --chow-bright-blue-shade: rgba(225 243 250 / 100%);\n  --chow-light-sand: rgba(250 250 249 / 100%);\n  --neutral-black: rgba(0 0 0 / 100%);\n  --neutral-white: rgba(255 255 255 / 100%);\n  --neutral-blue: rgba(222 222 236 / 100%);\n  --transparent: transparent;\n\n  // context for colors - use var() with the colors above\n  --site-background: var(--primary-sand);\n  --body-text-default: var(--neutral-black);\n  --headline-default: var(--neutral-black);\n\n  // text links\n  --text-link-default: var(--primary-royal-blue);\n  --text-link-hover: var(--primary-royal-blue);\n  --text-link-focus: var(--primary-royal-blue);\n  --text-link-active: var(--primary-royal-blue);\n  --text-link-visited: var(--neutral-black);\n  --text-link-default-underline: var(--transparent);\n  --text-link-hover-underline: var(--neutral-blue);\n  --text-link-focus-underline: var(--neutral-blue);\n  --text-link-active-underline: var(--transparent);\n  --text-link-visited-underline: var(--transparent);\n\n  // emphasized links\n  --emphasized-link-default: var(--neutral-black);\n  --emphasized-link-hover: var(--neutral-black);\n  --emphasized-link-focus: var(--neutral-black);\n  --emphasized-link-active: var(--neutral-black);\n  --emphasized-link-visited: var(--neutral-black);\n  --emphasized-link-default-underline: var(--primary-orange);\n  --emphasized-link-hover-underline: var(--primary-royal-blue);\n  --emphasized-link-focus-underline: var(--primary-royal-blue);\n  --emphasized-link-active-underline: var(--primary-orange);\n  --emphasized-link-visited-underline: var(--primary-orange);\n\n  // navigation links\n  --nav-link-default: var(--neutral-black);\n  --nav-link-hover: var(--primary-royal-blue);\n  --nav-link-focus: var(--primary-royal-blue);\n  --nav-link-active: var(--neutral-black);\n  --nav-link-visited: var(--neutral-black);\n  --nav-link-default-underline: var(--transparent);\n  --nav-link-hover-underline: var(--transparent);\n  --nav-link-focus-underline: var(--transparent);\n  --nav-link-active-underline: var(--transparent);\n  --nav-link-visited-underline: var(--transparent);\n\n  // forms\n  --form-label: var(--primary-navy);\n  --form-input-placeholder: var(--primary-navy);\n  --form-input-text: var(--neutral-black);\n  --form-input-background: var(--chow-bright-blue-shade);\n  --form-input-border: var(--primary-royal-blue);\n\n  // buttons\n  --button-default: var(--primary-royal-blue);\n  --button-hover: var(--primary-royal-blue);\n  --button-focus: var(--primary-royal-blue);\n  --button-active: var(--primary-royal-blue);\n  --button-visited: var(--primary-royal-blue);\n  --button-default-text: var(--neutral-white);\n  --button-hover-text: var(--neutral-white);\n  --button-focus-text: var(--neutral-white);\n  --button-active-text: var(--neutral-white);\n  --button-visited-text: var(--neutral-white);\n}\n", "/**\n  $ICONS\n**/\n\n@font-face {\n  font-family: icons;\n  src: url('../fonts/icons.eot');\n  src: url('../fonts/icons.eot?#iefix') format('embedded-opentype'),\n    url('../fonts/icons.ttf') format('truetype'),\n    url('../fonts/icons.woff') format('woff');\n  font-weight: normal;\n  font-style: normal;\n}\n\n[class^='icon-'],\n[class*=' icon-'] {\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: icons, sans-serif !important;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n\n  /* Better Font Rendering =========== */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n", "/**\n  CSS RESET\n**/\n\nhtml {\n  text-size-adjust: 100%;\n  box-sizing: border-box;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\nbody {\n  margin: 0;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\nh1,\nh2,\nh3,\nh4,\np,\nblockquote,\nfigure,\nol,\nul {\n  margin: 0;\n  padding: 0;\n}\n\nmain,\nli {\n  display: block;\n}\n\nol,\nul {\n  list-style: none;\n}\n\nh1,\nh2,\nh3,\nh4 {\n  font-size: inherit;\n}\n\nstrong {\n  font-weight: bold;\n}\n\na,\n[role='button'],\n.ajax,\n.cta {\n  color: inherit;\n}\n\na {\n  text-decoration: none;\n}\n\nbutton {\n  overflow: visible;\n  border: 0;\n  font: inherit;\n  -webkit-font-smoothing: inherit;\n  letter-spacing: inherit;\n  background: none;\n  cursor: pointer;\n}\n\n::-moz-focus-inner {\n  padding: 0;\n  border: 0;\n}\n\n:focus {\n  outline: 0;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n  border: 0;\n}\n\nblockquote,\nq {\n  quotes: none;\n}\n"], "names": [], "sourceRoot": ""}