# Target ALS - Setup from Kinsta Backup Script (PowerShell)
# This script automates pulling WordPress files from a Kinsta backup
# into your cloned repository for local development

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupFile
)

# Function to write colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to check if we're in the right directory
function Test-Directory {
    if (-not (Test-Path "wp-content\themes\targetals\package.json")) {
        Write-Error "This script must be run from the Target ALS project root directory"
        Write-Error "Make sure you're in the directory containing wp-content\themes\targetals\"
        exit 1
    }
}

# Function to check if backup file exists
function Test-BackupFile {
    param([string]$FilePath)
    if (-not (Test-Path $FilePath)) {
        Write-Error "Backup file not found: $FilePath"
        Write-Error "Please download a backup from Kinsta Dashboard and provide the correct path"
        exit 1
    }
}

# Function to create backup of existing files
function Backup-ExistingFiles {
    $backupDir = "backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Write-Status "Creating backup of existing files in $backupDir"
    
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    # Backup existing WordPress files
    $wpFiles = @("index.php", "wp-*.php", "xmlrpc.php", "license.txt", "readme.html")
    foreach ($pattern in $wpFiles) {
        Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue | ForEach-Object {
            Copy-Item $_ $backupDir -ErrorAction SilentlyContinue
        }
    }
    
    # Backup existing directories
    @("wp-admin", "wp-includes") | ForEach-Object {
        if (Test-Path $_) {
            Copy-Item $_ $backupDir -Recurse -ErrorAction SilentlyContinue
        }
    }
    
    if (Test-Path "wp-content\uploads") {
        New-Item -ItemType Directory -Path "$backupDir\wp-content" -Force | Out-Null
        Copy-Item "wp-content\uploads" "$backupDir\wp-content\" -Recurse -ErrorAction SilentlyContinue
    }
    
    Write-Success "Backup created in $backupDir"
}

# Function to extract and copy WordPress files
function Extract-WordPressFiles {
    param([string]$BackupFile)
    
    $tempDir = "temp-kinsta-extract"
    Write-Status "Extracting Kinsta backup..."
    
    # Create temporary directory
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    
    try {
        # Extract based on file type
        if ($BackupFile -match '\.(tar\.gz|tgz)$') {
            # Use tar if available (Windows 10 1803+)
            if (Get-Command tar -ErrorAction SilentlyContinue) {
                tar -xzf $BackupFile -C $tempDir
            } else {
                Write-Error "tar command not available. Please extract the .tar.gz file manually to '$tempDir'"
                exit 1
            }
        } elseif ($BackupFile -match '\.zip$') {
            Expand-Archive -Path $BackupFile -DestinationPath $tempDir
        } else {
            Write-Error "Unsupported backup file format. Please use .tar.gz, .tgz, or .zip"
            exit 1
        }
        
        # Find WordPress root
        $wpRoot = ""
        if (Test-Path "$tempDir\index.php") {
            $wpRoot = $tempDir
        } else {
            # Look for WordPress root in subdirectories
            $indexFiles = Get-ChildItem -Path $tempDir -Name "index.php" -Recurse
            foreach ($indexFile in $indexFiles) {
                $dir = Split-Path (Join-Path $tempDir $indexFile) -Parent
                if (Test-Path "$dir\wp-config-sample.php") {
                    $wpRoot = $dir
                    break
                }
            }
        }
        
        if (-not $wpRoot -or -not (Test-Path "$wpRoot\index.php")) {
            Write-Error "Could not find WordPress installation in backup"
            Remove-Item $tempDir -Recurse -Force
            exit 1
        }
        
        Write-Success "Found WordPress installation in: $wpRoot"
        
        # Copy WordPress root files
        Write-Status "Copying WordPress root files..."
        $wpFiles = @(
            "index.php", "wp-activate.php", "wp-blog-header.php", "wp-comments-post.php",
            "wp-config-sample.php", "wp-cron.php", "wp-links-opml.php", "wp-load.php",
            "wp-login.php", "wp-mail.php", "wp-settings.php", "wp-signup.php",
            "wp-trackback.php", "xmlrpc.php", "license.txt", "readme.html"
        )
        
        foreach ($file in $wpFiles) {
            $sourcePath = Join-Path $wpRoot $file
            if (Test-Path $sourcePath) {
                Copy-Item $sourcePath . -Force
                Write-Status "Copied $file"
            } else {
                Write-Warning "File not found in backup: $file"
            }
        }
        
        # Copy WordPress directories
        Write-Status "Copying WordPress directories..."
        
        $wpAdminPath = Join-Path $wpRoot "wp-admin"
        if (Test-Path $wpAdminPath) {
            Copy-Item $wpAdminPath . -Recurse -Force
            Write-Success "Copied wp-admin\"
        } else {
            Write-Warning "wp-admin directory not found in backup"
        }
        
        $wpIncludesPath = Join-Path $wpRoot "wp-includes"
        if (Test-Path $wpIncludesPath) {
            Copy-Item $wpIncludesPath . -Recurse -Force
            Write-Success "Copied wp-includes\"
        } else {
            Write-Warning "wp-includes directory not found in backup"
        }
        
        # Copy uploads directory
        $uploadsPath = Join-Path $wpRoot "wp-content\uploads"
        if (Test-Path $uploadsPath) {
            New-Item -ItemType Directory -Path "wp-content" -Force | Out-Null
            Copy-Item $uploadsPath "wp-content\" -Recurse -Force
            Write-Success "Copied wp-content\uploads\"
        } else {
            Write-Warning "wp-content\uploads directory not found in backup"
        }
        
        # Copy languages directory if it exists
        $languagesPath = Join-Path $wpRoot "wp-content\languages"
        if (Test-Path $languagesPath) {
            New-Item -ItemType Directory -Path "wp-content" -Force | Out-Null
            Copy-Item $languagesPath "wp-content\" -Recurse -Force
            Write-Success "Copied wp-content\languages\"
        }
        
    } finally {
        # Clean up
        Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        Write-Success "Cleanup completed"
    }
}

# Main execution
Write-Host "==========================================" -ForegroundColor Blue
Write-Host "  Target ALS - Kinsta Setup Script" -ForegroundColor Blue
Write-Host "==========================================" -ForegroundColor Blue

# Validate environment
Test-Directory
Test-BackupFile $BackupFile

# Confirm with user
Write-Host "This script will:" -ForegroundColor Yellow
Write-Host "1. Extract WordPress files from Kinsta backup" -ForegroundColor Yellow
Write-Host "2. Copy WordPress core files to current directory" -ForegroundColor Yellow
Write-Host "3. Copy wp-admin and wp-includes directories" -ForegroundColor Yellow
Write-Host "4. Copy wp-content\uploads directory" -ForegroundColor Yellow
Write-Host "" -ForegroundColor Yellow
Write-Host "Backup file: $BackupFile" -ForegroundColor Yellow

$confirmation = Read-Host "Do you want to continue? (y/N)"
if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
    Write-Status "Operation cancelled"
    exit 0
}

# Create backup of existing files
Backup-ExistingFiles

# Extract and copy files
Extract-WordPressFiles $BackupFile

Write-Host "==========================================" -ForegroundColor Green
Write-Host "  Setup Complete!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

Write-Success "WordPress files have been extracted from Kinsta backup"
Write-Status "Next steps:"
Write-Host "  1. Start Lando: lando start"
Write-Host "  2. Import database: lando db-import <database-file>"
Write-Host "  3. Update URLs: lando wp search-replace 'old-url' 'https://targetals.lndo.site'"
Write-Host "  4. Install theme dependencies: cd wp-content\themes\targetals && npm install"
