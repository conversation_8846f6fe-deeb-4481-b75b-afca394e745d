#!/bin/bash

# Target ALS - Setup from Kinsta Backup Script
# This script automates pulling WordPress files from a Kinsta backup
# into your cloned repository for local development

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if we're in the right directory
check_directory() {
    if [[ ! -f "wp-content/themes/targetals/package.json" ]]; then
        print_error "This script must be run from the Target ALS project root directory"
        print_error "Make sure you're in the directory containing wp-content/themes/targetals/"
        exit 1
    fi
}

# Function to check if backup file exists
check_backup_file() {
    if [[ ! -f "$1" ]]; then
        print_error "Backup file not found: $1"
        print_error "Please download a backup from Kinsta Dashboard and provide the correct path"
        exit 1
    fi
}

# Function to create backup of existing files
backup_existing() {
    local backup_dir="backup-$(date +%Y%m%d-%H%M%S)"
    print_status "Creating backup of existing files in $backup_dir"
    
    mkdir -p "$backup_dir"
    
    # Backup existing WordPress files if they exist
    for file in index.php wp-*.php xmlrpc.php license.txt readme.html; do
        if [[ -f "$file" ]]; then
            cp "$file" "$backup_dir/" 2>/dev/null || true
        fi
    done
    
    # Backup existing directories if they exist
    for dir in wp-admin wp-includes; do
        if [[ -d "$dir" ]]; then
            cp -r "$dir" "$backup_dir/" 2>/dev/null || true
        fi
    done
    
    if [[ -d "wp-content/uploads" ]]; then
        mkdir -p "$backup_dir/wp-content"
        cp -r "wp-content/uploads" "$backup_dir/wp-content/" 2>/dev/null || true
    fi
    
    print_success "Backup created in $backup_dir"
}

# Function to extract and copy WordPress files
extract_wordpress_files() {
    local backup_file="$1"
    local temp_dir="temp-kinsta-extract"
    
    print_status "Extracting Kinsta backup..."
    
    # Create temporary directory
    mkdir -p "$temp_dir"
    
    # Extract based on file type
    if [[ "$backup_file" == *.tar.gz ]] || [[ "$backup_file" == *.tgz ]]; then
        tar -xzf "$backup_file" -C "$temp_dir"
    elif [[ "$backup_file" == *.zip ]]; then
        unzip -q "$backup_file" -d "$temp_dir"
    else
        print_error "Unsupported backup file format. Please use .tar.gz, .tgz, or .zip"
        exit 1
    fi
    
    # Find the WordPress root in the extracted files
    local wp_root=""
    if [[ -f "$temp_dir/index.php" ]]; then
        wp_root="$temp_dir"
    else
        # Look for WordPress root in subdirectories
        wp_root=$(find "$temp_dir" -name "index.php" -path "*/public/*" | head -1 | xargs dirname 2>/dev/null || echo "")
        if [[ -z "$wp_root" ]]; then
            wp_root=$(find "$temp_dir" -name "index.php" | head -1 | xargs dirname 2>/dev/null || echo "")
        fi
    fi
    
    if [[ -z "$wp_root" || ! -f "$wp_root/index.php" ]]; then
        print_error "Could not find WordPress installation in backup"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    print_success "Found WordPress installation in: $wp_root"
    
    # Copy WordPress root files
    print_status "Copying WordPress root files..."
    local wp_files=(
        "index.php"
        "wp-activate.php"
        "wp-blog-header.php"
        "wp-comments-post.php"
        "wp-config-sample.php"
        "wp-cron.php"
        "wp-links-opml.php"
        "wp-load.php"
        "wp-login.php"
        "wp-mail.php"
        "wp-settings.php"
        "wp-signup.php"
        "wp-trackback.php"
        "xmlrpc.php"
        "license.txt"
        "readme.html"
    )
    
    for file in "${wp_files[@]}"; do
        if [[ -f "$wp_root/$file" ]]; then
            cp "$wp_root/$file" .
            print_status "Copied $file"
        else
            print_warning "File not found in backup: $file"
        fi
    done
    
    # Copy WordPress directories
    print_status "Copying WordPress directories..."
    
    if [[ -d "$wp_root/wp-admin" ]]; then
        cp -r "$wp_root/wp-admin" .
        print_success "Copied wp-admin/"
    else
        print_warning "wp-admin directory not found in backup"
    fi
    
    if [[ -d "$wp_root/wp-includes" ]]; then
        cp -r "$wp_root/wp-includes" .
        print_success "Copied wp-includes/"
    else
        print_warning "wp-includes directory not found in backup"
    fi
    
    # Copy uploads directory
    if [[ -d "$wp_root/wp-content/uploads" ]]; then
        mkdir -p "wp-content"
        cp -r "$wp_root/wp-content/uploads" "wp-content/"
        print_success "Copied wp-content/uploads/"
    else
        print_warning "wp-content/uploads directory not found in backup"
    fi
    
    # Copy languages directory if it exists
    if [[ -d "$wp_root/wp-content/languages" ]]; then
        mkdir -p "wp-content"
        cp -r "$wp_root/wp-content/languages" "wp-content/"
        print_success "Copied wp-content/languages/"
    fi
    
    # Clean up
    rm -rf "$temp_dir"
    print_success "Cleanup completed"
}

# Function to set proper permissions
set_permissions() {
    print_status "Setting proper file permissions..."
    
    # Set directory permissions
    find . -type d -name "wp-admin" -exec chmod 755 {} \; 2>/dev/null || true
    find . -type d -name "wp-includes" -exec chmod 755 {} \; 2>/dev/null || true
    find . -type d -path "*/wp-content/uploads" -exec chmod 755 {} \; 2>/dev/null || true
    
    # Set file permissions
    find . -name "*.php" -type f -exec chmod 644 {} \; 2>/dev/null || true
    
    print_success "Permissions set"
}

# Main function
main() {
    echo -e "${BLUE}"
    echo "=========================================="
    echo "  Target ALS - Kinsta Setup Script"
    echo "=========================================="
    echo -e "${NC}"
    
    # Check if backup file is provided
    if [[ $# -eq 0 ]]; then
        print_error "Usage: $0 <path-to-kinsta-backup-file>"
        print_error "Example: $0 ~/Downloads/targetals-backup.tar.gz"
        exit 1
    fi
    
    local backup_file="$1"
    
    # Validate environment
    check_directory
    check_backup_file "$backup_file"
    
    # Confirm with user
    echo -e "${YELLOW}This script will:"
    echo "1. Extract WordPress files from Kinsta backup"
    echo "2. Copy WordPress core files to current directory"
    echo "3. Copy wp-admin and wp-includes directories"
    echo "4. Copy wp-content/uploads directory"
    echo "5. Set proper file permissions"
    echo ""
    echo "Backup file: $backup_file"
    echo -e "${NC}"
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled"
        exit 0
    fi
    
    # Create backup of existing files
    backup_existing
    
    # Extract and copy files
    extract_wordpress_files "$backup_file"
    
    # Set permissions
    set_permissions
    
    echo -e "${GREEN}"
    echo "=========================================="
    echo "  Setup Complete!"
    echo "=========================================="
    echo -e "${NC}"
    
    print_success "WordPress files have been extracted from Kinsta backup"
    print_status "Next steps:"
    echo "  1. Start Lando: lando start"
    echo "  2. Import database: lando db-import <database-file>"
    echo "  3. Update URLs: lando wp search-replace 'old-url' 'https://targetals.lndo.site'"
    echo "  4. Install theme dependencies: cd wp-content/themes/targetals && npm install"
}

# Run main function with all arguments
main "$@"
