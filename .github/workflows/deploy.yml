name: Deploy to Prod Kinsta Site

on:
  push:
    branches:
      - main # Trigger the workflow only when changes are pushed to the main branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Setup Node.js (only if needed for build tasks)
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      # Checkout the latest code from the GitHub repository
      - name: Checkout code
        uses: actions/checkout@v4.1.6

      # Deploy to <PERSON>nst<PERSON> via SSH
      - name: Deploy via SSH
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.KINSTA_SERVER_IP }}
          username: ${{ secrets.KINSTA_USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }} # Optional, default is 22
          script: |
            # Navigate to the live site directory
            cd /www/temptargetals_877/public

            # Pull the latest changes from the GitHub repository
            git fetch origin main
            git reset --hard origin/main  # Ensure the live site matches the latest main branch