# Target ALS WordPress Site

WordPress site for Target ALS - https://www.targetals.org/

## Overview

This is a custom WordPress site built for Target ALS, featuring a development workflow with Lando for local development, custom Gutenberg blocks, and deployment to Kinsta hosting.

## Tech Stack

- **WordPress**: 6.x
- **PHP**: 8.1+
- **Node.js**: 20.19.3
- **Local Development**: Lando
- **Hosting**: Kinsta
- **Build System**: Webpack 5
- **Styling**: Sass/SCSS
- **Custom Blocks**: Gutenberg (Dynamic & ACF)

## Prerequisites

Before setting up the project locally, ensure you have:

- [Lando](https://lando.dev/) installed
- [nvm (Node Version Manager)](https://github.com/nvm-sh/nvm) for managing Node.js versions
- [Node.js](https://nodejs.org/) 20.19.3 (use nvm for version management)
- [Composer](https://getcomposer.org/) for PHP dependencies
- Git for version control

## Local Development Setup

### 1. Clone the Repository

```bash
git clone [repository-url] targetals
cd targetals
```

### 2. Sync WordPress Files from Kinsta

Since most WordPress core files and uploads are in `.gitignore`, you'll need to sync them from Kinsta. Here are the recommended methods:

**Files typically excluded from Git:**
- WordPress core files (`wp-admin/`, `wp-includes/`, `wp-*.php`)
- Uploads directory (`wp-content/uploads/`)
- Configuration files (`wp-config.php`)
- Third-party plugins (if not in repository)
- Node modules and build artifacts

#### Method A: Kinsta DevKinsta (Recommended)
```bash
# 1. Install DevKinsta (Kinsta's local development tool)
# 2. Connect to your Kinsta account
# 3. Pull the Target ALS site directly to local environment
# 4. This automatically syncs:
#    - WordPress core files
#    - Database
#    - Uploads
#    - Plugins
#    - Themes (except your custom theme in Git)

# Benefits:
# - One-click sync with production/staging
# - Automatic WordPress core updates
# - Easy database sync
# - Built-in backup/restore
```

#### Method B: rsync via SSH (Advanced)
```bash
# Get SSH credentials from Kinsta Dashboard
# Sync files efficiently with rsync

# Sync WordPress core files
rsync -avz --exclude='wp-config.php' \
  username@server:/www/sitename/public/ \
  ./

# Sync only uploads (for regular updates)
rsync -avz \
  username@server:/www/sitename/public/wp-content/uploads/ \
  ./wp-content/uploads/

# Create sync script for regular updates
cat > sync-from-kinsta.sh << 'EOF'
#!/bin/bash
echo "Syncing uploads from Kinsta..."
rsync -avz --progress \
  username@server:/www/sitename/public/wp-content/uploads/ \
  ./wp-content/uploads/
echo "Sync complete!"
EOF

chmod +x sync-from-kinsta.sh
```

#### Method C: WP-CLI + Manual Uploads Sync
```bash
# Download WordPress core (ensures latest version)
lando wp core download --force

# Manually sync uploads via SFTP/Dashboard
# 1. Connect to Kinsta via SFTP
# 2. Download wp-content/uploads/ directory
# 3. Copy to your local wp-content/uploads/

# Or use Kinsta backup method for uploads:
# 1. Create backup in Kinsta Dashboard
# 2. Download backup
# 3. Extract only wp-content/uploads/
```

#### Ongoing Sync Strategy

For keeping your local environment in sync:

```bash
# Weekly: Sync uploads and database
./sync-from-kinsta.sh  # (if using Method B)
lando db-import latest-backup.sql

# Monthly: Update WordPress core
lando wp core update
lando wp core update-db

# As needed: Sync specific uploads
# Use SFTP or DevKinsta for individual file updates
```

**Recommended Approach:**
1. **Initial setup**: Use DevKinsta for complete site pull
2. **Regular development**: Use Git for theme/plugin changes
3. **Content sync**: Use rsync script or DevKinsta for uploads
4. **Database sync**: Import from Kinsta backups as needed

### 3. Start Lando Environment

```bash
# Start the Lando environment
lando start

# This will:
# - Create a local WordPress environment
# - Set up database (MySQL)
# - Configure web server (Apache/Nginx)
# - Install PHP extensions
# - Map local domain (targetals.lndo.site)
```

### 4. Install Dependencies

```bash
# Install theme dependencies
cd wp-content/themes/targetals
nvm use
npm install
composer install

# Return to project root
cd ../../../
```

### 5. Configure WordPress

#### Import Database from Kinsta

```bash
# Download database backup from Kinsta
# Import using Lando
lando db-import database-backup.sql

# Update URLs for local development (adjust based on source database)
# For example, if from production:
lando wp search-replace 'https://www.targetals.org' 'https://targetals.lndo.site'
lando wp search-replace 'targetals.org' 'targetals.lndo.site'

# If from another environment, replace with the appropriate URL
```

### 6. Local Configuration

Create `wp-config-local.php` in the WordPress root:

```php
<?php
/**
 * Local development configuration
 * This file is loaded by wp-config.php when WP_ENVIRONMENT_TYPE is 'local'
 */

// Database Configuration (Lando provides these automatically)
define('DB_NAME', 'wordpress');
define('DB_USER', 'wordpress');
define('DB_PASSWORD', 'wordpress');
define('DB_HOST', 'database');

// Debug Configuration
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
define('SCRIPT_DEBUG', true);

// Environment
define('WP_ENVIRONMENT_TYPE', 'local');

// Disable file editing in admin
define('DISALLOW_FILE_EDIT', true);

// Memory limits
define('WP_MEMORY_LIMIT', '512M');
ini_set('memory_limit', '512M');

// Local-specific settings
define('AUTOMATIC_UPDATER_DISABLED', true);
define('WP_AUTO_UPDATE_CORE', false);
```

## Development Workflow

### Theme Development

```bash
# Navigate to theme directory
cd wp-content/themes/targetals

# Start development server (with hot reload)
npm run start

# Build for production
npm run build

# Run linting
npm run lint
npm run lint:fix

# Optimize images
npm run optimize:images
```

### Available Lando Commands

```bash
# WordPress CLI
lando wp [command]

# Database operations
lando db-import [file.sql]
lando db-export [filename.sql]

# Access database
lando mysql

# View logs
lando logs

# Restart services
lando restart

# Stop environment
lando stop

# Destroy environment (keeps files)
lando destroy
```

### Local URLs

- **Site**: https://targetals.lndo.site
- **Admin**: https://targetals.lndo.site/wp-admin
- **Database**: Accessible via `lando mysql`

## Kinsta Integration

### Staging Environments

Target ALS uses Kinsta's staging environment for testing:

- **Staging URLs**: [update with actual staging URLs]
- **Purpose**: Testing before production deployment
- **Database**: Separate from production

### Production Environment

- **Production URL**: https://www.targetals.org
- **Hosting**: Kinsta Premium WordPress Hosting
- **CDN**: Kinsta CDN enabled
- **Caching**: Kinsta's built-in caching

### Deployment Process

#### 1. Local Development
```bash
# Make changes locally
# Test thoroughly on targetals.lndo.site
npm run build  # Build production assets
```

#### 2. Push to Repository
```bash
git add .
git commit -m "Description of changes"
git push origin main
```

#### 3. Deploy to Staging
```bash
# Kinsta automatically deploys from main branch to staging
# Or manually deploy via Kinsta dashboard
```

#### 4. Test on Staging
- Review changes on staging environment
- Test functionality
- Verify responsive design
- Check performance

#### 5. Deploy to Production
```bash
# Deploy from Kinsta dashboard
# Or use Kinsta's deployment tools
```

## Environment Configuration

### wp-config.php Structure

```php
<?php
// Standard WordPress configuration
require_once(ABSPATH . 'wp-settings.php');

// Environment-specific configuration
$environment = wp_get_environment_type();

switch ($environment) {
    case 'local':
        if (file_exists(dirname(__FILE__) . '/wp-config-local.php')) {
            include(dirname(__FILE__) . '/wp-config-local.php');
        }
        break;

    case 'staging':
        if (file_exists(dirname(__FILE__) . '/wp-config-staging.php')) {
            include(dirname(__FILE__) . '/wp-config-staging.php');
        }
        break;

    case 'production':
    default:
        if (file_exists(dirname(__FILE__) . '/wp-config-production.php')) {
            include(dirname(__FILE__) . '/wp-config-production.php');
        }
        break;
}
```

### Environment Variables

Create `.env` file for sensitive data (never commit this):

```bash
# Database
DB_NAME=wordpress
DB_USER=wordpress
DB_PASSWORD=wordpress
DB_HOST=database

# WordPress
WP_HOME=https://targetals.lndo.site
WP_SITEURL=https://targetals.lndo.site

# API Keys
ACF_PRO_KEY=your-acf-pro-key
KINSTA_API_KEY=your-kinsta-api-key
```

## Lando Configuration

The `.lando.yml` file configures the local development environment:

```yaml
name: targetals
recipe: wordpress

config:
  php: '8.2'
  via: nginx
  webroot: .
  database: mariadb
  xdebug: true
  framework: wordpress
  site: targetals

services:
  database:
    type: mariadb:11.4
    platform: linux/amd64
```

### Production (Kinsta)

- **Kinsta CDN**: Automatically enabled
- **Server-level caching**: Built into Kinsta
- **Image optimization**: Kinsta's image optimization
- **PHP 8.1+**: Latest PHP version for performance

## Troubleshooting

### Common Lando Issues

#### Lando won't start
```bash
# Check Docker is running
docker ps

# Restart Lando
lando restart

# Rebuild if needed
lando rebuild
```

#### Database connection issues
```bash
# Check database service
lando info

# Restart database
lando restart database

# Import fresh database
lando db-import fresh-backup.sql
```

#### SSL/HTTPS issues
```bash
# Trust Lando's SSL certificate
lando info
# Follow the SSL certificate instructions
```

### WordPress Issues

#### Plugin conflicts
```bash
# Deactivate all plugins
lando wp plugin deactivate --all

# Activate one by one to identify conflicts
lando wp plugin activate plugin-name
```

#### Theme issues
```bash
# Switch to default theme
lando wp theme activate twentytwentythree

# Check for PHP errors
lando logs -s appserver
```

#### Memory issues
```bash
# Increase PHP memory limit in .lando/php.ini
memory_limit = 512M

# Restart Lando
lando restart
```

### Build Issues

#### Node.js version mismatch
```bash
# Use correct Node version
nvm use 20.19.3

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### Webpack build failures
```bash
# Clear webpack cache
npm run build -- --mode=development

# Check for JavaScript errors
npm run lint:js
```

## Security Considerations

### Local Development

- Use strong passwords even locally
- Keep plugins and WordPress core updated
- Don't commit sensitive data to Git
- Use HTTPS even in local development

### Production (Kinsta)

- Kinsta provides enterprise-level security
- Regular security updates
- DDoS protection
- Malware scanning
- SSL certificates included

## Backup Strategy

### Local Backups

```bash
# Export database regularly
lando db-export "backup-$(date +%Y%m%d).sql"

# Backup uploads directory
tar -czf "uploads-backup-$(date +%Y%m%d).tar.gz" wp-content/uploads/
```

### Kinsta Backups

- **Automatic daily backups**: Included with Kinsta
- **Manual backups**: Available via dashboard
- **Point-in-time restore**: Available for premium plans
- **Downloadable backups**: Can download for local use

## Monitoring and Maintenance

### Production Monitoring

- **Kinsta APM**: Application Performance Monitoring
- **Uptime monitoring**: Built into Kinsta
- **Error logging**: Available in Kinsta dashboard
- **Performance insights**: Detailed analytics

## Team Collaboration

### Git Workflow

1. **Feature branches**: Create branches for new features
2. **Pull requests**: Review code before merging
3. **Testing**: Test on staging before production
4. **Documentation**: Update README for significant changes

### Code Standards

- **PHP**: WordPress Coding Standards
- **JavaScript**: ESLint with Airbnb config
- **CSS/SCSS**: Stylelint with standard config
- **Formatting**: Prettier for consistent formatting

### Communication

- Document major changes in commit messages
- Use descriptive branch names
- Test thoroughly before pushing
- Communicate deployment schedules

## Resources

### Documentation
- [Lando Documentation](https://docs.lando.dev/)
- [Kinsta Documentation](https://kinsta.com/docs/)
- [WordPress Developer Resources](https://developer.wordpress.org/)
- [Target ALS Theme Blocks Guide](wp-content/themes/targetals/blocks/README.md)

### Support
- **Lando Issues**: [GitHub Issues](https://github.com/lando/lando/issues)
- **Kinsta Support**: Available 24/7 via dashboard
- **WordPress Support**: [WordPress.org Forums](https://wordpress.org/support/)

### Tools
- **Local Development**: Lando
- **Code Editor**: VS Code (recommended)
- **Database**: Sequel Pro or phpMyAdmin (recommended)
- **Git Client**: Command line, SourceTree, or GitHub Desktop


## Target ALS Theme Documentation

### Theme Overview

The Target ALS theme is a modern, responsive WordPress theme built with a comprehensive build system using Webpack, Sass, JavaScript and custom dynamic and ACF Gutenberg blocks.

### Theme Information

- **Theme Name**: Target ALS
- **Version**: 1.0.0
- **Author**: Phases & Blue State
- **Description**: A theme for Target ALS
- **Theme URI**: https://www.targetals.org/
- **Node Version**: 20.19.3

### Theme Features

- 🎨 Custom color palette with Target ALS brand colors
- 📱 Fully responsive design
- 🧱 Custom Gutenberg blocks (ta-heading, ta-caption, ta-verticalvideo)
- 🖼️ Advanced image optimization (WebP generation, compression)
- 🎯 SVG sprite system for icons
- 📦 Modern build system with Webpack 5
- 🎨 Sass/SCSS for styling
- ✅ Code quality tools (ESLint, Stylelint, Prettier)
- 🔧 PHP CodeSniffer with WordPress standards
- 🚀 Development server with hot reload
- 📐 CSS Grid and Flexbox layouts

### Color Palette

The theme uses a carefully crafted color system:

- **Primary Sand**: `#efeeeb`
- **Primary Orange**: `#ec6326`
- **Primary Navy**: `#000426`
- **Primary Royal Blue**: `#232e83`
- **Primary Bright Blue**: `#85d1eb`
- **Chow Yellow**: `#fad92f`
- **Neutral Colors**: Black, White, Blue variations

### Theme Directory Structure

```
wp-content/themes/targetals/
├── src/                    # Source files
│   ├── scss/              # Sass stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Source images
│   ├── icons/             # SVG icons
│   └── fonts/             # Font files
├── dist/                  # Compiled assets
│   ├── css/               # Compiled CSS
│   ├── js/                # Compiled JavaScript
│   └── assets/            # Optimized assets
├── blocks/                # Custom Gutenberg blocks
│   └── acf-blocks/        # ACF-based blocks
├── includes/              # PHP includes
│   ├── theme_functions/   # Theme functionality
│   ├── post-types/        # Custom post types
│   └── utils/             # Utility functions
├── modules/               # Template modules
├── scripts/               # Build scripts
└── vendor/                # Composer dependencies
```

### Theme Development Scripts

Navigate to the theme directory first:
```bash
cd wp-content/themes/targetals
```

Available scripts:

- **`npm run build`** - Production build with optimization
- **`npm run start`** - Development server with hot reload
- **`npm run lint`** - Run all linters (SCSS, JS, formatting)
- **`npm run lint:fix`** - Auto-fix linting issues
- **`npm run lint:php`** - PHP CodeSniffer check
- **`npm run lint:php:fix`** - Auto-fix PHP issues
- **`npm run optimize:images`** - Optimize and compress images
- **`npm run generate:webp`** - Generate WebP versions of images
- **`npm run pretty`** - Format code with Prettier

### Theme Development Workflow

1. Start the development server:
   ```bash
   cd wp-content/themes/targetals
   npm run start
   ```

2. Make changes to source files in the `src/` directory

3. The build system will automatically:
   - Compile Sass to CSS
   - Bundle and transpile JavaScript
   - Optimize images
   - Generate SVG sprites
   - Reload the browser

4. Before committing, run:
   ```bash
   npm run lint:fix
   npm run build
   ```

### Custom Theme Blocks

The theme includes several custom Gutenberg blocks.

For detailed block development guides, see:
- [Dynamic Blocks Development](#custom-gutenberg-blocks-development)
- [ACF Blocks Development](#acf-blocks-development-guide)

### Image Optimization

The theme includes comprehensive image optimization:

- **Compression**: Automatic compression of JPEG, PNG, GIF, and SVG files
- **WebP Generation**: Automatic WebP format generation for better performance
- **SVG Sprites**: Icon system using SVG sprites for optimal loading

### Code Quality

The theme enforces code quality through:

- **ESLint**: JavaScript linting with Airbnb configuration
- **Stylelint**: SCSS/CSS linting with standard configuration
- **Prettier**: Code formatting
- **PHP CodeSniffer**: PHP code standards (WordPress Coding Standards)
- **Husky**: Pre-commit hooks for automated quality checks

### Browser Support

- Last 2 versions of major browsers
- \> 2% market share
- Excludes IE 11 and below

### Theme Installation

1. Clone or download the theme to your WordPress themes directory:
   ```bash
   cd wp-content/themes/
   git clone [repository-url] targetals
   ```

2. Navigate to the theme directory:
   ```bash
   cd targetals
   ```

3. Install Node.js dependencies:
   ```bash
   npm install
   ```

4. Install PHP dependencies:
   ```bash
   composer install
   ```

5. Activate the theme in WordPress admin.

### Theme Requirements

- **nvm**: For Node.js version management
- **Node.js**: 20.19.3 (managed via nvm)
- **WordPress**: 5.0+
- **PHP**: 7.4+
- **Composer**: For PHP dependencies

### Contributing to Theme

1. Follow the established coding standards
2. Run linting tools before committing
3. Test across supported browsers
4. Update documentation as needed

### Theme License

ISC License

### Theme Support

For support and questions, contact the development team at Phases & Blue State.

## Custom Gutenberg Blocks Development

The Target ALS theme includes a comprehensive system for creating custom Gutenberg blocks. There are two types of blocks available:

### Block Types

#### Dynamic Blocks (JavaScript + PHP)
- `ta-heading/` - Custom heading block with level controls
- `ta-caption/` - Enhanced caption block with rich text

#### ACF Blocks (PHP + ACF)
- `acf-blocks/ta-verticalvideo/` - Video block with ACF fields

### Creating a New Dynamic Block

Dynamic blocks use a custom `JSXBlock` system that automatically registers blocks, compiles JavaScript using Webpack, and renders with PHP on the frontend.

#### 1. Create Block Directory

```bash
mkdir wp-content/themes/targetals/blocks/ta-yourblockname
```

#### 2. Create Required Files

**`block.json`** (Required) - Modern block registration:

```json
{
  "apiVersion": 2,
  "name": "targetals/ta-yourblockname",
  "title": "Your Block Title",
  "category": "targetals-blocks",
  "icon": "text",
  "description": "Description of your block",
  "keywords": ["keyword1", "keyword2"],
  "attributes": {
    "content": {
      "type": "string",
      "default": ""
    },
    "anchor": {
      "type": "string",
      "default": ""
    }
  },
  "supports": {
    "anchor": true,
    "color": {
      "text": true,
      "background": false
    }
  },
  "editorScript": "file:./index.js",
  "render": "file:./render.php"
}
```

**`config.php`** (Required) - JSXBlock configuration:

```php
<?php
return [
    'attributes' => [
        'content' => [
            'type' => 'string',
            'default' => '',
        ],
        'anchor' => [
            'type' => 'string',
            'default' => '',
        ],
    ],
    'render' => true, // Enable PHP rendering
];
```

**`index.js`** (Required) - JavaScript for block editor:

```javascript
import { RichText, useBlockProps } from '@wordpress/block-editor';
import { registerBlockType } from '@wordpress/blocks';
import metadata from './block.json';

registerBlockType(metadata.name, {
  ...metadata,
  edit: EditComponent,
  save: () => null // Dynamic block; rendering handled by PHP
});

function EditComponent({ attributes, setAttributes }) {
  const { content, anchor } = attributes;

  const blockProps = useBlockProps({
    id: anchor || undefined,
    className: 'your-block-class'
  });

  return (
    <div {...blockProps}>
      <RichText
        placeholder="Enter your content..."
        tagName="p"
        allowedFormats={['core/bold', 'core/italic', 'core/link']}
        value={content}
        onChange={(newContent) => setAttributes({ content: newContent })}
      />
    </div>
  );
}
```

**`render.php`** (Required) - PHP template for frontend:

```php
<?php
// Get attributes
$content = $attributes['content'] ?? '';
$anchor = $attributes['anchor'] ?? '';

// Build wrapper attributes
$wrapper_attributes = get_block_wrapper_attributes([
    'class' => 'your-block-class',
    'id' => $anchor ?: null,
]);
?>

<div <?php echo $wrapper_attributes; ?>>
    <?php if (!empty($content)): ?>
        <p><?php echo wp_kses_post($content); ?></p>
    <?php endif; ?>
</div>
```

#### 3. Register the Block

Add your block to `/wp-content/themes/targetals/blocks/register-ta-blocks.php`:

```php
function ta_allowed_block_types() {
    return array(
        // ... existing blocks ...
        'targetals/ta-yourblockname', // Add your block here
    );
}
```

#### 4. Build the Block

```bash
cd wp-content/themes/targetals
npm run build  # or npm run start for development
```

### Advanced Block Features

#### Block Controls (Toolbar)

Add toolbar controls to your block:

```javascript
import { BlockControls, ToolbarGroup, ToolbarButton } from '@wordpress/block-editor';

function EditComponent({ attributes, setAttributes }) {
  return (
    <>
      <BlockControls>
        <ToolbarGroup>
          <ToolbarButton
            icon="admin-bold"
            label="Toggle Bold"
            onClick={() => setAttributes({ isBold: !attributes.isBold })}
            isPressed={attributes.isBold}
          />
        </ToolbarGroup>
      </BlockControls>
      {/* Your block content */}
    </>
  );
}
```

#### Inspector Controls (Sidebar)

Add sidebar controls:

```javascript
import { InspectorControls } from '@wordpress/block-editor';
import { PanelBody, TextControl, ToggleControl } from '@wordpress/components';

function EditComponent({ attributes, setAttributes }) {
  return (
    <>
      <InspectorControls>
        <PanelBody title="Block Settings">
          <TextControl
            label="Custom Text"
            value={attributes.customText}
            onChange={(value) => setAttributes({ customText: value })}
          />
          <ToggleControl
            label="Enable Feature"
            checked={attributes.enableFeature}
            onChange={(value) => setAttributes({ enableFeature: value })}
          />
        </PanelBody>
      </InspectorControls>
      {/* Your block content */}
    </>
  );
}
```

#### InnerBlocks Support

For blocks that contain other blocks:

```javascript
import { InnerBlocks, useBlockProps } from '@wordpress/block-editor';

function EditComponent() {
  const blockProps = useBlockProps();

  const ALLOWED_BLOCKS = ['core/paragraph', 'core/heading'];
  const TEMPLATE = [
    ['core/heading', { placeholder: 'Enter heading...' }],
    ['core/paragraph', { placeholder: 'Enter content...' }]
  ];

  return (
    <div {...blockProps}>
      <InnerBlocks
        allowedBlocks={ALLOWED_BLOCKS}
        template={TEMPLATE}
        templateLock="all"
      />
    </div>
  );
}
```

### Creating ACF Blocks

For ACF-based blocks, create a directory in `wp-content/themes/targetals/blocks/acf-blocks/`:

#### Required Files for ACF Blocks

**`block.json`**:
```json
{
  "name": "targetals/ta-yourblock",
  "title": "Your ACF Block",
  "description": "Description of your ACF block",
  "category": "targetals-blocks",
  "icon": "text",
  "keywords": ["keyword1", "keyword2"],
  "acf": {
    "mode": "preview",
    "renderTemplate": "render.php"
  },
  "supports": {
    "anchor": true
  }
}
```

**`fields.php`** - ACF field group:
```php
<?php
acf_add_local_field_group([
    'key' => 'group_yourblock_unique_key',
    'title' => 'Block | Your Block Name',
    'fields' => [
        [
            'key' => 'field_yourfield_key',
            'label' => 'Your Field',
            'name' => 'your_field',
            'type' => 'text',
            'required' => 0,
        ],
    ],
    'location' => [
        [
            [
                'param' => 'block',
                'operator' => '==',
                'value' => 'targetals/ta-yourblock',
            ],
        ],
    ],
]);
```

**`render.php`** - Block template:
```php
<?php
$your_field = get_field('your_field');
$wrapper_attributes = get_block_wrapper_attributes(['class' => 'your-block']);
?>

<div <?php echo $wrapper_attributes; ?>>
    <?php if($your_field): ?>
        <p><?php echo esc_html($your_field); ?></p>
    <?php endif; ?>
</div>
```

### Block Categories

Available categories for Target ALS blocks:

- `targetals-blocks` - General Target ALS blocks
- `targetals-lists` - List-related blocks
- `targetals-sections` - Section blocks
- `targetals-heroes` - Hero blocks

### Block Development Best Practices

1. **Use block.json**: Modern block registration method
2. **Dynamic rendering**: Use PHP for dynamic content
3. **Sanitize output**: Always sanitize content in render.php
4. **Responsive design**: Ensure blocks work on all devices
5. **Accessibility**: Include proper ARIA labels and semantic HTML
6. **Performance**: Minimize JavaScript bundle size
7. **Consistent naming**: Use `ta-` prefix for block names
8. **Error handling**: Handle missing attributes gracefully

### Block Development Workflow

```bash
# Navigate to theme directory
cd wp-content/themes/targetals

# Start development with hot reload
npm run start

# Create new block directory
mkdir blocks/ta-newblock

# Build for production
npm run build

# Run linting
npm run lint:js
npm run lint:scss
```

### Troubleshooting Blocks

- **Block not appearing**: Check `ta_allowed_block_types()` registration
- **JavaScript errors**: Check browser console and build output
- **Styling issues**: Ensure CSS is compiled and classes are correct
- **Attributes not saving**: Verify attribute definitions match in all files
- **PHP errors**: Check render.php for syntax errors

## ACF Blocks Development Guide

ACF blocks provide a way to create custom Gutenberg blocks using PHP templates and ACF field groups. They are automatically registered using the `register_acf_blocks()` function.

### Creating a New ACF Block

#### 1. Create Block Directory

```bash
mkdir wp-content/themes/targetals/blocks/acf-blocks/ta-yourblockname
```

#### 2. Create Required Files

**`block.json`** (Required) - Block configuration:

```json
{
  "name": "targetals/ta-yourblockname",
  "title": "Your Block Title",
  "description": "Description of what your block does.",
  "category": "targetals-blocks",
  "icon": "<svg>...</svg>",
  "keywords": ["keyword1", "keyword2"],
  "acf": {
    "mode": "preview",
    "renderTemplate": "render.php"
  },
  "supports": {
    "anchor": true,
    "color": {
      "text": false,
      "background": true,
      "gradients": false
    },
    "typography": false,
    "jsx": true
  },
  "attributes": {
    "anchor": {
      "type": "string",
      "default": ""
    }
  },
  "example": {
    "attributes": {
      "mode": "preview",
      "data": {
        "use_preview_image": true
      }
    }
  }
}
```

**`fields.php`** (Required) - ACF field group definition:

```php
<?php
acf_add_local_field_group([
    'key' => 'group_yourblockname_unique_key',
    'title' => 'Block | Your Block Name',
    'fields' => [
        [
            'key' => 'field_yourfield_unique_key',
            'label' => 'Your Field Label',
            'name' => 'your_field_name',
            'type' => 'text', // or 'textarea', 'image', 'file', etc.
            'instructions' => 'Instructions for the field',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => [
                'width' => '',
                'class' => '',
                'id' => '',
            ],
        ],
        // Add more fields as needed
    ],
    'location' => [
        [
            [
                'param' => 'block',
                'operator' => '==',
                'value' => 'targetals/ta-yourblockname',
            ],
        ],
    ],
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'left',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => '',
]);
```

**`render.php`** (Required) - Block template:

```php
<?php
/**
 * Your Block Name template.
 *
 * @param array $block The block settings and attributes.
 */

// Get ACF fields
$your_field = get_field('your_field_name');

// Block wrapper attributes
$wrapper_attributes = get_block_wrapper_attributes([
    'class' => 'your-block-class',
]);
?>

<?php // Block preview
if( !empty( $block['data']['use_preview_image'] ) ) { ?>
    <figure>
        <img src="<?php echo get_template_directory_uri(); ?>/blocks/acf-blocks/ta-yourblockname/preview.jpg" alt="Preview of your block">
    </figure>
<?php } else { ?>

<div <?php echo $wrapper_attributes; ?>>
    <?php if(!empty($your_field)) { ?>
        <p><?php echo esc_html($your_field); ?></p>
    <?php } ?>
</div>

<?php } ?>
```

**`preview.jpg`** (Optional) - Preview image for the block editor.

#### 3. Register the ACF Block

Add your block to the allowed blocks list in `/wp-content/themes/targetals/blocks/register-ta-blocks.php`:

```php
function ta_allowed_block_types() {
    return array(
        // ... existing blocks ...

        // allow target als acf blocks
        'targetals/ta-verticalvideo',
        'targetals/ta-yourblockname', // Add your block here
    );
}
```

### ACF Block Configuration Options

#### Block.json Properties

- **name**: Unique block identifier (must start with `targetals/`)
- **title**: Display name in the block editor
- **description**: Block description
- **category**: Block category (use existing Target ALS categories)
- **icon**: SVG icon for the block
- **keywords**: Search keywords for the block
- **acf.mode**: `preview`, `edit`, or `auto`
- **acf.renderTemplate**: Path to render template (usually `render.php`)
- **supports**: WordPress block supports (anchor, color, typography, etc.)
- **attributes**: Block attributes
- **example**: Example data for block preview

#### ACF Modes

- **preview**: Shows rendered output in editor
- **edit**: Shows ACF fields in editor
- **auto**: Switches between preview and edit based on selection

#### Common ACF Field Types

- `text` - Single line text
- `textarea` - Multi-line text
- `wysiwyg` - Rich text editor
- `image` - Image upload
- `file` - File upload
- `select` - Dropdown select
- `checkbox` - Checkboxes
- `radio` - Radio buttons
- `true_false` - Toggle/switch
- `number` - Number input
- `url` - URL input
- `email` - Email input
- `date_picker` - Date picker
- `color_picker` - Color picker

### ACF Block Example: Simple Text Block

Here's a complete example of a simple ACF text block:

**Directory**: `wp-content/themes/targetals/blocks/acf-blocks/ta-simpletext/`

**block.json**:
```json
{
  "name": "targetals/ta-simpletext",
  "title": "Simple Text Block",
  "description": "A simple text block with custom styling.",
  "category": "targetals-blocks",
  "icon": "text",
  "keywords": ["text", "content"],
  "acf": {
    "mode": "preview",
    "renderTemplate": "render.php"
  },
  "supports": {
    "anchor": true
  }
}
```

**fields.php**:
```php
<?php
acf_add_local_field_group([
    'key' => 'group_simpletext_12345',
    'title' => 'Block | Simple Text',
    'fields' => [
        [
            'key' => 'field_simpletext_content_67890',
            'label' => 'Text Content',
            'name' => 'text_content',
            'type' => 'textarea',
            'required' => 1,
        ],
    ],
    'location' => [
        [
            [
                'param' => 'block',
                'operator' => '==',
                'value' => 'targetals/ta-simpletext',
            ],
        ],
    ],
]);
```

**render.php**:
```php
<?php
$text_content = get_field('text_content');
$wrapper_attributes = get_block_wrapper_attributes(['class' => 'simple-text-block']);
?>

<div <?php echo $wrapper_attributes; ?>>
    <?php if($text_content): ?>
        <p><?php echo nl2br(esc_html($text_content)); ?></p>
    <?php endif; ?>
</div>
```

### ACF Block Best Practices

1. **Unique Keys**: Always use unique keys for field groups and fields
2. **Naming Convention**: Use `ta-` prefix for block names
3. **Preview Images**: Include preview images for better UX
4. **Validation**: Add field validation where appropriate
5. **Sanitization**: Always sanitize output in render templates
6. **Accessibility**: Include proper ARIA labels and semantic HTML
7. **Responsive**: Ensure blocks work on all screen sizes

### ACF Block Troubleshooting

- **Block not appearing**: Check that it's added to `ta_allowed_block_types()`
- **Fields not showing**: Verify field group location rules match block name
- **Styling issues**: Ensure CSS is compiled and block classes are correct
- **Preview not working**: Check `renderTemplate` path and preview image